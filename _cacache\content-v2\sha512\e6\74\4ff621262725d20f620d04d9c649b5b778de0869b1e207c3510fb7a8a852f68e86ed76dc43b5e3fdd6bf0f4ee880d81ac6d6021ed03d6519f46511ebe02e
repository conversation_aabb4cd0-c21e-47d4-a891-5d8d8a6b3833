{"name": "babel-preset-es2015", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-preset-es2015", "version": "6.0.2", "description": "", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "68bb56ddc15e34e88db166fb08fe629648e8a775", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.2.tgz", "integrity": "sha512-nsoPVVV8NowKXwlmrBqEmWW6NGGVh6gYC2ZRbFz6+2UtJEZzEuj9fV2mihOWwHyOSpPKBz1cXbsReke7FKlBLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7x5J3Cy9ObtrinnyorqOGLBSBc5962EBgB0QTGIoFDgIhAPa7WPpnUXUXsWvLAjb7JYrqIlRkLRYqapY6oDMxfI2C"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.2", "babel-plugin-transform-es2015-literals": "^6.0.2", "babel-plugin-transform-es2015-function-name": "^6.0.2", "babel-plugin-transform-es2015-arrow-functions": "^6.0.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.2", "babel-plugin-transform-es2015-classes": "^6.0.2", "babel-plugin-transform-es2015-object-super": "^6.0.2", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.2", "babel-plugin-transform-es2015-computed-properties": "^6.0.2", "babel-plugin-transform-es2015-for-of": "^6.0.2", "babel-plugin-transform-es2015-sticky-regex": "^6.0.2", "babel-plugin-transform-es2015-unicode-regex": "^6.0.2", "babel-plugin-transform-es2015-constants": "^6.0.2", "babel-plugin-transform-es2015-spread": "^6.0.2", "babel-plugin-transform-es2015-parameters": "^6.0.2", "babel-plugin-transform-es2015-destructuring": "^6.0.2", "babel-plugin-transform-es2015-block-scoping": "^6.0.2", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.2", "babel-plugin-transform-regenerator": "^6.0.2"}, "hasInstallScript": false}, "6.0.8": {"name": "babel-preset-es2015", "version": "6.0.8", "description": "", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "2ed43688626ce2d34633ba43fbad496d4b425aa1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.8.tgz", "integrity": "sha512-sqLLwo09At2lZyJwi/74IDlOyABXLmLabPi2sZ/aLuFJI3HIWLcPKFnljNhlqRa+o1jBeChmvW2ylLkrqEJ2MQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdYHc1VvX3DLUErnty6j0AGttbOEhGivwkpzVBiW1LMwIgLh+YiRAbWI+C+Adxh6k2Qb6GZ7tKxpPEMXIiTGqVRLg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.2", "babel-plugin-transform-es2015-literals": "^6.0.2", "babel-plugin-transform-es2015-function-name": "^6.0.2", "babel-plugin-transform-es2015-arrow-functions": "^6.0.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.2", "babel-plugin-transform-es2015-classes": "^6.0.8", "babel-plugin-transform-es2015-object-super": "^6.0.2", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.2", "babel-plugin-transform-es2015-computed-properties": "^6.0.2", "babel-plugin-transform-es2015-for-of": "^6.0.2", "babel-plugin-transform-es2015-sticky-regex": "^6.0.2", "babel-plugin-transform-es2015-unicode-regex": "^6.0.2", "babel-plugin-transform-es2015-constants": "^6.0.2", "babel-plugin-transform-es2015-spread": "^6.0.2", "babel-plugin-transform-es2015-parameters": "^6.0.2", "babel-plugin-transform-es2015-destructuring": "^6.0.2", "babel-plugin-transform-es2015-block-scoping": "^6.0.2", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.2", "babel-plugin-transform-regenerator": "^6.0.8"}, "hasInstallScript": false}, "6.0.11": {"name": "babel-preset-es2015", "version": "6.0.11", "description": "", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "9e79470487c4681618fe90c3caef9c4f2e3c887e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.11.tgz", "integrity": "sha512-KG0BZ/3RSelMpRCagDbpLjgEeXBsgWYyLdAOUIvrqcNnBScvqiZCZXWjZN9nUYtJSs4Vj2sZGnPFqouGmAOGjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGlt/AtUYAGrDdw6MsfC+CeIkSfiT9J7tQHrI2/hoLyqAiEAtY3WPlFMKqm4W3vUPIhtCxlpO7MDQHOx9XyIekiZ1OM="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.2", "babel-plugin-transform-es2015-literals": "^6.0.2", "babel-plugin-transform-es2015-function-name": "^6.0.2", "babel-plugin-transform-es2015-arrow-functions": "^6.0.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.2", "babel-plugin-transform-es2015-classes": "^6.0.8", "babel-plugin-transform-es2015-object-super": "^6.0.2", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.2", "babel-plugin-transform-es2015-computed-properties": "^6.0.2", "babel-plugin-transform-es2015-for-of": "^6.0.2", "babel-plugin-transform-es2015-sticky-regex": "^6.0.2", "babel-plugin-transform-es2015-unicode-regex": "^6.0.2", "babel-plugin-transform-es2015-constants": "^6.0.2", "babel-plugin-transform-es2015-spread": "^6.0.2", "babel-plugin-transform-es2015-parameters": "^6.0.2", "babel-plugin-transform-es2015-destructuring": "^6.0.2", "babel-plugin-transform-es2015-block-scoping": "^6.0.2", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.0.2", "babel-plugin-transform-regenerator": "^6.0.8"}, "hasInstallScript": false}, "6.0.12": {"name": "babel-preset-es2015", "version": "6.0.12", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "f770c227d2c18f0aafb731622af2f5bfff1687c0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.12.tgz", "integrity": "sha512-74PLJyajYW87DY+3E8aL0IjWUWZ+L81ZpAEBJBW3HPMEGLOw8MF1qN9wjm2iUcaH6mwEALm1FFww7JgBgOXIwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTdDtsqywQCAL2dHYGvoHmpnkG7e3Kcj41R+r/HhxlBAiBt8vJ5z4bIdFJPecPRjdWwf+5oxj6xycnLn6Oq0XVLzw=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.2", "babel-plugin-transform-es2015-literals": "^6.0.2", "babel-plugin-transform-es2015-function-name": "^6.0.2", "babel-plugin-transform-es2015-arrow-functions": "^6.0.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.2", "babel-plugin-transform-es2015-classes": "^6.0.8", "babel-plugin-transform-es2015-object-super": "^6.0.2", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.2", "babel-plugin-transform-es2015-computed-properties": "^6.0.2", "babel-plugin-transform-es2015-for-of": "^6.0.2", "babel-plugin-transform-es2015-sticky-regex": "^6.0.2", "babel-plugin-transform-es2015-unicode-regex": "^6.0.2", "babel-plugin-transform-es2015-constants": "^6.0.2", "babel-plugin-transform-es2015-spread": "^6.0.2", "babel-plugin-transform-es2015-parameters": "^6.0.2", "babel-plugin-transform-es2015-destructuring": "^6.0.2", "babel-plugin-transform-es2015-block-scoping": "^6.0.2", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.0.12", "babel-plugin-transform-regenerator": "^6.0.12"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-preset-es2015", "version": "6.0.14", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "df2c1cec37f20678ad8326faa13e1c9fb7d83a5b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.14.tgz", "integrity": "sha512-VfaNQjA6UFhHekUqcTKtnC6CukN3O2sPk7bvBFhsXOA4VdVUvhHDIcx+JyjCgvQ7E+e9X6A65HCnKukqoiFkKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA6mMl09xW5ntKY9ppAxKDbyeMuQOToD1AfFytZk+AN5AiA1xi3XNSK3dHC5owQ68hCaLEsY/QjgIaQtI8lGUK9Pxw=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.14", "babel-plugin-transform-es2015-literals": "^6.0.14", "babel-plugin-transform-es2015-function-name": "^6.0.14", "babel-plugin-transform-es2015-arrow-functions": "^6.0.14", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.14", "babel-plugin-transform-es2015-classes": "^6.0.14", "babel-plugin-transform-es2015-object-super": "^6.0.14", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.14", "babel-plugin-transform-es2015-computed-properties": "^6.0.14", "babel-plugin-transform-es2015-for-of": "^6.0.14", "babel-plugin-transform-es2015-sticky-regex": "^6.0.14", "babel-plugin-transform-es2015-unicode-regex": "^6.0.14", "babel-plugin-transform-es2015-constants": "^6.0.14", "babel-plugin-transform-es2015-spread": "^6.0.14", "babel-plugin-transform-es2015-parameters": "^6.0.14", "babel-plugin-transform-es2015-destructuring": "^6.0.14", "babel-plugin-transform-es2015-block-scoping": "^6.0.14", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.14", "babel-plugin-transform-es2015-modules-commonjs": "^6.0.14", "babel-plugin-transform-regenerator": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-preset-es2015", "version": "6.0.15", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "09e0536c79b0a8c9a0ca0c46e84f6e46e528ef03", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.0.15.tgz", "integrity": "sha512-TWs+ga8S8t10z6ePPnNcFdUs3lBJP3Q7/8whGX4N9iFrvDhuVu+saZ4InIc5No9YQm1fMTZnblFi+sGgdvtHtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEY2tDnK/QBV4yi7Ye1bbo/ftRSmLuQgRfAYTE5ZThXiAiEAoTbvhsOqRYGuD7d789UkND72Afdzf+1OADEff8f9KtU="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.14", "babel-plugin-transform-es2015-literals": "^6.0.15", "babel-plugin-transform-es2015-function-name": "^6.0.14", "babel-plugin-transform-es2015-arrow-functions": "^6.0.14", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.14", "babel-plugin-transform-es2015-classes": "^6.0.15", "babel-plugin-transform-es2015-object-super": "^6.0.14", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.14", "babel-plugin-transform-es2015-computed-properties": "^6.0.14", "babel-plugin-transform-es2015-for-of": "^6.0.14", "babel-plugin-transform-es2015-sticky-regex": "^6.0.14", "babel-plugin-transform-es2015-unicode-regex": "^6.0.14", "babel-plugin-transform-es2015-constants": "^6.0.15", "babel-plugin-transform-es2015-spread": "^6.0.14", "babel-plugin-transform-es2015-parameters": "^6.0.14", "babel-plugin-transform-es2015-destructuring": "^6.0.14", "babel-plugin-transform-es2015-block-scoping": "^6.0.14", "babel-plugin-transform-es2015-typeof-symbol": "^6.0.15", "babel-plugin-transform-es2015-modules-commonjs": "^6.0.15", "babel-plugin-transform-regenerator": "^6.0.14"}, "hasInstallScript": false}, "6.1.2": {"name": "babel-preset-es2015", "version": "6.1.2", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "1c0a826fd4deb1d90011c66f1300bf4f847c0e00", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.1.2.tgz", "integrity": "sha512-8uzTSEcaU8jLBfja5R5JIueJ6MuaN2OAl55yS44la8Z4aH+82xu5fFHbOrgGaOs2MMd8u0PF5+kjOwgDuztmAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZ/y0VjkKf0JsBFIE3oxMmiKcMiFWG1edbi5Xn7iz1UQIgWeg1/en6REfSSTJNopIk89uVs2oIyzbc6oNmu6Icgsw="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.0.14", "babel-plugin-transform-es2015-literals": "^6.0.15", "babel-plugin-transform-es2015-function-name": "^6.0.14", "babel-plugin-transform-es2015-arrow-functions": "^6.0.14", "babel-plugin-transform-es2015-block-scoped-functions": "^6.0.14", "babel-plugin-transform-es2015-classes": "^6.1.2", "babel-plugin-transform-es2015-object-super": "^6.0.14", "babel-plugin-transform-es2015-shorthand-properties": "^6.0.14", "babel-plugin-transform-es2015-computed-properties": "^6.0.14", "babel-plugin-transform-es2015-for-of": "^6.0.14", "babel-plugin-transform-es2015-sticky-regex": "^6.0.14", "babel-plugin-transform-es2015-unicode-regex": "^6.0.14", "babel-plugin-transform-es2015-constants": "^6.0.15", "babel-plugin-transform-es2015-spread": "^6.0.14", "babel-plugin-transform-es2015-parameters": "^6.0.14", "babel-plugin-transform-es2015-destructuring": "^6.0.14", "babel-plugin-transform-es2015-block-scoping": "^6.0.14", "babel-plugin-transform-es2015-typeof-symbol": "^6.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.0.15", "babel-plugin-transform-regenerator": "^6.0.14"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-preset-es2015", "version": "6.1.4", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "854d585f60728700a36d06d014c8a379bade6844", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.1.4.tgz", "integrity": "sha512-lez+KrcEbsOAaHXV1prSwU0cq7p41aHjrS8dr9AADQdFfOxxA4U6xnaVkd8PWX/mgS/S94uEK+n5iwquuPU2FQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEx+6VdAVJqc39L5f+kQwBfDBdZ8dGdR3H1BsSW3TqWgIhAN9IzZw23wVboRFWS4Ayso3xJlTC+2467AFURwV2kOI8"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.1.4", "babel-plugin-transform-es2015-literals": "^6.1.4", "babel-plugin-transform-es2015-function-name": "^6.1.4", "babel-plugin-transform-es2015-arrow-functions": "^6.1.4", "babel-plugin-transform-es2015-block-scoped-functions": "^6.1.4", "babel-plugin-transform-es2015-classes": "^6.1.4", "babel-plugin-transform-es2015-object-super": "^6.1.4", "babel-plugin-transform-es2015-shorthand-properties": "^6.1.4", "babel-plugin-transform-es2015-computed-properties": "^6.1.4", "babel-plugin-transform-es2015-for-of": "^6.1.4", "babel-plugin-transform-es2015-sticky-regex": "^6.1.4", "babel-plugin-transform-es2015-unicode-regex": "^6.1.4", "babel-plugin-transform-es2015-constants": "^6.1.4", "babel-plugin-transform-es2015-spread": "^6.1.4", "babel-plugin-transform-es2015-parameters": "^6.1.4", "babel-plugin-transform-es2015-destructuring": "^6.1.4", "babel-plugin-transform-es2015-block-scoping": "^6.1.4", "babel-plugin-transform-es2015-typeof-symbol": "^6.1.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.1.4", "babel-plugin-transform-regenerator": "^6.1.4"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-preset-es2015", "version": "6.1.17", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "eeb303929583f47efee3998c120ccf87076679a2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.1.17.tgz", "integrity": "sha512-Yc3Bezs1rgyLtHMDrcFhMS8r5loYFcUElzRWHYfqzI3fzTTNhI5FsNiwJRncxUc7cFL1ItGNCAG7NL+28Rnr3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCImaxpsKrp/5xiwNN3PinCbBsoV5RtBpwzIQGtwOM5vAIgXUmbXwm5SQkP1RDLwRKuYYd7/ZowLJ9CpkOBimiveYo="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.1.17", "babel-plugin-transform-es2015-literals": "^6.1.17", "babel-plugin-transform-es2015-function-name": "^6.1.17", "babel-plugin-transform-es2015-arrow-functions": "^6.1.17", "babel-plugin-transform-es2015-block-scoped-functions": "^6.1.17", "babel-plugin-transform-es2015-classes": "^6.1.17", "babel-plugin-transform-es2015-object-super": "^6.1.17", "babel-plugin-transform-es2015-shorthand-properties": "^6.1.17", "babel-plugin-transform-es2015-computed-properties": "^6.1.17", "babel-plugin-transform-es2015-for-of": "^6.1.17", "babel-plugin-transform-es2015-sticky-regex": "^6.1.17", "babel-plugin-transform-es2015-unicode-regex": "^6.1.17", "babel-plugin-check-es2015-constants": "^6.1.17", "babel-plugin-transform-es2015-spread": "^6.1.17", "babel-plugin-transform-es2015-parameters": "^6.1.17", "babel-plugin-transform-es2015-destructuring": "^6.1.17", "babel-plugin-transform-es2015-block-scoping": "^6.1.17", "babel-plugin-transform-es2015-typeof-symbol": "^6.1.17", "babel-plugin-transform-es2015-modules-commonjs": "^6.1.17", "babel-plugin-transform-regenerator": "^6.1.17"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-preset-es2015", "version": "6.1.18", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "5b8f97acd9e50cbc4c4e24eab12d5b1abc74cdda", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.1.18.tgz", "integrity": "sha512-FgZwmmvg0jWxtYf4ZcyfWYXYrfuBlWRc4to6gcJqgTVC9LG+jMwLZLCdOyYgLFSs9gEho4CLnqEkl67l89rPGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCc3bpofuHLokkP30tfjOSrs4D8zPA2zDydzcaCLIeJSAIgdtTjoPU6HkbkScMqGHQL07yTjIDbqYTPJZQubJIgYcY="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.1.18", "babel-plugin-transform-es2015-literals": "^6.1.18", "babel-plugin-transform-es2015-function-name": "^6.1.18", "babel-plugin-transform-es2015-arrow-functions": "^6.1.18", "babel-plugin-transform-es2015-block-scoped-functions": "^6.1.18", "babel-plugin-transform-es2015-classes": "^6.1.18", "babel-plugin-transform-es2015-object-super": "^6.1.18", "babel-plugin-transform-es2015-shorthand-properties": "^6.1.18", "babel-plugin-transform-es2015-computed-properties": "^6.1.18", "babel-plugin-transform-es2015-for-of": "^6.1.18", "babel-plugin-transform-es2015-sticky-regex": "^6.1.18", "babel-plugin-transform-es2015-unicode-regex": "^6.1.18", "babel-plugin-check-es2015-constants": "^6.1.18", "babel-plugin-transform-es2015-spread": "^6.1.18", "babel-plugin-transform-es2015-parameters": "^6.1.18", "babel-plugin-transform-es2015-destructuring": "^6.1.18", "babel-plugin-transform-es2015-block-scoping": "^6.1.18", "babel-plugin-transform-es2015-typeof-symbol": "^6.1.18", "babel-plugin-transform-es2015-modules-commonjs": "^6.1.18", "babel-plugin-transform-regenerator": "^6.1.18"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-preset-es2015", "version": "6.2.4", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "bb566a596a63eaa1bcc394508cdb86f7420b6385", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.2.4.tgz", "integrity": "sha512-JE1w717NEGyIrHWgYuo9+2WKPdLmagoEb7s2SuYYREok0KLyiHKXw2kb+oC+ENwCd1C6spo9Bb+dqz5Tf4jwCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICtEQZiUTx150AF7QmVSb/7vO7pYnnYMyc6cTeofzdUdAiAZ3Gtx1y+OQvF+4maMAFevzzrSHw32UjfK5erAFkfO3g=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.2.4", "babel-plugin-transform-es2015-literals": "^6.2.4", "babel-plugin-transform-es2015-function-name": "^6.2.4", "babel-plugin-transform-es2015-arrow-functions": "^6.2.4", "babel-plugin-transform-es2015-block-scoped-functions": "^6.2.4", "babel-plugin-transform-es2015-classes": "^6.2.4", "babel-plugin-transform-es2015-object-super": "^6.2.4", "babel-plugin-transform-es2015-shorthand-properties": "^6.2.4", "babel-plugin-transform-es2015-computed-properties": "^6.2.4", "babel-plugin-transform-es2015-for-of": "^6.2.4", "babel-plugin-transform-es2015-sticky-regex": "^6.2.4", "babel-plugin-transform-es2015-unicode-regex": "^6.2.4", "babel-plugin-check-es2015-constants": "^6.2.4", "babel-plugin-transform-es2015-spread": "^6.2.4", "babel-plugin-transform-es2015-parameters": "^6.2.4", "babel-plugin-transform-es2015-destructuring": "^6.2.4", "babel-plugin-transform-es2015-block-scoping": "^6.2.4", "babel-plugin-transform-es2015-typeof-symbol": "^6.2.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.2.4", "babel-plugin-transform-regenerator": "^6.2.4"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-preset-es2015", "version": "6.3.13", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "97dce7ef292e18cb9b2b7545d80c593c28d9517f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.3.13.tgz", "integrity": "sha512-+G91ynAcnamEY24oxSU8CNPPm/aIa9mTEey2a0Eg5sJ59LXT7lcBMhdCSqd8PEVB7KjvQLyVo5wnAih8xNGmqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDtJuI6Kq0QpjcUbk4I9GNHMi70vaK4CSW1rFLn/imYAAiBS0nXVcFbmm3Z4PzKmXHIufTzQNOcRPG3gXBeXanOw0Q=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-typeof-symbol": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-regenerator": "^6.3.13"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-preset-es2015", "version": "6.5.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "a36dbebf7b40041bb22f8080db52846cde6c1037", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.5.0.tgz", "integrity": "sha512-4xJys5LoyMJKLelfV+1yTKUrhKS1W1Yrkdczr7RA97J8UwzMoW07X4n9uppYbV/PiLQ1n+X7T1Td/lztyvzfew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHpo4n0yDm0Um4Ei/ZuDQ2jQoBhlf0Uhb6JfL6PGa0XwAiEAuDD+SOx4M11cA+OSJVMf7vsHjBrDS1ffk5tbSF8US7Y="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.3.13", "babel-plugin-transform-es2015-typeof-symbol": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-regenerator": "^6.3.13"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-preset-es2015", "version": "6.5.0-1", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "bff0c80065e51eef0f0605e9d224cce58f4ec23b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.5.0-1.tgz", "integrity": "sha512-tx3JZlcgDDDOrAmr4kwzMCVivGP3f37UqZB4fz3NhSU9gchfiYwDk1jOSE3Xb8Be6CL+gYjjazpJQlzM+8og/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC87Et/9svoQ70Qv552Qf4EMEt6Fkhy4uL5D2UZ616ZlQIgFaCnpufSDBPQERH+pnhCz8eT5dle4m4KteAHouzoL+U="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.5.0-1", "babel-plugin-transform-es2015-literals": "^6.5.0-1", "babel-plugin-transform-es2015-function-name": "^6.5.0-1", "babel-plugin-transform-es2015-arrow-functions": "^6.5.0-1", "babel-plugin-transform-es2015-block-scoped-functions": "^6.5.0-1", "babel-plugin-transform-es2015-classes": "^6.5.0-1", "babel-plugin-transform-es2015-object-super": "^6.5.0-1", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0-1", "babel-plugin-transform-es2015-computed-properties": "^6.5.0-1", "babel-plugin-transform-es2015-for-of": "^6.5.0-1", "babel-plugin-transform-es2015-sticky-regex": "^6.5.0-1", "babel-plugin-transform-es2015-unicode-regex": "^6.5.0-1", "babel-plugin-check-es2015-constants": "^6.5.0-1", "babel-plugin-transform-es2015-spread": "^6.5.0-1", "babel-plugin-transform-es2015-parameters": "^6.5.0-1", "babel-plugin-transform-es2015-destructuring": "^6.5.0-1", "babel-plugin-transform-es2015-block-scoping": "^6.5.0-1", "babel-plugin-transform-es2015-typeof-symbol": "^6.5.0-1", "babel-plugin-transform-es2015-modules-commonjs": "^6.5.0-1", "babel-plugin-transform-regenerator": "^6.5.0-1"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-preset-es2015", "version": "6.6.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "88b33e58fec94c6ebde58dc65ece5d14e0ec2568", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.6.0.tgz", "integrity": "sha512-BDs1DGb4IBDKIPa2PsPjO0y/Xg7LPD3HVm61vS7UQ42C7LulQaztUW9p/B6v1dSOdsioM47WWFFLe5Pn0a2zyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHI8BV+i5IyBYF+U4gIvgGQ8dDLcZOsSJV+O/Lb7DTHzAiEA3NtNoKJgbYMK2aD1nfPKvJ5zEsN5W6a0WF4TOSP1H1I="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.6.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.6.0", "babel-plugin-transform-es2015-destructuring": "^6.6.0", "babel-plugin-transform-es2015-block-scoping": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-plugin-transform-regenerator": "^6.6.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-preset-es2015", "version": "6.9.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "95e4716ac4481dfb30999cb5c111814e1ada0f41", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.9.0.tgz", "integrity": "sha512-pRyYu50eT5HJXwsJe0ywSFUGkSrI6RGgMl/wSUagu0UjSrv/ebLLDOjqKW7P4i803KPpeZ62Hkt4Xyob9L6LFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB7M12iwSPTz/v3pwKTFeU0/EQtYkbD8bbZYb3raisY4AiADX3J5tEhPq4RuG1afH0b6KOm/VLJuh97KMcRLhyIVPw=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.9.0", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-block-scoping": "^6.9.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-plugin-transform-regenerator": "^6.9.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.9.0"}, "hasInstallScript": false}, "6.13.0": {"name": "babel-preset-es2015", "version": "6.13.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "6bfd679472cba9d76b542e2beaf8120adfe6da29", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.13.0.tgz", "integrity": "sha512-ZOEUJzGGeQ78xPLlNcRh9d4gd1BT+eCHNQvSMDKgA3trAmm6k8FqxDm2nuuFHxydPVJ9FrjNxHK3LLZcEGlXwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEQt1INp0dXA83omjKbTspA88Y7lkkt2IhNmNjKpOENUAiAjQBJwkKflwRV34UP9GVOmZld550yzM1jnFSbb6r9z+A=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.9.0", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.12.0", "babel-plugin-transform-es2015-modules-umd": "^6.12.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.9.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.9.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.9.0"}, "hasInstallScript": false}, "6.13.1": {"name": "babel-preset-es2015", "version": "6.13.1", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "6d78ae87681cdacd98f778d518d0bc5345668d45", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.13.1.tgz", "integrity": "sha512-FDEXgi+izc3b21fbfSwruxksOmgtLJvFVjvgxngMh2Y2cLMN8mvoZTdyRvhQO1pg0s50lKPRvzSKAGghy5OohA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxp7LLC5WeVk+mrpX4BnpY5q4CSFov4M2MQTsYbAU3sAIgRLMkgXcA4Py0wRj+3jPPGJe4cyaXReIkDysXiWAvWHE="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.9.0", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.12.0", "babel-plugin-transform-es2015-modules-umd": "^6.12.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.9.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.9.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.9.0"}, "hasInstallScript": false}, "6.13.2": {"name": "babel-preset-es2015", "version": "6.13.2", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "006c469a7528bd066f2917c8b4955309dcd53cfb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.13.2.tgz", "integrity": "sha512-f7h04SeB8lBDv6TKomK1wwDMW+ceAzr/rEj/CcLIeAGF2XKjvlnDEaiBTxng6lbvk5b+YB+bpEnx6kRFPYPdpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAiHm0gfEwLAj8548T2p/7QH9oCsfL1DYirXGhyMItA5AiB3fv45UI3REHX3uxhZ0Wow8uGgKfzJry0lu9Z5YT5aTg=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.9.0", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.12.0", "babel-plugin-transform-es2015-modules-umd": "^6.12.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.9.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.9.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.13.2", "babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-preset-es2015", "version": "6.14.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "cd2437a96f02a4d19bb87e87980bf0b0288d13eb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.14.0.tgz", "integrity": "sha512-ogjLirrzjxgkMizNwNaTFslRhpKykJ3cboykPoBRyY1vwMAUVtu4F7jdISs9aTQlPYX2OLj5I4302vaYJ70Ohw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7AUv7g3iDCwrjraOGp3DBZZAD8wWJrOnfDS2olCREjAiEAhtJcp1Tu9mu1tQBiv81MMEk53h7tJiW4NIe3kWesJFk="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.14.0", "babel-plugin-transform-es2015-classes": "^6.14.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.14.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.14.0", "babel-plugin-transform-es2015-modules-umd": "^6.12.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.9.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.14.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.13.2", "babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-preset-es2015", "version": "6.16.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "59acecd1efbebaf48f89404840f2fe78c4d2ad5c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.16.0.tgz", "integrity": "sha512-7L/OGwckZnRGHgV+kgo69T2S3RnEctizvt5YU9QzaWs6OFfXS2ZAvkXcPFjARcTPeQVipMNhC2R+p5G5drniPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAO6Ppyahdwf4ogA0V84ho10CK3Bya1hoQJyzUgYUd2gIhAP8ikKmaHQM2M3vrn93S+EkwUsEkABq+SKHmX0BFJPXc"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.14.0", "babel-plugin-transform-es2015-classes": "^6.14.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.16.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.6.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.16.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.14.0", "babel-plugin-transform-es2015-modules-umd": "^6.12.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.16.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.6.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.16.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.16.0", "babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-preset-es2015", "version": "6.18.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "b8c70df84ec948c43dcf2bf770e988eb7da88312", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.18.0.tgz", "integrity": "sha512-H9GDbUBadt4IpjwHuMvQlcpd5DCTxr+aRHgLpdvcmiNwEvSX4HRisenKFDgmxSgQGPY2Ot4UHyEQE0GW04aaSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRzoM/4+q9pvL4+YhdEfBGKfbCpTohGJFcR9zPSjygNQIhAKQBJyO0XzJ5gQDctq83hyENx4xcB8y/2U1H2dwl95oK"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.18.0", "babel-plugin-transform-es2015-classes": "^6.18.0", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.6.0", "babel-plugin-transform-es2015-for-of": "^6.18.0", "babel-plugin-transform-es2015-function-name": "^6.9.0", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-amd": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.18.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.18.0", "babel-plugin-transform-es2015-modules-umd": "^6.18.0", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.18.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.18.0", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.6.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.18.0", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-regenerator": "^6.16.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.18.0", "babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-preset-es2015", "version": "6.22.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "af5a98ecb35eb8af764ad8a5a05eb36dc4386835", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.22.0.tgz", "integrity": "sha512-clDvFphlHNqtK1UEQmNAkivHuyYZjjpHYCmUvGsmxzMVGrrN7s62C1pl6MeTdgN3NMH4h1GwF87lfzDK1WCOWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQ+WkMxNIS9bazX58F2Nh7yeMC1kCC+2i+5bBAshfxkgIgNMnFpjzUbr20a4QlkA4PqMRyjUbB/3P9qJp8XGqJyMk="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.22.0", "babel-plugin-transform-es2015-computed-properties": "^6.22.0", "babel-plugin-transform-es2015-destructuring": "^6.22.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.22.0", "babel-plugin-transform-es2015-for-of": "^6.22.0", "babel-plugin-transform-es2015-function-name": "^6.22.0", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.22.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.22.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.22.0", "babel-plugin-transform-es2015-modules-umd": "^6.22.0", "babel-plugin-transform-es2015-object-super": "^6.22.0", "babel-plugin-transform-es2015-parameters": "^6.22.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.22.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.22.0", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.22.0", "babel-plugin-transform-es2015-unicode-regex": "^6.22.0", "babel-plugin-transform-regenerator": "^6.22.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.22.0", "babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.1", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "42225e406ade907cc1adebd06f19d037a9a1d9ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.1.tgz", "integrity": "sha512-9gmRgfAiY5XH0bJjRhxKeESOHZ+ZDHT55F0Gv0uW2Xp2skw5kau/Xu26FmOpHJhjDVM/FXxe2U/U3pRjC4Z1Qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMb6qjec9+vTdROY9CUfhPa4S2BXvpJcKoixxYeAPqvgIhAIim32oFfTmiLT4U8UVmFHXxvRyoyXR25G6PE3V2sqFo"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.1", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.1", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.1", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.1", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.1", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.1", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.1", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.1", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.1", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.1", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.1", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.1", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.1", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.1", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.1", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.1", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.1", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.1", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.1", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.1", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.1", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.1", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.1", "babel-plugin-transform-regenerator": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.1", "babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "6.24.0": {"name": "babel-preset-es2015", "version": "6.24.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "c162d68b1932696e036cd3110dc1ccd303d2673a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.24.0.tgz", "integrity": "sha512-s17sA/BEOtsCViYkQQqkoG/3Gs1o6RHPODYwwBKztv22j5jeE9idggk0jJl0WLMY/r4QzeppZaSbC4x9psOWKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDHRFHx21iZ6KbaDTuiRAfCJU3NTN1T6gHqTz8Q956UfAiA1hhTC3MOd4NxS69foRH7V8RtQZ2hW+CwBvTGlbsRaPQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.22.0", "babel-plugin-transform-es2015-computed-properties": "^6.22.0", "babel-plugin-transform-es2015-destructuring": "^6.22.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.22.0", "babel-plugin-transform-es2015-for-of": "^6.22.0", "babel-plugin-transform-es2015-function-name": "^6.22.0", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.24.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.24.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.22.0", "babel-plugin-transform-es2015-modules-umd": "^6.24.0", "babel-plugin-transform-es2015-object-super": "^6.22.0", "babel-plugin-transform-es2015-parameters": "^6.22.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.22.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.22.0", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.22.0", "babel-plugin-transform-es2015-unicode-regex": "^6.22.0", "babel-plugin-transform-regenerator": "^6.22.0"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.24.0", "babel-helper-plugin-test-runner": "^6.24.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.3", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "16534cf8dbc86369dbf5d6bb08c9d43101b81628", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.3.tgz", "integrity": "sha512-bmvi1oPEYlkCID4eHy+BaUIVZgl6N3/tBl5THbsfGJxTE8b3XYctCu/zV6fwlJzxwyi5g1EmTDKwqHt5k6lF9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYgu2Ty/Zmx4wkSkcX+G8lUDp3iPv8YvZwP+a2YubOmAiA64FRqKgbolt1Mvpuo+hIa/TYcDPrMh3WT4Pq3hphcmw=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.3", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.3", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.3", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.3", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.3", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.3", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.3", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.3", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.3", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.3", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.3", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.3", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.3", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.3", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.3", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.3", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.3", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.3", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.3", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.3", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.3", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.3", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.3", "babel-plugin-transform-regenerator": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.3", "babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.7", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "3494dc42a67498781654ded1390e5f476d6dafa8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.7.tgz", "integrity": "sha512-29BG03AUte8zi2IgkCoCtlVWGS/7+u3fRyi16YUEQgghHQo+BfHDnyFyxLYw2asQzzZ2b43AtYwOQxdcL9tvsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAzINhv7WQvtAwrsMHDoVXSz8UKBauQdrnvgMGnoEZe9AiEAvNz1Hw5hR5F2M2HqsG4w4iM8Bltyd9udKrx2OaTzf4Y="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.7", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.7", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.7", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.7", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.7", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.7", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.7", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.7", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.7", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.7", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.7", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.7", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.7", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.7", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.7", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.7", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.7", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.7", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.7", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.7", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.7", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.7", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.7", "babel-plugin-transform-regenerator": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.7", "babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-preset-es2015", "version": "6.24.1", "description": "Babel preset for all es2015 plugins.", "deprecated": "🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!", "dist": {"shasum": "d44050d6bc2c9feea702aaf38d727a0210538939", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-6.24.1.tgz", "integrity": "sha512-XfwUqG1Ry6R43m4Wfob+vHbIVBIqTg/TJY4Snku1iIzeH7mUnwHA8Vagmv+ZQbPwhS8HgsdQvy28Py3k5zpoFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4iqoLymGfKULNR17Ma+FNISzXtn0ikb0biA7IP44TJwIhALQeQ7IsUBOtMQftdRJhI4zhtDqoQkOCkEuFTQMLTaXd"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.24.1", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-destructuring": "^6.22.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.24.1", "babel-plugin-transform-es2015-for-of": "^6.22.0", "babel-plugin-transform-es2015-function-name": "^6.24.1", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.24.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-plugin-transform-es2015-modules-systemjs": "^6.24.1", "babel-plugin-transform-es2015-modules-umd": "^6.24.1", "babel-plugin-transform-es2015-object-super": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.24.1", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.22.0", "babel-plugin-transform-es2015-unicode-regex": "^6.24.1", "babel-plugin-transform-regenerator": "^6.24.1"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "^6.24.1", "babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.8", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "722defecc38fd5ebe381073a5de1e0888a59c4a4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.8.tgz", "integrity": "sha512-xN2KiY/5q1ZgQH/aB7EhVKVa3rgqxP7ewSCn8C2RLhSZ2DkOaoCdhXQ+qo2iMWTshnuxfnnFq/Rmo870rEONhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQu/MQrJOimURIsNfB8GDkgNU3JznYaOW6OZMDxiCO1wIgYMrdltiBWQ6ck9Su1HuOiIMabFXNJ16FBfzoQyravME="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.8", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.8", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.8", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.8", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.8", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.8", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.8", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.8", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.8", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.8", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.8", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.8", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.8", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.8", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.8", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.8", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.8", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.8", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.8", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.8", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.8", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.8", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.8", "babel-plugin-transform-regenerator": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.8", "babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.9", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "e0de59ce2f4e0caa18c322ca50261fc15a31a98b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.9.tgz", "integrity": "sha512-m5FDakRygX3nNpUOkanZ/7WoWDG4GSmSJLSPuYDNH51q0tyZ6dLx0sPuQa3xORJ/+RYs4UYiLzk9tclJG/47hA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFZ+Hh0sJ0sooVV2NVS6SsosdSOUFsKxLfpYqA7fEk4cAiEA8z0d4bQ0yu1d7WTw6s6oMFAImK1rsTLr9zyNm5ywuyE="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.9", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.9", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.9", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.9", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.9", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.9", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.9", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.9", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.9", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.9", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.9", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.9", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.9", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.9", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.9", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.9", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.9", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.9", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.9", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.9", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.9", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.9", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.9", "babel-plugin-transform-regenerator": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.9", "babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.10", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "5094704c744331db18b15aa94c062cf917db4fb5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.10.tgz", "integrity": "sha512-FFmi6YflfIreKZOYV14+6AOl9myrIG0EM8qKqRlquhGOas2Ei4mQ9KUq5h1boE88DYhKhHcvf9+pvaL6sfrLcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgB+HQ/I8TIinO8+S8rONUxMbCGOEFNraiZdYJtkT9OgIhAIFLcLaxx/YyYisHOzvLkPfLVXfwHCOEjAlMUpEe8Acd"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.10", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.10", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.10", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.10", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.10", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.10", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.10", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.10", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.10", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.10", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.10", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.10", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.10", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.10", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.10", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.10", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.10", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.10", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.10", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.10", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.10", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.10", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.10", "babel-plugin-transform-regenerator": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-transform-fixture-test-runner": "7.0.0-alpha.10", "babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.11", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-RSh65piT5bBnO3O05zGAQtTdRdJpGqafFg1csWHYErlycFEl6Udd8FS8WWDaMly58Jyb465/jf6XQ/Rep779Eg==", "shasum": "9ee3af7fb0b6faf9d2eb876f8d3731b4c070c850", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGoAAV45Y0puIo5/IuuGrv7gxxTy9OrSBW2h/o5WChLBAiEAiEDFY7/6FOa0E1Eve83RFAANwJIpGLWKpvoyY1+p8Bg="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.11", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.11", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.11", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.11", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.11", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.11", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.11", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.11", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.11", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.11", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.11", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.11", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.11", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.11", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.11", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.11", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.11", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.11", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.11", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.11", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.11", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.11", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.11", "babel-plugin-transform-regenerator": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.12", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-s9l9FCputEry7T8P8vDVL4lQsW3LilnLANVxM7Lqeb7rkt3yB6gexZn77hE15EI/y0eMPHAYhcajEfE/Z13oNg==", "shasum": "990df94497f52f2e5a36cf2e2be367f0de454334", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0/aWHFnw2pm9HdCoeogr0IefVglFHn2WytagvjNRMAAIgW/23OAQQTctQWpHqZnlqnRV1fqO03aOkv2WyK8Gvtao="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.12", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.12", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.12", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.12", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.12", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.12", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.12", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.12", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.12", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.12", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.12", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.12", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.12", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.12", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.12", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.12", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.12", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.12", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.12", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.12", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.12", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.12", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.12", "babel-plugin-transform-regenerator": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.14", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "82980ffb33e0c1d3162bd83c6467e32e53512a71", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.14.tgz", "integrity": "sha512-LgqLomuw28Piywssh4itvErPD4y/RE0nCNTlXePIvV/b+hzuNfDbLia0gdiQcCHnk6URnqh1zTKK66cBaYOdNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4aUbJ1v2vWy2aU+CeKOS5t7bFL9+rpmHJLjpHhMNBfAiAr9hB4wsSj3zS7BKMHW8wEIbeJ0HC895jBNGGFDiOX5A=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.14", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.14", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.14", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.14", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.14", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.14", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.14", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.14", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.14", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.14", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.14", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.14", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.14", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.14", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.14", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.14", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.14", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.14", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.14", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.14", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.14", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.14", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.14", "babel-plugin-transform-regenerator": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.15", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "1063ffc60dbe0d4e58a9a9205389ba772200e622", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.15.tgz", "integrity": "sha512-RoFEJqmNuzUDtf27G8AnFiaHBNwUuD8tasp8/hbgODmeLrNFilpRi9vn+X2THjNdmlKQTncyuWCQIGcY06s4ZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRnfhu3Xv2eP5dsWlvb68GxFakm+b1Nyz6wf6DdxH/+AIhAJuS/2NBRCteh/r+eDYfk3fwqrJpRMSCEt8jTgZFf2k9"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.15", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.15", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.15", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.15", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.15", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.15", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.15", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.15", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.15", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.15", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.15", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.15", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.15", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.15", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.15", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.15", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.15", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.15", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.15", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.15", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.15", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.15", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.15", "babel-plugin-transform-regenerator": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.16", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "ac2cb21d71ba93324ad4da5625ec74060644e24c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.16.tgz", "integrity": "sha512-Gi01rA+6Rg9MxeRxrY5LVk7fP8oiFyPCDOzLFY0XwYh9q+cfyg85GbAzPHk03ojXazCAaPxIlKPZDOeMlk/AJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDhkKmYNT+OOSocvDR281RzZz7mVDs6xycjYDzxxVL2AiEAkLISP/jhaZqKg1/2jAbmjiTElEnEwPXI3cM6HtjY5JQ="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.16", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.16", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.16", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.16", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.16", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.16", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.16", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.16", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.16", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.16", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.16", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.16", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.16", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.16", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.16", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.16", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.16", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.16", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.16", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.16", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.16", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.16", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.16", "babel-plugin-transform-regenerator": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.17", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"shasum": "b4b66e62f4921356a669014aff16d8f9135bbd3b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.17.tgz", "integrity": "sha512-sjm+FCSZc1dZXLsOIfUiaSidh0bltsJ3B7WSZ562caWZm884ueqf9eGgHgbbRpNDHK6iBVaixenGz3LN7HSOXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFJfKw6TVxn+xdCE7DW7584tjVZgd+Uky6/o1gVQFLmMAiA78djN65c42Xf/ZnNIUG1uzl8CwiiHsv82PdjAJfMCVg=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.17", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.17", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.17", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.17", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.17", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.17", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.17", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.17", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.17", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.17", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.17", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.17", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.17", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.17", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.17", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.17", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.17", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.17", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.17", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.17", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.17", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.17", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.17", "babel-plugin-transform-regenerator": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.18", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-DHl1faIxNvNjeCy1sIOa7WBA2krFfd/huTqFS7uUygQAD3E2Asb9JL3h1wgzN7zyCCKFsvca5El3FP/HiyDhFw==", "shasum": "bb3bbc52a853643ef6d52ab998bf4f351129f683", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSPL6yOf+5KS35nu6+r86f37gkByaGbvbLbhABckIgZwIgfBFeFyOwsV2dlLgIldEFnfG5/aNlf3WKVlMO3xBJps4="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.18", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.18", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.18", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.18", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.18", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.18", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.18", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.18", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.18", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.18", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.18", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.18", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.18", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.18", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.18", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.18", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.18", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.18", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.18", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.18", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.18", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.18", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.18", "babel-plugin-transform-regenerator": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.19", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-3F0dVu/1lTpG2BRN6/rfWmQxJ1n9hu6w4L/CEWrB32/Uwn4kg52NvHZKYH2blEPsxqNWjDFuCgGEzqwhvLOyMQ==", "shasum": "938fd5feae186fff28c9f7c8cd1f0ee094174524", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHjytbwuiBxqMrw8q/Oilw8miQr6j16xt2pwCif1UCYLAiEAgTLn0dz499FkJFktGJMp4Rz+HIbWSDCewHeo8VAhexo="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.19", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.19", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.19", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.19", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.19", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.19", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.19", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.19", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.19", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.19", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.19", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.19", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.19", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.19", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.19", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.19", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.19", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.19", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.19", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.19", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.19", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.19", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.19", "babel-plugin-transform-regenerator": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-preset-es2015", "version": "7.0.0-alpha.20", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-n2h5BtiQPDbydvn6bmFX76qhcUeB2tv8JXyBCuRL1F591wVuZi1gvWocbJ3pBRQ0wR76zjhJmM7hYAvVtrGjlw==", "shasum": "381ee799959462309c0ff5850900b1032f1da409", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsc3a4Jkh5GrAZXBPnY81b1x0M02CZmgQ+PjCK6YNG0QIhAPCLP1V9BgHyXUfPLFW4cgXGA0fjY7+QQlxth2apbUY6"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-alpha.20", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-alpha.20", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-alpha.20", "babel-plugin-transform-es2015-block-scoping": "7.0.0-alpha.20", "babel-plugin-transform-es2015-classes": "7.0.0-alpha.20", "babel-plugin-transform-es2015-computed-properties": "7.0.0-alpha.20", "babel-plugin-transform-es2015-destructuring": "7.0.0-alpha.20", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-alpha.20", "babel-plugin-transform-es2015-for-of": "7.0.0-alpha.20", "babel-plugin-transform-es2015-function-name": "7.0.0-alpha.20", "babel-plugin-transform-es2015-literals": "7.0.0-alpha.20", "babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.20", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.20", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-alpha.20", "babel-plugin-transform-es2015-modules-umd": "7.0.0-alpha.20", "babel-plugin-transform-es2015-object-super": "7.0.0-alpha.20", "babel-plugin-transform-es2015-parameters": "7.0.0-alpha.20", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-alpha.20", "babel-plugin-transform-es2015-spread": "7.0.0-alpha.20", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-alpha.20", "babel-plugin-transform-es2015-template-literals": "7.0.0-alpha.20", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-alpha.20", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-alpha.20", "babel-plugin-transform-regenerator": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20", "babel-helper-transform-fixture-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-preset-es2015", "version": "7.0.0-beta.0", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-kZpx+EqkhPi5RIcy16chPlFsk4UVJkTMV+RBYgcPkANBKOztaFsL4U3BoWQetq9Ht5V9+7hQ4H+LiDx18HubhA==", "shasum": "7588ff331da276b1f9bd9ffdd1417fa7adc15513", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEO7ri09CcCbY1iAVVA61tED5AMxjTte4Xafuoe7JY71AiA03DU+AfPjl/YFVBaiAd4nI4OB4DDGvPLAqVaTtreF0w=="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-beta.0", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-beta.0", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-beta.0", "babel-plugin-transform-es2015-block-scoping": "7.0.0-beta.0", "babel-plugin-transform-es2015-classes": "7.0.0-beta.0", "babel-plugin-transform-es2015-computed-properties": "7.0.0-beta.0", "babel-plugin-transform-es2015-destructuring": "7.0.0-beta.0", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-beta.0", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.0", "babel-plugin-transform-es2015-function-name": "7.0.0-beta.0", "babel-plugin-transform-es2015-literals": "7.0.0-beta.0", "babel-plugin-transform-es2015-modules-amd": "7.0.0-beta.0", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-beta.0", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-beta.0", "babel-plugin-transform-es2015-modules-umd": "7.0.0-beta.0", "babel-plugin-transform-es2015-object-super": "7.0.0-beta.0", "babel-plugin-transform-es2015-parameters": "7.0.0-beta.0", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-beta.0", "babel-plugin-transform-es2015-spread": "7.0.0-beta.0", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-beta.0", "babel-plugin-transform-es2015-template-literals": "7.0.0-beta.0", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-beta.0", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-beta.0", "babel-plugin-transform-regenerator": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0", "babel-helper-transform-fixture-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-preset-es2015", "version": "7.0.0-beta.1", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-CNmAX4iAwpQgSJdr3rUoExI6+Tdk/EaCpGkiFJLgc+h0WGTn+LRa2sG7oG/Dmkwzegt+r5H6fhUejp5Xwf46lQ==", "shasum": "6e653df9f80d29da4f831d655d819d25828b9e8c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsidYOMnwAOuqaEFLxQmu2QmSorwb/fUVFy7C/L9U1FgIhAKAXK5ABEQetDneePGAY6/bszuGzKam5yyciBqRyemsJ"}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-beta.1", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-beta.1", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-beta.1", "babel-plugin-transform-es2015-block-scoping": "7.0.0-beta.1", "babel-plugin-transform-es2015-classes": "7.0.0-beta.1", "babel-plugin-transform-es2015-computed-properties": "7.0.0-beta.1", "babel-plugin-transform-es2015-destructuring": "7.0.0-beta.1", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-beta.1", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.1", "babel-plugin-transform-es2015-function-name": "7.0.0-beta.1", "babel-plugin-transform-es2015-instanceof": "7.0.0-beta.1", "babel-plugin-transform-es2015-literals": "7.0.0-beta.1", "babel-plugin-transform-es2015-modules-amd": "7.0.0-beta.1", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-beta.1", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-beta.1", "babel-plugin-transform-es2015-modules-umd": "7.0.0-beta.1", "babel-plugin-transform-es2015-object-super": "7.0.0-beta.1", "babel-plugin-transform-es2015-parameters": "7.0.0-beta.1", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-beta.1", "babel-plugin-transform-es2015-spread": "7.0.0-beta.1", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-beta.1", "babel-plugin-transform-es2015-template-literals": "7.0.0-beta.1", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-beta.1", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-beta.1", "babel-plugin-transform-regenerator": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1", "babel-helper-transform-fixture-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-preset-es2015", "version": "7.0.0-beta.2", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-fZcnifaIHkrU+EMmQ0TwMhdYW8toGDNUY10fPXyoq9gLr20AgsFTd96h93dS7Gn9DapXAcaGR4EjvqCAWhF4Qw==", "shasum": "90c513c92e3dcccd3fa79fe7d22925b1e0f0c76d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeaReHWjFp97O41Re93fn7l6VyFU8Htdr3eyQ7qL/sDgIgRDLw/GSxpu2vWSFWneBpfqUX5ifbLVs/L6kIV3gOht4="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-beta.2", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-beta.2", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-beta.2", "babel-plugin-transform-es2015-block-scoping": "7.0.0-beta.2", "babel-plugin-transform-es2015-classes": "7.0.0-beta.2", "babel-plugin-transform-es2015-computed-properties": "7.0.0-beta.2", "babel-plugin-transform-es2015-destructuring": "7.0.0-beta.2", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-beta.2", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.2", "babel-plugin-transform-es2015-function-name": "7.0.0-beta.2", "babel-plugin-transform-es2015-instanceof": "7.0.0-beta.2", "babel-plugin-transform-es2015-literals": "7.0.0-beta.2", "babel-plugin-transform-es2015-modules-amd": "7.0.0-beta.2", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-beta.2", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-beta.2", "babel-plugin-transform-es2015-modules-umd": "7.0.0-beta.2", "babel-plugin-transform-es2015-object-super": "7.0.0-beta.2", "babel-plugin-transform-es2015-parameters": "7.0.0-beta.2", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-beta.2", "babel-plugin-transform-es2015-spread": "7.0.0-beta.2", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-beta.2", "babel-plugin-transform-es2015-template-literals": "7.0.0-beta.2", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-beta.2", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-beta.2", "babel-plugin-transform-regenerator": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2", "babel-helper-transform-fixture-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-preset-es2015", "version": "7.0.0-beta.3", "description": "Babel preset for all es2015 plugins.", "deprecated": "👋 We've deprecated any official yearly presets in 6.x in favor or babel-preset-env. For 7.x it would be @babel/preset-env.", "dist": {"integrity": "sha512-rqN2pJJX4J0cprENJCtmDbE25mv2SInSpAcM5BGPGhZNXBffiMCcZ+L5Z1/XC7xLYodvp03pHrKOwEHX91QPwQ==", "shasum": "544428ac048e0bc227425f3fa2dcefee7117767f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-es2015/-/babel-preset-es2015-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4KS8nDhhXlhIEZVW+h09/DPVHZ4IIow+SHC6YdWeNSwIgKoIzDOd19N0p5sPWUQEcpJjGo8CWuAsQgRSFw55/Glw="}]}, "directories": {}, "dependencies": {"babel-plugin-check-es2015-constants": "7.0.0-beta.3", "babel-plugin-transform-es2015-arrow-functions": "7.0.0-beta.3", "babel-plugin-transform-es2015-block-scoped-functions": "7.0.0-beta.3", "babel-plugin-transform-es2015-block-scoping": "7.0.0-beta.3", "babel-plugin-transform-es2015-classes": "7.0.0-beta.3", "babel-plugin-transform-es2015-computed-properties": "7.0.0-beta.3", "babel-plugin-transform-es2015-destructuring": "7.0.0-beta.3", "babel-plugin-transform-es2015-duplicate-keys": "7.0.0-beta.3", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.3", "babel-plugin-transform-es2015-function-name": "7.0.0-beta.3", "babel-plugin-transform-es2015-instanceof": "7.0.0-beta.3", "babel-plugin-transform-es2015-literals": "7.0.0-beta.3", "babel-plugin-transform-es2015-modules-amd": "7.0.0-beta.3", "babel-plugin-transform-es2015-modules-commonjs": "7.0.0-beta.3", "babel-plugin-transform-es2015-modules-systemjs": "7.0.0-beta.3", "babel-plugin-transform-es2015-modules-umd": "7.0.0-beta.3", "babel-plugin-transform-es2015-object-super": "7.0.0-beta.3", "babel-plugin-transform-es2015-parameters": "7.0.0-beta.3", "babel-plugin-transform-es2015-shorthand-properties": "7.0.0-beta.3", "babel-plugin-transform-es2015-spread": "7.0.0-beta.3", "babel-plugin-transform-es2015-sticky-regex": "7.0.0-beta.3", "babel-plugin-transform-es2015-template-literals": "7.0.0-beta.3", "babel-plugin-transform-es2015-typeof-symbol": "7.0.0-beta.3", "babel-plugin-transform-es2015-unicode-regex": "7.0.0-beta.3", "babel-plugin-transform-regenerator": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3", "babel-helper-transform-fixture-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2023-07-21T15:42:24.495Z", "time": {"modified": "2023-07-21T15:42:24.495Z", "created": "2015-10-29T18:21:53.547Z", "6.0.2": "2015-10-29T18:21:53.547Z", "6.0.8": "2015-10-29T18:48:27.831Z", "6.0.11": "2015-10-30T00:13:50.755Z", "6.0.12": "2015-10-30T04:55:51.102Z", "6.0.14": "2015-10-30T23:43:21.299Z", "6.0.15": "2015-11-01T22:10:41.187Z", "6.1.2": "2015-11-05T11:11:55.904Z", "6.1.4": "2015-11-11T10:45:42.118Z", "6.1.17": "2015-11-12T21:43:43.982Z", "6.1.18": "2015-11-12T21:52:51.770Z", "6.2.4": "2015-11-25T03:15:45.413Z", "6.3.13": "2015-12-04T12:01:00.518Z", "6.5.0": "2016-02-07T00:08:20.892Z", "6.5.0-1": "2016-02-07T02:41:41.482Z", "6.6.0": "2016-02-29T21:13:06.311Z", "6.9.0": "2016-05-17T18:49:45.549Z", "6.13.0": "2016-08-04T23:35:06.083Z", "6.13.1": "2016-08-05T04:12:19.892Z", "6.13.2": "2016-08-05T13:51:37.830Z", "6.14.0": "2016-08-24T23:40:58.243Z", "6.16.0": "2016-09-28T19:38:59.598Z", "6.18.0": "2016-10-24T21:19:11.304Z", "6.22.0": "2017-01-20T00:34:23.215Z", "7.0.0-alpha.1": "2017-03-02T21:06:06.356Z", "6.24.0": "2017-03-13T02:18:17.092Z", "7.0.0-alpha.3": "2017-03-23T19:50:02.722Z", "7.0.0-alpha.7": "2017-04-05T21:14:41.331Z", "6.24.1": "2017-04-07T15:19:43.605Z", "7.0.0-alpha.8": "2017-04-17T19:13:31.671Z", "7.0.0-alpha.9": "2017-04-18T14:42:39.364Z", "7.0.0-alpha.10": "2017-05-25T19:18:00.899Z", "7.0.0-alpha.11": "2017-05-31T20:44:06.327Z", "7.0.0-alpha.12": "2017-05-31T21:12:20.579Z", "7.0.0-alpha.14": "2017-07-12T02:54:35.895Z", "7.0.0-alpha.15": "2017-07-12T03:36:52.861Z", "7.0.0-alpha.16": "2017-07-25T21:36:29.177Z", "7.0.0-alpha.17": "2017-07-26T12:40:18.386Z", "7.0.0-alpha.18": "2017-08-03T22:21:46.161Z", "7.0.0-alpha.19": "2017-08-07T22:22:29.276Z", "7.0.0-alpha.20": "2017-08-30T19:05:03.748Z", "7.0.0-beta.0": "2017-09-12T03:03:19.293Z", "7.0.0-beta.1": "2017-09-19T20:25:03.106Z", "7.0.0-beta.2": "2017-09-26T15:16:29.111Z", "7.0.0-beta.3": "2017-10-15T13:12:51.746Z"}}