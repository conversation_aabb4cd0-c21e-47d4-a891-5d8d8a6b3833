{"name": "ora", "versions": {"0.1.0": {"name": "ora", "version": "0.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "231ce3d0b0c1c90d3e300d4c6da789aa732d6616", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.1.0.tgz", "integrity": "sha512-umQl9AtNLdQ3czmOZMmVvA00ogQaXrTZ3gnDT+BspHrDkQnMIGgo6zNCD6p8stcOEoZMupkq6T9jTAyNI3ll7Q==", "signatures": [{"sig": "MEUCIF6VPf9vgyg+/bneO3XiPFmthZKph+qsZlH/MKuX2DGXAiEAk9/bApUs3LvYyhVugUSaezQ0xeIMUBdio6WIjPpR/UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "231ce3d0b0c1c90d3e300d4c6da789aa732d6616", "engines": {"node": ">=0.10.0"}, "gitHead": "48f58e1a976e43e16badd843e1c18c0cb692cd19", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/ora", "type": "git"}, "_npmVersion": "2.14.12", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.3.0", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.1.0.tgz_1457030823146_0.7543118973262608", "host": "packages-13-west.internal.npmjs.com"}, "contributors": []}, "0.1.1": {"name": "ora", "version": "0.1.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "95aa523e2ca0d4386049c361328fdd85769a517b", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.1.1.tgz", "integrity": "sha512-XiRnnTKYb4eWR7xm6yuYUdL3I9r0/zgvg3oChom1FJ2BxgK/6zXGETfIMGU0I6UlTQKY/peD/22svdKidzy0hg==", "signatures": [{"sig": "MEUCIQD6upMFPedsQFCmWFJgLEx5NdEo6NO+zle1X/Mi0ycCrQIgEPaAo+wYjRZwAhmFaUfv3rHHnD7u6nWqPsQKtVBG2eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "95aa523e2ca0d4386049c361328fdd85769a517b", "engines": {"node": ">=0.10.0"}, "gitHead": "2a1820f3392339a70292a02875b46027a13d868b", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/ora", "type": "git"}, "_npmVersion": "2.14.12", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.3.0", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.1.1.tgz_1457031107581_0.8343201945535839", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.2.0": {"name": "ora", "version": "0.2.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "46fa9c782ddcc0a7dbff71f86002426c5a0a9626", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.2.0.tgz", "integrity": "sha512-3Fg6mZcpBvFS37N2ZvFFTuGqZFWdLDqBAOYMcDMNewbgfcTnopmk3m0JNRPAix/BSDstw4iBik7pv2kC4LMUTw==", "signatures": [{"sig": "MEUCIBYcuXrboSnPu7//xMYftRUWowAk6t4pcXmhfrWdraimAiEAmpeQJQum+8exdGEizy5dUITNn7CvHqArhCIUnyMZvw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "46fa9c782ddcc0a7dbff71f86002426c5a0a9626", "engines": {"node": ">=0.10.0"}, "gitHead": "623abe3ef04df8b3b7aafd49b3112bf563de4b36", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/ora", "type": "git"}, "_npmVersion": "2.14.12", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.3.0", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.2.0.tgz_1457372112004_0.005990452598780394", "host": "packages-13-west.internal.npmjs.com"}, "contributors": []}, "0.2.1": {"name": "ora", "version": "0.2.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "a5cb2cadd8c9a74dd417b89ce6d5d34429a80759", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.2.1.tgz", "integrity": "sha512-W5DCCISzwnRSz6xy714YS2PrDXjtFXXx+bJy4fNjCmGV1/88hFs5aeAFwYGc+MJJawyExRln3Lfsy2uzEHBdsQ==", "signatures": [{"sig": "MEUCIQDAyMVGel6TcCgvIB7FaXxIKeGyl1P8slHLxlHFgXAN+AIgHfVb50CkO+KyXD/yoY9umyXjxja6Vh56V3jS3krbv58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a5cb2cadd8c9a74dd417b89ce6d5d34429a80759", "engines": {"node": ">=0.10.0"}, "gitHead": "ca4e8eaaee67010fab34f02a1031aafa27ebc0a8", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.3.0", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.2.1.tgz_1459148236299_0.6098175041843206", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.2.2": {"name": "ora", "version": "0.2.2", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "5d9d5791312ee6669cc2fc26f24c93f934004505", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.2.2.tgz", "integrity": "sha512-PQU223GtX7sqnp8BBzoXPZgqNsaUvA2MigkbpPdTyC7bm1JJTPmFbXAVvS4zeQQ4ZEFnZYkgmwjQyu/3Mk+rLQ==", "signatures": [{"sig": "MEQCIH7gdBrjhm+3Xrq5WFtzHIqAQP1PJt0EEatK66EIq7qTAiAj4Tym4E8xakn+hhE+Y8kKHpXas5f6eyrQPMBn3BFfBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "5d9d5791312ee6669cc2fc26f24c93f934004505", "engines": {"node": ">=0.10.0"}, "gitHead": "ddcafe31169a0b72eb165967b7a5198fba9d5b2e", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.2.2.tgz_1463651835053_0.058313180692493916", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.2.3": {"name": "ora", "version": "0.2.3", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.2.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "37527d220adcd53c39b73571d754156d5db657a4", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.2.3.tgz", "integrity": "sha512-MYGyg17e2GcoDlFrAP39zu4nrAQ+STzl4fosWjR8vAlT0a2wKuuAGZTecffdVLPsnEfxXVlrUcDZ1DU5skr+QQ==", "signatures": [{"sig": "MEYCIQC8Pe0SaV45lUDGkj1O036uG9Aj32L5xHf6j1bcK4UwxAIhAPuS8pTCdb9L4ayyc8tbhYYPMKKahvN6NImpLqiUI4nH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "37527d220adcd53c39b73571d754156d5db657a4", "engines": {"node": ">=0.10.0"}, "gitHead": "da8a5c4349af2f71d6bbf057950c7e749e0ff6eb", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-spinners": "^0.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "hook-std": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.2.3.tgz_1463655723889_0.998596457997337", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.3.0": {"name": "ora", "version": "0.3.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "367a078ad25cfb096da501115eb5b401e07d7495", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.3.0.tgz", "integrity": "sha512-Ez3H81qHXy4hOL45ERXyttL0IIZM1hD/veopJFZYHdtzZzyVPUNjLR2yfNPBKlx9rjxnWP8Qt2n9Z/D5/DPV3Q==", "signatures": [{"sig": "MEYCIQCszZyFHO3B0Ja4Yt5fcFKmbmjlqGp5UlpWDOB+O29uAgIhAJUzUU7/aZOs2ef61JadWItwT1dnwRrAncMCvnDtop1n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "367a078ad25cfb096da501115eb5b401e07d7495", "engines": {"node": ">=4"}, "gitHead": "f03a53abde328a14dcff2d8087d02f0ef64699c0", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.4.5", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "log-symbols": "^1.0.2", "cli-spinners": "^0.2.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.3.0.tgz_1469896922097_0.1848164456896484", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.4.0": {"name": "ora", "version": "0.4.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "fccb5a82e169ccd27c491bd00cc1924ff7a390bd", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.4.0.tgz", "integrity": "sha512-GruqDVgQNvHre5D4BBAjgw18J/kcKv8OKBhF6BlADfZR6aN4NiOyEduuZUUrOzqy30/XTmzgWyPy/uV6tsQGSA==", "signatures": [{"sig": "MEUCICh2tHYWPAHY/oIYd4tmXEHrUm0cjoHsWCet5uSVO6kdAiEAjZBIQ76fxZOFv56tCcyyEWFjDdA4mkT1UXVR8KC+TC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "fccb5a82e169ccd27c491bd00cc1924ff7a390bd", "engines": {"node": ">=4"}, "gitHead": "243079276dc3817a25012ebfd45bc01ce7f83dbb", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^1.0.2", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.4.0.tgz_1482009004323_0.09016014728695154", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.4.1": {"name": "ora", "version": "0.4.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@0.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "e268187c8890295f7859ac535fd8ca2c0ff64de7", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-0.4.1.tgz", "integrity": "sha512-MRR9kRGNqXwfZ3o4X9ojd1Czui8gPsq3qxH9yMJWgzkbPo1+rBZOtoidg9RrVA9SAgTSy0EjSgh0cCDUjCz97w==", "signatures": [{"sig": "MEYCIQC8CY929UO9sg6qNYUbfn3wFaeh0lM6VtiQp0bcLNw5kgIhAJPKiZHwZhx95b15v2h1xJv71tWEDaRFydkXcANN18nv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "e268187c8890295f7859ac535fd8ca2c0ff64de7", "engines": {"node": ">=4"}, "gitHead": "4ceeedd51795bb88a8033229d198e70cd8a2aff7", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^2.1.0", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-0.4.1.tgz_1484027084211_0.1974552720785141", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "1.0.0": {"name": "ora", "version": "1.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "7e46db48e5ac74118dc5f128ba89abdf81a02e6e", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-1.0.0.tgz", "integrity": "sha512-RnSEJH/sXuNI7cmM2//QW6sleVUSLC2QZoTtlKaAk5tvvZvXzKz4FEtHwdonRffdc1omh+VJNIhfjj9buOma2w==", "signatures": [{"sig": "MEUCIQDeOeGdcqX9Ih8zpZrWpYFHiTgMFqAJpq6k4g3IcDYnoQIgdRLqQHU2Uw2xnaY4JZr8nW8K0Spc5SW2/Y8CHXFwr08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "7e46db48e5ac74118dc5f128ba89abdf81a02e6e", "engines": {"node": ">=4"}, "gitHead": "05ec2e38c16c88438b8606f3c1785e60247d9feb", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^2.1.0", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-1.0.0.tgz_1484647386578_0.9834036054089665", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.1.0": {"name": "ora", "version": "1.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "69aaa4a209630e43b142c5f7ff41820da87e2faf", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-1.1.0.tgz", "integrity": "sha512-TcoTUuVmYO1hRxTTw1kPBcnGjdg0NwD1C7BFpITYnh/Sbzkc/uWkhzyLD6NFRflb2y5wbjN8x4BloUcoq+sXNA==", "signatures": [{"sig": "MEUCIAuiPTCC0vTUTl9rJsMlA134h6OSNjRlQoT+3R0Qyy7uAiEAieRoal65O8ZBCOkk9gEB/ye33MlrlHz0KlHF0bLbEic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "69aaa4a209630e43b142c5f7ff41820da87e2faf", "engines": {"node": ">=4"}, "gitHead": "f62b052e74142b5d5bd7c658ffc1bbf9a8e22104", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^2.1.0", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora-1.1.0.tgz_1484901230532_0.0683436170220375", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "1.2.0": {"name": "ora", "version": "1.2.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "32fb3183500efe83f5ea89101785f0ee6060fec9", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-1.2.0.tgz", "integrity": "sha512-q9OviUsoaDpwCKPnLXBKijNePrJm7dcrlYK4SIFmVdRyMpD1ACc2O46StenWIpdhp4doKRMYrOEJmwzcHfgboA==", "signatures": [{"sig": "MEUCIQDiGMCr3huHJJ3sHPhYAXHTV/3mxCdvwF7PjZND93WQuQIgEqX5PsenVnULs6ZHTGrdzYxgWgpg5a3npaoVaMhJXGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "32fb3183500efe83f5ea89101785f0ee6060fec9", "engines": {"node": ">=4"}, "gitHead": "4a60987a95ecd53526a74ab62505d321a61220dd", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^2.1.0", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0", "strip-ansi": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ora-1.2.0.tgz_1490362949865_0.0014431958552449942", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.3.0": {"name": "ora", "version": "1.3.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "80078dd2b92a934af66a3ad72a5b910694ede51a", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-1.3.0.tgz", "integrity": "sha512-6DFzEwRJxz7o/0K+7ecOLwSaWT5M0xuvb+1knfQbyi+GFk4O9bMX9NdDizLaORMcEy8kZyu3OjYNFItRa4MNOw==", "signatures": [{"sig": "MEYCIQCdvQbEeLyduBVoSlJeeTKKPvYY5D3+qWCT2DeE9RwttwIhAIHnATX9vOEKxlqsU9+KxPvL7YxGV0L1qTZ88rwOOIKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "80078dd2b92a934af66a3ad72a5b910694ede51a", "engines": {"node": ">=4"}, "gitHead": "ead117684069c70d2aff5c7a9e2f466f81689920", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"chalk": "^1.1.1", "cli-cursor": "^2.1.0", "log-symbols": "^1.0.2", "cli-spinners": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0", "strip-ansi": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ora-1.3.0.tgz_1497465637681_0.7260209184605628", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.4.0": {"name": "ora", "version": "1.4.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@1.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "884458215b3a5d4097592285f93321bb7a79e2e5", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-1.4.0.tgz", "integrity": "sha512-iMK1DOQxzzh2MBlVsU42G80mnrvUhqsMh74phHtDlrcTZPK0pH6o7l7DRshK+0YsxDyEuaOkziVdvM3T0QTzpw==", "signatures": [{"sig": "MEYCIQD7KzMXB28Y1qlHGW+YDG0xCDdAE6w/00l68Uc8gap9nQIhAL1GtoycvmA7atwH5o8US2hqHxyorWgoKzW3wMfrrEkB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "91dcdaae097ac4e81eeb6aac346e5512c8093440", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"chalk": "^2.1.0", "cli-cursor": "^2.1.0", "log-symbols": "^2.1.0", "cli-spinners": "^1.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0", "strip-ansi": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ora-1.4.0.tgz_1517537987786_0.9013640722259879", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "ora", "version": "2.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "8ec3a37fa7bffb54a3a0c188a1f6798e7e1827cd", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-g+IR0nMUXq1k4nE3gkENbN4wkF0XsVZFyxznTF6CdmwQ9qeTGONGpSR9LM5//1l0TVvJoJF3MkMtJp6slUsWFg==", "signatures": [{"sig": "MEUCIHkNKUAm6Ey0yBgb4zXVOPv3QI/PH1lL/d0LmucYhY6FAiEA9gRiQQOoG1PBPvyNNVpcB+gRApkXkTT1+GmcNxon5ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9941}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "6b953514c092f11c81dfbfd215bd63ad0f5c1762", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"chalk": "^2.3.1", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^4.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora_2.0.0_1519231461478_0.7378731403608505", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "ora", "version": "2.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "6caf2830eb924941861ec53a173799e008b51e5b", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-2.1.0.tgz", "fileCount": 4, "integrity": "sha512-hNNlAd3gfv/iPmsNxYoAPLvxg7HuPozww7fFonMZvL84tP6Ox5igfk5j/+a9rtJJwqMgKK+JgWsAQik5o0HTLA==", "signatures": [{"sig": "MEQCIBhQROihgKc+5/EGMWomEdgWInOp+7B+JcvISimFhh0GAiBcHeTmZc9raV8un0lDmgmBRgzYvuOPdyVviLTwTeas2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5rgBCRA9TVsSAnZWagAAv/wP/Rv8tFqApFmffQzVdJyx\noByUzXMHT5T1l5TcSmmstksldO0FLVN/wMcx0xPL8n+kvgiV+p4nBZDBKw/d\ntqwd3Bp/jzhXoWlVtnAsw0J9/4Nqf7PcTMwk6CwOsZ4juCDnx2dJB0WdYgom\nip88qhxmpbBvytjM9zU3OrNkR5EaPfrZtx4OO5jNq+fECwoS4S3jSVbwO3zP\ng5Pj9b7Ha5uKJKvqELts8PM7iFxt/HA6hHW6ri3APHLz4uEiYKy3y6B5Wj4u\nSpJ1lhS6d3dx+QVkgfa0vK9tDSJ8an65RY27HILCSE7LA3wRxQRa25wSZAGB\nMAUUxqX9VVsTKnTP0EbWGGiKlBnPTVt3pG62CFMe05ov5kNX4SX7X8RZC4mh\nta5Az5qekkVVB1g7A1UFSm3AT9B6v++UTB9jjLkB9WJaHr/UwJzSEYy7Ffcf\nyXipmUW4ibjO5ql/K6N6aoNT599vGEWA1nEWk9rsxP8HmyNRLx/3H+Z403op\nWNOz0DJkjlHL07qfamGLnJIg51wM747myrBvqCt+XbAullIGQyv2m0sPc17a\nbMX1+hToaFL8aJUImtLeXYaL+tUp6H2nQfRTcQpUlEJUKQMHQST8wZhlIQcj\n7vT3MB+8ek+m0ZCSHYSFyDsnLR6BGDhiOUbx9RhkblzWmWGVd7hGenzrSR9U\ncKl/\r\n=N2I1\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "fa2f07dd4fb757df4698a4b69f66bc01cfd5be5d", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"chalk": "^2.3.1", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^4.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora_2.1.0_1525069824135_0.18047202273809693", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "ora", "version": "3.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "8179e3525b9aafd99242d63cc206fd64732741d0", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-LBS97LFe2RV6GJmXBi6OKcETKyklHNMV0xw7BtsVn2MlsgsydyZetSCbCANr+PFLmDyv4KV88nn0eCKza665Mg==", "signatures": [{"sig": "MEQCIA74pul9SUW0KhGcLWhg1WXRRqTs8WJ0MKJ582jrlgZwAiASjoczy9rBGRGdwG9WH5rx4EsgD8GBIPrwfhrAT96iuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTl9UCRA9TVsSAnZWagAAzJ0QAI+wDw3/eP4YuvwlPgan\nw31owFe5XarXKf3b93UkrHcrvmOa8n1VR0A6wONCml2LTWQIIVmRGq37onEh\nfzyUBjHXnK7VMoAYVqDwDaWGk9Sn/cRFM6+5TWRCaeZhEk24ONoZXrBz5I9T\nO+AvL8M3NTfGEmtT0QLYbJm9ds+vzbEkc/pXFaCGcaEnnVOGKWYk4QVN80m2\nTXQlCcRfDV6/kAGWbMHwtJt/pkpQLM/5ABQyHsarw/wFSP1pvJKmmhUB0I/5\nns0as381n10bKdNAbPMmaU4saHIUB5sjXOajhQ+eJAyOrdLRZkEU03BS94db\nJgVA7gTmSMl2eyWEktVDUtXDf5kl7hPK6swk6HVAAJ6930S08T99Y35mFBcS\nO+BtoeeFDR55YqhvS+yaBlvX8QcYxkkliSCCahVpr7R6YkRTdCpRIzrsspPe\naPhnkj4qVu6knhJqe1vE7116WytxPQ2yV0G+XCrfdO5ephxfdJFJrHAjbCN1\nN+tXamZpCUDCsMEOSqZjUEtkhzqU0FBcZlGlHaFdZ3L3GBjIAq9y9QL2VOKB\nr2kFeC4jTBj7jYpN1CXxviO54TXqgEuwlRjhMFo/6/vLhcfaPvCaHdEvyscZ\n+MDHcX+TxUwDgiijRuHTEWi9Gb73C0h92LkwPbojveNp+87wW6lmR1tFFyRC\nQg1m\r\n=wSg3\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=6"}, "gitHead": "cb64942769cacd832694c09fe671e815675e616c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "6.14.3", "dependencies": {"chalk": "^2.3.1", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^4.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "1.0.0-beta.6", "get-stream": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.0.0_1531862868322_0.42487989736392096", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "ora", "version": "3.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "dbedd8c03b5d017fb67083e87ee52f5ec89823ed", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-vRBPaNCclUi8pUxRF/G8+5qEQkc6EgzKK1G2ZNJUIGu088Un5qIxFXeDgymvPRM9nmrcUOGzQgS1Vmtz+NtlMw==", "signatures": [{"sig": "MEYCIQDJ7a+TpntTa5hIGrSPI+iunrS1o3C8Fxzh6G+0/4fO6wIhAOTzXS3BcsjCNw1TLXNuxtItwRCQPwRtK3VnnXszenYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYQbgCRA9TVsSAnZWagAAOkoP/jpES3FyK/d/OVUxKQYk\nkmam7exn1NJl1AT13G7XJ7AilowYu7hsqg8VBaFxEwAreAH8HHwrdAuxO3bx\noE5NiEk9ae3iwre2FEvypmMYHaVsQJrH+9pOroIe+cZcXM/3g5Nyc93zkgKq\nK2iHpBwTM+V8xMoITtGwqJolj1pYGx8WKDCh40zXNHGaKF0QpzlnR8jgRk2U\np3b9UX73Aa01nk1YtTEeGiGVNl7JhTG6/tRSD46EP5eX5aKmZYJGhEAHMv3U\nYHzkoS80c0HJ0ztBPzblPGHQtH+ivGNPRmnyEsNtb20gP18wpqqXJPJJnoPf\nHCc2npz1KKYRN9I+CmL7aHN5hWLLJd+X/P4FETeec3z/qJCKyObptk4jksCr\ny2UfkCP1OawGsKEM6azUF/39oVKftNsp9djApkoQpb9QKmU1RAsSu4NRN/Q9\naTCWgJ430pqMmzsdIR/D7gahlXpD9dbj7qDZ/lYUMcpJdQHtkdoXl5jP/lSj\nWUlrrKMrsdeSIYo437a5aCLqYeoNh3mggp16uRUWkFQxfyWKyN/5GwI4PB5B\nbPqd/MqO+VCUA+G6LeED+ZEhyluPnL8CxlrDDkjFjDWYNMkw6xAzwRIpMsQT\nJwf2aHK/tLSeY5brZoaw2umQuKw9cDgD2M4HGiMAmTZeNNAijWBq2sXnxISd\nXBbk\r\n=JEZ7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "3f5ae20d8036dfa545608910aa59c66325145260", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^5.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^1.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "get-stream": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.1.0_1549862624351_0.07022497080948908", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.0": {"name": "ora", "version": "3.2.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "67e98a7e11f7f0ac95deaaaf11bb04de3d09e481", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-XHMZA5WieCbtg+tu0uPF8CjvwQdNzKCX6BVh3N6GFsEXH40mTk5dsw/ya1lBTUGJslcEFJFQ8cBhOgkkZXQtMA==", "signatures": [{"sig": "MEUCIQDHkiP1mGTlP2frawp5Ccir+Fxvho+DhYy1HqizFsUfmQIgaxMp4U1aU4VgXKDPowvAranQoy4qSyFj+/2txtTnoo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcetmCCRA9TVsSAnZWagAAZhYP/0XddlhzWayj4LVk6Khs\norM+j2+0NqEGrhZE1jp7/fRVhZmHRiNnSqnpb2kTiuQZG518dYBfsaqhe8aR\nfIuHs0N92CpMnm7eD2OkcfJ54SWlOQ6wOUdYQWeA46UAB0UiZCXFeRzwkShD\n9Xy9e78xZ9qTCUMKU501gpsNXrlnIM7Dw4M5FgzGy7WiXZFgaOMhXuKLvwBC\nvey4orUAJ6z/tEeMx6rOiSy/x7/sgAI4NKJmrzKSWvreTWHIwpm23/6fmynJ\nG6g8h4NMDp4KkU4QVcZbEyGz9XtFPskIxUjh6QEN9UeLaHzXCyg+k8Gro5XL\nxaZTYjkRMin1hbt0xUOVeYPOuL/HgCiMYiph6Y2Bke+F4jxpU5cKlyIcUUcu\n8SQ6hlx3FF34qh8HAXQFsH9bJ2tI/kWr/hGd+EkxurGm/GVF/mO1oGfCLLTR\n86TRUxeq7hJR7o70sFnS76UZqhKjz5k/7JMs5MQWiGXqWNuMeQWKPZZAQfn3\ngssBlqIdieARKuUJqPbXnutObu4Ei80gWC5eNG6W7+nhliYjqLr3lkh/3+u5\noAw3oC3oMzir4BeWRg6UZF73B6yAFsQljZ6GkzUWfbFSbVdoRGS3QK5cpQsV\ntnLbL6LhPnxDWZ8HKMgRN/bTaVjrP9xirNN4sbJrHAGQWyYmtOc/Gv2VvAE8\n57xA\r\n=hBYx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "f33e6dcfc3172d2fb537d6543e30171f4d056a5a", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^5.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "tsd-check": "^0.3.0", "get-stream": "^4.1.0", "@types/node": "^11.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.2.0_1551554946021_0.07381030507084696", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.3.0": {"name": "ora", "version": "3.3.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "9b3b6b03ba2dac40c7c7ce144cca76a0f1abb351", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-DTFh3rat9CftF9ldG++sa7ci6KocASWvm6aqNzuXnl8uNCkDprM39f8oZwnXnWoNIcbLYeBuPrruQ4+0+Bf0PA==", "signatures": [{"sig": "MEYCIQCa8wYom1mcJ0pvo9gaek7+cYoGwR1s6KOhOUtaAr9ufgIhAPTE9BsGw599TN1xOxTCFavP0lguakJCTVBgD4dR98I5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcow6MCRA9TVsSAnZWagAAcWwP/i3WJmeEZaIjqRZay7uE\nnBoWxhEUeGEsyuBOsoJixmt1fUTlJiHC/r3rqjn1qNdKRMQK60/kzBlVb7Gj\nfXXJtY/VOKixUYMzzQrTsKAZwkuF4FkJR5TjIUeYTiVX1bwMHNJIjM8AdBSc\n4MyTxZ08k2qnGtrl5VffQ7JLC9pD8eB4D5mRc87k22pCjfnFBKg9rnMUZP9j\nHvT1lFu5FLrXj0I9gdrHKif8lDaqL888dE+tZkhCGgOQI9lFbPatA7KaBWuq\naS6TW8wu8UtYqGV9RiDUyDEIz243ZUGmY+OL9phcAK9CQr7Fdpsd2lN5vhjX\n4wHonIMOvIDeYIw03hJRHoZjJyhAREdmbnfH9X5PG6FygTGnuMKNHsDzacoV\nZSygUmWWvDKN938O2adtV9CGG/0/z3dRO/07guZixecAC1f61JBPcXpymqa8\nv3gXBDehn4IxfBABs+u2W7yWuphtUJLVnnIzsqgcWTpgluMfQf8g8wE9tfv7\nCcVoEMpRhMaqaUU3EQsdwEN10WTkPpGisg73w9amCpDyhyCbAufOgJyhXojz\nmdhZ4iuOR3g7In9yaOsRbkLVV863gKmAIKY5e2t+k2duNXmm0mk0kk8N47JO\nb+PsHuGkPNjF2hMUKyBXJYEgG/gf9ITY5zRP6h83BKlIBA3wJUupHX37oE9e\nLuo/\r\n=FXMU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "76828e85c9cccd0bed8364da6114e42a4c3103c5", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^5.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "tsd-check": "^0.3.0", "get-stream": "^4.1.0", "@types/node": "^11.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.3.0_1554189964031_0.20095969955790505", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.3.1": {"name": "ora", "version": "3.3.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "b0da81069b99da75c4cadee2fc2c57d4564da531", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.3.1.tgz", "fileCount": 5, "integrity": "sha512-k80K0Q6hYokgaJoFePrxJfQJeZKZ38GVEI/BLNxAJXtQZGcMc5jLIAO9ttoI8A52wvfHmdySy8F52gz/sfFFFA==", "signatures": [{"sig": "MEUCICAVTz5DDqt3jO4h3q+3ODbqNGBk5wFUiI5Py9yAw9udAiEA68VUUfwH1weXR70FpOuPT7Nyc75sAfTXYiGLjp9F/xE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco702CRA9TVsSAnZWagAA3OIQAIIVOClpFf+vzBZEStwn\nubf86OO0U44aMUjrsMuBoSM4VGaau2ewJB1iVLGHBNNawqz56QpBU/F5mdfM\nl/FAGRdYFf+JzNzVuarkv0KdpFq+JPXf8E4TYVss7ehEGQ5JUlAuB30HQirJ\n3725vcisjyRa+2XWX82WCPQdrOXeNDAnj3TmLDme9ugPSsQVu9L+F1OxKz/X\nf3QmBMjV2vmpbY8fQ9QLdOUvtWUbVzkRTGkZUlQcVKmWlrSosaPvpAC6I6u0\nnNeS4AhQnJfnsYyN5335k53sicu7ceMX5cGIGtL/ZzCiGG2L0sO9VpggABjU\nhx7+ScOLRRLPspGEkqarxQrnmWafsxkn89cu4MOqdDELl4+Uy+FpF5Bvm8DA\nAuLLa2IPcnXeFJyKCXDMYCvo8ugcUAU6wkrzPoDK1Rc1Lyf1r+4ZTcD39PPe\nQIIXqT8NKbyeqysz9URnfCmM2/29Iz0hIW3pXaCKZQy2p6FeWpf+F1sbDH7l\nC/t01b1KJUdsc8vQL4WDMxDiPhCUQoRFj7lw6HyfBTWixOJT+JojmTY4n0bB\nAwN/B6oqbRGPGIAxMNf6122aK3km/uFoXk9H+miO1B/5Rwekm7T0ZcmWeXHO\ny8K9Sb1FvzloeSLjgV6M9ZMefoLe0gnz6avdlZJlK4BnXue0u5azk5dogsVz\nevwR\r\n=FrHI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "37f981ed2e66085e54dce4ca154ee96b15c00105", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^5.0.0", "log-symbols": "^2.2.0", "cli-spinners": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "tsd-check": "^0.3.0", "get-stream": "^4.1.0", "@types/node": "^11.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.3.1_1554234677801_0.8455963191806726", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.4.0": {"name": "ora", "version": "3.4.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@3.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "bf0752491059a3ef3ed4c85097531de9fdbcd318", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-3.4.0.tgz", "fileCount": 5, "integrity": "sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==", "signatures": [{"sig": "MEUCIGLf+PKyM2dTnQnpU0Ow+ZVpE1uGy3dAHzxtxnwMZnuRAiEAxYeD4DhefgaXW/JphSC43PDeJEoLNzwwhfDmC3eoUqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpECICRA9TVsSAnZWagAAgVUQAJrne/nOTnhx1nNaVEc9\nB6Vedb5J1P8gXnKAS2z68hLGP+nn5EmiiYPKDZN9o9s8FTnFuySZUm6NuPgc\nI2jlM4ExLbKtfBjlesXRNm/Ra5+pzKsKjpVTqY8gZF7r0FxfdFqdeaGuQW5Y\nxs2RmMA3vLcbVImzRJQiUC3JKxFrJRV9C48Rs7bDeM2yGxeX8l00n0JMN0j6\nL9pOOfSZhji0n+6hwEcHuczdygmO7uMMawVzC4D/omLZn8U5XJwBY/qpwv8o\nZM0I9O7s/UFw23o12OaZcE68v8k4DcwpOhfZ/NpKU1Tsv7SmYokauWswufm6\nSnmn8JLWL2doaUzxk9XYGGUTKeqNtGIVk01yU6o5zVSMD11JZPXRZeP4tvWY\nQXUt7EsQoBHKVBSM+wRO9LMOTFTIyDk0u7XVY458W72AgQPTIYt8JZ+Dq4Se\nmG0ZJxa2rJ/fJa53WQkHbQE/fjnHDjsFvdLmwSNT4v+3KrrraXn/8BTUT+n5\nvygyp/KCnRoaIs3j6/M1HndMZ/9FjkJ+r8Vlut7mdt4gjC8pxRFANgJ8CDi6\nhRFx7wMb3LIFURnBnRPlJopYVQlLtKiXrItbw2hCahf+bgkVn5e61kOGArPL\nVRGbrFHuH0e3Apdfyz4e/FS8KAuBQq6k9/3gWkYH73pBh4yVFXUCbdFOewhV\nh97/\r\n=S/As\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "042de2bc3a0cae6fad77659d20dfe173cab0a031", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^2.1.0", "strip-ansi": "^5.2.0", "log-symbols": "^2.2.0", "cli-spinners": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "get-stream": "^4.1.0", "@types/node": "^11.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/ora_3.4.0_1554268295655_0.47966699137935587", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "ora", "version": "4.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "374c4ee8c5fb91b5dbcd82de199f188d3e8fd5ec", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-2RaV0LWJgpWEjvpsW57H8pnzdVQJrtAr4VGk9cIqn58ePx5k1b0H3h9DS2Qj4cL1Cm012JSeg+7AcVNsis6AVQ==", "signatures": [{"sig": "MEUCIQCbGgM7RT9tcrQixMIeNJxM1gS3xWtFDpzVYxU3PT9G8gIgR+6rGtiZixR+Ip7V9n5vQUZaUaZ7IjrB6o/RBfvoWbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhze6CRA9TVsSAnZWagAApK4P/0WfGMfdLb1VV4fAIlVV\n+v9tJ+QvPBEL00+rOUVyZhTMlcbdbuDraDTx4FH89lcSrj21X2kIoWqTde58\nExCPPzPMVMuPr0JD4DS1z9eeTAED5zId+6AeLwDS+cINz179QEMQgmV8dPaF\nPe/p0iVrrIDdLzqn4sP37lLfe8heVKO5R85hGXTzVvfPjfkqwLZIZUFGMAaM\nDqpFCXNUmDiKt3+bOkPaOjv19zveJL1RRQdnw8GYb/SXgmHaeNhwMIm5Bov0\nvdMJa/Nm65V+vYEvyRK+eIKjXGkGO3VOBIdREVml26zOR3y9+Qslk6se4Fu0\n+x9NNbMZf3/XPKSpK0e2fVuONB1St+x/McQ80CkOTgKP0cn+UT+/IfGuvioJ\nLfSzY+GSdZxhIBPVYdCVjUvLVfaIe73jl1bKcoWFVY3hkiGLULdXp8Fnicrx\nqw8xj6hXjhE+7DXduwEIBX7wf1eeMhyzZFltzDqi5/lYB8w9OZ5qj5DJBqyI\n0AsGUFSxg4AUiKD3XHQjDe2GMIHI6XEd/lAT9+9ygpahQ2L6Rjb+2epctvan\nj8gCPjqBQb9ZVsLbBhnP3v2cGS29touG80H/QPtgjculQ2ne3KIDsL0amQ/q\nhfkQxW39fi2Iv+/Cq3rJnqBdR2YmCd5JxlL4qgYFTQi/NvwE72L6Iilj86R7\naARr\r\n=Odih\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "b4ed2e8711e4c67ac74c519e672b00299674a70d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^5.2.0", "log-symbols": "^3.0.0", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.4.0", "tsd": "^0.8.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.0_1569142713323_0.06694868752217831", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "ora", "version": "4.0.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "e0ddf8fcdcf28cf714f742f06a493cc84819621c", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-yQv2q0GO8rqP2wzdOxpu0FxUmRg4z/Lw0m6uSpukPJXoOMaQzrIpl+STKHzjryFP5ExQC56+y8+yXPar2iezaw==", "signatures": [{"sig": "MEQCIG5cG9WE3vjYi93s4jalwy8TDTziUMpgVsFj9z6ePIT2AiATh8X1amu1tihl+wru7S2+fmeFR5ewhxsIJRSuZDcuOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiIkVCRA9TVsSAnZWagAAAjoQAKEdixvamkWQw9MPn17f\nwlEfoqSZdBBfe5i5NpQUOxcW+OCknMlYM32noQoSLzsowBKhHg2xynv2DTpt\nZDrKggS8TjQBj+O0FZwzhN4unUSJUyX+b5kKyywiviJe2uv4jNDT39fV14oK\nXPTx7L8T4JLrpuS23aiBHx40uVsNh39L2QJkuVsGuF1UCCzGsnHiEheKO/FK\nUMe296/HdI7RVvZjB17MXUZgDcHEMsPis4FYk8wb+ENG8dA7P/ur3zWHCocI\n/p9Q0sD5Cq2skpS8L4SqQ75mgX0jpCc5tO7sbFQTlek/DEEyELIwMMma1pq2\n0CcNMc9jh78H+M0CY2ync+c1uuEXyn0hxkzz3LxzEo0/9kzR3Ostn+myvCKm\n/pHwHqEqAuJLzokVceh2iHNvMRzaMLyX3kiB18ZaZFjAK4R+7Cx0452jOQ42\nFeYLq4nwotzC3xMtRwcq/OjfZeocqG32ovdmuP5Jlm4XG6ZlpzAZelQ1H+KR\nW6hrl99Mi2HDAYDU3cQiLXcWppABkVP39nsFpbEGRPn9oXRIsmzhbISZjml7\nDH9V5wVR3rt5MTL/H2hN1cQIEiNvtyiJpFfgCDNQPBxIhvu2ctIXODXf1hQA\n12rB4WwbpCgukxZJh3OzEmqyVJT9dyVnKBF3D2Iz78f/C/qnF01kgtIIgcKJ\niSU4\r\n=U5mE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "8779c68012ab0365363a3f9b046a5410256503ce", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^5.2.0", "log-symbols": "^3.0.0", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.4.0", "tsd": "^0.8.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.1_1569229076571_0.41560726452910535", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.2": {"name": "ora", "version": "4.0.2", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "0e1e68fd45b135d28648b27cf08081fa6e8a297d", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-YUOZbamht5mfLxPmk4M35CD/5DuOkAacxlEUbStVXpBAt4fyhBf+vZHI/HRkI++QUp3sNoeA2Gw4C+hi4eGSig==", "signatures": [{"sig": "MEUCIQD/PU53extVT9peseaf29vR+MpAd0OelOYrpb1AgEnbNwIgXu8J1Cn66U84qdnLE+z+y5EZABDg1GGoz9R9UqdY2MU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkD/WCRA9TVsSAnZWagAAgfAQAI8pFHDvgglNlQWQn+im\nVzF2LKdeeC3SGsCzI7nifIc9qKYsRTCOrf52eG+g4wOmqH9M9xzMYf9KeAAm\nAHp4MNtZcShpg1N5d3Yiyr4gie+L/sp2w1VWNvXyGkf9qCF+9a5b4j4aSgTI\nvU8N82Du0IHkzlrXux2KUh7k98kBTs8+HX0/4+CWmKMRNEd2xu8Cz4v2b8E+\nFWlUNLRW54Q+ixHsaPY6mQJTvcN8ilJTSOWQ7fI6q2OjT/YWROyFMgigaWoG\nK427lVumyzj5+L3EvVlKzjg1zcvw2mfc7mlracoPlc3QYwmA8ewLXUs4Inzr\n02p+hiFfZSUaeO0mkzBZd8EjItIHgIQaJPYDvQR8KF67BIOfvIGZXwIFNIRf\nkWlGcCLNcD/EC2dnbK+0srMigu8CcIlb/i+uJc3YBReD08rz8sF35qGn2q2J\nico8omt/7fUa1zRE/Qjdgq5FSb9GCk4VTFcS49RGwaCbp0iNSmpIDOiNOn9U\nWMwKJQlj+b2bBhxkTpBjusY3rLjX2aPVyEPcyYneLEp2Olc7wpffzq3JjInO\nQgS6MNfsOgDgNTspWsGImdRxxCQjQzHONtyYkI0gacYlY7sdUBLE7PmXlVpo\nbtrrk9N7m5gwWMPQOX68UbbaHkGvcDPF5VDS8MCooWhPisszU8jLmYBcQZAu\n3Rcq\r\n=SIRI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "974630f403202d76297ae14c9a82311845327fa7", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "^2.4.2", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^5.2.0", "log-symbols": "^3.0.0", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.4.0", "tsd": "^0.8.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.2_1569734613852_0.5525749507727649", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.3": {"name": "ora", "version": "4.0.3", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "752a1b7b4be4825546a7a3d59256fa523b6b6d05", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-fnDebVFyz309A73cqCipVL1fBZewq4vwgSHfxh43vVy31mbyoQ8sCH3Oeaog/owYOs/lLlGVPCISQonTneg6Pg==", "signatures": [{"sig": "MEUCIAvbIgKj5Cm2Pxic9hqqqe6Ng5KZj/ZFPyvEifT07DqHAiEAjb4G1CmjFh9O5cAHgmIas+eM5BkIQwEKSZQH6X/82b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy9mSCRA9TVsSAnZWagAAcOYQAIKUwG++xVfuRT78uylw\noERRVw1oeQ0Ia/z4KtPGr8tdVJlGk46IByUtXujHgt+iBugzJaQTYUx0ocz2\nqmzGEU/QuscHGz+/Rpq0Cx/p706QeNVEooZ97L9759z/2KSvRlON+YVyP6Tz\nq6L/Ly83EZ4nYGPphnAjRGxIwEBMEY+6TdO3LbUrbtalV15FHBa0z6dIVR6Q\neJ4+Ta+AKzdkzQoBPjVLyoC9pAZnBaREWu5xwbjSBujLFtq0cXkGmu8npz5i\n9ZciiW0yQaj9aXGA6vCHr/HhRGcQo9jS1e0r7gGaH/3O2RNTGkHJIYKktf4a\nMWK1TnX5maH3kdSdel8i+5rQxlChT3YM5yIL2DNqv7iU3UDgDsj8aRtPYgbf\nghHkMdZTRxlR7H7xahaHTuclfTb3bxjuI3yTgJlcMUfBibvG2YGcgDa/SjV1\n3ihQmk+7Lxis/cxCxDO6DDhSmjrhHegGvX4wViIX6VP5Gz7UR1ChlAFVs2SJ\nbHhGN0QGeobmW3nm4niQywddDOQF/1SQgtvWzhH1irvBPbyMqXMgIfnAdQhJ\nopfasNVEcNNVCikEdZi/UKIZ4aNAMXHT+fXFKchYAuYOYfq+ozF8oWcY9dRb\nlEYMLri+jlHBbrpftL0evhn2tIb5wcIfXNP+cNVH3aMrVlusMfgBQmkRAtPx\nrgoS\r\n=ozLu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "1ad2eaca14e95d318a1a1eab504edd55232790a1", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"chalk": "^3.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^3.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.10.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.3_1573640593714_0.3319285039419544", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.4": {"name": "ora", "version": "4.0.4", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.4", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "e8da697cc5b6a47266655bf68e0fb588d29a545d", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-77iGeVU1cIdRhgFzCK8aw1fbtT1B/iZAvWjS+l/o1x0RShMgxHUZaD2yDpWsNCPwXg9z1ZA78Kbdvr8kBmG/Ww==", "signatures": [{"sig": "MEUCIQDuNETVxmbkA0j0HOi5y5SaCo7bpSe0Gs74MW7dbCtVLAIgVav0O1/0YoRp1XrxBysszOA3EziXNlBACCIzGRrLC+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenwsvCRA9TVsSAnZWagAAnc4QAIun1j5qonnwxqygQTI5\nPx1yTUivGmGJVzWSRCZzuGrbxqVIqXjOjYOPyf3i4H9jWIuKz80e7UbXNZL0\ny0GuH0nuu/pnka8JxV2X+7G/VqRKdfHOhO5lw4aEarCq6T7+3mD/ypCOOaEM\nedpgOMYv4ZcOsDP1riEFeaknoN7dCxxxPC39Lsz1ryONtoMH3Q5N827b7IgP\nMhWEBWsymG58yeCkfcUa4Z9z3ZFClPyvPunZG1nTS9NVaPVjSuOvzehAkFc9\nsJgKPQainqxEe9zf3WcYg0LhXK4nwY/GxCPf9uH513fFyzS2CKzFkyUJqWb4\nnkjKi+7mPZgMtJc7wMEYNkGqKY9i7DbCg41EPF9HKvkJJkEQXEffwDeiVARS\nVfvlCcZ9KdkDpLREwH0+AYto0lB8SkKUgl5gFNGk2gBYttL6+vcelw8h1ydo\neIwjzUvKzRN7hDUmxjiv9ZqOHwxT6r9IS/eGTtkRa7bXFxNWzA3Y9IiBnjSu\nr6f77zvvcSu37ACxtRdrqmpFdo5rWD+FPztLFWwgIt4cqD2DSV31ebBLKCKR\nGRZo7GnbMQFlSlPEIRN5Gvg6dbWDSlq/6B45YwgGHu5IUcN2us28LUPnRZk/\nJ1YQ2VDb22x+XdlL2HX2QTF1pQedkp0XTTiAsC7RlBKLtzdCSiWUUs1iOMOY\nkU8Z\r\n=fVws\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "132315d83a9f32a0d83704d7187b6b24a98e3717", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"chalk": "^3.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^3.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.10.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.4_1587481390555_0.9358416251648822", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.5": {"name": "ora", "version": "4.0.5", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.0.5", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "7410b5cc2d99fa637fd5099bbb9f02bfbb5a361e", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.0.5.tgz", "fileCount": 5, "integrity": "sha512-jCDgm9DqvRcNIAEv2wZPrh7E5PcQiDUnbnWbAfu4NGAE2ZNqPFbDixmWldy1YG2QfLeQhuiu6/h5VRrk6cG50w==", "signatures": [{"sig": "MEYCIQDx1rh236qXoBgxkTI78563ZWmxPDx3I40BfVxmjkLqrwIhAPtu6VA+k3teaH0iafWyYXSdKpThy2FMK0OzXGtRAtsR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEbrkCRA9TVsSAnZWagAApPYP+gJEL12bueUe8vg6KkVu\nYdjtCIcAcm7YfE9KOaBywahKLefHP+ibEfmFUOAM4u0OjHXcLOsyiPClFXXv\n3tOd9FLHr/pXQ+IpjsAhZ398jB4iTzqH4VC7A8ktszlkczXob7d737mMi68Q\ng7p11tFAE2KSqENZboW7K9qUpGLC6xDH3ObsrBEiI82FIMxoC97LJripRg/3\nUjlOkWsmDkv89lkH5cykG6IFVavfK/wZSzhnceY08lffA350btDu8qRFqkxl\nfcKmtUpYo292sMschqph5c6TM1Qrpzkd3ouH+V/6aNmoUNLYBMeO1WINd/mJ\ng9wI//xbR6qfrCNmTzVZ6nssKga86apJVwZnQ1fIoW4IEsESZK9jqzhy7uYs\ncCo1YpJ1aB7KpT4k0OhexXnaq0dktbQyptvqgiI5mLozLNOMm853EztdouEQ\n2T80VRn3kKWsS041W5BuMur2ZggnEdT8JPeopKrcfydo5ySaGXaJ8uUa2uTL\noomAyPrz9QcN3vskPiGP7qe089FYsunIZRgURUc1XuGlPmylCBS74r01b5wz\n0xXEF4LTWMxFYdoiyeYilVj3DsARZTlfCzHEY/l4cRrk64v/chFOjSo1kaNk\nfM52d63e3HEBbuh37WavTh08o8EChD5+QFI/JOmPDgO9K8i7OvyCoHUJt/rf\n7yeq\r\n=NsNj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ffcf7e365d3c9df73a4efc0adceda7a6eb4763a9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"chalk": "^3.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^3.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.10.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.0.5_1594997475968_0.6770850179504315", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.0": {"name": "ora", "version": "4.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "1fa5b17d2952ec9651be79ba32a328684870fc5d", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-PhDvXi7I+dLVyFekgqXt0iBDdo5ARzOxJNoj72TysoXkW4/oi1lRaeakr4skciyWgPpk6JPPGwO1V4zPr6YRZA==", "signatures": [{"sig": "MEUCIQCM91+1XZsJzFz7ME5DXbEhVpNlaU8k9n5k9mkt9vhhmgIgdLruMwbuxPJdNsrzOrgPuCX03fcR7w9PNgT9BwaFppw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfK+UdCRA9TVsSAnZWagAA2pEP/2cX6B2bR66nm99IJXJ6\nEck3dWCmFOBAVcoNLKmbO0ruOx/Gxcc7DbMiv1QF6lOwEC6h8HjPfGIAEMve\nOcNC8pmhN8FS/T0RreIJxmdxYxOVJRDYhJJkr+TY1esi4MALwbhN5CZ5GcE7\nm3Z7LC6gaRNN9yqE1QXUUbBz0McSQHmsZ9EVYzfaKcc9J6kN6vOjfBWDAvgI\nwrDbhBOdXp9Lq1lbpu0df3BKUahgDtMs6dU0KtobgkPX/6zjEj+zP45VqjU/\nk00iYsA0+DCMD6EFaLXwHe30vw0GSNFcvy44ip/NOz17qiQpKNCZE7SyRN3Z\nBcgwkA3Xm+DfnpRdj28ywlDqBWMR+iV2Fqns4D9xuEq9uvAno6bk8RELtEOl\neCxAf77rYQXiL6DCFb9cZ/g3KkcCNrWKJkddl0KR0kVljsuT4r073NjakXTu\nmahkEkXL84A7yPOegiAjzcgUntonGOc0kIzfzPvJJi2LSWkSQpwlkne8bf0L\nRo5R8jvICl07nPuTNFc1osyf2easxQLFEyUfeYPmxjMhkYxa8C+3hDJAMIzm\n1yzt7fiikUfaFz5+XBv9kmToL7Z6MQ9rvJRxzqSqwUq4eqem9XJzVDaOvy7o\nIQBcAVThpfU+8sxGzvPqO4sM6fWjEMR2QBwLFlQre6TmlfbwNDetHOg7Wxcs\nJUQI\r\n=+ESH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ccbc3336c7a95182539d041e949db0f2a90909e9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.4.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.0", "ava": "^2.4.0", "tsd": "^0.13.1", "get-stream": "^5.1.0", "@types/node": "^14.0.27"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.1.0_1596712220602_0.737507029969918", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.1": {"name": "ora", "version": "4.1.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@4.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "566cc0348a15c36f5f0e979612842e02ba9dddbc", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-sjYP8QyVWBpBZWD6Vr1M/KwknSw6kJOz41tvGMlwWeClHBtYKTbHMki1PsLZnxKpXMPbTKv9b3pjQu3REib96A==", "signatures": [{"sig": "MEUCIF4tNWSZhX5bmSFp9x0TpibshCg0saZb9cxWBqsTBTxaAiEA+zGcXqjoNpZOeIKRt2grpxFcrNVdljDE4hywLaCkDrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLXDaCRA9TVsSAnZWagAAam0QAIuiif8nEJiWaVeOuJGI\nnsgjA3ie3Ejd8pjBfwDhFyWGZ96HIVZO1eIjsGndzXnXLrmf4GhGjgUfRFPm\nJskrhRZWgMmrmfEM3Yv25mzYEe73t6lt0W2fhN45DUIj+AZO1+VTkA/TQsYg\nFB4X+1ZYRu8i+opIFuADIFIjjnd0uhDGZ2uP2A/UY3nV8ObveJKxrWgcCHsU\nsG1mu8J+s3ImEAU+sTLQslhIMHGHrTf1TxtdCDUyEVXa9kQyMJs+0s/GJ3G3\ne160DFjDICoKOv5lWAzuIGWNKsYR3VI0GU5CsiB1JVF0GWzkSAhJ4kqhqk5z\nR3yknDW6o+3j5FTLglY8i1BK9NOTTlHFZzkaT6ClOVU23QYmZgamzs1Vu3IW\nR0YK9+9nWxOrPVc1VzZkarpf871YZYGJelURtl99WITPIL6ZMwKzAKqnt8mL\nk8LMfzjirNYEDiozric4IRh0C7Woxx3PeZcfa3cl4PfSA2ZMNdGk5gc46ice\nSFpx6Pc30r24+u1tTNzTtuaQhlPWvPga6A+51rhXJZa2/D2aUg9HbdtSJ2DS\n/miSFf2jgMkSYvdV493nO9aOyznU0l9qPhtq1VYWBsuyQflNfkMMwR2zGrBS\nol5oFIS3VQtEZnpBQHrRsbctM/QAxcBLX8dIkKNzRezWRWGI7diLk8/xafKq\nKjfd\r\n=zfsu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "376bdaabc9e80ada6dd14dac28ecb3dd5b14f4dc", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"chalk": "^3.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^3.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.2.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.10.0", "get-stream": "^5.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/ora_4.1.1_1596813529841_0.26777599749794745", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "ora", "version": "5.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "4f0b34f2994877b49b452a707245ab1e9f6afccb", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-s26qdWqke2kjN/wC4dy+IQPBIMWBJlSU/0JZhk30ZDBLelW25rv66yutUWARMigpGPzcXHb+Nac5pNhN/WsARw==", "signatures": [{"sig": "MEUCIGorHWsv2GhJZJB0xIW8prAzdxaYCYmkOqVoM2ThbunhAiEAxolNpQpetkvyMXkAIo3p+e6b7Wmo8zG9lgGWuG3M0Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLXM0CRA9TVsSAnZWagAAIVcQAJ3DQ5ajlvcmYo6ak4Eo\npbBqfTCG0qZ4xEQPP5qSxdsrNSENCfk17MCE30UhO6JNzqSgI4wGM40BQ0qK\nKFYM/eOSBX4ldm0hRPFtSN+uJglQeMHqNZycUNaR1JX36itZoEL7h5dYnIgr\n87rkzFru2lzxm6bmxYcPsayx5jVWzpnleDwPL6liKf09ARUA6/TBsdiJciYM\ni7WzioJda1y5ghXbLN7DUkcu9Fz+1Puvyx/JagvErtGlDvKEu3pwlckopsHg\njGMTKi+25sR/QtE4iO3e0m/HT7RrIWdc1ec53fE9BWfnNLvuN+rB8rdqV0D8\nlYz1oRnBN274SfrjXkkw9uay+WNwQjUBYdQS1LggxnNszAolmP8E1Om50S9a\n0aWr5jJsFVdITS/FipxlQYV71jtHM18yj0DQtb/lkq74aeHCFbBJfpfQWWCw\npGEgrCPoBXTA1dUpalnNTdr3CLwXRB0jYliJYnttKx5VFuizXIEISJ0V03U+\nO1v9kDGnctQ9fh8dk0lTIDNq7+pyKiPQSfKK7B7dz8IAv9PZs+axTqWsDwl2\nYzQVZk0oUTY9AqH2U+9I+VlzlgAe6vi41Jb5T4UGnavOk7FGgPBSkoKFikSr\nfFPJbCt0seT4wjAeOXjLkCiik+zthjQGkPiD/5nzYPRf0rPj28rTsfLxirDL\nxbxQ\r\n=oiNS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "3dc7379b9fd5d2a7a5a6dea80495031c23f66b82", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.4.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.0", "ava": "^2.4.0", "tsd": "^0.13.1", "get-stream": "^5.1.0", "@types/node": "^14.0.27"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.0.0_1596814131751_0.004810034727434465", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.1.0": {"name": "ora", "version": "5.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "b188cf8cd2d4d9b13fd25383bc3e5cba352c94f8", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-9tXIMPvjZ7hPTbk8DFq1f7Kow/HU/pQYB60JbNq+QnGwcyhWVZaQ4hM9zQDEsPxw/muLpgiHSaumUZxCAmod/w==", "signatures": [{"sig": "MEUCIQCQqIu8sNL2DcrcLzCxFBXtTkiyXgHw4qgLbD7s10DSawIgGJUWE3f01mv297rsTIbC0z2lJgtGk+A8iv4I30+NHFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVUjrCRA9TVsSAnZWagAAVRwP/ApNv5GXY6QyiUlVsroM\nKkUvDHaNBasH6mpksaXuXerSFJqhyzxzBj3umcZHK0/SWDJIE1V3a8vtwx7z\nPpKk0iR2lvzeBIYzvgNKhTPswfheF4O0/76tDYf4fA3Feju6GbvSftfkvFeO\nY4Fiu/fDQ9zT4yo/IrT2Ufjrdf4fVQIHwPzgB7b/tFUX8nMT3CETUfHOoY21\nFADjGcbWORdRwOfCnLmZTNlyJy14VhaBc98RnYiuTRWBc9ksiMI6VFUL6mk8\nh9Z5XSKEsk5qXHIFxBhPdkHyx1jQCot33XthODodwlfpO38rjQqTlqZR6qCt\naD2XaEK68s1zU1w/VjcQDSO/lEVgb2tIyKZ1AFSjnr97K6ur1oGTBjgLMOLG\nlLbKePp+5ffMxA9LAhR2Yy4EYvbctfYFTCkwe/ta/L7ywFx6l4g2f9Xks5XK\n0K+qCjL6NTt0aDa5X/Q/3O4EmPV/xxKdqHlYk+qWCBbYmTACvbCsgqBmd92l\nMopAf3JNb4wI8w2RyJScwYi6pcgaSCvaiywH5h5iXcfY4o1D1sg8qDL+YWtc\nj24PHpcQfzu4ihqIaSDOqFTc1p5wa7Z+ekpaS/hr1bQEyVk/5dGb5/YMpVZ7\ngbqCqBI0t5aGxdb75l2GF6k95Vug2Coz1g/ZGAIJB6JCqBai40yoUWzBGQ0T\n2044\r\n=3nnt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6d19ac27466dc803985f7de66c7f1d50965588cd", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.22.0", "dependencies": {"chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.0.0", "mute-stream": "0.0.8", "cli-spinners": "^2.4.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.0", "ava": "^2.4.0", "tsd": "^0.13.1", "get-stream": "^5.1.0", "@types/node": "^14.0.27"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.1.0_1599424747301_0.425446927870206", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.2.0": {"name": "ora", "version": "5.2.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "de10bfd2d15514384af45f3fa9d9b1aaf344fda1", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.2.0.tgz", "fileCount": 5, "integrity": "sha512-+wG2v8TUU8EgzPHun1k/n45pXquQ9fHnbXVetl9rRgO6kjZszGGbraF3XPTIdgeA+s1lbRjSEftAnyT0w8ZMvQ==", "signatures": [{"sig": "MEUCIQCuUdlS+RxXHoWd1XDb7gJteQNWhuMduucT5Yf+up3CDwIgC/1f3s+QJ8cE7SeNE0HvC9FvDBBjvL9Lg25MEKKpxHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6ufECRA9TVsSAnZWagAAgwUQAKNk8pa32VmPe1oaQuTh\nkKrZnSaklZ/e/mn9LcVySYn9oUo/sHJHHg+s+ZsXKePHZ3kjEwps7kUN3xvt\n8dOUtGOP7Y6SHXI7UrovlIyUOmpAGJKcw0xWSr/ugmcdW4i77bmbeJfNA5wC\nUr0vBMDZy5bXqwDqgANknpVlObtYHqvrPL2kPhfgZGZw3TuNq7Jb+r1cEHH5\nrMLbxNJ8/tE5qSu8Mtaq1FxoFNqJTS42DsVZPBpsWyL2oacDSWKxZZuMCrX3\nI9tlw7xbCp7A4TfkB1qQoCK6X6/ZZzvIqjkC/Ad658+nqIwv2AMnuZWNAbSq\nKs3LKYaSMvtSHgb+/wmg7SPqnjEXWRF74zNt3/ZGdC5ts9GN0MJ0rQ0sJvc4\n0sAIrXgSwMQOiAqaye6h0T3CMw0gxa9jrxFW1vQJwAFRAhfoslneZgODeq5U\nuJjhCd9Cp/hE2/6tVPNxjoHBBs6KxD1g5ahupNJsS5mqWeFAFne5dvtmLtfT\n+oDRi75pKWFoYJYttdAM0ncZH6dt9uzPRZtroY7toljrc4+udONhk2bKTHi6\nJjkcV/J0CqLks35KIJKm6e+Sl5r1xHkEZ1F9zZtJdhVCr33tuVvBWz6Bx1+M\nT6W/y7v9YHGh+Z+6KxoH3JfBHJbDdV9wuREW17tsbYbdp2svqUgFrsl1YUi4\naiHg\r\n=skIR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "705a47371084198aa9cb7437b8055be8ec7cfbc8", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"bl": "^4.0.3", "chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.0.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0", "get-stream": "^6.0.0", "@types/node": "^14.14.16"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.2.0_1609230275722_0.0769240930537769", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.3.0": {"name": "ora", "version": "5.3.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "fb832899d3a1372fe71c8b2c534bbfe74961bb6f", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.3.0.tgz", "fileCount": 5, "integrity": "sha512-zAKMgGXUim0Jyd6CXK9lraBnD3H5yPGBPPOkC23a2BG6hsm4Zu6OQSjQuEtV0BHDf4aKHcUFvJiGRrFuW3MG8g==", "signatures": [{"sig": "MEUCIQCWb3QCUfpTk5gAaLOtS8I3ywlcRrBS7HLySCV+p1ZgnAIgYiY+Mi8iAcWw5bhW0WFMzdrHwRcC8g7iKCszb2d2F6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgB//LCRA9TVsSAnZWagAAOo4P/jTwVUxi+Kjvv3bwOU/4\n3e0/4CLdEuWMCq35MxxPXadpw6rO0K5EyN+4SCDaFPmJQ8/kn4buMXwLlKMQ\nsDbJWl5e08ZDzJyLsEAHjTi70unQIag+7ji/VH+7Kqzue1AQIeGdEYrkER0J\njg9gzgI5rXbiBHiIMxlSKq93EX650Mm1E3nYj9lzxephM8IbECixPUJr8Osk\nsm2fBC688xfIlSO5qJEBsUD9zC71X4Aj7dku/QhFrPlH+0R5swAeqnS7rne+\nmNWzn883ojHKpJbiEHVt+6aBkhawFDjde1phZklDcnMsdIuqfJQtjUd1ivji\ndkPDLg3jFhdlwA0Oh1F1SxrSicYkc2fDbuqQM8rsa5VpmLbqY69gW/AXfrgn\nf4bsrOE8o0nVw/Q/48P0DzH0EYvh9m1RaM+EQ5ozAqsL4PPXtCBEUsdjVFZy\nkqFEaAkMHs3rT85kRBP5wUPkGGWsZtkbzI1cIpOvwKgARtNzmlKy4uAP3dgZ\nLcr3egidIbQVhqbONR9kP2H2yJ6ruvHVyyLlZYV7cH+DkhCK3wkVQOfg8byV\neuXY2Vjh8P/bp48Go5OoeZy7izmWInCBZ/+BemlqZqyiv+qSf82AXHkX55VY\n/RNymnAb2FCYce7vXUsZICBxUdpewQKUpqxjxZCy+acMiQPAHwapnlt16b2d\nyN0j\r\n=RjDA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "29d9fcf7fa8e2126a78bd58230f9d918712b59c8", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"bl": "^4.0.3", "chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.0.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0", "get-stream": "^6.0.0", "@types/node": "^14.14.16"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.3.0_1611136970984_0.9740822230365274", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.4.0": {"name": "ora", "version": "5.4.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "42eda4855835b9cd14d33864c97a3c95a3f56bf4", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.4.0.tgz", "fileCount": 5, "integrity": "sha512-1StwyXQGoU6gdjYkyVcqOLnVlbKj+6yPNNOxJVgpt9t4eksKjiriiHuxktLYkgllwk+D6MbC4ihH84L1udRXPg==", "signatures": [{"sig": "MEYCIQCmaMBVZkg4ufU2XDxr/ztrOeu9V83D3TbPOa71Cia3SwIhAMR0PSB9i+XBY/NSYKoAtSuDfMt1oWGXq3MkWPm5zb+O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUey/CRA9TVsSAnZWagAAPxoP/3ZPKsbTGjZjsNKVxumg\nZdDOkMtKV4/2Rpn4Ag0Hsm49PmFNJvZiZDNpuggGlaldBM2dQddfeeHIyR7I\nfVf2gvrEyckiL8IUtFIYiUoNHxDHUl/1RhG7tZAg6XmBdabm9SBPeHMqn798\nozEtpstSUKWf5gxBm/iae8Zlex/L6oXtVhxhAC8Gl7kcgqMSHCFJQK4VNHZq\nvgebj1J2gqPNKoaVWrWXQgCi6FdUH4Qs1tnBWf/J25nusjHHTarHFHYYsYyE\nrTCHUBFDL0poo1pYbKVNTFq1dj/fr69PXF8C9GcJRAeK4hYio24xeJ97D8zo\nVriZWo75j1BLPLjHxTQLMSKnQsJei1RXI6EF9wKD3/nQeIGkMWqpbOPLktCX\nMSUQ6GBNSYviF0DhFEobqco0x2VwgN+luhtFp8D3ZNrHw4a7g6Y6efMIND8C\nIo+0AwZsDgS3TqWYAQhhA4M1sTGqXiLhFNJTl0Mhh1tLELzgDP2VhDey6JgY\niBovEMEFWzE4fBbuWMumfRMgMhmT5g1myKDidP/tM4vbO/flVdPtzu7+wEAn\nIXW6S4W6LSmpZgLIcWHlm4dFVFpFOfVjmkvyK+tjI/0ohAPTK2cUUE1SuV9f\nz0xaxI8PFH2QOUL2qbhHbKS17DZRDz9drvUegpG4begzT39wEKNmdAmAQQQS\nViVg\r\n=8WhY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c7d6dba4a723cfa640e6adb716cb6b5dad2c22d2", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^2.4.0", "tsd": "^0.14.0", "get-stream": "^6.0.0", "@types/node": "^14.14.35"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.4.0_1615981758615_0.5191482313303699", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.4.1": {"name": "ora", "version": "5.4.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@5.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "1b2678426af4ac4a509008e5e4ac9e9959db9e18", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-5.4.1.tgz", "fileCount": 5, "integrity": "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==", "signatures": [{"sig": "MEUCIET9WpZCLTWzfT2Qtgieo4IO5duVicGcREWDgABQy2XYAiEA5gWYiBpxVBjfsPU30NiU16CNfoDEVRddtMT1ontcp/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvx6JCRA9TVsSAnZWagAAn04P/A+AHB41GPUNlPQB2n4U\n0ijOnucAXLP+mcLRBsLsE5oBFEHAlN9565i2OERYSxkQ29nFvOxm4mvdvqlP\n/vX1mU6nz323P1K0bLNRd8ujy/aKvlU9F3rYZO1m8eMFSgvNIn6/39Tz0zJV\ngTvONOIUR1ZeQHxYDG292UyBB6/xiU3t8ynUuc58qZPjC3k2g5JWu2DqFUB2\nfQd0k0mWDM0NZknNmN6CUbb8EaohMQ4TIZtXn9JL/cChmKV/SHoxHazHNsLD\nlz/vN3ze70zSmISkEJicBhwgOZ98qhtjZ/+NrLThp7Fgh1RvYotjPuaOxxIn\nHniC0Ka5YFMMOyYgFhe7H1QK6SUwHRMj//BIJOyvyeIeneeGAmWnBAXsd7KC\nliJAMqKKRfvWSwUJssiJbUB/GIvKZdKd51HVuf4pPJ0I2DshZjsLkHDs3ntJ\nBUMRFr+vgsgrHHRWlmzQheJlfaL+SK3URyCK1V9x7anG4sGYruFImlv9oLww\ns5oow1rKZXCHkzp1Pv1hVYfSN3xJCUi7cxZ1iFHLijsEyMzdicROB0TCIpR/\naeDvwC3oOV8z5Tft43/CZ5/cR8Vc+6xteD7A8mAvRySsOolvoAtkyzJi78RC\nukf8UYv6Xw/4kwb8RFWz2dmgchFxzOArYrheKd60hSZth8i4I08UP+32UXuN\n5qOS\r\n=e5vQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "476935f318868265303d148992fc268639a0d573", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "wcwidth": "^1.0.1", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0", "log-symbols": "^4.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^2.4.0", "tsd": "^0.14.0", "get-stream": "^6.0.0", "@types/node": "^14.14.35"}, "_npmOperationalInternal": {"tmp": "tmp/ora_5.4.1_1623137929667_0.6666678811085194", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "ora", "version": "6.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "4081c50984a306e73133ff224bc9e4ba54d44d8b", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-486vZnogayiKcAZtoE1Vsx8LQh0O3CPIwTadakfbRt8fisAQSGRaeFYPdeP6wX32wMS/baWxCH6ksTDwvxpeng==", "signatures": [{"sig": "MEQCIBe8/o8AvwR3YmH6DxRB4V9D5+YMZ2YbO9jRqt+Yk7IqAiB4rAclZl1ZIfPyLKEA6YB2C3KnVPeS899wb4JXbJ7P1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJBfQCRA9TVsSAnZWagAAo4cP/2XPAyo/FwMisHD4ILz9\nBssijUmPdbuZck9w+dRgn2uH2dea9ab0o4GdLOoT04to4TGvWaq9SAY/YikC\nyvhitSznU9gBW6G3v9v8aJoCK5IE1kp2NjMN97p+Kv38DXFIl7fbK8nqPnFY\nve8gVv41aT84j6fJJQo5wSkQsp7XhV3j6Xg4xzQ6ejitBMrlO+fOPCOT0QeA\n+0J+1RR698U76a1/GNRnrAHrVs0/5UCoXwe9L8Tc8XjZz7/cK7eAsW0oZnj8\ndZ8oSV/2XkFKL659cP/fd8qA+PpZLdW8XxkquaYuia72ezsGvU1HWtchsuaR\nLz01WnY2AlDMoULd0Kp5aVNRLrMpEsREWbDHzHvUG1YxN1aFkBQejI9rFqO7\nOo3UTGUMBU0yah7HlVgJlgZBSac4TAxewwBBo9fa8K9fe9vMn2k2ZTuTagbt\nop1+nsnSWfv/OrHe8ik2mHADz+4fV/5CH/5o0iuFEylzqPE8Dst4+7ekEFzs\nHo2E0O9e2lQHsN3UR78b0wiYDtcW3R22wBAyDfdRGoB+FjXsHG0X4UL83Pau\nlfvQuhRxf+EjgtAbV54CvRkRY7QQXl3r8IwOnRjfZVABE0m+EIYKvy7zEg0C\nzWo2OBiDMQovGrcqTg5laIQFbQAD7guRDF5nwKP8e4WF5RTDMux/zgz8x8e8\nWKo7\r\n=HBUv\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "8364664d579f806ef65d259f5fe99c4bc82ffa5b", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"bl": "^5.0.0", "chalk": "^4.1.2", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.0", "log-symbols": "^5.0.0", "cli-spinners": "^2.6.0", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "get-stream": "^6.0.1", "@types/node": "^16.7.1", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.0.0_1629755344420_0.766827832085599", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.1": {"name": "ora", "version": "6.0.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "68caa9fd6c485a40d6f46c50a3940fa3df99c7f3", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-TDdKkKHdWE6jo/6pIa5U5AWcSVfpNRFJ8sdRJpioGNVPLAzZzHs/N+QhUfF7ZbyoC+rnDuNTKzeDJUbAza9g4g==", "signatures": [{"sig": "MEUCIQCwCGN+gn+m+UC4pE16B1T1jhN2jdukHxp5iL82YoRyfAIgfyr/KXNmZVkb2jQ9HNcqJf+QQov6Y+kp5a9LdXGLAQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP5MzCRA9TVsSAnZWagAANXMQAJM8Bd35pzS19hVA/FL7\n9VhutF8bCzw7jqkB71Ouf03MQNKckkMhWkB4puWFJXaiFMMoGQ1o3AKgDctn\n/R3BY3/N+84DdGcSLpcuTp3KScLIMSF+k6kgVaAAy4Q6NI9VwdrDvbnWgmvW\nSzxPjyygoFKl3DG1wi5Du0OBFdAvngcOnZ/XVJSZ77wuuOr7cKDBT/zgbD+h\npWgTVjUiQrjgv65ndcU7tPD+jbndAVf9MIrt4t2A7bTJIE2KsW3BcP6/fOWh\nJAfVYUJ9vx5HOI0atPNZyF/bXZdBrcdifbYPFvK1shAvmgUVnmW+33B1Dhda\nEjUj62b2rO79zivI1XgDa8Xm5qEgXONlWY8ejOdm2NzJ7qUe+w4dUMPPKgYr\nMiPxOK8sHuF8dcLKnVW9diYDFmaEh0Ak8m6MoBFVbqlLyKAkZd7d5QdXyQ0O\nGU3EqAxrA98iqpBtX7sP+pTwdxVU9no7HlVu5rwr5hywPt8l3L/tFo8H0dZl\nX611t4RaJIuIpYq1PO2maY017qYA5yHEOmHPpObwQklIAjjwx5HOyf5pD/AC\nFkpS+okRVR9f79hRW5HxLeWiQHrdyZAjq9aQNPWY9Oy1TI58ANAqMBDDvtWL\nvFu6XBcqeH2y4xrDHTKZ0VRtTZ8wZISFKmDxeRYlVSpPv+AHXa4KVklaE+9k\naqrR\r\n=Dp97\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c5026b7b411765636ae08a8f4a15789617ba695e", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"bl": "^5.0.0", "chalk": "^4.1.2", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.0.0", "cli-spinners": "^2.6.0", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "get-stream": "^6.0.1", "@types/node": "^16.9.1", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.0.1_1631556403343_0.845007042969252", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.0": {"name": "ora", "version": "6.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "86aa07058c4e9fb91444412d103b0d7e01aca973", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-CxEP6845hLK+NHFWZ+LplGO4zfw4QSfxTlqMfvlJ988GoiUeZDMzCvqsZkFHv69sPICmJH1MDxZoQFOKXerAVw==", "signatures": [{"sig": "MEUCIBE4hpsYD6mr9nNalLXIqiEoYzFyiu1D+GrtPgZmwESjAiEArx2pQIzFAbENHyjjoZBcBIbqeQVoGhmkm75zS6+xE9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE0bdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnPA/+ONzoxXW29Z5i6ZG+1dRFiNmzc3LjZGk1lkOxx87gCtCZ28qC\r\nyUUSwhjPoThnDcn5DxEfZTITyq7H4wXB9hQsqOW7bKAo+4zo9EQHhu8wfXkM\r\njlOfWowLGA6JL+OHbdujJGiGk3WBYPFPYCbA94rLTaW0dmS1CmZZ1t1n9BOi\r\nSOu3T6/IMaimrh4Sfn+tJBnXGkehAyI2HE1AS60WvNYk4oxB86wCyMGpndz7\r\nmOR4HAmr5SiVy829EirhV3iNf5mn2lw4hPypYus/zIgnGFouCGygBk59A3re\r\nyBY4GqCBCjB89Utuuz8NZMJtE/lE4bIzh2deTy5rqaT1ds1OsyXncrgDK27s\r\nHPhL8ceQ/GJk62AjJXgUG8fXFilBmupsYr2nS/z/o3NA0GJIFuKaWWNrQQKl\r\nqR2nMfQsnjoLGPZEKo372VkAZ8gPoGEZOpE8PXagc+68/MgMG7HeKrG/y65r\r\nGTqHuROotdH2K5i6lNolvf96ZHi/gPXaEHu1LzXId9vrZVHaChOROhay3TtE\r\nzxAIwqDi1tyZS9iV2kWO7H04DSQBQ25XW0uOA4BGMB7tePBLJvOxblOjrNDy\r\nzTlCsV9B03ipKIMrEhsZguvniKgLnKvgh3RZCRLWp/Vq1/chccKALrcP516+\r\n8BqiNxVVfyRBs52n5ydkoRfI+ZUlsiMqCLA=\r\n=THW1\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c2400db7a3695c2c420327680aeecda98cefc289", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"bl": "^5.0.0", "chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.1.0_1645430493775_0.10354190168738131", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.1": {"name": "ora", "version": "6.1.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "71e729509b4a782f62e17aba01ae07c8bdb77763", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.1.1.tgz", "fileCount": 6, "integrity": "sha512-MTvBeGEswwM6CjyuidTH4nKdmG8YudnsZZRMdpj4YFfm7qDnvOSTKU2biA3dqdnL5m5CT3R9bWk+M3A8fqtEZw==", "signatures": [{"sig": "MEYCIQCfFQxO2cDd+fKCWdnrOXXBue+cyqqWCh8pPv+a2oQQlAIhAI1lwWtbHqzZ7Vev04tiaF56Phzift3BBE9y/t0NUwbF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuDayACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLMg/7BBNXr7pqkb/VYCj2jNkXvSGfmFBDKve+sVbruvWfNwBgXffs\r\nF+CMVZVAqbg21kUv0kn6xNG74qLOHLwrKjIqLwgmAC01R8EOAdlMWB1pJ3FR\r\npWePjctwd1hXiBZ0rpGe8u99+Kefgnz4T2CM5fhGADNziYO3hSfDOTj1GS8P\r\nUMj2tpbLzKJs6OQUS5OijIig1OXuLFc46PS7TRhyHVFd0ydA+htf+5HbtcwA\r\nGjQGxSWn1umWYK3iFexNhuB+Pm5AEYe6ITg2gAAFcWeKvNijkXiIIyL0yUHD\r\nGN8Ih4y7cz+rLvh3R2lzip+UDNa8vAHXvY2ibu0pKTgqRZchyZLMO6e7/rys\r\nhFjyUEuh1wwwrn9FfAA5nzV78SXh4IcOqf9HHuwtY6no2HJR6KdjWZLvGT6H\r\n4t4qyhy5MjG/Q2UZ0ulUSFyIfmVy5fhvr0GJi/B/vtAiyN0womHyGBj6tbnt\r\n0N7dHEi3y2/JdS1x4cKy/dpaGYBZP+XyIh2BsnbYVDg97zvJwXI1NzhdwfC+\r\n72yQ2uqdCl1qAinsxZs5BV40Vi1WDLwhLnavcAkbHVCS/kfbAOvPOm2Zsm77\r\nLz60ODqgC6ObONFFHTcylg02Vpuz8qYFY0gmRanzAzYKRWwi99y6nPFuETKS\r\nf6T6fzVOyzbb5kIP4EXsHhhsXnYcnngFgEY=\r\n=zb9S\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "1c24e0ff9757606421e02dca0e0177e6ef67f4e0", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "18.3.0", "dependencies": {"bl": "^5.0.0", "chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.1.1_1656239794310_0.23885210581072935", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.2": {"name": "ora", "version": "6.1.2", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "7b3c1356b42fd90fb1dad043d5dbe649388a0bf5", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.1.2.tgz", "fileCount": 6, "integrity": "sha512-EJQ3NiP5Xo94wJXIzAyOtSb0QEIAUu7m8t6UZ9krbz0vAJqr92JpcK/lEXg91q6B9pEGqrykkd2EQplnifDSBw==", "signatures": [{"sig": "MEYCIQDl19+XRRgmsVWkHOV8zEyuShYj45hbmae/2hmh4gaFXQIhAM4K6YEVL4QZLz8LlvzdRc4VuVtI1NyBygs1hV/tN0TJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiujlEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJEA/+LWxSMcAk6bs27IUiZQmt7ZlnSBFQAz3I46eNHFiZY0Z8+Ns1\r\nzth974P5JkWtbKgIH626CCxAzXI7Wt1aDh8JmcJMkBB+QFe3qjm7icaAhU9T\r\nDhSQMYpLpyJssz/sNiJu/5qZ5oljkhMZKAQOuZh6us33Vbg+P66rjtpKuMF3\r\ncTYBVAbXj2nmhpvZmr9tW9qukmaOEExeJ+B0nWz5hcHqRqSNscnX00UKqijG\r\ngekO2F8KZcH+LK9EV9ApS3iAfNxHEbtU8d8Cwqlw/ERHjLLjUOUHEz1tSNyZ\r\nzy57eALqxWhS7zvDVGokkXqu7h10Zf+fjApraiKY3clkxdyoMywF6N8ntqaw\r\nr+5c04BN2iCBajAbjEXjplh8kWELscZJWJqffYG4fj0SA6uiR2kqFhdgRxDV\r\nHCdSrQ3WXhUIaVPofw3DSkJgdZgxb5wuuE+nkhcn2WiwtvSFePqZFPsdv4po\r\n9e+do/3QTq+TR8bU43qrX4rwxfT01SAQ32VQC/B7+Ja7BY9OEaf9PHgyLsXe\r\ns/FjR9lBIqF34rlqWuZ+t8bLXrzvBUt/WlRtqArv3EpsMC1I+Z+uGNmiOmfO\r\n0UZZlcVcSjv4GTvYMO88+wOXcYKb08ITufz1LISnUU1wplgYNaspr98cbfaE\r\nR6MOTVyAf5r8O/5itHbV7P6lrEfDK+PNCPk=\r\n=2DOK\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "702d1feea59d246e990b859de73e4bbe10d5950a", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.19.3", "dependencies": {"bl": "^5.0.0", "chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.1.2_1656371523861_0.47175414231452106", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.2.0": {"name": "ora", "version": "6.2.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "6d727902151d5b1badc599ace4df45a34d53f556", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.2.0.tgz", "fileCount": 5, "integrity": "sha512-c1qb/1rdE+EFDYiLXh10VY459uMh7DN9zlgd8mZJLoeiPpYllN8eAOiih2Rkah5ywxRm5tHN5C9zPheDq8d1MA==", "signatures": [{"sig": "MEUCIEU6gie+tvJccWAc6CWd4H2rgUZO/jEhCPhfv/k6Li8zAiEAlbMTpr1cjsVmS9xVhlxzwa8O+EJG4ColoMqkn3mqeHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFuBCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMVQ//Xffz5g9DD7mHP+dHlcXHxPcPaupM8L03s+ru0XHy8DrcQoc+\r\nsJjivoTzJNUhFCwtuL+5RnAXdsfp41t47BY0cfAqdbfkNEXyEhIcedHbmsdO\r\nosayc3c64G5fEE30EUlRvjN21xDZp0yIBevnl2HfKeA2Bed8Olp8p4HtLeTN\r\nPJKrdPR9ddNE0tmeziU4z3amNsk/o6JvBQbRMIms01Mj9m+NGqW7oXBwPy+M\r\nSGkJt8aXsxoqNQECYv8tU7yu7k+PMPVB0nwDuQ5E2QpE6GerKyVboOYkS99E\r\nickPml892up9uNfny9fQC+/vo7+o1Ms7Wr2OB1RUlJaWiWB/asvoDr+lknWf\r\nVfDhgXB0j/1Ncyek9WEvCw4MCVZfmT1GUOoOYOH5sagm2F9Bwf3bV+t/PY/Z\r\nG+y1UbY1huhaP6zZ73mBnkl2CZ+2sBRxZMpGsSu9NzZx6FjUHsy2YtmXTpRI\r\nVIGLsoVGNK4920MOIv3fnZsL9FkuX8nLulNw3NGX1T1pqzMB/+68/ZRE5LJm\r\nVJzo1kdkb6D2CYyxV3oc6b93j59+Ofmx6E3eP7W9lCdIzHU5cMXHMu1VpyQl\r\nR8EB1/0kkTcLVqPTcYcr/3FNWeq5Z7uFXMZfF5nvFRWuV6Wb5yiJIO2aW71h\r\nHHBPD4Vzd1KVLfJoPhYzv7ptoA0tmVw1Bzk=\r\n=J78Q\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "16cc2113ff4e925f03c872c6691ebb2e64055a85", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "stdin-discarder": "^0.1.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.2.0_1679220801905_0.8643561113867901", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.3.0": {"name": "ora", "version": "6.3.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "a314600999f514a989a0904f5c17c8b7c1f7c878", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.3.0.tgz", "fileCount": 5, "integrity": "sha512-1/D8uRFY0ay2kgBpmAwmSA404w4OoPVhHMqRqtjvrcK/dnzcEZxMJ+V4DUbyICu8IIVRclHcOf5wlD1tMY4GUQ==", "signatures": [{"sig": "MEUCIBJBSP7bgYev87EHHlbokQx14RbKqicCq1YJybNhiZXdAiEA9yrXOdXocT+twxUBrmF0teU024ya19ZKHtAySEIpLoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHU+OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOAhAAlcKneZQB4k7S93umuLAeAIUQ//j+NgCQ+9jcvaB5r2DXV4aA\r\n/Xgwwhu9gDL6DArEHcfMHcHxaE5DjbpdAx66/n7gvg83p52duaTyzw+YUTm1\r\nih4tP/aHdXw5GU63nYaCSjG6EHNRvRwGQeZr9kkZ5osW6hGWI+gkn1dG7QGd\r\nKGgcGU03aYl0jEDGJ11v1QbQz+wPxA/+D0c34MHugujhawI4KaX9ShKkJDqX\r\nlcN9rm3hnUffeWYZk3q9CE/bkn5MubTEbHfX/hOFPC0+2PG+q3TzFcA9dMqd\r\nANIkZu6iY7rCsYec/yCOQ4pNyLYhnOAJpfyBwHLD2YE06V0eMWozZ4G3uVeR\r\n6O/9NgSrnxHK8YLb5rlFTCGaWgdTNN6YK+2H+Rk0Gatc3SP8xbfoenO5lneb\r\n9VVqhWtGu+EObiq88PpQX5iw0gFXFrwNr/kA78CWUhtDyOt3S0A/NV5smXbY\r\nAWxO1R/aa86gPYl3L58mrbD6ad0qA63k7IpaxvgpeWE/KjBqW2ffzbE5JlyS\r\nNIZYxOH6rgUzLQiUTNY6GJvXbTNTghLK7OAdyUUaSBz2zm8cjZzIcIkAJnof\r\n8r8JzXCYVZT6xL5TpAKRiJ/xPvERZc0Q/rskEf03iaEzWpXyrH9gEGUxRgl/\r\ngIIf+rfwnKTY8HPYLRdhSlxTKvxd1+RZDdI=\r\n=/biP\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "287272d8b3b9930614d38311d0445c61a354a285", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "stdin-discarder": "^0.1.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.3.0_1679642510209_0.6711517190885243", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.3.1": {"name": "ora", "version": "6.3.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@6.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "a4e9e5c2cf5ee73c259e8b410273e706a2ad3ed6", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-6.3.1.tgz", "fileCount": 5, "integrity": "sha512-ERAyNnZOfqM+Ao3RAvIXkYh5joP220yf59gVe2X/cI6SiCxIdi4c9HZKZD8R6q/RDXEje1THBju6iExiSsgJaQ==", "signatures": [{"sig": "MEUCIDdw87UvQXKxoXx08gr3ZhrDU97x9tM94wBlzxR6f1RMAiEApJLBBSdXMFL7ozPEyt9VF5KbhQ6zPvNL3XqGZdJaiFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26735}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d802c38b33bbeb3e2f598c4522255c104a03a0cf", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"chalk": "^5.0.0", "wcwidth": "^1.0.1", "cli-cursor": "^4.0.0", "strip-ansi": "^7.0.1", "log-symbols": "^5.1.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "stdin-discarder": "^0.1.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "get-stream": "^6.0.1", "@types/node": "^17.0.18", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_6.3.1_1684137670962_0.****************", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "ora", "version": "7.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"rules": {"@typescript-eslint/no-redundant-type-constituents": "off"}}, "dist": {"shasum": "7c48955ba40ae487054d2153c2a72c670f9c6697", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-jYSRKdGWCA69vNIcpuJwf8zJ8n1wlEyYmT+P5ljsQHAn1stHi+HGXJQWKtCOybQ+7oOekPgIhTdj0xexo2F/Sg==", "signatures": [{"sig": "MEUCIQDB5yUJ+IPMJR1k0fAtoo3LCp293ZdLdVZWdhdgVydieQIgJ2JHpoO2lAYp98ri/GAQu4eHu0YrGH5/9bbM9UqfY8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26801}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d1a21e892e29fd501ac94ef281638a9857d7b2d0", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^4.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^5.1.0", "cli-spinners": "^2.9.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.1.0", "is-unicode-supported": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "ava": "^5.3.1", "tsd": "^0.28.1", "get-stream": "^7.0.1", "@types/node": "^20.4.5", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_7.0.0_1690569607942_0.3500310300897982", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.1": {"name": "ora", "version": "7.0.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@7.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "xo": {"rules": {"@typescript-eslint/no-redundant-type-constituents": "off"}}, "dist": {"shasum": "cdd530ecd865fe39e451a0e7697865669cb11930", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-7.0.1.tgz", "fileCount": 5, "integrity": "sha512-0TUxTiFJWv+JnjWm4o9yvuskpEJLXTcng8MJuKd+SzAzp2o+OP3HWqNhB4OdJRt1Vsd9/mR0oyaEYlOnL7XIRw==", "signatures": [{"sig": "MEQCIAqdvjLlcUAbqUw0pjOcrsw7kbWj64o3sQEr0agSph27AiAvrnC92enz9FjOVqvl9kRKQyRdlRoTSQ+mbWtuerBT2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26829}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a4a9c5d21405a6357f2ac40f54ddbdb6c7c7fd16", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^4.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^5.1.0", "cli-spinners": "^2.9.0", "string-width": "^6.1.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.1.0", "is-unicode-supported": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "ava": "^5.3.1", "tsd": "^0.28.1", "get-stream": "^7.0.1", "@types/node": "^20.4.5", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_7.0.1_1690905074875_0.3957581312242937", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.0": {"name": "ora", "version": "8.0.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "7e1c296aa96e78372eeef1bb45ded0224ba71549", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-R<PERSON>+M9AFI9f0h+iPBxBIok+tWHSQYiMpp+NCdEOPFWrSGxl+IIJ4iCiXetSEoiP0AnNv1EcER6l/7t4mfkZjew==", "signatures": [{"sig": "MEYCIQDOQ8zVJYY2FHJ2Sg2SOO1mmFpueUsLvq6LOiAeMyD6jwIhAPd618SJ4eo9y/1jSPHRsWKu4+GJLz6EkO9DUT7dfpHd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26806}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "41d75f22c4e30ffceb781cc478e2fc9f7446cb01", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^4.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^6.0.0", "cli-spinners": "^2.9.2", "string-width": "^7.0.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.2.0", "is-unicode-supported": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.30.0", "get-stream": "^8.0.1", "@types/node": "^20.10.5", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_8.0.0_1703276603172_0.2385449518633589", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.1": {"name": "ora", "version": "8.0.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@8.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "6dcb9250a629642cbe0d2df3a6331ad6f7a2af3e", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-8.0.1.tgz", "fileCount": 5, "integrity": "sha512-ANIvzobt1rls2BDny5fWZ3ZVKyD6nscLvfFRpQgfWsythlcsVUC9kL0zq6j2Z5z9wwp1kd7wpsD/T9qNPVLCaQ==", "signatures": [{"sig": "MEYCIQCRkocgqAwQ70U0Wsk+4+C4iux5uGmqganGNxsqGZuDKgIhAIBgNwCGkZK0pkPB2/vNe5OijEsFNh/9hk0mXYXzNvzG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26806}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "3c63d5e8569d94564b5280525350724817e9ac26", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Elegant terminal spinner", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^4.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^6.0.0", "cli-spinners": "^2.9.2", "string-width": "^7.0.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.2.1", "is-unicode-supported": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.30.0", "get-stream": "^8.0.1", "@types/node": "^20.10.5", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_8.0.1_1703331513168_0.03650531002759472", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.1.0": {"name": "ora", "version": "8.1.0", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@8.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "c3db2f9f83a2bec9e8ab71fe3b9ae234d65ca3a8", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-8.1.0.tgz", "fileCount": 5, "integrity": "sha512-GQEkNkH/GHOhPFXcqZs3IDahXEQcQxsSjEkK4KvEEST4t7eNzoMjxTzef+EZ+JluDEV+Raoi3WQ2CflnRdSVnQ==", "signatures": [{"sig": "MEUCIFC4LTupZ5kPz1VmFhOgvaGYE+u8nsg0OKITgTV2sKjUAiEA18KbIiUqwPwTOOXDcjqnM2pAwcpWvQEfZxw62BB46SA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27096}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f8ebaa0cc6c590d03580c8f21912f5d76f544cef", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Elegant terminal spinner", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.4", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^5.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^6.0.0", "cli-spinners": "^2.9.2", "string-width": "^7.2.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.2.2", "is-unicode-supported": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "ava": "^5.3.1", "tsd": "^0.31.1", "get-stream": "^9.0.1", "@types/node": "^22.5.0", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_8.1.0_1724608091688_0.8245732933027423", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.1.1": {"name": "ora", "version": "8.1.1", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ora@8.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ora#readme", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "dist": {"shasum": "8efc8865e44c87e4b55468a47e80a03e678b0e54", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-8.1.1.tgz", "fileCount": 5, "integrity": "sha512-YWielGi1XzG1UTvOaCFaNgEnuhZVMSHYkW/FQ7UX8O26PtlpdM84c0f7wLPlkvx2RfiQmnzd61d/MGxmpQeJPw==", "signatures": [{"sig": "MEUCIGM0Y+Ntj6DRa475AMwfRFDmirT+OaEmJt9cGO3/5IjHAiEApBsSAEcO4iUaQ11SY5DfPpH8LVn4z4ZxIjCHuOgONiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27474}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "86aca37a324dcb010da5c04660e5c81a4d8f1f9d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/ora.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Elegant terminal spinner", "directories": {}, "sideEffects": false, "_nodeVersion": "22.6.0", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^5.0.0", "strip-ansi": "^7.1.0", "log-symbols": "^6.0.0", "cli-spinners": "^2.9.2", "string-width": "^7.2.0", "is-interactive": "^2.0.0", "stdin-discarder": "^0.2.2", "is-unicode-supported": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "ava": "^5.3.1", "tsd": "^0.31.1", "get-stream": "^9.0.1", "@types/node": "^22.5.0", "transform-tty": "^1.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/ora_8.1.1_1730483985946_0.16988156657244535", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.2.0": {"name": "ora", "version": "8.2.0", "description": "Elegant terminal spinner", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ora.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^5.0.0", "cli-spinners": "^2.9.2", "is-interactive": "^2.0.0", "is-unicode-supported": "^2.0.0", "log-symbols": "^6.0.0", "stdin-discarder": "^0.2.2", "string-width": "^7.2.0", "strip-ansi": "^7.1.0"}, "devDependencies": {"@types/node": "^22.5.0", "ava": "^5.3.1", "get-stream": "^9.0.1", "transform-tty": "^1.0.11", "tsd": "^0.31.1", "xo": "^0.59.3"}, "_id": "ora@8.2.0", "gitHead": "79d0339e000cf956ba5581013f27bd86a41d56fc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "homepage": "https://github.com/sindresorhus/ora#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-weP+BZ8MVNnlCm8c0Qdc1WSWq4Qn7I+9CJGm7Qali6g44e/PUzbjNqJX5NJ9ljlNMosfJvg1fKEGILklK9cwnw==", "shasum": "8fbbb7151afe33b540dd153f171ffa8bd38e9861", "tarball": "https://mirrors.cloud.tencent.com/npm/ora/-/ora-8.2.0.tgz", "fileCount": 5, "unpackedSize": 27514, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC5NZc/KPbwadEhdzVUdP5wDqbUqAYZVbknd8hl/T2jAwIhAMgesT8DZJachzgw1YnzJK8EuJuMs4Qz3sm2IFI8arpn"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ora_8.2.0_1738481176536_0.45403176121108624"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2016-03-03T18:47:04.266Z", "modified": "2025-02-02T07:26:16.899Z", "0.1.0": "2016-03-03T18:47:04.266Z", "0.1.1": "2016-03-03T18:51:48.750Z", "0.2.0": "2016-03-07T17:35:14.026Z", "0.2.1": "2016-03-28T06:57:16.727Z", "0.2.2": "2016-05-19T09:57:19.624Z", "0.2.3": "2016-05-19T11:02:04.544Z", "0.3.0": "2016-07-30T16:42:05.270Z", "0.4.0": "2016-12-17T21:10:06.546Z", "0.4.1": "2017-01-10T05:44:46.127Z", "1.0.0": "2017-01-17T10:03:06.812Z", "1.1.0": "2017-01-20T08:33:52.233Z", "1.2.0": "2017-03-24T13:42:30.106Z", "1.3.0": "2017-06-14T18:40:38.761Z", "1.4.0": "2018-02-02T02:19:48.018Z", "2.0.0": "2018-02-21T16:44:21.596Z", "2.1.0": "2018-04-30T06:30:24.234Z", "3.0.0": "2018-07-17T21:27:48.391Z", "3.1.0": "2019-02-11T05:23:44.468Z", "3.2.0": "2019-03-02T19:29:06.233Z", "3.3.0": "2019-04-02T07:26:04.198Z", "3.3.1": "2019-04-02T19:51:18.041Z", "3.4.0": "2019-04-03T05:11:35.872Z", "4.0.0": "2019-09-22T08:58:33.550Z", "4.0.1": "2019-09-23T08:57:56.696Z", "4.0.2": "2019-09-29T05:23:34.022Z", "4.0.3": "2019-11-13T10:23:13.821Z", "4.0.4": "2020-04-21T15:03:10.722Z", "4.0.5": "2020-07-17T14:51:16.053Z", "4.1.0": "2020-08-06T11:10:20.697Z", "4.1.1": "2020-08-07T15:18:49.979Z", "5.0.0": "2020-08-07T15:28:51.855Z", "5.1.0": "2020-09-06T20:39:07.435Z", "5.2.0": "2020-12-29T08:24:36.034Z", "5.3.0": "2021-01-20T10:02:51.213Z", "5.4.0": "2021-03-17T11:49:18.775Z", "5.4.1": "2021-06-08T07:38:49.815Z", "6.0.0": "2021-08-23T21:49:04.568Z", "6.0.1": "2021-09-13T18:06:43.487Z", "6.1.0": "2022-02-21T08:01:33.962Z", "6.1.1": "2022-06-26T10:36:34.471Z", "6.1.2": "2022-06-27T23:12:04.032Z", "6.2.0": "2023-03-19T10:13:22.073Z", "6.3.0": "2023-03-24T07:21:50.518Z", "6.3.1": "2023-05-15T08:01:11.159Z", "7.0.0": "2023-07-28T18:40:08.096Z", "7.0.1": "2023-08-01T15:51:15.032Z", "8.0.0": "2023-12-22T20:23:23.455Z", "8.0.1": "2023-12-23T11:38:33.369Z", "8.1.0": "2024-08-25T17:48:11.817Z", "8.1.1": "2024-11-01T17:59:46.231Z", "8.2.0": "2025-02-02T07:26:16.712Z"}, "users": {}, "dist-tags": {"latest": "8.2.0"}, "_rev": "19257-138453835e6e9d88", "_id": "ora", "readme": "# ora\n\n> Elegant terminal spinner\n\n<p align=\"center\">\n\t<br>\n\t<img src=\"screenshot.svg\" width=\"500\">\n\t<br>\n</p>\n\n## Install\n\n```sh\nnpm install ora\n```\n\n*Check out [`yocto-spinner`](https://github.com/sindresorhus/yocto-spinner) for a smaller alternative.*\n\n## Usage\n\n```js\nimport ora from 'ora';\n\nconst spinner = ora('Loading unicorns').start();\n\nsetTimeout(() => {\n\tspinner.color = 'yellow';\n\tspinner.text = 'Loading rainbows';\n}, 1000);\n```\n\n## API\n\n### ora(text)\n### ora(options)\n\nIf a string is provided, it is treated as a shortcut for [`options.text`](#text).\n\n#### options\n\nType: `object`\n\n##### text\n\nType: `string`\n\nThe text to display next to the spinner.\n\n##### prefixText\n\nType: `string | () => string`\n\nText or a function that returns text to display before the spinner. No prefix text will be displayed if set to an empty string.\n\n##### suffixText\n\nType: `string | () => string`\n\nText or a function that returns text to display after the spinner text. No suffix text will be displayed if set to an empty string.\n\n##### spinner\n\nType: `string | object`\\\nDefault: `'dots'` <img src=\"screenshot-spinner.gif\" width=\"14\">\n\nThe name of one of the [provided spinners](#spinners). See `example.js` in this repo if you want to test out different spinners. On Windows (except for Windows Terminal), it will always use the `line` spinner as the Windows command-line doesn't have proper Unicode support.\n\nOr an object like:\n\n```js\n{\n\tframes: ['-', '+', '-'],\n\tinterval: 80 // Optional\n}\n```\n\n##### color\n\nType: `string | boolean`\\\nDefault: `'cyan'`\\\nValues: `'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' | 'gray' | boolean`\n\nThe color of the spinner.\n\n##### hideCursor\n\nType: `boolean`\\\nDefault: `true`\n\nSet to `false` to stop Ora from hiding the cursor.\n\n##### indent\n\nType: `number`\\\nDefault: `0`\n\nIndent the spinner with the given number of spaces.\n\n##### interval\n\nType: `number`\\\nDefault: Provided by the spinner or `100`\n\nInterval between each frame.\n\nSpinners provide their own recommended interval, so you don't really need to specify this.\n\n##### stream\n\nType: `stream.Writable`\\\nDefault: `process.stderr`\n\nStream to write the output.\n\nYou could for example set this to `process.stdout` instead.\n\n##### isEnabled\n\nType: `boolean`\n\nForce enable/disable the spinner. If not specified, the spinner will be enabled if the `stream` is being run inside a TTY context (not spawned or piped) and/or not in a CI environment.\n\nNote that `{isEnabled: false}` doesn't mean it won't output anything. It just means it won't output the spinner, colors, and other ansi escape codes. It will still log text.\n\n##### isSilent\n\nType: `boolean`\\\nDefault: `false`\n\nDisable the spinner and all log text. All output is suppressed and `isEnabled` will be considered `false`.\n\n##### discardStdin\n\nType: `boolean`\\\nDefault: `true`\n\nDiscard stdin input (except Ctrl+C) while running if it's TTY. This prevents the spinner from twitching on input, outputting broken lines on <kbd>Enter</kbd> key presses, and prevents buffering of input while the spinner is running.\n\nThis has no effect on Windows as there is no good way to implement discarding stdin properly there.\n\n### Instance\n\n#### .text <sup>get/set</sup>\n\nChange the text displayed after the spinner.\n\n#### .prefixText <sup>get/set</sup>\n\nChange the text before the spinner.\n\nNo prefix text will be displayed if set to an empty string.\n\n#### .suffixText <sup>get/set</sup>\n\nChange the text after the spinner text.\n\nNo suffix text will be displayed if set to an empty string.\n\n#### .color <sup>get/set</sup>\n\nChange the spinner color.\n\n#### .spinner <sup>get/set</sup>\n\nChange the spinner.\n\n#### .indent <sup>get/set</sup>\n\nChange the spinner indent.\n\n#### .isSpinning <sup>get</sup>\n\nA boolean indicating whether the instance is currently spinning.\n\n#### .interval <sup>get</sup>\n\nThe interval between each frame.\n\nThe interval is decided by the chosen spinner.\n\n#### .start(text?)\n\nStart the spinner. Returns the instance. Set the current text if `text` is provided.\n\n#### .stop()\n\nStop and clear the spinner. Returns the instance.\n\n#### .succeed(text?)\n\nStop the spinner, change it to a green `✔` and persist the current text, or `text` if provided. Returns the instance. See the GIF below.\n\n#### .fail(text?)\n\nStop the spinner, change it to a red `✖` and persist the current text, or `text` if provided. Returns the instance. See the GIF below.\n\n#### .warn(text?)\n\nStop the spinner, change it to a yellow `⚠` and persist the current text, or `text` if provided. Returns the instance.\n\n#### .info(text?)\n\nStop the spinner, change it to a blue `ℹ` and persist the current text, or `text` if provided. Returns the instance.\n\n#### .stopAndPersist(options?)\n\nStop the spinner and change the symbol or text. Returns the instance. See the GIF below.\n\n##### options\n\nType: `object`\n\n###### symbol\n\nType: `string`\\\nDefault: `' '`\n\nSymbol to replace the spinner with.\n\n###### text\n\nType: `string`\\\nDefault: Current `'text'`\n\nText to be persisted after the symbol.\n\n###### prefixText\n\nType: `string | () => string`\\\nDefault: Current `prefixText`\n\nText or a function that returns text to be persisted before the symbol. No prefix text will be displayed if set to an empty string.\n\n###### suffixText\n\nType: `string | () => string`\\\nDefault: Current `suffixText`\n\nText or a function that returns text to be persisted after the text after the symbol. No suffix text will be displayed if set to an empty string.\n\n<img src=\"screenshot-2.gif\" width=\"480\">\n\n#### .clear()\n\nClear the spinner. Returns the instance.\n\n#### .render()\n\nManually render a new frame. Returns the instance.\n\n#### .frame()\n\nGet a new frame.\n\n### oraPromise(action, text)\n### oraPromise(action, options)\n\nStarts a spinner for a promise or promise-returning function. The spinner is stopped with `.succeed()` if the promise fulfills or with `.fail()` if it rejects. Returns the promise.\n\n```js\nimport {oraPromise} from 'ora';\n\nawait oraPromise(somePromise);\n```\n\n#### action\n\nType: `Promise | ((spinner: ora.Ora) => Promise)`\n\n#### options\n\nType: `object`\n\nAll of the [options](#options) plus the following:\n\n##### successText\n\nType: `string | ((result: T) => string) | undefined`\n\nThe new text of the spinner when the promise is resolved.\n\nKeeps the existing text if `undefined`.\n\n##### failText\n\nType: `string | ((error: Error) => string) | undefined`\n\nThe new text of the spinner when the promise is rejected.\n\nKeeps the existing text if `undefined`.\n\n### spinners\n\nType: `Record<string, Spinner>`\n\nAll [provided spinners](https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json).\n\n## FAQ\n\n### How do I change the color of the text?\n\nUse [`chalk`](https://github.com/chalk/chalk) or [`yoctocolors`](https://github.com/sindresorhus/yoctocolors):\n\n```js\nimport ora from 'ora';\nimport chalk from 'chalk';\n\nconst spinner = ora(`Loading ${chalk.red('unicorns')}`).start();\n```\n\n### Why does the spinner freeze?\n\nJavaScript is single-threaded, so any synchronous operations will block the spinner's animation. To avoid this, prefer using asynchronous operations.\n\n## Related\n\n- [yocto-spinner](https://github.com/sindresorhus/yocto-spinner) - Tiny terminal spinner\n- [cli-spinners](https://github.com/sindresorhus/cli-spinners) - Spinners for use in the terminal\n\n**Ports**\n\n- [CLISpinner](https://github.com/kiliankoe/CLISpinner) - Terminal spinner library for Swift\n- [halo](https://github.com/ManrajGrover/halo) - Python port\n- [spinners](https://github.com/FGRibreau/spinners) - Terminal spinners for Rust\n- [marquee-ora](https://github.com/joeycozza/marquee-ora) - Scrolling marquee spinner for Ora\n- [briandowns/spinner](https://github.com/briandowns/spinner) - Terminal spinner/progress indicator for Go\n- [tj/go-spin](https://github.com/tj/go-spin) - Terminal spinner package for Go\n- [observablehq.com/@victordidenko/ora](https://observablehq.com/@victordidenko/ora) - Ora port to Observable notebooks\n- [kia](https://github.com/HarryPeach/kia) - Simple terminal spinners for Deno 🦕", "_attachments": {}}