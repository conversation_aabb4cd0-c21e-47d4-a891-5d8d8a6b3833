{"name": "supports-hyperlinks", "versions": {"1.0.0": {"name": "supports-hyperlinks", "version": "1.0.0", "keywords": [], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@1.0.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "ava": {"babel": {"presets": ["env", "stage-3"]}}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "5aa39343ee70cdec29e4ebad68d4398a84b5d5ab", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-1.0.0.tgz", "integrity": "sha512-/NGoqti1A9Lcc1r6O7lQOr/uI0ZldCydnqeChSHVY9l3LcaLIm35zM9yRyIyKPzFKBnU+xVp3KRjm6hB9hlfDA==", "signatures": [{"sig": "MEQCIE5e7IEB2s82mpcb0lNRLMRw7fBHrlVOX+4Ak6kBwuUwAiASE5oYEU8tRLDoXkzUBsE1zTcBcNU1gGCq/lWTogcyYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "2680b8599abc465242826b90a6b9075378aff166", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"has-flag": "^2.0.0", "supports-color": "^5.0.0"}, "devDependencies": {"xo": "^0.18.2", "ava": "^0.20.0", "nyc": "^11.0.0", "codecov": "^2.2.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-3": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks-1.0.0.tgz_1511832020464_0.8140476217959076", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.1": {"name": "supports-hyperlinks", "version": "1.0.1", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@1.0.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "ava": {"babel": {"presets": ["env", "stage-3"]}}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "71daedf36cc1060ac5100c351bb3da48c29c0ef7", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-1.0.1.tgz", "integrity": "sha512-HHi5kVSefKaJkGYXbDuKbUGRVxqnWGn3J2e39CYcNJEfWciGq2zYtOhXLTlvrOZW1QU7VX67w7fMmWafHX9Pfw==", "signatures": [{"sig": "MEQCIF6ahbqRjsf76xhH9yFBXgGVE52e+XmT2it1nyKglC9FAiAYJRJq/ybuwR7LoF1x8FTPrNp80leiHoJxMb9oBkudjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "fe34a2736bcb108c310d62e94284f610ed8b203b", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"has-flag": "^2.0.0", "supports-color": "^5.0.0"}, "devDependencies": {"xo": "^0.18.2", "ava": "^0.20.0", "nyc": "^11.0.0", "codecov": "^2.2.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-3": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks-1.0.1.tgz_1511833857356_0.612168702762574", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "supports-hyperlinks", "version": "2.0.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@2.0.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "b1b94a159e9df00b0a554b2d5f0e0a89690334b0", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-bFhn0MQ8qefLyJ3K7PpHiPUTuTVPWw6RXfaMeV6xgJLXtBbszyboz1bvGTVv4R0YpQm2DqlXXn0fFHhxUHVE5w==", "signatures": [{"sig": "MEQCIHHLsIah072lgUvwGHD0Xh9xfh1SeGdl4aGmVYQ7rmVgAiBVlHaqWn1w9HO5Q4TKQljOmTG9+ujq7v694Re6ZMioiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL2TACRA9TVsSAnZWagAAhAwQAI3h8odGZAy2qwECSZKL\niGsoE5cz0HBsJzI6uWdn7RcQxt61HTuKEWNuhtVqzxE/HUSFDpPEk4DllK+s\n7F8klvWnzcNsFiXADkA/HiZ5b1P/nkgbT6kZ5q1I4qWWoZi2tA7CALhGw+bz\n6AO4zW5nrtz8vC1L5K6O2ayTPS8TPCXTBEb0By1G+BCLCXtGsaWWcW/vTgrs\nLqow8+ApzFQVBNVLzem0OuCD1Cs2tXTtn/mlyfKoO7bNp6VmqOO5PWxKzrY3\nj+36b8/xwgnQl2fzCrQu+7YBGwltoZDGd4LDW6TyQSqYbJX5ALsRIwzzAh8p\nrSGb9Y+1fuWQxbh2VqU53Cvmd+MMOANZzV0la/8K/vbCrGYImKQfopHdEwLI\n+qu4DKTZIWlhhQ683xFgMW+EVuQ+AuB8ukL4pVFI3So9vVZsIEjGsgV1g/gq\nmmoSZB9db1eGDZRLmw9GWCcLdhRrsWtcEBX1Pv7AL+wNJiUdSlyHX1oFsHMJ\njyiovn6e7l1FluQRIvEPrKanZVeAflFLVLzeVBco7IFdWVg4IX9KH7GS7oCo\nkQBVP5+Pm6a1vLvAXikSa4dwQrOVywKSKW6lKv2DJDFfmlwIjeVLHqN59lVa\nGb/82litdwkuamQIhouInQdwKMiSwuqZkcGV19a6IRG1NvdM9qQC5DRlcDF9\nyYoS\r\n=DyfU\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "d69899797030a99ebf024b0c74e13027c99a6986", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_2.0.0_1563387072109_0.9532700614996101", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "supports-hyperlinks", "version": "2.1.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@2.1.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "f663df252af5f37c5d49bbd7eeefa9e0b9e59e47", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-zoE5/e+dnEijk6ASB6/qrK+oYdm2do1hjoLWrqUC/8WEIW1gbxFcKuBof7sW8ArN6e+AYvsE8HBGiVRWL/F5CA==", "signatures": [{"sig": "MEQCIF4zOks9FG554l+eNGrbgoqcFGEGM16DzdeyJDlLPfjhAiB8MUJmGvZbgD2nKE6TX+CBop/4yT9yWZLvy9rvIG5tZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePKvFCRA9TVsSAnZWagAAXYMQAJwAOq1397DihzVyEUAa\nzEGeQmfvqSABOWITH7isRwl4+SmxKkOIh7IEZf/FaNfzOZ9o/TvAERuufQjQ\nL4Bq/rusbFAQI4XQ3rq7yPwdf80GmT3DGMlbqMwG7eTVx00Px+naNaSq7XdU\nM5Bc07cMwV7MC+sj6rU5fwmplmBlZ/Gm2x1FtITaKii/7nQZurT2BXpKX2Uu\nDcVe6PQcyh66f2frbYecU/Lm42Ltrr/TSOvHgEgq7/tTQPOSpeDuv0op4ilf\n0Og5HWciWqVwhq1GaaArs/yeDDBHPzOKrSbQuZk6JKJARNxSO5ODZeelGYk+\nVIhNvE2oxIph/63zQrQ6PtzgvNCvzGVYZDvRhL2lS7FYkLupYnyDinBXADXB\n8sfy2fY5CNhNdh+UoY35oIP7UIPPCWeNcz/uT/49Gnn4bS15vCaoyJ5Pqj4H\n9yWRdgwLtFxFmE+PaQpmmPtE+ZJpfo7+9PF3qJXa/sCaeB8zJdGcsS90knvS\nVLeJHw3bJdsw4YbICrQ/GxWUtvFLmlEs73z0xOlc+5+SazreJMqhbgna1pk1\nPa6sZB8UAD5Nf4EQncIJ2OaRtDXqWhpJxn5aMowqb7vlU1cZbnt5L44ZfgMH\n5LR4RhBqbDJw1Dy6+JF+cqoOqiZBva0MXKS6bnCkZgV9WapaVFxO5kI/CPSa\nadn1\r\n=ROZL\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "6af53f634749394fea2de362b7866f0a57d44607", "scripts": {"test": "xo && nyc ava", "create-types": "tsc index.js --allowJs  --declaration --emitDeclarationOnly", "prepublishOnly": "npm run create-types"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0", "typescript": "^3.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_2.1.0_1581034437069_0.3445899160452104", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.2.0": {"name": "supports-hyperlinks", "version": "2.2.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@2.2.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "4f77b42488765891774b70c79babd87f9bd594bb", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-2.2.0.tgz", "fileCount": 5, "integrity": "sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ==", "signatures": [{"sig": "MEYCIQCUIl/BS5T8rtkaY53rjQIjyrpCz0bFZ9shuUqgxckEMQIhANdTXbqncV7BB721AhmdZlSJJfKPcSyp5N43E1YXXp5a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6705, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbOqGCRA9TVsSAnZWagAAYakQAJ8Zz6aP8cN/CY9IjCdw\nPv4bpxlA4ltQjRyRq1xtLySDRAwOd4vUG1Af6sBY4UxkR6tnAaeEdx9b0yiP\njPw6DVl4BEMzBzi21dGgmIW3OuwZhyE4tfAn3SySrwWCuMeN/GUp/AE5pKwC\nfVu8GyBXzRWig+4+2WttmN43uzCXc67oCiDBBqA4sqBhHu5XtY0XCU8PVZdB\n+9bUspbi9stzUycDAIl3fq8RfDOth/LOKRFVQGk4/dnfQE/xJIiR6eDcm7G+\nuZ2K3ebtydj+QCxXheKa+4+Ly+SJ/DBrZLVvVgJf0pULeetyAspMVIPUCiwx\nV6dZZf+kubHbnwORkcZ1VriaB4a0a3JdQjeCDQ1+Gy2Vxats3jwJTbM2ru33\nSQ68+4n10qbS8dA994LK5ouEM4PiuEnY2NKsTKuytBdq8ozxHmZKPJnb58GX\nqhyezKRbk4oiv+Z0q9HqeudMvrkurROWle1Wt+7PxRGIsii3FJ704q7BFFC9\n2zZVksPOIqjreKgijjPK6Z07vQxjoTQ5tlUnJWQkCaZ+W6axzy8LwrsmToV1\nHsQMcp8DE6LxyantM8jM8Fm53MZwZiSUHSMbYs8ogQrYTR32+JKsDJMmK4Ga\n4pgHvLUbD8TC0q4MKSflgyan7TuS28h/LOgRpL5PCfb0UQ+NAKaQz0WZK4Ej\nNdbN\r\n=cSn4\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "66834a8a4ccc07f9af6364778fcaa0666b61eccb", "scripts": {"test": "xo && nyc ava", "create-types": "tsc index.js --allowJs  --declaration --emitDeclarationOnly", "prepublishOnly": "npm run create-types"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "7.8.0", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0", "typescript": "^3.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_2.2.0_1617750662491_0.6291010861567068", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.3.0": {"name": "supports-hyperlinks", "version": "2.3.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@2.3.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "3943544347c1ff90b15effb03fc14ae45ec10624", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "fileCount": 5, "integrity": "sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==", "signatures": [{"sig": "MEUCIQCINlSJrlv5+AAxu7IaNCsMY3jUbKh7BUM9z98CcTj+2QIgKx3ONsLRqGG7QMKig1PFjKaXjhQ9XaKTt9VNWJKRLvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFtLJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUcg//XtK3j5pI4ujqdChc9v2POaLAWr2HY0pOSjWPPv56vcLykpiI\r\nuWcXIQT+PjFxJfOoZ+BpQ1ceHF9Mx5zX6DOA58yOSrhVli2FW7CiSFzl6HND\r\nXAHqbFYnE+o7H1gzx0f5wikQFGyLnt2OJfHzyPAyuStGjChMr0s9eY1Y1aw3\r\n+SC0hQL3PjiLwp0wmW/HLrk42o/z0uGVG7DFKQ71ofSu+9Mb78t7Joc4YTdr\r\n3l9gYJDsOfNUk/DP6B80PNCvf9KEp76Qf/rMOuiCH0jMb9emF/MFY+OazcYG\r\nQmjs89J9BduCjkBCyYIb/dQNJh5WRNKZS91qJvaw1m1y/SVwwEZjso+NJY2t\r\npprtaPtsxFwx3FoJELeW7rKVBBN9TuMJvfW3qRm92W1UNhuzKkj7xwORdYRO\r\nMV/Bc597cobM80rKTv1Ynj91E5f1jSjqth2QV16f9wlLLWjsy78VIq4kUTV3\r\nahLCKYMPvJHk1zrDq5ynhnEN1mhAMOID8ziTr6Tx9HT11oJjvsdAnM4yT3Ne\r\nPlDUmwO+n0/OAZx5V9GWXoYY7+wjmsunlbDD6pKH8xkJWk76ozkC7csV3ttF\r\nDt5Khfqf8gYco2R255t5J4k4cdxp1DjQlNOM3jqyx0Co8/Z3H091Hxq5u/Xk\r\nCxO1b+v3vUDnCwAYaDtG/zZbkiPegY5ylKA=\r\n=D83M\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "4cb672f76ca566f82c541b2a177aa6205875a0a6", "scripts": {"test": "xo && nyc ava", "create-types": "tsc index.js --allowJs  --declaration --emitDeclarationOnly", "prepublishOnly": "npm run create-types"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "14.19.3", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0", "typescript": "^3.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_2.3.0_1662440136874_0.16153616125736447", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "supports-hyperlinks", "version": "3.0.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@3.0.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "c711352a5c89070779b4dad54c05a2f14b15c94b", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-QBDPHyPQDRTy9ku4URNGY5Lah8PAaXs6tAAwp55sL5WCsSW7GIfdf6W5ixfziW+t7wh3GVvHyHHyQ1ESsoRvaA==", "signatures": [{"sig": "MEYCIQCJIGjdvq2m3tRlii0lpk+lFd/DlneUdyz26vBagxZeYAIhAMwoeGi9OawU4hDuLkNZcBAchPeOX7SnSCaJj83I0BYl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCF3kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6rg//TY+tMNk0pdUTpTIJ9r+Cfy5VOXEut3IKzUDL9PsouqK2gIiR\r\nvIwvNnyz9HyG1yiGNC0IA8EexEORCQxiBnmLrHbhj8I3iPg5BH5CpDnFu426\r\nAFsA+lKZaL9txa7aNiAHjYd4lb1+mZ+7KS5FE1YVeGsXfsCvUz9+AN8bo7PM\r\nyXAyZo/DXoS8ZTVxOFU7raigqSZTdldj1RGg1DymF+jfZqgGfl20n2BNilSL\r\ngA8K+K5rpF1qLvhh0HIwph+pqpAsH/KhvKs30RmW07PZjlIu9W8ZVCu27nV7\r\n6jB9gx6NzUESAiVBu+Y7o2P+BQcqkqBZdf7HCwWYKcVINlPWfNqbeoLMVdxu\r\nTga7zgiFybhCQHb4i1J0ZFu9ZDEYeSZQ4vq7QgV0l6tpf7BccCEOQZJ8fpRk\r\nWsXx/vEEc5NQN5prI788R/IckM+Hoa0SJoGoafptBfP/5PgdKCtAlNIvg6cX\r\nKY/I635kKKEz4u9841JZwsGEQO86oXTe110/6TiP2MAjMhtjivtdyj+dlulq\r\nRbFdedoqWNu/tna2QaW7UVALgTpXwurfm0l9VlWlsq4m/hRffosHlBgGlQeX\r\nafmkAsLtwPN3MvqoDN/SWM3zWRKM/zdxevMpFGLo47xSp48Y2z6eYC3JV4Re\r\nr3qcBGSub9BtCajDVkRxOX+NvgLiuyxp+kY=\r\n=te3Q\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "browser": "browser.js", "engines": {"node": ">=14.18"}, "gitHead": "99da35502fdfe4b9eb0d650389c6557e6251ef3b", "scripts": {"test": "xo && nyc ava && tsc", "create-types": "tsc --project declaration.tsconfig.json", "prepublishOnly": "npm run create-types"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0", "typescript": "^4.9.5", "@tsconfig/node14": "^1.0.3", "@types/supports-color": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_3.0.0_1678269924748_0.4712278572497024", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "supports-hyperlinks", "version": "3.1.0", "keywords": ["link", "terminal", "hyperlink", "cli"], "author": {"url": "github.com/jamestalmage", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-hyperlinks@3.1.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jamestalmage/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/jamestalmage/supports-hyperlinks/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "b56150ff0173baacc15f21956450b61f2b18d3ac", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-3.1.0.tgz", "fileCount": 6, "integrity": "sha512-2rn0BZ+/f7puLOHZm1HOJfwBggfaHXUpPUSSG/SWM4TWp5KCfmNYwnC3hruy2rZlMnmWZ+QAGpZfchu3f3695A==", "signatures": [{"sig": "MEYCIQD+Efslp7EDsWWVIbJJlsZd/GSt9vpxToG89qU34d83RQIhANuHm/NoNqUB6U2t6ixI+kAp1XY76ukuZln5JY/Zlr+5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7455}, "types": "./index.d.ts", "browser": "browser.js", "engines": {"node": ">=14.18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "e931b5b4b87b0c009bdadf1a612a0fa02acb9969", "scripts": {"test": "xo && nyc ava && tsc", "create-types": "tsc --project declaration.tsconfig.json", "prepublishOnly": "npm run create-types"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jamestalmage/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Detect if your terminal emulator supports hyperlinks", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.4", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "codecov": "^3.5.0", "typescript": "^4.9.5", "@tsconfig/node14": "^1.0.3", "@types/supports-color": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_3.1.0_1724183194454_0.4537178737861598", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.0": {"name": "supports-hyperlinks", "version": "3.2.0", "keywords": ["link", "terminal", "hyperlink", "cli", "detect", "check", "ansi", "escapes", "console"], "license": "MIT", "_id": "supports-hyperlinks@3.2.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/chalk/supports-hyperlinks/issues"}, "dist": {"shasum": "b8e485b179681dea496a1e7abdf8985bd3145461", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-3.2.0.tgz", "fileCount": 6, "integrity": "sha512-zFObLMyZeEwzAoKCyu1B91U79K2t7ApXuQfo8OuxwXLDgcKxuwM+YvcbIhm6QWqz7mHUH1TVytR1PwVVjEuMig==", "signatures": [{"sig": "MEUCIGDW2NidaPzIUzRujHjK9Xcm/t+TU86QgknjH/nxsw+dAiEAzh9pMKSZu0lNE0Mcsl/q4iNPXi3+8w5A/mPY+3w+JYg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7010}, "types": "./index.d.ts", "browser": "browser.js", "engines": {"node": ">=14.18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/chalk/supports-hyperlinks?sponsor=1", "gitHead": "72d47d3e48c7db34926e5e0fa3ad5cda303c34d8", "scripts": {"test": "ava", "//test": "xo && ava && tsc"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Detect whether a terminal supports hyperlinks", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.1", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.0", "ava": "^3.2.0", "codecov": "^3.5.0", "typescript": "^4.9.5", "@tsconfig/node14": "^1.0.3", "@types/supports-color": "^8.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_3.2.0_1738479189482_0.07829475308339551", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "4.0.0": {"name": "supports-hyperlinks", "version": "4.0.0", "keywords": ["link", "terminal", "hyperlink", "cli", "detect", "check", "ansi", "escapes", "console"], "license": "MIT", "_id": "supports-hyperlinks@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-hyperlinks#readme", "bugs": {"url": "https://github.com/chalk/supports-hyperlinks/issues"}, "dist": {"shasum": "0301d4266916b44de1310db906569be91d043657", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-+AGkj8vRkjR1g5ixsfzmoeYUaGyR4tU8JkGbWYGiilAwYc+oLyNHgoeYN/tGZJ7eyYpAF8QnTNzGTq/cwhn+bQ==", "signatures": [{"sig": "MEYCIQChXYhiuWrcYrXVlZxNghZ7Gx+0j8rDHpRnQVy+h/bU5wIhAOcpNqW00OCJv+e9C0SOltXshOakIdHhdQTqWf30vTVF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7398}, "type": "module", "types": "./index.d.ts", "browser": "browser.js", "engines": {"node": ">=20"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/chalk/supports-hyperlinks?sponsor=1", "gitHead": "c5d1720b5ccc8b8f3d6e97c6ea9bf42a3d69820f", "scripts": {"test": "xo && ava && tsc index.d.ts"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-hyperlinks.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Detect whether a terminal supports hyperlinks", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.1", "dependencies": {"has-flag": "^5.0.1", "supports-color": "^10.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "ava": "^6.2.0", "codecov": "^3.8.3", "typescript": "^5.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-hyperlinks_4.0.0_1743517089082_0.23303228412872268", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "4.1.0": {"name": "supports-hyperlinks", "version": "4.1.0", "description": "Detect whether a terminal supports hyperlinks", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-hyperlinks.git"}, "funding": "https://github.com/chalk/supports-hyperlinks?sponsor=1", "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "browser": "browser.js", "keywords": ["link", "terminal", "hyperlink", "cli", "detect", "check", "ansi", "escapes", "console"], "dependencies": {"has-flag": "^5.0.1", "supports-color": "^10.0.0"}, "devDependencies": {"ava": "^6.2.0", "codecov": "^3.8.3", "typescript": "^5.8.2", "xo": "^0.60.0"}, "_id": "supports-hyperlinks@4.1.0", "gitHead": "f987d96c802bbdeadb5947d6b64dd7f1cde45a31", "types": "./index.d.ts", "bugs": {"url": "https://github.com/chalk/supports-hyperlinks/issues"}, "homepage": "https://github.com/chalk/supports-hyperlinks#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-6lY0rDZ5bbZhAPrwpz/nMR6XmeaFmh2itk7YnIyph2jblPmDcKMCPkSdLFTlaX8snBvg7OJmaOL3WRLqMEqcJQ==", "shasum": "f006d9e2f6b9b6672f86c86c6f76bf52a69f4d91", "tarball": "https://mirrors.cloud.tencent.com/npm/supports-hyperlinks/-/supports-hyperlinks-4.1.0.tgz", "fileCount": 6, "unpackedSize": 7531, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDgkhTI/g0eHsDkr/J+lDrwURAG1VqF1xTigyPdx5VlGwIgR8b9jC12n3YsRCKsmgssZfPeikTHlMVWwtBmUzs3Qxk="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/supports-hyperlinks_4.1.0_1746859850306_0.12005065030891982"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2017-11-28T01:20:21.493Z", "modified": "2025-05-10T06:50:50.644Z", "1.0.0": "2017-11-28T01:20:21.493Z", "1.0.1": "2017-11-28T01:50:58.193Z", "2.0.0": "2019-07-17T18:11:12.257Z", "2.1.0": "2020-02-07T00:13:57.198Z", "2.2.0": "2021-04-06T23:11:02.630Z", "2.3.0": "2022-09-06T04:55:37.030Z", "3.0.0": "2023-03-08T10:05:24.926Z", "3.1.0": "2024-08-20T19:46:34.609Z", "3.2.0": "2025-02-02T06:53:09.656Z", "4.0.0": "2025-04-01T14:18:09.252Z", "4.1.0": "2025-05-10T06:50:50.472Z"}, "users": {}, "dist-tags": {"latest": "4.1.0"}, "_rev": "3355-5645e30df8938084", "_id": "supports-hyperlinks", "readme": "# supports-hyperlinks\n\n> Detect whether a terminal supports hyperlinks\n\nTerminal emulators are [starting to support hyperlinks](https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda). While many terminals have long detected URL's and linkified them, allowing you to Command-Click or Control-Click them to open a browser, you were forced to print the long unsightly URL's on the screen. As of spring 2017 [a few terminals](https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda) began supporting HTML like links, where the link text and destination could be specified separately.\n\nThis module allows you to detect if hyperlinks are supported in the current Terminal.\n\nAs this is a new development, we anticipate the list of supported terminals to grow rapidly. Please open an issue or submit a PR as new terminals implement support.\n\n## Install\n\n```sh\nnpm install supports-hyperlinks\n```\n\n## Usage\n\n```js\nimport supportsHyperlinks from 'supports-hyperlinks';\n\nif (supportsHyperlinks.stdout) {\n\tconsole.log('Terminal stdout supports hyperlinks');\n}\n\nif (supportsHyperlinks.stderr) {\n\tconsole.log('Terminal stderr supports hyperlinks');\n}\n```\n\n## API\n\nReturns an `Object` with a `stdout` and `stderr` property for testing either streams. Each property is a `boolean`, indicating whether or not hyperlinks are supported.\n\n## Info\n\nObeys the `--no-hyperlinks`, `--hyperlink=always`, and `--hyperlink=never` CLI flags.\n\nCan be overridden by the user with the flags `--hyperlinks=always` and `--no-hyperlinks`. For situations where using those flags are not possible, add the environment variable `FORCE_HYPERLINK=1` to forcefully enable hyperlinks or `FORCE_HYPERLINK=0` to forcefully disable. The use of `FORCE_HYPERLINK` overrides all other hyperlink support checks.", "_attachments": {}}