"""
Agent选择器
智能选择最适合的Agent组合执行任务
"""
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from .task_analyzer import TaskStep, TaskPlan

@dataclass
class AgentCapability:
    agent_id: str
    agent_name: str
    capabilities: List[str]
    specialties: List[str]
    performance_score: float = 0.8  # 历史表现评分
    load_factor: float = 1.0        # 负载因子

@dataclass
class AgentAssignment:
    step_id: str
    agent_id: str
    agent_name: str
    confidence: float               # 匹配度 0-1
    estimated_time: int            # 预估执行时间
    assignment_reason: str         # 选择原因

class AgentSelector:
    """Agent选择器 - 智能选择最适合的Agent组合"""
    
    def __init__(self):
        # Agent能力映射表
        self.agent_capabilities = {
            "composition_agent": AgentCapability(
                agent_id="composition_agent",
                agent_name="作文助手",
                capabilities=["写作", "创作", "文学", "内容生成"],
                specialties=["作文", "文章", "创意写作", "文学创作"],
                performance_score=0.9
            ),
            "daily_report_agent": AgentCapability(
                agent_id="daily_report_agent",
                agent_name="日报专家",
                capabilities=["报告", "总结", "工作记录", "文档"],
                specialties=["日报", "工作总结", "进展记录"],
                performance_score=0.85
            ),
            "week_report_agent": AgentCapability(
                agent_id="week_report_agent",
                agent_name="周报专家",
                capabilities=["报告", "总结", "工作分析", "文档"],
                specialties=["周报", "工作总结", "进展分析"],
                performance_score=0.85
            ),
            "month_report_agent": AgentCapability(
                agent_id="month_report_agent",
                agent_name="月报专家",
                capabilities=["报告", "分析", "趋势总结", "文档"],
                specialties=["月报", "深度分析", "趋势总结"],
                performance_score=0.85
            ),
            "schedule_agent": AgentCapability(
                agent_id="schedule_agent",
                agent_name="日程管理助手",
                capabilities=["日程", "时间管理", "安排", "提醒"],
                specialties=["日程安排", "会议管理", "时间规划"],
                performance_score=0.8
            ),
            "new_media_copywriting_agent": AgentCapability(
                agent_id="new_media_copywriting_agent",
                agent_name="新媒体文案",
                capabilities=["文案", "营销", "创意", "新媒体"],
                specialties=["新媒体文案", "营销文案", "创意内容"],
                performance_score=0.8
            ),
            "xiaohongshu_copywriting_agent": AgentCapability(
                agent_id="xiaohongshu_copywriting_agent",
                agent_name="小红书文案",
                capabilities=["小红书", "文案", "社交媒体", "种草"],
                specialties=["小红书文案", "种草内容", "社交媒体"],
                performance_score=0.8
            ),
            "xiaohongshu_creation_agent": AgentCapability(
                agent_id="xiaohongshu_creation_agent",
                agent_name="小红书创作",
                capabilities=["小红书", "内容创作", "运营", "策略"],
                specialties=["小红书创作", "内容运营", "平台策略"],
                performance_score=0.8
            ),
            "official_document_writing_agent": AgentCapability(
                agent_id="official_document_writing_agent",
                agent_name="公文写作",
                capabilities=["公文", "正式文档", "行政", "写作"],
                specialties=["公文写作", "正式文档", "行政文件"],
                performance_score=0.85
            )
        }
    
    def select_agents_for_plan(self, task_plan: TaskPlan) -> List[AgentAssignment]:
        """为任务计划选择合适的Agent"""
        assignments = []
        
        for step in task_plan.steps:
            assignment = self._select_agent_for_step(step)
            assignments.append(assignment)
        
        return assignments
    
    def _select_agent_for_step(self, step: TaskStep) -> AgentAssignment:
        """为单个任务步骤选择Agent"""
        
        # 1. 如果步骤已指定Agent类型，直接使用
        if step.agent_type in self.agent_capabilities:
            capability = self.agent_capabilities[step.agent_type]
            confidence = self._calculate_confidence(step, capability)
            
            return AgentAssignment(
                step_id=step.id,
                agent_id=step.agent_type,
                agent_name=capability.agent_name,
                confidence=confidence,
                estimated_time=step.estimated_time,
                assignment_reason=f"步骤指定使用{capability.agent_name}"
            )
        
        # 2. 根据任务特征选择最佳Agent
        best_agent = self._find_best_agent(step)
        
        return AgentAssignment(
            step_id=step.id,
            agent_id=best_agent.agent_id,
            agent_name=best_agent.agent_name,
            confidence=self._calculate_confidence(step, best_agent),
            estimated_time=step.estimated_time,
            assignment_reason=f"基于任务特征选择{best_agent.agent_name}"
        )
    
    def _find_best_agent(self, step: TaskStep) -> AgentCapability:
        """根据任务步骤找到最佳Agent"""
        best_agent = None
        best_score = 0.0
        
        for agent_id, capability in self.agent_capabilities.items():
            score = self._calculate_match_score(step, capability)
            
            if score > best_score:
                best_score = score
                best_agent = capability
        
        # 如果没有找到合适的Agent，返回默认的作文助手
        return best_agent or self.agent_capabilities["composition_agent"]
    
    def _calculate_match_score(self, step: TaskStep, capability: AgentCapability) -> float:
        """计算Agent与任务步骤的匹配度"""
        score = 0.0
        
        # 1. 基于任务类型匹配
        if step.task_type in capability.capabilities:
            score += 0.4
        
        # 2. 基于关键词匹配
        if step.keywords:
            keyword_matches = 0
            for keyword in step.keywords:
                for specialty in capability.specialties:
                    if keyword in specialty or specialty in keyword:
                        keyword_matches += 1
                        break
            
            if step.keywords:
                score += (keyword_matches / len(step.keywords)) * 0.3
        
        # 3. 基于描述文本匹配
        description_lower = step.description.lower()
        specialty_matches = 0
        for specialty in capability.specialties:
            if specialty in description_lower:
                specialty_matches += 1
        
        if capability.specialties:
            score += (specialty_matches / len(capability.specialties)) * 0.2
        
        # 4. 考虑历史表现
        score += capability.performance_score * 0.1
        
        return min(score, 1.0)  # 确保分数不超过1.0
    
    def _calculate_confidence(self, step: TaskStep, capability: AgentCapability) -> float:
        """计算选择置信度"""
        base_confidence = self._calculate_match_score(step, capability)
        
        # 根据负载因子调整置信度
        adjusted_confidence = base_confidence * (2.0 - capability.load_factor)
        
        return min(max(adjusted_confidence, 0.0), 1.0)
    
    def get_agent_info(self, agent_id: str) -> Optional[AgentCapability]:
        """获取Agent信息"""
        return self.agent_capabilities.get(agent_id)
    
    def list_available_agents(self) -> List[AgentCapability]:
        """列出所有可用的Agent"""
        return list(self.agent_capabilities.values())
    
    def update_agent_performance(self, agent_id: str, performance_score: float):
        """更新Agent的表现评分"""
        if agent_id in self.agent_capabilities:
            self.agent_capabilities[agent_id].performance_score = performance_score
    
    def update_agent_load(self, agent_id: str, load_factor: float):
        """更新Agent的负载因子"""
        if agent_id in self.agent_capabilities:
            self.agent_capabilities[agent_id].load_factor = load_factor