<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一规划助手</title>
    <link rel="stylesheet" href="execution_visualizer.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f8ff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-container {
            display: flex;
            height: 100vh;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .user .message-avatar {
            background: #3b82f6;
            color: white;
        }
        
        .assistant .message-avatar {
            background: #10b981;
            color: white;
        }
        
        .message-content {
            flex: 1;
            max-width: 70%;
        }
        
        .message-bubble {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .user .message-bubble {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .tool-call {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .tool-call-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 600;
            color: #92400e;
        }
        
        .thinking {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
            color: #0c4a6e;
        }
        
        .thinking-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .result-document {
            background: #f0fdf4;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-weight: 600;
            color: #065f46;
        }
        
        .download-links {
            display: flex;
            gap: 10px;
        }
        
        .download-btn {
            padding: 6px 12px;
            border: 1px solid #10b981;
            background: white;
            color: #10b981;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: #10b981;
            color: white;
        }
        
        .input-area {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 20px;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .user-avatar-container {
            flex-shrink: 0;
        }
        
        .user-input {
            flex: 1;
            min-height: 60px;
            max-height: 120px;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }
        
        .user-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .send-btn:hover {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .execution-status {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #64748b;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #9ca3af;
        }
        
        .status-indicator.running {
            background: #3b82f6;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.completed {
            background: #10b981;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-area">
            <div class="chat-header">
                🤖 统一规划助手
            </div>
            
            <div class="execution-status" id="execution-status" style="display: none;">
                <div class="status-indicator" id="status-indicator"></div>
                <span id="status-text">准备就绪</span>
            </div>
            
            <div class="messages-container" id="messages-container">
                <div class="message assistant">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-bubble">
                            👋 您好！我是统一规划助手，可以帮您完成各种任务。
                            <br><br>
                            请告诉我您需要什么帮助，比如：
                            <br>• 写一份小红书文案
                            <br>• 生成工作周报
                            <br>• 制定日程安排
                            <br>• 创作新媒体内容
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="input-area">
                <div class="input-container">
                    <div class="user-avatar-container">
                        <div class="message-avatar">👤</div>
                    </div>
                    <textarea 
                        id="user-input" 
                        class="user-input" 
                        placeholder="请输入您的需求..."
                    ></textarea>
                    <button id="send-btn" class="send-btn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isExecuting = false;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const userInput = document.getElementById('user-input');
            const sendBtn = document.getElementById('send-btn');
            const messagesContainer = document.getElementById('messages-container');
            
            // 绑定发送事件
            sendBtn.addEventListener('click', sendMessage);
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // 自动聚焦输入框
            userInput.focus();
        });
        
        function sendMessage() {
            const userInput = document.getElementById('user-input');
            const message = userInput.value.trim();
            
            if (!message || isExecuting) return;
            
            // 添加用户消息
            addMessage(message, true);
            userInput.value = '';
            
            // 开始执行
            executeTask(message);
        }
        
        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('messages-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const avatar = isUser ? '👤' : '🤖';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-bubble">${content}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function addToolCall(toolName, parameters) {
            const messagesContainer = document.getElementById('messages-container');
            const toolDiv = document.createElement('div');
            toolDiv.className = 'message assistant';
            
            toolDiv.innerHTML = `
                <div class="message-avatar">🔧</div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="tool-call">
                            <div class="tool-call-header">
                                <span>🔧</span>
                                <span>调用工具: ${toolName}</span>
                            </div>
                            <div>参数: ${JSON.stringify(parameters, null, 2)}</div>
                        </div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(toolDiv);
            scrollToBottom();
        }
        
        function addThinking(thought) {
            const messagesContainer = document.getElementById('messages-container');
            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'message assistant';
            
            thinkingDiv.innerHTML = `
                <div class="message-avatar">🧠</div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="thinking">
                            <div class="thinking-header">
                                <span>🧠</span>
                                <span>思考过程</span>
                            </div>
                            <div>${thought}</div>
                        </div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(thinkingDiv);
            scrollToBottom();
        }
        
        function addResultDocument(title, content, downloadLinks = []) {
            const messagesContainer = document.getElementById('messages-container');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'message assistant';
            
            const downloadButtons = downloadLinks.map(link => 
                `<a href="${link.url}" class="download-btn" download>${link.label}</a>`
            ).join('');
            
            resultDiv.innerHTML = `
                <div class="message-avatar">📄</div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="result-document">
                            <div class="document-header">
                                <div class="document-title">${title}</div>
                                <div class="download-links">
                                    ${downloadButtons}
                                </div>
                            </div>
                            <div class="document-content">
                                ${content}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(resultDiv);
            scrollToBottom();
        }
        
        function updateExecutionStatus(status, text) {
            const statusDiv = document.getElementById('execution-status');
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            statusDiv.style.display = 'flex';
            indicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }
        
        function executeTask(task) {
            isExecuting = true;
            updateExecutionStatus('running', '正在分析任务...');
            
            // 模拟执行过程
            setTimeout(() => {
                addThinking('分析用户需求，确定需要调用小红书文案专家...');
                
                setTimeout(() => {
                    addToolCall('call_agent', {
                        agent_id: 'xiaohongshu_copywriting_agent',
                        task: task,
                        context: '用户需要创作小红书文案'
                    });
                    
                    setTimeout(() => {
                        updateExecutionStatus('running', '正在生成文案...');
                        
                        setTimeout(() => {
                            updateExecutionStatus('completed', '任务完成');
                            
                            // 添加结果文档
                            addResultDocument(
                                '小红书文案',
                                `# 推荐护肤品的小红书文案
                                
## 封面设计建议
- 设计元素：清新自然的护肤品展示
- 色彩搭配：粉色系，体现温和护肤理念

## 正文内容
- **引入**：分享一款让我皮肤状态提升的护肤品
- **主体**：详细描述使用体验和效果
- **结尾**：推荐给有同样需求的朋友

> **[精彩金句]** *温和呵护，让肌肤重焕光彩*

## 注释
在撰写这篇文案时，我考虑了以下几点：
1. **目标受众分析**：针对护肤需求明确的年轻女性
2. **平台特性适应**：符合小红书种草风格
3. **吸引力构建**：突出产品效果和使用体验
4. **信息传达清晰**：确保文案信息准确完整`,
                                [
                                    { label: '📄 HTML', url: '#' },
                                    { label: '📝 Markdown', url: '#' },
                                    { label: '📊 PDF', url: '#' }
                                ]
                            );
                            
                            isExecuting = false;
                        }, 1000);
                        
                    }, 2000);
                    
                }, 1000);
                
            }, 1000);
        }
        
        function scrollToBottom() {
            const messagesContainer = document.getElementById('messages-container');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    </script>
</body>
</html> 