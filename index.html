<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Agno统一智能平台 - 模板版</title>
    <!-- <link rel="stylesheet" href="模板/style.css" /> -->
    <!-- Markdown 渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
    
    <!-- 引入头像配置 -->
    <script src="avatar_config.js"></script>
</head>
<body>
    <div class="chat-container">
        <!-- 左侧智能体列表 -->
        <div class="agent-sidebar">
            <div class="sidebar-header">
                <h3>🤖 AI 智能助手</h3>
                <p>选择您需要的助手类型</p>
                <div class="search-container">
                    <input type="text" id="agent-search" class="search-input" placeholder="搜索智能体..." />
                    <span class="search-icon">🔍</span>
                    <button class="clear-search" id="clear-search">✕</button>
                </div>
            </div>
            <div class="agent-list" id="agent-list">
                <!-- 智能体列表将在这里动态生成 -->
            </div>
            <div class="sidebar-footer">
                <div class="current-agent-display">
                    <span class="agent-indicator">●</span>
                    <span id="current-agent">请选择智能体</span>
                </div>
            </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-area">
            <div class="chat-header">
                <span>💬 与 <span id="chat-agent-name">智能助手</span> 对话</span>
            </div>
            <div class="messages-container" id="messages-container">
                <!-- 消息将在这里显示 -->
            </div>
            <div class="input-area">
                <div class="input-container">
                    <div class="user-avatar-container">
                        <img src="touxiang/996.png" class="user-avatar" alt="用户头像" />
                    </div>
                    <textarea id="user-input" class="user-input" placeholder="请输入您的问题..."></textarea>
                    <button id="send-btn" class="send-btn">发送消息</button>
                </div>
            </div>
        </div>
    </div>

<style>
    /* 基础样式 */
    :root {
        --primary-color: #2563EB;
        --secondary-color: #60A5FA;
        --bg-color: #F8FAFC;
        --secondary-bg: #E2E8F0;
        --text-color: #1E293B;
        --text-secondary: #64748B;
    }

    html, body {
        height: 100%;
        margin: 0;
        font-family: "Helvetica Neue", Inter, sans-serif;
    }

    .chat-container {
        display: flex;
        height: 100vh;
        background: #f5f8ff;
    }

    .agent-sidebar {
        width: 260px;
        background: white;
        border-right: 1px solid #e0e0e0;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }

    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
    }

    .sidebar-header h3 {
        margin: 0 0 10px 0;
        font-size: 18px;
    }

    .sidebar-header p {
        margin: 0 0 15px 0;
        font-size: 12px;
        opacity: 0.9;
    }

    .search-container {
        position: relative;
        margin-top: 15px;
    }

    .search-input {
        width: 100%;
        padding: 10px 35px 10px 12px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        outline: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
    }

    .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        pointer-events: none;
    }

    .clear-search {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        cursor: pointer;
        display: none;
        background: none;
        border: none;
        padding: 2px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .clear-search:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .agent-list {
        flex: 1;
        padding: 15px;
    }
    
    .agent-category-title {
        font-size: 14px;
        font-weight: 600;
        color: #64748B;
        margin: 10px 0;
        padding: 5px 10px;
        background: #f1f5f9;
        border-radius: 4px;
    }

    /* 折叠分类样式 */
    .category-header {
        display: flex;
        align-items: center;
        padding: 12px 10px;
        margin: 8px 0 4px 0;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #e2e8f0;
        user-select: none;
    }

    .category-header:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        border-color: #cbd5e1;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .category-toggle {
        font-size: 12px;
        color: #64748b;
        margin-right: 8px;
        transition: transform 0.3s ease;
        font-weight: bold;
    }

    .category-toggle.expanded {
        transform: rotate(0deg);
    }

    .category-title {
        flex: 1;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
    }

    .category-count {
        font-size: 12px;
        color: #9ca3af;
        background: #f3f4f6;
        padding: 2px 8px;
        border-radius: 12px;
        min-width: 20px;
        text-align: center;
    }

    .category-content {
        margin-left: 8px;
        border-left: 2px solid #e5e7eb;
        padding-left: 8px;
        transition: all 0.3s ease;
    }

    .category-content .agent-item {
        margin-left: 0;
    }
    
    .empty-agents-message {
        padding: 20px;
        text-align: center;
        color: #64748B;
        font-style: italic;
    }

    .agent-item {
        display: flex;
        padding: 12px;
        cursor: pointer;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .agent-item:hover {
        background-color: #f0f8ff;
        border-color: #e0e7ff;
    }

    .agent-item.selected {
        background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        transform: translateX(3px);
        position: relative;
    }

    .agent-item.selected::after {
        content: "●";
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #10b981;
        font-size: 12px;
        animation: pulse 2s infinite;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        border: 2px solid #f1f5f9;
        transition: border-color 0.3s ease;
    }

    .agent-item.selected .avatar {
        border-color: #3b82f6;
    }

    .agent-info {
        flex: 1;
        min-width: 0;
    }

    .name {
        font-weight: 600;
        font-size: 14px;
        color: #1f2937;
        margin-bottom: 4px;
    }

    .desc {
        font-size: 12px;
        color: #6b7280;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .agent-item.selected .name {
        color: #3b82f6;
        font-weight: 700;
    }

    .agent-item.selected .desc {
        color: #6366f1;
    }

    .sidebar-footer {
        padding: 15px;
        border-top: 1px solid #f0f0f0;
        background: #f8f9fa;
        text-align: center;
        font-size: 14px;
        color: #374151;
    }

    .current-agent-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 8px 12px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 8px;
        border: 1px solid #bae6fd;
    }

    .agent-indicator {
        color: #10b981;
        font-size: 16px;
        animation: pulse 2s infinite;
    }

    #current-agent {
        font-weight: 600;
        color: #0369a1;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    } 
   .chat-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: white;
    }

    .chat-header {
        padding: 15px 20px;
        background: #f0f8ff;
        border-bottom: 1px solid #e0e7ff;
        font-size: 14px;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .messages-container {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #ffffff;
    }

    .input-area {
        border-top: 1px solid #e0e0e0;
        padding: 20px;
        background: #f8f9fa;
    }

    .input-container {
        display: flex;
        gap: 12px;
        align-items: flex-end;
    }

    .user-avatar-container {
        flex-shrink: 0;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid #e5e7eb;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .user-avatar:hover {
        border-color: #3b82f6;
        transform: scale(1.05);
    }

    .user-input {
        flex: 1;
        min-height: 60px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        resize: vertical;
        font-family: inherit;
        outline: none;
        transition: border-color 0.3s ease;
    }

    .user-input:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    .send-btn {
        margin-top: 10px;
        padding: 10px 20px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s ease;
    }

    .send-btn:hover {
        background: #2563eb;
    }

    .send-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
    }

    .message-item {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        transition: all 0.3s ease;
    }

    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        flex-shrink: 0;
        border: 2px solid #f1f5f9;
        object-fit: cover;
    }

    .message-content {
        padding: 15px;
        border-radius: 12px;
        position: relative;
        flex: 1;
        min-width: 0;
        word-wrap: break-word;
        word-break: break-all;
        overflow-wrap: break-word;
    }

    .message-user {
        flex-direction: row;
        max-width: fit-content;
    }

    .message-user .message-content {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
        color: white;
        text-align: left;
        border-top-left-radius: 0;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        width: fit-content;
        min-width: 0;
    }

    .message-user .message-avatar {
        border-color: #3b82f6;
    }

    .message-ai {
        flex-direction: row;
        max-width: fit-content;
    }

    .message-ai .message-content {
        background: var(--bg-color);
        color: var(--text-color);
        border-top-left-radius: 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        width: fit-content;
        min-width: 0;
    }

    .message-ai .message-avatar {
        border-color: #f1f5f9;
    }

    /* Markdown内容样式 - 一级标题居中 */
    .message-content .markdown-container h1 {
        text-align: center;
        margin: 20px 0;
        padding: 10px 0;
        border-bottom: 2px solid #e2e8f0;
        font-weight: 700;
        color: var(--text-color);
    }

    .message-content .markdown-container h2 {
        margin: 16px 0 12px 0;
        font-weight: 600;
        color: var(--primary-color);
    }

    .message-content .markdown-container h3 {
        margin: 14px 0 10px 0;
        font-weight: 600;
        color: var(--text-color);
    }
</style>

<script>
// 全局变量 - 使用现有API接口
const API_BASE = '';
let currentMode = 'agent';
let availableAgents = [];
let currentSessionKey = null;
// 会话管理 - 为每个Agent/团队/工作流维护独立的聊天历史
let chatSessions = {
    agent: {},      // { agentId: [messages] }
    team: {},       // { teamId: [messages] }
    workflow: {}    // { workflowId: [messages] }
};

// Markdown 渲染配置
if (typeof marked !== 'undefined') {
    marked.setOptions({
        highlight: function(code, lang) {
            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {}
            }
            return code;
        },
        breaks: true,
        gfm: true
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('页面加载完成，开始初始化...');
    
    try {
        await checkHealth();
        await loadAgents();
        setupEventListeners();
        addSystemMessage('👋 欢迎使用Agno统一智能平台！请从左侧选择一个Agent开始对话。');
    } catch (error) {
        console.error('初始化过程中发生错误:', error);
        addSystemMessage('❌ 初始化失败，请检查控制台获取详细错误信息。');
    }
});

// 检查服务健康状态
async function checkHealth() {
    try {
        console.log('尝试连接服务器:', API_BASE);
        const response = await fetch(`${API_BASE}/api/health`, {
            mode: 'cors',
            headers: { 'Accept': 'application/json' }
        });
        const health = await response.json();
        console.log('服务状态:', health);
    } catch (error) {
        console.error('服务连接失败:', error);
        addSystemMessage('❌ 服务连接失败，请检查后端服务是否正常运行。');
    }
}

// 加载可用的Agent列表
async function loadAgents() {
    try {
        const response = await fetch(`${API_BASE}/api/agents`, {
            mode: 'cors',
            headers: { 'Accept': 'application/json' }
        });
        const data = await response.json();
        availableAgents = data.agents;
        
        renderAgentList();
        bindListItemEvents();
    } catch (error) {
        console.error('加载Agent列表失败:', error);
    }
}

// 渲染所有组件列表（Agent/Team/Flow）
function renderAgentList() {
    const agentList = document.getElementById('agent-list');
    agentList.innerHTML = '';
    
    // 渲染Agents分类
    if (availableAgents.length > 0) {
        const agentCategory = createCollapsibleCategory('🤖 智能体', 'agents', true);
        agentList.appendChild(agentCategory.header);
        agentList.appendChild(agentCategory.content);
        
        availableAgents.forEach(agent => {
            const listItem = createListItem(agent, 'agent');
            agentCategory.content.appendChild(listItem);
        });
        
        // 更新计数
        updateCategoryCount('agents', availableAgents.length);
    }
    
    // 加载并渲染Teams
    loadTeams().then(teams => {
        window.availableTeams = teams; // 保存到全局变量
        if (teams.length > 0) {
            const teamCategory = createCollapsibleCategory('👥 团队协作', 'teams', false);
            agentList.appendChild(teamCategory.header);
            agentList.appendChild(teamCategory.content);
            
            teams.forEach(team => {
                const listItem = createListItem(team, 'team');
                teamCategory.content.appendChild(listItem);
            });
            
            // 更新计数
            updateCategoryCount('teams', teams.length);
            
            // 重新绑定事件
            bindListItemEvents();
        }
    });
    
    // 加载并渲染Workflows
    loadWorkflows().then(workflows => {
        window.availableWorkflows = workflows; // 保存到全局变量
        if (workflows.length > 0) {
            const workflowCategory = createCollapsibleCategory('🔄 工作流程', 'workflows', false);
            agentList.appendChild(workflowCategory.header);
            agentList.appendChild(workflowCategory.content);
            
            workflows.forEach(workflow => {
                const listItem = createListItem(workflow, 'workflow');
                workflowCategory.content.appendChild(listItem);
            });
            
            // 更新计数
            updateCategoryCount('workflows', workflows.length);
            
            // 重新绑定事件
            bindListItemEvents();
        }
    });
    
    // 如果没有任何组件，显示提示信息
    if (availableAgents.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-agents-message';
        emptyMessage.textContent = '暂无可用的组件，请检查后端服务是否正常运行。';
        agentList.appendChild(emptyMessage);
    }
}

// 创建列表项
function createListItem(item, type = 'agent') {
    const listItem = document.createElement('div');
    listItem.className = 'agent-item';
    listItem.dataset.id = item.id;
    listItem.dataset.type = type;
    
    const avatarUrl = getAvatarUrl(type, item.id);
    const displayName = item.display_name || item.name || item.id;
    const description = item.brief || item.description || '';
    
    listItem.innerHTML = `
        <img src="${avatarUrl}" class="avatar" alt="${displayName}" />
        <div class="agent-info">
            <div class="name">${displayName}</div>
            <div class="desc">${description}</div>
        </div>
    `;
    
    return listItem;
}

// 获取头像URL - 从组件配置中获取
function getAvatarUrl(type, id) {
    // 从组件配置中获取头像
    if (type === 'agent') {
        const agent = availableAgents.find(a => a.id === id);
        if (agent && agent.avatar) {
            return agent.avatar;
        }
    } else if (type === 'team') {
        // 从teams配置中获取
        const team = window.availableTeams ? window.availableTeams.find(t => t.id === id) : null;
        if (team && team.avatar) {
            return team.avatar;
        }
    } else if (type === 'workflow') {
        // 从workflows配置中获取
        const workflow = window.availableWorkflows ? window.availableWorkflows.find(w => w.id === id) : null;
        if (workflow && workflow.avatar) {
            return workflow.avatar;
        }
    }
    
    // 默认头像
    return 'touxiang/agasf.png';
}

// 绑定列表项点击事件
function bindListItemEvents() {
    document.querySelectorAll('.agent-item').forEach(item => {
        // 先移除可能存在的旧事件监听器
        const newItem = item.cloneNode(true);
        item.parentNode.replaceChild(newItem, item);

        // 绑定新的事件监听器
        newItem.addEventListener('click', function() {
            // 移除所有active类
            document.querySelectorAll('.agent-item').forEach(i => {
                i.classList.remove('selected');
            });
            // 添加selected类到当前项
            this.classList.add('selected');

            // 更新聊天标题和当前模式
            const name = this.querySelector('.name').textContent;
            const type = this.dataset.type;
            const id = this.dataset.id;

            document.querySelector('#chat-agent-name').textContent = name;
            document.querySelector('#current-agent').textContent = name;

            // 设置当前会话和模式
            currentSessionKey = id;
            currentMode = type;

            // 加载会话历史
            loadSessionHistory(currentSessionKey);

            // 显示欢迎消息
            showWelcomeMessage(type, id, name);
        });
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 发送按钮点击事件
    document.getElementById('send-btn').addEventListener('click', sendMessage);
    
    // 输入框回车事件
    document.getElementById('user-input').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
}

// 发送消息
async function sendMessage() {
    const input = document.getElementById('user-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 检查是否选择了会话
    if (!currentSessionKey) {
        addSystemMessage('请先从左侧选择一个组件');
        return;
    }
    
    // 添加用户消息
    addMessage(message, true);
    input.value = '';

    // 设置发送按钮为禁用状态
    document.getElementById('send-btn').disabled = true;
    document.getElementById('send-btn').textContent = '发送中...';

    // 添加小延迟确保用户消息先显示
    await new Promise(resolve => setTimeout(resolve, 50));

    try {
        // 根据当前模式发送不同类型的消息
        switch(currentMode) {
            case 'agent':
                await sendAgentMessage(message);
                break;
            case 'team':
                await sendTeamMessage(message);
                break;
            case 'workflow':
                await sendWorkflowMessage(message);
                break;
            default:
                await sendAgentMessage(message);
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        addSystemMessage(`❌ 发送消息失败: ${error.message}`);
    } finally {
        // 恢复发送按钮状态
        document.getElementById('send-btn').disabled = false;
        document.getElementById('send-btn').textContent = '发送消息';
    }
}

// 发送Agent消息 - 使用流式响应
async function sendAgentMessage(message) {
    const response = await fetch(`${API_BASE}/api/chat`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            agent_id: currentSessionKey,
            stream: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    await handleStreamResponse(response);
}

// 发送团队消息
async function sendTeamMessage(message) {
    const response = await fetch(`${API_BASE}/api/team`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            team_id: currentSessionKey,
            stream: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    await handleStreamResponse(response);
}

// 发送工作流消息
async function sendWorkflowMessage(message) {
    const response = await fetch(`${API_BASE}/api/workflow`, {
        method: 'POST',
        mode: 'cors',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            input_data: { message: message },
            workflow_id: currentSessionKey,
            stream: false
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    const results = Object.values(data.results || {}).join('\n\n');
    addMessage(results || '工作流执行完成', false);
}

// 处理流式响应
async function handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let currentMessage = '';
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop();
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const dataStr = line.substring(6);
                    
                    try {
                        const data = JSON.parse(dataStr);
                        
                        if (data.type === 'content') {
                            currentMessage += data.content;
                            updateStreamingMessage(currentMessage);
                        } else if (data.type === 'end') {
                            if (currentMessage) {
                                // 将流式消息转换为正常消息，而不是创建新的消息容器
                                finalizeStreamingMessage(currentMessage);
                                currentMessage = ''; // 清空消息，避免重复添加
                            }
                        } else if (data.type === 'error') {
                            addSystemMessage(`❌ 错误: ${data.error}`);
                        }
                    } catch (e) {
                        console.log('非JSON数据:', dataStr);
                    }
                }
            }
        }

        // 注意：不要在这里再次添加消息，因为在 'end' 事件中已经处理了
        // 这里的检查主要是为了处理异常情况
        if (currentMessage) {
            console.warn('流式响应结束时仍有未处理的消息:', currentMessage);
            addMessage(currentMessage, false);
        }

    } catch (streamError) {
        console.error('流处理错误:', streamError);
        addSystemMessage(`❌ 流处理错误: ${streamError.message}`);
    }
}

// 添加消息
function addMessage(content, isUser = false, saveToHistory = true) {
    const messagesDiv = document.getElementById('messages-container');

    // 只有在添加AI消息时才移除流式消息，避免影响用户消息的显示
    if (!isUser) {
        const streamingMsg = messagesDiv.querySelector('.streaming-message');
        if (streamingMsg) {
            streamingMsg.remove();
        }
    }

    // 创建消息容器
    const messageContainer = document.createElement('div');
    messageContainer.className = isUser ? 'message-item message-user' : 'message-item message-ai';

    // 创建头像
    const avatar = document.createElement('img');
    avatar.className = 'message-avatar';

    if (isUser) {
        avatar.src = 'touxiang/996.png';
        avatar.alt = '用户头像';
    } else {
        avatar.src = getAvatarUrl('agent', currentSessionKey);
        avatar.alt = `${currentSessionKey} 头像`;
    }

    // 创建消息内容
    const message = document.createElement('div');
    message.className = 'message-content';

    // 处理Markdown内容
    try {
        if (isUser) {
            message.textContent = content;
        } else {
            const htmlContent = marked.parse(content);
            message.innerHTML = `<div class="markdown-container">${htmlContent}</div>`;

            // 高亮代码块
            message.querySelectorAll('pre code').forEach((block) => {
                if (typeof hljs !== 'undefined') {
                    hljs.highlightElement(block);
                }
            });
        }
    } catch (error) {
        message.textContent = content;
    }

    // 组装消息
    messageContainer.appendChild(avatar);
    messageContainer.appendChild(message);
    messagesDiv.appendChild(messageContainer);

    // 只有在需要时才保存到会话历史（避免加载历史时重复保存）
    if (currentSessionKey && saveToHistory) {
        saveMessageToSession(content, isUser ? 'user' : 'ai');
    }

    // 滚动到底部
    scrollToBottom();

    return messageContainer;
}

// 添加系统消息
function addSystemMessage(content) {
    const messagesDiv = document.getElementById('messages-container');
    
    const systemMessage = document.createElement('div');
    systemMessage.className = 'system-message';
    systemMessage.style.textAlign = 'center';
    systemMessage.style.margin = '10px 0';
    systemMessage.style.color = '#666';
    systemMessage.style.fontSize = '14px';
    systemMessage.style.padding = '10px';
    systemMessage.style.background = '#f0f8ff';
    systemMessage.style.borderRadius = '8px';
    
    try {
        const htmlContent = marked.parse(content);
        systemMessage.innerHTML = htmlContent;
    } catch (error) {
        systemMessage.textContent = content;
    }
    
    messagesDiv.appendChild(systemMessage);
    scrollToBottom();
}

// 更新流式消息显示
function updateStreamingMessage(content) {
    const messagesDiv = document.getElementById('messages-container');
    let streamingMsg = messagesDiv.querySelector('.streaming-message');
    
    if (!streamingMsg) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'message-item message-ai';
        
        const avatar = document.createElement('img');
        avatar.className = 'message-avatar';
        avatar.src = getAvatarUrl('agent', currentSessionKey);
        avatar.alt = `${currentSessionKey} 头像`;
        
        streamingMsg = document.createElement('div');
        streamingMsg.className = 'message-content streaming-message';
        
        messageContainer.appendChild(avatar);
        messageContainer.appendChild(streamingMsg);
        messagesDiv.appendChild(messageContainer);
    }
    
    // 更新内容
    try {
        const htmlContent = marked.parse(content);
        streamingMsg.innerHTML = `<div class="markdown-container">${htmlContent}</div>`;
    } catch (error) {
        streamingMsg.textContent = content;
    }
    
    scrollToBottom();
}

// 将流式消息转换为正常消息
function finalizeStreamingMessage(content) {
    const messagesDiv = document.getElementById('messages-container');
    const streamingMsg = messagesDiv.querySelector('.streaming-message');

    if (streamingMsg) {
        // 移除流式消息标记，使其成为正常消息
        streamingMsg.classList.remove('streaming-message');

        // 保存到会话历史
        if (currentSessionKey) {
            saveMessageToSession(content, 'ai');
        }
    }
}

// 保存消息到会话历史
function saveMessageToSession(content, type) {
    if (!currentSessionKey) return;

    if (!chatSessions[currentMode][currentSessionKey]) {
        chatSessions[currentMode][currentSessionKey] = [];
    }

    chatSessions[currentMode][currentSessionKey].push({
        content: content,
        type: type,
        timestamp: Date.now()
    });
}

// 加载会话历史
function loadSessionHistory(sessionKey) {
    if (!sessionKey) return;

    const messagesDiv = document.getElementById('messages-container');
    messagesDiv.innerHTML = '';

    const sessionMessages = chatSessions[currentMode][sessionKey] || [];

    sessionMessages.forEach(msg => {
        // 加载历史消息时不重复保存到会话历史
        addMessage(msg.content, msg.type === 'user', false);
    });
}

// 自动滚动到底部
function scrollToBottom() {
    const messagesDiv = document.getElementById('messages-container');
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

// 创建可折叠分类
function createCollapsibleCategory(title, categoryId, isExpanded = true) {
    // 创建分类标题
    const header = document.createElement('div');
    header.className = 'category-header';
    header.dataset.category = categoryId;
    header.innerHTML = `
        <span class="category-toggle ${isExpanded ? 'expanded' : ''}">${isExpanded ? '▼' : '▶'}</span>
        <span class="category-title">${title}</span>
        <span class="category-count" id="${categoryId}-count">0</span>
    `;
    
    // 创建分类内容容器
    const content = document.createElement('div');
    content.className = 'category-content';
    content.id = `${categoryId}-content`;
    content.style.display = isExpanded ? 'block' : 'none';
    
    // 添加点击事件
    header.addEventListener('click', () => {
        const toggle = header.querySelector('.category-toggle');
        const isCurrentlyExpanded = toggle.classList.contains('expanded');
        
        if (isCurrentlyExpanded) {
            toggle.classList.remove('expanded');
            toggle.textContent = '▶';
            content.style.display = 'none';
        } else {
            toggle.classList.add('expanded');
            toggle.textContent = '▼';
            content.style.display = 'block';
        }
    });
    
    return { header, content };
}

// 更新分类计数
function updateCategoryCount(categoryId, count) {
    const countElement = document.getElementById(`${categoryId}-count`);
    if (countElement) {
        countElement.textContent = count;
    }
}

// 加载团队列表
async function loadTeams() {
    try {
        const response = await fetch(`${API_BASE}/api/teams`, {
            mode: 'cors',
            headers: { 'Accept': 'application/json' }
        });
        if (response.ok) {
            const data = await response.json();
            return data.teams || [];
        }
    } catch (error) {
        console.error('加载团队列表失败:', error);
    }
    return [];
}

// 加载工作流列表
async function loadWorkflows() {
    try {
        const response = await fetch(`${API_BASE}/api/workflows`, {
            mode: 'cors',
            headers: { 'Accept': 'application/json' }
        });
        if (response.ok) {
            const data = await response.json();
            return data.workflows || [];
        }
    } catch (error) {
        console.error('加载工作流列表失败:', error);
    }
    return [];
}

// 显示欢迎消息
function showWelcomeMessage(type, id, name) {
    const welcomeMessages = {
        'agent': `👋 您好！我是 ${name}，很高兴为您服务！请告诉我您需要什么帮助。`,
        'team': `👥 欢迎使用 ${name} 团队协作模式！我们的团队将协同为您提供最佳解决方案。`,
        'workflow': `🔄 ${name} 工作流已准备就绪！请提供输入数据，我将按照预设流程为您处理。`
    };
    
    const message = welcomeMessages[type] || `欢迎使用 ${name}！`;
    addSystemMessage(message);
}

// 搜索功能
document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('agent-search');
    const clearButton = document.getElementById('clear-search');
    
    if (searchInput && clearButton) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            const agentItems = document.querySelectorAll('.agent-item');
            
            if (query) {
                clearButton.style.display = 'block';
            } else {
                clearButton.style.display = 'none';
            }
            
            agentItems.forEach(item => {
                const name = item.querySelector('.name').textContent.toLowerCase();
                const desc = item.querySelector('.desc').textContent.toLowerCase();
                
                if (name.includes(query) || desc.includes(query)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
        
        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            this.style.display = 'none';
            
            // 显示所有项目
            document.querySelectorAll('.agent-item').forEach(item => {
                item.style.display = 'flex';
            });
        });
    }
});

</script>
</body>
</html>