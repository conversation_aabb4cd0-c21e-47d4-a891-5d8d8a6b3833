{"name": "lowdb", "versions": {"0.1.0": {"name": "lowdb", "version": "0.1.0", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.1.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "bc70c6b7b0e355f30d706b34e81528cfb0b1e27b", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.1.0.tgz", "integrity": "sha512-Btd5HlTY36lAR16pv5L29WJn6xuHzySLJrw+L/nL0HLEBxXjG8BfyaStbnjHN3od3RkJAEyPHddUKhd9aBvxsg==", "signatures": [{"sig": "MEQCIDPUGIt2dxBoFf5+amJ/CtOFvaE3e0YhisVNoCIrWm5tAiAwy8Z2j1NIuTiO4Ib3frkSe85oouNH09IKC+rSJqMy3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "scripts": {"test": "grunt mochaTest", "prepublish": "grunt build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "JSON database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "underscore.db": "~0.6.0"}, "devDependencies": {"b": "^2.0.1", "grunt": "^0.4.3", "sinon": "~1.8.2", "grunt-coffeelint": "0.0.8", "grunt-mocha-test": "^0.9.4", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-watch": "^0.5.3", "grunt-contrib-coffee": "^0.10.1"}, "contributors": []}, "0.2.0": {"name": "lowdb", "version": "0.2.0", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.2.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "b520470187b0d7a5c60e5ca57ae33c2493008f12", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.2.0.tgz", "integrity": "sha512-5Kr9X2l4Tc123pxfddSaYAUifN75jWjx6F05pPAhzlrIIYJl0Z4QQWy10X3PlaBfQYWIfSEith6VSoTFY+YKNQ==", "signatures": [{"sig": "MEQCICOJig55jZEHjIWOQAaKd8Jsb3iXT1hlaXqwja3/olPoAiAiXlL4l7C/N26zu797RGuNSz7RszDxsaUPp51uhYpBNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b520470187b0d7a5c60e5ca57ae33c2493008f12", "scripts": {"test": "grunt mochaTest", "prepublish": "grunt build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "JSON database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "underscore.db": "~0.6.0"}, "devDependencies": {"b": "^2.0.1", "grunt": "^0.4.3", "sinon": "~1.8.2", "grunt-cli": "^0.1.13", "grunt-coffeelint": "0.0.8", "grunt-mocha-test": "^0.9.4", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-watch": "^0.5.3", "grunt-contrib-coffee": "^0.10.1"}, "contributors": []}, "0.3.0": {"name": "lowdb", "version": "0.3.0", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.3.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "0e591164cfcac746589ad33da2e85bcd4eca0bfe", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.3.0.tgz", "integrity": "sha512-10evGzPT/V6nCeC8AEdIDfOFOYWOk+f5He63NCSTWh6ZlZOBTQwAczvT1vR1vqmmYtoBKoMMNmqQTkqTuEbm7g==", "signatures": [{"sig": "MEQCIARmUBESnava8JiFJax9wiqrtM3Ygmg460MPtLVanLdEAiAV432Mso8jaOjl+h9DlMDoBgdlHM1QG6xHrst4jljqrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "0e591164cfcac746589ad33da2e85bcd4eca0bfe", "scripts": {"test": "grunt mochaTest", "benchmark": "grunt build && cd benchmark && node index.js", "prepublish": "grunt build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Flat JSON file database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "underscore.db": "~0.6.0"}, "devDependencies": {"b": "^2.0.1", "grunt": "^0.4.3", "sinon": "~1.8.2", "grunt-cli": "^0.1.13", "grunt-coffeelint": "0.0.8", "grunt-mocha-test": "^0.9.4", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-watch": "^0.5.3", "grunt-contrib-coffee": "^0.10.1"}, "contributors": []}, "0.3.1": {"name": "lowdb", "version": "0.3.1", "keywords": ["flat", "file", "database", "JSON", "JSONDatabase", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.3.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "520e39a391ccb04a55a6b39a0e845f1fb78e1257", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.3.1.tgz", "integrity": "sha512-LRuSi/SaXSTAuNKIFjiSELRX5Aal6b05sBVsg3jg9ynNbQNsgQLitiTQFXSc5pj058zAKr4EA78EzvxARR+8mw==", "signatures": [{"sig": "MEUCICkBsm15gudtgxfBEfEv4BkTRPRbxlMvT6j2x5jsIDTvAiEA4umpQ4HlbA/RlYGx+twVV07sIALd9Zi+DaMSUsEENos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "520e39a391ccb04a55a6b39a0e845f1fb78e1257", "gitHead": "5f3a1fe94469f4bfb5594871a861bdde6f62b1c0", "scripts": {"test": "grunt mochaTest", "benchmark": "grunt build && cd benchmark && node index.js", "precommit": "npm test", "prepublish": "grunt build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flat JSON file database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "underscore.db": "~0.6.0"}, "devDependencies": {"b": "^2.0.1", "grunt": "^0.4.3", "husky": "^0.4.3", "sinon": "~1.8.2", "grunt-cli": "^0.1.13", "grunt-coffeelint": "0.0.8", "grunt-mocha-test": "^0.9.4", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-watch": "^0.5.3", "grunt-contrib-coffee": "^0.10.1"}, "contributors": []}, "0.3.2": {"name": "lowdb", "version": "0.3.2", "keywords": ["flat", "file", "database", "JSON", "JSONDatabase", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.3.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "21f90071c67c226746de4ea560d331415fccbb48", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.3.2.tgz", "integrity": "sha512-1IUbEgwDybaccx7a+K4G+cbGKirwTtybkwu5iQ2pXIPdLw4sGn+ZtiHv4V8rPdI1clJMMo9sH5u80PQiL/+ygg==", "signatures": [{"sig": "MEYCIQC//RYlQyRB2RSdvwoPJG2pxu07d5IOH1/qMVjVQJHiHgIhANUyDqiWIeYrpdlSg0nzX6ANdNGdrFIkifN1TJiNEoUm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "21f90071c67c226746de4ea560d331415fccbb48", "gitHead": "0ccad42653bbf2d63db3ec8b3446de516aac4703", "scripts": {"test": "grunt mochaTest", "benchmark": "grunt build && cd benchmark && node index.js", "precommit": "npm test", "prepublish": "grunt build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flat JSON file database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "underscore.db": "~0.6.0"}, "devDependencies": {"b": "^2.0.1", "grunt": "^0.4.3", "husky": "^0.4.3", "sinon": "~1.8.2", "grunt-cli": "^0.1.13", "grunt-coffeelint": "0.0.8", "grunt-mocha-test": "^0.9.4", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-watch": "^0.5.3", "grunt-contrib-coffee": "^0.10.1"}, "contributors": []}, "0.4.0": {"name": "lowdb", "version": "0.4.0", "keywords": ["flat", "file", "database", "JSON", "JSONDatabase", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.4.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "6c9a36126b7f7aa745eb77180ae172a0b2334255", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.4.0.tgz", "integrity": "sha512-cYMSG81nI2fqIevKzqQzGlp+0md4BxyFRPmrmnWBjEufUL8Uw7T+P0/g+q7vxUHvntE5O0B8Q1o3rvJPfYrNZQ==", "signatures": [{"sig": "MEUCIAMAzu1xiRgW8N3k8/jo24vi6us9SFRWB6znitBEH2C4AiEAkU5NnfioZuTR7EdITUWZNNo7QhLdtASMmPqw3fgWBrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "6c9a36126b7f7aa745eb77180ae172a0b2334255", "gitHead": "7b23af519203108ebfba92343fe1e520147a342b", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "deprecated": "important bug fixed, please update to >=0.4.2", "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flat JSON file database", "directories": {}, "dependencies": {"lodash": "~2.4.1", "temp-write": "^1.0.0", "graceful-fs": "^3.0.2"}, "devDependencies": {"husky": "^0.6.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore.db": "^0.7.0"}, "contributors": []}, "0.4.1": {"name": "lowdb", "version": "0.4.1", "keywords": ["flat", "file", "database", "JSON", "JSONDatabase", "lo-dash", "lodash", "underscore", "underscore.db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.4.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "12f24e7d1cd90ea2dfa91d216be986a185bfa91d", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.4.1.tgz", "integrity": "sha512-pe6AWF+6w3pDlcDR8lokLdnY5Pymq7yV3eT6yW28KTIn9ySRvyx/oOBQKJFK8IZ70doRpq7ggKj1Mzy7ez9zdQ==", "signatures": [{"sig": "MEUCIQCATKJVRUfEO58DfO2KalXMMQIXl6PhHcU+s7aOghdAXQIgAIf3YMqWV3CJW1Ltd73fKFQeg3UzbaJohE2Toz9i1gE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "12f24e7d1cd90ea2dfa91d216be986a185bfa91d", "gitHead": "bfdb27112c8846c199edc596952d67ecbc13d422", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "deprecated": "important bug fixed, please update to >=0.4.2", "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flat JSON file database", "directories": {}, "dependencies": {"mv": "^2.0.3", "lodash": "~2.4.1", "temp-write": "^1.0.0", "graceful-fs": "^3.0.2"}, "devDependencies": {"husky": "^0.6.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore.db": "^0.7.0"}, "contributors": []}, "0.4.2": {"name": "lowdb", "version": "0.4.2", "keywords": ["flat", "file", "database", "JSON", "JSONDatabase", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.4.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "ca67f7a7163e93de16ae8d76c774c9be2a31b3ca", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.4.2.tgz", "integrity": "sha512-ZrcDV6e8tx9jNA63oSli3DZaY5vICYYUAi9l+g1z8bqailJRG/zQhflfdHm22Kx0qGmrX8F2025qPjroR8YViw==", "signatures": [{"sig": "MEUCICKjsSMQevYrrL7C97GuWfaE1C0MNsa8leEqQTF+DkcLAiEAyeT4HyAFPAsCb0AdaR4ZLeh7wNa2larjfpS80rx4BIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "ca67f7a7163e93de16ae8d76c774c9be2a31b3ca", "gitHead": "723491c74dd7e5a55ceed79014885bce0ee3e8a9", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"mv": "^2.0.3", "lodash": "~2.4.1", "temp-write": "^1.0.0", "graceful-fs": "^3.0.2"}, "devDependencies": {"husky": "^0.6.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.5.0": {"name": "lowdb", "version": "0.5.0", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.5.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "06a7927a93ad7e3ff4b1d8042b0581cc95edfeb9", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.5.0.tgz", "integrity": "sha512-d7fNyumjPqmqXchdTCQffs6Ze96GBsTzpxHtKCKhxrh01hReJTmw23f4abEvTFseQQAGRlnVnfKQ452uxcHn4Q==", "signatures": [{"sig": "MEUCIBBkvI+FW9XqMLbqlsaqHmtqDoalaPnTiwcOtSFEvJxlAiEAhgFZDXxiwUqNBa8aq1hO9OdJ79RRf2UbbOzPKFdize4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "06a7927a93ad7e3ff4b1d8042b0581cc95edfeb9", "gitHead": "f801a02b5e498e76b555a1997e1bf76592783e42", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"mv": "^2.0.3", "lodash": "~2.4.1", "temp-write": "^1.0.0", "graceful-fs": "^3.0.2"}, "devDependencies": {"husky": "^0.6.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.5.1": {"name": "lowdb", "version": "0.5.1", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.5.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "8891105f21b8e60417d3c076ce2900915a43decf", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.5.1.tgz", "integrity": "sha512-0wlmxzT9Ymdp80NQJIGZeP+Lg9VJaR3GfYjxKeJfDbxyMABOuDVob3wlDeV0iteFgV5XKGEu3XE2HCpSsHVyqw==", "signatures": [{"sig": "MEUCIQDDU6l8om0mlO8x2H5hGOmfbfoIBwXk5MhFB5m2YTmWRgIgbYVqyBjbcPPOkejAN6V6wsUFQGRHTagu1Npcut6jyZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "8891105f21b8e60417d3c076ce2900915a43decf", "gitHead": "913f691456e70a443365cf420851f41b53bc4870", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.1.4", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"mv": "^2.0.3", "uuid": "^2.0.1", "steno": "^0.1.1", "lodash": "~2.4.1", "graceful-fs": "^3.0.2"}, "devDependencies": {"husky": "^0.6.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.6.0": {"name": "lowdb", "version": "0.6.0", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.6.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "5f358dd89b054b04e9b79ad2de982d8b65507a7d", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.6.0.tgz", "integrity": "sha512-BsQL5V4E2k6PeKUNowCnyd6Qg3jNoR+Diol/ZYDx8JkWoNwbTRdBn4n65EJ3Lv078L3eMQMk8Dl/CQbjdX15zA==", "signatures": [{"sig": "MEUCIH5O9v1eUTlitWkJAW/MGJta+5i8Km3bNywE3dr6t2YcAiEAr/vV/fgj2XHAs07wdRe/6/thKwoe1OVVUjwB1bItvP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "5f358dd89b054b04e9b79ad2de982d8b65507a7d", "gitHead": "02536e8fee414995c3492b0d3e868f1725dac292", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"steno": "^0.3.1", "lodash": "~2.4.1"}, "devDependencies": {"husky": "^0.6.2", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.6.1": {"name": "lowdb", "version": "0.6.1", "keywords": ["flat", "file", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.6.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "df896456a3cd7d34a1b7700cccdf3f5e82cd89c2", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.6.1.tgz", "integrity": "sha512-nIEzjWG9lhWGBOwILRHCmCTSTCAZwDL3jG0qnYuzqvLoYbE1cR5pZ0BvZXrB/RFzCGMLqM6/FDpsyfZ+fD3uQg==", "signatures": [{"sig": "MEYCIQCxWNjhVsPGk1ZwXiD5pik7i5gA36O+KnhihCtO4oGsagIhALiMOxgMjRuatS7yMEHFGDkiZz5PoFTafqcOti3FS75t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "df896456a3cd7d34a1b7700cccdf3f5e82cd89c2", "gitHead": "c42ef38e4260531aeda2c08b5f37ab94e29a95a2", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Flat JSON file database", "directories": {}, "dependencies": {"steno": "^0.3.1", "lodash": "~2.4.1"}, "devDependencies": {"husky": "^0.6.2", "mocha": "^1.21.4", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.7.0": {"name": "lowdb", "version": "0.7.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.7.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "d0c6df17ec6458c3f446a2a91ec076c0d2157e8c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.7.0.tgz", "integrity": "sha512-ToJMwSV0uZkaUFLd8QP52uwXV0ul5kdSZKdJjVQVTDaO4ln3cjKJpO6976gA0GX874iLgx/ZFzRzQ7utq7kxZw==", "signatures": [{"sig": "MEUCIQC3w4h9C/uZwU83zaFEIlDPxri827cpq89/mMnyfkp7EwIgHe7mu3hXirWoDj1vKS4P2/05RA1UcOKMNbPhJghKPbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "d0c6df17ec6458c3f446a2a91ec076c0d2157e8c", "gitHead": "513f62cc9c1478cdfdda1820702cc4ce13b5c7a9", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Flat JSON file database", "directories": {}, "dependencies": {"steno": "^0.3.2", "lodash": "^3.0.1"}, "devDependencies": {"husky": "^0.6.2", "mocha": "^1.21.4", "sinon": "^1.12.2", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.7.1": {"name": "lowdb", "version": "0.7.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.7.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "6aca761c7d3851c457cbf8f1903b319188ce59c7", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.7.1.tgz", "integrity": "sha512-J0G8r+cv+b4YVCCr8g0l6jb/jN5GwPgkGjZjZmvOxN1re6xKP0q1lDS0ZmS1RD8NoqbEQV2kUf5BzZmuvtzoGQ==", "signatures": [{"sig": "MEUCIF/OcctN/DwnWeLC2kvinSbWfBIadEXKIRkL03JDM4BnAiEA2bT4C8ASBvo41aw+gPLJse4Gerj62L6P4X+x8BBfg9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "6aca761c7d3851c457cbf8f1903b319188ce59c7", "gitHead": "77637fbee0d5d59b961993969ab92931484df1f4", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Flat JSON file database", "directories": {}, "dependencies": {"steno": "^0.3.2", "lodash": "^3.1.0"}, "devDependencies": {"husky": "^0.6.2", "mocha": "^1.21.4", "sinon": "^1.12.2", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.7.2": {"name": "lowdb", "version": "0.7.2", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.7.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "33232ff718b266bb4d811afb50ba65670ca68c8c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.7.2.tgz", "integrity": "sha512-8QVzj5SuJycFqA7oAYbxCpEcaE7a3hG6ZLN6WtQU7ogweMtG3+/9yHKWhaJL4MpNDrv5R0pbSQJ/q+o7jfJq5A==", "signatures": [{"sig": "MEYCIQCS45t94JT3lzzfNuNbFOcd85F19pIFxKZMBfcLc5kPmgIhAKRetQG8wtWtRBajKoA6wuk6MW4QTB7FiP4JqgPFfRqw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "33232ff718b266bb4d811afb50ba65670ca68c8c", "gitHead": "2c4b6880d91434d1b9fb3fbceefa3df2e4101cfa", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"steno": "^0.3.2", "lodash": "^3.1.0"}, "devDependencies": {"husky": "^0.6.2", "mocha": "^1.21.4", "sinon": "^1.12.2", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.7.3": {"name": "lowdb", "version": "0.7.3", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.7.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "2047e3f3617ef647879141618834d59fe7bdc803", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.7.3.tgz", "integrity": "sha512-GOVgZOb/1XCiokzy+4/QWInH/2QORy9m78KTwfIGEQXBJTQzsp5xXu9mQQSkN4HsmDblMXKyOGCEXhUuMT4rRQ==", "signatures": [{"sig": "MEUCIBoq0BP2RlgszGhoKm85CvDRVYf8KieHGOZV0rGyoTotAiEAnaXR7DdyTP2nOV9k3Q4Tow6NlUkjuUP4oUIjaKey258=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "2047e3f3617ef647879141618834d59fe7bdc803", "gitHead": "442e45cf6b45a3a165147eb353b3d91f44ad66ec", "scripts": {"test": "mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"steno": "^0.3.2", "lodash": "^3.1.0"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^1.21.4", "sinon": "^1.12.2", "rimraf": "^2.2.8", "underscore-db": "^0.8.0"}, "contributors": []}, "0.8.0": {"name": "lowdb", "version": "0.8.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.8.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "91a8f08eb767af883ca27aa5886ddb817239b9c4", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.8.0.tgz", "integrity": "sha512-VzfzIG1kOXcIPV8w5B24pNes3lh9DZWtFU6DmOe+vASPmOMwsGzAHYIYSOjsrvzikEThKz149TAbwBZTtzithg==", "signatures": [{"sig": "MEUCIQCAU2NGLbGXyiwiF85qs4n33AW4vr0VsseGcWxwvcS96gIgdR7ZNWsjWzmDal7Yx1LewXFZc7UtMBv65BL6Chxgwss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "91a8f08eb767af883ca27aa5886ddb817239b9c4", "gitHead": "2daeeef1339b00f8210123e4f072ab813a4a34b6", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.8.0"}, "contributors": []}, "0.8.1": {"name": "lowdb", "version": "0.8.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.8.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "361c47c9cdc2cc1d22839e8664d7557c7169aeb0", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.8.1.tgz", "integrity": "sha512-iKk8skQSTd7UEYYZjme8XSDCs0DkPapmJjP8fDiLmvGCjVI6l3IA80O9sNFlquEAWAZFcye7GPNw7prbx7FfUQ==", "signatures": [{"sig": "MEYCIQDvk8nllxEf5fw//1cmTDwRcLbQSHT9Tc3IUVfjrfRhZQIhALZ6tTlofsZaiB2+2Kzp0xyLQ24TUtnSGbdNY2c1fxYI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "361c47c9cdc2cc1d22839e8664d7557c7169aeb0", "gitHead": "f231c3bf7863011a515e2b4d80f1d43f6e1981b9", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.8.0"}, "contributors": []}, "0.9.0": {"name": "lowdb", "version": "0.9.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.9.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "f33a6c04f991cafc47ab8e7f5486196945d80405", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.9.0.tgz", "integrity": "sha512-ko8uEEDOygsVl3F160XVMHTZDQgGFNab0hM3aGUaJhmYLjyxYIQiSSHO8B3HecgQDFyrN3KhmMmptk25SFY12w==", "signatures": [{"sig": "MEUCICwVNVzdAD4+hHTEhpc9e7HBcx/ZXy/Pbww9qHaNJxVuAiEAn4KTn636P4e5MgKQ4JgeTzem3VcEWVfb/8/9Whf9Xl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "f33a6c04f991cafc47ab8e7f5486196945d80405", "gitHead": "d89d78fe499a4e495e7f18f16b78e0136e381b32", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.9.0"}, "contributors": []}, "0.10.0": {"name": "lowdb", "version": "0.10.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.10.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "8269f2d5bff4551b675cc05e7692f29fb1b707da", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.10.0.tgz", "integrity": "sha512-QigblLOQ46BEcQofvICexn+VeCJzDvB4dpLOXlW14OohuTke4DvHVvvT+RH7GMNdMQyU7v5u8GRQgc2lFBYMwg==", "signatures": [{"sig": "MEUCIGBl1tW40Zr6/UquF/XOqwmnkssatGmpbFI+JbcgSTwGAiEAnn9P3Xjx6Y9RCmesVZO/K4H9Uaw1HQuV66gcbjQiho4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "8269f2d5bff4551b675cc05e7692f29fb1b707da", "gitHead": "994d8ef1cefec31f3cb7e892ccf0460117ee361a", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.9.0"}, "contributors": []}, "0.10.1": {"name": "lowdb", "version": "0.10.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.10.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "f1a4d70f898dd2216ef0a21fc8361c790c7c630a", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.10.1.tgz", "integrity": "sha512-B2s5q3ybMIh5LsV/19BRxSQlb04eaE6ZXf1/HkiuX2mCvJ5MrUGJJ2y4hjx0fxUkbLR23EnHgdPH3tnUkfEGXA==", "signatures": [{"sig": "MEYCIQCu7vOdAWutl+YlcwYNAFSqlzPrC7SW4h7aicdWCB/h8gIhALN9cC7odwdgMVewXqWezk+NiGZDK73wiGiWZ3APlSKp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "f1a4d70f898dd2216ef0a21fc8361c790c7c630a", "gitHead": "1ea859f39cacf32372c5543e552ef05a71bb3d17", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.9.0"}, "contributors": []}, "0.10.2": {"name": "lowdb", "version": "0.10.2", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.10.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "6c8275289759cde26f0b49842632d92b62ba60a9", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.10.2.tgz", "integrity": "sha512-MrHd++klCHW83bVF4cDyBXFZuFqVibsBf/GS2H9qvC8ks6NKo0TcUB1E1fXmiYK8p5f7Uoptjj8s94Rijwk8og==", "signatures": [{"sig": "MEUCIAlgcdi/vJVhJ8guLScsqUDsxPeAjcZyqPRfnbi95YrYAiEAw+JYP6Vz8P7oJOo4Rr4AjPqHHPok51C7uRqDGa2kLu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "6c8275289759cde26f0b49842632d92b62ba60a9", "gitHead": "2f5c69770b3182998b08d65915ab13099e97896b", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"husky": "^0.7.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.9.0"}, "contributors": []}, "0.10.3": {"name": "lowdb", "version": "0.10.3", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "underscore-db", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.10.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "a025412f48bb0aa4918b5396d7f0285b99154c9c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.10.3.tgz", "integrity": "sha512-bgWUMZY1JE1cJYQ8nK4dZ5UTse73q3twWT5vfPeLZWbHu/CeDiHJrpSx3+KiBXBG0CZB2ci9fNxj1kRdWRAhuw==", "signatures": [{"sig": "MEUCIQDnRoMuwKyPFeDv8ZGuKja8UW3+NNwllEA5Sdy9lOKQqgIgbtQyOsZ71PUrWERCu82bdLaj/h5x6h7tD0JdtXiXrKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "a025412f48bb0aa4918b5396d7f0285b99154c9c", "gitHead": "c3e7131d18f5e75c1a393527588daf97e039cc61", "scripts": {"test": "standard && mocha", "precommit": "npm test"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Flat JSON file database", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"q": "^1.4.1", "steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"husky": "^0.9.0", "mocha": "^2.2.5", "sinon": "^1.12.2", "rimraf": "^2.2.8", "standard": "^4.0.1", "underscore-db": "^0.9.0"}, "contributors": []}, "0.11.0": {"name": "lowdb", "version": "0.11.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "8579c879e5dc9657de45ce69273f0d7b12110eed", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.0.tgz", "integrity": "sha512-pXqXIX+tEZw4zdhHD1AW7CqjguWl+6yI48SoZfI1ay/wR8m4QXikTWdVh63ugWZ5+PKqoRizjhRGb/6qcfNqPQ==", "signatures": [{"sig": "MEUCIE/CTXlNPSleM3LJTcgJrv00v4COQpeG8iuLywWJ/THYAiEAwDcNLMqM3n3WeHAkuEG65I1fWxid5UHHcSVMIImpWxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "_shasum": "8579c879e5dc9657de45ce69273f0d7b12110eed", "engines": {"node": ">= 0.12"}, "gitHead": "5408cb023cd7aa8c5737b61027e50cb103d5b660", "scripts": {"test": "babel-node test/all && standard", "build": "babel src --out-dir lib", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.11.1": {"name": "lowdb", "version": "0.11.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "2139f979868b51ef140ab9d59efcb99e434bd4c5", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.1.tgz", "integrity": "sha512-uDS0H6yQbSWHVL3Oo1wgJJDkCBiWcSHGeRRu91X3g+cNQnEyl8kQ0uoqffipEsqX85ED3l7To3BaVMW4J0oJDw==", "signatures": [{"sig": "MEYCIQDykYE9IJ8//Hye0qbtPrwsuA8Xy8XVj/Rw4bMPjSox8AIhANBcB+TaBgUbrJJ7qFwqTZxq4rKdPPdYF4dxWLbOyfYr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "2139f979868b51ef140ab9d59efcb99e434bd4c5", "engines": {"node": ">= 0.12"}, "gitHead": "1c552818bd24da60cd8f3c74b0f9cbdd94889a25", "scripts": {"test": "babel-node test/all && standard {src,test}/**/*.js", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.11.2": {"name": "lowdb", "version": "0.11.2", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "247c0bc47d7042ccaf38dfb2d7e6db63f0dba014", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.2.tgz", "integrity": "sha512-YKJCRRCo79UiTi0uhFpf6UlvKZAe7rOlSpO3WASj4i/85BK2ugooiZYwogDDbogOCtZKvn6Q2NqAqMXHsYDJEA==", "signatures": [{"sig": "MEUCIQCNyRC3zL/q/hDKZ2aULwEaaJkM2yp4dGTz7w8ia5e7UAIgFYgR2saoyXcqDBgMuzb/9lyoHhiXczdXbiUy12FnJxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "247c0bc47d7042ccaf38dfb2d7e6db63f0dba014", "engines": {"node": ">= 0.12"}, "gitHead": "ff696c47b44f45c33b83678e3464a5b4479eebbd", "scripts": {"test": "babel-node test/all && standard", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.11.3": {"name": "lowdb", "version": "0.11.3", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "01cccfa59f635a9a81ab06a3e5e1409dd8e2671c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.3.tgz", "integrity": "sha512-c0DoIvHvG6MNhOBkUN7tLXk68Q1Uy1oDwjoliqqDewvjFR7ceEUuuJIBjz8BKHlUcT18XVjsbELTIXi0O0EmAw==", "signatures": [{"sig": "MEUCIF+kA6zoXtbRvXX/vkTvx46wYn2Zyrvx0kbdiAE36LrUAiEAmcdyykDmj/vsLSFZVsu7LpmOPgljL0gUOCXtnwfcoFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "01cccfa59f635a9a81ab06a3e5e1409dd8e2671c", "engines": {"node": ">= 0.12"}, "gitHead": "f14ae84514416d04d530c4e8aa997bb1ea679f93", "scripts": {"test": "babel-node test/all && standard", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.11.4": {"name": "lowdb", "version": "0.11.4", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.4", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "8554ab837f55c836fe3e3f2fea4e776f706ba2f4", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.4.tgz", "integrity": "sha512-97sVwbYybIBtVpspN3VwNjEpCYa3OdE9pezu9dSOGJAMPfmx+w4E4RyAgH5ursjX8lMmPE1EopLPkQ8B0Q4qcg==", "signatures": [{"sig": "MEYCIQDuyGx8WYv8rT2ye7IhcSbuCwD8IiLw5WHKzGiJ/fuQ/gIhAMEtsQwSQCv/+/18sc+yUaR+VDsh6QiCwKIEdlSVyDiG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "8554ab837f55c836fe3e3f2fea4e776f706ba2f4", "engines": {"node": ">= 0.12"}, "gitHead": "1f6528461de1eda6ce07996453513fa16735417d", "scripts": {"test": "babel-node test/all && standard", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.11.5": {"name": "lowdb", "version": "0.11.5", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.11.5", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "7bf3c750ef1b4828b39efc8724fbb134a280ad85", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.11.5.tgz", "integrity": "sha512-tIELTcAFvuXBmYLud2P+FdjlWGh1Dqo8DRZStt6HwSyRRK1QioCtgLr46fgsCNtGen+k5JTzjaKzJGtytHNV/w==", "signatures": [{"sig": "MEUCID8hgjd34kQMbEPbmZ1zja3WJwSjCmbxHDgXGAJrMAjoAiEAnSZ4qKC4zA3NM3oBTOF08W3tWbo0W7KZbIoH/4ciduk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "7bf3c750ef1b4828b39efc8724fbb134a280ad85", "engines": {"node": ">= 0.12"}, "gitHead": "078f74e10c930d9f579789360e8c65ad0847ca8c", "scripts": {"test": "babel-node test/all && standard", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "0.12.9", "dependencies": {"steno": "^0.4.1", "lodash": "^3.1.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.12.0": {"name": "lowdb", "version": "0.12.0", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "51e784020f4ac6f5e83a3c1434456bf885bff2e3", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.0.tgz", "integrity": "sha512-oxkpLFYdtuhu++yXem/wx8kyidzEeOBVjDKy0cqWUiVmDYRiXLVLAMYHbkBzTUxueLS6UACk1Fy2zZFP+LgxOA==", "signatures": [{"sig": "MEUCIQCf4FoEzuCtd0y/3q/UGfNB/hVFtvdibX38hqmTxlFjWgIgDjNp0rLIbo+gREp5qd3Sr9invzd8w1d1UDVy4iaO5WI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "51e784020f4ac6f5e83a3c1434456bf885bff2e3", "engines": {"node": ">= 0.12"}, "gitHead": "94db96fdc9f1bcef3c3f409e11b92e8ab2af5077", "scripts": {"test": "babel-node test/all | tap-spec", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.12.1": {"name": "lowdb", "version": "0.12.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "2c163888ad36e0efd418dff717f72e7d19cd33fa", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.1.tgz", "integrity": "sha512-O+DXN3V1+xBSllLl1GuxRAa4PPkCIDV9954zAoLqDVFjiNnBusfWVd5JBojb8JxUfrQnNm/c06fL2DIECEs+5A==", "signatures": [{"sig": "MEUCIQDsSWYkEc3xeMbi6NZdzi7Z0b3t/4nARGCXXXLEEY4cRQIgKTuMXgD+1njH+pBCzTOqeMcQJpUdQEnfAEJq/ZuVn9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "2c163888ad36e0efd418dff717f72e7d19cd33fa", "engines": {"node": ">= 0.12"}, "gitHead": "9e2042cef5072c90c56091de67393af6b6381511", "scripts": {"test": "babel-node test/all | tap-spec", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.9.0", "sinon": "^1.17.2", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.12.2": {"name": "lowdb", "version": "0.12.2", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "848c99217ee0e2f9618e2ac6227615cf1dccaddf", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.2.tgz", "integrity": "sha512-8tFCUyn/visZzUOc0xAVwcMvlFXiWTzxA9AH4gwzIiuKlpD7yoiQLgXSo7PRDTfujKQJ0u5FvpFJvBiFAcIUpw==", "signatures": [{"sig": "MEQCIGhg8kaYmlqBdpw9Ay2X6NDl6KPC1nHUMhWcSrlpjkv5AiAPXLuoJrFcCJvcQRrktOKzbYrqXnz5WkEfFYXK0e/RUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "848c99217ee0e2f9618e2ac6227615cf1dccaddf", "engines": {"node": ">= 0.12"}, "gitHead": "96e1e4fd333908011a473e4f6fee1dc68571ca11", "scripts": {"test": "babel-node test/all | tap-spec", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^3.0.8", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.10.2", "sinon": "^1.17.2", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "contributors": []}, "0.12.3": {"name": "lowdb", "version": "0.12.3", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "0e4e4d390ffa86a2486b8ffaf463ceb7f47b13f8", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.3.tgz", "integrity": "sha512-HcCQBU4H23iF5tO3UvrStCVBqJU9e6BjBZoBRSJm+09yS7PFLMEcNhlaXS78QSLKAdlgypfSgSxHYKJ58qYhOg==", "signatures": [{"sig": "MEUCIQCj6wpI1xzNesZ/eTfw4++evGJbVAiTP+mc3RY/DVXfFAIgdQlknXeQzEqUC8HSxvSSQcCj4I9uke8fuVb06Nqg5qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "0e4e4d390ffa86a2486b8ffaf463ceb7f47b13f8", "engines": {"node": ">= 0.12"}, "gitHead": "38e4755b1237fce1bfe07cd15a88d46b9789da0c", "scripts": {"test": "babel-node test/all | tap-spec", "build": "babel src --out-dir .", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.10.2", "sinon": "^1.17.2", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.12.3.tgz_1455399761974_0.49021967384032905", "host": "packages-5-east.internal.npmjs.com"}, "contributors": []}, "0.12.4": {"name": "lowdb", "version": "0.12.4", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.4", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "cb61b26fde5339771365835d862252e7c5440707", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.4.tgz", "integrity": "sha512-riP3qCBCD82WIQy43oijnFiZDI3A5rOvihykmzphgBzIBFP8ne7nyscOz32bSsNrbn+mG7kD+2bZqQ6ObnkAcw==", "signatures": [{"sig": "MEUCIH+7NWyBMMwIkUl32k9VbUm40yEq9pNve/jI57ae8LsUAiEA50M0tQjlyOfl17Z3tV/PZnVqQMZ97bql4k5PmrPgOLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "cb61b26fde5339771365835d862252e7c5440707", "engines": {"node": ">= 0.12"}, "gitHead": "f8ee2164b43fc49548912cb11dbf89656119cd6b", "scripts": {"min": "webpack -p src/dist.js dist/lowdb.min.js --output-library low", "umd": "webpack src/dist.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir . --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.0", "sinon": "^1.17.2", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.12.4.tgz_1455752315453_0.16219574306160212", "host": "packages-6-west.internal.npmjs.com"}, "contributors": []}, "0.12.5": {"name": "lowdb", "version": "0.12.5", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.12.5", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "de6cd8faa7ed641270098a829f0c1b1dd9f9a2ac", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.12.5.tgz", "integrity": "sha512-eRsSSDK9lPzuzOU3zyZiL+KBgWHtV87IY8szsAiaXKY4iFyLVSVKZXFzdxa9C377gB6rSoBv7687zXEs81+Y1w==", "signatures": [{"sig": "MEUCIBHY0YMsCE0P/PTvZsE+p23bCn9PW5Qlb01bxOKo4TkmAiEA58hI1EJFcwe3iqFh9nWHrYUaKFybLmXG76sexVRcQgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "de6cd8faa7ed641270098a829f0c1b1dd9f9a2ac", "engines": {"node": ">= 0.12"}, "gitHead": "9936c590084fa7ab07330d9b957083d5512e36bb", "scripts": {"min": "webpack -p src/dist.js dist/lowdb.min.js --output-library low", "umd": "webpack src/dist.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir . --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"steno": "^0.4.1", "lodash": "^4.0.0", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.0", "sinon": "^1.17.2", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.12.5.tgz_1456527581189_0.3513475195504725", "host": "packages-6-west.internal.npmjs.com"}, "contributors": []}, "0.13.0-beta.1": {"name": "lowdb", "version": "0.13.0-beta.1", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0-beta.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "dee2cde02c44d6593a11127f7ea2bf5b6f10e89b", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0-beta.1.tgz", "integrity": "sha512-HsCzrK63FCuQRzcw/5PmHV2t1xIyNBZTewQpQHLG1Th06LIgbEUE+0TP0rb6jZqk13i7kip62cImt77J+r7ztw==", "signatures": [{"sig": "MEYCIQC1rUSiBuEfTXPVw9j19txQX4cCJslFgPASMNI9IGp+gQIhANvG0He68A1Oit+E68DA+omADMd/kd5AU2WO7DV6CbiS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".", "_from": ".", "_shasum": "dee2cde02c44d6593a11127f7ea2bf5b6f10e89b", "browser": {"./lib/file-sync.js": "./lib/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "8892f53430afba6d0a391e717f91f11f5ca897b5", "scripts": {"min": "webpack -p src/dist.js dist/lowdb.min.js --output-library low", "umd": "webpack src/dist.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.0", "sinon": "^1.17.2", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0-beta.1.tgz_1462454325247_0.5998492017388344", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.13.0-beta.2": {"name": "lowdb", "version": "0.13.0-beta.2", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0-beta.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "6f78d1fad6e2fe71746d134bbb01bc1de93d280b", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0-beta.2.tgz", "integrity": "sha512-B1ksTpkafp50b+vn7FLb0KCcyod+d5KFPxTXxC/oKa7DwQHpF+dtY/laqhA3eU0XWQ6M+yTABHJvZgL9lfKIoQ==", "signatures": [{"sig": "MEUCIDz5oTExaejzN2EuV8QRDT8xpC+7xZJbgAiTy7vxIvzkAiEAnZ6B4RFC+9ggmmklaB3OJgwvmyX1EzsU6Of35Fg8QUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib", "_from": ".", "_shasum": "6f78d1fad6e2fe71746d134bbb01bc1de93d280b", "browser": {"./lib/file-sync.js": "./lib/browser.js", "./src/file-sync.js": "./src/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "72d4b4285bd7345d29eb1faaf3ac9b3f0faf5e44", "scripts": {"min": "webpack -p src/dist.js dist/lowdb.min.js --output-library low", "umd": "webpack src/dist.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.0", "sinon": "^1.17.2", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0-beta.2.tgz_1462723974508_0.19036288559436798", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.13.0-beta.3": {"name": "lowdb", "version": "0.13.0-beta.3", "keywords": ["flat", "file", "local", "database", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable", "extendable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0-beta.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "a65722df333b790410064b91778ecc6d8d666873", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0-beta.3.tgz", "integrity": "sha512-Jf4x8OayOqZG54yZ8A1QjOovWQDKw4+XC7STfiOtRNYDO75V+Gi0Y72Een/8mGjZ+QhJsh8zWnAlo8K2QJSMIA==", "signatures": [{"sig": "MEYCIQCJcDS/zmkzurprBrDZOa1ey28sP8E0mAWMctAtaC+xiAIhAIPiQQmi7Az5aJiTMvXgjy2EUP3uIZAkLPWsZm6VTLIx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib", "_from": ".", "_shasum": "a65722df333b790410064b91778ecc6d8d666873", "browser": {"./lib/file-sync.js": "./lib/browser.js", "./src/file-sync.js": "./src/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "3e084732f43033a044123392287d95a2a1a09e0f", "scripts": {"min": "webpack -p src/dist.js dist/lowdb.min.js --output-library low", "umd": "webpack src/dist.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "JSON database for Node and the browser powered by lodash", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.0", "sinon": "^1.17.2", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0-beta.3.tgz_1463231724667_0.09146564221009612", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.13.0-beta.4": {"name": "lowdb", "version": "0.13.0-beta.4", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0-beta.4", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "e3438d877362fcb6724163554c2573e7812d34d2", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0-beta.4.tgz", "integrity": "sha512-5fgCrWwWlds/fh93yZxFa1tZUudL5Hk+N35QV2CC+fUBhF5ZsxSKHsoqvNHwziugXEleeDYcp4a9f0CJ/b9X2A==", "signatures": [{"sig": "MEYCIQCLjwfVk4iybwhH6dFUvHKvISDWStvQp2AX+APShbWc9QIhAIkYZeYjTkoseEZlZOly7kYErQ0USHOY2nOY3E4nwMXl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib", "_from": ".", "_shasum": "e3438d877362fcb6724163554c2573e7812d34d2", "browser": {"lodash": false, "./lib/file-sync.js": "./lib/browser.js", "./src/file-sync.js": "./src/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "59f43fcfcab88daa6e77cba66b70538bbf383479", "scripts": {"min": "webpack -p src/index.js dist/lowdb.min.js --output-library low", "umd": "webpack src/index.js dist/lowdb.js --output-library low", "test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib --ignore dist.js", "build": "npm run babel && npm run umd && npm run min", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "5.7.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.4", "sinon": "^1.17.2", "lodash": "^4.12.0", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.10.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0-beta.4.tgz_1463484542004_0.15320559265092015", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.13.0-beta-5": {"name": "lowdb", "version": "0.13.0-beta-5", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0-beta-5", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "0a0df8523f576111129a300655f535fc6c7484bb", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0-beta-5.tgz", "integrity": "sha512-7lPJzhyNcUW+jjXYEb9Wd7iP90SK4YNWh8FhPrmRUwBkhdoofGOmTNLVKyzZEV53/6Bd5hn+Uqt/nYJ47qNluQ==", "signatures": [{"sig": "MEUCIC6AGjsdiyP/6xlOhe32hVpUNRqtSSPJe4gfBFmcGyrEAiEA6w6PLEKa3Ct56oxatarqj5EK1ICUgGFFXa8tHaZYesc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "0a0df8523f576111129a300655f535fc6c7484bb", "browser": {"./lib/index.node.js": "./lib/index.browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "2f106fe3be92887a7748c3ca6fbbccf271f18420", "scripts": {"test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib", "build": "npm run babel && npm run browserify && npm run uglify", "uglify": "uglifyjs dist/lowdb.js -o dist/lowdb.min.js", "precommit": "npm test", "browserify": "mkdir -p dist && browserify lib/index.browser.js -o dist/lowdb.js --standalone low", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.4", "sinon": "^1.17.2", "lodash": "^4.12.0", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.10.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0-beta-5.tgz_1464348293499_0.4329895644914359", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.13.0": {"name": "lowdb", "version": "0.13.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "96a39d87671526449448d592b0530c6d61424327", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.0.tgz", "integrity": "sha512-9h9PgOrv0RghZO8ESqyORyecIHohN+BoRvFuwNZSl5aNEEvLlUctdTOG2K72hOcRF21OPLJknbJ6u61WMm1mLQ==", "signatures": [{"sig": "MEUCIBKWlSUgbgeEAC97Pv+hDVFT2dR7nojl+yMfpL+Vg6RQAiEA0ldo1iy/bfASLdzZ/aqA29Jsiem48hGeHq5lUx7DIGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "96a39d87671526449448d592b0530c6d61424327", "browser": {"./lib/index.node.js": "./lib/index.browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "abc9560610cbc63764b49e9a52c2043f4307fa2d", "scripts": {"test": "babel-node test/all | tap-spec", "babel": "babel src --out-dir lib", "build": "npm run babel && npm run browserify && npm run uglify", "uglify": "uglifyjs dist/lowdb.js -o dist/lowdb.min.js", "precommit": "npm test", "browserify": "mkdir -p dist && browserify lib/index.browser.js -o dist/lowdb.js --standalone low", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.4", "sinon": "^1.17.2", "lodash": "^4.12.0", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.10.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.0.tgz_1465212159777_0.5862253818195313", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.13.1": {"name": "lowdb", "version": "0.13.1", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.13.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "ebb1057269721b40c316cedb1877e68d7f14ca0c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.13.1.tgz", "integrity": "sha512-UFdqQF3jrmFgSx7JEPwbEbHh6T/LNBiJGjqrMtG0XN2UtFjCb6eVFOI2GMJO5PApowakhZlMUjWXuR9vWQcnkw==", "signatures": [{"sig": "MEUCIQD+d8kwN6Fm7Ng99fZ2c0CGWFjkZ0iiNwrYyG3LxvotfAIgL2ml0PaVwYFU+aAQirpzbXPPyRm3qGV4yZ+VyOy1/TM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "ebb1057269721b40c316cedb1877e68d7f14ca0c", "browser": {"./lib/index.node.js": "./lib/index.browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "5e8278992b323815372834c061f27b9936535d39", "scripts": {"test": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "babel": "babel src --out-dir lib", "build": "npm run babel && npm run browserify && npm run uglify", "uglify": "uglifyjs dist/lowdb.js -o dist/lowdb.min.js", "precommit": "npm test", "browserify": "mkdir -p dist && browserify lib/index.browser.js -o dist/lowdb.js --standalone low", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"steno": "^0.4.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.4", "sinon": "^1.17.2", "lodash": "^4.12.0", "webpack": "^1.12.13", "standard": "^4.0.1", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-eslint": "^4.1.6", "babel-loader": "^6.2.2", "underscore-db": "^0.10.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "peerDependencies": {"lodash": "4"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.13.1.tgz_1465425782198_0.31838563922792673", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.14.0": {"name": "lowdb", "version": "0.14.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.14.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "cce4e6affe867c6cd4b196b382d3359b4d39efb5", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.14.0.tgz", "integrity": "sha512-ilVJrQ+019xOJuKjPzeH10aDNL4s8aTt4qQVAta9psn0W8Mu+LxzQv1S2u6lTOg/cQL+EHkVxD6nAdaacuCL/g==", "signatures": [{"sig": "MEUCIQD68fUzzjOveuYxfId9w732tKLZcVs+ft64jlt/JCo44AIgWWyXJ5WewOwhSngfInaR7PofacfD8ATy0ercsLryjIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "cce4e6affe867c6cd4b196b382d3359b4d39efb5", "browser": {"./lib/index.node.js": "./lib/index.browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "050c1966654ce32030840f6678dfa47bbb61f5f2", "scripts": {"test": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "babel": "babel src --out-dir lib", "build": "npm run babel && npm run browserify && npm run uglify", "uglify": "uglifyjs dist/lowdb.js -o dist/lowdb.min.js", "precommit": "npm test", "browserify": "mkdir -p dist && browserify lib/index.browser.js -o dist/lowdb.js --standalone low", "prepublish": "npm run build"}, "typings": "./lowdb.d.ts", "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "typescript": {"definition": "./lowdb.d.ts"}, "_npmVersion": "3.10.8", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.11.9", "sinon": "^1.17.2", "lodash": "^4.12.0", "webpack": "^1.12.13", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.14.0.tgz_1477427015548_0.9788182557094842", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.0": {"name": "lowdb", "version": "0.15.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "888d3ddd03b2ab96495c74972566efc724266b0c", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.0.tgz", "integrity": "sha512-4MHaMrh06eGYGI6GT9D8X3DXsvcEkpICocXdY+BGJDdDVdg0yScZeNl1Opv7l+SyV7XQwdhsv3MucySq6C90DQ==", "signatures": [{"sig": "MEYCIQDJxAlOSCcf7W2N9a3B+kDLEFVr+2r8/1l9toXly4ztQQIhAMw88B/3HRCZpM3JbNMOcSRYPd3i3JyHcqr5BFYoLM/Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "888d3ddd03b2ab96495c74972566efc724266b0c", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "2a3f37466e63e3dd1402f91593c238375101147f", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "rimraf dist && webpack && webpack -p", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "webpack": "^2.2.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.0.tgz_1486504181578_0.02161617996171117", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.1": {"name": "lowdb", "version": "0.15.1", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "dd47db6af4bf83df080852aaf9015f03a5897ee9", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.1.tgz", "integrity": "sha512-MyW6vjnNf6tGs/6KDPIK1ZBx9sfVvfWPFIwBrvbXMQ6Yk6sO5w1uu4aoAuKM6qhgnlumA6N2d3vBX6x5t2vPew==", "signatures": [{"sig": "MEYCIQCzBOV6igtHyKgb9RCG15rtDEf9AXgIbLFu+ByvhWEDlgIhAMhgdZrA4R1t30HXqGGlHjg9ptAlIGa8a5yTqpzGdlN6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.node.js", "_from": ".", "_shasum": "dd47db6af4bf83df080852aaf9015f03a5897ee9", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "66259406875610d5b92dc124bdfc03f6ba6230cc", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "rimraf dist && webpack && webpack -p", "precommit": "npm test", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "webpack": "^2.2.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.1.tgz_1486508616320_0.08757308777421713", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.2": {"name": "lowdb", "version": "0.15.2", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "55aeea589c7590ea2ddb299240b544f9bbfd7a03", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.2.tgz", "integrity": "sha512-NwzYeQp0Qxnc4/BKh4pIuvZuKsE9PFV+pIoIvPS1Qef/zhvZYwLIQmnADLpAJMWiAnVRJA9/6Kt2s7pTvh90zw==", "signatures": [{"sig": "MEUCIQDsy6vOGgS4PEYbvf5Eomw+ReRhSu5BWYhJR0YJG/1fyQIgZPeFJGgkZCxDRLx6gfRMbrevvHBDTyeUrDUcBewUtQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "55aeea589c7590ea2ddb299240b544f9bbfd7a03", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "70731a85a6f1aadd3697711ce1836a7fdc265064", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "webpack": "^2.2.1", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.2.tgz_1486595509663_0.22262496803887188", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.3": {"name": "lowdb", "version": "0.15.3", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "e382a1f199e829dd3028fb0aaa230e292d3ed673", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.3.tgz", "integrity": "sha512-toWTI0jocQuq6Qqm6WUzjsSbU9A9U5go+li+zZw8c48zRm1l1pPuHpCsRLRo50JxHyPr3Uy1F7b+AnBjgFxKJw==", "signatures": [{"sig": "MEYCIQDgFlD7QIMh785tqy0Cx5MseAVMgR7iliygSO5oQ3v3YAIhANeSSnY3EYP4kkkCJMsboAWGhdbmle4V3eTL9WgL5ESu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "e382a1f199e829dd3028fb0aaa230e292d3ed673", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "ac7bd0ff00ae980af6c1bffce89c3ed10c85c4c2", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.3.tgz_1486595681786_0.8801433467306197", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.4": {"name": "lowdb", "version": "0.15.4", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.4", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "659095d6ab704a9d27a0d0a7aaf02c28687c4ca4", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.4.tgz", "integrity": "sha512-uMiYae3IJFTO73VOfMcH13BDdVTKxP2+xwYd7XYqh8bT1i7x+i/cgTfTzvFyBYwKB9YtHh9mS5Sv66K2p00tYA==", "signatures": [{"sig": "MEUCIQDRvm4pH1Kdm+4/W8FAWiMtSkH9lF/KoSMCE6x+CfDp+wIgIlNeKAKWHuCI/MhCp40ckGXtqtVI4dn47636LunPIcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "659095d6ab704a9d27a0d0a7aaf02c28687c4ca4", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "2c78a4dc136bd8febe05086562b34315f277a66f", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.4.tgz_1486597151209_0.39006071723997593", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.15.5": {"name": "lowdb", "version": "0.15.5", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.15.5", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "9ade105df8aa573692d1221622b85414fbf4fa96", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.15.5.tgz", "integrity": "sha512-4PpZAmDRMLW6XykawKVxOD9n/HYy1dkuoI8pDlLnzfgYLkRCFDefVmeb4BV8IC+XYj2WSfwxblC9DWhnAl3u4g==", "signatures": [{"sig": "MEUCIQDBl7kPXcbmFOM/QSBT3P+yMAsYRdDktwJBsvqlHYLOcAIgT1pjMFrknjKlBAbssbGAnlSKGjd658FIdi+D7wwRtLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "9ade105df8aa573692d1221622b85414fbf4fa96", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "d5591100f08806027ad69ee4b6e40feed9eb1360", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "underscore-db": "^0.12.0", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.5.tgz_1487193253732_0.8331882986240089", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.16.0": {"name": "lowdb", "version": "0.16.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.16.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "0e3fb37332fa6189eca2d864e7ad9dc989359866", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.16.0.tgz", "integrity": "sha512-Jck2+HAaHnhC2Ae9QJ3yNS8NKJeslM2K/Wtnj0RgmdiO+4BADaMSWDrhIYeX+Ee7LU+tgjjekfWG1NBz72EH7w==", "signatures": [{"sig": "MEUCIEHzm71tQbKE8xjZbKgrMp1j/sk7MO5D+ieFPqkS3tAiAiEA/+Xj8ZoLbSY2fzKeotfpQbHbtJBhbzwvMQErdKon9KQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "0e3fb37332fa6189eca2d864e7ad9dc989359866", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "e72e967f463a393d63599568593d87cd0d84810e", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "lodash-id": "^0.13.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.16.0.tgz_1489059968765_0.3414036559406668", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.16.1": {"name": "lowdb", "version": "0.16.1", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.16.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "207b7b3134157d0160c6857e01e097620eb0832a", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.16.1.tgz", "integrity": "sha512-FvxFkHzN5kVp3ffIyWWiO9M+Ywctpao27g0S8xiZuurUJ6k4JkKSy4HuTzUusceuWGQ6VNuUZZwOrydXOYEcPQ==", "signatures": [{"sig": "MEUCIBsT2/1bhjxl3zNgNFWuL/X4YqLIXhOzLDL5rPU2K3djAiEAhFqahjkKCneHwEd5GNvcJvKpJwf3kupaYY2E9Q0wxmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "207b7b3134157d0160c6857e01e097620eb0832a", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "bcfda6d3b204f5b468c4a2ef13542ca1697ae92b", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3", "json-parse-helpfulerror": "^1.0.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "lodash-id": "^0.13.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.16.1.tgz_1491697534165_0.3278481843881309", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.16.2": {"name": "lowdb", "version": "0.16.2", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.16.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "a2a976eb66ec57797291970f3c87cdb61126fa3a", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.16.2.tgz", "integrity": "sha512-e8/j729JIQiFoIf/+hVo2rPPyOjPUm76hc1ieJRD9XrQFcMWoXEGanUQ+QisZh8roPou+qaL9PPCgN031MMz6Q==", "signatures": [{"sig": "MEUCIQCwaJFpMzT1L7QYJ22toKCkvDWZpJTiyCupsmZx5fqy1QIgPDl+jBkGndtpdhOVd7Ig3Re8m4ugL4yPAHnrePcJ+UY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "_from": ".", "_shasum": "a2a976eb66ec57797291970f3c87cdb61126fa3a", "browser": {"./lib/storages/file-sync.js": "./lib/storages/browser.js", "./src/storages/file-sync.js": "./src/storages/browser.js"}, "engines": {"node": ">= 0.12"}, "gitHead": "4a76af0f79d900043fd5ebf0ff4b378e8404bd89", "scripts": {"fix": "standard --fix", "tape": "tape -r babel-register -r babel-polyfill test/*.js | tap-spec", "test": "npm run tape && standard", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublish": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "standard": {"parser": "babel-es<PERSON>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3"}, "devDependencies": {"tape": "^4.2.2", "husky": "^0.13.0", "ramda": "^0.23.0", "sinon": "^1.17.2", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^2.2.1", "standard": "^8.5.0", "tap-spec": "^4.1.1", "tempfile": "^1.1.1", "babel-cli": "^6.2.0", "lodash-id": "^0.13.0", "babel-eslint": "^7.0.0", "babel-loader": "^6.2.2", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.1.18", "babel-preset-stage-3": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.16.2.tgz_1491762743528_0.6537299146875739", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "0.17.0": {"name": "lowdb", "version": "0.17.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.17.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "15cf04a6dac896a43daaae26ef13aa2aefca82d1", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.17.0.tgz", "integrity": "sha512-/7HC9hF/B4lfNemZT1UhnOoDRRynnndRmGBEXjeD0fvS7DhjevrD54akbOBPFWET5EhWw4AcJR0EBXSsKHz4gg==", "signatures": [{"sig": "MEQCIHxLZmDnOEdOkxzG/rYWINWOgWefYfEujC3WKWsWVTMAAiA/lQSQuGTQSRrsZYB9Es3XKNW9QHlpgnnoQMCRFPcCDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "engines": {"node": ">=4"}, "gitHead": "da611d97f709f0b8a6e163fb19b907ca33f9db60", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint . --ignore-path .gitignore", "test": "jest && npm run lint", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib && mv lib/adapters adapters", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "prepublishOnly": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"pify": "^3.0.0", "steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3"}, "devDependencies": {"mv": "^2.1.1", "jest": "^20.0.4", "delay": "^2.0.0", "husky": "^0.14.3", "ramda": "^0.24.1", "sinon": "^2.3.8", "eslint": "^3.19.0", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^3.3.0", "prettier": "^1.5.2", "tempfile": "^2.0.0", "babel-cli": "^6.2.0", "lodash-id": "^0.14.0", "babel-jest": "^20.0.3", "babel-eslint": "^7.0.0", "babel-loader": "^7.1.1", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-env": "^1.6.0", "eslint-plugin-node": "^5.1.0", "regenerator-runtime": "^0.11.0", "eslint-plugin-import": "^2.6.1", "eslint-plugin-promise": "^3.5.0", "eslint-config-prettier": "^2.3.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-prettier": "^2.1.2", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.17.0.tgz_1503252497870_0.6361934889573604", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.17.1": {"name": "lowdb", "version": "0.17.1", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.17.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "d57c2eab0cb36e41e0050ccda040c898d4c8ed68", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.17.1.tgz", "integrity": "sha512-o+MHSBz6yz/bu9T4RC7T9aNIDj1+bmwizLL+62sgKxYDelCIUfTG7INnqZ5pfetRKO2JPwvbxgBruz0I8+TaZg==", "signatures": [{"sig": "MEUCIQDrqYQK0c/m+P1lSq0+Ns7JkBPFSJKH4Kx6qusldUoeYQIgfn95HLQSvBL4YIXNQsLbrNzl4JcD83NDP1EkDor+/Oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "engines": {"node": ">=4"}, "gitHead": "3f0aeaeaa96adddf8f8fbb45a2c739f1131a71bf", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint . --ignore-path .gitignore", "test": "jest && npm run lint", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib && npm run mvAdapters", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "mvAdapters": "rimraf adapters && mv lib/adapters .", "prepublishOnly": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"pify": "^3.0.0", "steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3"}, "devDependencies": {"mv": "^2.1.1", "jest": "^20.0.4", "delay": "^2.0.0", "husky": "^0.14.3", "ramda": "^0.24.1", "sinon": "^2.3.8", "eslint": "^3.19.0", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^3.3.0", "prettier": "^1.5.2", "tempfile": "^2.0.0", "babel-cli": "^6.2.0", "lodash-id": "^0.14.0", "babel-jest": "^20.0.3", "babel-eslint": "^7.0.0", "babel-loader": "^7.1.1", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-env": "^1.6.0", "eslint-plugin-node": "^5.1.0", "regenerator-runtime": "^0.11.0", "eslint-plugin-import": "^2.6.1", "eslint-plugin-promise": "^3.5.0", "eslint-config-prettier": "^2.3.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-prettier": "^2.1.2", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.17.1.tgz_1503253318683_0.32279294170439243", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.17.2": {"name": "lowdb", "version": "0.17.2", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lo-dash", "lodash", "underscore", "localStorage", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@0.17.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "17633b7bd8c83ed5cedf3818f0d06f590b3a1c32", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-0.17.2.tgz", "integrity": "sha512-AY+OQ5ZW9rxKLwgewErkPEKxBSVknu4mnuah8lAR5sdzX9hO+SM2Tsgq66+jzWIOdXT/HB5VL2y2m6W3bAN8mA==", "signatures": [{"sig": "MEUCIQCGj8fQfQG4iCcOX0P4Ckv+s8SEUhCMuvHazgLrVDDuXQIgHUIMc1QOT+L+6ttMlHlufW4swoSiHXWQ9szQV7n9llk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "engines": {"node": ">=4"}, "gitHead": "cd6ec29b7b752c467fe827b1c2bd4ce3db9a8ea1", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint . --ignore-path .gitignore", "test": "jest && npm run lint", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib && npm run mvAdapters", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "mvAdapters": "rimraf adapters && mv lib/adapters .", "prepublishOnly": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "JSON database for Node and the browser powered by lodash API", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"pify": "^3.0.0", "steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3"}, "devDependencies": {"mv": "^2.1.1", "jest": "^20.0.4", "delay": "^2.0.0", "husky": "^0.14.3", "ramda": "^0.24.1", "sinon": "^2.3.8", "eslint": "^3.19.0", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^3.3.0", "prettier": "^1.5.2", "tempfile": "^2.0.0", "babel-cli": "^6.2.0", "lodash-id": "^0.14.0", "babel-jest": "^20.0.3", "babel-eslint": "^7.0.0", "babel-loader": "^7.1.1", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-env": "^1.6.0", "eslint-plugin-node": "^5.1.0", "regenerator-runtime": "^0.11.0", "eslint-plugin-import": "^2.6.1", "eslint-plugin-promise": "^3.5.0", "eslint-config-prettier": "^2.3.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-prettier": "^2.1.2", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.17.2.tgz_1503486371657_0.9203615502920002", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.0": {"name": "lowdb", "version": "1.0.0", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lodash", "localStorage", "electron", "embed", "embeddable"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@1.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "5243be6b22786ccce30e50c9a33eac36b20c8064", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-1.0.0.tgz", "integrity": "sha512-2+x8esE/Wb9SQ1F9IHaYWfsC9FIecLOPrK4g17FGEayjUWH172H6nwicRovGvSE2CPZouc2MCIqCI7h9d+GftQ==", "signatures": [{"sig": "MEYCIQCEOdaYtGyxHqwjowK8Su5x1HhCDqsmZaBBT+F0ZxphuQIhAMirnbFeG6YMF1DIOjJpdyR92J8RreSIx9FRSEJp4EY9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main.js", "engines": {"node": ">=4"}, "gitHead": "7623a58753f63c84ef9c2c3513444bc86a01204d", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint . --ignore-path .gitignore", "test": "jest && npm run lint", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib && npm run mvAdapters", "precommit": "npm test", "build:dist": "rimraf dist && webpack && webpack -p", "mvAdapters": "rimraf adapters && mv lib/adapters .", "prepublishOnly": "npm run build && pkg-ok"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Small JSON database for Node, Electron and the browser. Powered by Lodash.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"pify": "^3.0.0", "steno": "^0.4.1", "lodash": "4", "is-promise": "^2.1.0", "graceful-fs": "^4.1.3"}, "devDependencies": {"mv": "^2.1.1", "jest": "^20.0.4", "delay": "^2.0.0", "husky": "^0.14.3", "ramda": "^0.24.1", "sinon": "^3.2.1", "eslint": "^4.5.0", "pkg-ok": "^1.0.1", "rimraf": "^2.5.4", "webpack": "^3.3.0", "prettier": "^1.5.2", "tempfile": "^2.0.0", "babel-cli": "^6.2.0", "lodash-id": "^0.14.0", "babel-jest": "^20.0.3", "babel-eslint": "^7.0.0", "babel-loader": "^7.1.1", "babel-polyfill": "^6.9.1", "babel-register": "^6.9.0", "babel-preset-env": "^1.6.0", "eslint-plugin-node": "^5.1.0", "regenerator-runtime": "^0.11.0", "eslint-plugin-import": "^2.6.1", "eslint-plugin-promise": "^3.5.0", "eslint-config-prettier": "^2.3.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-prettier": "^2.1.2", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb-1.0.0.tgz_1504211136668_0.8861918419133872", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "lowdb", "version": "2.0.0", "keywords": ["database", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "Parity-7.0.0 AND MIT WITH Patron-1.0.0", "_id": "lowdb@2.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "f9ee1149e0d9fac7431aaceb7449f737c62b2978", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-2.0.0.tgz", "fileCount": 33, "integrity": "sha512-ISqesg4kY5uGhpa0sMT+9o6fdPupzxGFYk0TVklCobkxj1E9z3JdVc9ZXobbRjgh74AzimgQ2tBkJNnBsAISVA==", "signatures": [{"sig": "MEUCIQDG3vT+61LzCGyM+RsusEI9hDXsHWCGTdCbbwnAOEqlVwIgSbxQ/BydWpdUFECephUtbImHSXOiMs294z/Pc5DbWhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpgVQCRA9TVsSAnZWagAASIQQAJdx38v5JTIbkJUL411w\nuM9zUv91RYQBWEyhGL1Zzjh8lADqeWUG6FN9ohcDuR+lypeM7YtHGVAwSS4W\nYe6oVnH4dLmmUbtybYGdO3/o9LkcVJeVL75EZKk2HwSA2WJ3zspWRSNepEbz\nDsesO6SbchmRxJrMG8aI1/kEgK3rgHBQZRoN7PlcTejpocQs3JbYyHRLOv1J\nhPzg4/a+FuEBPtW48Pl5fR7eSpQOUiku2bJYxjWH1TpckOoL6UkOpL4fU6S7\n5D3XlWvXw8JNkxJncWTG1c30cn9KdmW3zuNbWKjaHkYFwSGKDBFk6ah6fFOh\nEHue01xD2lifUjcPDVdEbtoahrzo2tmJT9XpXFfFIAQcuL0bhwSjEK9I5ddL\nH/i9wd4wrfr2bB2FN4UmLbJOWhJCen8slVnkCTxyQJdxA3MIfJmYSeet2dcL\n+kg7d+sQwl+O9/uISIPZvDm8UFwgEhJQdLmFeBrGHzZhnVGVI+ppEsd3Qf+b\nfN+UgHlAWVzgfj7Nh8LhNuNk6dUdZizGY/Lm5B4Jz08npvaWtxt1V1TxV5v9\nNVulokP68gyFEPPsGnWr+4kI1/Vo8Y/U07J69XeFxOztkEoaNun1jlmLiv6j\nD74W0sRW3cmkng+OYajqF4Jfx/A0V1TIONBmxLz+IrBmHgadQtg9H80s2JI4\n5Nkd\r\n=j9Iy\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "gitHead": "fa6ad3c3bfdf0c606319e3bcda7b218fa4868b58", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "jest --config .jestrc.json", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.12.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {"example": "examples"}, "_nodeVersion": "16.1.0", "dependencies": {"steno": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "husky": "^6.0.0", "tempy": "^1.0.1", "lodash": "^4.17.21", "del-cli": "^3.0.1", "ts-jest": "^26.5.3", "typescript": "^4.2.3", "@types/jest": "^26.0.20", "@types/node": "^15.0.3", "@types/lodash": "^4.14.168", "@commitlint/cli": "^12.0.1", "@tsconfig/node12": "^1.0.7", "@commitlint/prompt-cli": "^12.0.1", "jest-localstorage-mock": "^2.4.8", "@typicode/eslint-config": "^0.1.0", "@commitlint/config-conventional": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_2.0.0_1621493072407_0.8247782376214798", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.1": {"name": "lowdb", "version": "2.0.1", "keywords": ["database", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "Parity-7.0.0 AND MIT WITH Patron-1.0.0", "_id": "lowdb@2.0.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "9f362c6d0af0ea6c01b36e7cd3ff365a018b06f1", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-2.0.1.tgz", "fileCount": 24, "integrity": "sha512-wTIM040Q5nzDaF2a/ZlSd37NJEA+HeKChaLdWp3v4SkqcDiau2u3lmvxtQFn1IcdJTcnjDXQ00BMm8LDTmO6Ug==", "signatures": [{"sig": "MEQCIDCxvcOwwnYPV30RU2msHUPMEJgtLO+Xrh5dSQ6rgw4MAiAnshQifS2yMPvXTwNiqwy7q8MnxAy+HU/oymmx6BYlqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpgZMCRA9TVsSAnZWagAApUEP/0INvMEIDmDbPfxJuIRT\nSAeftfol5Z8NLkJAnt6BfX33mqUUT7Vuzu+6tiQ+kEGZrUXy85kfhABE8eAg\nT+HrtVYEg1kedyvxuni6wdDjKUkqhtu6FZyKGKzfgk0hO76vBuezq+gdMEEg\nWHGAB4Byyilx1ncAN96ulWUBvR1ozf4MNDRe3kGCHU9IMrgbyHxJp8pEiVH0\njRBEjLNlIb7uyAytZdmtv/Wlo491dkloW9MM0UoStPndS/EPndEMwTfCtdLu\nZm3h4cohYXLvD2+q5alxzKcir1aAHr1O/wzw4LqONVCATm3PznldJ1tCos6V\nrg3ZlnMVHmjasC9T0s2znNTSPnfGWlfqzoHlZlihwRecboAgceD/H+cEBb9K\nr+Kon1eU+oD8gkt8PLiEcst3umrUSGIwBAFdKIEixLtx8Mdtp+2MdhV12AZ5\nm6tj7wkIMvSp2vprYNCw9BV2NKaxw145h3Emyd3Isx/d5Ndom52fB2VzUV2p\n+HnQzn7eGy+IstlOTM8h5M3bb8kYzp0IoS8roiXVXCiZ2mHiEpmPgN2Qzk+A\nF0WX+kSXU5OuYrYG3HpAfaAOY/i121SaSc5kTEG5ECp7hZDJIE5Pv/OvQ4qU\nNb1YTRqgx8dsVubpiTd90REuKrt+CEeLuEGGNaWjPw0ElTgsQg+/AsGFAtuc\nQYDo\r\n=1ajD\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "gitHead": "75866b8abe7f06ebb733c52378753f6706a96101", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "jest --config .jestrc.json", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.12.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"steno": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "husky": "^6.0.0", "tempy": "^1.0.1", "lodash": "^4.17.21", "del-cli": "^3.0.1", "ts-jest": "^26.5.3", "typescript": "^4.2.3", "@types/jest": "^26.0.20", "@types/node": "^15.0.3", "@types/lodash": "^4.14.168", "@commitlint/cli": "^12.0.1", "@tsconfig/node12": "^1.0.7", "@commitlint/prompt-cli": "^12.0.1", "jest-localstorage-mock": "^2.4.8", "@typicode/eslint-config": "^0.1.0", "@commitlint/config-conventional": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_2.0.1_1621493323992_0.272549281574497", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.2": {"name": "lowdb", "version": "2.0.2", "keywords": ["database", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "Parity-7.0.0 AND MIT WITH Patron-1.0.0", "_id": "lowdb@2.0.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"], "nonSemVerExperiments": {"configurableModuleFormat": true}}, "dist": {"shasum": "509381fc380ef88fdef512a5558ffcc091eb17a5", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-2.0.2.tgz", "fileCount": 24, "integrity": "sha512-hnJ3pQYgaacVX4ku5Wu/L0CTq38ng1tkQdiU0wMT56E/Z9SWgBp/PJu40DJUw7SLp3GDE2BigkHBzNC49ymEZA==", "signatures": [{"sig": "MEUCIQCWORikwBCmmWHKzHUvP7jwJSLaTkfW5/Wvq+M40q6beQIgBUyDUQr3q3L4NOUbd61QdeHj8WVsP39tCyNRrDN3l3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpxCZCRA9TVsSAnZWagAACgQP/0JLcJKS6QGJkA51vIKo\nus1uXvcsK+WWrxMAUVhoHCSaDYarJznGilqh3htXyZsF6aILF4CbbMLop6TH\nkR9OA6CymORzt/TxKEf5NAHp6eRb3M7g9EcuU3XCTLnKVZ44gT6OlVTLxKw3\nWRzbCEeWyCCj6fFKa0XSTbIlJm9DqfH6t37YcG4eNlAD5UCkrrEWNSqwDiXv\nexnRD6XyAyF9pcgGmbKHYIOGdN38VkDwbsdYtvSaJlS4t2snpIteneY+duaA\nZtynvbW0TB5e34WsdT6WxvtOSliMTrY9nCAh530UpYwMSZJQ/t3QQPE/g9CB\ndlnoM6QSAvWYb6n6Avw4zXze2do1G6luo0MB7ROITtEu6hlCKQmBABKBfQWJ\n6cKadzqtHufVTxYRix4uPoucG1SEB1X/j57ZQ/0B8mt2Lkbr8E99sYWF0+ac\nAEfOR0/C9zDuA/JFZHYzhZ/qGvt2/q7dkvkEMXdQq5QFlznQdXfPZsrYvx+1\n2a6pSwq6yyr19uMROeCpQYmIgyjKVo6ha0RAfbTgQps2WH2mtwNrdaJPBTDu\n5H+dhXWBsVsUOsEfyVtGa23E3QIi6IE2hvhHC2KxuzqIsD7KhwdCbxJ94R5b\nMP7Jx7VRuf9fFkZpdiAILTNFIMZBijA37l0kVr76FHJBsnXAaUqUobH0recF\nO/kb\r\n=Esdi\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "gitHead": "673947f1289398c068029d36de689600f2a8a107", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "ava", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"steno": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "husky": "^6.0.0", "tempy": "^1.0.1", "lodash": "^4.17.21", "del-cli": "^3.0.1", "ts-node": "^9.1.1", "typescript": "^4.2.3", "@types/node": "^15.0.3", "@types/lodash": "^4.14.168", "@commitlint/cli": "^12.0.1", "@tsconfig/node12": "^1.0.7", "@commitlint/prompt-cli": "^12.0.1", "@typicode/eslint-config": "^0.1.0", "@commitlint/config-conventional": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_2.0.2_1621561497119_0.05701030238981564", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.3": {"name": "lowdb", "version": "2.0.3", "keywords": ["database", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "Parity-7.0.0 AND MIT WITH Patron-1.0.0", "_id": "lowdb@2.0.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"], "nonSemVerExperiments": {"configurableModuleFormat": true}}, "dist": {"shasum": "5b823830a4c1757271200280474396f94fe4b10b", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-2.0.3.tgz", "fileCount": 24, "integrity": "sha512-XEBcYcArKhJAlRDnZbQX6wsTklfP645nZHppRehdqhFM7FYqnTAknfOulhYdgJuWQpMgfeZhtZhNXWdTEU8dEA==", "signatures": [{"sig": "MEUCIFj+IhQ+WdSwhueeRCnhqsOC7l1FhixRI2zgpM84Ajf0AiEAoLyhS2s5HO7rpBHilNyVyfBazYIFbLuuU6t9ChNUkVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgp7YkCRA9TVsSAnZWagAAcTcQAJE2aknWppuzC2zTGfZ/\nZxrEyMEk/T6yfj2EfDGn3wllDVsG9R+8ygF/a/b0M9EWWdjHwJx4SxvgS+6l\nG+0dJ/W4FD4w5FCjhOFIDbNYAOAYrzyoxHwwY8iTeLgaKFJYOHOU8asxPiER\ns31NpLBT/ZqB4+F+kyIM5fYVosWvVfCNjRW2vRazRzx4M2nR5DWsVKgGGNGC\nCHCYY6COb5ugzvMuIMajwR9nfSjb5Zz+q4++31QMcqde4Mliy/WdPSQcT1cX\nlAEx8YN4xcLeRM8ueyyWCw40Zhxw6XXdrviZWRBHgASpvJ18UeEmE3iFRpSa\nrRD/3Cz2iVMOFgf1pvOQM3UVWm7jzjzcED97NVIIhZgvvAGRj6WGci6lYsKm\n+2g0iCXXqHZdyCpzFCD9NJgb16ghJh44Rw1pkemK+mENGFZzR1DhMyxCizR0\n9R5GON+758jj9mIp7CncCoDl61+71DPI6qyCE2b04zNQEI1+uJDiNMHJADR5\n22fSQtZE6HqcW9eenG78wm8bIRQZo6BYTL5wR72xqRwT6M4VbXWyqX+X02a/\nfIlLMbeHsDTaHskQm7Mhr/xJxt8An68lVPua9RuCOS64Sccy0jqYRjkNqWbA\n0TCsPnhbl13XorWnNOf3Fs/bb7xT/z0WppZOyzFU7fKKLbEe3HN+I9AI3qfK\nvw4F\r\n=TDew\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "gitHead": "d75cc47ef1c815b4da5a89620b161388afafc4c4", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "ava", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"steno": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "husky": "^6.0.0", "tempy": "^1.0.1", "lodash": "^4.17.21", "del-cli": "^3.0.1", "ts-node": "^9.1.1", "typescript": "^4.2.3", "@types/node": "^15.0.3", "@types/lodash": "^4.14.168", "@commitlint/cli": "^12.0.1", "@tsconfig/node12": "^1.0.7", "@commitlint/prompt-cli": "^12.0.1", "@typicode/eslint-config": "^0.1.0", "@commitlint/config-conventional": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_2.0.3_1621603875622_0.9831478607322484", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "lowdb", "version": "2.1.0", "keywords": ["database", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "Parity-7.0.0 AND MIT WITH Patron-1.0.0", "_id": "lowdb@2.1.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"], "nonSemVerExperiments": {"configurableModuleFormat": true}}, "dist": {"shasum": "c8063e228b5ab3e082ece90e0512537ecb6e1e2a", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-2.1.0.tgz", "fileCount": 28, "integrity": "sha512-F4Go8/V37gAidTR3c5poyjprOpZSDNSLJVOmI0ny4D4q9rC37OkBhlzX0bqj7LZlT3UIj4FchmZrrSw7qY+eGQ==", "signatures": [{"sig": "MEQCIDWkFT6okNq+2BX5Om+qCaCL0Kc116pbJUnzqI8Tp43aAiA7fuTiuWjZLpRjYfHD0yFRzOix88CHrnp2kQNUsQqyVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23924, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgq4fDCRA9TVsSAnZWagAA2fQQAKOL/P9VXNA2Mp7b134Z\n/+GAzsX/A7roFYsV6TY0KjvqFmVczkWYZ8mWXqkJN+FnNnWgLDEb/gke89X3\nuVFEp87YW5BS5+V+GWqUJiO7aqTPjPxxYOk6NK/tBGQhlT/uY1XpJ5XOPF7a\nlfeZEYKJVlNer8RSKkiU0oYJ/M2W8IwYkzkA8l8R5ML9kNZ9XGTXpQluMQ8+\nmM3fHjV8zQk95ZSDfrrdk0JVstfxHaTx0dzzau2/d6Qkx2T7GYsW3nBAX9G+\nzPpGwf8GcC1ANTHuWTpUVfsLKUxicuoL1qiJijGoGoayMa/VM1bzodKKX5AI\nQbvaJd4FCapgJWUomKlrj7KmS0uOFPa0INO4Rk/pSobk/LnrsFlXJWm2ihrJ\nBFeMYZnixElzYlj3Tv3Ewfv0cZvsyI/VvEl3So6aYeG+El7AW/RQUotIQDAR\neo+1DjINqcWhPreqcBQOMWFioeReBYLS1fDGAORfBVqiZPhoN5vnPjno59Y/\nTLHHCdCXpxQ0bWywMZTQwCO2Y/u+Rf5IISCx6/cTrytkhSEAbqu+hNROdLPc\nmydCIOZYNqcsPMfSnMI9Dlp6L7y3+qbGaHEnuuqIJAsq7e53IgdImfCzArLt\ns/uNur6MLY0sxs+HVNHrOOBA82C4d15tOn4UoVPp2eexNflUEm0rH+06a4sW\nQxXa\r\n=hbrJ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "gitHead": "5249df8c01592b489191c769b9bdc35d30b49917", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "ava", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"steno": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "husky": "^6.0.0", "tempy": "^1.0.1", "lodash": "^4.17.21", "del-cli": "^3.0.1", "ts-node": "^9.1.1", "typescript": "^4.2.3", "@types/node": "^15.0.3", "@types/lodash": "^4.14.168", "@commitlint/cli": "^12.0.1", "@tsconfig/node12": "^1.0.7", "@commitlint/prompt-cli": "^12.0.1", "@typicode/eslint-config": "^0.1.0", "@commitlint/config-conventional": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_2.1.0_1621854147155_0.00985212700876481", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "lowdb", "version": "3.0.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@3.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "c10ab4e7eb86f1cbe255e35e60ffb0c6f42049e0", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-3.0.0.tgz", "fileCount": 25, "integrity": "sha512-9KZRulmIcU8fZuWiaM0d5e2/nPnrFyXkeXVpqT+MJS+vgbgOf1EbtvgQmba8HwUFgDl1oeZR6XqEJnkJmQdKmg==", "signatures": [{"sig": "MEUCIQCW3xd+/37dTw+hA5V8HJ38LUGzkg9f82T1qWn/TdoouQIgZ4ETjvTUsyu1J7Wt8201Y/np1Ahi3wMwek1jzvRi7Ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP2AuCRA9TVsSAnZWagAA5dUP/1a7uLwXQffEOG+9qukw\nurm087zD/gnUmXdDnVO65QLzHaWMEz8xcHy1fE7oYLpA41nOnQWI75/fZMsb\nYQN4CBEICn7SIxgMEZJelp3E3cKHsPhSaVRQ3HwcrjWzvtxPyHy1cFRzdRuE\n3Ti2vvGyTZ7yhQn9ZWbqadFhBsoCw5v+ZR0sFLFPntVtufV8CfikvmgiRCGy\nnUz0OsxeWce4XJjHCg+WfNrQKe1HQT5ILaQGB//F/qz+1/rpuek29iFnAaSE\n+Ex9zyrrbAuIgER3Rh85ih8g0fDxnkqlmSafR/VrNAQ1qcIYfbpZ/LXpR59M\nsONZ7iXTI9PnMAs5JFZNgtpGYU2wp+uH/GOYj+Yn9awe2pjdm9YTe+rJRGz0\nlgtSCUUpAbtJSn2RuYwySlPvoPrMYCuGcaCq9+SDjg+56IvYIMJo3AKUEiDN\nnpj4OeqT6RCkg2fLbzrSYLnjqTYX9vMP/8p02LyF8yLdybb2wG++qBF8/Qdo\nGZ2K5EZyUA3a3O/RY5Xf+9f7kjXzxO9/3fSosB6wjgbynbvvjRHxGWTz1onS\nAISUV6TJ5vsAN4rUXgqfC5t5fLJT0nD+ei1pDnHdKGNn3DiQAtZtXzGDbcny\nQbP9VcRv6cWolV5iFkgzQdC+AkpnYE8EtGIFenjkY4pGy6dLr/VxH2sjTNKm\njP8T\r\n=lTRW\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./lib/index.js", "funding": "https://github.com/sponsors/typicode", "gitHead": "1954f48be1a33db85fa9bd29a4220e6f709416fd", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "7.20.5", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"steno": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.0.2", "husky": "^7.0.2", "tempy": "^2.0.0", "lodash": "^4.17.21", "del-cli": "^4.0.1", "typescript": "^4.4.3", "@types/node": "^16.9.1", "@types/lodash": "^4.14.172", "@commitlint/cli": "^13.1.0", "@commitlint/prompt-cli": "^13.1.0", "@sindresorhus/tsconfig": "^2.0.0", "@typicode/eslint-config": "^0.1.2", "@commitlint/config-conventional": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_3.0.0_1631543342781_0.40083199738247033", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "lowdb", "version": "4.0.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@4.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "177bc8ce5b0110248cc61a402faf9a51ce62bc11", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-4.0.0.tgz", "fileCount": 25, "integrity": "sha512-wH4WPH2A+doyzd9mluhMQQsdrHNfOXJE5+C5N03QvH+8EoEMB1WWnjkfn1MkPtVdDrONRTojuNAWi6Es3rVtmA==", "signatures": [{"sig": "MEQCICteFzd0HMA2bmYzkXpy1gEWvXmgkTTEyMV8v60IfhytAiB4mGQcK77ZOua4JWrALZtofAjhLimRquJvnyld4qqfZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTeZ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH/A//RBXtJA4KAS/+UAQaN3U3OS6Apwu7NSjIi1dmK1RpicXB5Amb\r\nZunVGPg32uyNapLX9WRUlgyJ7/yzjuEnhMdoJiNF9aDVJgUW3lRwtGMBcEwN\r\n/1fxr0plk/nO41/VNe8/1whgdhLOrE1KZTkG4PjC/1pYIwyTLQx9p6MTdNqT\r\noKMNOBfcbrdjwAiJF2lu+2wbYgbvgCMsaZWfqfdqtb95cHuvlFgeMjlqNp4H\r\n6CYqjVV7K4lVFHbe9ogya5sAffyMSuldVLtSRmgxjytaZoMKSaGol8Z30c3R\r\nHsLp0eKGvoPq6IVKNP12jyMbtRh80LzzNab0ROVLwvTxErsNu2b9ff2ip2oo\r\nHTaV/hL4FmmhtRh5MpvC5vD+sEI9O3CzTEqUHOhgt5eDRhj+BnSHEs6x+nUP\r\nuCf9YsWblk6HOiFvWDvYTCQ06s5NlDrcIDsj6VuafNG6c++3fOmTHnNQsPT1\r\nk5sEElZMbKh9cMin8XwFhG7iXzSj9Mlytx1BZ0lfnHlf6EniluP2mm9q1D+s\r\n2unkbLujJRhpDq/GEHAwOtSshrxxPObHU99w5aqQmQnr/GcHawIJg7Q50PhF\r\nznYWBypxRymAMw8rwWCKKyJcD7l7IEkKE7uek1oGFB+DpTVUiZU1we7he5rI\r\n/zqe0WU14INc7E9cAjbr3w8+C8gY+LsDrY4=\r\n=oHCV\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": "./lib/index.js", "funding": "https://github.com/sponsors/typicode", "gitHead": "82bae2651dc1a089a802b57b2b3d503b43255831", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.25.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.0", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_4.0.0_1666049659361_0.7344658774055237", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "lowdb", "version": "4.0.1", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@4.0.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "9a7a12cfe00e2f1bf937ab385b2b8b6f5db54162", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-4.0.1.tgz", "fileCount": 25, "integrity": "sha512-tbOSxMEjBzKH3d8Ma3uJH6EfA/mITpyjN+8dikG292prSZCIV0SeacieMpQuK43ewANNSHOFofgND834aFsMkA==", "signatures": [{"sig": "MEUCIAfXehB+Mfv6VVoKqTFxb7T8tyHbi0g77XLraXg88WKoAiEA11H0Gw9oqi/N2FvKutaQ4GFJPr50uMDnp3oaSvzhnvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUzJeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhYA//QOZpMXaPP+0IhTMAmi2R+mbrpWNFJ6mywW+HhOB2xeowtgAY\r\nIDLslE4Iv+zslemGsTcQ9rheU59Gc4qGTHfxb3gkffCl6sk460TjsgvBWj7o\r\nTLH0QTSnohRoJ74k9jbSLMx824HHEYngax8DaJOvzDG5IMyoNKGLmP6vzi1t\r\nAHhxxgsz1RIiGu5ZqZw1RAt1wsfd87QwoKSLnYuAn69JElxF21pKC5KSWd8/\r\nE5aOS+y76S4+4SirZPj8HfYn20r8VabUh+aGK2gJTVoD28yRxrudU6CLNQiu\r\nWsRXdUDQ/FXMS9nwhhUY4lej9TJ1oZOtDI2NKLkB6kgj/luB0+DMs4hefK0K\r\nRj4G60S9svCB+e+eqvfMs0O9gAbTeo+zyxBfxV+yD3Ni3BWHN7TdSW7sERxl\r\nz73p/joAjxnohzxqW9R4SskFbAm6U3fqvuBBF0WgLkLOAnxA3xgaWQHNq/q9\r\nwleGVyyM8dSeExCXR7sAKYQODI/2y13fPe+PKDXnwvjJwwxLS46SvhMsQCUw\r\nIbcYo6QTqQuZIOZfMZXFBDYW3v8kOJN+7xyoQ0jBAC9jh7Qsg5UOOOYE9U2s\r\na3M3RGb4ZAN6t4PL6Wbi2nUEHetRLrnaMkzzv6LE3PsVxNJWFh5x6f3YV7RY\r\nGJvsTDb/Ou1drFWzCkUyr7OZ4/037lKHPjI=\r\n=xx3I\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": "./lib/index.js", "funding": "https://github.com/sponsors/typicode", "gitHead": "320365c08d19c9d61df9a5c6e656d77d9d4928fc", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push && git push --tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_4.0.1_1666396766429_0.8292731634049737", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "lowdb", "version": "5.0.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "7e8bc0e5008c46af5eb5175f21f0f676ab43749e", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.0.0.tgz", "fileCount": 27, "integrity": "sha512-pwsvB9L7nrZ5DOt2joIBFAlErirnhCCbICmFQaQ5T8+Xu/wuuF9xHvVePlyu/r2M9by+lw4LS3pvFkxBI9g/kA==", "signatures": [{"sig": "MEUCICjSrRP03eyaCGFM6sP6Vy8huRkMW0kP3CjQD3dkzXUJAiEAipxPDcnonO56hv6m4yhqpBstsTqZWVs2jXU4u1uxkVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUzkFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolWA//Qmwe0PAQ+GZoV6v/Zu9KPujC8UfZIHEd/6vpneAlxnUWrg9N\r\nTi3ZNeCPRoZ3GfYJ5nr0e4+5A61ZuZ2GuYNwhJIZwaWbeYahcV2tRlX2dzBN\r\ndee4bmn7D0PYvZIYZDdv3qFbul7dFnmImG9Z0yY35kfS1bZpfIbjvXvIlxcK\r\nDf8iQnZ2VO2zc0A5MHu8kRRWZGlpmDY2XS4vxns4mC2J8QQ5qbRR08UIvb76\r\nfgPPOR/j2ecLrgR0fFFjo4d7h9X53GjzMoIiDX/wDC6te0JLcL2DAhgY/t0q\r\n1NecrFlL3T6irMq6wLX4A4jxUKrHE8nDw81Y+Pn3agcWHXuocEWlvUrEWL4A\r\nBpNYSCRoFW+zCfbtWK6ttNR68GWLwhZQJNge35AeFPhJKSEnNm6kFz20lAba\r\n3yrk94HS6ZAyPEYy21OQKMDM4QIIfmz3REvXxQbGWUnbfje0l51zX1rAR6Bs\r\nDYrmcf9GsoEc6JyafOzyD6Pa9wDxGK9Yb80zxQbAdkPUJwI0esrKKAlk01yb\r\ntTsBbWueGNDPLFsjpbEzanaIfHkZsIq+sFKKYVIMpDD4x4kMS3a52JstU/TH\r\n7ph3yYHYxv+a8LyGq4+omWejZ/EugY/tsryUma9EVAQBdjrAqk+v33myexED\r\nzBBuvtm+9JSFyK3HcRZErUT5cxWm9EWr5jU=\r\n=9FYw\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node.js": "./lib/node.js", "./browser.js": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "49203a4a3f78bf41f4bbc22a46ba93385c321eb9", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.0.0_1666398469728_0.2987898150810544", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.1": {"name": "lowdb", "version": "5.0.1", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.0.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "82c24e921c848ef5d320eafd0ee7a2a99ca409a7", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.0.1.tgz", "fileCount": 27, "integrity": "sha512-gETj9h79qisSca28tMMYOGqjab29oquo9E1KEbcGpmlL9vQUrpQqKEy91Y9gkxfQ7LlHLDQlnTsH29t5A0cmrw==", "signatures": [{"sig": "MEQCIGSfkoFKtXtk59SqtYtC5Hb6bPmcJtIWzltz6GHvJnbhAiBwmLU5rtz4p8p9M96DppauUjZZ5ocE7bAN9rOevCwVBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUzqWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokFQ//dyy9ImOd0AJFz7q0uy3kUO5J9cs8ZIYfTRAELAF1EAPY+eQz\r\n5bCN6hJUe5pLgUIzv+ILqL2/3ANivgda7p98R0ZJkTd7aNDXoht6Vq1tkpun\r\n36JNPeJbY7WHvVRqPy0yynuLvj9c4r/fMX+mPDXa6gAFN3ZSPawKb8qOVJxA\r\nkxMSfxbDPQSa4w1CkhnkZvwKat2+WYoRWPL+OXcOglTF/Q1ae8f7eETXLRBq\r\nWad2Jo8srROoku2oxXXmvIaEhX2fmeqzGxK7FTCaVa+KVj+x8+CbU4x8usw3\r\nZrcupADKz1Hv2s09R2YFeP3uk0+PzYaW2ozcwDsMvdWdbj71zx01aw7GVmRa\r\njWJ+h9xighW2UZFTmXg2qasw63SA9gL0RFKQ/FX2PmNmHIeZt7mjilt8aDZQ\r\nCxSG50jt1XYr2tdipmvyXQbTMqnN4LFjvxDbhV0oEfCznFgw5wzv4OJiGsVB\r\neF4TNWN5qlzrkUe3gMQ0vmmNOUfC9tk1ApfLBS0n06RHd99MQExNZoDPvfVK\r\nPCajdiIKDMIjC3t/JdU0Vv7mUqgIAqPlhTWIvCKRTyCJXllbDAVLQeS1gWD8\r\n4vDpuej7wquI1+4niJfWo9fjIzC9ixpc/HXhFljZi0Knve60U10U6eH5Litd\r\nE+dHAXsBmN0knQFws50ZaMIfLIc+rXgBvAc=\r\n=UkTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node.js": "./lib/node.js", "./browser.js": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "4df44b7403cac9c362ffcd8e2b3af72713a1db7b", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.0.1_1666398869626_0.02692608857663581", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.2": {"name": "lowdb", "version": "5.0.2", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.0.2", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "ac698f8c0a116a54f2875adcca89f9d23f195b2b", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.0.2.tgz", "fileCount": 29, "integrity": "sha512-e9/k7OtLZR4St1EJXz0ljS4ZnOpzU0ZgE88m4W2tRsND7GOnNie9NK4q7BDnxQ654II4H8gKyDSitk9OyIozhg==", "signatures": [{"sig": "MEUCIBt27y2pJ59d2bM5wHi6Hl4P1eKv2/GoYQ+NwiSRs6buAiEAtIQ9ZV+yBjMhSDpVyrpzjQLqdAkFhQu2S+S8nEa1QkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUz2FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1LRAAg4KZyLIeF7MkBBVN1C2KvgH+j/5ORo71TCtAeR7XAG1gJGU+\r\n3kwOvM6CW3TOaT+dViYxlZvLdMR+tOpPiseMnc5O1YZe69yuVoq0Qbv6fsKo\r\nx8SejQMpTDXTd0teb+yg0lz03E49sviiOo4WgTdCEso3QJRh/FbxjrAOnuSW\r\nm/h25mJoYQh+juAnYHpCWG7hKUtX73foqeKwn3hXD2cEgQClecdpx9wk/17F\r\naibMdzgX3bQqhkCurizVzH+gwYTSIwF/UsA2EZfyDW+fA3SDx+U1r7/5f+YV\r\nQ2aw7/3MNWjtKHYMzeY9+HtXJ8SWBQ+rDdTyFaPjxFHMSYXUK4djBYEY9NyQ\r\n914lTrgBa6PpzUk6Y5z6rBHPkpug37PvefR+qRtjbVG/uMpj+lfyfL7UIi28\r\nIVT3n3Oi1KAkVIwTVx+vQTvNiGGQhcyBs77MMIvaFV2ZNa6FgOnkOxx6rVab\r\nZqOSV9v8HRPRlkUJjqaU1a3s4O+f54stAX5Y0N+L8djq7UpQWKhSdizdpxO1\r\nCWkhdWMVHFiFT3trOO76thMns8DESho7HInylMeLkHduvrhs/PA5mwoXhc2f\r\nRvK0xFAq/E3Jxdf3pbwlTRYEx0gc0vrH+vzjuSVNmllCTmbhsat+nlqo6AEE\r\nMaBydYn+c8Zx22zvkM50FvxWMqUZ8VDTGAo=\r\n=OJkv\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node.js": "./lib/node.js", "./browser.js": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "57fc3ed0cf860a358f16c1d821442bd70a06d9cb", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.0.2_1666399620961_0.6492196166870405", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.3": {"name": "lowdb", "version": "5.0.3", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.0.3", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "aa01ef44c81e0f74122ae784c642948157fb44b8", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.0.3.tgz", "fileCount": 29, "integrity": "sha512-26qk7jgHbLGytYoZOf7/hgcvksYkRpXSnCJTSbnXApDExMkOw/vjWyjDBba4HYbuZN23MIf4Dc8q99QIgzQthA==", "signatures": [{"sig": "MEQCIGbKRk4IWjcVVP+3/JFzemC0SympSVNqbUidzj+k/YP8AiBhDDEc/sFllqv+2Rqtw0Nhit7K0o1ihUO8m6NnBhZFCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjU0SXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvPg//Xzp9H6K6y+mp7A2koh++Ac3Ymxxt8yNv1fXEdNyR1zzgN9IU\r\nxPYwowLwn6nDA8jyH5Yj2QuYZzSDpULLeu799kQSV4gmXc7XIVIXzh1/qRlN\r\n+M6bHISM6m/GFhNG504dOKe9GeVJDPvAHZ3G20TGSKBfqUVqlbZVWGYyT/Zd\r\nt7Cl1NIabhtvNiaM2GF/TNj+dFV1rsyheNQw+kqQBVxdLOLYWx7YQBTfG+zU\r\nMgQezjpWIMazNuobqwoiA3cdz27rQuJh627IyVtzXo+qI6VkzpmwIVLB7Dvt\r\n0qslPrUEWrxu5wZPAY6rQlbD6tFOdIlr2j9FfNvsSubISKzabTXl15t7EZiR\r\nfKWGfCgDp0s3BtJgqa+tzDl9wpN0n/6HPyucGgh6QH54S3R4IU5vUX9zjpnP\r\nDN7AxJoyVmWTe+Me8x4Z8s/PLUnCoBHtm6f3PFJeD5PLTpp9SEfh5saPxfT8\r\n98fdpYIrfwzVQkDB5rxugDGZIXQUWA7Yinm8wjGNtoV6KptK36zyO8RVwDU5\r\nJrysWvtLpLhKOvFMefh2RBltFeYD+PWoV29K3yAmdpV+3z0udcOP+9s/5JLm\r\n5ipkBTwkpPm6K8QHm4iiCNgbtMJRLcwzcQCHvQE6jztkW18G8SLerEw5MJTm\r\n/B8MZMLJZuLYwSHG8lrQC0jF08CC9ZfenXM=\r\n=/oCg\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "b4f2effa7cfc7f47f97902572c42225336f8b759", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.0.3_1666401431514_0.6294443609887233", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.5": {"name": "lowdb", "version": "5.0.5", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.0.5", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "30315e5a42432df188dcd17f1e5a288de68848e8", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.0.5.tgz", "fileCount": 29, "integrity": "sha512-7EWKmIMhNKA8TXFhL8t0p6N2LC53l3ZqsWQGSksGhhjrcms9rbKlyrAh2PzSGK5v0KPJ2W5VItBnC3NDRzOnzQ==", "signatures": [{"sig": "MEUCIHZRImrLtST6oRZjuiqOTNfWBNArav9VycvHaJ4IBnxxAiEAzwo3YYRxoLsOgOq/j+Z24U0BOJGAcIrJaQ2BzzFj0/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVaHBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVnA//egPw1S3is4TIctdm+9viYeT2utH2sZ8Ji8N8CkZOMc8wNfnX\r\nqhONDO0HUcEGLX7BXLSCMoqvsIyR0OZiM4FtpF5iqNtyUKzPbNyVSHrPhU4Q\r\n/yo/xKUdQqwznpNtxSTOJ48fa5ev+8mJGbN+TA+ctORe6GKReVu8HSp+QAiG\r\n/aWmcWFHzqWhZtAlh/pdNT4cD6xfHAy0V83Z3aTE5WZMaVHBiALSIvJDZ3Yt\r\n/WVRhfMhpOYrgXOM6Hw3iHuEvvkwHyZKwi1qgXbBWTxaUSq9+/Ega8aHwpEF\r\nqJ9XuIKZrJH+spCYjc+gRHyF7V9gyyCfZQBsqlbMZN4rOTNGe6QSrB0gTiKE\r\nDK7GiArvQ3EAQMnALWQ6+1cXOyDcon8ADuHTEl470e14k3u5V6zeEjDcG5Hj\r\nyZqJCnoFoDQxcDgV6MD1478Cq4ojksTjO96Y2ndQl7hqrHT27RlB2S7wDDWY\r\npFj+Jwoj2EsCXXNXyKCKLKR3Qj3RqdAqEUKwhRZM4ZF+LggAfy51vH7ZN5XY\r\nN9sAmfsMaqPLY52Kn4cY9FKcZ+bf//8F94/kqr9gF6K2WUyxni+3a5jetbRd\r\nPkQvNAQA4zj+IYI/FegteIR4/PkMQhJBbtQIxIVR3jCwI3X41Go24SkPCt9x\r\nUFCseqao7K3tVmUgKTPy9uIchXbtVXj8Qmk=\r\n=ezZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "2531e94affc69f0d1fa67612c88c55b4217c33b1", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "npm run build && xv lib", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "19.0.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^1.1.1", "husky": "^8.0.1", "tempy": "^3.0.0", "eslint": "^8.26.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "@types/lodash": "^4.14.186", "@commitlint/cli": "^17.1.2", "@commitlint/prompt-cli": "^17.1.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.0.5_1666556353635_0.7289539171273958", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.1.0": {"name": "lowdb", "version": "5.1.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@5.1.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "3d9f13522ea92e445348a7d455a69ce570c2f51d", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-5.1.0.tgz", "fileCount": 33, "integrity": "sha512-OEysJ2S3j05RqehEypEv3h6EgdV4Y7LTq7LngRNqe1IxsInOm66/sa3fzoI6mmqs2CC+zIJW3vfncGNv2IGi3A==", "signatures": [{"sig": "MEQCIFj0g7FZIK42hbbcUNKaVyiI4Tz4ExAbGXZCQpxJT+iOAiAZJC19fycnZwfkcqmmmVbZr6TE5P5h1XyTgPXuyOMpgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3DW8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4gA//aSpCA7R1G64iESJ/NPvgVgMcq7rIEeaq642S0QY+QjKKmen5\r\n53eQdMDjFDWKDluOvOs8nteoE2AAc+npH//xgi9enm9Tvbyt3qxCfhfOq4Hz\r\nXvWQfKUrjOfcfaJEr5iJ7PZ6Jn5lefE+NCsqpdKQQpBmvcq9l0I3CLp4iDY9\r\nDDcR7WY3XyMSv94kBiSxyW8deE7N2gEcuxZ3KfIhq7s4PKFnbBSS8i2yubxp\r\nuGxd81U4zsqi6y4p8SmNjrbymERiShOvibF3BOZkNBk/eXroE5PKGq4yWbpy\r\nxGO0wCJyzh52J8T2iPRZNkAbVSan+cHqkBB7aBQSjjBwgAWZfQr3GjMTcU6C\r\n3rlJpgjY4njbVk9tyt2x6lWmBejK/iO3Of/lrp4SBixV/pzXF1jNXSrSHQsX\r\nox+/xtuYWrnuUET3LFrKdHeYP9wlTgOZxsQt1kIuaKVpLHNwC+AERj+kvP4L\r\nwoHupRec62NCMAUiGFdIMIrwTVqd06qipc2q+R3JWfI1WJf7sNwLo8n0ESjf\r\n/Mw1B4BY12ACpYxx3rIii+4RbR9VeU8EhU3tFA8vrNSm78mZ8XiaFS9yEQTa\r\n2OWZsjMGkMK6LOPA5oomsQsBhTl9a5GaMYQlZirk/E/t13BmNF1idw1yXr8u\r\nyujFXMRmDoMXgGuuQ8kyg05WIIeBoYuX+BE=\r\n=rEmD\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=14.16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "3ce2fc1cd6b0cd69ee4ec6356f335a52c0601027", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "xv --loader=ts-node/esm src", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "19.5.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^2.1.1", "husky": "^8.0.3", "tempy": "^3.0.0", "eslint": "^8.33.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.5", "@types/node": "^18.11.18", "@types/lodash": "^4.14.191", "@commitlint/cli": "^17.4.2", "@commitlint/prompt-cli": "^17.4.2", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_5.1.0_1675376059835_0.7128856548417146", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "lowdb", "version": "6.0.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@6.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "4c5e339c12da232e75911981968d02ec106ade69", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-6.0.0.tgz", "fileCount": 23, "integrity": "sha512-0gzvRjT5Nv3nYbjy0I3FcYjuTNFWLEb0nvRYyb56XodR+7lasgWdxQ6FhIziJu6xHIiJb9xxKLrB1OPJjFZkmQ==", "signatures": [{"sig": "MEYCIQC0w/8A0iUECjLg4J/RQirbcpGYle9EjW6gLLQNqEbOmwIhAMjNfXvU3eqLFExpEBkfIxwNUUspDOD2lm/TbBhCQCbL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPzusACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpk2Q//Zno1tH+DRpFRHUbMm3fmAI5KkCM9SIUoDgiGbLG4VoyTqW5L\r\ns69PMqVJz7qxY0xNq1uWn8xaEenekGp6pY2HxjTVURv70u5dbHriAjKxeUhf\r\neBoRl9qOEPaYJFLIYKTNc/Edb7YojOtGZzADZ0McD1TXotkfLIW6RGMU1RO2\r\nxwAkc9VlD1QQp/jGrQsnp6klJDMRf4+Mh60bet1ubDOEep3keeX3WTXURSzr\r\ntS6Kgv32d7XRCPfWgDWO49pBqV8XUF/FwWGLsLKg8GNuEHpi+D0e6aXWsRXg\r\nL2QrxO2hv2COol/ZDgqh1r6JU1bs/Na++NpH9KOdSmFI9JqK4/DOzToHrmxS\r\n4sk0bT7tvgXJhrWw7aGsPzpG3sMChZNIFf8oTxj7QZj3RRJzQnveXYQVsuEw\r\nF7NchNdb9LCQiIMnt/bLZ15XjxxX5OagNAGuO2xMqGQ5QRXhQ8Ou0LlbIuOB\r\nZb8ZUAJweanJ51LpcH5M+RgubPyGyUdjFkk0ZANCRkh4zOCsKUBkEs8T1vDM\r\n/jGnacGSdPnbOn/uaun1FG+vGhct/UpZLuV8ZxQ+TqwDvYJzCDUFbwdhPBZK\r\nhHhXGtVHeNn6dRJW+kfJaLhW8P0v/IZDt7Uxm2o5KE+UonxJaYo9VXx4ejHc\r\nybJ05GSUn/TIOKIRcyftTrDNnSqRqJAidhc=\r\n=UPbH\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "lib", "engines": {"node": ">=16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "94023ea3d5bd66f729e844870a4f6f2ffc5a99d4", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "xv --loader=ts-node/esm src", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "19.9.0", "dependencies": {"steno": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^2.1.1", "husky": "^8.0.3", "tempy": "^3.0.0", "eslint": "^8.38.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/lodash": "^4.14.194", "@types/express": "^4.17.17", "@commitlint/cli": "^17.6.1", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^17.6.1", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_6.0.0_1681865643822_0.5455881415173076", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.1": {"name": "lowdb", "version": "6.0.1", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@6.0.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "2c84ae74340fa81ace9c8f17b2fd7fbf9544e7d4", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-6.0.1.tgz", "fileCount": 23, "integrity": "sha512-1ktuKYLlQzAWwl4/PQkIr8JzNXgcTM6rAhpXaQ6BR+VwI98Q8ZwMFhBOn9u0ldcW3K/WWzhYpS3xyGTshgVGzA==", "signatures": [{"sig": "MEUCIQC67kCuYOyYyLsRwJ/ud19Ei/xMVYlMsG7fsVtf6KTCwwIgFsWXl01xaCKdCTcdo4KX5f/504qnkhsMPGC55yjOLWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkS4+9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfQw//cNesXJHa0jMASk1oFwy1GKlJXjzgV0032jfswxB/wJnW9+e4\r\nMa0sAcxMdPOok8HWBmxgm5C3nLvm5XdDg5TtLPoxwBDBzQl6m947Hz4GDEun\r\nqRXO2YoOp8+m9Wp93JtMFhod0FsSWsgLeI+SDQYrbjd37AkHzdOXJM6b+oPc\r\nO++lM30+yzyDUVufJv2koa0gPpuvkmqPuF0mFDbFrGBrvmYvHMSjQy/YGR6N\r\nZbSqtb6Po528AGxIkN9lL0BysZOaUiebIMuG0vZyrGJG/laTmEhpgHYptva+\r\njh9yfq3yfqcWqv3Np26b1thRQonCUuFObnvtWmkJpLTc+ldaWkNWvRuRjPw/\r\np7dkIMWfvPtDX06EM51jgTO/AbiHQymUd2IfYhmTWJl+fBGxXQ5AEShNwFnF\r\n876GYSN/J16nGDQGYgAOGbHJWfVtjU+flwLsGwLUi9Z4o/yiSPOLD0KW8gCq\r\nQsQGhMSzAFyTaOa9T1XCqHQHTFPJmRT4CdBGLWBrzpU8OvmhE2IzHabaKSzu\r\nNL4sQRMhb88xLuOzbOJx7qDzHi5SKwU2sy84UUO2m2o4Fh3NkAdd6XfG9hPO\r\nmwZzUD9gTj+/rGaTpdGBkqaQz3kX60JZbSfErtyRi7HY/8phmVoAg1OPgpom\r\nR/+DsOfFmOzD3YEZf8K9oS7pn6rAC3UN3zY=\r\n=QkdG\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./lib", "engines": {"node": ">=16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "338f7b91cf607c3a34dff55d797bc44f62713c0c", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "xv --loader=ts-node/esm src", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "19.9.0", "dependencies": {"steno": "^3.0.0"}, "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^2.1.1", "husky": "^8.0.3", "tempy": "^3.0.0", "eslint": "^8.38.0", "lodash": "^4.17.21", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/lodash": "^4.14.194", "@types/express": "^4.17.17", "@commitlint/cli": "^17.6.1", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^17.6.1", "@sindresorhus/tsconfig": "^3.0.1", "@typicode/eslint-config": "^1.1.0", "@commitlint/config-conventional": "^17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_6.0.1_1682673597145_0.36264940589827943", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.0": {"name": "lowdb", "version": "6.1.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@6.1.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "b17b8b1688f00b57dd2421f1beba3bbe3101b407", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-6.1.0.tgz", "fileCount": 27, "integrity": "sha512-jED4i1iWNf3OL1IcUMS7tD3n1L+vlKMTdXhFFj6rCQBdhkITt4j5i9us0wjoHkHhS2l2Od1QChT1e8XEZ0V+7w==", "signatures": [{"sig": "MEQCIAwWkRxWzCeRHaJAi06bBqBhl8leI2QKhZEjvz3n/+DoAiB8W0YL8Q17Nr8c1EUcsGBrd8d70hCcOClCpWAqAdfcfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20137}, "type": "module", "types": "./lib", "engines": {"node": ">=16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "d0fb65a847406399b009be83872630f519b9d2b0", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "xv --loader=ts-node/esm src", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"steno": "^3.1.0"}, "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^2.1.1", "husky": "^8.0.3", "tempy": "^3.1.0", "eslint": "^8.51.0", "lodash": "^4.17.21", "del-cli": "^5.1.0", "ts-node": "^10.9.1", "typescript": "^5.2.2", "@types/node": "^20.8.5", "@types/lodash": "^4.14.199", "@types/express": "^4.17.19", "@commitlint/cli": "^17.7.2", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^17.7.2", "@sindresorhus/tsconfig": "^5.0.0", "@typicode/eslint-config": "^1.2.0", "@commitlint/config-conventional": "^17.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_6.1.0_1697244526307_0.13294407102421024", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.1": {"name": "lowdb", "version": "6.1.1", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@6.1.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "fb3c66a116b0ef799641f677676071661f6804f4", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-6.1.1.tgz", "fileCount": 27, "integrity": "sha512-HO13FCxI8SCwfj2JRXOKgXggxnmfSc+l0aJsZ5I34X3pwzG/DPBSKyKu3Zkgg/pNmx854SVgE2la0oUeh6wzNw==", "signatures": [{"sig": "MEYCIQDlDHKWAjm0ZW2sfB+m3/8Qc1fUOkQxQZ6ellgP+aLojQIhALxEdVP4G6UH/KwIBiJ55M3LVq02nMqQtEaWaTRInBJ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20126}, "type": "module", "types": "./lib", "engines": {"node": ">=16"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "da434c258327bbe2b353b4ce50d5a938f6901c6c", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "xv --loader=ts-node/esm src", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"steno": "^3.1.1"}, "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"xv": "^2.1.1", "husky": "^8.0.3", "tempy": "^3.1.0", "eslint": "^8.51.0", "lodash": "^4.17.21", "del-cli": "^5.1.0", "ts-node": "^10.9.1", "typescript": "^5.2.2", "@types/node": "^20.8.5", "@types/lodash": "^4.14.199", "@types/express": "^4.17.19", "@commitlint/cli": "^17.7.2", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^17.7.2", "@sindresorhus/tsconfig": "^5.0.0", "@typicode/eslint-config": "^1.2.0", "@commitlint/config-conventional": "^17.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_6.1.1_1698069053697_0.4401921611457291", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "lowdb", "version": "7.0.0", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@7.0.0", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "6e39453950116a530a34f1d2474d4adfacc6b622", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-7.0.0.tgz", "fileCount": 29, "integrity": "sha512-WJ9SIMS3D/Pabu8nAL5wgJo2ldwT+yWu2KDKANyyoduJmbabECQbpg/PxJz7q4vXEXnmV8eJT1/gD0wLV0Q+xg==", "signatures": [{"sig": "MEUCIQCDCws+BP98wRgYIvH02Emsa2SRFRK9JmwuA++KrHbe7AIgH8K5BySIaUao/DWTaSie8BOdFnTKRKkNgqZQ/g215ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783}, "type": "module", "types": "./lib", "engines": {"node": ">=18"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "605cb93a9e314c58ac4890031f68fd8c9a1b3ac1", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "node --import tsx/esm --test src/**/*.test.ts src/**/**/*.test.ts", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"steno": "^4.0.2"}, "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.7.0", "husky": "^8.0.3", "tempy": "^3.1.0", "eslint": "^8.56.0", "lodash": "^4.17.21", "del-cli": "^5.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/lodash": "^4.14.202", "@types/express": "^4.17.21", "@commitlint/cli": "^18.4.3", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^18.4.3", "@sindresorhus/tsconfig": "^5.0.0", "@typicode/eslint-config": "^1.2.0", "@commitlint/config-conventional": "^18.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_7.0.0_1703630480206_0.2612994126202366", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.1": {"name": "lowdb", "version": "7.0.1", "keywords": ["database", "db", "electron", "embed", "embedded", "flat", "JSON", "local", "localStorage", "sessionStorage", "browser", "esm"], "author": {"name": "Typicode", "email": "<EMAIL>"}, "license": "MIT", "_id": "lowdb@7.0.1", "maintainers": [{"name": "typicode", "email": "<EMAIL>"}], "homepage": "https://github.com/typicode/lowdb#readme", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "dist": {"shasum": "7354a684547d76206b1c730b9434604235b125e5", "tarball": "https://mirrors.cloud.tencent.com/npm/lowdb/-/lowdb-7.0.1.tgz", "fileCount": 29, "integrity": "sha512-neJAj8GwF0e8EpycYIDFqEPcx9Qz4GUho20jWFR7YiFeXzF1YMLdxB36PypcTSPMA+4+LvgyMacYhlr18Zlymw==", "signatures": [{"sig": "MEQCIQCBohWcoXxLYvWeegehg+YAPMVAyfGaeuF2Rdnod0kazgIfMgp7VRwuvEquUYjiN8sum24Zig89GejIS9t4KGtXXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22873}, "type": "module", "types": "./lib", "engines": {"node": ">=18"}, "exports": {".": "./lib/index.js", "./node": "./lib/node.js", "./browser": "./lib/browser.js"}, "funding": "https://github.com/sponsors/typicode", "gitHead": "31458f3173f133d153afc5a8dfb5a76eaa1c7b66", "scripts": {"lint": "eslint src --ext .ts --ignore-path .gitignore", "test": "node --import tsx/esm --test src/**/*.test.ts src/**/**/*.test.ts", "build": "del-cli lib && tsc", "prepare": "husky install", "postversion": "git push --follow-tags && npm publish", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "typicode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typicode/lowdb.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Tiny local JSON database for Node, Electron and the browser", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"steno": "^4.0.2"}, "typesVersions": {"*": {"node": ["lib/node.d.ts"], "browser": ["lib/browser.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.7.0", "husky": "^8.0.3", "tempy": "^3.1.0", "eslint": "^8.56.0", "lodash": "^4.17.21", "del-cli": "^5.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/lodash": "^4.14.202", "@types/express": "^4.17.21", "@commitlint/cli": "^18.4.3", "express-async-handler": "^1.2.0", "@commitlint/prompt-cli": "^18.4.3", "@sindresorhus/tsconfig": "^5.0.0", "@typicode/eslint-config": "^1.2.0", "@commitlint/config-conventional": "^18.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/lowdb_7.0.1_1703633198819_0.2591648530944284", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2014-04-02T02:21:16.414Z", "modified": "2025-03-06T16:02:46.029Z", "0.1.0": "2014-04-02T02:21:16.414Z", "0.2.0": "2014-05-12T19:50:49.835Z", "0.3.0": "2014-06-10T19:03:14.348Z", "0.3.1": "2014-08-21T12:54:52.628Z", "0.3.2": "2014-08-26T19:42:04.377Z", "0.4.0": "2014-09-03T08:20:59.570Z", "0.4.1": "2014-09-17T00:04:55.595Z", "0.4.2": "2014-10-09T14:22:00.896Z", "0.5.0": "2014-10-15T13:28:22.923Z", "0.5.1": "2014-10-23T19:31:05.243Z", "0.6.0": "2014-12-02T16:02:37.109Z", "0.6.1": "2015-01-06T08:32:40.389Z", "0.7.0": "2015-02-03T10:59:15.762Z", "0.7.1": "2015-02-04T05:10:21.167Z", "0.7.2": "2015-03-04T15:49:20.548Z", "0.7.3": "2015-04-20T22:18:55.425Z", "0.8.0": "2015-06-02T20:24:30.074Z", "0.8.1": "2015-06-03T23:02:03.502Z", "0.9.0": "2015-06-15T13:54:23.460Z", "0.10.0": "2015-06-15T21:55:51.764Z", "0.10.1": "2015-06-29T06:52:13.725Z", "0.10.2": "2015-07-15T17:12:24.976Z", "0.10.3": "2015-12-01T23:05:45.033Z", "0.11.0": "2015-12-15T20:10:45.528Z", "0.11.1": "2015-12-16T11:31:59.240Z", "0.11.2": "2015-12-25T16:33:07.900Z", "0.11.3": "2016-01-07T04:45:39.497Z", "0.11.4": "2016-01-07T07:32:09.916Z", "0.11.5": "2016-01-13T20:37:28.177Z", "0.12.0": "2016-01-13T23:05:26.612Z", "0.12.1": "2016-01-14T22:12:15.835Z", "0.12.2": "2016-01-20T21:12:50.166Z", "0.12.3": "2016-02-13T21:42:46.041Z", "0.12.4": "2016-02-17T23:38:40.033Z", "0.12.5": "2016-02-26T22:59:44.218Z", "0.13.0-beta.1": "2016-05-05T13:18:46.653Z", "0.13.0-beta.2": "2016-05-08T16:12:55.539Z", "0.13.0-beta.3": "2016-05-14T13:15:26.612Z", "0.13.0-beta.4": "2016-05-17T11:29:04.399Z", "0.13.0-beta-5": "2016-05-27T11:24:55.881Z", "0.13.0": "2016-06-06T11:22:41.469Z", "0.13.1": "2016-06-08T22:43:04.239Z", "0.14.0": "2016-10-25T20:23:37.651Z", "0.15.0": "2017-02-07T21:49:42.281Z", "0.15.1": "2017-02-07T23:03:37.007Z", "0.15.2": "2017-02-08T23:11:50.368Z", "0.15.3": "2017-02-08T23:14:42.442Z", "0.15.4": "2017-02-08T23:39:11.969Z", "0.15.5": "2017-02-15T21:14:15.690Z", "0.16.0": "2017-03-09T11:46:10.766Z", "0.16.1": "2017-04-09T00:25:34.955Z", "0.16.2": "2017-04-09T18:32:24.245Z", "0.17.0": "2017-08-20T18:08:19.093Z", "0.17.1": "2017-08-20T18:21:59.709Z", "0.17.2": "2017-08-23T11:06:12.685Z", "1.0.0": "2017-08-31T20:25:37.676Z", "2.0.0": "2021-05-20T06:44:32.614Z", "2.0.1": "2021-05-20T06:48:44.183Z", "2.0.2": "2021-05-21T01:44:57.225Z", "2.0.3": "2021-05-21T13:31:15.765Z", "2.1.0": "2021-05-24T11:02:27.305Z", "3.0.0": "2021-09-13T14:29:02.928Z", "4.0.0": "2022-10-17T23:34:19.537Z", "4.0.1": "2022-10-21T23:59:26.580Z", "5.0.0": "2022-10-22T00:27:49.938Z", "5.0.1": "2022-10-22T00:34:30.157Z", "5.0.2": "2022-10-22T00:47:01.127Z", "5.0.3": "2022-10-22T01:17:11.723Z", "5.0.5": "2022-10-23T20:19:13.892Z", "5.1.0": "2023-02-02T22:14:20.015Z", "6.0.0": "2023-04-19T00:54:03.992Z", "6.0.1": "2023-04-28T09:19:57.310Z", "6.1.0": "2023-10-14T00:48:46.541Z", "6.1.1": "2023-10-23T13:50:53.923Z", "7.0.0": "2023-12-26T22:41:20.416Z", "7.0.1": "2023-12-26T23:26:38.995Z"}, "users": {}, "dist-tags": {"latest": "7.0.1"}, "_rev": "844-55da6868c6947180", "_id": "lowdb", "readme": "# lowdb [![](http://img.shields.io/npm/dm/lowdb.svg?style=flat)](https://www.npmjs.org/package/lowdb) [![Node.js CI](https://github.com/typicode/lowdb/actions/workflows/node.js.yml/badge.svg)](https://github.com/typicode/lowdb/actions/workflows/node.js.yml)\n\n> Simple to use type-safe local JSON database 🦉\n\nRead or create `db.json`\n\n```js\nconst db = await JSONFilePreset('db.json', { posts: [] })\n```\n\nUpdate data using `Array.prototype.*` and automatically write to `db.json`\n\n```js\nconst post = { id: 1, title: 'lowdb is awesome', views: 100 }\nawait db.update(({ posts }) => posts.push(post))\n```\n\n```js\n// db.json\n{\n  \"posts\": [\n    { \"id\": 1, \"title\": \"lowdb is awesome\", \"views\": 100 }\n  ]\n}\n```\n\nIn the same spirit, query using native `Array.prototype.*`\n\n```js\nconst { posts } = db.data\nconst first = posts.at(0)\nconst results = posts.filter((post) => post.title.includes('lowdb'))\nconst post1 = posts.find((post) => post.id === 1)\nconst sortedPosts = posts.toSorted((a, b) => a.views - b.views)\n```\n\nIt's that simple.\n\n## Sponsors\n\n<br>\n<br>\n\n<p align=\"center\">\n  <a href=\"https://mockend.com/\" target=\"_blank\">\n    <img src=\"https://jsonplaceholder.typicode.com/mockend.svg\" height=\"70px\">\n  </a>\n</p>\n\n<br>\n<br>\n\n[Become a sponsor and have your company logo here](https://github.com/sponsors/typicode) 👉 [GitHub Sponsors](https://github.com/sponsors/typicode)\n\n## Features\n\n- **Lightweight**\n- **Minimalist**\n- **TypeScript**\n- **Plain JavaScript**\n- Safe atomic writes\n- Hackable:\n  - Change storage, file format (JSON, YAML, ...) or add encryption via [adapters](#adapters)\n  - Extend it with lodash, ramda, ... for super powers!\n- Automatically switches to fast in-memory mode during tests\n\n## Install\n\n```sh\nnpm install lowdb\n```\n\n## Usage\n\n_Lowdb is a pure ESM package. If you're having trouble using it in your project, please [read this](https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c)._\n\n```js\nimport { JSONFilePreset } from 'lowdb/node'\n\n// Read or create db.json\nconst defaultData = { posts: [] }\nconst db = await JSONFilePreset('db.json', defaultData)\n\n// Update db.json\nawait db.update(({ posts }) => posts.push('hello world'))\n\n// Alternatively you can call db.write() explicitely later\n// to write to db.json\ndb.data.posts.push('hello world')\nawait db.write()\n```\n\n```js\n// db.json\n{\n  \"posts\": [ \"hello world\" ]\n}\n```\n\n### TypeScript\n\nYou can use TypeScript to check your data types.\n\n```ts\ntype Data = {\n  messages: string[]\n}\n\nconst defaultData: Data = { messages: [] }\nconst db = await JSONPreset<Data>('db.json', defaultData)\n\ndb.data.messages.push('foo') // ✅ Success\ndb.data.messages.push(1) // ❌ TypeScript error\n```\n\n### Lodash\n\nYou can extend lowdb with Lodash (or other libraries). To be able to extend it, we're not using `JSONPreset` here. Instead, we're using lower components.\n\n```ts\nimport { Low } from 'lowdb'\nimport { JSONFile } from 'lowdb/node'\nimport lodash from 'lodash'\n\ntype Post = {\n  id: number\n  title: string\n}\n\ntype Data = {\n  posts: Post[]\n}\n\n// Extend Low class with a new `chain` field\nclass LowWithLodash<T> extends Low<T> {\n  chain: lodash.ExpChain<this['data']> = lodash.chain(this).get('data')\n}\n\nconst defaultData: Data = {\n  posts: [],\n}\nconst adapter = new JSONFile<Data>('db.json', defaultData)\n\nconst db = new LowWithLodash(adapter)\nawait db.read()\n\n// Instead of db.data use db.chain to access lodash API\nconst post = db.chain.get('posts').find({ id: 1 }).value() // Important: value() must be called to execute chain\n```\n\n### CLI, Server, Browser and in tests usage\n\nSee [`src/examples/`](src/examples) directory.\n\n## API\n\n### Presets\n\nLowdb provides four presets for common cases.\n\n- `JSONFilePreset(filename, defaultData)`\n- `JSONFileSyncPreset(filename, defaultData)`\n- `LocalStoragePreset(name, defaultData)`\n- `SessionStoragePreset(name, defaultData)`\n\nSee [`src/examples/`](src/examples) directory for usage.\n\nLowdb is extremely flexible, if you need to extend it or modify its behavior, use the classes and adapters below instead of the presets.\n\n### Classes\n\nLowdb has two classes (for asynchronous and synchronous adapters).\n\n#### `new Low(adapter, defaultData)`\n\n```js\nimport { Low } from 'lowdb'\nimport { JSONFile } from 'lowdb/node'\n\nconst db = new Low(new JSONFile('file.json'), {})\nawait db.read()\nawait db.write()\n```\n\n#### `new LowSync(adapterSync, defaultData)`\n\n```js\nimport { LowSync } from 'lowdb'\nimport { JSONFileSync } from 'lowdb/node'\n\nconst db = new LowSync(new JSONFileSync('file.json'), {})\ndb.read()\ndb.write()\n```\n\n### Methods\n\n#### `db.read()`\n\nCalls `adapter.read()` and sets `db.data`.\n\n**Note:** `JSONFile` and `JSONFileSync` adapters will set `db.data` to `null` if file doesn't exist.\n\n```js\ndb.data // === null\ndb.read()\ndb.data // !== null\n```\n\n#### `db.write()`\n\nCalls `adapter.write(db.data)`.\n\n```js\ndb.data = { posts: [] }\ndb.write() // file.json will be { posts: [] }\ndb.data = {}\ndb.write() // file.json will be {}\n```\n\n#### `db.update(fn)`\n\nCalls `fn()` then `db.write()`.\n\n```js\ndb.update((data) => {\n  // make changes to data\n  // ...\n})\n// files.json will be updated\n```\n\n### Properties\n\n#### `db.data`\n\nHolds your db content. If you're using the adapters coming with lowdb, it can be any type supported by [`JSON.stringify`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify).\n\nFor example:\n\n```js\ndb.data = 'string'\ndb.data = [1, 2, 3]\ndb.data = { key: 'value' }\n```\n\n## Adapters\n\n### Lowdb adapters\n\n#### `JSONFile` `JSONFileSync`\n\nAdapters for reading and writing JSON files.\n\n```js\nimport { JSONFile, JSONFileSync } from 'lowdb/node'\n\nnew Low(new JSONFile(filename), {})\nnew LowSync(new JSONFileSync(filename), {})\n```\n\n#### `Memory` `MemorySync`\n\nIn-memory adapters. Useful for speeding up unit tests. See [`src/examples/`](src/examples) directory.\n\n```js\nimport { Memory, MemorySync } from 'lowdb'\n\nnew Low(new Memory(), {})\nnew LowSync(new MemorySync(), {})\n```\n\n#### `LocalStorage` `SessionStorage`\n\nSynchronous adapter for `window.localStorage` and `window.sessionStorage`.\n\n```js\nimport { LocalStorage, SessionStorage } from 'lowdb/browser'\nnew LowSync(new LocalStorage(name), {})\nnew LowSync(new SessionStorage(name), {})\n```\n\n### Utility adapters\n\n#### `TextFile` `TextFileSync`\n\nAdapters for reading and writing text. Useful for creating custom adapters.\n\n#### `DataFile` `DataFileSync`\n\nAdapters for easily supporting other data formats or adding behaviors (encrypt, compress...).\n\n```js\nimport { DataFile } from 'lowdb'\nnew DataFile(filename, {\n  parse: YAML.parse,\n  stringify: YAML.stringify\n})\nnew DataFile(filename, {\n  parse: (data) => { decypt(JSON.parse(data)) },\n  stringify: (str) => { encrypt(JSON.stringify(str)) }\n})\n```\n\n### Third-party adapters\n\nIf you've published an adapter for lowdb, feel free to create a PR to add it here.\n\n### Writing your own adapter\n\nYou may want to create an adapter to write `db.data` to YAML, XML, encrypt data, a remote storage, ...\n\nAn adapter is a simple class that just needs to expose two methods:\n\n```js\nclass AsyncAdapter {\n  read() {\n    /* ... */\n  } // should return Promise<data>\n  write(data) {\n    /* ... */\n  } // should return Promise<void>\n}\n\nclass SyncAdapter {\n  read() {\n    /* ... */\n  } // should return data\n  write(data) {\n    /* ... */\n  } // should return nothing\n}\n```\n\nFor example, let's say you have some async storage and want to create an adapter for it:\n\n```js\nimport { api } from './AsyncStorage'\n\nclass CustomAsyncAdapter {\n  // Optional: your adapter can take arguments\n  constructor(args) {\n    // ...\n  }\n\n  async read() {\n    const data = await api.read()\n    return data\n  }\n\n  async write(data) {\n    await api.write(data)\n  }\n}\n\nconst adapter = new CustomAsyncAdapter()\nconst db = new Low(adapter)\n```\n\nSee [`src/adapters/`](src/adapters) for more examples.\n\n#### Custom serialization\n\nTo create an adapter for another format than JSON, you can use `TextFile` or `TextFileSync`.\n\nFor example:\n\n```js\nimport { Adapter, Low } from 'lowdb'\nimport { TextFile } from 'lowdb/node'\nimport YAML from 'yaml'\n\nclass YAMLFile {\n  constructor(filename) {\n    this.adapter = new TextFile(filename)\n  }\n\n  async read() {\n    const data = await this.adapter.read()\n    if (data === null) {\n      return null\n    } else {\n      return YAML.parse(data)\n    }\n  }\n\n  write(obj) {\n    return this.adapter.write(YAML.stringify(obj))\n  }\n}\n\nconst adapter = new YAMLFile('file.yaml')\nconst db = new Low(adapter)\n```\n\n## Limits\n\nLowdb doesn't support Node's cluster module.\n\nIf you have large JavaScript objects (`~10-100MB`) you may hit some performance issues. This is because whenever you call `db.write`, the whole `db.data` is serialized using `JSON.stringify` and written to storage.\n\nDepending on your use case, this can be fine or not. It can be mitigated by doing batch operations and calling `db.write` only when you need it.\n\nIf you plan to scale, it's highly recommended to use databases like PostgreSQL or MongoDB instead.", "_attachments": {}}