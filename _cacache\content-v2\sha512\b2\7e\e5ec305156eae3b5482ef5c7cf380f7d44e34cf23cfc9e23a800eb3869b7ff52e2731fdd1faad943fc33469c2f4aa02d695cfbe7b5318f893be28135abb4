{"name": "lines-and-columns", "versions": {"1.0.0": {"name": "lines-and-columns", "version": "1.0.0", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.cjs.js", "jsnext:main": "dist/lines-and-columns.es6.js", "engines": {"node": ">=4.0.0"}, "scripts": {"build": "rm -rf dist && rollup -c -f cjs -o dist/lines-and-columns.cjs.js && rollup -c -f es6 -o dist/lines-and-columns.es6.js", "pretest": "npm run build", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.1.18", "babel-plugin-transform-es2015-destructuring": "^6.1.18", "babel-plugin-transform-es2015-modules-commonjs": "^6.2.0", "babel-plugin-transform-flow-strip-types": "^6.1.18", "babel-plugin-transform-strict-mode": "^6.2.0", "mocha": "^2.3.4", "rollup": "^0.21.0", "rollup-plugin-babel": "^2.1.0"}, "gitHead": "0e88ddfececd4af4f9eaf6c2987cd321f5e66459", "_id": "lines-and-columns@1.0.0", "_shasum": "de486cde66262180bce3119585766e9a7d056784", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "de486cde66262180bce3119585766e9a7d056784", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.0.0.tgz", "integrity": "sha512-6zPjI/ABrHq26yGsnqERtjBS5X8PB+OMLeKmWDS5Xs81Dzz4b8mzHGpZ76vJEd3MqCSZFuXRopYDLQbbAKQ5/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDF3Mn+7SYRJl18bz+hYALOPQhqEEzncXDSeF3oq+3EKAiAPEdScSROlo1vjTIfMYS/XAQDZUj7ajHqtpDrWI8BJRA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.0.1": {"name": "lines-and-columns", "version": "1.0.1", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.cjs.js", "jsnext:main": "dist/lines-and-columns.es6.js", "engines": {"node": ">=4.0.0"}, "scripts": {"build": "rm -rf dist && rollup -c -f cjs -o dist/lines-and-columns.cjs.js && rollup -c -f es6 -o dist/lines-and-columns.es6.js", "pretest": "npm run build", "test": "mocha test.js"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.1.18", "babel-plugin-transform-es2015-destructuring": "^6.1.18", "babel-plugin-transform-es2015-modules-commonjs": "^6.2.0", "babel-plugin-transform-flow-strip-types": "^6.1.18", "babel-plugin-transform-strict-mode": "^6.2.0", "mocha": "^2.3.4", "rollup": "^0.21.0", "rollup-plugin-babel": "^2.1.0"}, "gitHead": "7635f8fb3f9ff02a04f0f3e19cd012e18656e85c", "_id": "lines-and-columns@1.0.1", "_shasum": "30372b66259fb97378bdcd59c6c2fe447a7993c7", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "30372b66259fb97378bdcd59c6c2fe447a7993c7", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.0.1.tgz", "integrity": "sha512-qpHueAwTQ1+BhWKOuePC5nh52MogcsNTHfdgRTNmpKfHLEN6DbtRbQf6xEPRNVmVUeuqlQq84ROeLaPreCpS8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbKtnruaecMxdrElqQth/9CqZAC57rf7zppE/02PeiDAiATxSUH3eJWs4d2nq9KRRxR6UmmlOR9YejTmcGg9KV2hA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.0.2": {"name": "lines-and-columns", "version": "1.0.2", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.cjs.js", "jsnext:main": "dist/lines-and-columns.es6.js", "engines": {"node": ">=4.0.0"}, "scripts": {"build": "rm -rf dist && rollup -c -f cjs -o dist/lines-and-columns.cjs.js && rollup -c -f es6 -o dist/lines-and-columns.es6.js", "pretest": "npm run build", "test": "mocha test.js"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.1.18", "babel-plugin-transform-es2015-destructuring": "^6.1.18", "babel-plugin-transform-es2015-modules-commonjs": "^6.2.0", "babel-plugin-transform-flow-strip-types": "^6.1.18", "babel-plugin-transform-strict-mode": "^6.2.0", "mocha": "^2.3.4", "rollup": "^0.21.0", "rollup-plugin-babel": "^2.1.0"}, "gitHead": "610143f9664251127d20ca6d04aa7383e9e60cbc", "_id": "lines-and-columns@1.0.2", "_shasum": "33d9624cfcc5e3eb63923be2d8b7155fbb04c155", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "33d9624cfcc5e3eb63923be2d8b7155fbb04c155", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.0.2.tgz", "integrity": "sha512-JLPLufguZW3v23idTMD2Dv6+iyxcVlohQLZJVEshy5juPTmE/AGEBGmJelvOLPR4AqWnY/kAo3k6YBHlC1+3dg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFwDB+tzqn5UWTX2+5EhnkwW82K5G3FvMsemgLdB45ZHAiEAjP+wBESGp8jMWplqIVIYvrDu4p2gIXEPqTjo3/j+qg8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.1.0": {"name": "lines-and-columns", "version": "1.1.0", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.cjs.js", "jsnext:main": "dist/lines-and-columns.es6.js", "scripts": {"flow": "flow check --all", "build": "rm -rf dist && rollup -c -f cjs -o dist/lines-and-columns.cjs.js && rollup -c -f es6 -o dist/lines-and-columns.es6.js", "pretest": "npm run build", "test": "mocha test.js", "prepublish": "npm run flow && npm run build"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.5.0", "babel-plugin-transform-class-properties": "^6.6.0", "babel-plugin-transform-es2015-block-scoping": "^6.7.0", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-modules-commonjs": "^6.7.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-flow-strip-types": "^6.7.0", "babel-plugin-transform-strict-mode": "^6.6.5", "mocha": "^2.4.5", "rollup": "^0.25.4", "rollup-plugin-babel": "^2.4.0"}, "gitHead": "c1e6ba514d96fcd917383ccfb854cc33ea15cf9e", "_id": "lines-and-columns@1.1.0", "_shasum": "91b83757be950239b1f9ff6f46f1370d69d026d2", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "91b83757be950239b1f9ff6f46f1370d69d026d2", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.0.tgz", "integrity": "sha512-EjOc62wKB5odUIlr4JyxjRwlxtqFdM6gnPNSgxlSdlRiTqdQtkC75oWHydJDcqJaYxVo/53JCdXz2pejL02B5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFWv3B2jY0hebjT/9i3iS7a1qVamCI/6ChP7zNZfVDxeAiBPRoNfAWFfhEeT2p+4Ze+/iy4h/Y3g+S7HEldkRr+rrw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.0.tgz_1457488184614_0.5232401010580361"}, "directories": {}, "contributors": []}, "1.1.1": {"name": "lines-and-columns", "version": "1.1.1", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.cjs.js", "jsnext:main": "dist/lines-and-columns.es6.js", "scripts": {"flow": "flow check --all", "build": "rm -rf dist && rollup -c -f cjs -o dist/lines-and-columns.cjs.js && rollup -c -f es6 -o dist/lines-and-columns.es6.js", "pretest": "npm run build", "test": "mocha test.js", "prepublish": "npm run flow && npm run build"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.5.0", "babel-plugin-transform-class-properties": "^6.6.0", "babel-plugin-transform-es2015-block-scoping": "^6.7.0", "babel-plugin-transform-es2015-classes": "^6.6.5", "babel-plugin-transform-es2015-destructuring": "^6.6.5", "babel-plugin-transform-es2015-modules-commonjs": "^6.7.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-flow-strip-types": "^6.7.0", "babel-plugin-transform-strict-mode": "^6.6.5", "mocha": "^2.4.5", "rollup": "^0.25.4", "rollup-plugin-babel": "^2.4.0"}, "gitHead": "4650b8f7a8a739a06328a5de84400753c9cbe273", "_id": "lines-and-columns@1.1.1", "_shasum": "4f46560cc4176f202250bae717cc6121ce7ddb48", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4f46560cc4176f202250bae717cc6121ce7ddb48", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.1.tgz", "integrity": "sha512-QZZcNSKtmXciGthYI8EQ3qqkvQMdR1+sxtdRQzv8dR7+jSWiJqTl2kKNaMyAJBFhsYYsj/Up15AjCqaZozkEgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRZs5qWlF5RFKVMIgYQ7bhu1GGDRZRz6LCevDibiWCDQIgY7yb9bU42LusoxSoPxCHbFs0UwIyqFX6RuJbBbpqRcc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.1.tgz_1457488564610_0.6213514695409685"}, "directories": {}, "contributors": []}, "1.1.2": {"name": "lines-and-columns", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.js", "jsnext:main": "dist/lines-and-columns.mjs", "scripts": {"flow": "flow check --all", "prebuild": "rm -rf dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha", "prepublish": "npm run flow && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-plugin-transform-class-properties": "^6.10.2", "babel-plugin-transform-es2015-block-scoping": "^6.10.1", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.10.3", "babel-plugin-transform-es2015-shorthand-properties": "^6.8.0", "babel-plugin-transform-flow-strip-types": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0", "cz-conventional-changelog": "^1.1.6", "flow-bin": "^0.27.0", "mocha": "^2.5.3", "rollup": "^0.33.0", "rollup-plugin-babel": "^2.6.1", "semantic-release": "^4.3.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "version": "1.1.2", "gitHead": "79964233efe24ee9a5ae4eae85ebe7142ebddf41", "_id": "lines-and-columns@1.1.2", "_shasum": "648792a91ae008b973a325974782580c4e017275", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "6.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "648792a91ae008b973a325974782580c4e017275", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.2.tgz", "integrity": "sha512-F0g79Mp7j2XWTcF30mEDILAlWCDkOY8uVd+HAwsLe99rguffDJcNHAFyySyGgAU0heNoyaRP00iL6HakQyrBIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnXTPhREmERDNi1o8TMKSdx7Qsp+7wePX3hCJYffuyYgIgYDnQ2SmJ8OMH+AD/k+OVIFRRWWAyVmAnBiCVP3QV9Eg="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.2.tgz_1466739388702_0.5629058612976223"}, "directories": {}, "contributors": []}, "1.1.3": {"name": "lines-and-columns", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.js", "jsnext:main": "dist/lines-and-columns.mjs", "scripts": {"flow": "flow check --all", "prebuild": "rm -rf dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha", "prepublish": "npm run flow && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-plugin-transform-class-properties": "^6.10.2", "babel-plugin-transform-es2015-block-scoping": "^6.10.1", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.10.3", "babel-plugin-transform-es2015-shorthand-properties": "^6.8.0", "babel-plugin-transform-flow-strip-types": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0", "cz-conventional-changelog": "^1.1.6", "flow-bin": "^0.27.0", "mocha": "^2.5.3", "rollup": "^0.33.0", "rollup-plugin-babel": "^2.6.1", "semantic-release": "^4.3.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "version": "1.1.3", "gitHead": "9617425c265b2513647101a3be3a1ec940066746", "_id": "lines-and-columns@1.1.3", "_shasum": "f125832de8738e7be1598bf24d9feb21d0d9eb98", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "6.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f125832de8738e7be1598bf24d9feb21d0d9eb98", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.3.tgz", "integrity": "sha512-SOoxbuO7UQlCbff5DADtDeDsVXa/YwF+N7EuzJGPzRwk+oPbfEKURcHRBhlnIb1NLwA7obql+g4THNk+uTDOew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRIWe8F3vSGh5JyZUkSL2VFTNCio+nqnrSu6yBxxjXVQIgLpbmywuJlXebAHvTYjgvzYs22B2A6cG6okRfBWP//mo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.3.tgz_1466740355894_0.8184910498093814"}, "directories": {}, "contributors": []}, "1.1.4": {"name": "lines-and-columns", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.js", "jsnext:main": "dist/lines-and-columns.mjs", "scripts": {"flow": "flow check --all", "prebuild": "rm -rf dist", "build": "rollup -c && cp index.js.flow dist/lines-and-columns.js.flow", "pretest": "npm run build", "test": "mocha", "prepublish": "npm run flow && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-plugin-transform-class-properties": "^6.10.2", "babel-plugin-transform-es2015-block-scoping": "^6.10.1", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.10.3", "babel-plugin-transform-es2015-shorthand-properties": "^6.8.0", "babel-plugin-transform-flow-strip-types": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0", "cz-conventional-changelog": "^1.1.6", "flow-bin": "^0.27.0", "mocha": "^2.5.3", "rollup": "^0.33.0", "rollup-plugin-babel": "^2.6.1", "semantic-release": "^4.3.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "version": "1.1.4", "gitHead": "9f4a9b7dfbde6043a445845cfcb89a75da551d51", "_id": "lines-and-columns@1.1.4", "_shasum": "1255c21e10861baddd8107fbf53df03e0a805f26", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "6.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1255c21e10861baddd8107fbf53df03e0a805f26", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.4.tgz", "integrity": "sha512-qlf02asU3s05nBj4vopYjvrnjQMvKo84DhqTg400bd5Rc/+omJc8OASXj9nJlA1OfMroezjseXnnScS3owajgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHCD6yYexVj76Clfo4gviqGxZboDq3t61AcGdVY9ZAYhAiAPZ1MEho1hf3pq9Tg4cP+frhFd8G0g0oeNFm9gHHYiHg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.4.tgz_1466960264032_0.44263432384468615"}, "directories": {}, "contributors": []}, "1.1.5": {"name": "lines-and-columns", "description": "Maps lines and columns to character offsets and back.", "main": "dist/lines-and-columns.js", "jsnext:main": "dist/lines-and-columns.mjs", "scripts": {"flow": "flow check", "prebuild": "rm -rf dist", "build": "rollup -c && cp index.js.flow dist/lines-and-columns.js.flow", "pretest": "npm run build", "test": "mocha", "prepublish": "npm run flow && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"babel": "^6.5.2", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-plugin-transform-class-properties": "^6.10.2", "babel-plugin-transform-es2015-block-scoping": "^6.10.1", "babel-plugin-transform-es2015-classes": "^6.9.0", "babel-plugin-transform-es2015-destructuring": "^6.9.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.10.3", "babel-plugin-transform-es2015-shorthand-properties": "^6.8.0", "babel-plugin-transform-flow-strip-types": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0", "cz-conventional-changelog": "^1.1.6", "flow-bin": "^0.27.0", "mocha": "^2.5.3", "rollup": "^0.33.0", "rollup-plugin-babel": "^2.6.1", "semantic-release": "^4.3.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "version": "1.1.5", "gitHead": "b9726de36f160bf45aa64b24244572cf6bb11c77", "_id": "lines-and-columns@1.1.5", "_shasum": "fca3bf4dcbc1ed32e138b719b7eefccf401ffe4b", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "6.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fca3bf4dcbc1ed32e138b719b7eefccf401ffe4b", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.5.tgz", "integrity": "sha512-oVyZbrZsN6s0QlEoUDpynAniMRmqpI5P+12R5C150yky+nVeTPiymxy5geOMbxY+6uK7sFWwqWmqg0uGLV/97w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA/g05Q/99BWhmOumrPRVnQQadBPAin4yRryDm4cP0+GAiEA/Q+N59Pp4SQG48b8uqIVP1ElWEIfV+bNzNufJW5Y98M="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.5.tgz_1466978038879_0.6655475040897727"}, "directories": {}, "contributors": []}, "1.1.6": {"name": "lines-and-columns", "description": "Maps lines and columns to character offsets and back.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"lint": "tslint --config tslint.json --project tsconfig.json --type-check", "lint-fix": "tslint --config tslint.json --project tsconfig.json --type-check --fix", "prebuild": "rm -rf dist", "build": "./script/build", "pretest": "npm run build", "test": "mocha", "prepublish": "npm run lint && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "keywords": ["lines", "columns", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "devDependencies": {"@types/mocha": "^2.2.34", "@types/node": "^6.0.52", "mocha": "^3.2.0", "semantic-release": "^6.3.2", "ts-node": "^1.7.2", "tslint": "^4.1.1", "typescript": "^2.1.4"}, "version": "1.1.6", "gitHead": "bde3deba75c01fa95d18cd4786593df2318cdeb6", "_id": "lines-and-columns@1.1.6", "_shasum": "1c00c743b433cd0a4e80758f7b64a57440d9ff00", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "6.9.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1c00c743b433cd0a4e80758f7b64a57440d9ff00", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.6.tgz", "integrity": "sha512-8ZmlJFVK9iCmtLz19HpSsR8HaAMWBT284VMNednLwlIMDP2hJDCIhUp0IZ2xUcZ+Ob6BM0VvCSJwzASDM45NLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLl/FNkkYPyi2ee1uoarDfXWpx+xhCI11T8dc9V0Z5FgIhAJBncoAQNpYZQuUze6Xrm0kwlmyZxai9supNtSlWQUzC"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/lines-and-columns-1.1.6.tgz_1482255134226_0.4856822253204882"}, "directories": {}, "contributors": []}, "1.1.7": {"name": "lines-and-columns", "version": "1.1.7", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": "./build/index.mjs", "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "78591e9929c0546ebdc52b73ddc8a898fe699ed8", "_id": "lines-and-columns@1.1.7", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-HBp1gFWrO0H3YCWs7dSTUcSxcfREVWvTWr1qBLQlQ741CDHQuV+R2XYqEogJrscQs0obzz2N/TM5pDigBaf2XA==", "shasum": "a3dba31d1b53d293ba700e20035be10b36189d12", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.7.tgz", "fileCount": 5, "unpackedSize": 5452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmXNyCRA9TVsSAnZWagAAGu4P/A4itu1AGYzMG2JajK01\ntHGhny+0B1I1haWMelk2MSuJRSczT5mTtFapMPjIU6uJuzz/x4bSeZCAU65I\noHv6DEcXYmYIzGTcgT4B8hUBuSCCy9xF9wdl1V8qFRo9Hb0RxdSIj1lOHlsx\nW/TbRCsSFf1+LJQSiZo45mnc7SI60MvTSwwHHOt7leeZPh2KeAGq8xEq7PdU\nPkqrkPddxx2eOsGBWpGja7DuFj8uyhILB1B0VRm5URhZ2nwQCzTjOzYVNhC/\ntRIrd37fnFyBAYBREysrRwhrqG2CNDck9x4wV9+oKGyu5hF1vC0q3cuU5N2Q\ndyyKkjAp+td84kuXRfgT5mB0cgMCTDEAK3R2rBK8UsZgbcpVk2dtYZb1bR+S\n1TbnjPJ4aoN9ofl1gZeZR9FNMPz895OjGP3z0nGvsTh+lljYwr/EpFGHrS6D\nmVuLGbYze8Vd0fSUJ4T+AG8JAUeJLSc6mDT2+fs3yVGCASAakZ/tMq/fmK2p\ndrPmmpG2nbqqLs48nYdVmPBnjF7Sa6PnUw4CVyLp2xpzQekIXSvtZgi0/QbJ\nL05zsI1caPyl5mY8VoXhe6y8dY5bOJUBIPLBAjdyWyFwmaSS2VRX3fr9xmEi\nlbA/fY0sSI+i12CzftK+lr6UippxbfLfjrF9pyPEdEHucrMj14Z5rAfuE+SB\nfnLt\r\n=C9m7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQUZppFqgW90RpFo4y1Z8+fmkfElGz6hMOlRqr9sGXSQIhAPKWbpgAJ9nUljoftc/Vwlsx45g+9GvJeYrMjOhjwBbU"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.1.7_1637446514320_0.38816438343702564"}, "_hasShrinkwrap": false, "deprecated": "please use 1.1.8 or 2.0.1 or later", "contributors": []}, "1.2.0": {"name": "lines-and-columns", "version": "1.2.0", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": "./build/index.mjs", "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "2474adcebc7086ff2aadd5978318ba77d071e525", "_id": "lines-and-columns@1.2.0", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-tfO1siOurT114rus/3opoeRtTAWjfgQFY+p8Q2wswWVhXpjk99Rb/IKX76akoJDh7pxlQuf4XfZ4p/PSwBMuuQ==", "shasum": "730382e9a1cbb3fdeb5494b3915bfae145c0d3d2", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.0.tgz", "fileCount": 5, "unpackedSize": 5452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmXVnCRA9TVsSAnZWagAAJx8P/iKvY07uph8wHucPyFDS\nsO4gYQTpWogzsd91v2amkC7Y4h17Nh3x9oadxie+Pf+HXr8Nc6C+5JXE+RC3\npPv5SvijYdE84fUlW70Ap7toKXXYQvhlYhjy+ayPzQ+ZoPDG1BJ8OLJsPIGQ\nBpizK7bXBd1gjjkus897ZjTkq9QeWC/pJDKc2xkaushpZcC+v38XOTwbb0k0\neLyVUkLKmPGDvJ73xpCJm7VWyupY0tAY4QSyNUYg7jN5wH+1dieWf5oDJKMz\ny/tWsaNFL7zidFVZXQ2NZK4jVFNBfDZWzylle5FXAGsUwacoW4z1oZiBhExC\n5+pheyvlH+zPqU4sybenNnfJNAcUnsJaGEgYIv+k/VFQ+upVlQRWQuJlBe70\nfGPLndKcD90WOmkx3Fx1/dDyKnT/2BuiIzoJawFga2SlNIE/0d7PdhO8OzMH\ndgSNhkQLNAXP8PXVEACE4muYnqGveuVTPkHb0BZylvVwiovJlgudMr7JyPHz\nI4zalbrhLzV7vI7HJQSAjOMxPooJW4WJfsI+YSlkJrMLjy0TGyLNwcDlE1bT\ni02eLgjSjs9xewjCTDBGXrcZj3e1gSZBrKkXnPn+2ZlhYyGKOJxBssYKykws\n9opeOjmz/vEtoRWYJZb2ve/QKjN3ZHpIbROZ27QBb8PeQ/GBMFOKljFLPvck\nHE7N\r\n=JJNv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtdSsuKYkV3CQTnMWdwvf0fLDXmvcEmXLBENWcqG4bCQIgMImIr1HjD9mtKdYBKq2nFhjBdOyg3lCwLdnjopZ24BM="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.2.0_1637447015533_0.3933273335050267"}, "_hasShrinkwrap": false, "deprecated": "please use 1.2.1 or later", "contributors": []}, "2.0.0": {"name": "lines-and-columns", "version": "2.0.0", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": "./build/index.mjs", "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "f2ab22ac87f816751ca2f7377704f150ecc4ecb1", "_id": "lines-and-columns@2.0.0", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-gxLwtmUfKU+NaDpY16nyh/qKfvBaEdszWI5d5+ebUze7QnPgKkbiwWnygMSddD8Ra1GrWzp4TcmQAfoga0ckvA==", "shasum": "be2ba35c94d7ee3f24ddfd3d21b4a0795a399b1b", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmXY7CRA9TVsSAnZWagAAJiUP/22QBtwKeRd7DKtA5Uk4\nP9fStKKFCdxK8LlPUWAFe2/U7IeH1eBiTIRHY/jVYDVsewwstK4+wdLcHTIN\n06H+hyKFKDUI1LoEt3YEhrAq9CHC/g+O5H6ybVZcLqNgVjSa8Kh28h6eekrH\n/B1ooTwVise84GZqRkHE4bVgXkutST3donT1GSwXIl9uW1K1nhP32SyJa7bn\n5x2CF+i125tILwUsuiX7b1hbUmtAfWzl1hpqdkxmv8zA5sM6/+x7Q3d8QKxB\nSUNR+lcMhHY8qFE1+k3Ohoqn7Nr+JWT3YHjHwfupkirwjS/MIsnnnlMa0QCw\njEabC+uLRUy6TmoJ5fE6H0BPLg5TsmwNkGL3qBPwsa/HkIDHg+QBU/yImj2Q\nvmZKM8YrYwRy1rOmbMj1U/AO80A+aZm6cwc15JBUWEfV766YJTqokzLcERCz\nHcVguoujA8gJa8EmFyQfFm1BMtnerQWntB/4pNCPgVQcB50ENRWD9nFncazz\npCYivBT0Eom3tuCktyyIe7Rio4WOdAD4fCD71tsmC5c+3MZNMtzg3yBMl+eW\nBAmAJ5rY9f9BeF9R100t8h+eGac4VntTD/+pNEdrU2FOj/3H5FwQnQ6I1is7\nZW1oLxC5eRrY2OkrZ9G6Qvq6yP3CPeEpW426hrgbtU8HiBgwc/4tSY649Mbs\nABjC\r\n=QuB+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDM+DRe3tUdqbJrcCiMNTwntREtHi1UZGQkcojdTGsdBgIgTXgFvYtsAOAsaOTh8QvUSWjYOs8W/dXS+Q6ILFIAwSg="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_2.0.0_1637447227743_0.40634113968537044"}, "_hasShrinkwrap": false, "deprecated": "please 2.0.1 or later", "contributors": []}, "2.0.1": {"name": "lines-and-columns", "version": "2.0.1", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "d2aedf5349f43dcea50222a62c9fb4ccfd87f5db", "_id": "lines-and-columns@2.0.1", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-7rsOYyoMwr75yCekiYv4xp5LMXAYXtRI+eSOCaHdrwRe98DY5qQzMmOd+0U88/eFGsIrPO5yrNvcZnoauuG6QQ==", "shasum": "5ff4eb28c1b4b90c9eba78898de1642e722de5d6", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmXnLCRA9TVsSAnZWagAALlEP/iLEM1siSHft/oaad58Z\nE1audSoibi7VxnIlu8VrUHHKjjZbywWAxBskbstdLdp8hk3WgydO0e8vSKG7\nk7q3YNd/4kCExGBJ/owYnF0vMkvqKyXy+Qvo4jkU93XuI52yhFvB+u5eCj5I\nsb8E3R4ipq995I1jY2AykYAme1/HxZScQ7PapR/74Wr9kwp5DkHwvPDu5+2b\ncGM5Xgb+pWWB+7m8LOMIBg+Sp3Jj1RA4BbhnejjxCDOC4pOIq2aOMk//377+\nyjtwxW+uMwvuJ4jNGcUAjPMqP+rtm8OEAzDfNO3iiY2kyQ3Vo7qnGpCzCiub\nHDMxzNejUxieclwKGGEKWA766ibiLVTmS6b0wo8WmJWSQ4heRjjkNnWSPAdB\nbvy/ZBy4e8iYKHf9Oxw+sAZ6mitSqpXAPF0/JGpzhoETDWLj7sKQccpJiS56\nxm6eCMgF4DLnE5UBghWvtYpSsy/Mys542arxFgw4cZfurcfwy4KHTPvgYMRN\ni3qpF18s3x2twpdL9xhVZtFu5YNsXfFWUg1o4H+O0tOwdtr1JxGUdbK5OiS2\nZAIgSNFZYz2FMTJ4TpP7o58WGTpYJkvM/eZtvhCT8i3XixN1fGQMx71YzKxc\nkt/7myb2Ik4ioc7pfK+of2eT+8YcldlLGBPbnng1tTIDrtzCZLLVtlAZiDjs\n4ylL\r\n=UPYT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQlsSSROY5P3Z8lBSaRnW5mbMrUDpOq8GSvocAAY7VhQIgFCZv0ohbfe8ldGzSdSkoI6ChZJhXQBFahP+GhEt3p58="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_2.0.1_1637448139567_0.5111330770496894"}, "_hasShrinkwrap": false, "contributors": []}, "1.1.8": {"name": "lines-and-columns", "version": "1.1.8", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "213b884e58bfbbdcf54a855f10c0f0175d8f6d37", "_id": "lines-and-columns@1.1.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-4nimUILxeva1mIa9C6uSFcO69vWL0sh6wnldXZG18c9r9M5Vp8fnI9XOcDZPNII7m+JMRwnzPL4N5C5tAcKEuQ==", "shasum": "bcc1795411871b77192d8235a37b37f0c077f279", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.8.tgz", "fileCount": 5, "unpackedSize": 5451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmYAWCRA9TVsSAnZWagAADVQP/0gDgFVSxyyJFOCatZFe\nemvffjyFG/71kfdwJfXwuK6Id1XkeuiM6xG+6gFdkWtkMyW1zQIrsqM7Rl7D\nZhz1xU+Q5LyWmgVzjUYr0haCsVml8QqrfkXlKxCp39ZvA1K6l1a33KXiA2LW\nSbKmqqcj2yDyJSIVmABHfJ2JYH0WKaIkTRJbXhZmzc2FTM5NEQEZYuTzreKY\nHE/yeq9niw0kCrU9nx7SV3Z69v2dUI27DV7h84ZypIOd/yD5HP55zNPUNk9B\nEWJVgG5ErOAgOrhSeEXrR5jbHh4e+OPJZI8O04G+mbY+Hu5YLc+3OPpABOhO\nGLv3/gGrEu9nWG+gsiFk/Sr1aQS7JeE/R/5toHc2sUeFWVH95MJ+mI2asnzH\nxfWYO3lyuyGF+flNrWshDmyNCjmPxHGy/TjmgnIDPxPx4ccio3OQJEBOtgyp\nBJNfyMVX0gYIPlKhRbXCYlWPRZSLBgbOwUTRd/uKpwHiY445istPtr76PPRL\nYn4aoTodB9ub6AzdEBoxzSV0AMwnRrP+fq88y9gpL3p3eyYK8qQez6QEbLUZ\nhRLRRm+ZBjBB/GyV0JLueii7Oi38azZbgX8NOvCUb6k4v54Labrivbu/vgri\nseI7Z3fWtF+NTvByrlOuUUvQVjX2Yiuc/y5cWuBnNibV7RFlXSkr6bU/pQy7\npLXi\r\n=JohI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt91FGWL6Q4QD6LKmLzjKVPg5v7TpWmNSbpG9s/WbUmAIhALiHRnhIDc4fGc2sHyZMGahAUAeFGzSBzMwu5QJS265v"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.1.8_1637449750624_0.41938497636709804"}, "_hasShrinkwrap": false, "deprecated": "use 1.1.10 or later", "contributors": []}, "1.1.9": {"name": "lines-and-columns", "version": "1.1.9", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs"}, "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "f405090a3a69b431e1deca879531ae38fa73edb0", "_id": "lines-and-columns@1.1.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-rpDZBibnmaf8snIGG4CWqx5aAztoEeXWdLghALF202x6eS0FrbPxZnNf/NSeWkepOgNVsD4avd1FBg5kvvZIDA==", "shasum": "a5fdcb01d06bf3825c4ba120a7d96c6792184cae", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.9.tgz", "fileCount": 6, "unpackedSize": 7493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmYF3CRA9TVsSAnZWagAAo0AQAIfQFEB+r97WjRbVXEHZ\nNukVaGOy+9vdgDsk2pbuVcoQwfTQenM0h1ll8JI/SSlMiDu3fBaO1t+emRP6\ngFdx6Tl+BX9Jssm3jb+IhFQIAHZ2l9wAeoHodrra46i3zmAwAbxV92etixrV\nW1L+Xzv3gFIDvMYmzeMIcFgTFsyx1bT5/VKnqVIklqUjOT6vJJlhirkmMigH\n6F/r9qh0yZ+Ny28BX91Dkuo5je3mymjKI3YYQieROmq9B9yY9Suu7kMl0Pjc\nW7Vz2Vry5DDDX79Xg0Ai4cf/jtA0IOhl2YgAdItwWCpUQsK7jYzwdhtw1FFG\nIKQIKRoO79Dz+ymgvrXGas5KJknfvWUHkW5WLFsmIMqS1hefjAPnl6zijYhH\ndpLZmpeuONjIAO0DoDKrft9tDu6wgbrDPIq9ureo7ZOH+HMHyrawK+RQeUvN\nNtrXIa/Nbyx6LyrdjSXPTCg8xd/QECZVc0Ursggm9eY+X02A7zLOYfk31hlv\nLknKXVl12x+UGCZ1sdg2Hrs38q5ko2WNWslQUkg5dNWMHlkpIZLokHxX2aRY\nGpHFaMgcjwonPijoMQGH0bqYUmKMMT+AZIWPsTHsyVu1vNi5MA6z78zcRkls\nR5gSEG0W7TEVqZE6CX3lAMWZSmB7ebMPdSigq720pO3gEhFKTB1crxf9yIGS\n7Lt4\r\n=kQkj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVtDIG4o3inbbegCkGZLHQZRevI+hACBGt5oqzAWb73gIgYN36nDnCwCzk1BcKFFftGWFmqKgCwDCygDQ9uDEQk4g="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.1.9_1637450103213_0.43000422555653883"}, "_hasShrinkwrap": false, "deprecated": "use 1.1.10 or later", "contributors": []}, "1.2.1": {"name": "lines-and-columns", "version": "1.2.1", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs"}, "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "9b5d514e12b8ef3b56f9396c28c3518e2cae118d", "_id": "lines-and-columns@1.2.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-91ZNKhhjixyL5pQ0a6Chny53eg1Q7usO/eaU6Jjws6BhclcINGKG4zDkA6Wq11pZ6oqvElwBV+rnRTfG7t1Xmw==", "shasum": "2710d566aed4caeb6761f4c78d94435f15a939e9", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.1.tgz", "fileCount": 6, "unpackedSize": 7493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmYH/CRA9TVsSAnZWagAACkEP/3Sayx/QxgUKM3/BXQvU\nSFqZsRDE/Dqb1Zncg3cUooUB+JTyVmwnI2uL/B6hiJ0oNBsj7jhab6sVfDIa\n3bDyOYpljQNEoeAoG5riJ7D0xArsKufqYJHiOXGvesF9adXOvBebE5BiW6Oc\n9XSxzgoGgShWzDoYccVU6Y7dsmxe4Vr6kVymsqQ2jRr9Le8vAMCiVsIaNnl0\nKVy+PKe8XAWqsCEqSBk/lDJBwsq08es/vpKZe5vL86D/Nci/qtpEqvE84YD6\nG2NCrAVU2QoVC//OewrGZpFL6xE+xdT3s3X67QnwG1bSjDIErWNriM+VrPOS\nd/NxDALFnsgv935YDcPpt+Kx0j4zzz5WYeUIDVuQd4sQEYV+FYG/Qjy+htho\nXx3koA1uzsTRwDe13et+P/Nahj6rd+T1kQjHW1B0I0+xTR/MQvhO6EvuABd7\nqWlq+2JzNgbuyMYE69vk9QOH6xyZj0TwrFDkqW3xrf/9BA3OBe/4f7/HImkY\nbq78txpV7W6jIDaXlYu0Led2FTVMODK+1gJ0B1z0sXbG97oxGd+OVf51+vew\np/yAopYRWPPWaGAP43AUbFtux7NtelU4qMOs02h7tmAEYvsK+G93EPysIZKy\n98kNhzQ+q46BjVgu4Sz3sW+SCEZvZoNQfNDnSC1XynKL+ftV6MZuf6j94zxu\n6vs9\r\n=TQwt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDri3R7XUPMeLVAKIo4meRiAIQg9jA963i10MLqYkwHgAIgMxaqw2JmjYPeRk5Z5r6JE9OExrtkvfdt9DFX3NMWRaI="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.2.1_1637450239485_0.44838416996774044"}, "_hasShrinkwrap": false, "deprecated": "use 1.2.3 or later", "contributors": []}, "2.0.2": {"name": "lines-and-columns", "version": "2.0.2", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs"}, "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --project tsconfig.build.json && mv build/index.js build/index.mjs && tsc --project tsconfig.build.json --module commonjs && mv build/index.js build/index.cjs", "build:watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "1596423297c02ea1dd4f28fbfa07a5dfc8ff6e25", "_id": "lines-and-columns@2.0.2", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-RaBDLqsC4A5/YmPV5DENC2/Q4DaXsk89b+jhUFPl0kvkztkyjGnJAH0I6Bcze/fO06ieP0NxTM+02iXFlt0OZQ==", "shasum": "84de68939c83376b568327a156899c64a369f4d5", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-2.0.2.tgz", "fileCount": 6, "unpackedSize": 7659, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYZTKmiQaKKsmMlBwsumn4hnQ+p3ihHd3Ax6x5ULft5gIhAMObQx0sqUbv+n3lOXEXMutw3hVkZKBmESF0eqz1vAyT"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_2.0.2_1637450926833_0.25550799192942764"}, "_hasShrinkwrap": false, "contributors": []}, "1.2.2": {"name": "lines-and-columns", "version": "1.2.2", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "readmeFilename": "README.md", "gitHead": "88c0068267d24b990775630f05137b720d0802c5", "_id": "lines-and-columns@1.2.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-nXjfuvceZmVgD6K/cDwDU4tVAwTqjZfLRqcBSX33Efu5/RA7hRhuNOzKw1YugxVqNbf+pfhglAaKQs8jn/KLSw==", "shasum": "f74afb0ad97d43cf72df6bc99611d3abe08e4175", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.2.tgz", "fileCount": 5, "unpackedSize": 5473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmZLHCRA9TVsSAnZWagAA08wQAIwGpq/POa65Ws+5Seh6\nzM3d6LQINjShACE8wsl+mbn8DoXoYriM5v6DjohIQQng6K5F/EFfZX7qRjLp\nymdhLIle/h2hZCmV0RYSPXoM441UBIteop96ErrDJXOsl1Ke6VQXY2BHF9Nj\nPtF8un+eTsAdIJN1zIOcJGZYkZKuaGqpbKRVDAVRZCMvuXwJpGXUocyCO09E\nlvj/c/iBWPzOyC6APl1wrdGFtAIu9FzsbXh1r7Y5I889Y0AKK9uyPRUSp3Jp\nQtTphnILnmiqSjg+y6f9yKqN1DBKErpsEJuWYve2lbmHbtLubsVRulEmQqGR\nHvBAQmOuU0moIqzpGoRvxcDOGtwCuyBEJ1pK4yxzmTxB55Lx+9lcC3bDAZpg\nVUaYD1/O/K3RBO1dMoaDUtxU8qNvPHmUDwI7ElcO/Cr9rsW1vaQfPE8h4eUQ\nux4DjF/EhI1kMMmIQsMEpw4WtcUBCvBD5pJO1VETpADsQxCMzmcR2GziFe9q\nZFjmpGeQFcyelCqKmQ2nu0/92/IwnEuPtNx5xegNKbTVf4hUSKPp1/D+qh/L\nALdHTXTnsDggIad9GlnjW52GJMo6eULWIcB1i3wuOKDUadQzlrN4q7GLWFhz\nv8ylJVVGeAmenphkkymsBgMUr6ZNgVP0TPuoFTFLpbg+zn+u5S8aPtbOUJaC\nZrju\r\n=uUbQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4PUhFx7QypSVSNI3+Ygo0Oxab3wS4uyu9MkzUho45bAiEA54JMP4VcShyH+CYeREoAGPiDgsDrhWsdIu6Ojg/1X0s="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.2.2_1637454535337_0.15639165278752087"}, "_hasShrinkwrap": false, "deprecated": "use 1.2.3 or later", "contributors": []}, "1.2.3": {"name": "lines-and-columns", "version": "1.2.3", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "readmeFilename": "README.md", "gitHead": "202600ade7764fad528c0621d7640c43e535d4d1", "_id": "lines-and-columns@1.2.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-bQEvPBbeyPQGdQ/1H/tnLUz0479EH/9pHxCe3TGRQFxpg/PIbBeTUwpm7xyewdFfHjKLGidU/lSH1QkF+i8HUg==", "shasum": "3360dbb0dfffd23a6ad0300135efacb50f50eb73", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.3.tgz", "fileCount": 5, "unpackedSize": 5453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmZM/CRA9TVsSAnZWagAAtnYQAIcGEoNmDTkeHC10MGTj\nX/71yuCaQZhaZKSD3sbATqNdTG7gV6HA8GP9Gs2zXmC+DfyDYIdJO4Eafclo\nEtulNcfZDmjMQeqBvyXw/jmCNUejxM2BD6gFZNOdb753ndEv/Qg2DB1B9Wlu\ne7+cLBrb6y/GPmBPoQknORjggOR4RTg3OU22dte5r+rrbkLDLxb8f+ranGnF\nbwrozis+zfH1mH9KQAwYMxm9Fm+5M4TDRPeYZfAH57+6yM+VwKxcJQJSKmJa\njzYWdbfJefMwtT/80NzhILkQyRtbhX5yrfNkluLvqca8YWQ37WoEgnD7se9+\nv1YBoWM3jyS+icBYDXU6r+nhCC2sA4ZZsz3/o5SfIQ+JkyG9+eHG7AK4GLQx\nmty2ZqRGL+LNwXj8tNI4mFIsMEZWpk75XxJnnqcHQlYvKSkecCJkegRUncQT\nmVUJxOYAUQ1lyXmpFioZqIEL7nW5X33b0bHj6g8WmgIv6NXGK/eo0spYVM0m\nsI9A4oPgrTHowhhkvj4huNV6Tkgm1M6V7i8QhJVpsgxnvqb6UZC57nxxxNyV\nsrp3l4fMRqCIDEmU/8Cfd8XYZcOiFJruPKWUb3KHE14uCmbJhlZ56d6HQ7Fr\np2xPc4stPpbXlI2eyQpElg5qkflzEO3tcHNefB9DpTO4FPOc5o0rI6pFNbSA\noEBT\r\n=ltsJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEj8q87wr+aoHUWSwPR77tgcGfC22k5yV/+09bto1IX6AiBOap0ffwBafM8a08S+xorRI+vmoT14FjuwVTN1IgiWMg=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.2.3_1637454655610_0.309924235629945"}, "_hasShrinkwrap": false, "contributors": []}, "1.1.10": {"name": "lines-and-columns", "version": "1.1.10", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "readmeFilename": "README.md", "gitHead": "4a49b9d06430260762411f08143c5c83f99f4f7d", "_id": "lines-and-columns@1.1.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-aDLy3yn9C/nOFPoH6DolAGBlJ5fjLPjPtNMA5rtUvmTQfg67sDcPURcCUbwuIdcewMQ3Kv90s/2W1Hkd6PRSNw==", "shasum": "72d31c564ea5f3f6f12cfc43e157471f522e232f", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.10.tgz", "fileCount": 5, "unpackedSize": 5454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmZOuCRA9TVsSAnZWagAANGcP/iyyWtDX56WeQA0zPjQW\nVsn1mjGm0sE0nm6uVwaYs10mZen/HuDRKKw4iyvbbpm1QVw4jp1GfqZPSdpb\nO44sg8IWY2onNKg4P3xtkvMR3q31AwThSwaFCB+3trr5Sdq5lGYacIOc9/+T\n5vxekfbltRGVcnllqjSpXnBFx6hD139/CCrEcuJQiqPF1VzyfRsw0Hb8x93t\nUuiJT5slvEfwyfLBvy5negsLpShVlJXzu6JQ0hHUdLJe8opYPz9xgNHkEBFg\n/ye8cCA7pz2exr8jmj3vVFzVzRlZT0xSbwmWLYYCl2fIjinaQgYUthMw25Ey\n3wWC6kmZNSXzLq13hcALNyQVR/4tJOoFFqqNkn4f3iPkNFZ6MEFrMaU329lS\nochScTxXT2Ecu2d4umEaPclga7cQVxlR3Oa4Y68d9GPmucNyYtl330IuTWm4\nYdtVEN4W4WocS/sPtI02zWtUjrt4QdZhLr6Sw1jX8XSiAEOk/IhEuDndMw5k\n8Uy9qmKNBSAwa4X4FhVq55MYnWhEJF6JCJOqGSGhW135RoPjIiOUqDu5ifHX\nWchCNZCQWnGiiR3nfkptXEk4v7dwAgW8Hy6aSOM0uMxa4Kv2f6c31ufNAF7U\nrXYLzuxNAd7B+UW4KhHdlymXjHBgJ48acbGhQ7ayTaeZ/EOVmSIe9eA3MeF9\ndVsz\r\n=HNMk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF4tmoniMdunowUHvTIQoT3SMzw8IevO98hId5gYDOIeAiAWTxyxw9ezME+z5bxM56orq6nKxGq6VskHnmsSgEPkNw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.1.10_1637454766267_0.9413256950847306"}, "_hasShrinkwrap": false, "contributors": []}, "1.1.11": {"name": "lines-and-columns", "version": "1.1.11", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "readmeFilename": "README.md", "gitHead": "2add0e01a821878b3f9184c321d82d62cb9e397f", "_id": "lines-and-columns@1.1.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-UewVW4JbFN3c0XERx53POdcTx1/bnXGaAtR7gFLQxORgkpZP0VDg6Y5GddrOVx2abR1aG4cWf3x4DfA1WspMKA==", "shasum": "ca5695c70af5b6c3194fd87fa6e584dc2ddcf053", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.1.11.tgz", "fileCount": 5, "unpackedSize": 5387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmb1cCRA9TVsSAnZWagAAyZYQAIy8ZVU8LPQRHsnn0PQb\nGxr0Yg3Eb38pzIpvvNPmhxDQ2fc/TNvQDYYqVVSsjdtzK94usptCjg2ChnjL\n5Zsse0K7i53bmKiUxEmGR7PmBxRAHeWKfZmjUGAf3WpUa40f83Hi2KQwjVY6\nhbxpYSMb4V5Tda1/ilKzs8mWFcvAKl1PyHFOMnIoMs8WHFlCgPjK0sEh3n1W\nhm5ZOy/NxYp0iO/pvDaf7BPZu9X/ysP0asi0oBJ+fMtyQ5G3eYBcIIPO9oMC\ngkFQ4eTQUFOeCmR7+n+vmiN8ZjKiA2flhqhomfP95/zr6WcBcvCfmxwfPW8r\nTfJd+in+ZEPS56o1mdbn9GBh2zWIltJzyAU5wwfDAv0+LNlFrc/qVFjnBF89\nCrwyABy0QUxq9eFRIan+YY0GSAmx3lbOqMkdUppgPq21EOndG+/j/jtFM33a\n8g7nP7rIjMrOLFsP3/g6LFS++7xrbXdliV+7bfQpSGDW4ZZkdRhTaL+qQ0Lh\nD6N6vXBWCfShuPUsLr/6l1x/lTywFqIboy2fCMqAC8N0Z8BYGKRnf7bGoMEy\nHVZtbX09mk1rn2e0nwzkW1unlMHU926Jjyw0zrClbh3UJwIUKxrmuUjE187w\nur5+tCeCx8YPEDZPhEGAfHofQDR0Qmet+fwj2a29KGGExPu3mOQE71gT0sDv\nYLYB\r\n=t1G8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGIQzc/1R2Oxur6XFLJh9SH/GR0r1Jy05QL1UMkVTn8QIhAKNBoAv1GGSDNyw6ujkNJQWp2OcBwz9dCLNZzlUjgIT8"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.1.11_1637465435935_0.013558742045275629"}, "_hasShrinkwrap": false, "contributors": []}, "1.2.4": {"name": "lines-and-columns", "version": "1.2.4", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "readmeFilename": "README.md", "gitHead": "3389156275890966091dec7611105fa5d47eb964", "_id": "lines-and-columns@1.2.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "shasum": "eca284f75d2965079309dc0ad9255abb2ebc1632", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "fileCount": 5, "unpackedSize": 5386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmb17CRA9TVsSAnZWagAAguoQAIJBXpCvs7iskM9Oc5ch\nz0+lWCHNwncm+VIXaRJYZoLir4XVONiF+feLSEmZx87uR8QY41EP2eTHjZ+b\n0BnlUWNQFbuQmDb9QUBxgvOXM4tgedxNFWTeyG+cNaykKUJW6LqrQ6AxmLKE\n7UsSn7MYy8CxDTDSHZ9cdvS/C7ClPgFxp2mrD4o99jk70dS/rgLMsUwwwHPT\nn7TP7jDAEACOp2wiE+h7EBhO5D6bZE0pSf46Ov8nILJFJmJdZuMb6ZJSvljA\n4iualY5RlIMwM6E2Kt7HyDwk1EcKn5yK2zWehebS9hnh2jRIZ5eXWDpEE+n4\nQ3KI4au1NHmdwHvlfQiq2V3JslCgcJkHP4hn8ygM4TuqE7ZSMmXvcuS18crd\nxiRcmx6Eyhk6nCGobvoNf1mt1NfTQqwH/1TiQ/GD+/Ixxknx3paJ8fbd5ehy\nZ5KOfzN8eFjIhM+BBIhc22NgdbpEhWCrwc6ltSGw995J/lA2RzNXsifk5Cfd\n1my1QtO49qU50LcEl+c1Fx/FadMWTFiqknsoETljiTU/kqhFpjveX1rNZ2h0\nQSOCXhjIKMPnREvAGSaysBvMtdrnQQFHmUi9lXZaabuKTC/+xcmmwVzdlMN+\nREmd5gNIurK+syIquhrML7APVFDJSoGbW96FRrvZv3irKPg0yllhireo68mA\n9OQh\r\n=D3U5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDozT4lVm3UQbTEUHPrOZswpxaIfRxc0JXzz5K2t5UBAiA4xixyywcCsxdpVcdXg2mYmvqlM8YSUGjaQV+tVLbhaA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_1.2.4_1637465467723_0.5441094030662781"}, "_hasShrinkwrap": false, "contributors": []}, "2.0.3": {"name": "lines-and-columns", "version": "2.0.3", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs"}, "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --project tsconfig.build.json && mv build/index.js build/index.mjs && tsc --project tsconfig.build.json --module commonjs && mv build/index.js build/index.cjs", "build:watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "5febeb9674ded91908d3893581224f64111d95c3", "_id": "lines-and-columns@2.0.3", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-cNOjgCnLB+FnvWWtyRTzmB3POJ+cXxTA81LoW7u8JdmhfXzriropYwpjShnz1QLLWsQwY7nIxoDmcPTwphDK9w==", "shasum": "b2f0badedb556b747020ab8ea7f0373e22efac1b", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-2.0.3.tgz", "fileCount": 6, "unpackedSize": 7657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnFzFCRA9TVsSAnZWagAA+C4P/3KQhvKs5A1BkFWOP4tL\nZiccD2HrBpIny7DOL5rEPdtyeHe9aJIBwNwNAT9k9ISJexZYr95aatmL7KfJ\nh+E2niEz/uB58SBWg7GKeQGJVOPo025eVgbR/V39qVn9bGUBemlSkjIC5fhO\nNVK7xZ4OMmV8ShDNDheJ4fwO7Cy2Yu5lqFvRMMkfKDqhUtv4zWf0Dymc1TgI\nw2fqnraReaxZF6aA40ewTHLkLheSKSzdw58d/mdOdHGreCuKyyB4mPMCyQlo\nN3n3yzYLsqIssPG9xWPa5pzA0qZJMIkz8uWM83KpE0BUrr64z3RC2C/kSK7O\nY95si/A0EKOuGU3J4hcAf9SVAYyP4ZLeQBRx0eid7df7k0Sc5/A4/OnA1BNR\ndFRX4NVewwaVuUXbuSByzji3GuSCvsiHgq2E2O9qfH+RGIKFJA9jafNbckmP\nXEr8LHFiNa5WBiQ7fhxUqwLoFLwZX3PCzBAFPLg5Yb4O0LU1f14BbO/6e8Uj\n40s09GTWxM3QWSj1G9nazK4sF3CaIcVnD5O6F74pGikd3EDTizOrS5Ejyg6C\nEkLQWNHSH5Sz1MQWEcM2Z9LGF1JZBitNBMZM/fGeqpFkd8Wh732vVsL6hMz8\ncYjZWxQccitIUbe8CCdVh0UwlEUJsLxDkZGaTWo7YG3u8fKw5JLX6Dh2To64\ndYU1\r\n=EVIP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDRUQbmPoUvotF/PZFSjF5VNRhRGdmi4AYD2ZFc0TRaCAiABQcFOwUdZidPJGnTUWpiZvNyBhhy7vWh9AuK0Mu3jAg=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_2.0.3_1637637316867_0.07975102360943587"}, "_hasShrinkwrap": false, "contributors": []}, "2.0.4": {"name": "lines-and-columns", "version": "2.0.4", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs", "types": "./build/index.d.ts"}, "types": "./build/index.d.ts", "scripts": {"prebuild": "rm -rf build", "build": "tsc --project tsconfig.build.json && mv build/index.js build/index.mjs && tsc --project tsconfig.build.json --module commonjs && mv build/index.js build/index.cjs", "build:watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "gitHead": "eea2581b131685f2c21de777fd037c8ddd343354", "_id": "lines-and-columns@2.0.4", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-wM1+Z03eypVAVUCE7QdSqpVIvelbOakn1M0bPDoA4SGWPx3sNDVUiMo3L6To6WWGClB7VyXnhQ4Sn7gxiJbE6A==", "shasum": "d00318855905d2660d8c0822e3f5a4715855fc42", "tarball": "https://mirrors.cloud.tencent.com/npm/lines-and-columns/-/lines-and-columns-2.0.4.tgz", "fileCount": 6, "unpackedSize": 7692, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmt14XwDhVzhJ3E+zDPnDZl8Fza3nZVHKqdIqYJ56ygQIhAKJh/bqACgNw339ZduolqOvlwdilzKk69fYG8wyBHtuZ"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lines-and-columns_2.0.4_1699321313143_0.3882401338787851"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2023-11-07T01:41:53.488Z", "created": "2015-12-02T19:57:59.708Z", "1.0.0": "2015-12-02T19:57:59.708Z", "1.0.1": "2015-12-02T20:00:17.654Z", "1.0.2": "2015-12-05T19:32:05.157Z", "1.1.0": "2016-03-09T01:49:47.038Z", "1.1.1": "2016-03-09T01:56:07.519Z", "1.1.2": "2016-06-24T03:36:31.344Z", "1.1.3": "2016-06-24T03:52:38.515Z", "1.1.4": "2016-06-26T16:57:46.382Z", "1.1.5": "2016-06-26T21:54:01.493Z", "1.1.6": "2016-12-20T17:32:14.921Z", "1.1.7": "2021-11-20T22:15:14.474Z", "1.2.0": "2021-11-20T22:23:35.670Z", "2.0.0": "2021-11-20T22:27:07.947Z", "2.0.1": "2021-11-20T22:42:19.703Z", "1.1.8": "2021-11-20T23:09:10.780Z", "1.1.9": "2021-11-20T23:15:03.350Z", "1.2.1": "2021-11-20T23:17:19.625Z", "2.0.2": "2021-11-20T23:28:46.961Z", "1.2.2": "2021-11-21T00:28:55.542Z", "1.2.3": "2021-11-21T00:30:55.831Z", "1.1.10": "2021-11-21T00:32:46.542Z", "1.1.11": "2021-11-21T03:30:36.129Z", "1.2.4": "2021-11-21T03:31:07.871Z", "2.0.3": "2021-11-23T03:15:17.010Z", "2.0.4": "2023-11-07T01:41:53.302Z"}, "users": {}, "dist-tags": {"latest": "2.0.4", "v12x": "1.2.4", "v11x": "1.1.11"}, "_rev": "6384-a79a0e3d7a0bede5", "_id": "lines-and-columns", "readme": "# lines-and-columns\n\nMaps lines and columns to character offsets and back. This is useful for parsers\nand other text processors that deal in character ranges but process text with\nmeaningful lines and columns.\n\n## Install\n\n```\n$ npm install [--save] lines-and-columns\n```\n\n## Usage\n\n```js\nimport { LinesAndColumns } from 'lines-and-columns'\n\nconst lines = new LinesAndColumns(\n  `table {\n  border: 0\n}`\n)\n\nlines.locationForIndex(9)\n// { line: 1, column: 1 }\n\nlines.indexForLocation({ line: 1, column: 2 })\n// 10\n```\n\n## License\n\nMIT", "_attachments": {}}