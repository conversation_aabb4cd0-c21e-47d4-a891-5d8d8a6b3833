"""
日报专家Agent
专注于企业文档撰写，擅长简洁记录和展示工作进展。
"""
from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "daily_report_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "日报专家",
    "brief": "企业文档撰写专家，擅长工作进展记录",
    "category": "office",
    "avatar": "touxiang/882.png",
    "tags": ["日报", "工作总结", "文档", "记录"]
}

def create_agent():
    """创建日报专家Agent实例"""
    from .shared_memory import get_shared_memory, get_agent_storage
    
    # 使用全局共享的Memory实例
    shared_memory = get_shared_memory()
    
    # 获取Agent独立的存储实例
    agent_storage = get_agent_storage(AGENT_ID)
    
    return Agent(
        name="日报",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是企业文档撰写专家，擅长简洁记录和展示工作进展，快速提炼关键信息。",
        instructions="\n".join([
            "技能: 高效整理信息，提炼关键任务与成果，简洁表达复杂内容。",
            "目标: 提供简洁的工作日报模板，突出核心任务与成果，避免冗余。",
            "要求: 日报简明扼要，突出重点，格式统一规范。",
            "输出格式: 包括日期、姓名、部门、工作内容、问题及解决方案、明日计划等，内容清晰简洁。"
        ]),
        memory=shared_memory,  # 使用共享记忆
        enable_user_memories=True,
        enable_session_summaries=True,
        storage=agent_storage,  # 使用独立存储
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )