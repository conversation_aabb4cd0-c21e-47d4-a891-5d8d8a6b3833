{"name": "babel-helper-function-name", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.0": {"name": "babel-helper-function-name", "version": "6.0.0", "description": "## Usage", "dist": {"shasum": "20797ea3c878102ead1de6ea6e168ab488e44792", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.0.0.tgz", "integrity": "sha512-c47m4nMjQ5i8HAfPUMCxVnUM3yygeYymrCtp4gRjtcOo6Neo9rdF9BKzpG5bg8EaW+i8Pw9jc0RIHZbk0VMxBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfqogcTcq+k0F9px5/cKD6yeRpge0G58ntkrL0kUayTAIgaCaYRozsPXtGCV70xbwycfeYAWbFJIgJghFeNWJiaSE="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-types": "^6.0.0", "babel-traverse": "^6.0.0", "babel-helper-get-function-arity": "^6.0.0", "babel-template": "^6.0.0"}, "hasInstallScript": false}, "6.0.2": {"name": "babel-helper-function-name", "version": "6.0.2", "description": "## Usage", "dist": {"shasum": "898513f7c3e81f39d50cdea96bc211504c55b233", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.0.2.tgz", "integrity": "sha512-YPwVrjCq6iaOktZ3/G9NSAV6sRLWYppEDn7BRsM5CEJDb1SP+w4FXNfXseBAkppcsffevRnRSBp4VZOMCtiNFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaoDAzpHBgfQ5MjlLapy1Q61Wj2qfR9W3ztqGFyUtXOAIhAKAnIpmUlCJDX7RqhVd/0vRVSS2D1nuHwdJBD/T0Z+Fp"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.2", "babel-types": "^6.0.2", "babel-traverse": "^6.0.2", "babel-helper-get-function-arity": "^6.0.2", "babel-template": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-helper-function-name", "version": "6.0.14", "description": "## Usage", "dist": {"shasum": "cc058a6324b1c60bc491d27d4e7dbdb1aad61986", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.0.14.tgz", "integrity": "sha512-+YHr0oEjqGh6KLWipw2Tv6ilEFffynysR5qAqRNxSdEdnyYK2G/M1Mp/HoG325eDs/NTyz0StiJWfZBmccxK6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFEZl9pe+kF5/n7yi0FWTye5k0KEmgUtTtEwHG2ZjI3gIhAJtB6NJPF2Ku7J8+zK8QubV8OnG0AEJF/uy0cRsWm7Y6"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.0.14", "babel-traverse": "^6.0.14", "babel-helper-get-function-arity": "^6.0.14", "babel-template": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-helper-function-name", "version": "6.0.15", "description": "## Usage", "dist": {"shasum": "b2a7f56c632572b66bc9b3cd9ded08b27b7f71aa", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.0.15.tgz", "integrity": "sha512-Oia0vCPu8UJMH9jg8KlcFQ65CRODfYxC1uViOW6dliirdEd46pfdcPI8pzcobxwvp9UKz3+qBqI4kxbSu+nx9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkUwA08/fmQiHDjZrAwwiLDXIBRdtdGNL7e9PoD2odcAiAR6enMA4bRw1IWadR3HwCfMbcqfaVs8R6N+hKCVheacw=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.0.15", "babel-traverse": "^6.0.14", "babel-helper-get-function-arity": "^6.0.15", "babel-template": "^6.0.15"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-helper-function-name", "version": "6.1.5", "description": "## Usage", "dist": {"shasum": "7be411e8d4cb6dff0327da274827204e75f7b11c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.5.tgz", "integrity": "sha512-vL+OohPcrGPcDexVq2iFcyBWyCTg2kO+ACjWbs/nXuOISvCXB3VGR7+4ctQmotKZ8wkOpkR6Ff/2uzJ59fr/xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDk5DJ/gOKbS0sqF4bCvWi5PFyWC4UGmW/f0tohm8BoxQIhAKD/uX3Htpce7/bZwW1n7pTj6oJiUA9m73XmO90RZmpe"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.5", "babel-traverse": "^6.1.5", "babel-helper-get-function-arity": "^6.1.5", "babel-template": "^6.1.5"}, "hasInstallScript": false}, "6.1.6": {"name": "babel-helper-function-name", "version": "6.1.6", "description": "## Usage", "dist": {"shasum": "2073cf1d0ba66ba884f99223f477cb4321fe7bc0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.6.tgz", "integrity": "sha512-eiZZdbNv3/GiK30QSoM3TJ1hw06DU8IsDB7uW3V2wSt/UFObNuztEK50gZhwDa9IrkgKLn70SgfOxB2UhSBZUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/EFDqNDrnOX/IkuJ1M60+OInUio36mtrw1SPIjQ8HUAiAiM2kwpSpjOqxCVhCinjDu/eXzDrGipdu++dXkJB7JNw=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.6", "babel-traverse": "^6.1.6", "babel-helper-get-function-arity": "^6.1.6", "babel-template": "^6.1.6"}, "hasInstallScript": false}, "6.1.7": {"name": "babel-helper-function-name", "version": "6.1.7", "description": "## Usage", "dist": {"shasum": "2868bd09c5781e8f3429d6d063e4653e86cb773c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.7.tgz", "integrity": "sha512-+Loaw3vvxAbn47b5WYy/N1NkpdCEXaTUvIgPEgbS4CH3cqt7+vnlCjVmcB4d6YPGvGepGgPiFeFItedRy/5VFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlKtwtcKyhAzjFuLggnt2X1PzBS8G65tCP/RKNK11IUgIhAOxmtEyzTbjzqnV8T0z8j/cM77x1we233mjR5s/XAQ87"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.7", "babel-traverse": "^6.1.7", "babel-helper-get-function-arity": "^6.1.7", "babel-template": "^6.1.7"}, "hasInstallScript": false}, "6.1.8": {"name": "babel-helper-function-name", "version": "6.1.8", "description": "## Usage", "dist": {"shasum": "f39f651cb159b6779497dfe8f4f772466a931787", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.8.tgz", "integrity": "sha512-Wxg0GGTyjZHLqW5ESzx92VxSFz8Qwn6lJL4uG9HB5QnA0e4sdVVdeqR3/uKDYyqpbMynvXhzIHQrvHZ5BJpTUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICNTPiAwFWc5zyy/QQNw1Ru4NpijbJEt0QzsKjU7khwRAiBNhSCxhZwZi/2RqdCgwWD2TbXlv35qXSx4WC76KnwWjQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.8", "babel-traverse": "^6.1.8", "babel-helper-get-function-arity": "^6.1.8", "babel-template": "^6.1.8"}, "hasInstallScript": false}, "6.1.9": {"name": "babel-helper-function-name", "version": "6.1.9", "description": "## Usage", "dist": {"shasum": "339dbcd98d186fda9c8a25edae0300b4c58dbe71", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.9.tgz", "integrity": "sha512-C/fLdJ8NAM/FxLHaLqbDeQTyDHtL8Zdt+vjIKVB7fMPmwpH0/ET8Caf97b75WgaVR1yZWnl89Vc+kj/nRsyqFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAzwd11aggxzJ2ThHWPMmEFa03Crj3AKkZhRti9XwQs5AiBSJjqM+b+KouNVWyEd7pFdGOltD829NRXH9k3ypSqV6A=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.9", "babel-traverse": "^6.1.9", "babel-helper-get-function-arity": "^6.1.9", "babel-template": "^6.1.9"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-helper-function-name", "version": "6.1.10", "description": "## Usage", "dist": {"shasum": "85a73adda3a44780b4a85c62aae15a7f2415d9ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.10.tgz", "integrity": "sha512-dzF+lYdH4wXciVsZsLZScwbGzC08HRS0/7PxYiVg3qytTWCdCyGpwJQkZ33xDrVYug1opv1Jjha8khHkKmWnsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCv+hiBpqIkcyubUDSX4S0JDrE/ou2dmBIqshCRy5/2fAIhAOEfwmGXxTso6ZRg6Aaih7DJ8Sqm4t+Nd0j58LDx5URU"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.10", "babel-traverse": "^6.1.10", "babel-helper-get-function-arity": "^6.1.10", "babel-template": "^6.1.10"}, "hasInstallScript": false}, "6.1.12": {"name": "babel-helper-function-name", "version": "6.1.12", "description": "## Usage", "dist": {"shasum": "9db1b4d7b16698fec8a547171937bd7f144348cc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.12.tgz", "integrity": "sha512-ynFbkOynWk5zYOgzzLaA+ynsTffJaufsYrYBn3Jm7qJQkvBAYq4CWkWVtO5YJqrvjlSkq3e2Rs++qQtAbewRKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh1aLfvSoAGDCMhAb6/JQi0M6FvflCavmf1x+DY2+tIgIgOukusz6tWrQMjVkVQ+tBoTnmTmFY7mCWXeSPWKk8Cq8="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.12", "babel-traverse": "^6.1.12", "babel-helper-get-function-arity": "^6.1.12", "babel-template": "^6.1.12"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-helper-function-name", "version": "6.1.13", "description": "## Usage", "dist": {"shasum": "8ebf66b5c56d2979d12bb61b92429b7bd24bebdf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.13.tgz", "integrity": "sha512-XOxLIT+F0GsckFXr93dRmk4P5V287uepft0RIxFfDLJBY4wVo5gFVX8w9gl9J55suwF//yhteYuBOoaxPbVm1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfMXANyGIEtERuYJ2k6hu0Jeokoj8rIBoompt9TpLqiwIhANQSvdf+mC7CDtmzMkrMMfVy3pluUndIwObJeOMnv0sI"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.13", "babel-traverse": "^6.1.13", "babel-helper-get-function-arity": "^6.1.13", "babel-template": "^6.1.13"}, "hasInstallScript": false}, "6.1.16": {"name": "babel-helper-function-name", "version": "6.1.16", "description": "## Usage", "dist": {"shasum": "000175897ae808ab74b33581adae7e7fb85e4e39", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.16.tgz", "integrity": "sha512-SQLGXNDtRuEIgPz8eqOs6RcqHJZWrRIMn/ArqN8CFqivPLNaWRTbnSbHnFdS07ypCU2Ry5LS1twcprEg+/nxyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE9urW9Ly7gtGDXM8Q46lE+WZn0UOz+4+H5W4NerXG+uAiA5MGLsEZcrIa0jaa4mQAAPIkw/FHF00AaErMu2QK7T0Q=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.16", "babel-traverse": "^6.1.16", "babel-helper-get-function-arity": "^6.1.16", "babel-template": "^6.1.16"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-helper-function-name", "version": "6.1.17", "description": "## Usage", "dist": {"shasum": "68f1bb3af027817813a8fd15459aaf05cfc16b4e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.17.tgz", "integrity": "sha512-po/XT/iknUVSeJPXokfBvzOPH2H40IsKIW/T/Kl1vLWtH4k7qKV6WKRXAnYffSoO34NfUsNNNYld248ADUrANA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4dSjOChZ988hRbGtRTK2sQAzt4gi4xhx6hWJxxM0BoAiB0jXS7VM3n9gPLTdI/yUoqDdt5FxV4iKPw3WdvifrU8A=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.17", "babel-traverse": "^6.1.17", "babel-helper-get-function-arity": "^6.1.17", "babel-template": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-helper-function-name", "version": "6.1.18", "description": "## Usage", "dist": {"shasum": "179790b4530c0c6a4ed79a26733888ef864f800b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.1.18.tgz", "integrity": "sha512-l36CQAqteVkCHcV7YHNZrnsd5ujreJrWyl1XyuSz5HWvuuLfAr5RedC7UDFSK1UI8qzFhNGK5cIptZld3Dtjyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/5mY0Qh8jx0VpCmlr3SKx5EwjYGQKPV8Q6Ajn758BSwIgFjwga6/2ieSqd33cNyhWN7ZBU9X5cbiFL1lH3rPbIvk="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.1.18", "babel-traverse": "^6.1.18", "babel-helper-get-function-arity": "^6.1.18", "babel-template": "^6.1.18"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-helper-function-name", "version": "6.2.0", "description": "## Usage", "dist": {"shasum": "399eef5c85b72b2dafd4ba69125afabba073bfa3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.2.0.tgz", "integrity": "sha512-saYu1RymZEpy/6qVePMdRi4KKugWZ1Xy8aKFHtbpujB6a5nHh7m7JQB5stVYJOV/iZV5VqqIE1BAG+xHMgj2vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjdc4YN8J+fcChhaO/vOnMvvomMDDwzEK1zySt6nZOuQIhAJgMmWXDGgFgesp2GZwy/0NRIr820XXR5CQXnz+UVoTy"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.2.0", "babel-traverse": "^6.2.0", "babel-helper-get-function-arity": "^6.2.0", "babel-template": "^6.2.0"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-helper-function-name", "version": "6.2.4", "description": "## Usage", "dist": {"shasum": "803ef4aafb489899058ef6dd969ff2a93992bff4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.2.4.tgz", "integrity": "sha512-Zd3015pkRILdKY+vl+c1WM2s9GKF1X0dOhHjSltrp5kfeWh6eik5Wy4sB8Y+JlQHIA6pdkieemuOy6PR0c45iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDHD/4jE24Dl0Zvav2jcqIslN0E1fFgisryWcexnt++hAiA9Gz81GSn/WYkG8G7YF2Q5bbM+6+awdYZRl7eeImxItw=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.2.4", "babel-traverse": "^6.2.4", "babel-helper-get-function-arity": "^6.2.4", "babel-template": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-helper-function-name", "version": "6.3.13", "description": "## Usage", "dist": {"shasum": "483c2f9e5d4654206588c434352400597ba18d75", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.3.13.tgz", "integrity": "sha512-vUerDyd6OCNBnrC2qd+vUgIUI4n02lvEWb8pxiC5FPvo2AT2CZPRvUibIQYIxK3c9+zQdOrL3tzH0dRrxuH2JA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbi6PpdK5XUkTVxu6nlU2i537EgdM0C7xtjDKV1vegKAiBPNNtVldcwkqCmCzX23am4VcbCu/spiaE2T6YxX/FDYA=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.3.13", "babel-traverse": "^6.3.13", "babel-helper-get-function-arity": "^6.3.13", "babel-template": "^6.3.13"}, "hasInstallScript": false}, "6.3.15": {"name": "babel-helper-function-name", "version": "6.3.15", "description": "## Usage", "dist": {"shasum": "58334f4e299377bba4f84240caaf7bf9ac710eda", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.3.15.tgz", "integrity": "sha512-JpuMn68Inp+62NP0FzLoTy6+v2cAFopSKancLQEJ7gmy2gjZpQ9XI6ZaIWTsKNY2VYIf7PTXaWCXK1IDrEt3Kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2MTpeX7uJDWJSby+VhjuI692JGISuSdbKbnBe5vDmdAiEAl8CfUSTmMx21Lu6sEXQrIGG5RcxxmyGjMUC5zmAyMm4="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.3.13", "babel-traverse": "^6.3.15", "babel-helper-get-function-arity": "^6.3.13", "babel-template": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-helper-function-name", "version": "6.4.0", "description": "## Usage", "dist": {"shasum": "e13e1457f79469684d4fa553dd5c4301665efbbb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.4.0.tgz", "integrity": "sha512-t5YDMjheEY+GiZuD8a1yMBvjv5S9uTHKzaiYupBJghScc+P0+sRC6ApFyeayV60gVIl7VzPbtc6wfQUWXoyD+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAMgfdTmlFwO5PpXralhPPat2xAnoNGl6boQhbR9rWZtAiAl/c9D3ZLzcexX0JeUIeOk08EAO+KwFGJg7HbtuynIRQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.4.0", "babel-traverse": "^6.3.15", "babel-helper-get-function-arity": "^6.3.13", "babel-template": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-helper-function-name", "version": "6.5.0", "description": "## Usage", "dist": {"shasum": "749976077008e833149d7c7b0a278dc99be212d5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.5.0.tgz", "integrity": "sha512-4opF7upxy8HjCyllVusWfG5aio5rjf1p9Emzlz4+M1BU8dRtig/HSde65RX3e7JUedoGGkTpRonAalAAJ9wGvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDn/KNetWvWaudQX0oIMVtrWyvSrKWMRT7qBKu0gYXEEwIgDn3OqqcTeLYWFrpZh6j0WoY3sUKu9QepCd12/gW5y/0="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.4.0", "babel-traverse": "^6.3.15", "babel-helper-get-function-arity": "^6.3.13", "babel-template": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-helper-function-name", "version": "6.5.0-1", "description": "## Usage", "dist": {"shasum": "0e21c22c2ec713e6a657e2493573eae3d46d65d0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.5.0-1.tgz", "integrity": "sha512-gApvbEu5h24rGb1Yh0jDU2PIEoRV+P5G3aH86jJVfmABhKUl71D/F2Wv+Fkg2Rj0Khvs9CVU0hNiMF6scmvJPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFbU1SsyoPtBDoOvn2upc3l2+jXM7pMn3O/5dO6u266jAiBvEin3D1np5UVTy1jvBem+pCPHxYDz2744Jcb6eSROxQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.5.0-1", "babel-traverse": "^6.5.0-1", "babel-helper-get-function-arity": "^6.5.0-1", "babel-template": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-helper-function-name", "version": "6.6.0", "description": "## Usage", "dist": {"shasum": "1c2866b3cd089f71bbe5fac0bf2932002065bbbb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.6.0.tgz", "integrity": "sha512-UVoNS+yuULSHmuH8GPw00BAMaBCl4XF5uUbYKV6bJGKVvRn28L6933p5ywLP5L2Nh2yIIKbVRJoXxkeYNVBF2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHL1JnneuGJ6MUeFozuRI64Sp8qTfH/MFgREhe5d5gkAIhAMnSP8KOD0Pxl4EUUYKbGMt7gAAAPM7kWm99ERkXznLm"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-types": "^6.6.0", "babel-traverse": "^6.6.0", "babel-helper-get-function-arity": "^6.3.13", "babel-template": "^6.6.0"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-helper-function-name", "version": "6.8.0", "description": "## Usage", "dist": {"shasum": "a0336ba14526a075cdf502fc52d3fe84b12f7a34", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.8.0.tgz", "integrity": "sha512-mxoXL+GUSaqbyQ4/kRdzTVGx4x6FIcZUJGET6tOFrRxg153skUNRsJRzOA77xSFCeyCxeIFsApVuQWlqhLgQbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAxOdUlOeax14AxyiiEWBxBDIgfff7ISXwmlQA90gmkZAiAHB8/T+2k3muXhiGXeuWQyIdocoGSdAVvJ7QU7I9/rHw=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-types": "^6.8.0", "babel-traverse": "^6.8.0", "babel-helper-get-function-arity": "^6.8.0", "babel-template": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-helper-function-name", "version": "6.18.0", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "68ec71aeba1f3e28b2a6f0730190b754a9bf30e6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.18.0.tgz", "integrity": "sha512-hibwSh8POeHXw4GSzR52LX/oNvadDU5oKiPX3L4yQL/oATExlb8I6PuIeFey+Ns0aCznnUB4T9cHrOaFmBjcTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCy5jPNVSTEGDlFLYo4MFXeiIl7VQBzd1Bys7NTlu5wAQIgfZ2yRvWQUpPGFr1IMcQrYXEslpplF6hyKximOcZvUtw="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-types": "^6.18.0", "babel-traverse": "^6.18.0", "babel-helper-get-function-arity": "^6.18.0", "babel-template": "^6.8.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-helper-function-name", "version": "6.22.0", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "51f1bdc4bb89b15f57a9b249f33d742816dcbefc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.22.0.tgz", "integrity": "sha512-tHE9UdX0eAC2uXhLldTbTPl/Ov04GHMWDfb8b4KOem23qOrUSNgH6cdfXX5frv8vKadjP7hGfVyhE4cT01zORQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYIC/M36WZEdWLg/3cT57GU2T+9tzwhl5ZYfcOX0RPBAIgD4oAuJhPQ9WaL4YM4eDzTeWw0YbAKyJvQYgr5vpRfJw="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.22.0", "babel-types": "^6.22.0", "babel-traverse": "^6.22.0", "babel-helper-get-function-arity": "^6.22.0", "babel-template": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-helper-function-name", "version": "6.23.0", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "25742d67175c8903dbe4b6cb9d9e1fcb8dcf23a6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.23.0.tgz", "integrity": "sha512-eVDgoRGK7G1BeqxqpELT7M3gAoh/dIoZtuxvZfSxSAEIvg4n2YRHKxiZW7WKcKJrridNxVW1kDqhdbFH2teClA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWkZF0/NysgX0O+DsN4ELjLmqZ3t5UBrAEGURrPXdG+wIhANAgTMd67p2do6SngZ2EnDLDeCR41iv7raWds58ev5yO"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.22.0", "babel-types": "^6.23.0", "babel-traverse": "^6.23.0", "babel-helper-get-function-arity": "^6.22.0", "babel-template": "^6.23.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.1", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "06e552eec4af509b8680474d1bcf14d7b8a627f7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.1.tgz", "integrity": "sha512-Y+kJxvdF1TT6UNNoOb290nmwhZV09c6KaicC9PsgN5mrwQyb7Q8x0S8tCiikO8gGpLMmRkXS/zsozDU2Ihn4Ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpSS8berpGq2hs28Tf0uo72ewk9YkEvGOeKmBM+mMY6gIgH5uu+yhD7eVDtRqCDBCYKj+ovzhJFq5vlOuY9kR9ts0="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.1", "babel-traverse": "7.0.0-alpha.1", "babel-helper-get-function-arity": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.3", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "c20acaadfff6632b7c7efccab31d60d5e8e59814", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.3.tgz", "integrity": "sha512-7xDTCMQhXTOo2cMRxvHSv1H+yiPdqCyPzV/gztPGD6B/Ogi0pvuEA6eTHal7MUa+TXKhSpzGlv1Dmr7bwzxkaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9oNxEv9E94ZyCRr1r9Bg7N0kzX7A9rQ6CtPcjJ2Kh5QIgDlGsOuDEhOBen21vWIvSezMeYZL9rwxbFTYaOPyZhSk="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.3", "babel-traverse": "7.0.0-alpha.3", "babel-helper-get-function-arity": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.7", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "19aecddc5402f941c5726802993077b41ea9832d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.7.tgz", "integrity": "sha512-vHzusStMFuN9stZKMLHl9RojybS7AO6mmC9QIGuMzVL22j8amx/mSPCewNQE40RUYvRCJaufZBSZ1fapHXUtzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDe6DpBDQsgxkFZwbpopPtAFNBsVPMa/DiuEAiYGzXr6AIgLV00CP7yfSknA+VaR3a2aILWn9oh7YUa9bTRjN6cp40="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.7", "babel-traverse": "7.0.0-alpha.7", "babel-helper-get-function-arity": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-helper-function-name", "version": "6.24.1", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "d3475b8c03ed98242a25b48351ab18399d3580a9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz", "integrity": "sha512-Oo6+e2iX+o9eVvJ9Y5eKL5iryeRdsIkwRYheCuhYdVHsdEQysbc2z2QkqCLIYnNxkT5Ss3ggrHdXiDI7Dhrn4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEm144jlbCbMdxSCiZnCbwLaqU42yHUe3f85PRVJoPxpAiA13x52Wyj30ofaAcyfFpYeZEkW/h7hzZ4A9JJ+g+XbIQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1", "babel-traverse": "^6.24.1", "babel-helper-get-function-arity": "^6.24.1", "babel-template": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.8", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "b28e20f4c356713b7cf5ef5bc7b28ee2588c8fb1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.8.tgz", "integrity": "sha512-dqKluLGJnr+I7EUO+bXr3R8DRRquilBJyJeEseMvmcUYZ9GRUpXikAwq9ElkYVxtVwX20zCcAoQeIG1hhu0LYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzvZpLs6qK2DkATXJOwrrTEtecJr/blPhTH9xVZXMvfAiEA28VLCt/dJkuZjATkDWhHJf76CumlhKx9bTPHxvW4Qt4="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.7", "babel-traverse": "7.0.0-alpha.8", "babel-helper-get-function-arity": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.9", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "03d62dd9e68385085668367fce861a8a5b30a886", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.9.tgz", "integrity": "sha512-UzyZipEoMUTB71NmieB2VLfecnuLIpVB7Y0WZemGEra2eZ5+J/GveCeGElWG6Guw0/OhnGYAvfdt53cz7DHQPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB0tgLsgD/OgLMpLC+9gMgPbFk3PZtgvn+g/E1vBS728AiBc0PzeDbly/G1SvfKIW4UNCTFl4ouG22xy2IMybOY4CQ=="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.9", "babel-traverse": "7.0.0-alpha.9", "babel-helper-get-function-arity": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.10", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "0c3e955f65db9eddc96345db0de84547dff936d5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.10.tgz", "integrity": "sha512-H2opkJqyccVB+ruhmTRN+yYMnEO7/3YcUNNlkANFKFmakg7Dai3iwEOsc71PZpJWX7rP67NGGwAqYbiG6+0dgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGji5FE/kickYQTUEcBmEe2h6npOgykxLy4vbFJQ508CAiEA5T8vTEPeSYSRSWUXdPw3SXv4nh3ysIORz5kDuAmbmyc="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.10", "babel-traverse": "7.0.0-alpha.10", "babel-helper-get-function-arity": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.11", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-sKW62BfyBY2zSXrDVonBsbGeqAJLTICghdjUZuQ81NUc2Ygri5l4ZqC/inYNhgr+IJaFTvxCb2ycjRWJfqvIHw==", "shasum": "a0a157018348a7c3f3f8524c6128bb8341a092ea", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGTs/7jwT4Y2vfjjCq7+cwiumE7MLm6ufRxgfu/X+42UAiEAhgmSY25gF5x2iXstNLTO4ODAkQaKJkMuP5e1fwtDm3I="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.11", "babel-template": "7.0.0-alpha.11", "babel-traverse": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.12", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-Eoq0Wj1tjFARXUSxglwAG8LbBa+ddJ011V13K45xyFn2v6khOJG9hoVwEwlgv5oXKK4Cb8XVVVFd2daysyFqDQ==", "shasum": "69ad0d8df236cefa8824989148742ba757ea459b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOvHjvQQKYEdMFh8C2sGda35z+PkoOUO8WSDkXZchQiAIgXmgCDu0MoovnMnsoCbpofJsXvAahhkdgrJ7RsNMpvuU="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12", "babel-traverse": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.14", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "3a469fb9f31830a70ce163328ca4a662338ec50d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.14.tgz", "integrity": "sha512-mp0L367iXAf5ORZoYzy5DBLk3dAdLw1burCg0cMtZcitRDUhl/dg5dx2hhcW2JeSZuFCCgM10vw330pAeu6OmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKtiWn/AkH2CJ/7CB+aRebtSP1vVE1/pKC43dN4pF85AIhALgP9fEjzxuzOimnoZbmFwT3Iyh3dpi7ThZp7u+uftMW"}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14", "babel-traverse": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.15", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "087bb6bb6677acde36b3c19f6bc1afedb3d12e30", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.15.tgz", "integrity": "sha512-Et3sW6XQpaW4vl2hpEy+SeiNxUERMS/MErW1zNqbYKBQHXT5BK5s2lX20XcjNBxO4tfdgKD5qboXszvkaGoFzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICSE4vzcu7jYJaH54Kd640u9m0qzpDDhzj0NQpYkTo6AAiAytErkRbX/4iNnhTUAH4BZOb2+QXzyhNYacNcVEu7azA=="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15", "babel-traverse": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.16", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "a159510698c7200976b9af666f7456a6ef3e1e6f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.16.tgz", "integrity": "sha512-GOhZZLqa1FjBmnXt23IptQq5ZuhC2Ngf8BE2Q4YVBOIpT3Xl4UMFkhjrytHIIchfS3PcCcN8AUqcCwljLNZ5/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBWcDY+qdhVkOu3/k2RRk6138kw63j7m20ZnMhEuDTxnAiAuOCgh5hQsyYp+0x4bcSe3ogDVPlMzZjbP36yNzv/qkA=="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16", "babel-traverse": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.17", "description": "Helper function to change the property 'name' of every function", "dist": {"shasum": "b976290af579712345ecd6a146e6dff86b47b8ed", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.17.tgz", "integrity": "sha512-reNV8T5WhYNHaWJ4mEak9fkGvytDjutahSlIriVce3wv6Cbxa5/ZIOQXy1fFwKEuVbXuxkgX6yQJXZwgM6beow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8TmBM2bJUNalDxU5amqFBYWGUi7y6o3GObNGXjwv1eAIgLkMrQ2pvyaPWx+rQIaC3+hbmj2KfZYDDbjjt/kuHdRs="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17", "babel-traverse": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.18", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-81GpegdU6S8C44xI3xiGkas7tVxf6Yr/GQUX67WXTQ957NEOubEJ0geOqYFClrIOQhuKaYqcjIEpqiwPx4rafQ==", "shasum": "c53a5091c3a2fbb9b72b351be390fc616021b7e0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0sALK73zQiG9WzJhXFJ7NaD2nowoGsxav1ZWWR+vu2AiB8rzQrZ75kHdJ24AQ4hTj9BNfb08HVeElOlOQUSJH+dQ=="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18", "babel-traverse": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.19", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-RBrnSG/Y6fI1HuCc2t1q1bBOcWRK8vvRzzthjfBKhQBrDWJBa86r8EvbqTJUnYl2qOUtY9D8djdhTBL2uvit5Q==", "shasum": "6da459bdbc17fc2bb6a623fbcbfc57dc4ab20e9f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMtQacNs6BKyXfLsS25uQwbpsLooBIw+VFitm6tv7xwgIhAOo6+VW/6AizUmK0PTiajZizLIgUGnZRngjLfGq722Du"}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19", "babel-traverse": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-helper-function-name", "version": "7.0.0-alpha.20", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-otkWB0lg3QB4HMkSLddrJ7XkzDQSzGdmiRHh2GKYgqPkEqusT8ItheIF5hRkIFTJCmBBGBte2G/ITroakb7trw==", "shasum": "b560517ad8a6ca3029ac28bf558ef04bbfd8af85", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGwFm5ionDWNgJHzTOkS0wD4+bch7Peih8SrLvaJdlHGAiEAg6KA6QBvT4G4eGT9BjV5bDvBwwpnFQCuktCl7Rvu2M0="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20", "babel-traverse": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-helper-function-name", "version": "7.0.0-beta.0", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-DaQccFBBWBEzMdqbKmNXamY0m1yLHJGOdbbEsNoGdJrrU7wAF3wwowtDDPzF0ZT3SqJXPgZW/P2kgBX9moMuAA==", "shasum": "d1b6779b647e5c5c31ebeb05e13b998e4d352d56", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/vGizTj1/LuyoDGZl4LP2cNmqnuZWKqHhKeBiBX2bCAiEA9ZDA2BMdXQCuqmBGGLFx4Qszfln09jyaioHMBx3O1bc="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0", "babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-helper-function-name", "version": "7.0.0-beta.1", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-WpYIDh/uRxxdBj2RwRyEbK6Z0MG1pVe0p9zMKsO9K8mMKZbLpPsx10HKwu08YK1a+3x+ROn1Q2w+/qBlLOKy8Q==", "shasum": "80d85b2dfe41b8ed1bc0edd68f806cbec7b952aa", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJFm51lhM2xNfQmXcLdqY3tW8yrLhYfQdfgXMQaCRVaAIhAKwTfRrZvnxMJ6EElOsc58aDzLBOFBzWVytq1yZBC7JK"}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1", "babel-traverse": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-helper-function-name", "version": "7.0.0-beta.2", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-7jdswv5q6zYBRapDjfUSZrVeU4tM2rtUqGFon6w/2AhgXLzXgQPt1kjYnpIOnQo+UiHCtE8wIjgVXGGnMhRUqA==", "shasum": "f051ccee25525210e113738e46e1a122654a6bee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdIjlmUrrbM4yxzuPcF+a7zW221vfaqP9xXdiw1RAiwQIgIXpoGnnY0ly3EQLjzg/sOMcTGndJv8dNuSgbh60eCEE="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2", "babel-traverse": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-helper-function-name", "version": "7.0.0-beta.3", "description": "Helper function to change the property 'name' of every function", "dist": {"integrity": "sha512-iMWYqwDarQOVlEGcK1MfbtK9vrFGs5Z4UQsdASJUHdhBp918EM5kndwriiIbhUX8gr2B/CEV/udJkFTrHsjdMQ==", "shasum": "e86dd2eb2c09e06e392e79e203fc02427b24c871", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-function-name/-/babel-helper-function-name-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBthMNiyn/d/34lgdtVjrYQ/nRcQRBkv02OS4VB6lexmAiAdYV2nYw4kWZMB+NGNirSafrZg2Qacpvzv6N4SlIPfZw=="}]}, "directories": {}, "dependencies": {"babel-helper-get-function-arity": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3", "babel-traverse": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:00:01.175Z", "time": {"modified": "2022-06-13T04:00:01.175Z", "created": "2015-10-29T17:54:53.828Z", "6.0.0": "2015-10-29T17:54:53.828Z", "6.0.2": "2015-10-29T18:09:08.043Z", "6.0.14": "2015-10-30T23:33:02.385Z", "6.0.15": "2015-11-01T22:08:53.045Z", "6.1.5": "2015-11-12T06:50:30.796Z", "6.1.6": "2015-11-12T07:33:48.981Z", "6.1.7": "2015-11-12T07:38:13.089Z", "6.1.8": "2015-11-12T07:41:15.936Z", "6.1.9": "2015-11-12T07:47:04.681Z", "6.1.10": "2015-11-12T07:53:41.101Z", "6.1.12": "2015-11-12T08:48:48.967Z", "6.1.13": "2015-11-12T19:58:27.856Z", "6.1.16": "2015-11-12T21:34:08.109Z", "6.1.17": "2015-11-12T21:41:04.597Z", "6.1.18": "2015-11-12T21:47:21.250Z", "6.2.0": "2015-11-19T04:34:11.362Z", "6.2.4": "2015-11-25T03:13:05.221Z", "6.3.13": "2015-12-04T11:57:21.965Z", "6.3.15": "2015-12-06T16:31:52.882Z", "6.4.0": "2016-01-06T20:34:21.273Z", "6.5.0": "2016-02-07T00:06:59.918Z", "6.5.0-1": "2016-02-07T02:39:46.692Z", "6.6.0": "2016-02-29T21:12:32.435Z", "6.8.0": "2016-05-02T23:44:03.420Z", "6.18.0": "2016-10-24T21:18:48.877Z", "6.22.0": "2017-01-20T00:33:32.376Z", "6.23.0": "2017-02-14T01:14:25.628Z", "7.0.0-alpha.1": "2017-03-02T21:05:44.179Z", "7.0.0-alpha.3": "2017-03-23T19:49:43.928Z", "7.0.0-alpha.7": "2017-04-05T21:14:15.154Z", "6.24.1": "2017-04-07T15:19:30.256Z", "7.0.0-alpha.8": "2017-04-17T19:13:09.362Z", "7.0.0-alpha.9": "2017-04-18T14:42:18.035Z", "7.0.0-alpha.10": "2017-05-25T19:17:41.911Z", "7.0.0-alpha.11": "2017-05-31T20:43:52.663Z", "7.0.0-alpha.12": "2017-05-31T21:12:08.691Z", "7.0.0-alpha.14": "2017-07-12T02:54:20.602Z", "7.0.0-alpha.15": "2017-07-12T03:36:37.292Z", "7.0.0-alpha.16": "2017-07-25T21:18:30.165Z", "7.0.0-alpha.17": "2017-07-26T12:40:02.511Z", "7.0.0-alpha.18": "2017-08-03T22:21:32.528Z", "7.0.0-alpha.19": "2017-08-07T22:22:16.177Z", "7.0.0-alpha.20": "2017-08-30T19:04:38.257Z", "7.0.0-beta.0": "2017-09-12T03:03:03.282Z", "7.0.0-beta.1": "2017-09-19T20:24:49.219Z", "7.0.0-beta.2": "2017-09-26T15:16:02.824Z", "7.0.0-beta.3": "2017-10-15T13:12:29.074Z"}}