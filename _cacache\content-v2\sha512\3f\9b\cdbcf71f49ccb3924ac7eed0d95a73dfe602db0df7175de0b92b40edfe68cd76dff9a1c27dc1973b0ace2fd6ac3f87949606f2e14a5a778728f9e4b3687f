{"name": "html-minifier", "dist-tags": {"latest": "4.0.0"}, "versions": {"0.4.3": {"name": "html-minifier", "version": "0.4.3", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "193b65f5e1a901b74356e656aab24453494fb872", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.4.3.tgz", "integrity": "sha512-WF9M7k9HJ75Dh1DIJcyNkCraPm/BvTHU8Nm+63IpBUdynDW/hfnEb+ofxGbQmRCSvlVJyP8LwQ9/kWXNLGTquQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxnlD3Yc+qVfEmlwsFo2hYry2MnYitvAWzwwklimQ8AgIhAJwhDSv0s5BIvX5TJsk6erzjf+JGGfCw2syevDBe63SN"}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "dependencies": {}, "devDependencies": {}, "hasInstallScript": false}, "0.4.4": {"name": "html-minifier", "version": "0.4.4", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "9d76bb2328cbc1524b1c1bfcfb6bd694de5d18a8", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.4.4.tgz", "integrity": "sha512-ZXblNQI1cm1N+WpJZyyrUeNgdx+iy9YKKp8vRc2Cl7Ul9frOzV2oltljgxRpGKMYxQQTUZ9HDWqNMUR8I8InAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeu79G56x0p0dbF7SLjMCU3vrIMK8a5QAyZGureDu2bAiA97Ikkya1/V2/Ni4s2PCWdq2OHNz+frbgmLD+H7JLrGA=="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "dependencies": {}, "devDependencies": {}, "hasInstallScript": false}, "0.4.5": {"name": "html-minifier", "version": "0.4.5", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "763a1c806c0c18ee8117021bf137b18db056e786", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.4.5.tgz", "integrity": "sha512-tOPGFGeSixCalEQUvHnB0IHLFFtqZMSgDDPGAcRT3lmIQMtqMe1j2EMcY3aiod51C9KoxeyhLP/iYrx0YWbvJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGg2HtlyZiJDfk5gm1mIa2n6dECWeVCw0dsYEWUSkcKQAiEAofKqa0oNoWUTYIH4wnG+5Cke0+jua4zKyGX29aEg8Tg="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "dependencies": {}, "devDependencies": {}, "hasInstallScript": false}, "0.5.0": {"name": "html-minifier", "version": "0.5.0", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "69335cb085be54df4f382976329a931372fdd163", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.0.tgz", "integrity": "sha512-QLAtVPN/QGZvaVAf3FuiikZ7g3CF1Fsd+XG8wNA8yQJTVSOQO1+OVWn6Z/MEauD9LLUE5Bi2IQ1xIdsUWNVPcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZmJ7V0m0aG47KkRrmdW+5a6NrRAELu1Z8kf6YrersmAIgDQMDhTVnO0BshuAeqY2DFFZXSZvI6HzXOQ6Zrpg0zfc="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "devDependencies": {"qunit": "0.5.x", "jshint": "1.1.x", "uglify-js": "2.2.x"}, "hasInstallScript": false}, "0.5.1": {"name": "html-minifier", "version": "0.5.1", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "954877c13e9df7104addcf629e3f92e4889f0a5e", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.1.tgz", "integrity": "sha512-oHhfJo2jeBTB9DaBbW/xercDoZkB2HCdgRJUXh5XusS8pCkuT0YO28UyZHsXbN2aY57sc33fcYL8KKfs4VMfvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBnqt2mf4CI3yah22znV9Dkr5SXql6ODUZqg5Lttn8BDAiBcH/TjLx2wDzpgcqreGa99D7BQuemamwm0+iyFV/VXDQ=="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "devDependencies": {"qunit": "0.5.x", "jshint": "1.1.x", "uglify-js": "2.2.x"}, "hasInstallScript": false}, "0.5.2": {"name": "html-minifier", "version": "0.5.2", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "4e95cb29778292743b31262c7eeaac1dfb2e801b", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.2.tgz", "integrity": "sha512-NZ7w89d/ZyPECUn8vd4yfwQinslvcwRHQzwuK3bv9+p8O9Ss/mPle/iB8srWNep67x5AfdHHWw2oXUSTZG7ARA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOZ2QslnYNWtFtnWHs3dfjiy6Gv+c8/X13Lw8HaQWG3AIgXf4/pbR67pOdcH64bJLf7dx/O5/C73NglgGCqxM9H5M="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "devDependencies": {"qunit": "0.5.x", "jshint": "1.1.x", "uglify-js": "2.2.x"}, "hasInstallScript": false}, "0.5.4": {"name": "html-minifier", "version": "0.5.4", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "f84ecb10afe3d0918e6c371c43c51d42769173a2", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.4.tgz", "integrity": "sha512-L/zVgaml7IyVMNM4VVuXQNs2Pd4X6ZcWX6PgAF9VupfCFWj9mmudTQphjn+8RTcHh3RwjBhagAo9Akt4T+mfAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0vJhyBw3h23NWwydMjYtTiQbDgKkp/68mtW0gjVGkbAiAMyXoe0ImR/wuMDEl3GiDZVtZfW1XQ32F7AM/GMNuz+Q=="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "devDependencies": {"qunit": "0.5.x", "jshint": "1.1.x", "uglify-js": "2.2.x"}, "hasInstallScript": false}, "0.5.5": {"name": "html-minifier", "version": "0.5.5", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "b3f9be3fd1aa30ebf55c29c7e92bd3f4a5268867", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.5.tgz", "integrity": "sha512-/F08+Rjpy04TpfPxT9ubmFizFV7ZmK15HmuRl83TKNv7F5OiWUIR1jW2q73dIcEx/njICnyZ1R6fkQtcWosnQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuiK5uytp0zVbZQqA+y9fxctJ+7F+xbgOvGutDg8ekKQIgCMKzMA+WTJQu+9hARXrxMqhr5lOpzi9KARfkZypsF20="}]}, "engines": {"node": ">=0.4.8"}, "directories": {"src": "./src"}, "devDependencies": {"qunit": "0.5.x", "jshint": "1.1.x", "uglify-js": "2.2.x"}, "hasInstallScript": false}, "0.5.6": {"name": "html-minifier", "version": "0.5.6", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "0f15b437c27b5ce9aa84a44ca2850880e9257996", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.5.6.tgz", "integrity": "sha512-EyVdmQtnBXfgJOPhLYSJZKFAeCclumRjHYSXbWMSDzWPKjJRAxBAMkxAV6GUHWm3Fd2+mX2uHZL2MOVNffwJ/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF3PHWC4hLd1ZInxm0E1SGsaqHo/dKE9sjdJ+2WoZ46QIgBV5co2PZi0yqmr5IdwsTro6hdJKh2DHSrvutcWfiwDc="}]}, "engines": {"node": ">=0.8.0"}, "directories": {"test": "tests"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.3.x", "grunt-contrib-jshint": "0.8.x", "grunt-contrib-qunit": "0.4.x", "grunt-contrib-uglify": "0.3.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.4.x", "load-grunt-tasks": "0.3.x", "qunit": "0.5.x", "time-grunt": "0.2.x"}, "hasInstallScript": false}, "0.6.0": {"name": "html-minifier", "version": "0.6.0", "description": "HTML minifier with lint-like capabilities.", "dist": {"shasum": "6e6d22a35b5b1cf8fdae28417df230aa9d21ae72", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.0.tgz", "integrity": "sha512-UUQauEERY5FAxBRq1fMzvQcsNnYjNuO0r0/am1K297yM4kx6Prxn1VMhTPmjhbatKFKNPJALM7RPiFAtzr0RLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID6XfRGcOHUnA96kdI5Lx7xXdGW+d3ecvczxiv3kYMEYAiABmy4iLu5K4T5CdiS8fTg7jw6Voax8BrgMLnpkyyiTvw=="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"clean-css": "2.1.x", "uglify-js": "2.4.x"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.4.x", "grunt-contrib-uglify": "0.4.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.4.x", "load-grunt-tasks": "0.4.x", "qunit": "0.6.x", "time-grunt": "0.3.x"}, "hasInstallScript": false}, "0.6.1": {"name": "html-minifier", "version": "0.6.1", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "9a59e316eb9a354b8be1a8bc5a3fc5737420ddeb", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.1.tgz", "integrity": "sha512-GRGzs0CtuW3UWiK1DNvcs9JsK4QzfGtZWeR/sr54hASHaS2Z58YyYck+GqZ+vD/ms2dpJpHPyn/1Wty6euEhsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLsk64EKBZH5jqZFgsBebK6uOyvkOM/DDM4GEfAy02eAIgJdCuXeMUg4pfWPlEKPabYihmJQ22yOkR10/wfRXel/U="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.1.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.4.x", "grunt-contrib-uglify": "0.4.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.4.x", "load-grunt-tasks": "0.4.x", "qunit": "0.6.x", "time-grunt": "0.3.x"}, "hasInstallScript": false}, "0.6.2": {"name": "html-minifier", "version": "0.6.2", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "4cce9d940257b41f14bc31213f23bdc7a68d8cff", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.2.tgz", "integrity": "sha512-k0yGYuxTakXhos0MHaeAvqsEUlfYXvokgeC1crnylvP5tPI+9qq1596eeu8NgfzAiVsjVccklCUOxJmKfLvJKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyn1ldzmI+ej07TVWWn3WunzfZ3p50EXrqE9ndBIDWiQIgVZKIX406Cpp8u8UWR03z6U1dIFX6YuHbwZ8Vl0klWFY="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.1.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.4.x", "grunt-contrib-uglify": "0.4.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.4.x", "load-grunt-tasks": "0.4.x", "qunit": "0.6.x", "time-grunt": "0.3.x"}, "hasInstallScript": false}, "0.6.3": {"name": "html-minifier", "version": "0.6.3", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "11ab235b207128c39188c3ccad4509ddda0da6be", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.3.tgz", "integrity": "sha512-F6Yon+0sxaZIFsp3/ltQWafI1kEhcChiHoQGOFn/kwn8ltRTbl4ZnbijLSgqglynirbipoaMvw4B7Xz2EXm7xQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9mHB8d3OG/4mMFtTvKCnjf2pASk7h3oloYliaUjUhhQIgMUxUPl9KzvSY8eoW2qt17pZZN0RpEjDiO0EPl0a6QL4="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.6.x", "time-grunt": "0.3.x"}, "hasInstallScript": false}, "0.6.4": {"name": "html-minifier", "version": "0.6.4", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "a03a89c02c7b2b9c3522f2aaa470110795ca5bcc", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.4.tgz", "integrity": "sha512-0Y1VpsVGHsfX3k84pM24wJM8rgtu3+IaJyH4X0jL9/aUexTbKIya/ITsIV0c3x0ZfLTLUrt1FgVc7IJ1UjEDPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICA2+47Zm97DtP+oGrIYindtvUsdqyf/ZmxlduFTPFWEAiEAh1tsBOYLyZY1+MGhMqiIwycJDrQRNkUSOSd8DKGvxqs="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs-checker": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.6.x", "time-grunt": "0.3.x"}, "hasInstallScript": false}, "0.6.5": {"name": "html-minifier", "version": "0.6.5", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "e5dcad68d760b08aeed8b7da3e8cbc0bc37b002b", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.5.tgz", "integrity": "sha512-O5VopH4NG6jl/aArUMgkrGjfQVw4AvHKvOSX7ZNfRFtfMcM+ogQq4KR82pU5iJeyjE/bSZfLBA0FVrB+kxilWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqv+RhedAOKrzNpl3z5S4tYCoC1GtPwcLCz2mBUeyylQIhALCO/o9V4KyYqldums6KxTguTtAnFcFnlWzAr0hy5IIt"}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.4.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.6.x", "time-grunt": "0.3.x", "underscore": "1.6.x"}, "hasInstallScript": false}, "0.6.6": {"name": "html-minifier", "version": "0.6.6", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "231b2038a4de25a219f503540ca62eb9c8845905", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.6.tgz", "integrity": "sha512-/6C97vmI+M5SCfsW2cMD/JMje/xxk/91tA8+p47kxu7SEZwW9/EhdsASenkg9Ysc2a6Eq5vPG+aTPS5GvFzx0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkL+0PL0MKiwqh0Lz0r4FGK5bulxuSNKXd6uCXlfwyswIgZ87qBtj8m2JnUhBXNNpVidAtz+RdifYztM0I+MeHykc="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x"}, "devDependencies": {"cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.7.x", "time-grunt": "0.4.x", "underscore": "1.6.x"}, "hasInstallScript": false}, "0.6.7": {"name": "html-minifier", "version": "0.6.7", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "73282173983f5fd52cbf4977fbdb15aaec15514b", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.7.tgz", "integrity": "sha512-dqccXpsHEJS13S3EOBIhV70X+VigV3QaGavyGje5FVFrJcnlMceUO1vxCH1YundBzBPsj46YiWh1Je//xU6rSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmUx5AJeI404Tnb5ymfTxivNOx4W8dI8EMzv/7SJZMkwIgPLJ3OSZIyuFT60bIlE0U5rmbpB9Ic0hhNRHyGbnU5Qk="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.7.x", "time-grunt": "1.0.x"}, "hasInstallScript": false}, "0.6.8": {"name": "html-minifier", "version": "0.6.8", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "596e17e5faf1040c3c68b5d136d32d79efc9ed07", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.8.tgz", "integrity": "sha512-DQjCI4EyPid02ZWeWRbZK74/JT6PH9HsDN6QHPDFljUtKIoPulFxJNGnkTTfDbQZ6qEgPjOP0YqiLRpd0l8O4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtaUEtECMhXNk9MxvXoTIaN6mCXu1C4M6rw1Ac+mdiSAIhAOUrMP32m04Hb50tPhFqBKf3nn8ZK/ll4rGhBJUi/bTr"}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.5.x", "grunt-exec": "0.4.x", "grunt-jscs": "0.6.x", "load-grunt-tasks": "0.6.x", "qunit": "0.7.x", "time-grunt": "1.0.x"}, "hasInstallScript": false}, "0.6.9": {"name": "html-minifier", "version": "0.6.9", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "5105dc236f5e7e1a8ba651d4ab981386fc7abe53", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.6.9.tgz", "integrity": "sha512-bXyO1gBCn/mpnQvhWoM14r/1DRC9z7fkP40D5tP7QSd6JurLknkYSXqWX+DdCD6TQPApfYIplFLvoloGzQougQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBXRIaH+BdNX7WxgU2UyHFRhOW8GLcN7gwMSJ1sviBGoAiEAqaqZSjKHbbIHybevCyKIxQCcxiq6TqFNnpVRMSKtBWU="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.1.x", "clean-css": "2.2.x", "cli": "0.6.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.6.x", "grunt-exec": "0.4.x", "grunt-jscs": "0.7.x", "load-grunt-tasks": "0.6.x", "qunit": "0.7.x", "time-grunt": "1.0.x"}, "hasInstallScript": false}, "0.7.0": {"name": "html-minifier", "version": "0.7.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "39d22ced9209c5e008776edff6ae384f735d7cca", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.7.0.tgz", "integrity": "sha512-YuuJw/JVEcXX+JRtK4gYIC53YkUIi3+3nq4meqVtYUDDPat4Ud0A94eyzYBLAYYVwYOvFGKjqQSW3yWes3OaUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+stYXC9/yRZoQuGDSuyucP1LeAgsxjn+ZPja02xTBuwIhAOyuYhp0CL6NXQZ8UiwzqNaDB6kPM6sC4XtyCXpQuh1+"}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.2.x", "clean-css": "3.0.x", "cli": "0.6.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"chalk": "0.5.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.5.x", "grunt-contrib-uglify": "0.7.x", "grunt-exec": "0.4.x", "grunt-jscs": "1.2.x", "load-grunt-tasks": "3.1.x", "qunit": "0.7.x", "time-grunt": "1.0.x"}, "hasInstallScript": false}, "0.7.1": {"name": "html-minifier", "version": "0.7.1", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "056f8f9cc246e6233b895a6e0ba40747dd1b0c52", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.7.1.tgz", "integrity": "sha512-RKhFFOeS28i6OhT/gxj0/gKy0chlsSqq5nptIQuBjD5Z4/Ag8O9TiN+R34xIC+/8z200Cq2SnImWxvfJKe/PfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaUw8cCGj2e/kugHipT0ECSElSZly4gLUO1fQ5knR9UQIgSlk1iw27bZQ1SGRvbBwntrJLk5GNTIBJ6rFJr5ztp84="}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.1.x", "cli": "0.6.x", "concat-stream": "1.4.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"chalk": "1.0.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.8.x", "grunt-exec": "0.4.x", "grunt-jscs": "1.6.x", "load-grunt-tasks": "3.1.x", "qunit": "0.7.x", "time-grunt": "1.1.x"}, "hasInstallScript": false}, "0.7.2": {"name": "html-minifier", "version": "0.7.2", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "2b7959b1051a481e71cd7c6e59a64272af895cfd", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.7.2.tgz", "integrity": "sha512-cHehMFjY/IdGN7k0w94ppDO+I1rvDQ1hI2ve6uok8ZFU9lOy/TZvQrF5LxV0eAANCGZ4krYSCmZGDVr67YJ6aw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZhWWffwvWwlc+gNB+NnGM4tKpfI8uANE/yzilUNv3eQIhAIzH98dl0WWgRnXgVS3E0J5MWCdcAym68wgPmNd+WAgm"}]}, "engines": {"node": ">=0.10.0"}, "directories": {"test": "tests"}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.1.x", "cli": "0.6.x", "concat-stream": "1.4.x", "uglify-js": "2.4.x", "relateurl": "0.2.x"}, "devDependencies": {"chalk": "1.0.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.8.x", "grunt-exec": "0.4.x", "grunt-jscs": "1.6.x", "load-grunt-tasks": "3.1.x", "qunit": "0.7.x", "time-grunt": "1.1.x"}, "hasInstallScript": false}, "0.8.0": {"name": "html-minifier", "version": "0.8.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "12ccbbaffc3d1674f707816ce4825d4b892281ba", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-0.8.0.tgz", "integrity": "sha512-QiT7LjLePqbK2VYi0N38oCBWqL/Aro+1nPVs0kyxDfASkOxMECpK7BqTRHbxfMFmJbsD3AogC7iAzMK3yA2h7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDhyyJFKVYwVJYxuyXSqLRP5vVuspa/SeEiXIOn4Dz1QIhAKjDVxOvq2reibPSJb3ZLWTxoshpiUPBrr7cLfO6vpyp"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.10.x", "concat-stream": "1.5.x", "relateurl": "0.2.x", "uglify-js": "2.4.x"}, "devDependencies": {"chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.9.x", "grunt-exec": "0.4.x", "grunt-jscs": "2.1.x", "load-grunt-tasks": "3.2.x", "qunit": "0.7.x", "time-grunt": "1.2.x"}, "hasInstallScript": false}, "1.0.0": {"name": "html-minifier", "version": "1.0.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "8b101086f352f6b1b5b1a7929a803e49c2d2aba9", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.0.0.tgz", "integrity": "sha512-d6IzOXypjFJ3xejB2/99slL0XHYpD+bYIxNLu3XsuLTkzWLGZHAIveDEf2idNEBhxSM9drMIRa9+kdF5i2+8OQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCL5vLOgY0oONufziG9WsY+ZUkq5gtssR2r5M9rrv0wQwIgIj7jEXl+Up/3dU+wEIZ+3ICoxTrx3aJF1mgXP9fxjjk="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "relateurl": "0.2.x", "uglify-js": "2.5.x"}, "devDependencies": {"chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.9.x", "grunt-exec": "0.4.x", "grunt-jscs": "2.1.x", "load-grunt-tasks": "3.3.x", "qunit": "0.7.x", "time-grunt": "1.2.x"}, "hasInstallScript": false}, "1.0.1": {"name": "html-minifier", "version": "1.0.1", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "3545b936b0c910cee88c18ec90852cff02f84b8f", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.0.1.tgz", "integrity": "sha512-ba3NazuCGwEvX7WUavltR+4SvUBHPQJRHLxn2fPkp6pD8LXVHDDapQCYxp3+gyW76jrqlNUPMxGUSTxs9VmpPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHR2hf4PtGONkX9cyUL30nIlX3Zw2JdWQrub1kcYk9BkAiBEwkX3ULXl0OPbYq1yX21ipRI5jcZDwpP8xkgnn+BXKA=="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.11.x", "grunt-exec": "0.4.x", "grunt-jscs": "2.5.x", "load-grunt-tasks": "3.4.x", "qunit": "0.7.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.1.1": {"name": "html-minifier", "version": "1.1.1", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "6850f99f9244a6541ad57d2327a204c9567b698f", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.1.1.tgz", "integrity": "sha512-MQrOBhF0e777EunXixotr1dgg6EZhNnwYhw2MoPtWm4I9INTJMmLlJasMrxND+T8ytF2r+ItYlBCJpauO2dq4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICdpuraXzpy7nvE6Irw5s4diw8pEC7Txi84vhMaUj48mAiEAzKZrjO6JVpHJ4ZbZaSwkGR8hq5uh5tbxF0gTiPZ4DR0="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "0.11.x", "grunt-contrib-qunit": "0.7.x", "grunt-contrib-uglify": "0.11.x", "grunt-exec": "0.4.x", "grunt-jscs": "2.5.x", "load-grunt-tasks": "3.4.x", "qunit": "0.7.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.2.0": {"name": "html-minifier", "version": "1.2.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "0891d9dd0e5c75762e06fdcf209c8038af1bddab", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.2.0.tgz", "integrity": "sha512-o9Wx0jDSHAToEwpsM4k+Yr3/no3eZcLjAJsgOhRLPRlXfNi7moUPRO7d62czHB66LCTG3KlL/i1Z3qVSyNpCWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3I8mej64/PwZblXM8JEKRVV0r/+JHJChE9lvTvtBTTAiEAv+3hv1EtQvvAl1oxVfqdyWXO+LhzqQHHs9ykBcKLyqM="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"brotli": "1.1.x", "chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-contrib-concat": "0.5.x", "grunt-contrib-jshint": "1.0.x", "grunt-contrib-qunit": "1.0.x", "grunt-contrib-uglify": "0.11.x", "grunt-exec": "0.4.x", "grunt-jscs": "2.7.x", "load-grunt-tasks": "3.4.x", "lzma": "2.2.x", "minimize": "1.8.x", "progress": "1.1.x", "qunit": "0.9.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.3.0": {"name": "html-minifier", "version": "1.3.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "bc6286710682ff4666d8271cf02c63a06103d0ec", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.3.0.tgz", "integrity": "sha512-tZwuEaAyH+0n8T2Mrq0LHjDUOq6xULyp/B+/rH2xwtlNqqxHvp8ffU6lZ6If44rXFKe2JOzNqBrDZHy6D6d0Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICnctwo17oLDXvrLYPFwPDgCfEHgCgqG3KKabE2wIDo1AiBFBOQfpHFbjlWULPwRR4NZ+bqdydwdhvzlhCyKZvMVHg=="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"browserify": "13.0.x", "brotli": "1.1.x", "chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-cli": "0.1.x", "grunt-contrib-concat": "1.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.0.x", "grunt-exec": "0.4.x", "grunt-lib-phantomjs": "1.0.x", "load-grunt-tasks": "3.4.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x", "qunit": "0.9.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.3.1": {"name": "html-minifier", "version": "1.3.1", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "181771e9a190c8bf898b2cc5581e030e639402d7", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.3.1.tgz", "integrity": "sha512-mJrUlCETafCBSUddOlRvK+bfoSdmuo4Qqh4LSwefmEGctf1PnOGbO7xNhOCwrnARfVDFqNEXwF8rsvm6u2G3Ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzp5Vyhi0qZflT+CmvNafLZ0bfGQDIAIRXFFQGrpcYtQIhANpP02yM4wEeBYSZjXYQ5DIIcNpwi1CHQ6tUA2uSG9le"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"browserify": "13.0.x", "brotli": "1.1.x", "chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-cli": "1.1.x", "grunt-contrib-concat": "1.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.0.x", "grunt-exec": "0.4.x", "grunt-lib-phantomjs": "1.0.x", "load-grunt-tasks": "3.4.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x", "qunit": "0.9.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.4.0": {"name": "html-minifier", "version": "1.4.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "5f85bc13935ebcacc5ccaedcb13959dc05755f14", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.4.0.tgz", "integrity": "sha512-Fw0xkRln4LEvO2RoCjUC3XZ2hyRBxZJMXcjRxu5ZkpLEjZefA8YC2+1tzGZyKqmXSyAxShaLznXH/4T0ljtnuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChXK3r2HneGvaXzsXMFHYhbjVS3UTuvABCKffKvcZalwIgRDKDXCIEcLdeSyLs5S0akWv4DPNe3JcfXs852bwb1Bg="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "cli": "0.11.x", "concat-stream": "1.5.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"brotli": "1.2.x", "chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "0.4.x", "grunt-browserify": "5.0.x", "grunt-cli": "1.1.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.0.x", "grunt-exec": "0.4.x", "grunt-lib-phantomjs": "1.0.x", "load-grunt-tasks": "3.4.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x", "qunit": "0.9.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "1.5.0": {"name": "html-minifier", "version": "1.5.0", "description": "HTML minifier with lint-like capabilities.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "beb05fd9cc340945865c10f40aedf469af4b1534", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-1.5.0.tgz", "integrity": "sha512-PbQhdswLYvb+Co5I24yEEsVeVYykYTz4PBXmZqbUdO7eTa+gfAlafYcUfVqX+bw/slxJHPHIpBfPrQ17xoPTPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6RDsRsMf11LZfTu7EmpbDkYa6HkfaJKl5qkuWSOfx0AiEA0uLzs9CwfPkt+GHdhKIwPLN422cPaqdZGuk/CuqZQ98="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "concat-stream": "1.5.x", "he": "1.0.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"brotli": "1.2.x", "chalk": "1.1.x", "cli-table": "0.3.x", "grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.0.x", "grunt-lib-phantomjs": "1.0.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x", "qunit": "0.9.x", "time-grunt": "1.3.x"}, "hasInstallScript": false}, "2.0.0": {"name": "html-minifier", "version": "2.0.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "292c0dbe5fb2cca16497f6514d62b29e6404e607", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.0.0.tgz", "integrity": "sha512-yWKhaNCVO2y+F+bPUN4DV4U3p/dZl74g/YJT/qs4WLJ3HHas19Nai+Ng4evgo9CwlbQLc+SC/r4rHgV8XsdR8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG/C85n7CvdcsbpZHfYTM+xyl3ajjsF80hfqg6+JvKboAiEAlLOGFLl4m+afDjKt1bXwug92gifb0wKeO2vy1/VAIQg="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "concat-stream": "1.5.x", "he": "1.0.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x", "brotli": "1.2.x", "chalk": "1.1.x", "cli-table": "0.3.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "optionalDependencies": {"brotli": "1.2.x", "chalk": "1.1.x", "cli-table": "0.3.x", "lzma": "2.3.x", "minimize": "1.8.x", "progress": "1.1.x"}, "hasInstallScript": false}, "2.1.0": {"name": "html-minifier", "version": "2.1.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "cb3dd866af40f7ea603c648871e7c6be9b77d88e", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.0.tgz", "integrity": "sha512-sT7kyl0LRtfTgG5YwqrgQ8ahrBYugSrZc1OGK7msuqNbA1CiY0YumScxMnGs9OI/32oXSaCDKdapCurdbfMv/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcUaHi30HRJngjNzaEzV1KdmDijb62xbzGUJC6jpzexwIhANH1rob4Uiy1p7mfy36YYQAHeg0/7GjcWkW8cbC0oXVQ"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "concat-stream": "1.5.x", "he": "1.0.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "hasInstallScript": false}, "2.1.1": {"name": "html-minifier", "version": "2.1.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "ff308a2b3d156ffdea16de3cbb94ce4116e68c7f", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.1.tgz", "integrity": "sha512-h74MU81a/UmfYr8lzGZ7Gusq6j10WD7zhlW4SwmlgQRsw2fmpBMcyFh2DI0WkmAF5ZBVSaZJku+Y9EU8B9Mpow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4YwfujjbKSfGtG2r0bt0btEBgYoTYwEwTK1ZWWUwRUwIhAOlV/5cqzBzqwyPWyb1ytoKSAc/o/7VnN2XIhn+3BFX8"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.0.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "hasInstallScript": false}, "2.1.2": {"name": "html-minifier", "version": "2.1.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "07b1437b6663dd6cf3e9964e88947df5bc1c2fc5", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.2.tgz", "integrity": "sha512-1tWySfhC2JKarsUwtOl1l6EgGsmED9nUWWYHL7GInViqM70tP0Vbtx6ChTfM0bR9RevlfHHoY0M2UQQ9LG6gvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICRcswmYToXSuZS4J6j34LBC+tu962kbXVOyGPV6qcdnAiAS+9j2hjJwsBodoMcSuBYUNicc4A5R28f377pnXQnA7Q=="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.0.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "hasInstallScript": false}, "2.1.3": {"name": "html-minifier", "version": "2.1.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "94b1fafdc75cf9efec49290c58969178eb494627", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.3.tgz", "integrity": "sha512-GLKP1N7YCQZqg4dl22ftY2ebxGQJd6lu/FZDYHATSwwiFAYw/wl85IdRUSEm9+lwvMscBJd42Q5gHgrwXTK7Aw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwe07dnRwPSdaS1D7T5ogtb3n7eNsTuEAhNo8tzMgTXQIhAIfMRJDFP4/S5V3xE2tACllAYQC327AXi9djCPWog00B"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "2.3.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "hasInstallScript": false}, "2.1.4": {"name": "html-minifier", "version": "2.1.4", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "a6ea039c05783fa1e7afdf0d3ad83fe0c0771f86", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.4.tgz", "integrity": "sha512-+fRe6D2lr4PE3/rFo1Q4X5W+ylz1tt1Q7tHqb89dJ45psEqtHU29GFia+1FsqZIrCLcwHHn0l8KCMeNFhkUbbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDedaeNWPCjRnUZhBA821MSIEyOvvGHcdJidTFluFdgEwIgJWDZeyF034DLXM7NZ/KEpIaGomz2sCv3vOBX+e84Rgs="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "1.x"}, "hasInstallScript": false}, "2.1.5": {"name": "html-minifier", "version": "2.1.5", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "ebba8d5b99108043c7c3f2d8b2a410122e7eabcb", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.5.tgz", "integrity": "sha512-gHWOyACTtw7mfS9nXZKjaw3sq4LSDPDh3IeBbNX0ydRMx/Zexg7tfSqCGSD6pxLTlpxgp+nARO02l7Ec2BJDrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnSt4AAQSTgwERU5O4k0auvDW1ewlziiG3EaQSBrTjVwIhAM31yG7eZpdMn62z+KdyOWNtyh5wq2IYVY5/H85RV91+"}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "2.1.6": {"name": "html-minifier", "version": "2.1.6", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "6236d794099e2cf97c02e7cb5b456184131b4367", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.6.tgz", "integrity": "sha512-ywb0yOzMMj298RukDLgl7XUvHQmBXrAhlbcbSpeKapm8XCDceuifKu/AMqqXJDjbhSKP1ctm/U1PgD/WYuC1fA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD34Wy8TpOCycDunJeSBlGQUS3oqG/BEO0j7dWJTZ8+AIgMRPCeCNwRm2un6nB6n6ZW2OcKGmDjookWjDmrLMkRhc="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "18.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "2.1.7": {"name": "html-minifier", "version": "2.1.7", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "9051d6fcbbcf214ed307e1ad74f432bb9ad655cc", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-2.1.7.tgz", "integrity": "sha512-HDb93Rn0fdb/DS0DbTDapR9LlK8zrSccJwukR5Mt+adCd6+ocTpymknnUiBo3JTQ3nXLC3qPhA5diIGdO1CF4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHYAB3PfsvzFGELEpy9kNGQb3YGBwG1N5HkHu8iV5qyAiAq+a0XzAGzt1ff5uUy59nL8WONL7PiU+UAlafOoTd7XQ=="}]}, "engines": {"node": ">=0.10.0"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.6.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "19.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.0.0": {"name": "html-minifier", "version": "3.0.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "7c973655fa3a0604dcb95f4eb002e16857d654c4", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.0.0.tgz", "integrity": "sha512-aczwLmNSD3uBmb4+5DGgCdQqpVWRfjvgvAOLn4CftIzYurc+ZzpRW+sdxz9PDashE23VRivuLmVCLKA4RAQ9CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICabDr3rP69jolscpuPJesh9vFOTYsR5txZRSaJEnvFBAiApwEl63+aqoOl0vJeKaEfhrvDNuCHfZPiUv5cxkkGQmQ=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "19.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.0.1": {"name": "html-minifier", "version": "3.0.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "d694580afb10dade448a1cd96683a1adb1f08bc9", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.0.1.tgz", "integrity": "sha512-+oY9TlsP3FO1MjDDB+NQSLSDMC5YOtP6U7/6zGPXSpWZKZwBkxQLu7fED7zOHQHnMZEnkmLxlb8tG6pmf/yx3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWZX5Hswr/SiVpg0Ku9+BY0cFsoL7JoXAhRBbHEYLzkwIgHBfr8wiFH8PmmymMgXGswQI1U6knzI29jEc0An3co4M="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "1.0.x", "grunt-eslint": "19.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.0.2": {"name": "html-minifier", "version": "3.0.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "4e5dc28ccd3b2cba2a3bbd20d572af83f48faaeb", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.0.2.tgz", "integrity": "sha512-gNJKa6EzPmgig1UZIdcdP8G+Qv9JxdmFB/qITQ+88uGGbR7ennclPBiJtsHyOD7dsKAc+oJ+XpvMEPhZnxXplg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE7DhVbmqF+etflIfkiuUtWQNCQ8r9HWGNxxHttttqjHAiEA31nDhcHY0YjIkeIjXnjJgHgFCE3i00GVlGpn4j+6F3s="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "grunt-eslint": "19.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.0.3": {"name": "html-minifier", "version": "3.0.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "21bb5463519b710a3b0a0e8e50a2ec0b2b690e4c", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.0.3.tgz", "integrity": "sha512-RL<PERSON>zutpO4Mv8ToW2b/CzZQ94IF3Cb6xYGbT+54k8BxQCQuntxdJr99/PkEnLDf92zakibKb5fKHJMSF6t3O/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFPK+u2px9R5Qk3qY2HoyxCwkNFELFvuWxzEeGqgivLoAiADGbtxYLcNE7wAis2CN4oRUL3c0buQd4Xuel1gDF/7rg=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.1.0": {"name": "html-minifier", "version": "3.1.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "e0b35d7a1fff89ae989e2fa51c572bc0d1f2d1f6", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.1.0.tgz", "integrity": "sha512-oagtb2hsaRmhb2UpuAEi76O/TgjxNyn4TVzLTIlNgBxlct4OOMWhCqfGI6Uvnus8kDyuUCiKqpklLN8nnT2UpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfIfG7KYuXNi0j0VuqlFwYKU4BY7uu+yFCKCdiKgY3dAIgS4AM4DBQhWLF2QrxYiyENCdSPp/FaQLGvc2Y9Sggp6g="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.1.1": {"name": "html-minifier", "version": "3.1.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "73bd0600fc9d68f536b13a788360245b6d76eb9c", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.1.1.tgz", "integrity": "sha512-Gxzf2Y7TIzzRebtr78bxYuxaeIJYvuw3t7B0Zsf/rRVrM50lL9ilZbefCD+HmAB2UBxXfoRIqBFGA9FloJvSQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiQcHLRSd48SpEKMZ+Q3hemoIdxk/UzTZfopCA8XzSqAIgRyzloShSBzyrq+bNMIfsLIQaRMoIUAgAiaR2vuj8JC8="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"change-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.2.2": {"name": "html-minifier", "version": "3.2.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "30d9fc4f22967bf056c68851e42a787bd45089a5", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.2.2.tgz", "integrity": "sha512-h813Xt7nBVjfv/kIKuWY1DCRSkhzQgVG0Cw7jSO+9gT7dR73ij7SCXwkuyzt/m5fiESdIZtMpqzdnj7ThyZb7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+wcTBCK3PQLihpRJdLyfXkG9e8+Zc/YpuergD0y+R0AIgAr9PCyE8W1YRxc9O272O3S87zGOL3CLUocuJS0g8xTo="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.2.3": {"name": "html-minifier", "version": "3.2.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "d2ff536e24d95726c332493d8f77d84dbed85372", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.2.3.tgz", "integrity": "sha512-7wLdcsg/HtUxBESmH40rEABbjJ2xOCjEhazwvDufs4Sh5UulzPkO3GZzNQCctzxbcaDuCQEYckIWzsDe3mtBOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwaLRATyKq4DYE/cZXGbyTXDo4IOejKXUjwAsERf4BugIhAP3FlduQmOwAkGCFmvleLRpBsGtirNVxy56L/16QHx10"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "3.4.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.3.0": {"name": "html-minifier", "version": "3.3.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "a9b5b8eda501362d4c5699db02a8dc72013d1fab", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.3.0.tgz", "integrity": "sha512-Hd5k4B5nIxd4alLrm6JSApJWq1z3uI8eixnIsiosTowLNLF4AZdXH9rnHMpzbpeHQKi/HPwTc4mF5MBITOEDcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm1Q+IqBEebW7GQ3ss9k3qqDcmn/EwCwtySDijwqlGpAIhALk2mtIYsXfF57og33oz5lCmA0GhUMZW5lHpjPNDxjvR"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.3.1": {"name": "html-minifier", "version": "3.3.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "dd38e60571537bf34a8171889c64fce73c45edad", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.3.1.tgz", "integrity": "sha512-DYTaRXVBoNM6MiL433UOrU+zeetxv2i1PXarN9NF868MbL+dLPQion/ag4NH1nlJF0ViYDTfoz0ayNA1RlDKZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHcZGdgur33Fxfeh2Y0pmwezz6W2N/f802Mx9Pdgo2AwAiEAxbU/knMhyRnXjdr7K02epsXALy8vJZunqEjOu4lYf9U="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.3.2": {"name": "html-minifier", "version": "3.3.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "7259c2028d058dd90783745b3e4b246243a98d35", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.3.2.tgz", "integrity": "sha512-njNDRbIwpt2/+CxzbUn3v00Utg+Yd98UsSnpPlZQIY9lfODf2w+/EETrhWJyuvbloMJpIPh8m9vG4WZ9lrVVsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFX9qk/Z1lkgX5plOz9iVhxkeq4aQGS/vlcggk2N+sPKAiEA1xN21blPKiYyD4PGzGNojPCdGoNgqs+uXdLl8egNJWY="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.1.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.3.3": {"name": "html-minifier", "version": "3.3.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "5e85516b2aff3c3fb9bda351879375868386d6f6", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.3.3.tgz", "integrity": "sha512-6agHe0rQd162crTL/imZ0ZoyfLDEv2zkuAYn55nG54PQuZdq1BrlebodWWDFOlw6bDicr11dLrLdxF4hAq6DTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBISliFdz4v4OLjMkXSt5xqhLqyfvnXMqKU3/OpaGOG0AiEA914Yxa1mFWhAaokppy/2zYrhsofgHs084cBfLA+wm6Y="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.7.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.1.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.4.0": {"name": "html-minifier", "version": "3.4.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "80f236b7374d70f017fecaac42d37bb1170d2975", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.4.0.tgz", "integrity": "sha512-X4nIIw9hb+sbHrvSd6KMSQAW+cLvlmAQh13cORUbZ2Zvv0JOceBZXe3BbieeFy2kImf3h4EdID6OZrgYBhq9kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1O/h3nVHuQevjWWrUF7biNueWnr/Xcd9Kgeusf6k6YwIhAJG/vdnexUWuIvJp+884wZFfEOMK6zhBaFdZhO5ZHqjq"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.8.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.1.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.4.1": {"name": "html-minifier", "version": "3.4.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "0bf3c54d7f116d500b78c51fc4246039922dc250", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.4.1.tgz", "integrity": "sha512-/EFUlY7bAU4EzkJfCMIj5Bdim+6LSnCE99xDxXES+M3X7dsfVkRmiuT7NxhNtopaGoqfzPLP9T1JukGMRee5eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICXvTFOvFUp4GfIoz0S6HevTIpL/iwgCvrG5XlxxQXnpAiEApQJxp5mortjZLDGxOxcJ2RVjxk9THEMi6yM9J4hK6t0="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.8.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.2.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.4.2": {"name": "html-minifier", "version": "3.4.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "31896baaf735c1d95f7a0b7291f9dc36c0720752", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.4.2.tgz", "integrity": "sha512-1yidF+U2YMXvH84Y2h1vfPepzznU+bpQvrrh7ACtRmPdy+fdvdvHDEptulIyQhxd0R+xplDYFgA30ZfJi5SgGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYyUTlpmEEm4ZAV6YXRe2peqYCArecW+Pi8huW8RVfSgIgOrlIDA3yMRXYciqeT7zXJfGk4AdK7qvs7m7yZmMJrh8="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "2.8.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.2.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.4.3": {"name": "html-minifier", "version": "3.4.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "eb3a7297c804611f470454eeebe0aacc427e424a", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.4.3.tgz", "integrity": "sha512-TterQ4JwXhcH3H55sLmSbuuR//8WJDWYP/rLrjr2oofMhjIKbjI6Ouaswud/74R6zZrwCq45z2uX0+DADvz5sw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLxaCUgFFJo367ynWj6jTD0f0jnet31SBhopHgqlvPoAIhAMBAxlGnurcp4PLIo7kMWd2NdwrFy/fKkt0KgIpBIyC1"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "~2.8.22"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.3.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x", "uglify-to-browserify": "1.0.x"}, "hasInstallScript": false}, "3.4.4": {"name": "html-minifier", "version": "3.4.4", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "616fe3e3ef16da02b393d9a6099eeff468a35df0", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.4.4.tgz", "integrity": "sha512-p/lvDjUZ7qXNQzwmytSZQvsOp1ZvyJ7rE3IRrd/ZbApZ2WpmFTa/hFkUsNsviJZ7nbywMGl2AapnpwLT4n7sJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt3HyaI8CKgQHzE2Zr6jtqjIivbowF1f5GozL4xgZJlQIgI75Yp/OadDb8NCLPAkkF4GUT0Y77gEs2Tcao0Gft0ZQ="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.0.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "~2.8.22"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "2.3.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x", "uglify-to-browserify": "1.0.x"}, "hasInstallScript": false}, "3.5.0": {"name": "html-minifier", "version": "3.5.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "98be1b18f87443592722f654e67a1541f22018cb", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.0.tgz", "integrity": "sha512-eI5ZWYzWwGAY36KnfvwkVgT4w21bMK/eEw0d5/AK5TVMm/m2nM/zBGfp7xBeeN2GHTCN1S4rjvTaFzPkjSCpqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAR63E0Mw+OvAIKrtxiEk5+CXWtBJNxQOaHeFgeDPBvBAiEA/mElkW1LyOxTFZaTXSBTlmrw493N6UHhTnm+jru+++s="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.0.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "3.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.1": {"name": "html-minifier", "version": "3.5.1", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "0fbba015129b4ac9a30b887aec66f39d57cd37a9", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.1.tgz", "integrity": "sha512-DqYaFEVQmhB4uqeYiADjyP+5RvDGCxuTQyty4P5zwQjSQtuQr+RCyuvDcbV0L2Rop5K8Sinsizfrz9tFvQ40tA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCE/J9M4VeYK8DNSxygolTuoFSVCEslxutsAegPLos3+gIhAIuR+STeoCrcP4WSW/3pzShOEMhqCBu8WaVzsJUfMAzL"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.0.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "3.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.2": {"name": "html-minifier", "version": "3.5.2", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"shasum": "d73bc3ff448942408818ce609bf3fb0ea7ef4eb7", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.2.tgz", "integrity": "sha512-CpXODZQ75jOxqF5CR0vqPKV9LuHw96ijVRbEsSPTPFs4gKd5uuMNEUsAvRgz9OSXS/D4fItq0X8362oXMyjZPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMWtAyxJMXkIRzP4MwXmw/xLPazS1asN7so6APohtc6AiB+/O5W7mrHiOAzFmI5mK9ZmKxGwi2v4lxfDnidYDKpbA=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.0.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "3.0.x", "gruntify-eslint": "3.1.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.3": {"name": "html-minifier", "version": "3.5.3", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-iKRzQQDuTCsq0Ultbi/mfJJnR0D3AdZKTq966Gsp92xkmAPCV4Xi08qhJ0Dl3ZAWemSgJ7qZK+UsZc0gFqK6wg==", "shasum": "4a275e3b1a16639abb79b4c11191ff0d0fcf1ab9", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwXu5oD5fj0yH99eEeJgC2KJ8MK8kziMdP5kcK7Sc/PAiBogK67IjoMuaYQregYPZUi8ccXYB0bWVOwpR08gEQXpg=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.11.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.0.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.0.x", "grunt-contrib-uglify": "3.0.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.4": {"name": "html-minifier", "version": "3.5.4", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-kzvy0Lvs4anPdqWgeXiLisMqAKyHwgajAQaarzwZJHkTU40/Cbqup873U2WtPem8uPbGddVnPes3wtfzQz3+hg==", "shasum": "e9bbcca964a6815617ed7c31f1f20b476b50b807", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGqi89Uf3p8/Gkju0wksed61e1Unpc2u+O72/vLeQbVHAiEAxbLAOvnrM4PzpbFZ4eOqeVvlPVwqd2jM2GtMRIoq/Mg="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.11.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.1.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.0.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.5": {"name": "html-minifier", "version": "3.5.5", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-g+1+NBycQI0fGnggd52JM8TRUweG7+9W2wrtjGitMAqc4G7maweAHvVAAjz9veHseIH3tYKE2lk2USGSoewIrQ==", "shasum": "3bdc9427e638bbe3dbde96c0eb988b044f02739e", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrteuquRoeXQKNKxHVTChRZFMOtti7X/Ko1RZZsNzAXgIhANn17DZlH5xhhnKpjpljnoZIixWB8cer02QSiqaZ7oLh"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.11.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.1.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.1.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.6": {"name": "html-minifier", "version": "3.5.6", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-88FjtKrlak2XjczhxrBomgzV4jmGzM3UnHRBScRkJcmcRum0kb+IwhVAETJ8AVp7j0p3xugjSaw9L+RmI5/QOA==", "shasum": "7e4e661a09999599c7d8e8a2b8d7fb7430bb5c3e", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA1qTPFzC89MRzB4Wd9Gx9mLZele/v9EjFi6wq+sqWXUAiEAjoxfUNbaqVw/N3UL0Pc5qGnqCZSWm5+kPgw1fTu4flY="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.11.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.1.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.1.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.7": {"name": "html-minifier", "version": "3.5.7", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-GISXn6oKDo7+gVpKOgZJTbHMCUI2TSGfpg/8jgencWhWJsvEmsvp3M8emX7QocsXsYznWloLib3OeSfeyb/ewg==", "shasum": "511e69bb5a8e7677d1012ebe03819aa02ca06208", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG2e3joPPB1vegrrK8PHw35qHKm7Bi4Ax/s7MZh16z4AIgTagGZLMriajDCm2AfsWCGapgnhhNLluyb5gPX/QgoCU="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.12.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.2.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.1.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunitjs": "2.x"}, "hasInstallScript": false}, "3.5.8": {"name": "html-minifier", "version": "3.5.8", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-WX7D6PB9PFq05fZ1/CyxPUuyqXed6vh2fGOM80+zJT5wAO93D/cUjLs0CcbBFjQmlwmCgRvl97RurtArIpOnkw==", "shasum": "5ccdb1f73a0d654e6090147511f6e6b2ee312700", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.8.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEAJXZz87sH4W082sV6UbFfhju0eE4z3s1eDYB77ixULAiEAgG8Ky9R4tc7+jlPVpxxZLX0s2tovsRhpxz9G3a7jx48="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.12.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "hasInstallScript": false}, "3.5.9": {"name": "html-minifier", "version": "3.5.9", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-EZqO91XJwkj8BeLx9C12sKB/AHoTANaZax39vEOP9f/X/9jgJ3r1O2+neabuHqpz5kJO71TapP9JrtCY39su1A==", "shasum": "74424014b872598d4bb0e20ac420926ec61024b6", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.9.tgz", "fileCount": 9, "unpackedSize": 88323, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2aTKb3++zHBch1KYT6qYe1metWxmGhGjFyZPbYWffWAiEAxLXJFImUtY0KzJsGKOo1v/IJ3W2PSOYdfKwUK7b1f+k="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.14.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.10": {"name": "html-minifier", "version": "3.5.10", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-5c8iAyeIGAiuFhVjJ0qy1lgvyQxxuZgjeOuMnoK/wjEyy8DF3xKUnE9pO+6H7VMir976K6SGlZV8ZEmIOea/Zg==", "shasum": "8522c772c388db81aa5c26f62033302d906ea1c7", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.10.tgz", "fileCount": 9, "unpackedSize": 88517, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChdDWkJgwR0azblK88MrqmyIKtaJtgH/aVbUu/Fncu+gIhAN1mH0w9TpJSJA5UPS1Pj3vHD/f6MekB8EJj8QOmWgdG"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.14.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.11": {"name": "html-minifier", "version": "3.5.11", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-kIi9C090qWW5cGxEf+EwNUczduyVR6krk29WB3zDSWBQN6xuh/1jCXgmY4SvqzaJMOZFCnf8wcNzA8iPsfLiUQ==", "shasum": "f248927f2e076733f58c136de0376553beb101f8", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.11.tgz", "fileCount": 9, "unpackedSize": 88640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1WJZMhAPaK9TcBgRzFbLvwiu49WvrD6+MdGevkoXPoAIhAJ1NnHENb1VO5UQgsUBhugwwvS50tiQCs4N+75dXFwjv"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.12": {"name": "html-minifier", "version": "3.5.12", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-+N778qLf0RWBscD0TPGoYdeGNDZ0s76/0pQhY1/409EOudcENkm9IbSkk37RDyPdg/09GVHTKotU4ya93RF1Gg==", "shasum": "6bfad4d0327f5b8d2b62f5854654ac3703b9b031", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.12.tgz", "fileCount": 9, "unpackedSize": 88687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAHEJSDxnD0dAwMkvjgkWdnkADcUhU3Ue67MUA7QNir/AiAEGCSjmne8KUt3rrAcWbdh6lBwe/KsY4V4JY9NiXIRIg=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.13": {"name": "html-minifier", "version": "3.5.13", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-B7P99uf0LPQ5lslyhrAZAXE7Lk1tpiv52KVapKbeFhgqNMUI7JBd/fYLX55imu3Rz7sCTzZM6r/IBe4oT7qCjg==", "shasum": "6bca6d533a7f18a476dc6aeb3d113071ab5c165e", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.13.tgz", "fileCount": 9, "unpackedSize": 92853, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwFNki1rgV5MW1+VXNP4hy7zLPY0Tk8d1Mwdo7B27idQIhAOFd4NbRFEKzWYSK9bGB99wAoBkSp9EUU5PAeex9H6sH"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.14": {"name": "html-minifier", "version": "3.5.14", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-sZjw6zhQgyUnIlIPU+W80XpRjWjdxHtNcxjfyOskOsCTDKytcfLY04wsQY/83Yqb4ndoiD2FtauiL7Yg6uUQFQ==", "shasum": "88653b24b344274e3e3d7052f1541ebea054ac60", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.14.tgz", "fileCount": 9, "unpackedSize": 92868, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGX07wC1kzbrvaXY8bYI+bDdo5PrFeW/gSxfFjSWMmDAIhAIDGW4VtVQ9L52Lg4vS1oUu9nvVePU2qUPi/mjPpBqmQ"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.2.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.15": {"name": "html-minifier", "version": "3.5.15", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-<PERSON>Za4rfb6tZOZ3Z8Xf0jKxXkiDcFWldQePGYFDcgKqES2sXeWaEv9y6QQvWUtX3ySI3feApQi5uCsHLINQ6NoAw==", "shasum": "f869848d4543cbfd84f26d5514a2a87cbf9a05e0", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.15.tgz", "fileCount": 9, "unpackedSize": 92918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1OqfCRA9TVsSAnZWagAAnOkQAJSSSeV7c+qrnA4HetL5\n1dIrUtsMvdeGZgjCs7EGIeCwvtAIClIp7oV5mUfDXkYvaqbmGh9Fs1oV8c17\nB39UgbdPC36WxjLnhCJSPl8OM9e+zgpJ4zhMF6ABPvJfjRgvcZGUmwrTqNm2\nOcOd47nFoaJdqP/WMyZCvZDaGORC8dpSkbaxZEUy+5Ty10GzpxkZBPi4B3ln\nTqmqNdKg/V76bT2Qifix3kMDYPXUJUF7va5U0jTCg7NmuVtDR/2tC9JnwtoO\n56bBHiFasD0gi0Pl7aQb5/LTKmXzHWCAeB0x9iGrhdk4L1+SqVDXyqsTje14\nebf0SKSG9OAKHjo0Ga24NZbPCoJI8WJBS5di6y5/LQjEa3KpI48t9ziifDhI\nzoQ/LchkqEC6AK6+JhQTc49nxU9NU1ckru5ovdfTo42dJfLJ38MOKxD1I4O6\nTNFcpJMuPp/wcGNAP4CqqJ6tRW/c5tMEdgfWPbezxYBRzkR6kDsXVgC2mzdx\n7bJr+jaVzRqkGt+R0choeeNOGMUPjCjczLjMLk1mngAwkBM3XOiubSvb5Aph\nUmIwRfyRgBg+od3cH+CpraMAUol12A59ztpNBY9a1vE90FlqQcVHX4JFTA81\nK7/pv/r76kyYSb6Izmak/56SzQFfDG6hfd+v7mBFYtty62jubin/irGiIFCm\nXx01\r\n=FnJ0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC03mw2EO7/KzchoalLAafLtHIxYslTjjGoW2bg1otWmAIhAJHA58VL/ecy8K2uAl3L4r6lH5S1qijKxKfgs/cJeR5N"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.16": {"name": "html-minifier", "version": "3.5.16", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-zP5EfLSpiLRp0aAgud4CQXPQZm9kXwWjR/cF0PfdOj+jjWnOaCgeZcll4kYXSvIBPeUMmyaSc7mM4IDtA+kboA==", "shasum": "39f5aabaf78bdfc057fe67334226efd7f3851175", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.16.tgz", "fileCount": 9, "unpackedSize": 93771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAcFrCRA9TVsSAnZWagAAgQEQAJUNhIhvj0XI5+zghZeK\n2HVGtJ1JMfdfXxub2seeCRDX4nkn0YEgVhYQENojBGKKbjMf3YWNSRTTa/f5\nmLhWRQJcphXMaxO4ab18i/+MVamOod/hyyOoDquiH2oSBNnf2o1CzbYaHpDi\nV1caKWGLqEIscXdt6TrwYjzcmtV4tv910Qs4L+ZaI8TWONGYADuHYqZZUbxO\nwz0geHkHRhSeyIXj20jBm4dUbe34DIc3U7EzOUtkfr1DGUcc+iGUUdyzPMoQ\nktu/f6minViJfjGwMxhlybCIvl6XPQypCkhqtUc092rZNU8jwLuIur8Q3SZY\nPvlI6jPd5Brtj9gaClHwIOY/PZzEid+OH1yC1MMme00u6hdID2KePy5JSHKo\nvO5pfydaTK9Lb08392irLqtOU08hu9Da+cPPOy1uLNexOxxv0Kcq0Kcmwf0I\n9hfjWkloO1RBOod37nasni5C9TbTedhmD4/Ay9H3RshAeq43Y/bMXEG9IPjA\nF++l4pQU0BZNagvnFU1EbeZCUD3sX5P4tn3vwEwUmsn/hfdG1Ysy0X+GDxMt\nlmNSt8ZA/yZwSoVfNv6po5t0lQVLuOiVrbMPg/8Djmxzc1p6aQCezBwv9vBQ\n3XgS85+XSCYwOxrXGDadFLBjzxQYc1hdZcJvwEMrasolFQm+wNwrCPYqE4a+\n/5m+\r\n=jLxw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAsvsC4nxKjqULwNxBOHEHt5BEBSkpfPvKBMZfGZ2KLYAiEAyNlgQ4ZF9blbZVNcH9hWsk7RDEQQgKe7GLnE+fwzSyA="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.3.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.17": {"name": "html-minifier", "version": "3.5.17", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-O+StuKL0UWfwX5Zv4rFxd60DPcT5DVjGq1AlnP6VQ8wzudft/W4hx5Wl98aSYNwFBHY6XWJreRw/BehX4l+diQ==", "shasum": "fe9834c4288e4d5b4dfe18fbc7f3f811c108e5ea", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.17.tgz", "fileCount": 9, "unpackedSize": 93771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMVWwCRA9TVsSAnZWagAAMFUP/04k1Tz9ST7H8UW0gNj4\nmXR/pj9rzC7rVgceUhKH+Ih/tLkzmiW4MBnbZGFzFVrI4DT3rrGcx1qQysg7\nDQIJUY/BfjtQNQSumUsVAJx9Djl5dqdwLXQ8I2zqv1515MdzMZFKdz6DkAgd\n1JkyuNlqucTbl9TKQ0IWkgc3+pda8PqqgqIzsgUl7csjYPbn81MGX2dX1PAn\n3H+3euEy4Vf59jRxQCQ9x7srczbWQQjccm/6X1vYwG8dltD9FcZwTQJOy05G\nohK9xKfQ7cRoQ9qCtFeia4tZPHfmPYuxM71cE2Iy6hFkkPcRBlueZA+29sam\no/bt8oLWcmDShYjjZTjDcPK+ECVj62Bc/WBOCN/R3GabWmqT2N0gb7X/+5Na\nIri2eOM4wBsBlj7nU4pLSKbcWS3l0w9Za9wz9DVUfJJYbXcSZr7CGsSU0rJG\n1MqA9iajChcuVYknZEgsVk00Sn9mrJUUaEGpkA4ZEicI8bKtbd7WP0xzL3Cj\nZaabBoFHcuFsCrpl/orDT/xQupG/b/DIcngHLNR6xSEGTiwgVlhs+CG1C+mq\nmnuDmox4iocdaqASrJ+8Z09IpcRg15s25+VnQhwfLDov4vC+UXfs7o2ZaVNE\nbpTzGO8W9XLk/4RITMKC9e2dlhoYwRVWJ+C+VZdqpV4cExtnG/ppNRMceXcg\nNI0U\r\n=vAym\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFApZlBeqcvUNAi9i7/CYurLoK7NZGJKj4G3nHAVH6Z6AiEAp9kTieZg61KJVoL6sJ+QL4BRa5Gu3xL3+3mEfPMKMPI="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.15.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.18": {"name": "html-minifier", "version": "3.5.18", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-sczoq/9zeXiKZMj8tsQzHJE7EyjrpMHvblTLuh9o8h5923a6Ts5uQ/3YdY+xIqJYRjzHQPlrHjfjh0BtwPJG0g==", "shasum": "fc8b02826cbbafc6de19a103c41c830a91cffe5a", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.18.tgz", "fileCount": 9, "unpackedSize": 93563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOyIUCRA9TVsSAnZWagAAFZAQAIcGDh1cLcpRQO+Nb0SH\nYpNlPoe9w0BIyHklz2T164JZPfdy2lAynvcg0xENMDAQFMJfV4z+0QhV2mia\njGqsXeyt80dPDmu7MgqGl0S45pYtGYUap/RRFdzMnyGCCAaauFG6RQ7zTd43\nzVf8blmLoDYR5vqt0+36Xxele/N3wNGCqRbQWKfvs7DL66wJUNkfPxA41/uP\nSgDDwN33SJS9JzC/kWogBhJLR66oNgQEja47dNadrvKmchAlBM1FDo4uF80O\nXx9mkBqwr4GJe3jgION0WIVsYw6U28LiF8CmgZiOVduHWlExFk2wpnrs3jUN\no2M/KDB9p54r2qpnqlDyw2NDdgUDztCNscFF/O88K6wmyVnVlKcBxyKKDGpf\n9T16b0qx6aMLuwTPYJSnRxActoG+vkLeGdLdFK4jGw56e2h76ow4KO4mqq7W\nR+COSluWPKHe0FeKxfDnW0oRSUFsS6TdEylX4aqSH38eMlc+agYlJlGN3raU\ngPjbtrQJ3AmjgQ6LGA/0U6TZ+A6OLN0ZfiG6Eatp71LvYPUUi1OeDopuyDYf\ncyjBhRsd8jSm6KDhbURo9GKpuqNvAdh+MsiU5iP6FJJCfPHLJSye25iBfcLC\ncC1f8cmjD5PVWmvecz6DNpuUULB/Kc1gUh0TH5AoNqep4EL8CRaOQ3lEyhIi\nSFQ6\r\n=Z/JB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvBL5rYW7r/jNewEt95RhURfUNDATUh1iD/AAjJp2BxQIhAOxO3B1XT+8ndF8M6OTHgFO441hAFTWXC7g/j0xIZtrK"}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.16.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.19": {"name": "html-minifier", "version": "3.5.19", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-Qr2JC9nsjK8oCrEmuB430ZIA8YWbF3D5LSjywD75FTuXmeqacwHgIM8wp3vHYzzPbklSjp53RdmDuzR4ub2HzA==", "shasum": "ed53c4b7326fe507bc3a1adbcc3bbb56660a2ebd", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.19.tgz", "fileCount": 9, "unpackedSize": 93713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSGqVCRA9TVsSAnZWagAAqkMP/1JlFHpw3T8av01Jp4+o\nCcyXgHox4K9HQKTZFsFhC6Ib+WKx+1A0l8PEd3dTsOaVLxjFBNiSf0MBvhh8\npUKKNuggYvhzUHd0cNj+G4J3I4fKiNh1JV45Qww/DfkkqyjCvsK4t1OGKK6j\nXGiw7uKKOfiSZPPgCvahVtLbnjWfGB9wuW1wT0kr4DXx8zj6rMZtFA510RKk\n/pCCW8hcu76SjTReo3k97xMbDYOmJ0qGZpBRTMCniqKrjA5rqHHTJPX4l3G4\n3ZNZwCoiHs1xr+PwekByXDJvMC/GMotlOCh/OYhIykcrGgfR9UP2A0N3jApV\n3Au8ElPFE7l6MWqCIxk/wZAsbKhE8IGe29/kkN6geQ/jJnAFrqY7TVNuS5mX\ndoWCcSkUohyQcnTl58fi8pdouVkuhBhWvZbTv4635IYbee2z8OE+uKyFY3sh\n+VEjdfwz9/5y4HMbNOwaCFXK6IHoZyJ9/PworQ+kxBXtI/FPBj938Wu1DPjZ\nZWtiAEBLMhNtcNhVUotrEfhOvr/Vc+MHbr/GhAJAq3ih9QgECBOedZ9G+jDT\neWSJDmAaNJhajXx/xuxQAbnlX/G4Zcfg4WG+XiENABNGgwlR3f4SlWi+PLrt\nMaPhRsN4xSS1sP3GwX8B86HWkValBszangJEWx/ewtIbjKkmwxR1xUT9qp0N\nhE6l\r\n=2+T1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSJaREVJU2PasfM1ZIBLMfK6Mpkc0GvtAWbAy8PWCuogIgPfrfmIq/JmBxuR/GBanHgSrQFmWGxVaivcoWWQ2AYig="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.16.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.3.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.20": {"name": "html-minifier", "version": "3.5.20", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-ZmgNLaTp54+HFKkONyLFEfs5dd/ZOtlquKaTnqIWFmx3Av5zG6ZPcV2d0o9XM2fXOTxxIf6eDcwzFFotke/5zA==", "shasum": "7b19fd3caa0cb79f7cde5ee5c3abdf8ecaa6bb14", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.20.tgz", "fileCount": 9, "unpackedSize": 94429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeTF0CRA9TVsSAnZWagAAMhEP/2Lpq9vojxjHMEZjG8ct\nvlLfFgA1xFo31y38w2/pXST4KHUFBWnMw5kLSnmneXnGtELUsTuAxlob8ykz\nNrZFN1DqLGcrj2AouIN+V4PnzVddijJQiHxI4mNNVEyAokULfXRgL8P8jwwF\nKHKkpNH6CefwzrtTSTTmcdUInqtz+tLvOWZfi8HMXWunu6A0kKet2oBldfx6\nnRtef4mFGFcv0++K3JM1z+0hMQnfCNIv3195ICnRi42xg29JaX75oZ98Km02\na68S8RTeAoDkgzLRo5WsfLPN0+vu8q+6zO4PlqmVV35Vg4yDTx3ASNSIBXgE\n4hmqmoi8+v3SjEdzmWYzsM2ZtqCslnpOrQnSnvVzkfU3mtEU8zvSxK+gCbXW\nZUtHT19awUBGHhWWffgvlA1hwvzlWJs84g14260ku2t8sBBNam01y4V1ehHO\njXqIpgTZsTUivUfLDjnrBGWfitl5zdxm7e40D+mt/lIF+5Hkni5MttiDvfHg\nXox0g8BNRDecr7WxAjZpgUTiEfBYNsBw6/b7zahn91mVRnJNjiZsfjTyWkVa\nLgXIbxKGh2qHSEwLvmzSPsNVEJT8r2O6LzHwa1fPIIWk9vBAdnP1L56V38m9\nGPSEx8YsSZ7xq3/j+wCBqJEeggAmaUVgzj979PeTduWbdRvbvd6w68JgZlmn\n5bgh\r\n=rpnV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHJ2r44ZxqLbw5nczwNeNUmwHQDH9IBibECGTV7ywPAgAiBa2XfpoP+mVWYSlca9cUjtuBfNu8mpLlbjyI9xBEs9tg=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.4.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.21": {"name": "html-minifier", "version": "3.5.21", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA==", "shasum": "d0040e054730e354db008463593194015212d20c", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-3.5.21.tgz", "fileCount": 9, "unpackedSize": 95870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0drgCRA9TVsSAnZWagAAdKkQAJfceYcLP5GdHUcKemDe\n93kkuwdUH5ebWjsa4MOsKQ6uy1gmJexoQBIXR4j7dy/uXuCbV7EY8YD287pf\nXWkcWAwxC195BsRhql/6U8FZddQcekFVVTzGx8WPWnMQAPNixvaI3Wv/fYw4\nbR1co93Ho+MIGKIJ1YTPrBuASokosHGdO7Q4QTdT9Fpboh5EK+6nDu+BRdKr\n9Xx8aHdHgWaCzHhukyf/n89rxd3AM2Y4IhyRI5R93QpV4ckneM7Ig7Z/Frkc\noRiuXbORJb9o3a2/SmnkURrW6z37YNC1V679sQOckBHhInmpmdF23vgqB7wY\nUCZP9dDiac/gOQqopGko2/bsGOI9KPppDpPBQXrauTNvOpzI5MTsxArAODOK\nuS6A1jyusBQVXliesCxemnRtZWi8T+8bSSilT0EKAcXrvCP4kb5BRlqPupeC\nKW/XK7t//ZaQRoztrMUen/G6xH5TA7L3vu+2BKg8oPo3o4DXpoXA+Ig0DA69\n844risjoYMaUDAqhDlecHkXIObCY+dtLhuMt6IJJsyp7EqfdAbt9Ip5rknrv\nUzkNKPNIZQG8z/JhsN3rrb2oTniCjuyMTZs+TtCmAY+XSV8YkUjnGsnPochG\nnkXkYTVQNlnFPrgPT2iyt9wcze/lOV3+aiTWvpj6ZCw+485hwKO4TAmTk6ot\ntGKT\r\n=34+G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICLqdjCLNpFDvWl3I9w560zhm7houy5G/lWDnmeDftePAiBB4zqv095tfZ6FFISqh1KLWUxrz0o8P7lwx33VqlW2Xg=="}]}, "engines": {"node": ">=4"}, "directories": {}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.2.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.4.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0": {"name": "html-minifier", "version": "4.0.0", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "bin": {"html-minifier": "./cli.js"}, "dist": {"integrity": "sha512-aoGxanpFPLg7MkIl/DDFYtb0iWz7jMFGqFhvEDZga6/4QTjneiD8I/NXL1x5aaoCp7FSIT6h/OhykDdPsbtMig==", "shasum": "cca9aad8bce1175e02e17a8c33e46d8988889f56", "tarball": "https://mirrors.cloud.tencent.com/npm/html-minifier/-/html-minifier-4.0.0.tgz", "fileCount": 9, "unpackedSize": 96816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcol0wCRA9TVsSAnZWagAAz5kP/iIcspTU0E/+8QUv2Eyo\nnC4me3ydHdOO+Jvl0+9QsF1evYBT3hcQjs/IFDjzw1V00JnbsXzigRQ+1XE7\nK1nMRr8SWH5XT2khr7sZ4QkgATtgrwebgqfXp05Sae1egknzneZNe61kxYLs\n5pSOn0U/P5HPApI4SYGshFythweAe42ZElb+CoH9dw87AmPhvzWwIkAO5R3U\nISe8gzwD6Y2zEdqgh0KItYmSSGU30gbPQHgy41D452RuMljTn32eGmIoSJqX\nWatwvCEWkwaHKMLzSyajMw1OpnZeCjsf3kMTI4mjDpyPAyFQKjZ35Q7w/GAa\nT/SZ2lZRCVHHX4g36pQhCMRFqMR34s7PK8kp1tDQFDRetKNzq+yP2e855DL/\nznZNHphNL8zOstiRzsMnGwJcPpdVNl7wExMqnUH6vINpC7QMMHmlXYVfxUNl\npAcPhJ5e1sdrfJVAlrWp/N/zHENLOZ2Cp/wbge3ijPztQmBpcu/sa3tIEVf7\nWsAd04p5+fGRbUCnpNlSUj+iwQosxxYKjfQrNm9+XILJSWspEJmxakfFJxja\nqSLVLWyDgciSX5ApB5OeJhdhOAJl/4KUMXqNMBgMMGSerArCSnYNeaoZow5k\nVggx4Smbl/1xdLyoeLgNEtRgR5wqZb0hfMXQrmk/8Xpiz4dgYO3BbfNXP0me\n+ZR6\r\n=veN4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPUXltlxC56RMNcqoT09ccHY28d7JYccSySHs63P2EqAIgfe9xZmy3xEypJuA3AJ+sAajno++itFEi9hMVmlM0C70="}]}, "engines": {"node": ">=6"}, "directories": {}, "dependencies": {"camel-case": "^3.0.0", "clean-css": "^4.2.1", "commander": "^2.19.0", "he": "^1.2.0", "param-case": "^2.1.1", "relateurl": "^0.2.7", "uglify-js": "^3.5.1"}, "devDependencies": {"grunt": "^1.0.4", "grunt-browserify": "^5.3.0", "grunt-contrib-uglify": "^4.0.1", "grunt-eslint": "^21.0.0", "phantomjs-prebuilt": "^2.1.16", "qunit": "^2.9.2"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2023-09-10T02:44:54.330Z", "time": {"modified": "2023-09-10T02:44:54.330Z", "created": "2011-08-05T22:37:09.311Z", "0.4.3": "2011-08-05T22:37:11.460Z", "0.4.4": "2011-08-08T22:42:57.057Z", "0.4.5": "2011-08-08T22:54:04.206Z", "0.5.0": "2013-04-02T12:37:44.048Z", "0.5.1": "2013-05-12T17:10:53.063Z", "0.5.2": "2013-05-13T00:05:56.850Z", "0.5.4": "2013-09-04T11:33:42.109Z", "0.5.5": "2014-01-21T19:02:31.006Z", "0.5.6": "2014-03-12T19:08:29.200Z", "0.6.0": "2014-05-04T10:56:38.742Z", "0.6.1": "2014-05-22T18:32:22.044Z", "0.6.2": "2014-06-19T20:49:12.661Z", "0.6.3": "2014-06-24T13:13:34.790Z", "0.6.4": "2014-07-06T18:14:55.320Z", "0.6.5": "2014-07-06T18:16:14.814Z", "0.6.6": "2014-07-27T17:40:03.761Z", "0.6.7": "2014-09-02T14:01:17.885Z", "0.6.8": "2014-09-03T14:59:02.997Z", "0.6.9": "2014-10-16T15:39:25.898Z", "0.7.0": "2015-02-04T12:30:04.299Z", "0.7.1": "2015-04-04T14:56:02.996Z", "0.7.2": "2015-04-05T15:59:31.758Z", "0.8.0": "2015-09-23T18:14:24.345Z", "1.0.0": "2015-10-25T15:26:57.543Z", "1.0.1": "2015-12-24T16:38:34.854Z", "1.1.1": "2016-01-04T22:11:26.133Z", "1.2.0": "2016-02-19T19:26:22.995Z", "1.3.0": "2016-03-16T19:22:51.240Z", "1.3.1": "2016-03-23T06:08:52.725Z", "1.4.0": "2016-03-30T21:31:27.162Z", "1.5.0": "2016-04-10T06:46:54.569Z", "2.0.0": "2016-04-19T05:21:57.878Z", "2.1.0": "2016-04-23T10:43:20.731Z", "2.1.1": "2016-05-01T19:42:01.509Z", "2.1.2": "2016-05-02T13:07:55.029Z", "2.1.3": "2016-05-14T15:27:23.691Z", "2.1.4": "2016-06-16T11:58:42.436Z", "2.1.5": "2016-06-18T15:48:34.865Z", "2.1.6": "2016-06-25T05:18:44.962Z", "2.1.7": "2016-07-03T06:56:19.568Z", "3.0.0": "2016-07-08T07:30:50.116Z", "3.0.1": "2016-07-09T04:57:49.969Z", "3.0.2": "2016-07-30T15:29:27.855Z", "3.0.3": "2016-09-16T06:56:32.067Z", "3.1.0": "2016-09-26T02:33:34.275Z", "3.1.1": "2016-11-06T09:50:22.616Z", "3.2.0": "2016-11-17T16:46:03.350Z", "3.2.1": "2016-11-17T17:20:01.559Z", "3.2.2": "2016-11-17T21:36:11.179Z", "3.2.3": "2016-11-24T06:56:31.810Z", "3.3.0": "2017-01-24T17:21:30.120Z", "3.3.1": "2017-02-08T01:47:52.475Z", "3.3.2": "2017-02-19T12:23:18.174Z", "3.3.3": "2017-02-19T13:17:03.514Z", "3.4.0": "2017-02-28T14:41:29.392Z", "3.4.1": "2017-03-13T20:10:17.189Z", "3.4.2": "2017-03-19T06:45:58.162Z", "3.4.3": "2017-04-09T07:44:38.214Z", "3.4.4": "2017-05-06T12:38:02.391Z", "3.5.0": "2017-05-15T20:12:58.017Z", "3.5.1": "2017-05-22T13:36:46.994Z", "3.5.2": "2017-05-25T09:38:54.764Z", "3.5.3": "2017-07-23T08:12:03.631Z", "3.5.4": "2017-09-10T19:15:32.901Z", "3.5.5": "2017-09-12T17:11:11.212Z", "3.5.6": "2017-10-17T22:24:27.799Z", "3.5.7": "2017-11-25T22:38:58.754Z", "3.5.8": "2017-12-24T20:38:04.534Z", "3.5.9": "2018-02-09T15:47:21.496Z", "3.5.10": "2018-03-04T12:05:59.852Z", "3.5.11": "2018-03-14T18:52:42.834Z", "3.5.12": "2018-03-19T18:37:55.530Z", "3.5.13": "2018-03-31T12:11:20.922Z", "3.5.14": "2018-04-07T19:51:32.156Z", "3.5.15": "2018-04-16T18:25:27.846Z", "3.5.16": "2018-05-20T18:41:46.520Z", "3.5.17": "2018-06-25T20:50:56.697Z", "3.5.18": "2018-07-03T07:13:24.887Z", "3.5.19": "2018-07-13T09:02:13.951Z", "3.5.20": "2018-08-19T08:59:31.674Z", "3.5.21": "2018-10-25T15:01:51.629Z", "4.0.0": "2019-04-01T18:49:19.719Z"}}