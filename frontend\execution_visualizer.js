/**
 * 执行可视化组件
 * 用于实时展示统一规划助手的执行状态和Agent协作过程
 */
class ExecutionVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentExecution = null;
        this.executionSteps = [];
        this.isActive = false;
        this.eventSource = null;
        
        this.setupEventListeners();
        this.initializeUI();
    }
    
    initializeUI() {
        if (!this.container) {
            console.error('执行可视化容器未找到:', containerId);
            return;
        }
        
        this.container.innerHTML = `
            <div class="execution-visualizer">
                <div class="execution-header">
                    <h3>🚀 统一规划执行状态</h3>
                    <div class="execution-controls">
                        <button id="pause-execution" class="control-btn" disabled>⏸️ 暂停</button>
                        <button id="stop-execution" class="control-btn" disabled>⏹️ 停止</button>
                        <button id="export-results" class="control-btn" disabled>📤 导出</button>
                    </div>
                </div>
                
                <div class="execution-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">等待开始...</div>
                </div>
                
                <div class="execution-timeline">
                    <div class="timeline-header">
                        <h4>📋 执行步骤</h4>
                        <div class="timeline-stats">
                            <span class="total-steps">总步骤: 0</span>
                            <span class="completed-steps">已完成: 0</span>
                            <span class="current-step">当前: -</span>
                        </div>
                    </div>
                    <div class="timeline-content" id="timeline-content">
                        <div class="welcome-message">
                            <div class="welcome-icon">🤖</div>
                            <h4>欢迎使用统一规划系统</h4>
                            <p>请在聊天界面输入您的任务需求，系统将为您智能规划和执行</p>
                        </div>
                    </div>
                </div>
                
                <div class="execution-results" id="execution-results" style="display: none;">
                    <div class="results-header">
                        <h4>📊 执行结果</h4>
                        <div class="format-selector">
                            <select id="export-format">
                                <option value="html">HTML报告</option>
                                <option value="markdown">Markdown</option>
                                <option value="json">JSON数据</option>
                            </select>
                        </div>
                    </div>
                    <div class="results-content" id="results-content"></div>
                </div>
            </div>
        `;
        
        this.bindControlEvents();
    }
    
    bindControlEvents() {
        const pauseBtn = this.container.querySelector('#pause-execution');
        const stopBtn = this.container.querySelector('#stop-execution');
        const exportBtn = this.container.querySelector('#export-results');
        
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => this.pauseExecution());
        }
        
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopExecution());
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportResults());
        }
    }
    
    setupEventListeners() {
        // 监听来自聊天界面的执行事件
        window.addEventListener('execution-started', (event) => {
            this.startExecution(event.detail);
        });
        
        window.addEventListener('agent-called', (event) => {
            this.addAgentStep(event.detail);
        });
        
        window.addEventListener('step-completed', (event) => {
            this.completeStep(event.detail);
        });
        
        window.addEventListener('execution-completed', (event) => {
            this.completeExecution(event.detail);
        });
    }
    
    startExecution(executionData) {
        this.currentExecution = executionData;
        this.executionSteps = [];
        this.isActive = true;
        
        // 更新UI状态
        this.updateExecutionHeader(executionData);
        this.enableControls();
        this.clearTimeline();
        
        // 添加开始步骤
        this.addStep({
            id: 'start',
            title: '任务分析',
            description: '分析用户需求并制定执行计划',
            status: 'running',
            agent: '统一规划助手',
            timestamp: new Date()
        });
        
        console.log('执行可视化器已启动:', executionData);
    }
    
    addAgentStep(stepData) {
        this.addStep({
            id: stepData.step_id || `step_${Date.now()}`,
            title: stepData.agent_name || stepData.agent_id,
            description: stepData.task || '执行专业任务',
            status: 'running',
            agent: stepData.agent_id,
            timestamp: new Date(),
            task: stepData.task
        });
        
        this.updateProgress();
    }
    
    addStep(stepData) {
        this.executionSteps.push(stepData);
        this.renderStep(stepData);
        this.updateProgress();
    }
    
    completeStep(stepData) {
        const step = this.executionSteps.find(s => s.id === stepData.step_id);
        if (step) {
            step.status = 'completed';
            step.result = stepData.result;
            step.completedAt = new Date();
            
            this.updateStepUI(step);
            this.updateProgress();
        }
    }
    
    completeExecution(executionData) {
        this.isActive = false;
        this.currentExecution = executionData;
        
        // 更新UI状态
        this.disableControls();
        this.showResults(executionData.results);
        
        console.log('执行完成:', executionData);
    }
    
    renderStep(step) {
        const timelineContent = this.container.querySelector('#timeline-content');
        if (!timelineContent) return;
        
        const stepElement = document.createElement('div');
        stepElement.className = `timeline-step ${step.status}`;
        stepElement.id = `step-${step.id}`;
        
        stepElement.innerHTML = `
            <div class="step-header">
                <div class="step-status">
                    <span class="status-icon">${this.getStatusIcon(step.status)}</span>
                </div>
                <div class="step-info">
                    <div class="step-title">${step.title}</div>
                    <div class="step-agent">${step.agent}</div>
                </div>
                <div class="step-time">${this.formatTime(step.timestamp)}</div>
            </div>
            <div class="step-content">
                <div class="step-description">${step.description}</div>
                ${step.task ? `<div class="step-task">任务: ${step.task}</div>` : ''}
                <div class="step-result" style="display: none;"></div>
            </div>
        `;
        
        timelineContent.appendChild(stepElement);
    }
    
    updateStepUI(step) {
        const stepElement = document.getElementById(`step-${step.id}`);
        if (!stepElement) return;
        
        // 更新状态
        stepElement.className = `timeline-step ${step.status}`;
        const statusIcon = stepElement.querySelector('.status-icon');
        if (statusIcon) {
            statusIcon.textContent = this.getStatusIcon(step.status);
        }
        
        // 显示结果
        if (step.result) {
            const resultElement = stepElement.querySelector('.step-result');
            if (resultElement) {
                resultElement.innerHTML = `
                    <div class="result-content">
                        <h5>执行结果:</h5>
                        <div class="result-text">${this.formatResult(step.result)}</div>
                    </div>
                `;
                resultElement.style.display = 'block';
            }
        }
    }
    
    updateProgress() {
        const totalSteps = this.executionSteps.length;
        const completedSteps = this.executionSteps.filter(s => s.status === 'completed').length;
        const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
        
        // 更新进度条
        const progressFill = this.container.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
        
        // 更新进度文本
        const progressText = this.container.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = `${completedSteps}/${totalSteps} 步骤已完成`;
        }
        
        // 更新统计信息
        const totalStepsSpan = this.container.querySelector('.total-steps');
        const completedStepsSpan = this.container.querySelector('.completed-steps');
        const currentStepSpan = this.container.querySelector('.current-step');
        
        if (totalStepsSpan) totalStepsSpan.textContent = `总步骤: ${totalSteps}`;
        if (completedStepsSpan) completedStepsSpan.textContent = `已完成: ${completedSteps}`;
        
        const currentStep = this.executionSteps.find(s => s.status === 'running');
        if (currentStepSpan) {
            currentStepSpan.textContent = currentStep ? `当前: ${currentStep.title}` : '当前: -';
        }
    }
    
    updateExecutionHeader(executionData) {
        const header = this.container.querySelector('.execution-header h3');
        if (header) {
            header.textContent = `🚀 执行中: ${executionData.task || '统一规划任务'}`;
        }
    }
    
    clearTimeline() {
        const timelineContent = this.container.querySelector('#timeline-content');
        if (timelineContent) {
            timelineContent.innerHTML = '';
        }
    }
    
    enableControls() {
        const pauseBtn = this.container.querySelector('#pause-execution');
        const stopBtn = this.container.querySelector('#stop-execution');
        
        if (pauseBtn) pauseBtn.disabled = false;
        if (stopBtn) stopBtn.disabled = false;
    }
    
    disableControls() {
        const pauseBtn = this.container.querySelector('#pause-execution');
        const stopBtn = this.container.querySelector('#stop-execution');
        const exportBtn = this.container.querySelector('#export-results');
        
        if (pauseBtn) pauseBtn.disabled = true;
        if (stopBtn) stopBtn.disabled = true;
        if (exportBtn) exportBtn.disabled = false;
    }
    
    showResults(results) {
        const resultsPanel = this.container.querySelector('#execution-results');
        const resultsContent = this.container.querySelector('#results-content');
        
        if (resultsPanel && resultsContent) {
            resultsContent.innerHTML = this.formatResults(results);
            resultsPanel.style.display = 'block';
        }
    }
    
    getStatusIcon(status) {
        switch (status) {
            case 'running': return '🔄';
            case 'completed': return '✅';
            case 'failed': return '❌';
            case 'pending': return '⏳';
            default: return '⏳';
        }
    }
    
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    formatResult(result) {
        if (typeof result === 'string') {
            return result.length > 200 ? result.substring(0, 200) + '...' : result;
        }
        return JSON.stringify(result, null, 2);
    }
    
    formatResults(results) {
        let html = '<div class="results-summary">';
        
        for (const [stepId, result] of Object.entries(results)) {
            html += `
                <div class="result-item">
                    <h5>步骤: ${stepId}</h5>
                    <div class="result-content">${this.formatResult(result)}</div>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    pauseExecution() {
        console.log('暂停执行');
        // 发送暂停事件
        window.dispatchEvent(new CustomEvent('execution-paused'));
    }
    
    stopExecution() {
        console.log('停止执行');
        // 发送停止事件
        window.dispatchEvent(new CustomEvent('execution-stopped'));
    }
    
    exportResults() {
        const format = this.container.querySelector('#export-format')?.value || 'html';
        console.log('导出结果，格式:', format);
        
        // 发送导出事件
        window.dispatchEvent(new CustomEvent('export-results', {
            detail: {
                format: format,
                results: this.currentExecution?.results || {}
            }
        }));
    }
}

// 导出类
window.ExecutionVisualizer = ExecutionVisualizer; 