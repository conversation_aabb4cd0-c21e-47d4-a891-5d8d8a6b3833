{"name": "babel-plugin-transform-es2015-modules-umd", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.0.2", "description": "## Installation", "dist": {"shasum": "b78799d85738ff113522899cf15b4d73a23ec1b9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.0.2.tgz", "integrity": "sha512-Mf76ekN5gxIAOJV5EXQFxTwt312VjKYSp//eHK+S1P8DeBYJW4r47cnSRdEvIoJi2RJQOfRJbUt0n/Ex+3rPRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3j/1Zq8MiPeQP8DNbN5Ay9jI90n2cDK0DxtRXKR0x+gIhAMhtkKXZ27MzbFEYYb7m/qC3X+oJrHF4VnhgNkGZ/0Yq"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.0.2", "babel-template": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "8fdde2f2467d3c13439be613caccfaf9a9968ef1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.0.14.tgz", "integrity": "sha512-AzwH9/dvKBG6tzcXHDOw+yny2AAoKsBs/FiqHqCact7nyU+lkeN5y7tHI1wJ2i3rXPwPLyO2CE3V6cqdvNZ59w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9TQ6PHh2GPhjbkV/85w7Fa6JrLDKTvlz4FJM3I3/kBwIgT4vUBt2ku9O8JZnTWEgD2erpF2zaGM78ttej0diOGXY="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.0.14", "babel-template": "^6.0.14", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.0.15", "description": "## Installation", "dist": {"shasum": "fcbe0ca83b4b86369a73e427e57570d543c07211", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.0.15.tgz", "integrity": "sha512-tc3Wz7H46Ta5iqxs+B3nuj5OhDA1454L/lOyhxPsETySPhU6Jr/HJyIKZ0QKmZ07FldF4Y4pO/MdgfzAJ1BHwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsb1KBSEZkmuZomvsyTzc1zvV/a3uTFrQSxfpfzq82lAIhANu+cnD4doIaA6NTizus9hVRtdlJS7DV57vaFeufTd81"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.0.15", "babel-template": "^6.0.15", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "53af9d8a73aab7c6e2e522a4d7e084e5959b6c11", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.1.4.tgz", "integrity": "sha512-GULqHYyL/gxHhvOxFITS9JI00ZUwL07tt5LyVif02v5IXiMCml+YSuW6rQkaHhjVBxlBQPVDVAYE1iMgpR9t7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxzV3+g9AxH1jkBKtMJPAcdD/1SrAqjDbIX4ecK9hXMAiEA+oZAD67tNACCbrHF2imtFkQ/qIPyQWEnuNfIEAu+ndI="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.1.4", "babel-template": "^6.0.15", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "17aa0e28614b0543f04770e554cb214f86174183", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.1.5.tgz", "integrity": "sha512-/bBdYTxc5dOOnHNi8wEejP7pd+534JZV1sxyfsfHG30dzoE/uX2PRrm+qooF8b+R5FE6IDC6yHTAAMqmFvAx8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGj0NzMlKGv+V0/mqFD58qESYIjrLUR2O3x3APvzmoeWAiBi6uXBCLeyCMvlGl3J2TMlJO2uWOrGwWtHplmJ3eoWzw=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.1.5", "babel-template": "^6.1.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "6284891aeee29e23ceb9d73d5e2ff0dac244b651", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.1.17.tgz", "integrity": "sha512-XQcmJQROPuUqP6Mhxn8aTj65K3dfIRbbEfw7AyjAisn5HInFUjcN2+8VbPnUEL3YaVnr6TSv/yV8oI5EUFQtyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6rfnddDyMXi2FQ5yXN4uMjbK6qNMQh/gIBRLZf/ybAQIhAKF1JgFq7/zFwfphnatf4G5apqm+ml3qAQnhBarF29Ub"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.1.17", "babel-template": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "66a9ac682742bc2a344e3e0e5ac3e7493875d13a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.1.18.tgz", "integrity": "sha512-+/WWmdT99M0Rpo4AQhOeGnrFiQ/o0SiiKvgi8TBAoDUHAgoUIrk/58erhq099AOfexVTi2kZcjsOUN7qbLrRpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJfaREE51FGeBLczRMVYYy82deq7q7wPvwVhLUI/77JAiEAmHae7nTbgz29NLx634tjkFIUXE0NAYXmHjlF57eX4+E="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.1.18", "babel-template": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "ad56eeb39a15922345727046188f1fc4cc5fab9b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.2.4.tgz", "integrity": "sha512-4Y8v5Sin2QWhLGDIw7mmPamw9BFBCipNuEZwOAAg5mrAwigiUBg6H9NsGxf5MunI+h4xXpEcxECGp0hFtDcCxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTNPftveWp8sjPozwB8MhyjzFFIW0f3QY2otMo268I3wIhAK6NOKH9Gudg02ckT8HgCVgikV88Y6WsoICpKhj5nUDT"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.2.4", "babel-template": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "2e4984e37ed7e8c4afd82ab8271b822e05b2bc49", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.3.13.tgz", "integrity": "sha512-PMzfaCt3A/Z0tEr0B+CLU+vEQxKyQ2ok2GhL4BvSxmYPsmizoopBmul1F3De8Xm2986z0vg7kjU2wKzXrGoLRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYoSATaJBS8C58XnOfz97zLhiH6j686S4v5qJfnZ0JOgIhAK7i31IARfZBkHJdW5zGyd/BqBD31vX7adR/V7oiXOJN"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.3.13", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "82e5bf9013b20d759ad06daad767ee988a9ac53b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.4.0.tgz", "integrity": "sha512-MjfnHK6upkM3c4ncODl77yV8CJSn6H0lk8xPVtwHFwFvq9S85135o8+NEwbTpUdsyWMvO5DrzDeNJaD2Q2ygDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFM9hHBRvayrkhL171+uTLqpYmYyaa+gAR/N67nGmhglAiA0LqbR5ZV9eXtiCWRcUdgRIcmT1jal11nzThfGLv4TRw=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.4.0", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.3": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.4.3", "description": "## Installation", "dist": {"shasum": "e53a887be7fe8e98ad930359f37f5a4afe379f9f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.4.3.tgz", "integrity": "sha512-8IrRsTZ/NuP4cgj/iz46r0iEuRtyHtqSgZaekMfAjHspc+vhAObFWG9VW1Tt7ylf++KN2mZUhBDYaKZiPeLLYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6z/ZHdyih0LOQLRDqewrz55ZohQP2xnlAnhZCxDRpnAiEAhT4/3jEm7HE+dOOihmjWH0+C/6vl+P/J99F6jQwGobM="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.4.3", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "1b7c9d927df74b9aec121c2e2fed5354432eac1f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.5.0.tgz", "integrity": "sha512-Bavul1sWp2F85lYp7N0o+cKZSgr9U5MjObwAoRc9qpwIW+8OLY6KqEf/3ngATmT3AJbABzjxLWXs5/sL4ppuNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQtO6KfDrPeaWpFk2Uq697QCMWtsi2ir4tLxloos1J5gIgZtAIEDsDuxkLAeQ2CrPjH0OlT35QK7SKkfr1zbP7jas="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.4.3", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "77a7d82e681777f70aaf3d156cff47c018173722", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.5.0-1.tgz", "integrity": "sha512-vD/EScB05rBStyZJQF6PSKXtyCJSRL2Nu0FLMLkHNytJ+IuDJoaD0XfG0SAyKHEdTZodBVqgDTnhgOKbAbNqlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVDcwlitTEq8ZrIm4tQJgtIrzOyp1Zl5UwCQDGDmcS1AiB3bIDxiA5aFoOnVD+tY4SeJLavBSInyxN/jkbL9BxPmA=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.5.0-1", "babel-template": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.6.0", "description": "## Installation", "dist": {"shasum": "5cd90f37d13dd8aa5f353332ba7a5a7a5ef135e2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.6.0.tgz", "integrity": "sha512-T33sqQXBeV0xsKG/FlPt26AtlQDkwZTSFNW60rSLr0T20kswNaJjgj5oxqDQBtqkTizEWIG7gP5ih1zk+dvRxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGWXkQ7wBLLGYzMg7MnqdI3nsSCI1iZU+J1hW8onlmB7AiAIfHxYHb8hTW+EDSkhzn2wGB5y0Euq7N10dAC5YfthSg=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.6.0", "babel-template": "^6.6.0", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.6.5", "description": "## Installation", "dist": {"shasum": "9310c753090ff3b33e31ab1ce62acd39377c7d98", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.6.5.tgz", "integrity": "sha512-31woheB2dE60rBEn8CtDbH753ht2J54VTdSbHlrunIJmrv996th0apbDhfaB42l/lvwnrbJeFesrX1UFyQTDLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ/DWlUHHEFLV6JYP/whZGQ3pqQ56Pqz3TfJb1+fSBowIgDePqk3b4xT0xgfr167enCJzPRDFmg8kHMGFPzTv+8L0="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.6.5", "babel-template": "^6.6.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "fad2f4cc0955681d6fe87b69d327a443c6e37e5d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.8.0.tgz", "integrity": "sha512-tFT8oIPlJgp7e15opBpm607m1OvRHOZ/MAOCF19as8xH/0ouNAjqo0Yu/CRz8/gK+DPEMfJ9r/yBj2JYkcUU4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDHdQA3DB0QUnIRQ/XiZIH1w9gcqIPITMb8qYAgTcxqPAiAxqw96KHAOLaUNMvRjDW56oplvxEAY7PVmWXBw5yTl9A=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.12.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.12.0", "description": "## Installation", "dist": {"shasum": "5d73559eb49266775ed281c40be88a421bd371a3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.12.0.tgz", "integrity": "sha512-lTzGFgziPIOdr0yO3Y/eFfwrgEJfrbFvZy9lwamLL0/2C4SfWuexqLM3weZCT9Z2/U8849Gsb05PpV3OnCfg3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcXn4smVKVlbTFbG61b/L9fTdgsi0JXkqnfCzFXeO4MQIgIASR1mlUPNAIu9PG+V/N0x5qsmTE+c+1Qn4jZtSwyms="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.8.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.18.0", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "23351770ece5c1f8e83ed67cb1d7992884491e50", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.18.0.tgz", "integrity": "sha512-M3pjSg+hirP9lix2gnYLTGJRwQl3aTWU3qxGSxM6IOWSaMrZGj8ZH1qLaeeXrmDn3oUIiQf4c1UrHDgyvrwXAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQ+FgZ+T9akQcjf/NTEfzvLbdwcDWIAJyK55WHYOaUOAiAaOEHyniPr2Dxs5pWgJ4D25KrJTlUXk4llkEwLxCbnsA=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.18.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.22.0", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "60d0ba3bd23258719c64391d9bf492d648dc0fae", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.22.0.tgz", "integrity": "sha512-b4xXP8vO8B8m0cj3DS5Q/05DRfl00r4tX0jxESKg0znnVPPzYWGDyMwCDYfuGQru1Vn/i0LAak87EuU+oN/YFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfgOksw+re3D3qupenqlLiYxRPUi9Rqyd6D+Ue/JBk8wIhANayyjoDco0CX47BeQdQhxpZXSDOI/2sYmlJDXo4S0d/"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.22.0", "babel-template": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.23.0", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "8d284ae2e19ed8fe21d2b1b26d6e7e0fcd94f0f1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.23.0.tgz", "integrity": "sha512-Ooy+JAnnhyWlBmq7dQXG+WU+a2CwPnRosprEIK6eM87o2ukEVMNT1+0+kVMS1YQV9leymhbtAewcIh6gHjKT0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDunE8bZmM36r9MGP9KG+++rhuIQQNUkZrKc/TQUr3NcAIgB9PCAPSuoutwG2EALEj5Py5uRD8MWVct2I7pid0Crsw="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.22.0", "babel-template": "^6.23.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.1", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "a8dcb8be27d5f3ba6fd80c1037abd9f1a37d826d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.1.tgz", "integrity": "sha512-smmKp8+4oYVvPXO7P4udQqzza9Ckhlr973cMWTTdGsvgErRIixKfvf6betj1eXIN1lTF7SWwc3MhAez9E8Hwwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDj804/57KNnQShLreM0j3R4wLeFuEsIi9V3RgI0HWfEwIhALHy0yNkEhpqejnI1w759bRsTBed1dZRWEm+XKLyemx8"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "6.24.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.24.0", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "fd5fa63521cae8d273927c3958afd7c067733450", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.0.tgz", "integrity": "sha512-/4TWXh6sey67PrbcORe1wsnRAXcscAzzIriqQDgsBYwlF3NZasJclEDJLuqz5SaWv2uNyyXHuwfTENx5dkWADg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZDq5lBMNZa/4u7fqpkpMX66kF4VqScKfmKdHQqFhsCwIgBAGNuFUz6eKwURXuEYctHrB5FdQAVdoGMnV073ACPZM="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.24.0", "babel-template": "^6.23.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.3", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "ec37fa367540262900b0b14cda7f2006bb1e1430", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.3.tgz", "integrity": "sha512-dGBGIA0DH6cZg1y4EAHI1JDeg1cXGQsS9xGOCXadtXb0u1D5oGmaOV5uE2oujlPKeGZ5p6Gbf//buEgzLITocw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKPyGT5SGUbEvxO3Y2vKqoikR5trMA47FSAhVc/D3o5AiBxlgxHn8a+5cLK7hYqih3D0DzW6pTWuaJ3AAsRrfCVEQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.7", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "cd73ec6d2f1d8a3492a5ce440a451b71f9dbacd5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.7.tgz", "integrity": "sha512-4b1jP3h5RLr2ntSE1Hd2ESrGUo3LMf8DoU4nLNPdR6d1mVi2kkdAYhKUkcUKZxWGe+ICyfY8tGr6/cYsRjug2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvJGFiK7/r6O1XCXE30L/0HcKuiMagHDh7zZIseY+GZQIhAP6X/eh03HbSs/9n1zZIrpvTN2oi9ZtbifiZ7r6z4FCu"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "6.24.1", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "ac997e6285cd18ed6176adb607d602344ad38468", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz", "integrity": "sha512-LpVbiT9CLsuAIp3IG0tfbVo81QIhn6pE8xBJ7XSeCtFlMltuar5VuBV6y6Q45tpui9QWcy5i0vLQfCfrnF7Kiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDV7EfWQ2A8nXxIHGDF2J/SzY4WMoN5ObVxCpWnfiCQAiAp9Ygya2/dIlBHGbyOUMgzNfKGBcPXlIhBhWNyHeCWwA=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "^6.24.1", "babel-template": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.8", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "65e866cda069514d2de19f2ab7360a765e2ed1b3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.8.tgz", "integrity": "sha512-GrjAcT2lpIDqrooMQYBQ+whdZ87yq/vxODrQKG0uHHJl+w37Pwi2+geruzWSSs/lgfqQ/DpIEfYCaYBIrJPPUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjFtCvDKMvjciJqaVpdcC6uq/egbZTX6VWvbD4m0KokwIgPWJl8i63SM7BM7iq+oAxKaT2mZQPOXb+fDonsfPxfOg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.8", "babel-template": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.9", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "27398241f7c8965f39451ebb9daac0d5ff58e53a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.9.tgz", "integrity": "sha512-q/9KXZZyQ322ytofSzYNKnYg4L51XaX4qsHjX+QppoZTgcOsLNyFiBdxf7swTOk8S+K75Z34XPI49G868aNGxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIACuhwpF/4tdbJq6NJtxxtcLMaaSGdzOYpur1XqnjbR2AiEAwOrFR87Y0d7Fxi18Gz6mWOn0KUXqQ7mM16UapGxBiTE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.10", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "dca9040937296420716ff7bf8c2e94b2af56a28d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.10.tgz", "integrity": "sha512-RjwoQY5Ohy8x/eHNvtiyIMnmJfyOsCjvXNeofhoew78kWaF1uhAbbMLTjs0a6wCI6ahfGco5yJDsG6iY3l87FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLLLF1Q7/xQIB7tDqsQknZFWHsb/9fcd2tTTx/Q9YEWwIgcMxUsYLtXiEXLoeBvi7k0RRVhFAUTi3fUisYmNp3+EE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.11", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-GpYIDhSyaLUXYkhAR98uH0oCtx9Na/cHPqBQbYDa5QaGxvmMUPq6E/1cuWafAt8AmlCzH6isXCTkuzWp8aDwEg==", "shasum": "5136248296a4472375b66acea0ac3943e407d643", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHmbFFFhJFyJKD1qyQYbbLUKtP42ILi/oLpK7drZMMlJAiEAq7jqXt8CZdWdoRCWs8EHGsx2x6DTrV+pmQARQcEJXDY="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.11", "babel-template": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.12", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-579lCrXiWLomFHf46tjlhtGsawDQfQ4SIwijqrIk+PMZidOugOXzCV9LjQNXRVqYjaztrEFkuGvF8YBaACxqAw==", "shasum": "a8bc11e2d93d928a2c94cc1b9d9822b1b023d3a5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8EHWv/BCL5BOlOXddGftFwW92O2bEuTby6OQV1C8S/AIgXJZlDFqslqxsqgP320FzcHidf+NXhY93TeS020zch8s="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.14", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "f9e5e39142070186d6eb91cc1c8d0180b8af3a92", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.14.tgz", "integrity": "sha512-QiLKUq5yTntGJNy2VNs4nJYH4ZiRz9Jy5l/KxGxIcc1c5WNV0a3mED1FqCO39nDaapNhACaCyNHcjzvl3MTT6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTpO4z8L8eRLPP63xD6rr1DBznWpYYGKNrmXr1y0CVLAiEA276Zzs5p7Nr9M4x70SMyLY1KZuPCsHXLPZ3MHXU6Gmg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.15", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "5a1c6f842194b1ed7767db5b395e45688c300847", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.15.tgz", "integrity": "sha512-+gF8AxbeOLF8XTnKpj6HSXOIl5lpuporszMuPgOV5GXqB99/aVUTqnXPNnDmIiDg78Zshj4gBY3Murl2f6GT7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICeqgbAfJPqRRBxXeHQGmLpXMdED/Wu4sMqEW8QMjKCuAiAhcYHT7HlYPzhlrfbQrqmoFwGU+kV/Nft0q+aBZ8j92A=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.16", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "072044ee72945cec1e65c43f51fb391441f119de", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.16.tgz", "integrity": "sha512-1Pp58Dn1yWMLFU6VPnhCs8NcExkXjFxRCHdxaUaj9PmC5Vmbe4J80Hw1f0spACTvwqG2vLfhsL9gDCZArbQvjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRxurmQXmcTs6SK+Tr1CQv1CihfaoWMHEda4sX5+Zl/QIgcLgPK91vfxvClQOeSVg2PE8Lq6eAFRWvFOgZe525QNk="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.17", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"shasum": "1dc8914605e6481219a658baecd4e525b8a5a769", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.17.tgz", "integrity": "sha512-jEbSgTH7Dswgy4f+2Je9PEJ5Nx1FZ1AlNO9HN5c/G4yd+yPBkZhNWLEvmObkKm9MR5NL7OUA66FB2MzFEapcpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuQVlb0/kEyGBh3n2EsxNYjX7/TLg2EWFCRpt6L8bMBAIgeW6LY9iGVDtAmnq4tNc5YDXQrrXiXNLHh96eXE9V/rg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.18", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-f4uaVBOyeauJsO+sx2GfPlTrvImh43fXR1TzoVxi0/R4j8YjgFiJNBnu3hhFSEBToMubqifOE3HVkyvlKeJrmw==", "shasum": "aa0e33f8aedb1dd32793cf7dcc48444314899f7b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQm5z81Q6sArURqrHL35feWrsDnjT0+Svhdk0+V4A1uQIhAOio0a8atrRgb3P+lTmASCFOA5M0uBB1f1SHgscvBI5r"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.19", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-9CVtEzzqj64x9PPeBXzOPKYxRos4u8Z0b1LWWI/K7BEIHWL91C07RLCLYAwnOec9jAU3oy64/gysOjW8i3hwsQ==", "shasum": "f0b1a497b453a4113097c8780e241f1895449ae6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHb4O1s88nZLOWZkxMq2CP/X5KgO8rj9sy6aFMAmmH6QIgGcDMX+ZuhBPFkf8ztKZe9mlWQfimfOEPIIXmzHvzpuA="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-alpha.20", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-K8Hnvh3JE+xE2lheAwxny6Ls6adrknGg7pCViXEuyeZgJYcnhoNCKAAsmjpwl1LfBqIUIK6VAQzrdva8R+1DbA==", "shasum": "297a95296d95878c2c5ecae663c1e3fcfdef6fb6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC5TFQxe5KpSY1yC/ctChhQ9WuZ2TTIbClAzef3+VZnwAiBAPQ3TO7wns19fr13IQOHrniHAo4zj86F9CeSW+xxa8g=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-beta.0", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-m4Rax8Myc3fQZvdibK9vKvQhiXz+s6IdJwB7pRQe2k3EsBMZkxVD62I04m11yPssbjBNLBDPtg65+ngqWBkSXg==", "shasum": "4352f885dbc7068a90408300242b9b509c5beebd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwFAlVmnvMeq0jM5yiPDC9hfYN9rPtEDXbs8dFUSMEMAiBl4BIIm028Hrp71ccTIcQgs7L+4jNVxCqCqu8pZpjseg=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-amd": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-beta.1", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-oKG8E6jbiPm90C6g31AZqU2JzLJmD8BxQPm/OPmhV6MAF+AeBG9lqwN3H3y63HnqsncBbo4Bz9XH4FK5NZxOoA==", "shasum": "1fa1d480081bcefd66b76f95bc099f1f63229c94", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+wC36v6YMp8IF2T4C5zL5i9BqajUAoZ4EukZXhCrlIAIgaS4t33DTacqVzs0smR/kzrIo8GG41U+A3Y117QVoQ4w="}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-beta.2", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-NCHaZsJabJbvNy5ya8rbL7Jfjx5XmRpva602r25aaAl8Wt5P/7AiVmHgVYRetw85Yd5DB2LtQTqPmjjDmHm6zQ==", "shasum": "b781bf06e2bb36290ba4bb6c6119a79905d5f0be", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbYxz5eWq5qysMuHy1emJlMlsZyaVPyfOWt8MIxF4gNgIhAOrt8j1QsiFhIAkUCtWY6ClJZK17udYmj5EXVn/fBUrf"}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-es2015-modules-umd", "version": "7.0.0-beta.3", "description": "This plugin transforms ES2015 modules to UMD", "dist": {"integrity": "sha512-sBn8Dtg2r8QCRmm8pT9kpNSn0qnOorN+gYy45gUwf9NBt6oGftncuecMlj1Mo3NCyRqHQ1WnwHLX4C3yDB5LPg==", "shasum": "79713a0f7678b0b041d56e871423afa623c84751", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBEkQ7+vH1vqqp9hZMjViepCUOeCjGssGojbDKFiImwqAiEAn9OMelsSQr6VFtaAN3VoAHK78Zv4EbfaJXn2d8qRhSw="}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:24.764Z", "time": {"modified": "2022-06-13T04:04:24.764Z", "created": "2015-10-29T18:14:58.498Z", "6.0.2": "2015-10-29T18:14:58.498Z", "6.0.14": "2015-10-30T23:38:13.147Z", "6.0.15": "2015-11-01T22:10:26.642Z", "6.1.4": "2015-11-11T10:24:57.917Z", "6.1.5": "2015-11-12T07:01:27.137Z", "6.1.17": "2015-11-12T21:42:18.765Z", "6.1.18": "2015-11-12T21:50:17.561Z", "6.2.4": "2015-11-25T03:14:31.901Z", "6.3.13": "2015-12-04T11:59:21.629Z", "6.4.0": "2016-01-06T20:34:52.624Z", "6.4.3": "2016-01-14T05:56:30.594Z", "6.5.0": "2016-02-07T00:07:41.749Z", "6.5.0-1": "2016-02-07T02:40:53.280Z", "6.6.0": "2016-02-29T21:12:50.260Z", "6.6.5": "2016-03-04T23:16:58.447Z", "6.8.0": "2016-05-02T23:44:48.133Z", "6.12.0": "2016-07-27T19:23:23.451Z", "6.18.0": "2016-10-24T21:19:06.570Z", "6.22.0": "2017-01-20T00:33:57.842Z", "6.23.0": "2017-02-14T01:14:28.052Z", "7.0.0-alpha.1": "2017-03-02T21:06:02.016Z", "6.24.0": "2017-03-13T02:18:15.425Z", "7.0.0-alpha.3": "2017-03-23T19:49:58.672Z", "7.0.0-alpha.7": "2017-04-05T21:14:35.628Z", "6.24.1": "2017-04-07T15:19:39.055Z", "7.0.0-alpha.8": "2017-04-17T19:13:26.724Z", "7.0.0-alpha.9": "2017-04-18T14:42:35.878Z", "7.0.0-alpha.10": "2017-05-25T19:17:56.263Z", "7.0.0-alpha.11": "2017-05-31T20:44:03.281Z", "7.0.0-alpha.12": "2017-05-31T21:12:18.005Z", "7.0.0-alpha.14": "2017-07-12T02:54:12.766Z", "7.0.0-alpha.15": "2017-07-12T03:36:29.689Z", "7.0.0-alpha.16": "2017-07-25T21:18:22.661Z", "7.0.0-alpha.17": "2017-07-26T12:39:54.680Z", "7.0.0-alpha.18": "2017-08-03T22:21:27.738Z", "7.0.0-alpha.19": "2017-08-07T22:22:10.156Z", "7.0.0-alpha.20": "2017-08-30T19:04:26.952Z", "7.0.0-beta.0": "2017-09-12T03:02:55.507Z", "7.0.0-beta.1": "2017-09-19T20:24:45.497Z", "7.0.0-beta.2": "2017-09-26T15:15:55.557Z", "7.0.0-beta.3": "2017-10-15T13:12:23.145Z"}}