{"name": "babel-helper-builder-binary-assignment-operator-visitor", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.0.0", "description": "## Usage", "dist": {"shasum": "1f5fdd211f37ca61e6c63497278fdf073c683a1b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.0.0.tgz", "integrity": "sha512-Ne7ej71O3wE6SvIfJQ2pCMRPDfD/wrzy1tmM3kLEhu+g82L7dJrtWJfYgMFWCUZFczZKF1D0k1FTSoazD+00sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjEDK0xPH3A41DW6ghIVtKe4kFZS8Blvx7qELosPpD+AiByPr45PKuOdypCFGkSCfNVib/7MXVaJVQwW/6MZmoQVg=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.0.0", "babel-runtime": "^6.0.0", "babel-types": "^6.0.0"}, "hasInstallScript": false}, "6.0.2": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.0.2", "description": "## Usage", "dist": {"shasum": "b3da311893a389c7f8a640c34ec21288cd75f472", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.0.2.tgz", "integrity": "sha512-0arNhJ4NCMR8W32xnc2bFkvkMM8Zj7H06+KToBue/eLq/DNv+m7BLxVSW220MF+3AGLKf7Bg/cBHzlPZr+nmXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAZM5aUPD/Pfd0vEh9MPg3FtIplkVHgvp7N5kbTFxAWtAiBgTj9YgpmxbCfXhgqAG2QmR9mUjDiBxlOUsEWHzKWtWw=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.0.2", "babel-runtime": "^6.0.2", "babel-types": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.0.14", "description": "## Usage", "dist": {"shasum": "980237b0ea524a18c393bd2f8d1a97da36e87992", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.0.14.tgz", "integrity": "sha512-cQqw+xEJK6v6rxMJn3zXcxdhJijN0tSNeHfcJdepG8iu4E6QszS7Bv21vRHIwG8bvliocklQWjZv2zjsarmd/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBaEhmrdmGFwgJvyNKjv2zAWbyq86t6dRiv+ZDDnFOX6AiB1MK5fjbnWnzTQJv19cFs2DUltMoc3NXzh29GYP/3DWA=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.0.15", "description": "## Usage", "dist": {"shasum": "bec04177720875aa80450c5e76f6e05bb5e3ae12", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.0.15.tgz", "integrity": "sha512-r/WZQFvqWVGbTaMtVKcMjIGbo3csSrNcaiYKGjHNPvEgzsze3irUAArwxGh8vrzhj9YnnUqcmmNSc40rHJuh4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiiUgUw8AlugWGRaY4vYKWEMYsiN69nFOus0CW+MqWBAiEAyHb404CdUeZv4msNP7gFA/nK/EJgss0SHDdHMhRc8WY="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.0.15", "babel-runtime": "^5.0.0", "babel-types": "^6.0.15"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.5", "description": "## Usage", "dist": {"shasum": "781bfbff99eceadf7cefb13eeb7aef8c5cb87fc5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.5.tgz", "integrity": "sha512-95mYaWFelu9mEs83FQdH7kbwMmaMNv/32TLnSAi/B0AYweUXXaD0BRxqmo97VqPyNKzfEFeQOCC8gpB7f0CANg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGZ6MlUeVXcE09+AWA7TDRJzs9LPheG1id4szlkjguBJAiAxdUouZMsGwfxGis2KGeM/jPSq+cHM1rX2uoY0tPdf6Q=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.5", "babel-runtime": "^5.0.0", "babel-types": "^6.1.5"}, "hasInstallScript": false}, "6.1.6": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.6", "description": "## Usage", "dist": {"shasum": "88dafd1cf6a5e8f89aeb5f76eb1967a07bdeb456", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.6.tgz", "integrity": "sha512-W3Fi2NvTzirU0QkfYGmv0qL3ySckAsnja4VYAUGz8Q20PfzT91/xL187ZrhnHZ4qD+JFVaVQ+KNQ/cbpjQaS8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHQb30+Hf2E2THh9K7GyJmkwuICluCRZpFnr7CPw2HAsAiEAn/BuHFeF6lFf9pIcLNym17cJOuMGJ7IdX6xOAXfvDuE="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.6", "babel-runtime": "^5.0.0", "babel-types": "^6.1.6"}, "hasInstallScript": false}, "6.1.7": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.7", "description": "## Usage", "dist": {"shasum": "c0b45ee85dbbf8010c35325ae0930bd29de5ae7e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.7.tgz", "integrity": "sha512-iBXpGcC2Pwab1RC2VkO2LBPsWNW7AIbgMN31G6FgZ0SCJbNwrRRJxrnBd28bVcEDTFn5L+DcM85wUL9Mq9pa8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcojv9C9CAYxRhGdxwwB+WIFhAfC2UWZGd/+MhLQ3ALQIhAKk6KHU04QdjrIZfJ05R5shtT0OEWFAVZibXueOfzs81"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.7", "babel-runtime": "^5.0.0", "babel-types": "^6.1.7"}, "hasInstallScript": false}, "6.1.8": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.8", "description": "## Usage", "dist": {"shasum": "946e8a721a72b3e25f30b349c7715de1be42b478", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.8.tgz", "integrity": "sha512-BGJkeSol/sgmw4szBqHc57GbmP6zoi9J6WbMWmByvAOEiuza0KcOGJh1e3kbPFOKkKmobdEBgvg/eJYa1/vBqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTcecifnvzJW/hx/eb//UVmE2U5zRNvG53XVCOvVEIsgIgGBQAg9wUDXg8H3hCnARxrOkaguWGhyIoQEc9HXCTBv0="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.8", "babel-runtime": "^5.0.0", "babel-types": "^6.1.8"}, "hasInstallScript": false}, "6.1.9": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.9", "description": "## Usage", "dist": {"shasum": "4ca4ff266e16de69cddb2b56389da9b203bd24d9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.9.tgz", "integrity": "sha512-yxKVzSa5ixz5lDNgFJBA8WHW8ATOOWg4BpKLP6GRDsE6lpixGPsuh36xErpu1ISfKWqKfwkBcfpipgeA5JDtCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEt02mV9U9hOEvxWObdExE7jr8YgnhCEeMqIWunX/ec4AiEAsqcutLs35EeE58DO9UlJx4ZrRelghhj7/AcrvyphV8E="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.9", "babel-runtime": "^5.0.0", "babel-types": "^6.1.9"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.10", "description": "## Usage", "dist": {"shasum": "51aeee8aef83320c5018b74ab502c1ae2e3c095e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.10.tgz", "integrity": "sha512-oDyBVRWsy009c1ZSUkZLanRVLBS2+kN6qburYsUJV3TneaSA71nBiiLbHDYctajVn0a/x1edZijxoly0IqcQrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB97sEafWprUmCxbCs00cQgz10ZAzDVVWVB963TpYmVGAiBpCmtCYrKlHjdi7g/SMrN8PvDHxCPx2V/4qvDubbS1NA=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.10", "babel-runtime": "^5.0.0", "babel-types": "^6.1.10"}, "hasInstallScript": false}, "6.1.11": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.11", "description": "## Usage", "dist": {"shasum": "2facdf14ec850b19d23a14e478b25868c8395b16", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.11.tgz", "integrity": "sha512-pbrBjVG2UA5IOKFGBGLvoS3JVkyA+D0+fmbaUcyMqEkeiL9AfDPMNdTyVduhT+nePfwZ1txDc0RCVzoh2SH6UQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvGKKLziki5sZBsm+tN37OY3AzzslgRKsB2586xUj/RAiEAis2S0AStU9j1t++RLHJmv9clB302E1LjTmoLmYs4qks="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.11", "babel-runtime": "^5.0.0", "babel-types": "^6.1.11"}, "hasInstallScript": false}, "6.1.12": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.12", "description": "## Usage", "dist": {"shasum": "e2566e4585440207eefa3f4446b7d190695ab815", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.12.tgz", "integrity": "sha512-zNAK6OD8uGR+rNSYzTPUC00eDQhBJDF2Gd1VGueTbDM61wNr4pt+MjEIVyBdZ1JFwbDnfHpGylidDWf6fHQ7MA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6BlCnO97QtHkv88OiYpWsZeQpyF76zSydk1ttDMOlzwIgaHjCTQ9iz0/jNYk7QAE0mg9QKlWd5Q9eMva7p9rS5jQ="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.12", "babel-runtime": "^5.0.0", "babel-types": "^6.1.12"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.13", "description": "## Usage", "dist": {"shasum": "ee1eb321cf67aa5273c2870ba87e248dd8fb0a1f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.13.tgz", "integrity": "sha512-rEc/YncIkh3i9e0d8/qrF3+9PzuE3JU3R4OJebtlw6nHSkhzc/2dSc4OoAZF9y/+cDXUP5key4NPPZi/HXkY9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIChbDJWpeknoxGQ5OxdadQVnDvnnnWLz1mse63pztNq0AiEA0+pDgf0c9K76y5v2lBcgn0Q0E4ic8B7h68NvwTZxuVA="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.13", "babel-runtime": "^5.0.0", "babel-types": "^6.1.13"}, "hasInstallScript": false}, "6.1.15": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.15", "description": "## Usage", "dist": {"shasum": "eaf0b9ac95e297570052b8b6083b81e2f2e63330", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.15.tgz", "integrity": "sha512-ZucKMJXDtbHCuKzHZjbuqXFQSe/lyUv60O5/Mx1v7e/7oLRZQ3hzXWvZLFkpOrhSl43ww2vPdtx3EvQ9EYRPvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDChS96Ma5sNIh2tpXUL1xJRCyzbtc1RYIGjlrz+ihuDQIgV3jiCxXW81iZaX1fPBCx5mkvlsJLoT0ccuhW9ixxmAM="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.15", "babel-runtime": "^5.0.0", "babel-types": "^6.1.15"}, "hasInstallScript": false}, "6.1.16": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.16", "description": "## Usage", "dist": {"shasum": "ffef7bb2444bf48bf99dd76a013eaa83170ae425", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.16.tgz", "integrity": "sha512-HMaByuQxgCl5HC72/YidFaeffJEOD5XImZo8zL01hY4Kqkcw2AoFxqZ8A9S94LvhsSDpRzTyYiGPX1o1x1AuKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUddZYaTHRfH9uHwfXukhAOPYVrsAIFTiOobMohdF+cgIgJOeWa4iGbLtSToRO7/TVYv8PiQ70CXueU91ta/7L2Sg="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.16", "babel-runtime": "^5.0.0", "babel-types": "^6.1.16"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.17", "description": "## Usage", "dist": {"shasum": "95a879618aa20fbb6fc70f9f2bef09f6ca8549f8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.17.tgz", "integrity": "sha512-0WVPyd3Kuji/IQbF+ZZJ1MomegKrvDFR6mWB2C269QqZlESrNJiZ7eDEuYv6qSuuaCQIqYfit1kGcoALkPOWJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtgTqbGPEPMKnk8gZLQ2dw8zCAletNl6CWZaHCvx4QFgIgaMNGoRRCxp0/oHauIVFGJ8HpjOSUuuFBGFBjl/keJnc="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.17", "babel-runtime": "^5.0.0", "babel-types": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.1.18", "description": "## Usage", "dist": {"shasum": "7a77bfdd04bc14f9cb6fb5e3d672303a397d43dd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.1.18.tgz", "integrity": "sha512-/sKqH+fuqWmFtlMcM61+SCkQMuBqPxQ1vMVnSeM0vqLKjRhjzVgWAO754CZ4jmyoB4yDNZSouPFE0TjZpwutcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHbSljnRoXL/byF/rY8Z9E1kHfvdwGp3Cbb9PHmUBqVwIgVsBUYomlVj+E2vFQmz/RRjRDknkG9a1VFg8pXrjg4p0="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.1.18", "babel-runtime": "^5.0.0", "babel-types": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.2.4", "description": "## Usage", "dist": {"shasum": "0a610858b26b5662ba3760246ecebe10975f3c58", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.2.4.tgz", "integrity": "sha512-oyKLqnoCXGxbbD9NHtO1sQjqjZaJo+ymoXFT9afijLaNwNtfRD35IVPprDuSMENyQszLTSe4dyYNlzJ7j55krg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUhcQEQ1QYlF5WVveN42MSC6iBuBMCOFbHdbCadOyyiAiEA1Mmdb71ZDw4quT5dj2htKRInJ8u9XlW9stQhJYEXYbg="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.2.4", "babel-runtime": "^5.0.0", "babel-types": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.3.13", "description": "## Usage", "dist": {"shasum": "90a892c4f384baa072dae7d372628f0e5d8e89bc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.3.13.tgz", "integrity": "sha512-ZgWvNL3UyAKs+EQqmYAYx1ENAfsWb4AQRXAc0Odts5qdXvSudHZB2UY08SmU71Gvgk1Z4H7IRctrJarFc6aNvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEI6trFhJcf1RuAD7p7wyXUj7KXnmma8uExegb7qjJBPAiAqm2qB3K+AegAI/138B3pg9GmT6w9RxRVZJUAaySAEkw=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.5.0", "description": "## Usage", "dist": {"shasum": "2e6d99ae9a50deca786e6d57fb4b688f3cf9b32c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.5.0.tgz", "integrity": "sha512-2IS54a8suocVVpd68kavfMgWuvN/TRBlbDmiQiVYHJmWDdSeboxv+wIOBae00ZRwI+rEPu4pNT2oov9+R2WrcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1fmW1vqIvF/dKQGlLRE6hqmqO6gG3h1VsVuoMbJx8cgIge1ZBzR4yHLOb9Xzsr/Pe3SD7x/Lqt7yCH8bOvMw5jQY="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.5.0-1", "description": "## Usage", "dist": {"shasum": "63deb2107829f1716288431fed9e675e64851ce2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.5.0-1.tgz", "integrity": "sha512-AKosAbTL7cQUyUBF5y9A9P3lsxrXTNq872jhg12gYQOxPr3Tw2W5yunYKTBBzAqm2aMY0x1qVFqPP5lBlvGIpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7L4BhqgmsKOoKHy5ZZRnouF7XYIMDIIpqjcWC3pHJzAIgeb1wiDib5flwqxwghZrTAhg36OOQPPsRr2fDMse+sgg="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-types": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.6.0", "description": "## Usage", "dist": {"shasum": "774c279ce9a154f766b516a3137a4d1d8ec1488a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.6.0.tgz", "integrity": "sha512-kMRMm1yw92fFDOzNzyhDYGnyea03UCm75bZ1ck2fVdwuiRRyNPJK6dbEBoLDGH8Vd5wlZd1oV8e9kYP0KEgQHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLEIDs1VWPWitB49z3/lPSTVqJJW2zSAlaKDqlGoOV1gIhAKq5YIXWTT8nxgf1yYUyps0HZZajeQ3nSCxkmK7REOf1"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.6.0"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.6.5", "description": "## Usage", "dist": {"shasum": "e7023d750cbe454749513c77eb6d5ea28b428c0f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.6.5.tgz", "integrity": "sha512-pLuBOZWDl8A+y5ntzOR5JX17ZxB+Vmg7ElBMk6LJ6b+dcZyoq6W59DN3z710GLAs2BgPKyG0N4QWUXZ5KcUiHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6RONVh+couwWHVDf/aAnx6CU/XQlEQlIAJK3l02A6SAIhALgKLAaHKYsx7zfiZCgceILCcQoDmoryqK4G18TnYTUj"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.6.5", "babel-runtime": "^5.0.0", "babel-types": "^6.6.5"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.8.0", "description": "## Usage", "dist": {"shasum": "bb165c0705d242f6c5d98612d9d013544edec593", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.8.0.tgz", "integrity": "sha512-cczeZ0h52FRRZg8cZ4VDU9xVtQl/yxNCptonWbuiWJNvn11UylfxICvKPVpiCtTRneQmaBBiJp5exWqmOPFt0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwLb/wATHXZK2mT2ghnK7iJVipoBpW0PhEtrUQHVJO/AIgWYH+akhy5g55Dlzl1aEb/ol9uR1QBC7ifb1yd8yc+r0="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.8.0", "babel-runtime": "^6.0.0", "babel-types": "^6.8.0"}, "hasInstallScript": false}, "6.15.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.15.0", "description": "## Usage", "dist": {"shasum": "39e9ee143f797b642262e4646c681c32089ef1ab", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.15.0.tgz", "integrity": "sha512-f4ZN1w7s0Ivy1LUQIYy63kPOFkoK2UUu9IqqYPpte2QugxKpPk1ZGu+t34Dq0Da/dRJGDv74cQzzp0+4NaO3Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/+fvkOxmPEiMihaFzP/ko6OoPMwNhdwmgM24gX0Lb/wIgMyDnelKzAQpoQrdWlBavh5fIP+UWWbBa1hpZHn6Piy4="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.8.0", "babel-runtime": "^6.0.0", "babel-types": "^6.15.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.18.0", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "8ae814989f7a53682152e3401a04fabd0bb333a6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.18.0.tgz", "integrity": "sha512-1IMPHTAr8UC547MwfBGFdbiwuheseIETwr2TTZAf5B6ms46vTHwc0UEY3HRqpf+aSwyiWOJGwqWxeqd8FQ4SJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNdb+6ESAvKtRoMfJQzVVp9fKMRo5xNsVV2L1pHRvA0AiEA5JvAVTqHfjST7Hrb/R1TgvMTf+lxhph4/EbpvC9pnD8="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.18.0", "babel-runtime": "^6.0.0", "babel-types": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.22.0", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "29df56be144d81bdeac08262bfa41d2c5e91cdcd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.22.0.tgz", "integrity": "sha512-tDI5JKM9Ro9DAAPc6XailNOYfH5XDdiwUWGclx7pD8RWnXh4R6T5ACy2bYUXBst+lz/ciNrMWUMQ546flvOB1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJJJ2efxxcW95vGn1MbWhXkGfHgEdO8vDxFUfTt7PV9wIgbXBQudDjFAlnih8roN+2nP8LF8UkmjvUntMdb5FZtjM="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.22.0", "babel-runtime": "^6.22.0", "babel-types": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.1", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "53a9c373ef7cfcb93303539c2124d8409277e73f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.1.tgz", "integrity": "sha512-HmgbfxGdIkXR4XFxFF9gU4/a/nMMabar3jCjeanfB+GujQMmrqIvh3gx6M7Sq0pt42n8H5NYuMqPR3ITHToYBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6d7YtLj+JjHvpSR23DthtUJb4UAxNN12w7z8c+mbHEAIgW1EjSt+fusL9RGjG7+RCaavgTrQM5yYa/peBnMrYwY4="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.3", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "36d95f05539d19e56a93f52f147c772367c53ca5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.3.tgz", "integrity": "sha512-7kIREZOjzpmGoJlDhSoHJhKZkXr1DbvDme/4AWfTxTGg2d3sGPsuziXEVXs3AAVCLjCKcdQniMpImo5Lnv9N5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPnzeS3KlNIfPJyxG0KHjdrnqY/2jmhqRTB6dQssOfoAiEAtrGeEzq3wAv/H5MQoHmbXH/fxaMQL9oddlMZg5KWgBE="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.7", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "5c909122947ac486f1d3a84836327e9cd9553980", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.7.tgz", "integrity": "sha512-eeFa9fhM+B9driol4xwlIAOcl9iwb4kjlex8dwyyWyiQbsag/qtRa/FSx2cDJPGF9cbaQ9UmnQnbvBiCEYoxIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICUBWtOrZ5yQtr6NdHxp2hoYNrWqsMkXVLBg/gDJSqmXAiEA2SprZo1+0tZbNTBBNmxeQMRk45P1D6sMDPiWeGFuBSE="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.7", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.24.1", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "cce4517ada356f4220bcae8a02c2b346f9a56664", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz", "integrity": "sha512-gCtfYORSG1fUMX4kKraymq607FWgMWg+j42IFPc18kFQEsmtaibP4UrqsXt8FlEJle25HUd4tsoDR7H2wDhe9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGdu+d8h/bt+aop9bGWTFuwykvTXkHHOmsW1gsMAa0xwIgQXGSnowyAYX+XcjsTCJo1tmZaAbpsbowOZC1O5ZdKeI="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.8", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "38daf02a4854f62ca3cc3accc731faa24dc018dd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.8.tgz", "integrity": "sha512-NOM5/w7T9rrVTKtdzQz8CpzTwypzFZ6t3ftO84tjvwlq3kMZ0RomqsiTPtkYUSJ5v1qP/nyG8ZIgpbvvpKjMkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwR/Z0WD8PjEK8QYcPU6SJ5StwxA7H1jkYNNaYknRY7gIgaMbPEtNtfmocHXl9mg39S3XMyD6itwVFyMDMLkE5Tkc="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.9", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "e4fba3497096df735c50fab945f61917bb4f6dff", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.9.tgz", "integrity": "sha512-extCFLjkpx2h7deQ/EqoGkqHGk8UFErRDjXdhoLnfcRwZ4eIpcU6d+b5GsWnIQqeof7WwQYG5HNMpvvOsRMt9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6i3/LRIjMqBjcH1tTcOAVZE8eBgmSQthruNvCUYjIjAiASRQ0OEWGLgLHxrUKVsoLmHqV6zWXiWDunMo76jPT2jQ=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.10", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "7a9d98abc232291e8abf3bb17a0a64f8b7e4ce45", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.10.tgz", "integrity": "sha512-Wvk4Jtf0Lnxk8ueVOiC/CoIJESPDgpH5Hprfy26X0KfMKf42irZZzKacUqiq7ZSsuUadAhLHfAFJziVwGr7E4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHnQr7kZumnBfEZb52zPtOiZNL060PlUkwS2txdidw9dAiA1H1fePYdcdfxSY3FLW1hmp3pDyjKEkyz269oqKmZm/A=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.11", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-vMQpLio494fg9VsOcPHF+c3sNYGNt/OOQucYGq3GpfyuzXHGOF61zFTgHgkeIlNRQXPZjj9tdoChYr51JAMdBw==", "shasum": "b11933488c4e9c6c285c4d6c7a5515a662fe3c23", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBxft9E++RxbBh+KY4G/6OmjtJJDmeIGVdCUfJkxl6nUAiBecs+uL7zMWPj0C6PL+4IcIbDBUSyE957qDsLO1QJd9Q=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.12", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-XAkJAZnCCjRJiuqIPw+ECyS7y44wJEe7qLRJ5mW+2/UkDA3aysS5Rsh5+w78Bjv3veFCJn8dNbqRB8PiJWH7Jw==", "shasum": "60b19d499ef29cc9e52e6a5cedc5809a443278a8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBrWDytRIa3p1D+EnXYHDRdLo1kDqmGp3FXEMHDdWavuAiEA0mqq1I6SdQ9UC1mEtK9WZZ62l1vRMLVKPYvfDTWFsTU="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.14", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "a69c99cc28d1f3846447fb8732d058fabf11afbb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.14.tgz", "integrity": "sha512-Tjd2UMQq0wjNviwzGriXELnj/reO1g29wc6OzOOpC4xwD3UNxwkEf4bYnpeDWUwIVipmyTwxonFsH+61x++6Ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBAE9M5blr1aOnHw12q8gYyfgA9+8NBMRb0kthBzFP4bAiBrgnwGa70osp+AoM5SG8fsHklkAy/OkoGzovad3Dudqw=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.15", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "67104d0ca28fefd3fd5cbba8b7f780144a116a7c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.15.tgz", "integrity": "sha512-zmyDyP3W8MHwTw5/fhs1Cjxb3mhadkpxZH92Wh0TCXD4Jd2WQDB56HagWX7MvhOWckJJ7e8BUypFeviuQabKZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+zmzqTDd2QPMOz9wwCl+4cUZVxcW5cn6rwb89UkcA4gIhAPcToygtbWFG337ajpc9TJI8eZB/72FMrwktmB5jJyZE"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.16", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "31c66895ff3de0857fad45760bc2e569ff1120ec", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.16.tgz", "integrity": "sha512-Wq/TWnXeFZ2s5/TPIIaIzOJ76PMZT6OdiR7/6Y7V1wkr5zksd+Ve+1dfgTkadrKEL3AYXq8FDZq6GRWV7IG4bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2oCnsl6/ygR28Ju+U5bse+5VBjYH3yBR3f23sWnK3CwIgT4rwzq+gO2RHa8SkUuG4KT908RBOO2xTsGnyFw9Ous4="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.17", "description": "Helper function to build binary assignment operator visitors", "dist": {"shasum": "422255eb0fbd3d6090b454aeeb5959f6dff5c3d9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.17.tgz", "integrity": "sha512-pDtT+g/s5lgx6DDbfs5KbuJRz8x3FnWx8gWHWmajpT+6bx45ofbdndlwuvvihIr5m4OiQUQLh0Wqt/Iew79pVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLyy4UX+xbp9ro4BMnppKgMxIGVvgPDgT+fD0GELPgngIhAPgCKuIbtSpyig2FupzrGg0U059a7KeRqRqtYDpe/CFl"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.18", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-Lnh8RjSrxM2xEGF+vQ5zePQROLakOxyydDd3FjKOUb5YZtY990q2iqt82OJsWQq+ogaNzvSIqJ9MN0+b4RT0zA==", "shasum": "a596a17aa1afa4557e69b9d41664235805b6cf6f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGeW5U8FIAlNITerFDO5eeu9eRV36jmVpYMZQFwmknf5AiADJUVtOXDNNcOH5n3HVi73SxSOFOjCiZCZsEqYNaVJhg=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.19", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-Kgzqk2IE3ZZfIT4q1t9wYXof6UdyWeMKSvaZ3CCdl7LK/d0fZ40INuMraTJM7KIfkzNLBZ3orbJ+XT1wh6gYJA==", "shasum": "b1f685023ca414bf6edeb6d4c99604523fdb6d6a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvROG1+yThpJB/ykp7N71j3jbZyj/wYyMFz8B240Kw4AiBTUZonZ3yWDuHapP9zDTGFIDn6mr3/jP2/Orq38st1wQ=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-alpha.20", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-p2C1W/QCklBD9Gk53BaRfqOUoRWEv+FqtMjunimSLPl55bHlLLiSLPbJ+wn83cUtRTjBAoUSWnQdZBLY4I/+xQ==", "shasum": "0fe2ec68b5a48a1520568b95a391c680d672ee19", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSdbrmGXBuCQsmi3sfes5R6NLu5JEkW0M5oPNdroFfbQIhANJD/BYhOMnNoCRlQAnbIB0t2i517sKoOBidObLzFbvW"}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-beta.0", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-8qCbLCB4FIPQORYhcl1BTht5JE5KvPl4FsS0dtAxfSwp4n96/Haip2H8SCBdwqVqwCZBbwvZ3eY/yyE/BLugow==", "shasum": "693fafa04de4fae7ab4b1db1d1a2432a3d854508", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICW8AKqJXyMfAD0heoQ1CZ5r+TpFyqpmIBxaIsjkQJPYAiEA0816oXFlSnxD40rfjIR7mcHlhFMsFaqWMIrWW04lq/g="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-beta.1", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-tz8qLEQnEmKCGgpuzkGKGaWtqvudKZSn2hPa2FT9U6OA4O6jAwMMBxEPxxOaGBK/hHUlyArUtUL66ziVPuX0xg==", "shasum": "7df91f4764ce1462d84d842c0d9dd685e6a30c13", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpx/nEJgxp+KkONHBxy3/gyUJW0hxFqMWHVDXaOFmpHAiB1GrYzYsimCRlG2jqXfJaKzJCPUHa109YNUtkCX360Cw=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-beta.2", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-sjI5PTdi1uqFMgLBb/EbQ5bs3owL0hxZIyXLcDYFrovI6Ks4UrshZC6ffBMX5VtWQdyQdwDmzirMLNqkp4FDYg==", "shasum": "a9d510e27835c25c3de167813abb7a655c3c5a57", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICGVF/1qyhlTLA3mkFd/fUG378g+G1B4kCHcOmJxTTiVAiEA6H/m+3w0j39EOOLoDj8oDpqA5H+mhhobpZq72qQQM8M="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "7.0.0-beta.3", "description": "Helper function to build binary assignment operator visitors", "dist": {"integrity": "sha512-faPNvJ2w/YLRN0pD3mX3Ro/Og+uBNMdwPXg2bq5xXERQCyuYWzAmVbKnjGNaI4CLPDEbAZiZpq+AlMO3cT0QQA==", "shasum": "2066d342b96c2874db1f650443438ca15b497e79", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGaQAuIFSSU5y2dVlVUYlpVSCWkjqUtCSBKC/JBg9gESAiAuIykKQKToZVfq+b5CYAlm0aUSnyiOGPBGL3IXdBz1TQ=="}]}, "directories": {}, "dependencies": {"babel-helper-explode-assignable-expression": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T03:59:57.392Z", "time": {"modified": "2022-06-13T03:59:57.392Z", "created": "2015-10-29T17:53:45.824Z", "6.0.0": "2015-10-29T17:53:45.824Z", "6.0.2": "2015-10-29T18:08:12.777Z", "6.0.14": "2015-10-30T23:32:04.663Z", "6.0.15": "2015-11-01T22:08:13.564Z", "6.1.5": "2015-11-12T06:49:52.249Z", "6.1.6": "2015-11-12T07:33:26.517Z", "6.1.7": "2015-11-12T07:37:58.425Z", "6.1.8": "2015-11-12T07:40:59.825Z", "6.1.9": "2015-11-12T07:46:41.721Z", "6.1.10": "2015-11-12T07:53:28.425Z", "6.1.11": "2015-11-12T07:59:14.362Z", "6.1.12": "2015-11-12T08:48:29.529Z", "6.1.13": "2015-11-12T19:58:16.710Z", "6.1.15": "2015-11-12T20:20:08.738Z", "6.1.16": "2015-11-12T21:33:48.313Z", "6.1.17": "2015-11-12T21:40:53.778Z", "6.1.18": "2015-11-12T21:46:58.670Z", "6.2.4": "2015-11-25T03:12:38.594Z", "6.3.13": "2015-12-04T11:57:04.601Z", "6.5.0": "2016-02-07T00:06:51.360Z", "6.5.0-1": "2016-02-07T02:39:38.626Z", "6.6.0": "2016-02-29T21:12:25.660Z", "6.6.5": "2016-03-04T23:16:28.737Z", "6.8.0": "2016-05-02T23:43:56.991Z", "6.15.0": "2016-09-01T15:03:10.082Z", "6.18.0": "2016-10-24T21:18:42.770Z", "6.22.0": "2017-01-20T00:33:28.733Z", "7.0.0-alpha.1": "2017-03-02T21:05:43.088Z", "7.0.0-alpha.3": "2017-03-23T19:49:44.220Z", "7.0.0-alpha.7": "2017-04-05T21:14:15.035Z", "6.24.1": "2017-04-07T15:19:19.637Z", "7.0.0-alpha.8": "2017-04-17T19:13:10.884Z", "7.0.0-alpha.9": "2017-04-18T14:42:17.713Z", "7.0.0-alpha.10": "2017-05-25T19:17:42.024Z", "7.0.0-alpha.11": "2017-05-31T20:43:54.961Z", "7.0.0-alpha.12": "2017-05-31T21:12:08.662Z", "7.0.0-alpha.14": "2017-07-12T02:54:23.051Z", "7.0.0-alpha.15": "2017-07-12T03:36:40.414Z", "7.0.0-alpha.16": "2017-07-25T21:18:32.667Z", "7.0.0-alpha.17": "2017-07-26T12:40:05.637Z", "7.0.0-alpha.18": "2017-08-03T22:21:35.345Z", "7.0.0-alpha.19": "2017-08-07T22:22:18.181Z", "7.0.0-alpha.20": "2017-08-30T19:04:42.586Z", "7.0.0-beta.0": "2017-09-12T03:03:05.964Z", "7.0.0-beta.1": "2017-09-19T20:24:52.941Z", "7.0.0-beta.2": "2017-09-26T15:16:09.127Z", "7.0.0-beta.3": "2017-10-15T13:12:34.617Z"}}