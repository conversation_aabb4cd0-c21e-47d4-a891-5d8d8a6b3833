{"name": "babel-traverse", "dist-tags": {"latest": "6.26.0", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-traverse", "version": "6.0.2", "description": "", "dist": {"shasum": "132f21f46001f3ae5a53fcc8f8d46bdd2af1765c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.2.tgz", "integrity": "sha512-EZ6xjKzh04xsLGECfwyKP21axCtZbJ39fagu7jO3/4yyiqaUfO775EktOM8vjhIV6cuyQNYSnRURIzGU54vM+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCQPeUUyXC54RV+U/Ehr9NXb1T4Xygx7c+j+dz9boFFAIhAMFG284SnJ0a2i1A8Hy2vKHqvQ2KQCL5jMMNjtE11EqE"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.2", "babel-messages": "^6.0.2", "babel-runtime": "^6.0.2", "babel-types": "^6.0.2", "babylon": "^6.0.2", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-traverse", "version": "6.0.14", "description": "", "dist": {"shasum": "1d0abd3834e9c93ae5d22b2bec8877bcf33306c4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.14.tgz", "integrity": "sha512-jJ9aSgYWpK0pXDVkv3ndXnNf1f8S7ta/Z7NuOCfk4V9YnsU+Rk+W6Q92tsctK1KtqzCOW5yn7WN7KSuNBp45Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTcjbsQPcjpePie7PjAJFoF5P+RGTXE0oK58U8ayJeUgIgRlNXhe7K7zRzvqsDlU8yUPHj6t88eyzm9+uS0mwqd1c="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.14", "babylon": "^6.0.14", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.16": {"name": "babel-traverse", "version": "6.0.16", "description": "", "dist": {"shasum": "2d539d25e07e186b158704bc042a3445b1b248f6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.16.tgz", "integrity": "sha512-wzmyS44sGQUJDUQxB2D7bbXApym52Q3hne82q/H1Z5nEFJQyXwsU8uo8BxJRl6zAZS39zdOCgPsDd4fKfQbthw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNxgLoQzHOmNQ8eN2EXvlPup3KXt8Zrir+0F4LlCpYAAiBA1pvRJhhYmA81Dg7qZVDY9Me7fMbGjDyE8o0UUeXIKw=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.14", "babylon": "^6.0.14", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.17": {"name": "babel-traverse", "version": "6.0.17", "description": "", "dist": {"shasum": "183c1b288374c804f55b73a1279fb812ae9e5f22", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.17.tgz", "integrity": "sha512-iY/JkmWVuGd7Djn9jjI8kyrGdcnHe43sGpTniO3xk+qz1Gakv9fJqBbbktArJA85t4qbzTmhdcppkqVcthnU2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvOCgBlORs3XwN6lNm2mBJxLaSzjxgwPhfAfP30aT+qAiEAiyj/Tz3b0e5spWQHWnLbbsbLMdAyzrFqZAX9UsuSCxo="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.17", "babylon": "^6.0.17", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.18": {"name": "babel-traverse", "version": "6.0.18", "description": "", "dist": {"shasum": "e781b022f17a4c11e5d4b4a8536017eb447528ac", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.18.tgz", "integrity": "sha512-heWtUGty82yOqdLpCpNCS1su+ig/8rxkqpuLWLU5lHhycN7uYgB62jTLsb48SUqAj3Nr8L3D9PAUQn+D/Xzr9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3lpQMSbkhWp68FO+c7Wdc8/SoO5uWhqVJPMgDOIItqAIhAP903l/mSLwtY4kn3wpawZURRlApRb5+3Z6aVpqthDoZ"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.18", "babylon": "^6.0.18", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.19": {"name": "babel-traverse", "version": "6.0.19", "description": "", "dist": {"shasum": "609692e76d6ba8b2113e3dcc6666585e203dfae3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.19.tgz", "integrity": "sha512-LZpIzEFJQVtOjpZhlufDyU3rMF7xGFXLVLFzGJT/B3ikIE/vOiACcCbQEwwR5qP6NbZIFUKoAo/rAT/v0Y9y/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG5H9QFaPwVl3pTkE1d7BIKlpv7dXsEghttFD3EaK92qAiBdC0VTDenlmSqyFzc9KVURUX26dkpPKsM7tbZXXAZlXQ=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.19", "babylon": "^6.0.18", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.0.20": {"name": "babel-traverse", "version": "6.0.20", "description": "", "dist": {"shasum": "09895a7ecbbc9e2c0f5fd061bdef1054124178d4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.0.20.tgz", "integrity": "sha512-C/PTw1UE1FJ6kQxplJRAD8TotwdEUCYkqV4z4Mn1lRIvmV1dg+pJwNHr6VoGdNdBEJ/ZosM2LQ2RKiZHqKRReQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFkvNWDU5wECsbK4VvxagAN3WGjlnPLb3F8tfYAvQTRwIhAMoiQEbshDWkcf6cZ2YWjIiiKieCkuQROqccb5M/cHuw"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.19", "babylon": "^6.0.18", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.1.2": {"name": "babel-traverse", "version": "6.1.2", "description": "", "dist": {"shasum": "d79da3c1514a8256d88c5dcc51593479b44e03a3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.1.2.tgz", "integrity": "sha512-GEt24uOu3ZE+MDrABAF0iS/1CRXc03uSHnRC69z0gigomg8vp4vFYj2kxdBGgIuCq/xSn9w8mJ8er9oTJE73Xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyx8U1CTV3a/eArk5tskkmECqF208eDCZVkjdDT8LP2AIgDhwSXj5Wh41PH1VnaFlNwWmlTZYFQm6X0Q/g3svf+9A="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.0.14", "babel-messages": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.1.2", "babylon": "^6.1.2", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-traverse", "version": "6.1.4", "description": "", "dist": {"shasum": "b610cff115b0c5aeac6fab201187bfa8d5368223", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.1.4.tgz", "integrity": "sha512-4nY7tB7aSoWk6tVoBN1qeSAa1fodDm7Gdv/JKbFugFctyTIu4rY/mYjO7qZzNT1fsBcwGXsbf3hr+BVQAHyAuw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICef8bUuNxcHrkpBJJePDHSkgF6wkSIn4U71iMQhwtGuAiEAx5p0PWdrXpApxhrgKrlMa9fGklUq/KyF/DpWpjMBoVg="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.4", "babel-messages": "^6.1.4", "babel-runtime": "^5.0.0", "babel-types": "^6.1.4", "babylon": "^6.1.4", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-traverse", "version": "6.1.17", "description": "", "dist": {"shasum": "0179428dbf6d4f1cf49fe7f74cd3fe594785f5b5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.1.17.tgz", "integrity": "sha512-HaIsxpIWjslRWOAxj9OhP77OK4x2eU0kZnfkh7zuJFUuYe6DpwH2eyNs2UZWQqM2uhJFMfq3e4CsFP48ttiQkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHjrcUxRQ0LxzKAkoXINpsOES9E4yXSvdFMlI/Ngs0kWAiBT5xy6REAJXM+SfkDoOIQKbQTDAeafYGDSS54p6UKFmw=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.17", "babel-messages": "^6.1.17", "babel-runtime": "^5.0.0", "babel-types": "^6.1.17", "babylon": "^6.1.17", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-traverse", "version": "6.1.18", "description": "", "dist": {"shasum": "e558fc0b5d11131a0634338c1d4fa6ec5bb45aad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.1.18.tgz", "integrity": "sha512-CyK4k2hFr2XVWLBSZZKQZfrtPl4EZB0RNts7Im+7VjnDT28t/4CawLSFhmDyxz5EFT2gYWX47kdoKhbT6B3+BQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHFrKrQOOPQC3mCcoii5ArZs6v8+kIga4J0kT7u6nfNnAiBgzK5TZ2isqAvN66rrC3st9k9ZoEdGuWuFyMuIELeeBw=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.18", "babel-messages": "^6.1.18", "babel-runtime": "^5.0.0", "babel-types": "^6.1.18", "babylon": "^6.1.18", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.1.20": {"name": "babel-traverse", "version": "6.1.20", "description": "", "dist": {"shasum": "0c94b2b13ae5b61eae8fed1986a455b5d51b57a5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.1.20.tgz", "integrity": "sha512-I+Wpk0TvGLgAA2NmBaCnDGa8Igf1R0fDPRfcOP7JqUUhWr7U4JSjopgqFycFYoewKKYfGUH3wBvNv0W3sRTlRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3lQqsEQoe2+W6ls4rIY1nYM0x29mJcTJ5kN3SfuRcgAiArBQHaM48oufBQlXvu2aGz+FuVIIoyVPFt3E+73J7SjA=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.18", "babel-messages": "^6.1.18", "babel-runtime": "^5.0.0", "babel-types": "^6.1.18", "babylon": "^6.1.20", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.1.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-traverse", "version": "6.2.0", "description": "", "dist": {"shasum": "0347d4d8f5987ce1d9165938578ee6c51499cddb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.2.0.tgz", "integrity": "sha512-4+j1wiUCws34cRdLJWR7rFIcV6G/gMVtimx1Mf9RvKo2cp6ypMYMgJ4HmU5/XkEPlMckZjxnFqu8TAPaRv8QRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0CI5Ft2E61eGX2kza45+/HSWpW4Iq84rt3S9B6VMMNAIgVksb3DEZOaS0XsCDGoy7HtYxtY2wdTuAHxXM3OxkfIY="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.18", "babel-messages": "^6.2.0", "babel-runtime": "^5.0.0", "babel-types": "^6.2.0", "babylon": "^6.2.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-traverse", "version": "6.2.4", "description": "", "dist": {"shasum": "f87c85d3a27fa7325812b6f0366470899c4bcf6a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.2.4.tgz", "integrity": "sha512-M5qd0GDLlzdN+69qYKT+pXFR6GNjkUcpPJwFqdF4sVqdUuo60T+wB3CeI2eCBqLb2ktzlDmgswFm0G61pa1iYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgaIJ0CduikJ/3Ph+aTMmZYXFX26Hschvw69s+pShMBQIgThtTOhZvf6gWcoR+CcnrXDtPfbbwvNNW7rj/auVtfNM="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.2.4", "babel-messages": "^6.2.4", "babel-runtime": "^5.0.0", "babel-types": "^6.2.4", "babylon": "^6.2.4", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.2": {"name": "babel-traverse", "version": "6.3.2", "description": "", "dist": {"shasum": "0d850308a50595e7bab1815dbfa67e41fbc0b939", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.2.tgz", "integrity": "sha512-Me81DsyvzjJtkkmmndcdxmaKNiW2O+iW0BZAgzdjdZc5VLClgU0C9+2g/zgt67zrYuNmPRU3FyvpS9jl2Jo+gg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/g5jdj2qpuy+C5uNrrknXkSIdpSMg9l8PGrxAlygu6QIgXonVzHtznMjtZgJnPT/IowevwzuTAA0GzciVtCif8Z4="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.1.18", "babel-messages": "^6.2.0", "babel-runtime": "^5.0.0", "babel-types": "^6.2.0", "babylon": "^6.2.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-traverse", "version": "6.3.13", "description": "", "dist": {"shasum": "5c10779e53f87d7fb149cb05a86bd103bf2fea73", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.13.tgz", "integrity": "sha512-kh0ZCOd0mQnR70RlL+LMTB+txi9t5xJD6reXMGRfo6XrgFe2dfrs/U2yzdrCCMAnfiqom4jz6uJRys4okgQPdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJI0UYG3cxOiF4kxFEZFNRFVl5IP4gBRKDeVfNDjW8HAiEAoDYqoSQl9uLXBUqohYuBD+fU1yDL/kS508R5QkEAt9A="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.13", "babylon": "^6.3.13", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.14": {"name": "babel-traverse", "version": "6.3.14", "description": "", "dist": {"shasum": "1089827a2255245b12bdf5107d907e0b97064fe2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.14.tgz", "integrity": "sha512-K4AZWtX6Zr9W1Nj6dYcOPx0qbAkbvLnWsO12QKAi8UIizYhoN4huc9ZoG1EAlftc3znuWWNJyWetwzWKfLjV5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLeMthFrzFpafew2ABHWWI2YgueLrgbuVrNRFj5jLJOAiBQbQFbPCKwK1FogAo0tzIzrwz9itp0uyB0CD1saMv4qw=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.14", "babylon": "^6.3.14", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.15": {"name": "babel-traverse", "version": "6.3.15", "description": "", "dist": {"shasum": "3348bb4e67db1ebb642d4809a577cfa980c04f35", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.15.tgz", "integrity": "sha512-PGvX142L9w/k5wDfR3TIXQjao4zPwI9Do+tfqQ/uGZ4qN2N7M0LTwLuv3XTI3JSqX5KMS4sSzELiujC86dNitw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC54znNiL7FydAZkLeVHST9pQod1/OAL2sRsNVRCt+W7AiEA8kx5r336HFqxc1FW4VJVLR64VRiINlwtjxPaCx5Zwfs="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.14", "babylon": "^6.3.15", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.16": {"name": "babel-traverse", "version": "6.3.16", "description": "", "dist": {"shasum": "8553554f1399e6531892c2bc43b158e6c1b555d8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.16.tgz", "integrity": "sha512-+l4VaH0Yr2ux+NS1cc0oBUqTu3a86ZLloUQ8wpbR75k7OaZjMf4Ef2lpBebZVmTEH1npFUgGpKJP9sUCkJKXiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7itsAj8jn7k2E5swBfPcJq0/tqeRfJiEdcFeUaeedfQIhAN2wQkMtvQMrcAaKILN+rgp9GRvrJBXZ0YEZ75rpPMkL"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.14", "babylon": "^6.3.15", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.17": {"name": "babel-traverse", "version": "6.3.17", "description": "", "dist": {"shasum": "3ed809918589fe545c899a87ae8678b82c029a12", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.17.tgz", "integrity": "sha512-KDMtrfdN9LsoGrXb/vj+x4yuzpistjKJQkQHchCfd8L00LKcpdw4v/MtiIPo5PSVjgKDpBqY3QJSeqaNFIQoig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhVKbXDin7dWlh98g9XqjHcW5y4iq+KWEBiSyOz2JoewIhALsqb+Vd23DXHscifz1grQohrThklqOKAz4GRe8jhrg7"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.17", "babylon": "^6.3.15", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.19": {"name": "babel-traverse", "version": "6.3.19", "description": "", "dist": {"shasum": "66b5ab5f272867e19ab82e21a9d8273d5ce8e7f0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.19.tgz", "integrity": "sha512-tCBGeuNh4FJThNBaf7IhGkqUtAC5rRF7GGu+J+lOzIeq+P8L/5urGgBz0M19WJVw+I2Rsn/i2goK0LLu/MXnGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWkf1JDaudYaNjfuI30AJK4BI8bVbCxHdgln1KIC2wCAIgGZrs2QUo1iDAJqS0bzjVxolwGCPnLQMn4omwH3jdB58="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.17", "babylon": "^6.3.15", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.21": {"name": "babel-traverse", "version": "6.3.21", "description": "", "dist": {"shasum": "108ad2fdc0f9527da798cb6f43485912e442ece4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.21.tgz", "integrity": "sha512-nHJ1AqLeUwnCmHfxKXw5XQwOTt5q5ytqKm0+3vnfzRf6rNGw/twqe9dwQry77KQZ046jB7l81e51mJMfI38kUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDQGlIp8MTFPtk7/r9uW72X4NaFcS3wHTMM46Ff3UKJ/AiEAmznfkA7lZdz9Yeg24YkSbkDqTsQiSYtpQgHypk7ZJ5M="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.21", "babylon": "^6.3.21", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.24": {"name": "babel-traverse", "version": "6.3.24", "description": "", "dist": {"shasum": "7f4bbe207bfd165d38d737ee2e5d246c6a45f413", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.24.tgz", "integrity": "sha512-XoHn56o9rgEAnadRw6IlCkweExeP8R+e/pcxG9mSpdAtcGAzCilGBJUhypUacU5LXRb9fFWtZ66H8t0niPGQsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDx04RTocJXAHY/Kk6V4A39dKkTQce7TiMhe20r36u/cgIhANXugQyWaFFr9M7MUan0nx9lcGgilh6fMaTKqvInf/3x"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.24", "babylon": "^6.3.21", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.25": {"name": "babel-traverse", "version": "6.3.25", "description": "", "dist": {"shasum": "0e01af3fc55a883f5e733c7e81bba0a299e9e577", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.25.tgz", "integrity": "sha512-fnPXd32Iyq/t/HF6eq/YaYdXi0KvjkseG9HECtsZmj0GW/qTX2k9HLoGFeX8A7hFcun6wbnfy3hFUUUsPk300Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBBjCZMs3Vx0Vz0TJQ/9kqk4a+pIU6WCHwzJB9LvdjbwIhAN57xHApLlVhrmjg72hO6lzCdCdUjSrCeIj4iT7oSyKI"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.24", "babylon": "^6.3.25", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.3.26": {"name": "babel-traverse", "version": "6.3.26", "description": "", "dist": {"shasum": "7b9c62b36bdfcf80ac06c327d3384f4e0e046047", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.3.26.tgz", "integrity": "sha512-2/KXVKLkZKu+t9AxwVfVmuTyvg3U4EXkhZTXIi1MoXInk7QLtadtCVRtnRXNIazhOi/qif6P/otuWifRynr/4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDx6baiQl9XrB3eeATW/LBbIgR+Nn0f0sJCqZGlLP6U4QIgfuh2kD9nh8mezgyq9r779MQ7DrcxK3a7f/p1sigj5DI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.24", "babylon": "^6.3.26", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.4.5": {"name": "babel-traverse", "version": "6.4.5", "description": "", "dist": {"shasum": "84b95416ee0895f1e423cc59376f35bfb08884db", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.4.5.tgz", "integrity": "sha512-wnPk+E2wc8ZSuPG7LjgDy/eAIwZo7sm1Tor8BBiGzGLNs9cU982bY+q0YxRR5baDZSkcYZ6rfaOVQzHoYfjpvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqXHqUh2ay0Bm4H4jyx7rgrLGRayLnqANhLq5UifcraAiBEeOIh4b+uct9aQNFbBbA7+vqB0l+f8KBLbq5fZ4MssA=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.4.5", "babylon": "^6.4.5", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-traverse", "version": "6.5.0", "description": "", "dist": {"shasum": "d8e510ccb9c5c4b8b2f4b0285562c21e3bc703d3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.5.0.tgz", "integrity": "sha512-HAeqMf31l19PafvK59ruZt0sU67rvFRJfDjypMPe/2JifCA9V+qqMpec8kkD4LA4hy2g7jLa5TD8A6qz/Uua9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcJ09Uo4Sf/XOr8jqfEjf5qZ3JsTjzTUHFkJakmYXiwAIgPTHnSyljjAfwqXKoXcxeMc4yT9/XBakStGa5lQAlpek="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.3.13", "babel-messages": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.4.5", "babylon": "^6.4.5", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-traverse", "version": "6.5.0-1", "description": "", "dist": {"shasum": "396bbb2affd42b6652531efc48688aebb11fef3a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.5.0-1.tgz", "integrity": "sha512-//PZRFaYvvFIwLB4ygSBPfa5L6aQpqzhQMCRLIXtiweDA/z2KY7RIJ64ikX4XLMDJI1OO9T5H1buX6lJB40JVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWB6mqMiBIUx4wTNxOnhBqv6uL01u0ZVFtLsiv0UCGVAIhAP+WXrkhXvUnY39bZzY9nTVeLoMn+1YJta7voUtttUCs"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.5.0-1", "babel-messages": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-types": "^6.5.0-1", "babylon": "^6.5.0-1", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-traverse", "version": "6.6.0", "description": "", "dist": {"shasum": "bd2201519feb6378349035e37832d63a134bf3e2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.6.0.tgz", "integrity": "sha512-Y8Foe83/muaF9wtwKXGKOYbyOgD7EtknmdoW7lmDcT/7LA/fBysx092BzMGEAoWv8i0Gj/QM5ex+IT1Kr/Bisg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDYobAvKv/oPkV9i1bl/3fMy4qJxYxkF2N437Uc1dp5AiAYeFeGn/W/A56/UeNzuYdTnzRa1tugGbcUSb/7Qyf+DQ=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.0", "babel-messages": "^6.6.0", "babel-runtime": "^5.0.0", "babel-types": "^6.6.0", "babylon": "^6.6.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.6.2": {"name": "babel-traverse", "version": "6.6.2", "description": "", "dist": {"shasum": "a935614d7e7776442fbbb6898d354ddda8e2bc69", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.6.2.tgz", "integrity": "sha512-elK3Lp8TY5GAMNsxQwD/Erye1jMPm8Sh7lNi4jYseaOwVictd4k88dkX5UMeHZWZKsM801xrsWFVhCJjx001XQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrV7qPnEDN54OglWT5f5uE3Rbwlsr9Hf4CA6ehn2Am/QIgTHLNiShgPPCs2UtWueo8MWmbtQzsorVn+VNX+GSLb3o="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.0", "babel-messages": "^6.6.0", "babel-runtime": "^5.0.0", "babel-types": "^6.6.0", "babylon": "^6.6.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.6.3": {"name": "babel-traverse", "version": "6.6.3", "description": "", "dist": {"shasum": "bb76f8dc894606e5d4308407c0228721d0df4e5a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.6.3.tgz", "integrity": "sha512-FrGCArqJ4DKTdObywuv8BTfGKf60rfv44DWjBQODRgdNis8Yp1vbKTDKIboM4K0XMp4eh8OIZ5G6syBif3z2IA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaX1j6cST0WUm3Cyb22LcOMQEhsX1NR51nMUkWGeoQ0QIgYWIl9SKS4Zwqc7QF0oCW2Ua9tVtrQhWW3dPteyRLx30="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.0", "babel-messages": "^6.6.0", "babel-runtime": "^5.0.0", "babel-types": "^6.6.0", "babylon": "^6.6.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-traverse", "version": "6.6.4", "description": "", "dist": {"shasum": "c319c0fbacff36fee4d253aa3ca28a06c4a22704", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.6.4.tgz", "integrity": "sha512-7QUEO+XdOK1K3T9YgzV8GLzNo5oFp2vkt+KC6NdbTS8S2x7tpvl/22etfMbbOhC7OCuqMvFlHfvOJ0lHM+qkIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDbQaeq1D04sJIYsn6Qk6pSdGrlcfoWmRlhNo1w/GBtAiEA65MSuiF4tCSMMyTDpauUluC3FHJsP2QR5RMcEF7EtDI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.0", "babel-messages": "^6.6.0", "babel-runtime": "^5.0.0", "babel-types": "^6.6.4", "babylon": "^6.6.4", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-traverse", "version": "6.6.5", "description": "", "dist": {"shasum": "ade61a636fdf8bef7465b328c8c6f73c32da79aa", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.6.5.tgz", "integrity": "sha512-DFewudS/GBGKCTCFyyNFiMENCK3VEDdF2pFOQmD91IiViVW7dhSCpm4uAgVqyf2zfEXKzsjWsHNwklA45Trlyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICEOSHoqn7o10Q8lUowIyg2PJisuefHfxv/8NZkqHnpKAiArDAezNdA293+KTVBZB5feks23EEo0Q8YNdPpQLE9ssA=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.5", "babel-messages": "^6.6.5", "babel-runtime": "^5.0.0", "babel-types": "^6.6.5", "babylon": "^6.6.5", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.0": {"name": "babel-traverse", "version": "6.7.0", "description": "", "dist": {"shasum": "1df98b1f83fa70d12671695464f171034691ede6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.0.tgz", "integrity": "sha512-PkRw/p6Es0hsl/2u3QUzdn9/4DIeuYcZhOZ0GmsK+VPoF0qYjxuPL4ILHZaSjBduMPhkQUXxxVr29Go42VpgSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3jlUpi2d5YVm0KUViRkhbhEpNHXQJLVTosdej2jXPZgIhAICD5ixd1Ro43SVteDXKYEppHdKLoHgCL2qKOn35aa9m"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.6.5", "babel-messages": "^6.6.5", "babel-runtime": "^5.0.0", "babel-types": "^6.7.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.2": {"name": "babel-traverse", "version": "6.7.2", "description": "", "dist": {"shasum": "8b7edd004f984be75fd4cdd889b98973a2f5f380", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.2.tgz", "integrity": "sha512-JQFXdvuAKLO6gp0btWJRaDPj6rpDQKZoSFc8nxszXWs985XCITIK0GmjAH3QIKiFPVfcpVt36184teXXnp2e4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyncN7W2LoR1hFp6WUp1Xm7kibgVB0nlQ/sp7B1vvBRAiB10xuvloinJM6/286+kQ4gvLwtuxzLTUDkVra3Gbrquw=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.7.2", "babel-messages": "^6.7.2", "babel-runtime": "^5.0.0", "babel-types": "^6.7.2", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.3": {"name": "babel-traverse", "version": "6.7.3", "description": "", "dist": {"shasum": "9f5f12093c82edeb2db0749a8fbf15aa5d401ccb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.3.tgz", "integrity": "sha512-94mgnL2emtNsR9OoGmgiCVaMVgpiRCfd9ZBb9cd0nG1FVJYhH6/1e2d+33yta+vkaS0Ev0i08FtbrksnG3EUWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClsPptmqjMy9+WrB9RqA7PlKYOtwLPakbChucVSRQftgIhAPbAJFq6KqpfOe9DYnhDrgQpmEP4Y05ELhBVrD978J7H"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.7.2", "babel-messages": "^6.7.2", "babel-runtime": "^5.0.0", "babel-types": "^6.7.2", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.4": {"name": "babel-traverse", "version": "6.7.4", "description": "", "dist": {"shasum": "8f1a7eb5c154fb6da1d8f50819038b4cc48a6f4c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.4.tgz", "integrity": "sha512-oGc+avqDc4//xE/zcENeOaFg2we3PGo/OgVjTLbCypEjup4W+P31t5q3kBr8o05FGw3MNtTs4saxYd+WXtyCgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDooqojDqPDUzWJgglaCe7208U37sAEa5z1nZk2B5yDiAiEA7/Q7f/tpNHKGGNePrsSpPX9yclqKatFQDCLzf4yzrmY="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.7.4", "babel-messages": "^6.7.2", "babel-runtime": "^5.0.0", "babel-types": "^6.7.2", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.5": {"name": "babel-traverse", "version": "6.7.5", "description": "", "dist": {"shasum": "e487c5881cbe216c4b92c6df271e0309b5980c56", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.5.tgz", "integrity": "sha512-aFSijgPj7mm/ezf9PQGx+B8Px56hDJrHAvHk84SdWuKiyV63G01ixlgQ4yO4sfDiSb/QI5iJalR86AaxagEi5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIUxtE8fbT5IB3Q/N6muZzQYq4dN+lCvoizjM89bnkvAiEAj1Bxa5GEW50NyiRSpCdVPMB0QydyzTSxCVSTmNJT0AI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.7.5", "babel-messages": "^6.7.2", "babel-runtime": "^5.0.0", "babel-types": "^6.7.2", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.7.6": {"name": "babel-traverse", "version": "6.7.6", "description": "", "dist": {"shasum": "a14928044040b2a6c80aa595148af0b6378d1c93", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.7.6.tgz", "integrity": "sha512-fJ2c+7M1aXvk24Z9oo0ibJPvHDZqHykd/vmtLZ0orpRh2JLo63mwVb54f3i7TBv3b0N+9Rv5oDGopFDFgSmQnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGvvRcuTuPmA0teN9cYjPanXcIBmo27Dl/fBgxJ0ZFbgIgNeQ14yavGFv+OYER4uECkxspTaBQ1i5GCcOh7HYvdLI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.7.5", "babel-messages": "^6.7.2", "babel-runtime": "^5.0.0", "babel-types": "^6.7.2", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-traverse", "version": "6.8.0", "description": "", "dist": {"shasum": "bcda9de2362c3f3c8b161019cf8f3e1b244d6533", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.8.0.tgz", "integrity": "sha512-4OxCNbjeUGVM9dcUI6Sl/1pJGfCW9aFuDunM4pq77yHqAYV+13X+udgQKjY3TK1cycEK9uUQvB5j0mGTUgsqRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7d58ue/s8+uF5VGzqdfyPG6d+9rbkSgoBA/Be+kPhbAIgMygvV1T4rnSuWB6h1kDnm4FuL7NcJ4ROMy4gwcy11u8="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.0.0", "babel-types": "^6.8.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^3.10.1", "repeating": "^1.1.3"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-traverse", "version": "6.9.0", "description": "", "dist": {"shasum": "6656b3828c7aa97a72ad44985efb2c3619a93566", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.9.0.tgz", "integrity": "sha512-/inrdD92qMccgUsWYcxbgeks2eTIuZcbO5//pP2N5ZVId5DbLNGbgh01EDf4/JXzHOE0++s5SCUmmTBsr5clNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDN6Jh51MiH9hPmqeMZ3fAl3ScOUx6+/+MJn8OBDlR2aQIhAIn3H9M1p2Uws6SmNcws4dQ9eiaF8iCbBk3zwMG9sT8o"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.9.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.10.4": {"name": "babel-traverse", "version": "6.10.4", "description": "", "dist": {"shasum": "dbcf41ff1f32eb614859cead4871160f1f120d78", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.10.4.tgz", "integrity": "sha512-4db9oPOH4eFIhNR9nlwCU4AVSuEh+1BOhbpG1BGvaHzlxoN4udx8C5w1MQkMBdXJ52g8hYjI6E9jXLUNBGcgUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvu0Jl/nS6uUuNGmGDGrBnkzIrlWOVGvpAnsu1ZctYSgIhAL0tSJrJ3DcideKluRG4T8Ww4SA9XasJPWY+ZQPCR53v"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.9.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.11.4": {"name": "babel-traverse", "version": "6.11.4", "description": "", "dist": {"shasum": "3a7def6a4c1fe9f58b59c9a22be81f619f82976c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.11.4.tgz", "integrity": "sha512-jtfNO6tz/3DSjP2HMDLPG+cvGIZ4wEzuR8jt6pbBnYAa0uMXkE3V+TsYGTW6Qsnr69wzUVcERLYKURZAKhHT1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEFjRdbcMJueRA8YpQusogulmqa1TzlNnyenBW00BHPgIhALeMBSGPy+sEsImqvnQrbDlIrueVrNeWtqiTaxdYmMyx"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.9.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.12.0": {"name": "babel-traverse", "version": "6.12.0", "description": "", "dist": {"shasum": "f22f54fa0d6eeb7f63585246bab6e637858f5d94", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.12.0.tgz", "integrity": "sha512-6RjoRj+aRsjviiPMJtRkcM3D5VC5qOARGj8Oz+/JT5nuJCrBk0wQBT/lQdnYF4YRao1T3ARx+nnC0CwDZrwS+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpLYtoDl1Lz5ASYhNSaPHWzA/ylJa8zt2eBfzL+LHcNgIgWZsQx4KIGFuWBhWVkCLj4nY3fwJIIPDNleQvuz0ajHc="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.9.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.13.0": {"name": "babel-traverse", "version": "6.13.0", "description": "", "dist": {"shasum": "8835b4abae31814e8f7adebb8296b8c7ad0cecc1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.13.0.tgz", "integrity": "sha512-MalpIUKr7RctqCWj+e5bu6xToLo6jDaoz5UW17eYLymmTjw/HcV5hAJcPta/6JWx0RQDVxbZUPXbVkfMewvDLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOnJLeE9A0ev6ptjr0+Df5ujkbA9zRqRwv2TxMLzvLgAiEA/ym+osfsnw0lNRSJ/csaBZcgjVXggkZgxCJ106YIupk="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.13.0", "babylon": "^6.7.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-traverse", "version": "6.14.0", "description": "", "dist": {"shasum": "8d6bbf6273fc98ebc7eb9a222e9fb5260e9efab2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.14.0.tgz", "integrity": "sha512-DU69QCozhX2JdQoO3By4ReVEsCxzOUBYK4/nO5SjXWb6i7dqLXlSfJHraT1e4waGdr+rW2KEGeZkE8qXtjxf2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9fthRRNvzJDElAZvi2kO/Va6XYvH8AR/5JYB5Tz3JrAiEAjKWFIBzoDew1MuUNrV2waTnuPRCCFYxdKGhrq7AtG4I="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.14.0", "babylon": "^6.9.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.15.0": {"name": "babel-traverse", "version": "6.15.0", "description": "", "dist": {"shasum": "f0114c8c84cfee32c94eca02bcd0d2cbc8928478", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.15.0.tgz", "integrity": "sha512-bM/Erbs+6pyytIqWOwIZPJqUIJ3Qy7l1B8CRrYWbXj6ApTxIa/qGdHhdEFU1gnQ5M08MQEfd8UPMT0rHxdmFWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQBoUGDMafBuvYSCYRjsaAtJEBA6kyBZDOoGMlJBYReAiBJxG3qpDwTPJJlWT0ch83Cw5TgBnIA3m5xaaVeTFL5ow=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.8.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.15.0", "babylon": "^6.9.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-traverse", "version": "6.16.0", "description": "", "dist": {"shasum": "fba85ae1fd4d107de9ce003149cc57f53bef0c4f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.16.0.tgz", "integrity": "sha512-Q7Umj0Vzfx3/7pyoVNEKj51aDKnY9as/Wv9wkLZKJtYT+uZa2BkEz3uLs1RJDbrCovlqSEgfSoEyk9XMhoTdVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICosodhX74JJd/NiPv3+ku1wfFc5IMb6YyDTeej95MezAiBVJi+B3Ezd6PbTfQEYDTIuRfkfDiZqIypXBu+AHY8iNA=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.16.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.16.0", "babylon": "^6.11.0", "debug": "^2.2.0", "globals": "^8.3.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-traverse", "version": "6.18.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "5aeaa980baed2a07c8c47329cd90c3b90c80f05e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.18.0.tgz", "integrity": "sha512-TuE1X+aAaCD9pXGFrZGgiX2DSGY5TqfTsws0HQ0ruSLzOytYZ505z9pVp9RGmo4Qs1ySADMhW8CpS9O2R7NxYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeabjIbKhOc/4t9P+/jzxSCBika87TdC8NhPYzvY8p7wIhAOMW7PJgw7SRUIAkxA6lqzQWuTiOrnFRJwazAgeNn2w1"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.16.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.18.0", "babylon": "^6.11.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.19.0": {"name": "babel-traverse", "version": "6.19.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "68363fb821e26247d52a519a84b2ceab8df4f55a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.19.0.tgz", "integrity": "sha512-wT23/j4YSPtEu4IrMjMLuW50udHW4AvBYFxOo1JzSIi+b0j4SehIqC3dV9dBMFGBmz8ikO4tKQEoKPYJ4Umdhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAH7USunvOPn0bG5XnTm8aWZYA67Je1k3E60LEvdrVdyAiEAwotv/UL5GX1otTxr6+yzVfbjHdV+puGUyUeDCPk9uvM="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.16.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.9.0", "babel-types": "^6.19.0", "babylon": "^6.11.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.20.0": {"name": "babel-traverse", "version": "6.20.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "5378d1a743e3d856e6a52289994100bbdfd9872a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.20.0.tgz", "integrity": "sha512-fWr9cf43c+6fIvYbfBD+hfpaqI/xWfv+JHlBw8ohrcu3tWdJ98Uj0/PRCWbgZw3++3/qVF6AZjs0zDehiE3EYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwV6A2QaBPADDacTiovF78MHWqyuWljd+K0ag0UQC8bQIhAKgS8M+xy2JzhXMfw4CCU32z2uz/gjTme4WpqkROGuUZ"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.20.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.20.0", "babel-types": "^6.20.0", "babylon": "^6.11.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.21.0": {"name": "babel-traverse", "version": "6.21.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "69c6365804f1a4f69eb1213f85b00a818b8c21ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.21.0.tgz", "integrity": "sha512-cZBXjV8LlK4oYF2VtNIqcuoVS+Vo0Soe+/WkFl1SuP8gAn+N4zOujWWf6LzK+ovJsrtiDAWdAl6PHDBpr1Pv7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnAn0gKQWJmI0QOWaYJJdXGgw9aGoBD6jxmFlQgGj1zgIgWkzOmWBj7MXV1G33AsZvt5o663osoaqMbZwZQAjQWAw="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.20.0", "babel-messages": "^6.8.0", "babel-runtime": "^6.20.0", "babel-types": "^6.21.0", "babylon": "^6.11.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-traverse", "version": "6.22.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "998b6de3228bd2bab0d748a6ec0588c32033d3c7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.22.0.tgz", "integrity": "sha512-N74hqF1OsP7yf3AqouQcbJjCmK7xi5cLYO1J/obeX/+g8Ph1SmZME9e0YYvznjxOUvMQWiYbz+QzFrhH+MGQ4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHKI358JrM3XfH8z0AegKJSCK1IkShkl6Q9lLFg/Gc/7AiEA4jLxXEzwysjHXMWGJSKMHBO6sOpu1lfz9mEKN0L0JFI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.22.0", "babel-runtime": "^6.22.0", "babel-types": "^6.22.0", "babylon": "^6.15.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.22.0"}, "hasInstallScript": false}, "6.22.1": {"name": "babel-traverse", "version": "6.22.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "3b95cd6b7427d6f1f757704908f2fc9748a5f59f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.22.1.tgz", "integrity": "sha512-F+PHPvWyyDEZa9pv/86rPHrsTyijKd+Rm2pGoYDq/EsWwFrhr+4MvtBPLbO8H2kjJmKyrRfaImTeVUOO4N3bxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGvuhlo2LErX1AOaAKkig2vy1ny7K3VKbBIEs3Pn2ONIAiEAzunA+rIfFurjzPeaHJthj+P3FqSI93Iz6CgL44vb5SI="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.22.0", "babel-runtime": "^6.22.0", "babel-types": "^6.22.0", "babylon": "^6.15.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-traverse", "version": "6.23.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "dfee7148fc8342e4f7419251fe186947d97b00e0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.23.0.tgz", "integrity": "sha512-zvf1DUfAuqF1PavdfoFumI9x7seE6ziTbn6JeXqN5UBtYzyVflLr2U/4vh5oaUrnLLuPcWqa5nTbMJq5p6jz5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSi38V4Ddti+YiiIhqRqe6J9koe3SL2w2Zv1kjjurSWAiEA1ezciWldrRKEcVdDxNpPKxpq1I4IyEZAraVMNdtPG9Y="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.23.0", "babylon": "^6.15.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.23.0"}, "hasInstallScript": false}, "6.23.1": {"name": "babel-traverse", "version": "6.23.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d3cb59010ecd06a97d81310065f966b699e14f48", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.23.1.tgz", "integrity": "sha512-kwwn8lC9L7L7BBHueShzZlYZ5zRHRYrKV/RAd9mdNvkYKamF21AhqpR6eFnule/EOxZgZ2NQTN2qlHTQ+UfMmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZBxedtREOD520tlemWlEb8InyAkRUoI49i96wi+uBJgIgL6kv7rV8s5ndYKfvuMSPqXbZiWT/yTxYzl/nc65Mh5A="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.23.0", "babylon": "^6.15.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.23.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-traverse", "version": "7.0.0-alpha.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "57056517ba1bfaf126f336a454c3c9f00ecbb547", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.1.tgz", "integrity": "sha512-U2Vz3j83I16GFdexp+wLWUtVbWyGXIV8LQ6GxHd5l7ViqrFrGRUqabdlLtbof70A7LRq2JhG1BLikCz4Ltw8qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGl8KEeAmJ0nKI7lkPro2pP2dlmDIQjAGDhMxCBlpGJeAiBi7m0N7CHyS6FVSAQyhUAXzO1zEv/0gjCmJNQ4GFvOPA=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.1", "babel-messages": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1", "babylon": "7.0.0-beta.4", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-traverse", "version": "7.0.0-alpha.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ac5bd86d101b410daf705ff4a6cbfc0123ca49f0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.3.tgz", "integrity": "sha512-WoZuqHvGLfcadGJ+RYE9Pzw0tQCQNoAAxZbEdNAlbWnwaXRN7gdeKw45bfC1M1Qjy+0wXirtLTdP+wPTJ8uaLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCj4V3FMiCsrGdHpV87QIV2fUCQHszvjh3+hqlT8VmzJQIhAORjeDFspLxE6KN3YnUDi/MwUY5DKUfq9Lzgs0xAlqiA"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.3", "babel-messages": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3", "babylon": "7.0.0-beta.7", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.3", "babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-traverse", "version": "7.0.0-alpha.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "61cc89061b0ad0a5f9fc6df81117fac428bc4148", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.7.tgz", "integrity": "sha512-bP2jsELwKp+XkzhFJR/pqI53yh8SeJE7217JlicVipy2pFhu/qDzF3AR4tcCGpt8ygjDINNgLyLgjxBXEm30bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAPJOXlEJ+Ewp4thI9JrOxjyf6kiKtqFgd9TXV7neh9nAiEA2vT4EapJZ0mLXhQTKlPnXIibNzc2aFlpVjoHk//+k98="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.3", "babel-messages": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.7", "babylon": "7.0.0-beta.8", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.7", "babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-traverse", "version": "6.24.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ab36673fd356f9a0948659e7b338d5feadb31695", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.24.1.tgz", "integrity": "sha512-RQbPHCrxJAARham80la+q7sfbZC7k/oPjqS+9h1DZO0qmjz9dn7XVuT7PfHOHwxpqMZGXLP58BWWDUwruFZH1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID/DkL7VVdB+16jXEDygQMRlJQUVrECxEmRCBB9IO/xUAiEAkTyonwTluCk7cUdiW7vZZufkskhyFmwXd0gJdRvbQ78="}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1", "babylon": "^6.15.0", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-traverse", "version": "7.0.0-alpha.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c2a727265c9e0c36d1d64e28407bad4904e1dd3d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.8.tgz", "integrity": "sha512-bqejqdqSN1Ny4xrfDU1zBeafymLg/NNrePg0moEVSc2PdZL8XXeayw8eykseyNHaQqLbfl8Zvcuobanv4zqh0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBte4iXmR7stpnlHjuF48zejIBHCcm84Tx+B23tSzDxnAiBUGFqVZgQS+x8tWjKlCw4fdtwU3mF44h2NrtKHXmtAVQ=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.3", "babel-messages": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7", "babylon": "7.0.0-beta.8", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.8", "babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-traverse", "version": "7.0.0-alpha.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "749abf53c908ca80a8c96f5ca958d33732e0714f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.9.tgz", "integrity": "sha512-o/1ub6m1FEpcuCLYHgbcFIOakCzoAY2OhHUmnnfxiXUtaEUflleZe4SXGaCq8w3PySnBlsXLCeTPobkzkG/y+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8LbN1TNGTAbwsXq/W5jiTuIT3wgNuOdX+IK///bw+eAiBzUwvoNIld49JZzhJV9C9hCCXQyCRyCHOqT0w6hHZhOg=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.9", "babel-messages": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9", "babylon": "7.0.0-beta.8", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.9", "babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-traverse", "version": "7.0.0-alpha.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ba2cae7d285f7a9677946afb966a8ec01e82959f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.10.tgz", "integrity": "sha512-OxyVIJArdmC+1oQS53bkWhr9seUt03CHqf8sjdkgWEf5VlGYIGfEgnWhyrGZYhCQRjTstjKOmPfOoqCkb95bqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH02klsEjk2DG/jCu7lLXOX15SVTHRWY+cFwsBWJI8IzAiEA69WdPTC6hf0uAYgRXAL1zVHdNwd0YuVf/sXq4Q6kq7Y="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.9", "babel-helper-function-name": "7.0.0-alpha.7", "babel-messages": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10", "babylon": "7.0.0-beta.10", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.10", "babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-traverse", "version": "7.0.0-alpha.11", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-d4Im8cS0F6sxAlAUI+vRrr2tPYrZ5/djpn7aIvvknkwqjXIs2pYi0NZE/9yFPQfCmdMEZnnKGGBgCNaoGwaOjQ==", "shasum": "918c9e9172dbd8f2ca7639fb15856c8a6e3b6e22", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEI26+7BWUzDS4V+yMRpQwmQYcf5WvS6HPNBb7o234dkAiEA7xuaKhxMehtH4POHpxqDdW4+TTDqBOmWjxuX8ZRs5v4="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.11", "babel-helper-function-name": "7.0.0-alpha.7", "babel-messages": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.11", "babylon": "7.0.0-beta.12", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.11", "babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-traverse", "version": "7.0.0-alpha.12", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-PV<PERSON>+hnJHU6q1s7BiSpjuprk2lhC7z29ZMdb0FIQaFZMLV3rU0Lmqm0vqn1MCHO5/83DKe1UhDfE3IGXgY+DJYA==", "shasum": "0c0855a60287e56e54cf61ea9f135cdf759d6ad0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAk3hWhKnwe1+If3AQb+WgWmraBDDNA/znqXiGCcxTDIAiEAmu9RlVYlqQwEzgdR4dJ7LziQMgZG7g+qTc2XkfDYdug="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.12", "babel-helper-function-name": "7.0.0-alpha.7", "babel-messages": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12", "babylon": "7.0.0-beta.12", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.12", "babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "6.25.0": {"name": "babel-traverse", "version": "6.25.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2257497e2fcd19b89edc13c4c91381f9512496f1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.25.0.tgz", "integrity": "sha512-nTp4cMQ6tI37rTmE0oUBQKJVxdwKhz0Wzh5KzaV2a+GjdkGDdJV7Vz2aAIWQqGs/fh5lUAiDuKrQontA0Z94IQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+OMPMGeFKOT7AGfKSaqk2xsE6pUyxpLXyEeJFInPymgIhALm4vbRlh/T9/UdJ5d5wPAyFZcdxW7XORbBAUsPF6uBe"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.25.0", "babylon": "^6.17.2", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "^6.25.0"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-traverse", "version": "7.0.0-alpha.14", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "79be03b93663dd7586904eefab6d62df793bdcd8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.14.tgz", "integrity": "sha512-Xt9TbSnJKNylQM7VXDIJevgc4H2g38lC99EGxm6bOVqwU4TljGA3wF0HAuPORZtIM/4KGHTBdj31LitZinKmVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdH9ngrRcwgkVmWu15BmkZDZF6xv/dxS0Qnid2Ay+uuAIgGBm7a56xeUJybzIYvpYW6tnMouROPdv7wT7bNEr8zGY="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.14", "babel-helper-function-name": "7.0.0-alpha.14", "babel-messages": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14", "babylon": "7.0.0-beta.15", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.14", "babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-traverse", "version": "7.0.0-alpha.15", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d7af52cb8ee6e0867b778da17166e6f01498b11a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.15.tgz", "integrity": "sha512-QxdDaAYnulEHsSWzZ75m+aCXNy1wT6IJMoY68LzRAEk3iIHvZbizMrK3SqfG+Mxcssw7VGQNR7oWRwniTKGXaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwcnq+f7oYqRolsqgKSrk6EHlVHDX/e+MIKmJLP6sfwAiAu5zYx4Q3oXnib5ihhiW0wpcx7Kr1fSOaqpXRJefnzZg=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.15", "babel-helper-function-name": "7.0.0-alpha.15", "babel-messages": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15", "babylon": "7.0.0-beta.15", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.15", "babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-traverse", "version": "7.0.0-alpha.16", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "65f077c16fbb9fdab8ab8c1728120e868cc45759", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.16.tgz", "integrity": "sha512-wDWC0rf+46VPlKk5JrVEDEyncO1RqeIO6hZ3hUbhmW41Hnov2xEmb3Nnyl9FLHUIBb6M7n8/Fg1PWNeV3kp85Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH+37fRsnU39B20buw8Z8h90K02WonpyvgKYw5cXv5hoAiBRqW5+FaPl2pjWZeX+TsoIMyzqcYTLENeqCAEKqyAxww=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.16", "babel-helper-function-name": "7.0.0-alpha.16", "babel-messages": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16", "babylon": "7.0.0-beta.17", "debug": "^2.2.0", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.16", "babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-traverse", "version": "7.0.0-alpha.17", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c8a33788274bc4138c529ec9c29f0bacb573f42c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.17.tgz", "integrity": "sha512-beFmFxs1oQ0AGkKM0+nn8Qhd6Jcoll4NilSQGMnw4tvi1Da650NO2yEn/av0YLhiRFN9C7bOJzI2avUboW1+HA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHmyOhj80n8/icJubIAx7Dc/GXlScm033oAGn0sMWKtcAiEAmV1noRaUFnKckUogYou7yR/lJCOIM0//o5sbzsTB9J8="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.17", "babel-helper-function-name": "7.0.0-alpha.17", "babel-messages": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17", "babylon": "7.0.0-beta.18", "debug": "^2.2.0", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.17", "babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-traverse", "version": "7.0.0-alpha.18", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-6h0ShBdxosHTlGRkEhxDOURlF289J51H8K65lOZxV/UGG0WidRFmGNSe6Il2ffcPhs8k3jHs+y8cmjLvOU3H2g==", "shasum": "54ef34dbfb527f9394c25e9ab3b1d73ba50ade77", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbRDStICIHeShDPRQiwfeBTBIFF7DAkhSKpZ0iJAd/BAIhAJ99yPA2GFg1YRrIFAvqVJesJiHa4vXFNszu720JjHn6"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.18", "babel-helper-function-name": "7.0.0-alpha.18", "babel-messages": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18", "babylon": "7.0.0-beta.18", "debug": "^2.2.0", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.18", "babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-traverse", "version": "7.0.0-alpha.19", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-nW2yJJjVTKnvmqWSh8jsUAJaQMhTDhD4CPdy7hLhOTxGORPKP+V5ax8wQCHhIbgU+lV2oJ9iqJUpvGvjs7GRwA==", "shasum": "083f3f00a413fd9ec38383c0d5ae79511d9ed53d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDODd9ESJ8RLxib/vVqtHh6DXVVlEwb6ZF3EYcwOgKsEAiARNtVtRIrAlTzeA+R0QGSC032zctNNiZx1fOfUByTGsg=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.19", "babel-helper-function-name": "7.0.0-alpha.19", "babel-messages": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19", "babylon": "7.0.0-beta.19", "debug": "^2.2.0", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.19", "babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "6.26.0": {"name": "babel-traverse", "version": "6.26.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-6.26.0.tgz", "integrity": "sha512-iSxeXx7apsjCHe9c7n8VtRXGzI2Bk1rBSOJgCCjfyXb6v1aCqE1KSEpq/8SXuVN8Ka/Rh1WDTF0MDzkvTA4MIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCr+B7j5T6dE+Gup9uwm9fexW6mcI1hr0zCJl5lkyqnKwIhAMtdazDrOZzC2elBDPi+hJdLKkbYJ1/W0aKw861oi6g8"}]}, "directories": {}, "dependencies": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}, "devDependencies": {"babel-generator": "^6.26.0"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-traverse", "version": "7.0.0-alpha.20", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-5tWyBompTy/Qa8uJqQ/vNWUr8GQURHXLysi8lPlrGtsTVIpV9AqJRFWa1Wuu9Wrxvb/lNwzbpyGv7rF51WBsHQ==", "shasum": "f11f15ad4db4d911880ea241fed1c5643bbfa377", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWekdZjJkWXHMFaPR6dwWN+VBrCC7ZrXr9LmhsIZQ/egIhAMu+MyvIzVgmwrDpQzgQcDxJUhSYWPQLXLYF3tiTRrHU"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-alpha.20", "babel-helper-function-name": "7.0.0-alpha.20", "babel-messages": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20", "babylon": "7.0.0-beta.22", "debug": "^2.2.0", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-alpha.20", "babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-traverse", "version": "7.0.0-beta.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-IKzuTqUcQtMRZ0Vv5RjIrGGj33eBKmNTNeRexWSyjPPuAciyNkva1rt7WXPfHfkb+dX7coRAIUhzeTUEzhnwdA==", "shasum": "da14be9b762f62a2f060db464eaafdd8cd072a41", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCk6s4a678kHDWPck6IjdStT+IzVjSkkfzN7HYW8uoQuQIhAIJtoE31pGeCUkI9+bjzNRWLrn7Uuw8UhWjHlT1C+SxN"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-beta.0", "babel-helper-function-name": "7.0.0-beta.0", "babel-messages": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0", "babylon": "7.0.0-beta.22", "debug": "^3.0.1", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-beta.0", "babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-traverse", "version": "7.0.0-beta.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-Y86/b/nB4bvd7UJgxIjzE6VJuPjUHr2a6GhBERF8ZiDQ14m4fKeAXG3PlsEvjoMEXtiJcFYfQXej3ElXL7kc7A==", "shasum": "24e51e3c6a4b795e7d0798511f314785076324ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkH3E0XbZ+NECFdkfrHbhnLcqIh9iifAQLo6HNLjsqrAiANBVJ4udieklldMqFhBfFHWUC7H6VQwEW6QpACPyR3wQ=="}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-beta.1", "babel-helper-function-name": "7.0.0-beta.1", "babel-messages": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1", "babylon": "7.0.0-beta.22", "debug": "^3.0.1", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-beta.1", "babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-traverse", "version": "7.0.0-beta.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-kFgrI9e0DTyGutb1OtTKltpPYWEngVGKzXokcOdzl2+++k14doLgquUMxjHrzQ+2Ymc1Q3qggY2yUwxuo8AZjA==", "shasum": "4073ace28b2602bb250cc6473a49730f516214bb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuvAUqFy89ggHI9ZvnqbETBeCcvLgwj6FD/jY7/EJuzwIhAKOIkpVuYhYEs0e9FWJUcJuyI01V5XAeNbUId0yscLX+"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-beta.2", "babel-helper-function-name": "7.0.0-beta.2", "babel-messages": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2", "babylon": "7.0.0-beta.25", "debug": "^3.0.1", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-beta.2", "babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-traverse", "version": "7.0.0-beta.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"integrity": "sha512-xyh/aPYuedMAfQlSj2kjHjsEmY5/Dpxs576L05DySAVMrV+ADX6l4mTOLysAEGwJfkePJlDLhFuS6SKaxv1V7w==", "shasum": "3cf0a45d53d934d85275d8770775d7944fc7c199", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-traverse/-/babel-traverse-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgkwy+xtzh6+aKKfX5Mx7HDfdd12FNmY1rzAMhERDi2gIhAIgwSlWNYPs5c6XrSK/eGGCZJDVJ8xu36P5glmoqSDvO"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-beta.3", "babel-helper-function-name": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3", "babylon": "7.0.0-beta.27", "debug": "^3.0.1", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "devDependencies": {"babel-generator": "7.0.0-beta.3", "babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:08:28.653Z", "time": {"modified": "2022-06-13T04:08:28.653Z", "created": "2015-10-29T18:22:31.769Z", "6.0.2": "2015-10-29T18:22:31.769Z", "6.0.14": "2015-10-30T23:44:25.322Z", "6.0.16": "2015-11-02T07:14:46.282Z", "6.0.17": "2015-11-02T19:54:48.638Z", "6.0.18": "2015-11-03T01:25:33.292Z", "6.0.19": "2015-11-03T03:15:24.091Z", "6.0.20": "2015-11-03T04:22:18.317Z", "6.1.2": "2015-11-05T11:12:26.366Z", "6.1.4": "2015-11-11T10:47:03.157Z", "6.1.17": "2015-11-12T21:44:03.753Z", "6.1.18": "2015-11-12T21:53:16.653Z", "6.1.20": "2015-11-13T11:39:28.621Z", "6.2.0": "2015-11-19T04:34:42.409Z", "6.2.4": "2015-11-25T03:16:05.171Z", "6.3.2": "2015-12-04T03:48:22.724Z", "6.3.13": "2015-12-04T12:01:31.374Z", "6.3.14": "2015-12-04T18:52:43.916Z", "6.3.15": "2015-12-06T16:32:00.979Z", "6.3.16": "2015-12-09T04:10:48.624Z", "6.3.17": "2015-12-11T01:20:39.750Z", "6.3.19": "2015-12-14T22:11:49.258Z", "6.3.21": "2015-12-18T08:23:15.244Z", "6.3.24": "2015-12-18T23:53:55.917Z", "6.3.25": "2015-12-21T21:59:18.386Z", "6.3.26": "2015-12-23T07:12:14.730Z", "6.4.5": "2016-01-19T23:02:59.588Z", "6.5.0": "2016-02-07T00:08:30.921Z", "6.5.0-1": "2016-02-07T02:41:54.110Z", "6.6.0": "2016-02-29T21:13:10.694Z", "6.6.2": "2016-03-01T14:08:40.811Z", "6.6.3": "2016-03-01T16:40:54.722Z", "6.6.4": "2016-03-02T21:30:02.686Z", "6.6.5": "2016-03-04T23:17:17.751Z", "6.7.0": "2016-03-09T00:53:08.529Z", "6.7.2": "2016-03-10T22:41:34.741Z", "6.7.3": "2016-03-11T01:35:58.249Z", "6.7.4": "2016-03-23T03:37:50.789Z", "6.7.5": "2016-04-08T03:24:54.295Z", "6.7.6": "2016-04-08T16:27:15.055Z", "6.8.0": "2016-05-02T23:45:24.007Z", "6.9.0": "2016-05-17T18:49:50.130Z", "6.10.4": "2016-06-21T16:43:40.683Z", "6.11.4": "2016-07-20T02:00:00.183Z", "6.12.0": "2016-07-27T19:23:21.459Z", "6.13.0": "2016-08-04T23:35:09.146Z", "6.14.0": "2016-08-24T23:41:04.438Z", "6.15.0": "2016-09-01T15:03:13.048Z", "6.16.0": "2016-09-28T19:39:07.238Z", "6.18.0": "2016-10-24T21:19:13.958Z", "6.19.0": "2016-11-16T16:15:28.682Z", "6.20.0": "2016-12-08T23:25:48.078Z", "6.21.0": "2016-12-16T21:56:02.605Z", "6.22.0": "2017-01-20T00:34:31.092Z", "6.22.1": "2017-01-20T03:12:57.483Z", "6.23.0": "2017-02-14T01:14:19.882Z", "6.23.1": "2017-02-14T02:18:18.661Z", "7.0.0-alpha.1": "2017-03-02T21:05:33.652Z", "7.0.0-alpha.3": "2017-03-23T19:49:38.219Z", "7.0.0-alpha.7": "2017-04-05T21:14:05.313Z", "6.24.1": "2017-04-07T15:19:10.259Z", "7.0.0-alpha.8": "2017-04-17T19:13:02.188Z", "7.0.0-alpha.9": "2017-04-18T14:42:08.583Z", "7.0.0-alpha.10": "2017-05-25T19:17:35.685Z", "7.0.0-alpha.11": "2017-05-31T20:43:48.264Z", "7.0.0-alpha.12": "2017-05-31T21:12:02.987Z", "6.25.0": "2017-06-08T21:29:07.373Z", "7.0.0-alpha.14": "2017-07-12T02:54:15.637Z", "7.0.0-alpha.15": "2017-07-12T03:36:32.394Z", "7.0.0-alpha.16": "2017-07-25T21:18:25.261Z", "7.0.0-alpha.17": "2017-07-26T12:39:57.686Z", "7.0.0-alpha.18": "2017-08-03T22:21:29.455Z", "7.0.0-alpha.19": "2017-08-07T22:22:12.723Z", "6.26.0": "2017-08-16T15:54:16.482Z", "7.0.0-alpha.20": "2017-08-30T19:04:30.563Z", "7.0.0-beta.0": "2017-09-12T03:02:58.338Z", "7.0.0-beta.1": "2017-09-19T20:24:47.711Z", "7.0.0-beta.2": "2017-09-26T15:16:00.170Z", "7.0.0-beta.3": "2017-10-15T13:12:26.109Z"}}