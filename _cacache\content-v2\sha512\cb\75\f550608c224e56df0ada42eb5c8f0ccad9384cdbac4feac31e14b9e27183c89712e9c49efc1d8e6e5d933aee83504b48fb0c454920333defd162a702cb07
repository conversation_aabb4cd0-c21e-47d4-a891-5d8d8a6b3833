{"name": "babel-plugin-transform-es2015-modules-systemjs", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.0.2", "description": "## Installation", "dist": {"shasum": "6cc174a0bb5c56b50ef468571663dcc5da096341", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.0.2.tgz", "integrity": "sha512-p3DhAYJqTTtJ+W8/WlinXdbPU7W4xrtEmHpmxiXGqo2j6wzGcBC4JTCUy4BQVQodwj6jnTi0vc6+0wpbyhi1iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxS2fRL2HjrcgL8K/Tgcsz+rXUmogDO/96DFQoXe8FLAiBQzFR2EDkc80F4vii65PsZogRzMx87/90EuTeV5k8ccA=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.2", "babel-helper-hoist-variables": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.12": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.0.12", "description": "## Installation", "dist": {"shasum": "b6fa71a0f27c4cc0c1e3b87eb024ab10207ea6e4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.0.12.tgz", "integrity": "sha512-qWNf0/20Fuah+FaSkVeQhUrKbmxb/jB867hiQaDwzvCM2T7xbyMVWNdO6trOD003rqpOqFfm2ZJ4TlYcn/pCYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDk7ULmoq3RodlOiAUnZ7vC7nyhiuaG5j/OIryU25VuDAIhAMF9platTqL5wsXnPbIbN0RB50/ajVNgHVNL5NftTZG/"}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.12", "babel-helper-hoist-variables": "^6.0.2", "babel-runtime": "^6.0.12", "babel-plugin-transform-strict-mode": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "3a3a80c65deb51f0551468880e2a6ab1550b04ef", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.0.14.tgz", "integrity": "sha512-x5MBS2gj0tAQxurTqED4lrGWtgyp84qx4K+Tgp/e8yLTmwbyyVl/KcR9BPVYSbfAnFF7IR8bSRiBlmHbx4T8Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0s1jUoFlkPCjWOCUgJkuMEjugzb+9JBGp8yCQfirtNwIhAKNzWKHOg9JZOLHXB4m+TK/uTfp1dm8E0gcJ31u3HJ+q"}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.14", "babel-helper-hoist-variables": "^6.0.14", "babel-runtime": "^6.0.14", "babel-plugin-transform-strict-mode": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.0.15", "description": "## Installation", "dist": {"shasum": "1b227b40724dd6165af2a6ee9ec9dc1c3308f9b9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.0.15.tgz", "integrity": "sha512-0su0LRijQpWPqI6oZYsDEpCTxJyX0VMwFxeilHECIhjsFmE1T3P5U7tQLpCIvdHh9fqskM4uZr70AaduKC5AFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6/yV/VD16Fl1HBG0RT7XvRmn4eqfxbYqj0oOJ1Obt1QIgQE6OCEVcon+h79MF3vlfHSoFGdE7FVUvCXrtpXUxwcY="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.15", "babel-helper-hoist-variables": "^6.0.15", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.0.15"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "aace16de7d88953f54cd76a5a36e79c35e026b3d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.1.4.tgz", "integrity": "sha512-NGCJdiByh/9VoSkR8pmVhi46s6lkOTQ7kF5elTF/+h7wlSdOHzn2cL1kQGRJFZJkDjDvzijwgLYx78ST5jCVqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+wXyFAn4IH99jVaaeKaGezXeQJgbgDlcemf+33EJyNQIhAJRzQ0qnVN4RJO7NQSwExZgdLhujGtNNtG0on0Z76+Nj"}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.15", "babel-helper-hoist-variables": "^6.0.15", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.1.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "3e20d8a09a98be51d243f39304b868e93470bff5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.1.5.tgz", "integrity": "sha512-Vf0BKfRGf/8oJpkb6UQYJSvPa/p+PJG5c2Gsow5LDl0OlmpScvgq1/08sujCVVFKJVUr4SLKf7cYZjE8cLFriA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDu8lDwXsbqiTYPEnkW0fQ6AGw3VhhY2ob5PMR2vnxIKQIhAN1SvlTIIqM2eITV0LeO2DdETxeTQ60uVay2Svniorhu"}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.5", "babel-helper-hoist-variables": "^6.1.5", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.1.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "8e8dc7ffdd6ec0d1b2ae47004723f2d22b8e1552", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.1.17.tgz", "integrity": "sha512-Rkgc7CB8vI9mr/Zoe/8I6CYQKGr9o3WxD6ourrVg6NAy/e6VUmfanmtgY8JruZx3PoSs6oi8tyQRd5EKUvkIGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDPP58XPsWMrf0CYmWaMIkNFw9gfHTS6aFRvQs6dlji5AiEAt96SZNEgfXfxICKyKoUpaDoGiGy2uLyPA7VLUpdAc+E="}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.17", "babel-helper-hoist-variables": "^6.1.17", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.1.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "4916b3ae19376f10e3958b39b737c9918dd0c36f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.1.18.tgz", "integrity": "sha512-ZVRqntrZTIL7L8Juaw3FLM+Ag7PY9Va8QrRCxlOI2H2q7GwO4prilSJ2DCxBPX0YHwYfrijSLJ1+a5tL5dTO+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDygJn9HBxmr+Ddj55BlcmL9T8CkQWmuNC11UVLf6hKdAIhAOPtzS217++cibxV3CURUqvDDHd8iu5VHCr8OmG6fd5G"}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.18", "babel-helper-hoist-variables": "^6.1.18", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.1.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "6340c8a47233cbf4e39b1272ca4a372acee76742", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.2.4.tgz", "integrity": "sha512-P4/iJIobcV3t0sStKllkSNDDaFT0LPHe9+/BipgxA+HiDdh14LVUE7uwfY6nB2l5CrlDJItU4BipcGeevyY2OA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhFhZdXT52zupZR33WZoQTa4MiOpstbPZyqPYxPoF+PAiEAmv2o3GVs2VdF8d76CNs3tfRouvt4lcAMhCkWrZcWcDE="}]}, "directories": {}, "dependencies": {"babel-template": "^6.2.4", "babel-helper-hoist-variables": "^6.2.4", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.2.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "04d26421be73e6181b847e5f439d639926617729", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.3.13.tgz", "integrity": "sha512-DQOMxrIr7q2UrG/oXWyTeUgDScLIue2GzSgelB5YwAMAUtBYM/DrG7P6KIsImq5W+wk/e7b/TF9GT1KojTWquw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpf1zU5HrCufMHFFvAXhOIPlrhcxguXjKEywXyD+MSfQIhAK2CWP1WOwupjIC/iG38I3flxnualznjgaGMGKyyDdND"}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-helper-hoist-variables": "^6.3.13", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "07c1a3326c6a6c14d45e51999332a5815ca274c0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.4.0.tgz", "integrity": "sha512-XcoM79zNZ4CxQJduVJlIHVZhu50VlyNEuFAcwJjmndv6OzKrqhJIaPpt/vX/MMtL4qkZlFzg6g5afkRWcPgppg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlJO/ZkmGe/mVDDuoIP7M6PEm2fQCYgFAPPIbaaXIW5wIgMEksihUhYQb/0yKcknkssMX/aiZoXZow2UHk7M5M4qU="}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-helper-hoist-variables": "^6.3.13", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "cf084747428693eb437eca55ce6fc3689a3a9c4f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.5.0.tgz", "integrity": "sha512-XjMcQX9Wn9f91A8KkSECu/Kt+Fh8bczJmGc/FzFDedYTJDbPIfU5i9WdB/CKZ1X/P69ENOf7bzzUCwb4/iFgyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICnKkEMME5rW+PmzNwrk0K2pYabqgE3L4im6Y6bzXK+EAiEAlcxDaEaAETZGy0+Qd++4LQn6gQAXAC971sC9sR24SMs="}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-helper-hoist-variables": "^6.3.13", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "6eec3e949cd6726259a37e8e58af247f86c09426", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.5.0-1.tgz", "integrity": "sha512-D5PvlYEJc70frcHMdL37YKNyWLtp4otMNClVW3teJ343NMEYl+RLUv9fpWcvwdG9RQoRUaqwaztfEY5fePaerA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID7ZVnFpHG34cPxnPyvd3y+U6RbDotOUCImucABXSxeNAiEA3zhC82uSdZCuk3dnxVJF5Hwide81veTdce1a63i46LE="}]}, "directories": {}, "dependencies": {"babel-template": "^6.5.0-1", "babel-helper-hoist-variables": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.5.0-1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.6.0", "description": "## Installation", "dist": {"shasum": "10f4a5183bfc707f677a59bebc261497516185af", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.6.0.tgz", "integrity": "sha512-8xUpDXIbS7xC7EZtP1hrvF0xE/CBktTtTBcilCpV0pYrP82f9XFqGR08klObImiX0bsgiMQn01MeSGKtOcJmtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB7LFeUJeckmsXLrVyGx0bxndXyo663BXdWLFzVe3stdAiEA/o6zjXqSKEMbZOeqzwBMbA+mIDrGPvZGGZgEzFqZ6SY="}]}, "directories": {}, "dependencies": {"babel-template": "^6.6.0", "babel-helper-hoist-variables": "^6.3.13", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.6.4", "description": "## Installation", "dist": {"shasum": "508b861925267da6c5538f5b5e03a775b0df076a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.6.4.tgz", "integrity": "sha512-JMT9xrPTI0vlY8rHutATmd/H6OprRinw+KNnhYyRVySAHn8ssfCgraJH4hBsSA220iriXF55/Gfvi/s26IZMUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/Wo4ThacZvMroU/H+uGV+2ML4GVeTrhoevmQXOrsoagIgKk0pfNsnNYeKWMsG3p07Y86OtgJ/ftL3VBdVKAno+84="}]}, "directories": {}, "dependencies": {"babel-template": "^6.6.4", "babel-helper-hoist-variables": "^6.6.4", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.6.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.6.5", "description": "## Installation", "dist": {"shasum": "46bdc41304fb0e2e17521f9c8c395cededf30de7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.6.5.tgz", "integrity": "sha512-kCnsp/MEbQuVDPaSjdazgRfR+T/yhJQnqogLsyCYYLs0PVLX1T3eFLVh447l2RV6pjyJOJZK1/LTdmH504QUqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLEdt00wdAWJNuh+PpS9kk4kwBpnS1i7U7fnfIBxyt+wIhAJsKTKf6475TOpIsaZorxG51Ix+PyOGNu6ezDwpB3Z6r"}]}, "directories": {}, "dependencies": {"babel-template": "^6.6.5", "babel-helper-hoist-variables": "^6.6.5", "babel-runtime": "^5.0.0", "babel-plugin-transform-strict-mode": "^6.6.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "7fd5ac538137ba9109ee57c060d05cb0a82db46a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.8.0.tgz", "integrity": "sha512-Z0qQE/xZ79vboPary5Ef8f7RTTvmilt4S64UT5SI6BlUzC7XEQfy2Y2gu74x3DMS6sHBh7Js128tbaAMHDrVqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICe/4kT7XPRvQqxuNQw95yIO9r6qQQ/wX8OIdw3KXzmPAiEA6ddEZ7zc3IOZvYNGyrRRc0mF2iE1lec6EC9pOdGEwbM="}]}, "directories": {}, "dependencies": {"babel-template": "^6.8.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.0.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.9.0", "description": "## Installation", "dist": {"shasum": "f91647dc4ce5fe4b5240366fc28bbcbf62c7e763", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.9.0.tgz", "integrity": "sha512-IxndngR1bonofu2GtwJIr5zgOTCIgNpw/11nzVliM60gy0rWCdF+BB2kkq7XkQ4LzJ6odGYociCSOJi3gE8uOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDlRmdP2uNM9ktzRI4KGgh+kRD+Q+Iby5fIVDYYYWfRAIgJoLqsm67VTN2WupemORfSp4oUdtrGPStpXX1Ul6lFgc="}]}, "directories": {}, "dependencies": {"babel-template": "^6.9.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.9.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.11.5": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.11.5", "description": "## Installation", "dist": {"shasum": "6333de152bf5e9a5930edba1f13acc42b0aba4ac", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.11.5.tgz", "integrity": "sha512-T+/m/5OC7voBtYB4P5KydPOnFLjhNXGfTX3Fl9kgbLRqJPpeF9cNq8+0YIAvme3t1AcfVMfP2IRCBqS1DaSvDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEamkfLM/Tadu2RuSQH3sqE+OGIx4OBeOfR8esvVTEoTAiEAuKOh3O38HZ8LIrJKaHjzCIKjxVWVuhVcvrz2ETqeo94="}]}, "directories": {}, "dependencies": {"babel-template": "^6.9.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.9.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.11.6": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.11.6", "description": "## Installation", "dist": {"shasum": "a88007b8d7f3c560b037f376c4f0c2752145114f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.11.6.tgz", "integrity": "sha512-T/iZ4qEdDnwdve4XJDEG6cTq/jSfLrwwuPLU1UEloG0X8T17LWXW3RbzFKn+YDVYaYofUCztrlveLk0RiuLAtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/NiBVWRbQUaEDiSc+SfCu53bxCddXsphcmyziiSnkDgIgf96/p38UcI2rgjroX81UcXyFLOfegK8EZiLg38d5c90="}]}, "directories": {}, "dependencies": {"babel-template": "^6.9.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.11.6"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.12.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.12.0", "description": "## Installation", "dist": {"shasum": "02f7561fd1d3a4c6865bbba06b8a25c7e266e1c4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.12.0.tgz", "integrity": "sha512-HURECkuOobrwP3TXKiaBQZXiHZjC0B9k+I9NtUQOut+BZCuN2e3PCiP+qEVlFCo3URr6DWyUk5O5S1IYTX2pSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnNUmxIfGa4sg26lY3PfW9LbA1f3so2QkzXvQVPlOOEAIhAPAsVIifkj+KQpp3BCkmpHFpcRyVoSZy7Py+nTIOjg71"}]}, "directories": {}, "dependencies": {"babel-template": "^6.9.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.11.6"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.14.0", "description": "## Installation", "dist": {"shasum": "c519b5c73e32388e679c9b1edf41b2fc23dc3303", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.14.0.tgz", "integrity": "sha512-fO/kWRGP6UA8Rr7Gr7gh3cUiMTh2JhlHeEmH/laU/j9gH3aCrsXKbbMOPPA4q5LgipY8rPRF0mxusxQiI6zdUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnhjdDyuB+TrCuic9Oh5JUIEmPD6Pwu6yPlSf2KPO0MwIhAP6HpngnkUkzz2BY/n09wON4Hp24qKR6VTjOBC8bMZkb"}]}, "directories": {}, "dependencies": {"babel-template": "^6.14.0", "babel-helper-hoist-variables": "^6.8.0", "babel-runtime": "^6.11.6"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.18.0", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "f09294707163edae4d3b3e8bfacecd01d920b7ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.18.0.tgz", "integrity": "sha512-rOEUhjY7jAIgceMx6edECbJG6INSkOshd6pBSWma13CTC7/d2ZVN6AcDdPsPJESK2N0RekJSz25cnHhI3rZxQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETjB0kUVZZkyHXjeAfq1o2bYlMrylVrszGotp76TbONAiAMKddLDeKBm/5gvZEkmCQeDppSq7pUG5P8PLVB2gYJyw=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.14.0", "babel-helper-hoist-variables": "^6.18.0", "babel-runtime": "^6.11.6"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.19.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.19.0", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "50438136eba74527efa00a5b0fefaf1dc4071da6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.19.0.tgz", "integrity": "sha512-06zjREx6wSo3ZJXV0jWojVJdzuW1FXGrURLNcMkppf4kcQ5rZeFlZ6eUmY/6fPEkdpY9L3aGTOVEQ4Q5jPwdcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXW6erAq7fFMpLjyKZo7AqRxYIEK4F/65y1gydfp19DQIhAP728awGHHcVKxbeyIch0Ms52MVzrqjFUq1TE9uK96fT"}]}, "directories": {}, "dependencies": {"babel-template": "^6.14.0", "babel-helper-hoist-variables": "^6.18.0", "babel-runtime": "^6.11.6"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.22.0", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "810cd0cd025a08383b84236b92c6e31f88e644ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.22.0.tgz", "integrity": "sha512-pG1FX0yJMThgXFlcP5hT2Ij2msFeu66T5aLGWo136/8h/b7Xvu9EZeI/QWCPE/zuAIlLqzX/wGX96dCDdaEegw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD42pYMMFVeQNH/97BWZAJnzbWarQzFD6L/xFPgyzGf6gIgFDH/CIAjLektZ7K/SR++MH0yREoIZfwKi8DWxGXZQh8="}]}, "directories": {}, "dependencies": {"babel-template": "^6.22.0", "babel-helper-hoist-variables": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.23.0", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "ae3469227ffac39b0310d90fec73bfdc4f6317b0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.23.0.tgz", "integrity": "sha512-m8n+/bPmKQ/pAbh428786gRdXvdE73wjUtco9BZA6WpmXhAMX6f23Y3FaYVLgqS8R6kHfOSj35QaJPpIes7mPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3afTF2x+AV7S2G2jM9bBitUmHHj1l81cDSRn8+0s0pAiEArkfIGo9ueNYSUWjj44sS1dJ7v/WiXnld5PiZEXbxRlg="}]}, "directories": {}, "dependencies": {"babel-template": "^6.23.0", "babel-helper-hoist-variables": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.1", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "883751f42ec0a956a8afe3ffe0eef56b3f79e7f7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.1.tgz", "integrity": "sha512-2IAQtyBPk2MG6x8skAtZMKNdE9QqscfIg8srY9QLLh2Zuz4KVNzWRl4zOx8bPT65hSh26RKvTcq4QMy9JPtd7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2o7ORNbf7P0fNs9Zn1UX4M1Lcvpolif/+ln52F9Ko0wIhAI22aTWAwlbAWqysFtLVG42kq1H64Godtz5CL3XFMmaU"}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.1", "babel-helper-hoist-variables": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.3", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "b725ff684cf660001700fb9377b58940a6977b91", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.3.tgz", "integrity": "sha512-DGG6LTYvU0DweP+PeBsfRJxHsbez6tUjPeLrAEEIeiBVnrsWNCK9pZDWxAzgNM+C7wqY7kKVDycJfwVwqF/YHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE3EpY12SD/ntRWcAOs1dMqQGH6yaF/N+YJ/tR4OCsjfAiEA8wCRdpZirNpg7KIlmW1f3BGO89ovvghfp9RzJT61u78="}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.3", "babel-helper-hoist-variables": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.7", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "2e2ad4576e433c803e65297b1ca70ca68a432eff", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.7.tgz", "integrity": "sha512-dZzXLgfaBfKU95+a0QBSz9eALGq1cCFvTF0pHN6xVXPe8/qXLhzMbhRuLt0aXvW4PfLFPX9Ca55sklalDATXKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+pCB6LcTHcV19VkhrfpcxHFqnFXwdNgHOkdUiXBhNNAIhAKc+dA7KEEEc++XYI1rcg+wTKzpfQopGmdRreC4106Lv"}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.7", "babel-helper-hoist-variables": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7", "babel-plugin-syntax-dynamic-import": "^7.0.0-alpha.3"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.24.1", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "ff89a142b9119a906195f5f106ecf305d9407d23", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz", "integrity": "sha512-ONFIPsq8y4bls5PPsAWYXH/21Hqv64TBxdje0FvU3MhIV6QM2j5YS7KvAzg/nTIVLot2D2fmFQrFWCbgHlFEjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdFMaa15WeDKXykdtg/+egg9cH0jMXtNM+9MBSGMmzVAIgcceb+52AEdoGgACkf0RJnWER11KKq7ABkwRToMALrQw="}]}, "directories": {}, "dependencies": {"babel-template": "^6.24.1", "babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.8", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "212bf0e3c07231c012d9176bca5fdd68410dd599", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.8.tgz", "integrity": "sha512-qfFDNKvnbM8qWwq+36oxbvGNpsDdlwcW3eKdu/IGfqti0JtG4Ex3qejDNncnl4zs8JDWphgi0IzauPcc4sadCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhAWescf5pifr+4idbAjEOuFTCeiilyaslQm+318k4uAIgApMcsXxtuEWKelyqPVDdwBTzmJkyMik4Jm//6pTv7LQ="}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.8", "babel-helper-hoist-variables": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8", "babel-plugin-syntax-dynamic-import": "^7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.9", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "bec788c8064fd846034da6997540f99bda422acf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.9.tgz", "integrity": "sha512-UkWHoSO/fMQCDxQcb88QUpLUau9o2vcbVZYRCHMc73Z4gRqqDgb7bq/we6ht6HCYlV5RZzkHTNVFR/rhsYbDdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFywM/5SBmPB0pC/xct6XDcnL4JvUkfAM9QeY8Mnr/9fAiEAnskLDpzbOkCWmRGKuDyd8jx56reRT/Ve1uFQJCIb+Rk="}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.9", "babel-helper-hoist-variables": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.10", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "fae5209f8a8a11c158d1b1acf4db13c198cf1368", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.10.tgz", "integrity": "sha512-jGtBjuJ8Ej5n7IBGQimYTTi+d+RUMu3jWC74AOivxeH9/xQ2mrfr+Y+XyroHGly/p+UxdoocxTzqfE+KvYXtSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhZNQmVxPSCS+M4uZoKoeBmQz0nfVt2xfzR1k5ZPc9+AiB6ySeEh9xDmxV0u5AY6KdZe8ailFTDbwsD/F5Fa2vcxQ=="}]}, "directories": {}, "dependencies": {"babel-template": "7.0.0-alpha.10", "babel-helper-hoist-variables": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.11", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-h6LsUB/Uga39jkn4orMEIS/McXK51+u5pnXl+jxlnrM559JxlNrYRhSOZ6wNGSN7u4FEIKwbR1E4B4GGE4U5CQ==", "shasum": "e9d1fbec9a59b237f7175addacbb0515795504a9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxsZtz7mKsEl43MAmJNoBfEjY+fKg4hbn1g/cvuWuu0QIgX6MQU3bWjXIOfCahTYQgRWwhHmlAg6DEqzTNoYF5bS4="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.11", "babel-template": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.12", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-xlIeocko7byVlE8uGQKyge5+oYKCNQy5koi1ecJhQsppjQ4U8lpgg8DCWoYdlY0vvw69UkUa3WoDrWSsMjDYFw==", "shasum": "e43564a441df4b4b7771f3e4a3b1d2860480c990", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5XFxlHnr2MSy21CP14OQ2JsMCSQh17f1qYwonYp3nnQIhALkCQYiyhWCLXS7h1V3wzJxz4J7HAtQUFk47l3tzJzm8"}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.14", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "2f75bf4793683a9aa8bc2cb8af099ac585b9b44b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.14.tgz", "integrity": "sha512-q1a9N2/NDvlZcTvnruWOhtjozZPUdEzSiQzzSQjxPIwxCEyyQczn4MbJRILJ1ck3qxfzihkTI/LKIiS+Nmk5zw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWlovV8PVgSeIgjfpCmd7Q/5Pg1ZGzUwsrQc/CDjAtegIhAJ0qzTlGXpXT1DG3/3TAFLbTLLxuA+pmb7sHJoTHjmIm"}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.15", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "52b4305748800baa0e49aa928ed9f616e4ab5536", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.15.tgz", "integrity": "sha512-oNPkJjQ/zhXD0h4mPn4ihtUP1u92V+KCuBvo0FSaerdisnzkg3VVPJ/Xtx13wEXZbT+3PRmxtkj1/NQdsggveg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgnx4j/kN+kfsXAdCAFIdVwkHGX1JobYkImknyHizBbgIgXCsXvjCAsRGMGwzb2Fs2tH3/7sCDGyRxCaa9kU28gu4="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.16", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "a96845a9a7c797f221cf2c3d463660cc6052a9e1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.16.tgz", "integrity": "sha512-SxCo4oaQev+6Y8GtTzLSh2tiEYjsL01kYCPo6+6ZeD4/g5ZVIu+9xEeYffkErnm7SZdhfxOEfkvzNCZzFPcTFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPTNIZ3XpOWYcGenEAhhHTrBkr2ZHQbyERwN6UqHp+AAiEA9uIZWIlKvNuHwY0Fc6/aUJCRa9R0GVbKCseuMjZ8jh4="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.17", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"shasum": "9e3b87a7d5bd6cad1995a82d53b872650591c9fc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.17.tgz", "integrity": "sha512-eiCKoBXgwfYbVXyjChHtIlSL5uwVmiDejsF/SPNN+R2FPtNkKM4Xg9q9KseN2Y1KoCXe8qvTCZh/vcohizJO/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4Q8GADOnXNhyjxSu0zWXUVEg5UBQwvPqGlnke5Xe3IAiEAtYb94Kdi1NBcZlSiB5z38f5h2kB+l/Koz8mbC0aNmPE="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.18", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-n2QKL2VRd3/3h4rFsjSDs3pgcrNDQO5X33yz1X1NJKbP7aFINkWlZoCCe+JcFizUYGlKLA9Jf0YogyjIIBDFmg==", "shasum": "8ae907e80fb6bfe95df75a5f04fde50b43e8436e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG96gy3uVvXg2OanJHgANxZZNk45bCuKXR8Q2BAba27MAiByWC8jXiIuHNmTk56OG7VmzokhkJWg/1Ukjstrv0CveQ=="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.19", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-E0u/sEI6B6d1TFFW4v1bEJlFNm8a6RicsNyy3bIpRvS8wtY5RVXKEaqn7ZgzXBBPgBciiqn/JrQMrpMtr75fZQ==", "shasum": "251dfe6a1c42ad9a85f676c3f9e0404332860a6c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQqG/+RJ8C5nTTRUzvF1GcUFglBDw7POt9VKhkZAFZmAIgTsmwL6SlVcIj9MR+xXinPq4CRG0lE7OCYMhIRRmGuOI="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-alpha.20", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-gWTeffBxMiOdPipyFMCOPm2+bDryhFrAPHkNVnTiGWqNacMJb6QUEdCavOrFpfPHb+KfuKq8LrLE+2NMHY+Esg==", "shasum": "3879efc0501a8df8212dae4044cd4c744c193a9f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGFmi4gLHnUvFgTU7ibzjETnDHEZDJIJohlLTHkkulE2AiEAr5FbVoArgi4a9sWDSpR9EsySxbPmDDNS0xlou6XMKig="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20", "babel-plugin-syntax-dynamic-import": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-beta.0", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-jyBJZc530vNpHYB0lnm4nHHQnV4atFnqq02oEm5sUHro+66IVJmQ33m63rMQ4fxpMxhOQg8a6g1Cc19fWs8qnQ==", "shasum": "0071a2c6c7860e6f7c805f80788b000b09f95600", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk1soCpr23v2O8OmjFe3hKTTGGxehbTNqbf9BGRvcp7QIgAjzvGMsHrs1QNAWa42eTmBxb1f67axFhal6vSHFRax8="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0", "babel-plugin-syntax-dynamic-import": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-beta.1", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-MH2fSV+m0pI6YHXFtqzdpEnLwaJcvHiLzyZA3cp1YfGdwxkU4nE2tpXQemFk5cwu0oHG8cIzynX626MxdgXP8Q==", "shasum": "bbfeed8a03af04135500533f3e23fdceef6e2ead", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpXBaWVBKeCKkkeB2fn6SCZzrbxtvt9BR7R6ile6mJtQIgXXiwqZXEhRORD3zAo1MVJmLYyk/UnEM+jJRZ2tgoZ/c="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1", "babel-plugin-syntax-dynamic-import": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-beta.2", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-PJuJQ5igk64ziQg+5cpd8drct3WovRi1nvY187hZG6P32xEtA9n4Cz1nSgR1vCOQ0TWyKW9ZnMq9eax0bjCjPQ==", "shasum": "f25a3f6e6f01e14cf9f32dda3433594942983863", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0X9/GCJioR0/UQWR/CQuAgTBaTZ0k5dXhQENfjzqTpQIgAKF+5IOE0ZaO81e+CU8COC801SsvQkCgB/6qmq4nuvU="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2", "babel-plugin-syntax-dynamic-import": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "7.0.0-beta.3", "description": "This plugin transforms ES2015 modules to SystemJS", "dist": {"integrity": "sha512-i621ym8MDfuG3gmgq22uS6JLj+bIJl0mMsZDI/s3457aAPKDl5HwyWGjxcVZHw6WpSKGf4ulqvospsgXOMQ4bA==", "shasum": "a74c000939941d705c8c7c1e68983f35412cc042", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHN6tAtp931OSm4jnmlI1mNnN3VL0nA0es7AaDVtpbfAiAhlHs5UyrNUArf+uBOsF+FOoUwMjPgnrkWTXlL9/BMeQ=="}]}, "directories": {}, "dependencies": {"babel-helper-hoist-variables": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3", "babel-plugin-syntax-dynamic-import": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:24.183Z", "time": {"modified": "2022-06-13T04:04:24.183Z", "created": "2015-10-29T18:14:50.363Z", "6.0.2": "2015-10-29T18:14:50.363Z", "6.0.12": "2015-10-30T04:55:36.735Z", "6.0.14": "2015-10-30T23:38:05.421Z", "6.0.15": "2015-11-01T22:10:21.401Z", "6.1.4": "2015-11-11T10:24:53.365Z", "6.1.5": "2015-11-12T07:01:22.490Z", "6.1.17": "2015-11-12T21:42:15.176Z", "6.1.18": "2015-11-12T21:50:15.313Z", "6.2.4": "2015-11-25T03:14:30.337Z", "6.3.13": "2015-12-04T11:59:16.978Z", "6.4.0": "2016-01-06T20:34:50.197Z", "6.5.0": "2016-02-07T00:07:40.779Z", "6.5.0-1": "2016-02-07T02:40:50.387Z", "6.6.0": "2016-02-29T21:12:50.128Z", "6.6.4": "2016-03-02T21:29:49.368Z", "6.6.5": "2016-03-04T23:16:57.869Z", "6.8.0": "2016-05-02T23:44:46.135Z", "6.9.0": "2016-05-17T18:49:39.679Z", "6.11.5": "2016-07-23T18:09:38.562Z", "6.11.6": "2016-07-26T22:12:29.338Z", "6.12.0": "2016-07-27T19:23:23.044Z", "6.14.0": "2016-08-24T23:40:54.997Z", "6.18.0": "2016-10-24T21:19:06.325Z", "6.19.0": "2016-11-16T16:15:26.347Z", "6.22.0": "2017-01-20T00:33:57.692Z", "6.23.0": "2017-02-14T01:14:26.357Z", "7.0.0-alpha.1": "2017-03-02T21:05:49.461Z", "7.0.0-alpha.3": "2017-03-23T19:49:49.646Z", "7.0.0-alpha.7": "2017-04-05T21:14:21.915Z", "6.24.1": "2017-04-07T15:19:25.592Z", "7.0.0-alpha.8": "2017-04-17T19:13:14.761Z", "7.0.0-alpha.9": "2017-04-18T14:42:20.368Z", "7.0.0-alpha.10": "2017-05-25T19:17:46.274Z", "7.0.0-alpha.11": "2017-05-31T20:43:56.290Z", "7.0.0-alpha.12": "2017-05-31T21:12:11.717Z", "7.0.0-alpha.14": "2017-07-12T02:54:07.485Z", "7.0.0-alpha.15": "2017-07-12T03:36:24.499Z", "7.0.0-alpha.16": "2017-07-25T21:18:17.831Z", "7.0.0-alpha.17": "2017-07-26T12:39:49.508Z", "7.0.0-alpha.18": "2017-08-03T22:21:24.588Z", "7.0.0-alpha.19": "2017-08-07T22:22:06.355Z", "7.0.0-alpha.20": "2017-08-30T19:04:20.898Z", "7.0.0-beta.0": "2017-09-12T03:02:50.488Z", "7.0.0-beta.1": "2017-09-19T20:10:09.208Z", "7.0.0-beta.2": "2017-09-26T15:15:52.626Z", "7.0.0-beta.3": "2017-10-15T13:12:17.388Z"}}