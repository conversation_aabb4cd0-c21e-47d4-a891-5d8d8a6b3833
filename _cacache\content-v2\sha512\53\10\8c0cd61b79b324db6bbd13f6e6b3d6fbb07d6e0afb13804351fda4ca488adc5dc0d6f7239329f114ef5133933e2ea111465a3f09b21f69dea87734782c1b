{"name": "babel-preset-stage-3", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.14": {"name": "babel-preset-stage-3", "version": "6.0.14", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "d4e53dd040b2fb40a835ac9d783de43b42b56a48", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.0.14.tgz", "integrity": "sha512-5hhrSUGLXNf4AV4dqMORu4XIVqh5aZgvtcaFx1EntaFvnmoGffxGbFoaNvPCym16HxThS429z8MycpTYZe5Q1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0N5S58wkRAfamzUBc09/5AfzksHPnDC4q5NCoxPHLuQIhAJOX2M5ezm08RuAsiqVszdUUiLQ+saWtoUOIr1Czb4IV"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.0.14", "babel-plugin-transform-exponentiation-operator": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-preset-stage-3", "version": "6.0.15", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "eb3aa1ec15df04200c817849aa831d6fbea70afb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.0.15.tgz", "integrity": "sha512-n7ALjCFhIvhrJgcNXG3oSDNnGOLZhhzqiDXgGiBxhpSb+4bV9BdBG53zR+7SwFhJ8avl2OS7W3UmxkjyicgJEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNrNgc8jT3HH+FiccBe1Oz5bCpuLVLgcZVYHisbgBvYAiEA2JMfC3izEe9t1lCeVwjLSOI3QyMKHnlCXMVwATcRt9k="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.0.14", "babel-plugin-transform-exponentiation-operator": "^6.0.14"}, "hasInstallScript": false}, "6.1.2": {"name": "babel-preset-stage-3", "version": "6.1.2", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "434f1a2e3d7f6e66661e2473314365826ad22f6b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.1.2.tgz", "integrity": "sha512-uD+a/vC8VzAXQQgmg5aRfeDcfVu51PeEXYMfWaYWuWfGGbxe6nwoESaTT2lhI4pUu8uOUGRjCTNAlI3gVyeQGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIn4U0N8OJFonTbtVirUsaK9GqJMsX3rpbW7djr8usvQIgNm4W6XMYVwGS1tuka0AGA9cqKQOyJeQicMYm74imPpg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.0.14", "babel-plugin-transform-exponentiation-operator": "^6.0.14"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-preset-stage-3", "version": "6.1.17", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "639a41e732bd1e01145413ff9cce85160dd424ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.1.17.tgz", "integrity": "sha512-y0iEpSWF1woHX2tSmG77EkTxOCqvceBI5kmTShPo1nSlOspxK9anj0s19YWbFvGAguUziLk7wK06EKLkhMN0eQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwOjATQaVH+Sql4K0MMGZ+lAIYf4nfkPJ004J9Sb92cQIgG/UUhNhgf0eMToKhE3f/T8yhrRX+ls8fNuDIRj/l0J4="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.1.17", "babel-plugin-transform-exponentiation-operator": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-preset-stage-3", "version": "6.1.18", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "0e3456062afe99c13347281cf209ae3d2311f497", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.1.18.tgz", "integrity": "sha512-LZt0nzMmaMljbhyaMOr46vlyai7urRfQAdLJguimYHnP6tFjBY4AXzNptGBR0CYAS1o+mUVWJRUxkQzlGK8jaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcpT7ti9mfcOt36HQBrvwdAIMvYpy+G5iQrDHsYZO5egIhAJogoZsDr7sN7ve6DWDvrVdVnF6jV40i/3MyyR1N1TdA"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.1.18", "babel-plugin-transform-exponentiation-operator": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-preset-stage-3", "version": "6.2.4", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "7f69151e5808bdd9f3a2a73951c86f1a371d230c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.2.4.tgz", "integrity": "sha512-G9pJzss1Os13bZtXZA1lLvQyzhLVkq9qvVtq2xP7dHl930x6l90UqLdrBcPbvV+s8REpd1gZ33O3tHmin2ptCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7ImmXsC6FSnsW2yc8u4BqvueT9ENNR9+oD7YvS0NKTAiBAvwtO6Kgd1YiAmvBR6Usy1JincGt965N6jTKaeA/QwA=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.2.4", "babel-plugin-transform-exponentiation-operator": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-preset-stage-3", "version": "6.3.13", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "5f0aa70e3d4176ffab08b1d5cc1cb8210777ba3e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.3.13.tgz", "integrity": "sha512-puqPW1AzjdMfmBFY599uNtnXI+EP8TIOoJp4d2cdWpMz3M0WkkOmsL6alG4TT5St5FDwB18wzqpzCU2vqpLFeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGeXq+9aKzh5p5yVX+lFh/r5dFiXS+WtdT/Lb1wFJm5HAiEAgnzXU/P+2pRX89QQg3c+/7n1WzGks/7Edh2cW63wSwM="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.3.13", "babel-plugin-transform-exponentiation-operator": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-preset-stage-3", "version": "6.5.0", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "542dac58684dad1b0bc4aee85ba52851cc6e75cc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.5.0.tgz", "integrity": "sha512-9kI6n2LZueXELw53h5qn9qkIWic8WpY8MrWGurMatRzBE+eXrT7NE1KJLltNAkTEMt+i0GZaSAdH2IENLWm1uQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD16eyWa0t37vEzAOz7CBXxupFpIvHHWqKeyVlpQoYTuwIgEPVZBB+9+mHfpOhFMydsi5w+A8rFLcsYUbNzGmaBeZE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.3.13", "babel-plugin-transform-exponentiation-operator": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-preset-stage-3", "version": "6.5.0-1", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "d3aa082d1da5ab67328d23f8c4a60d75fb72e194", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.5.0-1.tgz", "integrity": "sha512-dSfZheOv3HFZuEQTt5f6hhCVIyYYbzMLS1mqObirBLjpZN2RhgTdLHfzkGR5UZG4sB8ijrVS7BcBfQgDpzwxwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGn7wSEwtOXLqeQZydUck27HlmDrJndkVOm8l+fTR7hzAiAvjDLNlQypKOXRnZtCRgrZtBg+RKrhqHCi9p62jtuCDQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-to-generator": "^6.5.0-1", "babel-plugin-transform-exponentiation-operator": "^6.5.0-1"}, "hasInstallScript": false}, "6.11.0": {"name": "babel-preset-stage-3", "version": "6.11.0", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "1315059fde5b65f6e4cec4f2b591627470213065", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.11.0.tgz", "integrity": "sha512-zy5fvF6ORQ8AtN4MRTF91/hpa2CHxFZcnmPWPHFmhJJVMuLJomGD7QJgCKizBpY3C4WeGHbVybodhTVRaal17g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBVmJY3jTRumj5ZbJEvYMdlZOHgzrtykaXNK7ZRdw3gXAiAG/j19Ap4WIRkueXXb8qE7MpM0Da4cQ01V2ScoNbGTcQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-trailing-function-commas": "^6.3.13", "babel-plugin-transform-async-to-generator": "^6.3.13", "babel-plugin-transform-exponentiation-operator": "^6.3.13"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-preset-stage-3", "version": "6.16.0", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "bf808564a359bcce2a599979be9eea0ee7580d98", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.16.0.tgz", "integrity": "sha512-TPiAjr68i075JvXo1MvtdjbDS2tFy62DKqpt6DAdvaLNJNMAmYsggdhFgMyImF+6RTqks7US4AzHqKKx5KTMtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEd097FWlJ+ige6L7zWCBlU8eCKKJf47MJ0I2kSl0KZwIgD/ntMPBxtin4Q/eQwI0Shl94orwVDM5uttvhMwUOi1E="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-trailing-function-commas": "^6.3.13", "babel-plugin-transform-async-to-generator": "^6.16.0", "babel-plugin-transform-exponentiation-operator": "^6.3.13"}, "hasInstallScript": false}, "6.17.0": {"name": "babel-preset-stage-3", "version": "6.17.0", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "b6638e46db6e91e3f889013d8ce143917c685e39", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.17.0.tgz", "integrity": "sha512-7ybUzbeiV+AKW6WVIRc36crqhi18veEcePceI0IKxN6OFLKYQgTFQVbdHfAdTji3mBjyYjYkW8ruwvgSXzzGdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEei/Vb2db8JQ/s50d/PekGoxZMv5PvXiW3DSAsY6jnmAiA1uo4SC/YFIs34p6LvpAMg4966Em5D4LZFNwXqBE9Tdg=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-trailing-function-commas": "^6.3.13", "babel-plugin-transform-async-generator-functions": "^6.17.0", "babel-plugin-transform-async-to-generator": "^6.16.0", "babel-plugin-transform-exponentiation-operator": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.16.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-preset-stage-3", "version": "6.22.0", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "a4e92bbace7456fafdf651d7a7657ee0bbca9c2e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.22.0.tgz", "integrity": "sha512-5c6afh34fe2I08YZ/OT3MS3V4lldld9093WBDr+neooHzdlFrJosAYaZCkkwdGrZOewuva1/5cE/T8fFqCELmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOo+wopb7XS27WvF7vz29O+W7AWTuegszOF/3Fbgo8owIhAPGWN4lllT2aKL557qt8TEeJileNBfAiYYfDqZsH5gif"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-trailing-function-commas": "^6.22.0", "babel-plugin-transform-async-generator-functions": "^6.22.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-plugin-transform-exponentiation-operator": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.1", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "366f69c5296da66c1fae73b898ee823a4e18e2e5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.1.tgz", "integrity": "sha512-ONBK4Bp5oSFBnqeQEp1wl0Ac73kZAxLhq394T7oduoZnsD4Ujbq21Ffo3Pkb0VPW0r/TZk0KliU/7ou+8pdT1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAt7x0VimknPgmuKCxgUtWitT2UJ/FJbrZ6aHl5gEvsCAiEAmbfR4DFa21lvd5kBlgl9B0jMu460vDsj0Vot1KtILDY="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-generator-functions": "7.0.0-alpha.1", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.3", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "3196bf3699c980372bc9f260fa0db70786d4d4a1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.3.tgz", "integrity": "sha512-fDA8KwTzx7j+brgCka56EfBBTT1kdM4Lrpux3TYkHYVre4h76505/jafBKyOMLaliBAi7sIn6NflNVP9notgIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuUvBV1sk5uHOAsqFABrO6D+VssboF4GUb8HLI2xG+sgIgZTKdpCQ/2BA56wmMjeUm5ac/m3dq8wwPAAHz0cP9+7U="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-generator-functions": "7.0.0-alpha.3", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.7", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "fcc57d7ec8d4f072b65f2e33e4d6977e7750faf8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.7.tgz", "integrity": "sha512-7UJFk+kzEDPd2vDbHnilFWo31nVgAgndciYw4PKgLLfHUH3h2DsUXubO18GhkyVfw53TWzMcRw5maRbx+4YE0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2LJt3QrH1I+FIGrutTJPOoYeW6UoCrZNuqmlVQFZlLgIgGgP7GOsHTiUo7mReF2VHrslEWqrmbvk8zIIjF03KDmw="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-async-generator-functions": "7.0.0-alpha.7", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-preset-stage-3", "version": "6.24.1", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "836ada0a9e7a7fa37cb138fb9326f87934a48395", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.24.1.tgz", "integrity": "sha512-eCbEOF8uN0KypFXJmZXn2sTk7bPV9uM5xov7G/7BM08TbQEObsVs0cEWfy6NQySlfk7JBi/t+XJP1JkruYfthA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDOpDwmZS+VTHCK9LuhRe9xJxI6r3vVtIMc90eAS6dHHAiEApJJveGSej87akFNwkOdkPg4A3gVL7Abih+eplOYmTeE="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-trailing-function-commas": "^6.22.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-exponentiation-operator": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.8", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "db791c331ee4fcb52baf1d4a4e8211ea706bfe27", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.8.tgz", "integrity": "sha512-xu3uwbvy7kAf4kgsOihBsqAeytsZCsfJhmlMsbOK8X0th4aqb+FfEyPZh9apxwYuNZNjvNyKDs6sXLiiM9u1tA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMg/BxdtBqZBhSR0ujYLQ0zdiZqVSOC5Zwjog9ZHYfSAiEAvkJ9VaJZkvQPAhwMccXiy2FYytACj02GZ+p2ljtiaiA="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.3", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.8", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.9", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "9c4fe45075f8bdff0e7213c4224b5b9aa99ddd63", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.9.tgz", "integrity": "sha512-DT5WvB0Zsl4+cWDY/yUCkr6PgFjyI5Di1p/wFPavkuDs3/0zaqs3Ro/A9al+dOl4um6MtYzNQbhy/oI6qZ/VkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFtexrgFuHXnPqoR5eenr6oPeaRpDFE5U0miYpQ99GODAiAk7o+TvmDa12LsYr+4LGIF6JBMe8t7v++cv2XZSuvm+w=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.9", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.10", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "d31deaab3f3e32c6e3dd8f049b4548be5f84ed3d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.10.tgz", "integrity": "sha512-/ZXnkioCnP8FIBcecbMpJgWCjTWxxOKt0lAojX6RwQ4KEffIu9b6v7rHIfSaVVCsRdYA78NRFQSDZis0JE5csw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7JgJXKHsaMHA3lB8Pl1YvSnlBSjfrp3F9qlAFRaVBCwIgPwwdZUpRovaOSHR6RpWIdOIoFe5ms7C5IiSGnnVDdFQ="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.10", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.11", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-4j0aTPIYEojScOOdjq9I95Jm1hAeOt4IB2NAilrB3iXt4Q6JvjSHiHpWUJ2dHteT9MCJzzfL3Nr8Y44Y7YXMGQ==", "shasum": "c8a04a20b7b6cc31c6574cb99cfdc32ba67152ea", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBiXjQFrcHRea4/E0VbJI6xHSSWzdAg2SAvQZXS3hfOtAiEAh2c7LXJMLJr4bd4VwlJvJbQ+JHVagrnLsHNHBFDwfPg="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.9", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.11", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.12", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-urVmZrsS4NXfJxPHrUoxP6TVlDqZ35HzENQigZ5Nh9f+aKNWlWT0mtafXhhp7MvIY4FrD+ug2V3ZuEp6ASHi9g==", "shasum": "eea816d2447fc38f1a27f4621fa08dda0c37016e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfFEsGWPCYJsniQtw8r6gucGthSLlEQ94xUSrf3AqnbAiAaVd5/XfPc94uvrC22a8RKPAKgePey3H6SGF0JAnb9Iw=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.12", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.12", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.14", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "fbb663a889da845d5efcf16da081d39a216ba7ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.14.tgz", "integrity": "sha512-DK6BIF5ppk1tEHy8KTKMQGoL6/l9VJGD2cJ/20l2QG7hsFF/dCsCM5POaC6TbOn9VPfm5T24UJRK1sk3E79IXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxEwYfPMLyAgLdZEKJhbpOUO/TLaSwfzJK14ILnOOmmAIhAJ4hT/R5ZsGAvOrADRsR5hKSSNCD8jas+I8HkL3nMO9j"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.14", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.14", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.14", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.15", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "1d2d2a5f4cf6c743dc04a5d9bba13f5c63561b09", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.15.tgz", "integrity": "sha512-Obqtm+EWwcdvw6sa6RqGlatPRdUr55hYoXiGZtdZip20gD3yON+9DTS/PiJVOUtGNXXE001TEI/Nf4M9b8eazw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDz/R7EOOB67dDMigNnRJRi5zFPyUp+fLJa9tuIka0b/AiEAiIctb5Wlx86NAU+19oPYJAO+t39GXbMs8N4gqw5MEIg="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.15", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.15", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.15", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.16", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "033013f6a644359a7d7947ec6a0dc1ca2fa7e922", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.16.tgz", "integrity": "sha512-WcDwhxyEnVMd2F54Ukuj/dh6Ui3kLWV2Lw9y4a6i6hNGeZ7ae1PAmFZE5IR1sJAgJ7xaZNdARIgk4btJwKMDjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAwQoePJ5HV8cvnOnuvEmpR9i30wCZWS8cFLJqrRejgwIhALxKGViOYne/6mt717oaSG4y+1PjyG1YXNR8wyyAgsm9"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.16", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.16", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.16", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.17", "description": "Babel preset for stage 3 plugins", "dist": {"shasum": "5546b6cdb03a58a239a548939e33a53acc62dee7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.17.tgz", "integrity": "sha512-ELZzA/rlqi4db/DCX+2TBQrXUx8Hp9hNKSYYh8iO5MSvlIeO0WQr8YynXKkDeayuMp+5svNgWic5MojZV9lD4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuQhkfs1GB60e6/wj6R2Q99dGMdF+XDQLwJXSPEMb2uAIhANeagKvIn2j9Jgo3WlzIqvufsU+WSHLmSdJTyxsPwDFK"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.17", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.17", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.17", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.18", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-Eff5aCBGah5mTia3yHFMx7vneYq3pNgy7Ao/1KxSmIyYlbG4bmA8M2DlCr1dmC5PILN7tQrX6eGk0Fk0UzzsoA==", "shasum": "4e003b5de44e47fc0983474c584eb7247de63a9b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyKVkby4abOSC+H8TIysDZsWlRMnnvqck1VqlXjANpVwIhAIleg8wJ4TitMLWV74cqZqj1jmlvG1UFIdr3JxjVvr8K"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.18", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.18", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.18", "babel-plugin-transform-optional-catch-binding": "7.0.0-alpha.18", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.19", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-2fyH+IAYdCJraoOmUO6D9CqFdNSWgXaU9difhnw8LlgXwWC9KcdkEXNHRA1tvDdhAdPb4bboF4JyvfZnGDs7zg==", "shasum": "9d35d812e2ebbad6318ddb5ef89758a0a4ff5677", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH2dCWAxC3l8AeElD5OUSlcZgz+pgMU6/Dm7yrtsDLI0AiEA4cvroL+1S/f5bVyjLeZZeHgzolW7pWPRDeWYCatrLhI="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.19", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.19", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.19", "babel-plugin-transform-optional-catch-binding": "7.0.0-alpha.19", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-preset-stage-3", "version": "7.0.0-alpha.20", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-28ieSr3QYVqONVRCuvciOBOJyrLG6NFGjCW73o8BWRr4dX9NaHt2QRVlm+3NbhqCEPu45hE7IpMFVN+mUD4Npw==", "shasum": "ba3b423356df74c29d5eb512a2f635c369d5dc5e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHlZqcThsMjv3wB1ShEcGFi8k9P25Gvyz5g/onOPeHxAiATYud6vLgLwBKuUqmXxfCQhTFCall26w6wxZTpA54t0Q=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-alpha.20", "babel-plugin-transform-async-generator-functions": "7.0.0-alpha.20", "babel-plugin-transform-class-properties": "7.0.0-alpha.20", "babel-plugin-transform-object-rest-spread": "7.0.0-alpha.20", "babel-plugin-transform-optional-catch-binding": "7.0.0-alpha.20", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-preset-stage-3", "version": "7.0.0-beta.0", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-ga8D3+t1ERr5eP9wy+9f/Apo2BPKOIP8QiTK+8BQo4h7vAwAPzcC4TpZU/KCw7ZPDmvWA5sRT/a2aMvCebZDbg==", "shasum": "16b3bfd4349f679012af92f20436af371ad3de0d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLHofH/qBxXLAWiH1KepEJekTJs9y3EXHMmOfglO4YgAiAceDGSdjr7Y3yZdzBmltNQwSr9rXbsf39aIhfRboBTjQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-beta.0", "babel-plugin-transform-async-generator-functions": "7.0.0-beta.0", "babel-plugin-transform-class-properties": "7.0.0-beta.0", "babel-plugin-transform-object-rest-spread": "7.0.0-beta.0", "babel-plugin-transform-optional-catch-binding": "7.0.0-beta.0", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-preset-stage-3", "version": "7.0.0-beta.1", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-St36r9Nfj94g7vGPhCtpj6mjGBsAALut4RjhR3fONw/N1ON1YBJ/bHjLMxzEv5Lg7n0P8nx6+sGto3WqbfEkpg==", "shasum": "2cb563f4b00234ed9fe4519348ddb5943ac92f9b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGuj+jXdHIle7cIdCiPpoWVW0QBR3L9ZJVM15rm/+m28AiBg3S2mXgKJTtu3xx+XnsYPnsfzFYVFBMLm7A0hN9ygDg=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-beta.1", "babel-plugin-transform-async-generator-functions": "7.0.0-beta.1", "babel-plugin-transform-class-properties": "7.0.0-beta.1", "babel-plugin-transform-object-rest-spread": "7.0.0-beta.1", "babel-plugin-transform-optional-catch-binding": "7.0.0-beta.1", "babel-plugin-transform-unicode-property-regex": "^2.0.5"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-preset-stage-3", "version": "7.0.0-beta.2", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-hJqYkFV7yyRo7hP10QwHgFOJOnEYmz9F3NIKh/Uw6u/V18d+YeR08QLCddZ7n0N3rljc7eZDeUQqLNFBNJFzuA==", "shasum": "ce5dbc3ce976d9f8d6ac2f058f2e2cf594634843", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWpiOaEj2grp8H24VxUgWutpi7a1d7+5L2mOJ0yQ6oDAIgKi5CUDIbqLKcXbDwYAVDPK19FuKt4nioFMo7XBYTPRM="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-beta.2", "babel-plugin-transform-async-generator-functions": "7.0.0-beta.2", "babel-plugin-transform-class-properties": "7.0.0-beta.2", "babel-plugin-transform-object-rest-spread": "7.0.0-beta.2", "babel-plugin-transform-optional-catch-binding": "7.0.0-beta.2", "babel-plugin-transform-unicode-property-regex": "^2.0.5"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-preset-stage-3", "version": "7.0.0-beta.3", "description": "Babel preset for stage 3 plugins", "dist": {"integrity": "sha512-ITAuVCs1YH91ZAAu5sozvwq6FHjdqIQB+t2Y+4Bz/SOssElCprchGCOQVdoLn+Kkb2oOGOZh1LzTWlGTJ4Gdmg==", "shasum": "c78a371719199c9bfaef73ae0e27d4e72f59342b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-preset-stage-3/-/babel-preset-stage-3-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtoQJYBTvBNJgKho4/mCB/fxVnKw5MUVVEDsVc8VNiNAiEAsJ0ZWD0y3c+n2WCMRVL4vgBA5h8hhoKGd/FT1qz0nYU="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-dynamic-import": "7.0.0-beta.3", "babel-plugin-transform-async-generator-functions": "7.0.0-beta.3", "babel-plugin-transform-class-properties": "7.0.0-beta.3", "babel-plugin-transform-object-rest-spread": "7.0.0-beta.3", "babel-plugin-transform-optional-catch-binding": "7.0.0-beta.3", "babel-plugin-transform-unicode-property-regex": "^2.0.5"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:07:59.899Z", "time": {"modified": "2022-06-13T04:07:59.899Z", "created": "2015-10-30T23:44:02.116Z", "6.0.14": "2015-10-30T23:44:02.116Z", "6.0.15": "2015-11-01T22:11:05.668Z", "6.1.2": "2015-11-05T11:12:20.764Z", "6.1.17": "2015-11-12T21:43:57.592Z", "6.1.18": "2015-11-12T21:53:04.857Z", "6.2.4": "2015-11-25T03:15:56.029Z", "6.3.13": "2015-12-04T12:01:17.325Z", "6.5.0": "2016-02-07T00:08:25.309Z", "6.5.0-1": "2016-02-07T02:41:48.318Z", "6.11.0": "2016-06-27T00:21:34.800Z", "6.16.0": "2016-09-28T19:39:04.227Z", "6.17.0": "2016-10-01T19:23:25.936Z", "6.22.0": "2017-01-20T00:34:27.638Z", "7.0.0-alpha.1": "2017-03-02T21:06:05.065Z", "7.0.0-alpha.3": "2017-03-23T19:50:02.770Z", "7.0.0-alpha.7": "2017-04-05T21:14:43.888Z", "6.24.1": "2017-04-07T15:19:43.575Z", "7.0.0-alpha.8": "2017-04-17T19:13:33.433Z", "7.0.0-alpha.9": "2017-04-18T14:42:42.169Z", "7.0.0-alpha.10": "2017-05-25T19:18:02.612Z", "7.0.0-alpha.11": "2017-05-31T20:44:07.581Z", "7.0.0-alpha.12": "2017-05-31T21:12:21.806Z", "7.0.0-alpha.14": "2017-07-12T02:54:35.673Z", "7.0.0-alpha.15": "2017-07-12T03:36:52.721Z", "7.0.0-alpha.16": "2017-07-25T21:36:33.959Z", "7.0.0-alpha.17": "2017-07-26T12:40:25.160Z", "7.0.0-alpha.18": "2017-08-03T22:21:49.352Z", "7.0.0-alpha.19": "2017-08-07T22:22:32.683Z", "7.0.0-alpha.20": "2017-08-30T19:05:09.701Z", "7.0.0-beta.0": "2017-09-12T03:03:24.308Z", "7.0.0-beta.1": "2017-09-19T20:25:04.643Z", "7.0.0-beta.2": "2017-09-26T15:16:31.752Z", "7.0.0-beta.3": "2017-10-15T13:12:54.399Z"}}