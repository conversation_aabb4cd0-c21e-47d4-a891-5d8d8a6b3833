{"name": "@types/lowdb", "versions": {"0.15.0": {"name": "@types/lowdb", "version": "0.15.0", "license": "MIT", "_id": "@types/lowdb@0.15.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode,"}], "dist": {"shasum": "e672ea150dd17900df9a195d909aff5ffba370ca", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-0.15.0.tgz", "integrity": "sha512-oeI2g4Xe+8PC1KNjVSq1Msc/ZNuO7X6wtCuHDkY/JRy6ZcoupV0vOCSYJayXCw4/4o2X9eIxTB8TZJe+6TdrmA==", "signatures": [{"sig": "MEUCIGRg3M8ThZbEPr0oegGglMUR7miDzVnlxzUodhfKxR8DAiEAkf9kBA02/bDSJNCUkX3g9K26+CftTE2lKyF9NXZ9lkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/lowdb-0.15.0.tgz_1490142840782_0.32285380526445806", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "3c7a0f92437a269442d3b0d7e86e3c52deabed2799cfc9c9d51969d50e03be6c"}, "1.0.0": {"name": "@types/lowdb", "version": "1.0.0", "license": "MIT", "_id": "@types/lowdb@1.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "603f73895660537f57cb248c1169311b451151bd", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.0.tgz", "integrity": "sha512-gYmB2gACsmtIOnmH9fIrxclLBUfvSS62pIfvKwXNTSkOt6wTH3OKjXa/TJyBfJYgyIQlJlCkK+9oJ9YvwdYwLg==", "signatures": [{"sig": "MEYCIQClOhQpmrfawQx8k8qTLDnH5gQc8rGuUnxo7ZitlRmvrAIhAKtcDBHXZ8JaoRdQDXH0bU9B00LLHA1kbFAM4ENnZzEU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb-1.0.0.tgz_1514421469028_0.12181786843575537", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2395bc758711acc23a075858bc0e0d28e46381adbdcf7ee3495a37049d63cde0"}, "1.0.1": {"name": "@types/lowdb", "version": "1.0.1", "license": "MIT", "_id": "@types/lowdb@1.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "fbe7923afbd0cd686d7a095792b9859cdcfeefb1", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.1.tgz", "fileCount": 14, "integrity": "sha512-tvpzG1N62KKdmCXdGgl3cC4wOJ2LJffQCQN3UHUznHFfLYx3U8ojsb2bRdflxoZiYwAgAlF7ROC6BIxfd1NVCg==", "signatures": [{"sig": "MEYCIQDW7myq6vfxEeJ011aCFQbzlt26zzpg4RD+TNQIDM2FzQIhAJAi2ux7zFS5a5ygdy5voiqgH/LmyNypd6IxfqFIC/Yk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa378aCRA9TVsSAnZWagAAzr0P/jwKNrMKCqT4zx8q6z83\nYSkirvbZjHrklp5M1DhT36UiawZOAMnjmxXe6BqlCCR2/bBW2pOBSlBiIr0k\nDEBYD9bI+Yfwo7u7W1pW8ypKAymeQrSkvYjR77ofJ7ahxWJc0O8UFpKqEUuv\nzK/f+8s8d36OAHQk/IOytAEK8r7wQtKOHHMck+81ZTjuTVI5Utr89gk37bF9\njlZtq0WUxgljuVV+JeSU4TGgz576AhOtYPVMWHRzZOlzDEzfKn/yLvg1hnr2\nX+5futjPSY6MsaIh5IdwHMfqMhIdbsDYiK3VHCBTSNQcJg9pDKLkEmiF+7v5\nw1/4Ru6e5jff/tdM4R2u7+AcfeHKtpZeRpLJ9YJyiBi5IF3z3z6SVbjDovbU\n4ZmtiaIkKEd97mSSh4ZtJI2SZEgeA3y8QUaOklSf8sxiRNiLp9fiAyK5rEwq\nrH6pXNUcC0Km5xbvKMQHnrVjlWh2DlTAlRY2NX0eh7UGAtTXQDmI9BEyZukO\nSlhpO2IZpL60rmo9Zprale6RcjJDtlk98tCtxTN2uVDHMVU05krfUVEH9UCS\nUCgWPKNscwhskuecYpX0DDngv1pAFSClu23ue6ccY4HK/DZbHe2GP58QNz/9\nSAQGDXUXybWrMxzraWdIUQbySFgmbrYiczcIsQ566Urw35SItShMlb/py5Ns\n+Zx1\r\n=Cwvh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.1_1524612890432_0.15269815235593787", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "538c2f9aeca281393cf5b32bc1d7d0c4a2d46eb593d5d1e4b14397e48e768965"}, "1.0.2": {"name": "@types/lowdb", "version": "1.0.2", "license": "MIT", "_id": "@types/lowdb@1.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "e24d8422aed75d79cd71ef12c94fada3647d3d66", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.2.tgz", "fileCount": 14, "integrity": "sha512-0lS8jOba45tcXne01LXkw06x8uqpIKuh8LTwTOo2zmIXCVoXXmIxAemAGoLJvzNc8Q0qBG+fJT0xJMx7N0FLtA==", "signatures": [{"sig": "MEUCIC5APgvZky+d9MjL1+TvoxLttcWlbo/Unc161Qf/wswlAiEAwSH32QTMz1yFiu3HDpkYvvZURjCdf+8XlEAr4OLLCg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/0DSCRA9TVsSAnZWagAA4rUP/ipI+iONObt28GFPeH67\nz1JPeFtgNVBMsnJGKUXK0cWCjebBHZ7wrwGCh55o0uQG2lvKdb6f2qmwTQry\nxmUSk61PAlFeDIzpBQ1E8LhGaxomxMmy6/Yt7i7D2rD4OPb60qq9CDyRX8iQ\nPsoeS0xCe62QMlJKO8m0D0RzaSMaM+xHbhwdqg1fE388Q3kI4bIQujZLJw3h\nIvtF8+s0ckeadSAwzEuqH13s/79mPSffdcYf73NziAhThp3A2PbRUy5a4mFj\nQX7RJf4fQHmfEFneUNpFymdbCHEwMrnpC8RR7fso6/uHXvReE3og65ha+cjO\nlu5aBOjk9XZQDESdmooOXdYRDIxFxx/r+MOUYwNao0X+Dk2LDVpAnpIj+UHU\nA8t1fTqBNPlBrStcBU85QdzY4nshL7HGahB3o/o2yxlktDB4v5iLPFaXjNvO\nVEOo0yhVGW+Smi4O1EDBTEc2O4yyzQYWyL1piDc7tpPRcPcNwI+DnCtEEEna\ngme2Hqw05stgAwe0utB8vsEmlOKnoRRDpx5tMJJm9GZ21syBlk49IUqGz2Ys\njB53Dsnm1Raz00R8+CVZsFlU3PUwDQ2pp2ke0suy7RJf6KhfjJ30eJYI8xgS\nAkAYffKxO//QNoAoYWovR8GtIjK5r3d9pCTSZKgZ5YDyyRft/6calKtgLgJU\ni8ac\r\n=SqB4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.2_1526677713981_0.03497001193068616", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ded0d64bc1271e9acacd996c2d224486ba564e60d24f828240cd499f67e35e33"}, "1.0.3": {"name": "@types/lowdb", "version": "1.0.3", "license": "MIT", "_id": "@types/lowdb@1.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "24b34e4bd3e51e5d9a4cbb51153ba4dec6aabc04", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.3.tgz", "fileCount": 14, "integrity": "sha512-FPipUkAZ9GxgkTUnFrwfAqACh7J3IuQVnPVfzPjwWGtiWFe+cV9S1DlmNsex6KGrfJcYdwOx8+efi5d3ox17MQ==", "signatures": [{"sig": "MEYCIQDbpum5IAEEbH17UfPvAZ7jbk2vdwTHF+xrbBVJOr5wbQIhAM2h0GhK5MaSzviw573gA0yI6ulU2i8f227TxZ1zWsar", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQ8aVCRA9TVsSAnZWagAA8iIP+QBLYAnBwFgnLaK0/mmM\nAzRKc/43E8OApM8x/4UCHX4hOE3XsfczhahU1F2y+oy5fXdNRkj88FJiHiDP\nRt+vOhSKSEmqIjFvZ3W+6uC9bmrZuGgTY8o9E3Aa6ZOp6bHxSeNVVLH1N7ky\nNwD81kcnVbY95FHGNCqgwL9T6zu9qrFedtDlQ1rMP/otD/o8muR3PtDG/Ilr\n480uY5hCjx5CsRvjBYQMYdNdfauJ4UQdTJ5q0g6E/dSxZTadf8tgb1WHJvtn\nypo4xlnZb04wVrE4ehrqMMzupK8q+kQHD//Skv4PQnam09WkUKNsDkWSqCIR\nDi8r2ndRgfWaYP3pxileN0ujBDC1fuFuIzzH1pnixow3HLM96BU+SCRirJQz\nzYc6D6jFBM1vfaBWLjyVlpzZKp58MHu6yv9o/Iz+Fjm2Hn8+POAA3vcwjpAQ\nAsImxqpQu+7OmPAlKq1xpAzH1uIDfgmrYPbMgVH6YSxowCb6P9Rt2onXFlZN\nU/f5cruFmz4X8w0HHnkx7l0DO8GVATKWfqOnMLZ7mlv2OOEwMihocNHZN3LL\nMvHsN1sqdvlA4ElIAfBy2ErdJUdvBJx70g63Jjdp9+ixFF4LObg3S1F1XIOe\nGEbtUiwauxTNufwF/AP4HfXPbFlhDppMLtiByR1IB8+RXKxDweUFbPSMNUoo\nAC/0\r\n=XwEk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.3_1531168405816_0.7895735118850054", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f25331bc412947f8112cc6d1b7a11db0d2acd27cdded95ce9d3df83831aed08f"}, "1.0.4": {"name": "@types/lowdb", "version": "1.0.4", "license": "MIT", "_id": "@types/lowdb@1.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "aeb9cd9d1c6afc89019702d3c214d25486cf4389", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.4.tgz", "fileCount": 14, "integrity": "sha512-wasQr9ghrWD9OXnWvXKwsUGWRgkvrqpEnTXTT9pYw1KeNUb1QWQ8B+YBLYYIvLcM6uquPzVAJWIFckPlFnOBpg==", "signatures": [{"sig": "MEYCIQDL+8e/834XbchYCeqZjnP1gT1zi8korXUOCCvyINVMdAIhAJXWB7PAuDjA+wUC3XGIUSwnF7tFcQZotLd/QsPyYC7w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSUyXCRA9TVsSAnZWagAAFYgP/0w6BxmB18rcqABtC+26\nT2cyN2n8LzEX+0OpqLLSxyckDKZdpZdJb5KsiVoApuStAjjtPJD2FYwOb22M\nllETRF8UL0emcIGezEjZBFOkhDNo7w+RG5nNEipWvBqN0yuczbkopPH3+UAE\nxFvwMFzXmTXgbdZioUrGeTo1fwCCqGYZnCqZUEyGFugRaDzJyWCaJJlQy46H\nXJH5RvVozyvAdL9Db0Nht+m21ejrpNA61efMGdHDO0TVCGuCB6T9dApzi4bF\nVRo5Shdv1c6HKY96fwr5mPFm5kIZpoE/n+lLN3fFjLV+RdnJhTANZx4LnSic\nYdABOhacsFu669jnC5b8crnzxxfG2xuomI4u49U5wbj6i38mmCgACo8zr0tW\n8Rp4wAtN8/lhlDSlC7YY0rB1ItXD6XncLIySSSlhbO0Xw5cBL3LczZOBELXp\nu4DegCkecgKEG9UuTFIYGwIPD5wVerIQ4zCoz/0yjSk4aWYIqWxkvqdBc1G+\nvRsfjl7wBOvyv/5mqPv6OoannMGYbVH4HmdVK+2jw7O2IsqVDoYdAvzQTmT6\nHeGd954DljixXKbp7D3LMlv/ovI4pNT4V9dB//gGjjH0cF9e6UYJUycqedX+\nyBbzIhhQgJZZIqBXxl9VKhww2wBI4EeWjSen2gDIRD9rpMtRASU9PsxizGpm\n5bob\r\n=FYrP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.4_1531530391239_0.2562828022815653", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6d59b95eafbe1fa4c60c3da87bf1e70f0a357794099cc816853a1c0b8595cd85"}, "1.0.5": {"name": "@types/lowdb", "version": "1.0.5", "license": "MIT", "_id": "@types/lowdb@1.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "54034ec057d260e525b5a4603bc279a13fdbb56c", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.5.tgz", "fileCount": 14, "integrity": "sha512-UaaxeB1wUsDAXf9Iw5iulhR4hYnrbxEABVBze/Gkd8yhQzwA/Ay/FqyH2G8mo7T8tnZro6jP6ealoYrmirh1Yw==", "signatures": [{"sig": "MEUCIAne2CioMhWKl1u5SUiI+nowL/ylQOpPkXwaZP0zMDg/AiEAr2cJV9uKzQxlbl2Hl+OsHywz3uv54zm4hHMEKPuFL7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbY7KwCRA9TVsSAnZWagAA9eYQAJD0DF/kaDWZzaO4eNw4\nQoYEFgRuzmOJGQJwO5SHcwL19iQlFVoOF78S4karwtAm6z9mVvrs2ktQpisY\n2rxM/NnPgYEfJCpVjlDh+wa3FKVfXVN8xYMSjgd0g6huEw9osx9CYKvpflO4\n2WYt+1gDGNo8634EXMu5n5ENIO1yf4sRsE31X1lEHXxyz5TAcsfc4gT4HBYm\nA4G6KRocrj5l7/39/o9vNazcDksrrBYsn7/UPUEVCIKJ15xKU/QmRFXmKH1t\nli0GJTvZZ1IGjv+TEemV7Zluxi5hRXFoK5JyKMbP76nhlPe2UXh5N9tw4Ci9\nyQnxm5ugkgE37jIvur4qVBx7Pj0MYJnG41XraM2x3jzA1c6D31LB9ZfF2eEy\nbg46g/oschpE044kmLZKY7QX1KJ4PV/G/frPC42EcN4Ru7WoLGEIkZdj8lUl\nT9q4jtGsVokYLJrpcb6IDRsBi+XCwQj+Im1sgdrhLbEHWqw6PJjI++iaqfqW\n8KD1w3w9LzpBBI4KiXMLiSCkSQh33S9DHLyYGUowWrRuH59MOCSOpDmITCyU\nPjRc/qRngnWshp1VdPj+TErkKbUnYpaqPqAZC+P1ZGjtk5CTb4xXOq1GipFs\n3fRmuyiYiuOb0okEd5MRk+SvVtWODK3T6Nilu1cdVG30p/cs6fEg/tTO3PVu\nWbe2\r\n=NdbG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.5_1533260464304_0.2547620598297309", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fd097f59a8099fc6c66ce5b0d6c476e87aa487999372e07d6057fec4173bd433"}, "1.0.6": {"name": "@types/lowdb", "version": "1.0.6", "license": "MIT", "_id": "@types/lowdb@1.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0e7adecb87cd79c1e97d50043d9835b65d59023f", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.6.tgz", "fileCount": 14, "integrity": "sha512-C/p2p3ud6buHPUaj5QTN3gGera9Pi39aCQoQ1ngRZ2hsWeoqok4aCF/Jjj8FDsnSOTaQHrKI92/KHGt6S+Oy+Q==", "signatures": [{"sig": "MEYCIQCyyutyTb5zPjomdk6XB9JyuiynynpNl7M7lX25hyfkVQIhALg1meeGKCegxIRweZ+Na1tQXS1j6jt33xo8UnkoWyyB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvPDFCRA9TVsSAnZWagAAS1wQAIB22YPTcnTgVraJdsw6\nNy/2fydqJFCzYhcy5DRhX27EDz7grHSUJwluGz7xwooAMug2j+NuFXDba53E\nR58+s/4g+k2l5W0SAq4K/1pxkgoFriO5tywabgOBDV/cUhnC5W1T0kVscP9+\nC4QbivG2MLGriFl+idiGv+1/XC6onkew0ZQgH05CNHMUAZS1M+IV0ximWPR7\nIpMq/HSMYv+uy1WWNCpHzFHbNT2/5AWy7YT8Id1W2ryvJI0aqubj7qmGlWSN\nlBuNTnamsKp2bPZyS65uXF4vFAI/1O7vBH3fxi5Xv7qL2J052z7jc2sqzCCu\nNBJQUnWZxLw7/JZIPj2glnl6EyYN+Opr43IaIXx0lS5gAJFEGcih/l2hlTN4\nNLtjxrbY+yDc4uGN9/5Zmo0zQwNXIMNHU7Kg1LBKkt2qM09+kfeDBoU1Kn8n\ngDSEX+b8ejABBDjEyMuUa7qjwyGCg+L6DMBlN1djSfCCDhqwmCIIW3jZc99O\n7gUFVWmi70jPkiV3HB/RBfr5XVaq2+iFrrRZzB9gavGU3t2BchYXpQIkYzwv\n+Zt50kENSGVZtT3cEoqHJW/Z8vp++BpcyMCmVpgGhfsmp3uhmbITIzVi9Tyh\njHkgN4sNwD+NzqBzkvotb6PtOtvw+Zimtg/euweHET8Kb+0Lu3bvf7cdOfAx\nj1OS\r\n=FFQH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.6_1539109060554_0.18574405883744038", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "da0a1d8c62043192153a5b489a5cbcd96c3e59811bf8420ffdd09096a0163863"}, "1.0.7": {"name": "@types/lowdb", "version": "1.0.7", "license": "MIT", "_id": "@types/lowdb@1.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "ad87647f7dc8e9daa9fe98f197956a3c6bc1ced3", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.7.tgz", "fileCount": 14, "integrity": "sha512-lN+zGYeEYCSI895/lP+vwEhYJW53rA4rxKJjW4YRFUbo+qFMTSyno4YuWsZN8+QFk0FFA6s4eMMgQH1KZPZnIA==", "signatures": [{"sig": "MEUCIQCFkWBeAeNNlxiSfQF+pOMjnw90x7zgIBPK7fZFFuUVIwIgeTBJLH76ImroVsu+IFO/MJ5q2aF2iUnyFZDPMHcojSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfbzZCRA9TVsSAnZWagAApQcP/0HgyoQ31dr5uqodqPFN\nGVodolKZW7AqoSQS2ymk40tVgPrtbpdWVFGJLKP1fbyIa0dpgtOPTU0Vdg58\n0VYWAX8gy9M29UygzpfTlqQaJHX2Wg0pXNmXbkFVUyaqIA8DGE0bIZwsek11\n0lNT7e81f+W0vqZxjp8qn0E/jW6dga5RwOxIV3F5IU4iicGXxUez3CoK07n8\ne5HdOrtypOM3TdXIb6ObXzEAW20fi9ugmSj8fP9arhBdPW+kHDY4ERa1KfJ9\ntnnDJNi2eILc89BApvy1p0EOVdLjWS/HKfCqP87r47JgFFAG6rZkX+51TONH\nVNWtKrPwUvETWlPFtmCKRueUGqkfMT8VoDMTt1ZXgD9x83sJxIo7kKH+vb3x\n7rVuCbfc008ut984e5DJTjQDA5mUPRAq/TkWopjJnk2o1DDVSqfi2auZoQfr\nHIL6EYbdXxlCvhS+4mARPexz7Qo2Q40i16QuNLqYrnlC6V9yXBkFv1FUQzuL\n2AshIKhhhQVZ+GmvHw5oofXxkEPI3xYF4lUs+V+ZNdxf5d/t3mKUxuDuF6s2\neZ+AtHAUU5j6qOIDLlgmS99U3AaCCKUnDlUBUDlFPHSRYvn3Bpg1tnm3p0wh\nYQ1Br4ei46SwyBLIECii/m7BT4WTMySFeKLMw0tGJ9PBzUqlQC0wmY0gkg2a\n5VXp\r\n=GgaL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.7_1551744216501_0.33573255398276736", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7aee7130126ecc17dd228454a95ebe542fd1be3e8df120ebe27b878da0775766"}, "1.0.8": {"name": "@types/lowdb", "version": "1.0.8", "license": "MIT", "_id": "@types/lowdb@1.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4bbaaa8d42c38100403233543298790f06f89160", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.8.tgz", "fileCount": 14, "integrity": "sha512-5GwjHs2BELxlGOCdhjMt0LoQ2iWf6l1RZ6rSpI11v9hp7oSleYiEJ3pMt8QAa9d6Zo52bxZ9b+CXYuCx2oEbVg==", "signatures": [{"sig": "MEQCIAEsHKjA5PVizW0HxXtV2dvxgIGNtGycWrJzb7LhjejcAiAFpy5Rpxt4zB2K1t8RtthIcojVaNlJHF8rtTEcFRLLQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0he+CRA9TVsSAnZWagAA6foP/iuNIt80tlc+4ERhGI0r\nhAEspTwHw5NUT1H46OTGueA4/iM9JDDmiF19RiGzitd1V0zAVWMr9CchETBB\nIHabzjOcuwBczoiDeZUnO6upbHl1+al6TDAvbs5NBPI1GffUVexJtjKBCvrk\n6RLXYcnkLNfHUUEPBtNGtOiWvqO0ZEPUeIGUymwLyo5YrqxP11hxn6PKRJq1\nw5IhR1EvCfJ+yKWyaOxlom2H5chFcY34/xLdVA/6WRmId127NNB6i2VTlMw/\nNateon0eQLAnEXTj8sc3PXXc2Co2VB9hj8QYwdIjIKcCIvKkPwGemlf+JH0Z\nKMAxKxHfbLs8CNfRPo8RH060MCht7iwGvDe1ed5ujA7ByeN6Kb5Y518JiGqJ\nY/V4c3zOTpwLY4hSUNQhmDNx8QasalR6gZOb0gzP5x9NJLjSZ2TXbyN0Un/y\nKgbPfC4HSaIloD4wN5UtoqFKZbFto1aqs7SVY43rJgYlzODtDmNc/zPxfoZx\n1jgFR74+jNT6LQeQ3eHFrRG6AnyxlpvrN01HBzOodma1GbYJF0xYzAec2rOT\nzoq5YC4g5oaQrdrfDqq+q2yyGo1YY/KhIEyNO1ZltiulFD8XfMAb8CD8gMNr\nMh7NAKBT2eRhw00lbhfACQullVg8T39YHl85golWbj9jOfVXQK6uUJajaBTv\np2Hn\r\n=QnwJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.8_1557272510093_0.9684932519926437", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e1de0a7a3565d4184de63ed1fa6c18a3df65e572b9bab0bd090bfd01600ff4ce"}, "1.0.9": {"name": "@types/lowdb", "version": "1.0.9", "license": "MIT", "_id": "@types/lowdb@1.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "1f6c27df72dd1c64522cc9a4566796d2dd058d38", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.9.tgz", "fileCount": 13, "integrity": "sha512-LBRG5EPXFOJDoJc9jACstMhtMP+u+UkPYllBeGQXXKiaHc+uzJs9+/Aynb/5KkX33DtrIiKyzNVTPQc/4RcD6A==", "signatures": [{"sig": "MEUCIEJC9xiz6o/NHK0Vtz+Dj2BNqacrFR2UyKOaX/QpTLylAiEAnVc9X+KWiKM8epvkTzRZleMcl+Iqapr2kr4Tn7XMztU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1E9SCRA9TVsSAnZWagAA4cQP/jSMQizJxtqHevw+g5wc\n1WLqpuMyO3jF5oIwanq36W2j536iXRUs/O5+rUMrax75aMvlX/nq1gWULJck\nr2LxuynA+FX/3NFm6DpM/c0w86jxJL+Ui1MQXq83NlJqDBw9raQQGntL4ehF\nfSwke9IfoP4FZhICYAQSxFQHq3D94yRB5Uwdot5sHpI3w7hniNKb8EDcS/1K\nSIbvH3VLzWqCB62SBvMl+7xfu2w0ng+s/6gmTBpwl56HUD1DKJZklcSJ5Ioi\nuhx78k/voQh92GpxmFHOWRsxdeHJar6wlkyU4qmWXw5TYwddjMUpKR84IEee\nnX9V3xNnpKJhHSXHsUwrycVvQmLwQVIfQ4r1F6VnevHmMTBNmwTEm8e+13lF\nWrMZCK+XVrS3e3i4qtb4u7ebjkbjWcytG5SyplfaNs7QFqxdQwwW2FKyzLXD\n9n4DK8okzrrOtwasJgZ0+ldRkmu7Zf24HuXcNmTRDyDpFcQOwgYY0wluTUBZ\n0e0Uvr0ANyCBMGFF9yv9+AjNZeQiHXUkgJiErOP/GnNBzzY5887SokWgsX88\nOfnV0rt5aCSm/3IT/dAYA+tUW7mPVFZiInY4bDJFNwUxkWbDFxIC0ychoGNE\nVBEmyN2z4DDu+NLKdpO9ZFgi03k0Gg6/9uY/wdyEjimh2XAILivcZGebGha2\ndqZf\r\n=tQHu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.9_1557417809997_0.20448456155620942", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "955e1e0438632d887127f2d0105ef9158eabe7ac556fd64362beedd70c71c78d"}, "1.0.10": {"name": "@types/lowdb", "version": "1.0.10", "license": "MIT", "_id": "@types/lowdb@1.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "6f9916d85fb535b3ed2dbf24c4347b3af9b11a72", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.10.tgz", "fileCount": 13, "integrity": "sha512-DmmY+UZAAc2cs4pYnr7WOp9tFYrDeQ2cKSC5JuAQ2ai40IctBT/ef+Qm2ulLOfqR7POqR+5hnAouU7hojCoMCg==", "signatures": [{"sig": "MEQCIAgZ0pXSzXSuKsvPOae7diJzq4gE/bwG3nD/Z5GfitOsAiBINW+DVEIrVB8n54WSdbIxJnRdqzVG2+rbWPf+cNjvRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgo/pGCRA9TVsSAnZWagAAs7gQAJnELLm7JfPv+Ka9DFc0\n4vT4OB7xZRgWazJCzL27gmt48Qw9YAN0f8XAXlkXlPkCC6bSBPuZOCewmv8U\nMBVElf9GRji0am1X4nvfN3YpGtIhzBEwpEB1rDo1VxchQ6snqoMLbkUYeGNU\nxiNuxBcE+PmivoHRF0XQRVSE0BFPER4+qWv5cTyqkeDMmuORhQHHta1I+x2b\n5tEpmPRpeu5uj/v0xfrpj0vyKDad8+TTkFVHTKq4RwuPvbQ96wQYf9ngj1y6\nG3eBEtvMjydc4ShB0LZq5io/5vRJWvUAtheIxIoJSUaOMg6KkldzQbCUCTpt\n4sycS+GZaDFrgxB8JHsxBocE28luxpp6FT0N0QxDjDMcbTG4EGopq0if6iqD\nFULlx1IXtt9bUQ1QWffzdG4Q3JigD4NWEzO+QOvlkRJFA/lvzeiAGlzg2aR0\nRnHaslb9Trgtf8q9MXqq7NJSpPJfllccFaW7+XKLUwCPy4057uHOE3KNtRLJ\nAekwMk/HX1JzkL7+CmgDSLlIM0SlFP+GmXGsvP+wNc1CkB7N4rnKg/qlo0C8\nIUvPcgR+RcdSzqHPreuKoPN3LcOc4H/XZHIfzV1adYGe0mcI7y1zFLL+TWCH\nebQWHqYc0SmD+nV9qvD7J36xyN5spqjhJY8XKrsqMz1aq0QjAUZpiXC6Z6Mr\ndN+n\r\n=ZrRa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.10_1621359173760_0.365394870495237", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d3895f3994c16eee7eb7d2b886bb28e2d84323551064af85c8fbfff81732e09f"}, "1.0.11": {"name": "@types/lowdb", "version": "1.0.11", "license": "MIT", "_id": "@types/lowdb@1.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lowdb", "dist": {"shasum": "d8336a635ea0dbd48a7f6f62fb9fccc5ec358ae3", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.11.tgz", "fileCount": 13, "integrity": "sha512-h99VMxvTuz+VsXUVCCJo4dsps4vbkXwvU71TpmxDoiBU24bJ0VBygIHgmMm+UPoQIFihmV6euRik4z8J7XDJWg==", "signatures": [{"sig": "MEYCIQCW/zgQs5SAlv3QdofDN/93E840gI+95uljFNspN15vFQIhALRGOLxX9aeGKCdWvC7J2xmeGh2hsdUZEcUyDKH9nh4H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5NU7CRA9TVsSAnZWagAAGCQP/2Y7ox7m68rqg9h7DVuz\njtxKaQhlz3l+e2ha3pdv4UYqoIWc7G/+2UMtw4PoyzwaLe46CYdThIfMPzpr\nRk0TAqAvWD6e9UB8gvMcLCQSe92XQnj9imqOJcEnxsAlu8ilyYK4a+Ch36QW\nD2KOWATy8FCFUyKaI2rFhpoUKQu8B/JVu/KWuTflxeC8oHqkrle71/1F87UI\ni7h6+oUp2Ri14Kp/CWcl8nteJKgKy9GfowAYMoFGTIGFszx0fARSAk0ptgua\njyPNqoelvuGRRFrX1qgK++nLtiGpxu1n98UrWcJ1U3k1K9UrzJAM2Pxa56yt\nW7uFivn43ES80mg4vf/ydSPmY19H0XIJ1AEFHTGeRJEWThjm5ao5WL0oBfSj\nDUol3+hpEPGpqnTQ1FMVCJnaXcOZK4MhMZe5W1KrJ3i1P+fa3yHsHfDgLRl0\nMmAPzj+W0XYLlbQ45v1ayEUlICGXZhHlmjy+ZaTUWhzFX+y7+s0eFuizxF+n\nkO6b8TEuk2cJsw2WA3K+w8W+uP4ucn8k4K217AtS8I6NahyeJ+nf6dYOzoyN\nMInxDvAY5/UPibjxKmn+j99RVmfrwlVJfN9w8PwZhsWu5vv9Qr6lgO5+3IbY\n5rpSVV6/wmlXkyUHpYLVAC5bfYglbaxR1s9exg56lj6SF1KP04opcHT4dkIb\nbGem\r\n=32U4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.11_1625609531401_0.9860618597281512", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "be8d09a197053077a524071ee7f52be9bc6c63ed2a9bb779ebb2100212fff732"}, "1.0.12": {"name": "@types/lowdb", "version": "1.0.12", "license": "MIT", "_id": "@types/lowdb@1.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lowdb", "dist": {"shasum": "e9828264cb5cc5ccd5462c437a9a625d34088f7f", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.12.tgz", "fileCount": 14, "integrity": "sha512-m/hOfY7nuwo9V3yApvR6aJ3uZP6iNC74S7Bx5BWz0L7IrzjKyzUur/jEdlYWBWWVjmkCz+ECK9nk8UJoQa8aZw==", "signatures": [{"sig": "MEYCIQDGRaOrBNAWm8akbSGfjj86shcK1VQ2bPPNn3lwUJBZdwIhAIbuiJGmqCoV/Fj0SzDOGjIu8MXe5bABfGQ2Qb323WY/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13431}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for Lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.12_1693425438673_0.5587629099525093", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85587f02c16be417d632ec98e981f40c5fe1ae2606de1a8afe790f7c66542cff"}, "1.0.13": {"name": "@types/lowdb", "version": "1.0.13", "license": "MIT", "_id": "@types/lowdb@1.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lowdb", "dist": {"shasum": "0c7be910eb4a0b83cec9bed7e147eeb88780aa1a", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.13.tgz", "fileCount": 9, "integrity": "sha512-IBQwi4NYT7bxWtzsQzvXTGTPC52w3brUwuzPE2oghVCeNBvp9MWHpIBqw+Igd4SrmcBjiT9K0E78Ia7+Ml7EwA==", "signatures": [{"sig": "MEUCIQDfGeA8vhUgrIY/4tTryfKrKjzkq00kY0FRijEh4DeS+AIgK0bE9VSjcRP8Ro8Qw3GKbLKpnqo7jhXmzwSQWizCsAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12636}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.13_1697616421098_0.8331723915270199", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "558871081eed2f1de4e30d767b9b3cad2f4bd16773c6a2e0ca4af69190891e03"}, "1.0.14": {"name": "@types/lowdb", "version": "1.0.14", "license": "MIT", "_id": "@types/lowdb@1.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lowdb", "dist": {"shasum": "966f79e444767c9192a27f567699d0d8cd7d215c", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.14.tgz", "fileCount": 14, "integrity": "sha512-t34ssKfsqGYnRCjkDOMUsLQb4yfuYV6Fl6iJqU2RIiAxuzlvlrMNdMUa86fguWLCt6RFnId2G5pnSIMJ28McnQ==", "signatures": [{"sig": "MEQCIHUlSBvbpyGUSj9hs/JC040ryJ7kb9LxCXRuY7eVn5mbAiBvCW4NhQFWG+dYiLzt3dM2CsDcKTZ1blD/puvI6zpLzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13110}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.14_1697653629605_0.5072794725745295", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "09de443eba3eb94c3cf3471dd97031737ebc2bb9658eedd09130c8e008306a59"}, "1.0.15": {"name": "@types/lowdb", "version": "1.0.15", "license": "MIT", "_id": "@types/lowdb@1.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/typicode", "name": "typicode", "githubUsername": "typicode"}, {"url": "https://github.com/niieani", "name": "Bazyli Brzóska", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lowdb", "dist": {"shasum": "c1d1859db89bcec2741b6c95f28c16f01472228b", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-1.0.15.tgz", "fileCount": 14, "integrity": "sha512-xaMNIveDCryK4UvnUJOc2BCOH0lPivdvWHrutsLryo9r9Id3RqZq2RDmT4eddiEPYzu7nJMw6nFIcVifcqjWqg==", "signatures": [{"sig": "MEQCIDqoUlwN9geDoa7XEuQJ2tCYluP6accwUy9L6HBwQGhgAiBJGOO5THpTCmvlA27sRkdFTBJyIrF+inj70lfSoGjWug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13110}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/lowdb"}, "description": "TypeScript definitions for lowdb", "directories": {}, "dependencies": {"@types/lodash": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/lowdb_1.0.15_1699353566086_0.44137660849211624", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3da11f41fdce7afd951641a6535f003af351d93622deb02a2cfb8e427c1d5224"}, "2.0.3": {"name": "@types/lowdb", "version": "2.0.3", "description": "Stub TypeScript definitions entry for lowdb, which provides its own types definitions", "main": "", "scripts": {}, "license": "MIT", "dependencies": {"lowdb": "*"}, "deprecated": "This is a stub types definition. lowdb provides its own type definitions, so you do not need this installed.", "_id": "@types/lowdb@2.0.3", "dist": {"integrity": "sha512-YZUXD9jLt2aLsi0FIeL490JQ3r//Osp9OZYJLTbhRk2cJjrrtFmD0DvZ+GeFOjMlVB6l8AWT2/80ufJR94dQBA==", "shasum": "e736b30db623794eeefdef67b32f6ef548edd162", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/lowdb/-/lowdb-2.0.3.tgz", "fileCount": 4, "unpackedSize": 1716, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDQbGwSTf9mteZex3UdHMs69AX6dQvKabWK2xdmkGxRHAiEAle689YK8P/mLCLOfbe2JHME2t7/du1N5Ty85Ws8Za3I="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lowdb_2.0.3_1743821413619_0.9836178674140585"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2017-03-22T00:34:02.695Z", "modified": "2025-04-05T02:50:13.997Z", "0.15.0": "2017-03-22T00:34:02.695Z", "1.0.0": "2017-12-28T00:37:49.252Z", "1.0.1": "2018-04-24T23:34:50.526Z", "1.0.2": "2018-05-18T21:08:34.208Z", "1.0.3": "2018-07-09T20:33:25.888Z", "1.0.4": "2018-07-14T01:06:31.285Z", "1.0.5": "2018-08-03T01:41:04.414Z", "1.0.6": "2018-10-09T18:17:40.731Z", "1.0.7": "2019-03-05T00:03:36.691Z", "1.0.8": "2019-05-07T23:41:50.313Z", "1.0.9": "2019-05-09T16:03:30.095Z", "1.0.10": "2021-05-18T17:32:54.315Z", "1.0.11": "2021-07-06T22:12:11.524Z", "1.0.12": "2023-08-30T19:57:18.860Z", "1.0.13": "2023-10-18T08:07:01.309Z", "1.0.14": "2023-10-18T18:27:09.927Z", "1.0.15": "2023-11-07T10:39:26.324Z", "2.0.3": "2025-04-05T02:50:13.789Z"}, "users": {}, "dist-tags": {"ts2.0": "0.15.0", "ts2.1": "0.15.0", "ts2.2": "0.15.0", "ts2.3": "0.15.0", "ts2.4": "0.15.0", "ts2.5": "0.15.0", "ts2.6": "1.0.6", "ts2.7": "1.0.6", "ts2.8": "1.0.8", "ts2.9": "1.0.8", "ts3.0": "1.0.8", "ts3.1": "1.0.9", "ts3.2": "1.0.9", "ts3.3": "1.0.9", "ts3.4": "1.0.9", "ts3.5": "1.0.10", "ts3.6": "1.0.11", "ts3.7": "1.0.11", "ts3.8": "1.0.11", "ts3.9": "1.0.11", "ts4.0": "1.0.11", "ts4.1": "1.0.11", "ts4.2": "1.0.11", "ts4.3": "1.0.12", "ts4.4": "1.0.12", "ts5.8": "1.0.15", "ts5.7": "1.0.15", "latest": "2.0.3", "ts4.5": "1.0.15", "ts4.6": "1.0.15", "ts4.7": "1.0.15", "ts4.8": "1.0.15", "ts4.9": "1.0.15", "ts5.0": "1.0.15", "ts5.1": "1.0.15", "ts5.2": "1.0.15", "ts5.3": "1.0.15", "ts5.4": "1.0.15", "ts5.5": "1.0.15", "ts5.9": "1.0.15", "ts5.6": "1.0.15"}, "_rev": "31-9d1d2908cb83525a", "_id": "@types/lowdb", "readme": "[object Object]", "_attachments": {}}