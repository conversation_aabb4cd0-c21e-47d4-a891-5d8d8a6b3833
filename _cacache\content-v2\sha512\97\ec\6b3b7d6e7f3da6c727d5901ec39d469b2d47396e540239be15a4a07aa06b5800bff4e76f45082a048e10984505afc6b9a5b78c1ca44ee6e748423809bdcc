{"name": "babel-plugin-transform-class-constructor-call", "dist-tags": {"latest": "6.24.1"}, "versions": {"6.0.12": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.0.12", "description": "## Installation", "dist": {"shasum": "f7291061fe8d2152b977a0ac1f1aa8fa46058947", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.0.12.tgz", "integrity": "sha512-L124BiwZqfPB/3VBZPDJlN3h6F0nCflRM9Kijl23UXSdDx0dcFxof71933tqN9NDWRdhZR9nPGQqXmQ06bJ8Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8FBfEqtiu2wqV1lCPhOImvPi1x+HizCU0cUDWACzEKAIgPWa5njyFmUn2ZTVFtYAXbIjdJhz30TIlul8eBotsGCo="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.12", "babel-plugin-syntax-class-constructor-call": "^6.0.12", "babel-runtime": "^6.0.12"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "b0f42bedf4568731f6141a1f33d039ce108caab4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.0.14.tgz", "integrity": "sha512-+5oSw0/f5c7qE2KdMQ1+rRFzvA1jIt/prmgVqL+Cj+l3PAzVNRMaX7ijafv4C9EpeUmDFzj3pZLCmS55RLggyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1sJhDThrY9YSQoHOlFUHAhdIlU6ukVF21nXOjN/WVUAIgGVe3lp8swRSTde/ybxI9r/kCd9jDIOsAbWR4TOeS0RY="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.14", "babel-plugin-syntax-class-constructor-call": "^6.0.14", "babel-runtime": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.0.15", "description": "## Installation", "dist": {"shasum": "eac608ba93355af465aa0a5a36fac91bf889f1d0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.0.15.tgz", "integrity": "sha512-YsDVm0Od+wTIePhvCPvkDsQDnfQmltnleo7E5BgdUmBWeylTkaGfxBZGeg1VZt10AQYjMFFt0shH35A4R4Bn9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDLl9KF3Sdp8MR8ApV5Ox/pa2sb3FUkIR5Sexwppsr+VAiEAic2izj0sQDwksNNRwdjUtCfwz/D1fJoYnC5EwCigs8o="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.15", "babel-plugin-syntax-class-constructor-call": "^6.0.15", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.0.20": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.0.20", "description": "## Installation", "dist": {"shasum": "d37333d55f2e91c4eaf96bc61339435cbb72e7fe", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.0.20.tgz", "integrity": "sha512-+FiFOe2F2qFQoEuScXVXQTEDBmkjMpUb9szL0/KXAIzKjpPyDYCS9OiU8XrQES7ou7VO9i0LGt0CqsJUe+e27Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBhTlGhLy96xjFMhyN4xiN/SpQjcxfCPiULmCBHZzhDAIhAImEuZ+83oKCBAIR21SDkX6ZJGCGLL+4AE/3Mb7oAHpA"}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.15", "babel-plugin-syntax-class-constructor-call": "^6.0.15", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "82276c62562cefca23d5a41f28635904c0e5a5a6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.4.tgz", "integrity": "sha512-TdzMkQFvJUyECBaGiRY2q2mzUmPNUaD/jcrtMI3mVquDx/k0ZJQwu3+MT0ONwR3alkKR9ap7IsyDLL4azVZrUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyt2C5cmx4lrA4zkRtSOjmPkE0v/tfTMvyR84fIOH2lwIgINA8EmAplFsYznYnMkcszHB0po5cWLxLf9BQC/OW11Q="}]}, "directories": {}, "dependencies": {"babel-template": "^6.0.15", "babel-plugin-syntax-class-constructor-call": "^6.1.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "a803c3f2c4cd6ce05cf57c172f978b3375864d3e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.5.tgz", "integrity": "sha512-dA1NLh2PuOgnNTsyVdCq8JpiMjNYG3rRLP7LOkVAAGViWtMobVn71OPY8PjEGcFvLUUwzhb3RZkYmxXtJwaKdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5nVLADX1MfeIoxBIWALKEt2+Ij0XHvQNq+6dpreiZcQIgFulen4vVEbuqcLcSxRv9LJg2Ch2OwVPclhd9hOT4ypg="}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.5", "babel-plugin-syntax-class-constructor-call": "^6.1.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.10", "description": "## Installation", "dist": {"shasum": "62db1435970753579361a9b3d0356c909abfd9b5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.10.tgz", "integrity": "sha512-VJnfv5mgBu7WctL6SzEFesJE/K+QCuIN4vYyNbuTR5R9e6Y8srcidYhhIkuzhpGjiwdho9QkuOsm92jMplIOIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD18id4FajhBzw6j4Dxd1qwtP9EiYMlcmnqB5YyT759OwIgZwo/k8/+o7iQOqRD2BAu/QVkW1vxay9Qk577aQhoxNg="}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.10", "babel-plugin-syntax-class-constructor-call": "^6.1.10", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.10"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.13", "description": "## Installation", "dist": {"shasum": "61d4da10687d893b6b5c1ad29b03dc1cd5f28b45", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.13.tgz", "integrity": "sha512-Dqr/Dz4HHyyaPW60fqpRd6FJMTQdfn5BzEu5HCt4689/W4XsUd32CaGxb3c8RDZDwd34tQT8z1qcLKuWsGaKOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/V8HOl4y8ViBSL7ELTEih6EiSD8jXxplKbaJYhVfkegIhAKq/F7DHn3iomBBRFk6kKF5UXz9TjxFUQFUQE1kgRWtG"}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.13", "babel-plugin-syntax-class-constructor-call": "^6.1.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.13"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "b03fd53f18ced17ebacc093305df9dd7cc04753d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.17.tgz", "integrity": "sha512-SickoPdiA4y05uTOEX9Ng/nv0ZWhu3IylA5OaHbTMNDHIJjxoS1qmpvPXHdBtFbiOw4J23f43aZlQnjDA2Dgpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFzrrs6CaR5SuxhDJ+udVQk+3bgzlvH0qrsPZspzLyaRAiEA21MHLL9qNiz+OaL/ZdkzKijd460kTgwnnJTBE82H6ag="}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.17", "babel-plugin-syntax-class-constructor-call": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "48a930790a3d982bbdbe2505c1dedc1621c3f998", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.1.18.tgz", "integrity": "sha512-2bpvHrsq+AzuqF6MAa1CZfqWJm1uR3SjwZSo1OPikvgmjq/E50Gnl0yRwcrQH4VT9wb2fbyELP7NZN98G3yJOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjbl2o7/vQMyoBNYCeAckUHhXmYHTxFPejiGQUoxjBrQIgdkSnwlfeCKKJFRaa0wLAPafi6QRU5ti7/kUzwP9T29k="}]}, "directories": {}, "dependencies": {"babel-template": "^6.1.18", "babel-plugin-syntax-class-constructor-call": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "67c53831addca0c98dd1cfeb5861fd30cd23991b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.2.4.tgz", "integrity": "sha512-BrijehlksC8Q20dBRD/O7JaNau3fT1UbGwo2pWChiZ0X75ZhVV730R0tF7U1dcWtV0BPFp1mJy9HyFrT75zlCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXcZwhD1uNQFdwyUYtiVQJY5Y6eQvwv6sq8dK+W0GBmAiBQMuUkL1wHB+tlQz/duCXPIJZ4Yv/qUkF6rRUCHw3nyQ=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.2.4", "babel-plugin-syntax-class-constructor-call": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "05813f21f047475576de1a0734c9ef447a99d674", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.3.13.tgz", "integrity": "sha512-NFg8FIzhuftiIiGSfFpe/dKUJsib7p1mQ9oBBqdDOHTbFBP7QhdnBk1aY6H76x33LpkVPcTRVLlyDLVVpWvZgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsEAWawKh2cYUZdeizaWNfPThyZs02iYmPMknpF+CzOAiEA9kzO+FtE6Jzpam4qsizALm9gcLCTJwbTS5tM0NsCrGc="}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-plugin-syntax-class-constructor-call": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "897934f6411878cdedea5aeccec3c04678f95676", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.4.0.tgz", "integrity": "sha512-UfkIIvYeMtxl79kzOv+rMSj6nE2u2Hy3xDeYOm90B9oAx4zhhB8QmncFloT3dz2tOlsxpsQcO1dFR8p3Lp5HEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGx6oJjZCmbQcXOsOYXuPOgBAiqNf2aAuckIHVijVW4wIhAPqLBlODEnB2+fy4i4VVDXlHdcoSK9fnctHU73txt30K"}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-plugin-syntax-class-constructor-call": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "0e00d1b1207b2f6fd2405a4e87a02a126c8f3a6d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.5.0.tgz", "integrity": "sha512-LYzGeiZbG69zhCNLrkqooPoagttV8Q/6zdn4hcBCbcCz8aqby+HACISOZBH5M/IGidsZkpoIT2BHxrbQF8wMRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQQ3aycWKmdAatPzZUKF9D5r3iw9dRz9jC3I5osCZMFAiBH3GErRexDTo+JWRyKfWklTRkGhGFci3/jRyuFLqlodg=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.3.13", "babel-plugin-syntax-class-constructor-call": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "7bea5a53eab9dfd51d2e85cf742f499e024703a5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.5.0-1.tgz", "integrity": "sha512-gX+Jh6Me6kqtMNB1h2yr3m0s+JYkN+LHpSuoARl/QkPjZytyU5+KnxPVB8ct+R8mVDgLSNwBkJhZ4CfMEzRcTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBd75B64eW6y2mAOy/1IMsB8QpU1+h0FAIVwzUMR7YyYAiAqjWK/q5Z8SGHB1J8aqLGQlyR+80V/O1ucaxElRHdbpQ=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.5.0-1", "babel-plugin-syntax-class-constructor-call": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.6.4", "description": "## Installation", "dist": {"shasum": "7ddf7e2183c6a147ddda1c72f288de81a7a37e49", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.6.4.tgz", "integrity": "sha512-iVngxpIIoPLtbx3RJTPO1eu1AEYPopFIQrHdeNj94VHkoVvJ8ab+NG8GhsJfn+faEjGs37/nanpCvykDsXZHlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFUaJmDsvWO17IJA7P+yVKOqLuOx0JQApio/K5HDqcsWAiBQVcegNFGzn/c81x7UysAlKY0ezvQu5N1ADzuKtJnhWQ=="}]}, "directories": {}, "dependencies": {"babel-template": "^6.6.4", "babel-plugin-syntax-class-constructor-call": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.6.5", "description": "## Installation", "dist": {"shasum": "50331bcdb1d33e37a39a6426db1de03e9f78a066", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.6.5.tgz", "integrity": "sha512-IlnUGXpZovxPlr4UX5ahyCPNepC93ZtUQIh53h66CPnJLBoLvXdlDtl52M+sLhnDdf+/lK964QUOIHYIj+6vhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwaW2RXxM0Xyh0a9nQ92/lPY3CeeSFXc1ylBG6Vw8DTQIgNps3aRPq9zsJEQlQDCX4DvFWriLXyO6lVLTautOr51g="}]}, "directories": {}, "dependencies": {"babel-template": "^6.6.5", "babel-plugin-syntax-class-constructor-call": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "6e740bc80f16d295fa598d92518666020a906192", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.8.0.tgz", "integrity": "sha512-txkrPzUGjTe/xTnjPxesUofppJczv+sQD99LniWcA0nkBfSEHmfdimISm0mjZm+aV9kyfaeCDqVFe+wIoP/gqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDETER1nZ+8v7ofyUE1L3eKCtECjE9qlGTfuhdbcB2/9AiEArX6sQ1oxVIk6DKgOrfdcTpCawIqJ3qndZps7eWmM4/8="}]}, "directories": {}, "dependencies": {"babel-template": "^6.8.0", "babel-plugin-syntax-class-constructor-call": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.18.0", "description": "This plugin allows <PERSON><PERSON> to transform class constructors (deprecated)", "dist": {"shasum": "80855e38a1ab47b8c6c647f8ea1bcd2c00ca3aae", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.18.0.tgz", "integrity": "sha512-dkSFZ+IvRDBBVP//H5Y32X/tpWhGAlytKTELhs1iLpAFSvFkJyfmD1+IKjFmrqs+lSXTLThFJtVN/gCFunfRyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbn8BpZumkRNjla4VN1ggP4qTvAO34nml+1gZrx/ISnQIhAKKufC6tacu+MM5FAXcAT1nu+awAWNw/0fR4nGwnxFKi"}]}, "directories": {}, "dependencies": {"babel-template": "^6.8.0", "babel-plugin-syntax-class-constructor-call": "^6.18.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.22.0", "description": "This plugin allows <PERSON><PERSON> to transform class constructors (deprecated)", "dist": {"shasum": "11a4d2216abb5b0eef298b493748f4f2f4869120", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.22.0.tgz", "integrity": "sha512-iJJ5givsSa8NWaSv+DgCaKt/NvUYv87MEhD6BQE+5YJIscAXZ3ax4Fob3Wl0cGHGgXBO7/nCk9H+kD50CxxFNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0IWPEvJt3zFTPOIV/99VGhNZ7Amg9BXjgYaEmqD8FFAiEA+NiE/sjkYD85n3iXOwGcT5LuvHA6AJpi3gfyL8/UmZE="}]}, "directories": {}, "dependencies": {"babel-template": "^6.22.0", "babel-plugin-syntax-class-constructor-call": "^6.18.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-class-constructor-call", "version": "6.24.1", "description": "This plugin allows <PERSON><PERSON> to transform class constructors (deprecated)", "dist": {"shasum": "80dc285505ac067dcb8d6c65e2f6f11ab7765ef9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-constructor-call/-/babel-plugin-transform-class-constructor-call-6.24.1.tgz", "integrity": "sha512-RvYukT1Nh7njz8P8326ztpQUGCKwmjgu6aRIx1lkvylWITYcskg29vy1Kp8WXIq7FvhXsz0Crf2kS94bjB690A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdZfLO1r0nal6/Vfi09GuL8Nng+IvaUruOqITWQoJ/uAIgEcLL1OxyTb99gSTjo+RWjOSLAiupIDSUUlrvGC4l5zU="}]}, "directories": {}, "dependencies": {"babel-template": "^6.24.1", "babel-plugin-syntax-class-constructor-call": "^6.18.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}}, "modified": "2024-04-16T05:01:48.390Z", "time": {"modified": "2024-04-16T05:01:48.390Z", "created": "2015-10-30T04:55:23.314Z", "6.0.12": "2015-10-30T04:55:23.314Z", "6.0.14": "2015-10-30T23:36:07.608Z", "6.0.15": "2015-11-01T22:09:51.893Z", "6.0.20": "2015-11-03T04:22:07.579Z", "6.1.4": "2015-11-11T10:14:40.433Z", "6.1.5": "2015-11-12T06:55:00.169Z", "6.1.10": "2015-11-12T07:54:41.416Z", "6.1.13": "2015-11-12T19:59:18.269Z", "6.1.17": "2015-11-12T21:41:50.846Z", "6.1.18": "2015-11-12T21:49:10.129Z", "6.2.4": "2015-11-25T03:14:04.589Z", "6.3.13": "2015-12-04T11:58:44.401Z", "6.4.0": "2016-01-06T20:34:33.182Z", "6.5.0": "2016-02-07T00:07:26.816Z", "6.5.0-1": "2016-02-07T02:40:20.131Z", "6.6.4": "2016-03-02T21:29:37.822Z", "6.6.5": "2016-03-04T23:16:47.885Z", "6.8.0": "2016-05-02T23:44:29.546Z", "6.18.0": "2016-10-24T21:18:59.085Z", "6.22.0": "2017-01-20T00:33:46.009Z", "6.24.1": "2017-04-07T15:19:21.024Z"}}