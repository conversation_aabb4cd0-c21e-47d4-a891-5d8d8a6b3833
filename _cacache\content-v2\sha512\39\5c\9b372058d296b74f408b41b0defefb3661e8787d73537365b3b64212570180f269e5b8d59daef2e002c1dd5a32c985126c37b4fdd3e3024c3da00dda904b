{"name": "regenerator-transform", "dist-tags": {"latest": "0.15.2", "next": "0.15.2"}, "versions": {"0.9.0": {"name": "regenerator-transform", "version": "0.9.0", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "771f055b0114d1eab08161d7536785bac03c5729", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.0.tgz", "integrity": "sha512-evdBU3iBLhguKbl4BspUwyR/vmHSxyNiFg4rMUoPADeq1WLzwu7dokkvIuJ7gaKLBXQqNskbab50ZqHBvDsmfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH9lLAgTKELtjPETKtdfneLhyH5MEQ6tvNL/eU41alHgAiEA7H77VU+uVqPOr2URQqErB3HV50sRYT0qfOarr8bJgpY="}]}, "directories": {}, "dependencies": {"babel-core": "~6.9.1", "babel-plugin-syntax-async-functions": "~6.8.0", "babel-plugin-transform-es2015-block-scoping": "~6.9.0", "babel-plugin-transform-es2015-for-of": "~6.8.0", "babel-runtime": "~6.9.2", "babel-traverse": "~6.9.0", "babel-types": "~6.9.1", "babylon": "~6.8.0", "private": "~0.1.6"}, "devDependencies": {"babel-cli": "~6.9.0", "babel-plugin-transform-runtime": "~6.9.0", "babel-preset-es2015": "~6.9.0"}, "hasInstallScript": false}, "0.9.1": {"name": "regenerator-transform", "version": "0.9.1", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "fb113bd1cfa432c8c9781add0423cf9914d9f48e", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.1.tgz", "integrity": "sha512-2JqXNBZi34Hv8gla/3tNguiafrqrD0XsF5/uvduBTEFT7PiluWDHtoxFSSdFgMlDh2UUdwm8loGsCN3qUEmxJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEso+9NL+SegxgoywKFdqMPInwkUqpDY4dr8FaqzycDgIhAP/EqYKoIdmC9V3u6FMGCMs8FYxLlOYsgNFndDo576oM"}]}, "directories": {}, "dependencies": {"babel-core": "~6.9.1", "babel-plugin-syntax-async-functions": "~6.8.0", "babel-plugin-transform-es2015-block-scoping": "~6.9.0", "babel-plugin-transform-es2015-for-of": "~6.8.0", "babel-runtime": "~6.9.2", "babel-traverse": "~6.9.0", "babel-types": "~6.9.1", "babylon": "~6.8.0", "private": "~0.1.6"}, "devDependencies": {"babel-cli": "~6.9.0", "babel-plugin-transform-runtime": "~6.9.0", "babel-preset-es2015": "~6.9.0"}, "hasInstallScript": false}, "0.9.2": {"name": "regenerator-transform", "version": "0.9.2", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "0d3c579d4f793b375d969638eee1be08bfb53fff", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.2.tgz", "integrity": "sha512-xwj+C+Ju4LaM4gbXGWANSiwo48JhuBcchycd+PopIzBoSl91mnjQaLtiAWDDHcGI0BtrY16txk7GkOleNwAStg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCT21qlYcU0GyAqVWFmumfnaEx0rTVAMj3QTuVYkp4NtgIhAOAcjiYESKaOnc+wGmolsc/JbvWiPm3OnfB1mAXRtS0v"}]}, "directories": {}, "dependencies": {"babel-core": "~6.9.1", "babel-plugin-syntax-async-functions": "~6.8.0", "babel-plugin-transform-es2015-block-scoping": "~6.9.0", "babel-plugin-transform-es2015-for-of": "~6.8.0", "babel-runtime": "~6.9.2", "babel-traverse": "~6.9.0", "babel-types": "~6.9.1", "babylon": "~6.8.0", "private": "~0.1.6"}, "devDependencies": {"babel-cli": "~6.9.0", "babel-plugin-transform-runtime": "~6.9.0", "babel-preset-es2015": "~6.9.0"}, "hasInstallScript": false}, "0.9.3": {"name": "regenerator-transform", "version": "0.9.3", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "ef70d19c34a31d89b93d486725dd4b5acb2b500d", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.3.tgz", "integrity": "sha512-6O4tOnUwqK9YpraTn9v9CC1Jc5UFL5fxRMJWF75tdWNZHn1Fz+c548YkJBMjpFt3fLiBB8CBWF3CMQglO00BQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDqXMSOt1EqOs8rW9z6F9snC7lFs8qcSI3zqHgO6DxOgIhAIv7/NEqkOJEzC60bZ5sSdVQf5/jLR6w/Jv/qRtowpJf"}]}, "directories": {}, "dependencies": {"babel-runtime": "~6.9.2", "babel-types": "~6.9.1", "private": "~0.1.6"}, "devDependencies": {"babel-cli": "~6.9.0", "babel-plugin-transform-runtime": "~6.9.0", "babel-preset-es2015": "~6.9.0"}, "hasInstallScript": false}, "0.9.4": {"name": "regenerator-transform", "version": "0.9.4", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "082eb609cfce61d37e333c69183c061323f5bfe7", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.4.tgz", "integrity": "sha512-d5QxkEM1evYpErL6Fov4DHgEwFqeuUtcIYQW+qpgXvZGX+UUWFlh7XkAPoszkj0TZ+i5q8CE8oXgXQYxrWgKDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICpiblGhqlARYcvCzxRVd9eKRDTAbzUqans1/ipHPJmpAiEA3kg4w5xANcfHYkowu0mp4YKpoO2+dIo316XTaYehY3Q="}]}, "directories": {}, "dependencies": {"babel-cli": "^6.18.0", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.18.0", "babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.9.0"}, "hasInstallScript": false}, "0.9.5": {"name": "regenerator-transform", "version": "0.9.5", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "29bdd1c5ae575ded4285674ed9d962d2fae0b87d", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.5.tgz", "integrity": "sha512-7q2jobj471OBv09VZy04Kf0QeBiY2mjdSW1df2M6oauz2iGLQBZQ/NqEoLNGc+nJR79e6tvSmReQ5nE2D4yg2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFzgw1ENxFyTUpvtAl680dFka1hmZPscW9yXaZ1K6G2KAiEAxgq1eDaJy8rkZVsEePplJEqgz4TIWaxeVvsha4S4gs8="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.9.0"}, "hasInstallScript": false}, "0.9.6": {"name": "regenerator-transform", "version": "0.9.6", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "c67394f080427691411ff882a09a7f324d303e9d", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.6.tgz", "integrity": "sha512-uVCJcGCY2asOauH/CqYR/4nywT2lVGkwt2kLniJ1x10A8oYMddfIxDi/OyjpDKxDcCq/eCZc2eLKVJp/UFUDgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFN2awtMhErGfgLUthrkbMeAyePDNI8xM9a2OKOt/HifAiEAvyq3qTCm7Qif1urvDYO6P+eaoaOruCcUdhTMa0ju9VQ="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.9.0"}, "hasInstallScript": false}, "0.9.7": {"name": "regenerator-transform", "version": "0.9.7", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "5303238aea0eba8b6243d4aa22740b4896443267", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.7.tgz", "integrity": "sha512-ANkxafL3sVcurd2HZtYdJ/dS8HUIfVK16G22ramXege0K+CA2ViCjMGpTMNjN32ZNpKRPuFUrYIHIkoqYhBDlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJVYGmNbj8JPXpixyK3ObD3Y/l9KBM8uCWcPP4fimArwIgPQ2FAiQQR8PsrH/0npnqmvkHTBbYC08AENq2NGBsOkk="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.9.0"}, "hasInstallScript": false}, "0.9.8": {"name": "regenerator-transform", "version": "0.9.8", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "0f88bb2bc03932ddb7b6b7312e68078f01026d6c", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.8.tgz", "integrity": "sha512-ScQwQyMsJMUF1hVbGWyCPKOx6uCXwfIryUzhX13vea8XmYkiJKsOUMR3S+8Dhj6cYtXhsDiU93FtZzaSITJ+Rg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEzToR2r9kGpzJHhYnh95DLsTLt6Lm4BOry0/QwCPU+QIhAPbX3MP8u8tmCjfffDHeK67bkaBLVH9lL+0LzgDryGJO"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.9.0"}, "hasInstallScript": false}, "0.9.9": {"name": "regenerator-transform", "version": "0.9.9", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "007ed7faef28ebc9cc9f5060641d938adecd2daa", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.9.tgz", "integrity": "sha512-iAtdnbhZFUjwlqHVq8sd4KXOChzSJjlVqP/tUJTr9mXBY9XGnuJ+gXgbQoXVfNFCEVqo4AXRpNE2TGmUhxPIFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDax1dgLhBSOzzZRP74FZlUNvepRocXEffwi5zlXHIILwIgIvfoTV7EeCH+799sB1mR7/YmjhFAs6Cet72+WxKt4c0="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.18.0"}, "hasInstallScript": false}, "0.9.10": {"name": "regenerator-transform", "version": "0.9.10", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "0bf70c8506df34c8d35616383c0ff1c4ea64ba1c", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.10.tgz", "integrity": "sha512-Yph2xMj+OLlZTMfEbOczE65tEgvO7ETQNWhidyZ0RH9MtwxoiMwjD5UiKfXuVGnqTPUG0r9/R35v+JHBtOK6bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmVDHkdJHeShFN1hEfimzanmIMSFNF3AHm+r8Dyn5wSgIhALWL2Bzv9Xxjl7ODt5VtFgl6X0fns6E4Zp6xm5ilcDFl"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-es2015": "^6.18.0"}, "hasInstallScript": false}, "0.9.11": {"name": "regenerator-transform", "version": "0.9.11", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "3a7d067520cb7b7176769eb5ff868691befe1283", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.11.tgz", "integrity": "sha512-mBYWw6lTiHC5EVHo5yBiBgOUU6kgi7QGb3kQVyRw3of/REGxoELtLDSEQQ96ZFo084w7pOFw1nv85Jvo36ZP9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtsWIUl9Ma2F2oyWO+2EISVcNkYm6Iiigng/QvLx/2aQIhAOxJ6WLWSuPi9vMcKNywi0wzFA+QvgGg6aGctV5oaWqj"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.9.12": {"name": "regenerator-transform", "version": "0.9.12", "description": "Explode async and generator functions into a state machine.", "dist": {"shasum": "652ba0b33bd4e8e59b3e2c57e7ec6c157a3a3f3e", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.9.12.tgz", "integrity": "sha512-EQb+l2qs2CEt6KHnpsQ4hBsyeusDzDSta3A+8SVaBWAusbA/h/7EoAH1mRf8zRODt1bJLD7CdR3CFuZ0ZRgU0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXYtTXGYrq6LfnJtJaibYiPRtUiWOI+pqqdADSbqlEugIgE/3QKGI5CbTyyEw4hoVgJDGNBJ/rhDiHTMO0R/i9wKE="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.10.0": {"name": "regenerator-transform", "version": "0.10.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-0oMTqaJuM3Q6RWqts6U0/ijW3xcnY8d/KimL3IkQW1zib1gmSb1lKoFKNF+kSDmriGESlOHcwoI1XpXKNEGcLg==", "shasum": "f9ab3eac9cc2de38431d996a6a8abf1c50f2e459", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.10.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSxybqXG0AqUFJ6D+5RxGkTGHK17ld+Z94Co7ZeX7W5wIhALjLitZuHL2cueTiAnEAXffSbRxcwmQJxx1Z3NVGg/5j"}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.10.1": {"name": "regenerator-transform", "version": "0.10.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-PJepbvDbuK1xgIgnau7Y90cwaAmO/LCLMI2mPvaXq2heGMR3aWW5/BQvYrhJ8jgmQjXewXvBjzfqKcVOmhjZ6Q==", "shasum": "1e4996837231da8b7f3cf4114d71b5691a0680dd", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.10.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsitoiWppN/dh/C2cI23g3xkVk15zQiHZ17C7G7bWUBQIgZDFq4ZXXV4i5l37jlV5BmTdC1PKnDInQyuj/w8VwyCQ="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.11.0": {"name": "regenerator-transform", "version": "0.11.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-Sjf2q/nIQX+6rg+acRT+ERFYk2tBDaRYPl1A+tMQuMaQU/e8HqwOpX5rYe5FDtUwuwTv4rKpQG9hf+EzwnWEfg==", "shasum": "0e204c1e69defc6ac70677232b29ea5d4d1df989", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.11.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDpEXDab5mDjyjaG5CZTtROLNCaCTBD3CabaMd0kaGFFAiBYK3R0CvKjF5lJ3WrNqHc7FYEx9iiSdVtzT/vEezLnrg=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.19.0", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.11.1": {"name": "regenerator-transform", "version": "0.11.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-q8SAPMEyARQHzyXNWNrlz5oNnI6k4yo8ncEcvH1aXjUCqyVQ7TeV/AttqBLcySCReXVk/+fUydo/RaEIAOnRfA==", "shasum": "d3548a723f30bb9d69f2d17c4d0609516ac3f0e2", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.11.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZa/ZTdz7JjcDGTLJchSNfCvOxzCrwKIIUDg6hL0uejgIhAOsPBz2k/x91THXjOxBnISoikDuu7+vJuB3hwgXFsDuP"}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-beta.3", "private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.12.0": {"name": "regenerator-transform", "version": "0.12.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-j81jUjE0Gn3gttdQ6ipSWuQ+0CP8WySfNNpKMtOfcFnvIl0Jr5g3oGazqv6Tmppo6NPYEZ1jkvQQZHO3jFrVIg==", "shasum": "272c41a237ffea5a205fece23cda24a9c46e0cb3", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.12.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3TEsNWBCg8FTc5/GvyLl8xYm3Xwoi2uBSoWs9bMUVKAiEA4DxU6fNUczLQrqV42Q7wdFVXryuZCudLTnMhcq5HRUA="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.12.1": {"name": "regenerator-transform", "version": "0.12.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-RAcAGzEuU74v7FgnOLT7qG1Nk5mTJSXzZjYAfZ3gZWQxNI6aWn0ny2uX6ZRdYT/oZraT3o+YgFt3rDBEfUPZjw==", "shasum": "6d3e98f031d68d8a35483a9d7b2bad87612ac926", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.12.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGgQQFS5TWK1BEYXghTHZp+4V48tXvlPI9wOu/8tlmtfAiBnUrkMyZtcbe/pbsvBo7yqdj5OuAOjTVk8v2FGRQCJtA=="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.12.2": {"name": "regenerator-transform", "version": "0.12.2", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-mo4Xl+yQEvkGnQ6BU+mO4Dit6N8ndtwIJ6fq8naCbNiizlh3DUce/vm1W2GuTuMsckc6+w+TexG3nOdf9niNaQ==", "shasum": "55c038e9203086b445c67fcd77422eb53a4c406b", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.12.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFHi8X9YJ5hu7MISiwi/nWggdta3ZjnPO+Xp68PIZN/7AiA6KaPt0skxCWN8JzUu2z1dslkvmJEmW/zMI1pk7KdDiw=="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.12.3": {"name": "regenerator-transform", "version": "0.12.3", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-y2uxO/6u+tVmtEDIKo+tLCtI0GcbQr0OreosKgCd7HP4VypGjtTrw79DezuwT+W5QX0YWuvpeBOgumrepwM1kA==", "shasum": "459adfb64f6a27164ab991b7873f45ab969eca8b", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.12.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxSsW686JC25nsS2i6N5eno3G82fF6z+D8PldYPO+huAiEA/Z6cYK36PR6c5QFVopZLInh+oagW5OOpOHzOxcGQ2fs="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "hasInstallScript": false}, "0.12.4": {"name": "regenerator-transform", "version": "0.12.4", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-p2I0fY+TbSLD2/VFTFb/ypEHxs3e3AjU0DzttdPqk2bSmDhfSh5E54b86Yc6XhUa5KykK1tgbvZ4Nr82oCJWkQ==", "shasum": "aa9b6c59f4b97be080e972506c560b3bccbfcff0", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.12.4.tgz", "fileCount": 19, "unpackedSize": 129499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDERmCRA9TVsSAnZWagAAuYkP/1ZTUdINMTUZdnpby4C2\nSsUNeY1p9WxcjQ8+6+ENGv4HyUiQfffQZ1e2D4/Wg88YHVUifGinwcCEk8k2\nCvPPMnQ/8jxFHWLhk+nMYtZpOhnc+UHcQdpp1Jaf1MShlgh0oTyoEJ4P+t/H\nkAsZDjMxac16D3ZrnV45HDfyjNscqN6iAJ+jcZ6wPtEnsZAgtZJdG0HmnfdY\n4s8HHylpxImOLXXUqlX5vYg8lpyohH3XR/EziGKJ9m0nnjIzSjEVlR3qGlpR\ny+BdRVe0kSQoxOINqWrz0DSkr76rPT51w3MNd/kbuuKb6pzIYizlvbmMkjSC\n3cQ0bwFL+mDQsjDrZWSaxaebdL0ebyi2VfsdYvanrn+Rr7MpXSDcfdKiEILP\nsmwy0QT55PF9Gq+3+mxK9tvI4HIqXQLp9QZa0pApCDmHsAqhXk9zhlNXLQjJ\n/EX2U1pEaUeHOJwT5Tz+YWVG/c69mn2Bo7OySvQ8bGM80+ebGTnWRb4FtVEm\nj/UnMJvPPhI8XeNDp+ukq5FIr1eSEwgni8f0GA9nC0aHDBUmUSa3POvMvE72\nD2IoecfIq0jg2Ewvua8GbdpcGB7JxR7/p7SvL8NlIk3qeq2mQSotoWIc3vOj\nn4zKZdfMiH6u5KxZObFqtLR321FAz1ZYkdBRXMblcZmotfgxkqr89MM9sGia\nLQQ6\r\n=nWzl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5tdLkTfFTTIgAQj76GK6Nf1wdVOOFJTSD2sRFA5EAhwIhAOV9urENEp54hj6dZXk7cOmuNBlEhmFJ1n1t6jbeecSc"}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-env": "^1.2.2"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.0": {"name": "regenerator-transform", "version": "0.13.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-M4x+qeWBte1I/y6N7Tjc7f+2gvgmv30q2n0+004XA4plbGR8/dTNkMa8jPFxl/1ca5+4uwZhSiiVivF7jQ33ww==", "shasum": "6fb9b1147fc6cf0aa7edf9526e6e4b43ad90db17", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.13.0.tgz", "fileCount": 18, "unpackedSize": 119838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIFFCCRA9TVsSAnZWagAAJmIP/1oVPGHcEvQLdpdftWkX\nECfInwWzoLPY03PcNbfyvzFOQpMVyuEvCNEIucKrv+kvr/M3Skgd/1debQSi\nxTtfmgg1lauVWTA2JBp1bfYd4Od5vuIQRqVdyX9AppE5HXVF8vaCiCRIR7Xv\nXMGHFw75WC9hE5M/XlBcSDCFoFSjEdPg6TSQlXQetwBitKpqQSxKP0qvpODx\ns/6qXpoLeU9B/nud1zm3gztnPo9T5KVguZmlweXhS8YB2TGidov68AL9UKRD\n8VySPbBNL5hmnm94+wfzDV4SuMslF8ktCKEXOSC9fCWkj847Kr2db7t2/Ise\n5LnhGmJVLsw+rqbeCNuD51nLVL+XfdovpyP9tbhQ64yjuosZUrQCVE9ez7vT\nvp1zoVYzn3fICTnc8tdM0YcKPhSzCM78YxJCnhuD0ecvPAa2M57lwDvJQNO9\nbu9UgjuFRtom3XXWiwmKW59RwugR+S7lewV2fehuyViWGlBBNH7Jn/BfYI7g\nJu2cSwjwapA6a51Rw/wSmCQ0DrLpakriFD+wpn2/cXm/rXpYwXwnWSkN8ZrV\nGA3icLOQtP2wsTSqMI5Kh6LXUVqTjmLfMGDC6fUMi4fQxABOqG8t9GNv1U3E\nU7CgKAAZGAzPDSlqWnIEC4JRaQguq9Hv4oWhrz+Sa8SMGJ/FBXc5OjnRbsOE\nLPjO\r\n=hiV/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFbTRTA6VOJkr4Q+7z7EG5gNINETLwrYiC25NMlTvMj4AiAlu4dqdPOEPk5vgWdTBLPZk7FzxbuQ9zsvagfwjaxhOQ=="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.51", "@babel/preset-env": "^7.0.0-beta.51"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.1": {"name": "regenerator-transform", "version": "0.13.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-ak2ok7PzUSwtjw5ed3uXFwRzJa79yDwsNljU5Pz0+S9XJ1cyqlAWUyN06FNHAF4zbnzTww0yQwBJAxykQHKPVw==", "shasum": "847e1a1f4a7caa56ee429649a314263a5ce511e6", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.13.1.tgz", "fileCount": 18, "unpackedSize": 122162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLo+KCRA9TVsSAnZWagAAG/MP/1L6RnQ5CBAP7DB+lph5\n4Bmx1FNrfz1JvlMsZo5nNgnAZwiE+yXPJcB2uaokl+PH02X+cAXLhSfj2pgy\nFJIETAt0gPf8ALqzkfwi8RntdpYnpij31V/RyqxGSkCQg1s4CX3tb0nIsYmX\nsZ2emQFZBM5hPs+k7PgCPp0cZ1ukX0hdM4fCkFfXJ3b40m2amiQX6+nf8ODt\nAeD5pQIBSPoLbjemgUo+0PZhmWCpzy9cziwM3Ha7AFFt3R3jpOmXCvL8Pl92\n+RySutnEQknIeD2Nexdi6lUSbNY+zoKlu7pFJOxXeTzvkjYdsJe6ZEU30HJQ\nQwctI6ViB0bQDBLaXVmhkmxZAMRI5jxUDQ0fWR9Gl0lOcfKNsKOkV37u4bUc\nMg0fsZvPsoO/D9EPh5Lotv/2nx0BEI1lRbNvywoewQ9JGc/O10gsD1WWcFnp\noGRB98AjmwJoIHXBBLeoTDZvHES1DV8u+2KEWkET3XmF0orQcZtAuaSZTElU\niFfKbHwNqJrvDk50CQw2qINnDoFFGCgEBzBKKkIsstfmEDVLHoSUADlt9pqb\nADirZoxycNL8W3RdqETJem7qrxobq9j3aqNmSYgYyQZG3HKq5pVIkAoYNnqy\nsZxAm7IDIU9pcbple7VUnzlEtBqX7wpjGFVTr+IwLhSJ/LSGmUUqqrZIq0or\ndM/t\r\n=KXr+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBk4nufRK90ClEbTBVYo7OcjGKxLssEJbAmd4XOzg5B7AiEAkzW8frcKoJj/j+e2HtSMwb/SzE36lusXq7DjQ83qMa8="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.51", "@babel/preset-env": "^7.0.0-beta.51"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.2": {"name": "regenerator-transform", "version": "0.13.2", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-LGqJgr0xfD3pR71dcM8DTNhl6slyt3C3D9YllITTw4aLuf1ovJuw4aOUr6NjwhntUOaJyV1OsPyGxoxh7rt0gA==", "shasum": "e197efac11da22165c43f707d9f1c6f8898cb596", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.13.2.tgz", "fileCount": 18, "unpackedSize": 123055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLpRRCRA9TVsSAnZWagAAeZQQAIq3BJVKwyGkGk3LWmAF\n2eEGpnLdROTl9gcsPIYLB4voSJBL6aYu/w7GZ4V9VC+IYozkKCBvkCQpssKG\ncg1wZn14J9n7KtUemVYxrcCnF0ndMlNfzn81LnzVdNuACGtXnoXl4XWF6Qtm\nJAeMI+28NiEuUZtoY9pLJ4qONi5R53lfN0MtNp7KU2TyGXl1SEfHsp3Vy2/g\nkgwd2XWBoXp1j285QnbjIAjBTRf3JkQ8rkWxROF8gE3vPTOG+qhSP0142soO\n//Y/iNiQ5r2BYS8+OJ5<PERSON>+BwUmdx7EA42ZUNjaFJszKa04D+0v4YJgYepIGG\n8xk+EttX5GzD/8re94dPNusxiOQR/Mq3DTXLX36qwJb5OIn16UORE1TNwacN\n0CyRKFl5ou4IybsTD51mgE2FNIANKnzub5jQFXFD8nI2Xabd0o3yxOj1fKFK\nFN9PWqjLe5qsED7dUzIEizkBq6Uir/HaC9FOyF8lCRB16Ygnj5SwOK5cidxZ\n3wD1RuLm6358pP5wIPryzATURnwbvpzOUcQa4Opii90jF7lU6CgCfb/OI7sJ\nJWkc7PRUdzsbQHQJ+BtTjawrqvxMrZ9jUEO1CeJYv8f2TZUnlndMKm6S+xZM\nvs55YEEbKkDz9KY4kHYc7v4et8B9aQu3dh6thy7LgBYpBx8n4rXdburKGFGQ\nGzVg\r\n=hIAW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuKpaev7w3Rhd9G3rSUCNs935Wy0sV+V0cYNsPMUsuvQIhAJjCsOaauUGNDk4JcLGZjFr568HQIAZ6glpioJUDOIlg"}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.51", "@babel/preset-env": "^7.0.0-beta.51"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.3": {"name": "regenerator-transform", "version": "0.13.3", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-5ipTrZFSq5vU2YoGoww4uaRVAK4wyYC4TSICibbfEPOruUu8FFP7ErV0BjmbIOEpn3O/k9na9UEdYR/3m7N6uA==", "shasum": "264bd9ff38a8ce24b06e0636496b2c856b57bcbb", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.13.3.tgz", "fileCount": 18, "unpackedSize": 123857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLqi2CRA9TVsSAnZWagAAZ/sP/1KYmuN/qT2N7CuletR7\n/i/M9HokBhZ/OHzBLXvnutqxaqgAqdRv9RCwMk8F++aaZNdQ1MSFQ2Uk2Gw4\n7adgRZ2VwTStMdfAIy5MPWXD7iwSzzByD2ND2uUTKDR5t17lBFjZ1y0yIXZa\nbfAJpnGOFAbTddihEPq8mMLdHV4ln78IT1+LpD48rqUKpkIUJBdfcr5h1KWn\nmey1krZl1/A5bSXE4O7TOHXh/6E0vJYq3AHKxySWgnQU7gVjM5aICoRnNsqI\ndTFXmWpm4UlTcCVG+HNAD8cCBXEwHWxvMkYwV49mh6UETIoph5NyRlUb62vY\nJzsAN/xwdfzxCvO4I5PnwEUiQLLkaKQId778SKDMITIznX3yFfAxZxig2PVK\nT2jfgfXlgBEH7hZIUCQjojKWeXMl6B2dfvJlQOwFmJegGG2UJLN+4dC+atpB\nhA7hmAl4U2rgXh6xvDiB/GibAPoP8/NuVyCr16Ecfx6g40ITi10lawqrNY6l\nrIQRkr2MWf66lcVpBZhRD7CEQGyQYvpY3j6io6h+otWC1gm9xJN7caPogoWg\ngijmbbuBxrFZDYtkZpFgtVdBfy2JUyjpdTD3/8k0rkxks71T0mLkYQjknjyR\ngGk4MO0vqjr0fQj2ekiTdElT15sg6TKrtXyPWSCh+jMiS18m/Ge2WJDwNBez\naVvI\r\n=GoZi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBat5yl4Ju9ruF3bIX/YsUKCjF42D85SiuboLpL4ngAIAiBXGHi/qkQCHEmznnTUreCbiRRsJUCcsrmPiYAm4LyIOg=="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.51", "@babel/preset-env": "^7.0.0-beta.51"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.4": {"name": "regenerator-transform", "version": "0.13.4", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-T0QMBjK3J0MtxjPmdIMXm72Wvj2Abb0Bd4HADdfijwMdoIsyQZ6fWC7kDFhk2YinBBEMZDL7Y7wh0J1sGx3S4A==", "shasum": "18f6763cf1382c69c36df76c6ce122cc694284fb", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.13.4.tgz", "fileCount": 18, "unpackedSize": 123992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbsAfCRA9TVsSAnZWagAAe8kP/0bpKD3T5m0TcsyHW8Ho\nXITTZ9I7Ea7olBJ4AzmSbFzIPp7Wuuk/QIRpQOkd51Xc12MlLbv5aW5oq4AA\nD4u/kgcwc42OwpZbPLf6sCtd4Qg9DRnOgWIKh5jwVqVdOUJFOuuIgPVUGkdJ\nKGwTkhZaF6XoCpoaNZ2GjSGdOGuW65iLJtoq9GRdSEefAiv0ZuYCmh9kHV7H\nT9eJ8ZvewGYlgXWwKfutcY0Vh4gM2uH5Pz0W5U8u8sugoIgwZme/PjrWGtFU\nfUrNh/7DbJ+udoLiQV0qiesGxPCXKTSxlXxx4XFW0MJDQasaZU7i2N2QvpqX\n0rX//xhsKGpS0XZgVrBpTXKgHBnLMag23syYGM93WYnfqd1O/2F2Sur9kQlM\nRGYu6jsNNbgL3aXhi2g8xKC+hqjvp8q4fLpvPRnKp0sRQd8896GkdQ02K7Cv\nJ+CWV3jdPYINhsrwMJpH/xRU8+Z/hW70gFQZndJy1+7IyR05oxQJ4MzfTs4C\nK7klB6ZDU0b0llkAdw0FSPiAe8QBiatsQifC0pjLF8OCyuXMg2wJ4BZviLU5\nmFkCFMeayfwNR/NDBt6Y80oCspniozSprQsY4GbOyTBY0JcR7MyWod7I8J5b\nFNq2tdeRykqTZo63j4YFeY1DhFrF5NAp5qtnI0OmiGISbmqXtIhBhlE5ZteW\nt7oj\r\n=AyFK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJt0++eGJhB0s6PbqoBnuSSwmvffIZ7hWvoDwad87dSAiEA9M586ZpUUYDyxnZhdKU2DRDQ4KoTNKwW0tPzv/36nhE="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.0": {"name": "regenerator-transform", "version": "0.14.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-rtOelq4Cawlbmq9xuMR5gdFmv7ku/sFoB7sRiywx7aq53bc52b4j6zvH7Te1Vt/X2YveDKnCGUbioieU7FEL3w==", "shasum": "2ca9aaf7a2c239dd32e4761218425b8c7a86ecaf", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.0.tgz", "fileCount": 18, "unpackedSize": 126273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyNvlCRA9TVsSAnZWagAAN9oP/ivf/uIH+uxFJV2QQJhV\nMW8sRpaH0E3qC4FZT2SFXmm+VljRzSivXXXIEXpWdSIAKMz0+LcdUti1jekp\nX8HHfWUlwID5i6BqDLj//u4EWzwLK2T4eoxS93cSFBwMe6qbJT/ORdzzUZEH\nn0A9mL7LiQ3pJ+czzw9+aQkQcGYhPIGog/uAtKaWlYv4H6C8h7fWWq+504vo\n5ne2Wi61vOo4j3MTgRyJbaWeUf7bZH0lfctZRYGWYCMg4e4LCcLE0xumV6bg\nHHhVo6TrS4AmjlRjNfTiKousfd3Pz3YOwmMMM0xL+oSFyC/HxNCU/Zx38YWd\nq39yivDL1ohD6Q6nrQNsfUqNbCoIMn/SOXDOKrwIwpKvAs/m0KjTFT6/M6t2\nUC9Dl2U9EpaJmBq9TSUkXyw4FciN1eqV/VMRHyWA3TtKEVDHHuifhW9BS5Z9\nbjIa0KZoZzmuf+mYY/pRw2gCPLpcSsBBndDv1eijuyIGlYM5Oi68KuKyTDqz\nuBzzwXYGETIPWtGYH013BhAEOm3mHSiMah87jOjQJ83t94p/wGhUd88b2AcO\nES4HddrjXFBL3UD9RA/7Kc3Js/B21UhGRM1QU8biSgjS633vY2MA+0iytvpq\ncJ/Zz7gGCqwCuWDT0LDlc9M0UXD95cuyTrXRgoa4qZPI6as/UKA1tPIxZk/V\n7Voa\r\n=L2D6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+vHYO0sRQQjkUcVPIE74Xxms93IZTlCDYtdU9QgAcswIgc7N7a4yiR4ob/1OR/EZPZ1CL9mEF9qdRPshqvXwaaPA="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.1": {"name": "regenerator-transform", "version": "0.14.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-flVuee02C3FKRISbxhXl9mGzdbWUVHubl1SMaknjxkFB1/iqpJhArQUvRxOOPEc/9tAiX0BaQ28FJH10E4isSQ==", "shasum": "3b2fce4e1ab7732c08f665dfdb314749c7ddd2fb", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.1.tgz", "fileCount": 19, "unpackedSize": 127783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMeEJCRA9TVsSAnZWagAAzIIP/02HyEO1l4OhmAc0mWIO\n8rJOu3wOgpFFnxJ3BRwlwpxE++aPytHIAbWIWa9pdXylLFVKVengkWhjO15g\nlytJVm2yGxMK4mBBsqE2TizZld4WEbPSWDJ9mjOfMMecKjtuI1tER204m1tf\nQtaJnb76OzMtQ2J5hMLnRAXEiVtnvibG4eaSQ3SJcV2rp2rikg3w/1nMpP1Y\nQS9yOnys59/wHm2GNJ22I2GoUx2/xx8ofezbIr6ZfPAQKWQBK+aEt0tBCNM9\noOG+I7Skn0l5LTZ4hHKHnwIcYwYTdSHEVfGSQjUZzT/tfcLDnhmxwNvOjZzl\neHUk7ZoTfODQCf4tIUG8A5pFjHpSso9/DRE3e0qVtOb+JqIelb6y8d0dqk+Z\nrLsAiMK7XZJm3ssuJ1Ox2B8QAIMDAyHREWx7yTFgd7ag0H+Hp6eR1yjlKIme\nWFWDgkUoTbcCCMmoWH6wYSBtuej4VYLrgSdWMFgOCRXnCp8JqXpCUBmkRJL0\ndTMWqbK75yzTjkLKypMvVYtgdzxvqTDamqSybWJMEhNbgHARAWRDyXTHKtca\nO0vluP1ajLk9Z4jdtcZOJLFk8XbPXOFCK5Bn0rYkLWFSdpHprRHwmJepI86q\nz1gMKGVpxFNgtkhqVMPYgIR07DugOOshXJPRIqTl5cfk+l8wie3bR3XlygDX\n6sDM\r\n=LqND\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqe9AcoGLOD9W+CA5tlzSkSxrdcTRyoAgVYOKqYw4pdAiAoxqxHmVVNDyDg0FTX/v9YWzcSXf4WxHoiWJPtFwfvGw=="}]}, "directories": {}, "dependencies": {"private": "^0.1.6"}, "devDependencies": {"@babel/cli": "7.5.5", "@babel/core": "7.5.5", "@babel/preset-env": "7.5.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.2": {"name": "regenerator-transform", "version": "0.14.2", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-V4+lGplCM/ikqi5/mkkpJ06e9Bujq1NFmNLvsCs56zg3ZbzrnUzAtizZ24TXxtRX/W2jcdScwQCnbL0CICTFkQ==", "shasum": "949d9d87468ff88d5a7e4734ebb994a892de1ff2", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.2.tgz", "fileCount": 19, "unpackedSize": 127284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUrRJCRA9TVsSAnZWagAAY70QAJZJQwC5U/7GQC/qYGc5\nvvmoXFK7UrSCGbeXGWO3wJSkSbcA4ZVVL9csNj74PHEus8ShTc3rklWKxO4G\n2k7oVWMPHFx6REDg3qKcbVj2GX7g1NbNvhBrJK17ENrp7fbgU4gnblYfNBno\nLBScimqevZJpo7IqsabIFu/nrpTkwZKzg6qX/J8Ek2zuXY59S/Cw3n2B7W5g\nAdHt9i61prVJARNaqvHK21fRjFgOYPYN1094Qxssqw6GgtvP78QSUunqA6Ls\nSf3EqtFyvTR9LBhCv1LNv5Nm+it9yN/pD8lDRGldHWOqShlafs14UMkvsOrI\nBOFzIyEFI22YwLblUWN6L6X7V1ZcTXYiBby/f6jPrtAM/uMpey/iDkAS/ilP\n3Av5mmR7Zp9NAFqh7khixBuKzl89uThfuxCoTvkx6Njzaih+WLMkdwaUzM3a\n5pFeFofn2Nngc5er2UZ/upBkkjk7NUqnSUPBPeissWFfME8B0fsVoytf+rz/\noSQnlLGW4yqoM/NY35o1fCTBuvvHuF6t5oM3gl+PVMeB8fvQbYKl8EiJiGP2\nQPhCE7aayk6uLKnQ8vrT9pUGEy531WyqB+//fxea0m1AfjDeVQcddHvXqK/S\nD3xYqAmsaPdELx44bXvUno2fJV2nsNtg3BKxkaFgRihf2CwCjaKth01PBeSj\nL6OA\r\n=dqxu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHA40ic9vxmzNtKtZOhlvH9iyBxUBQ2rAzYM6MPb1B4YAiEAq/xsEBIoQxSR55cK92mWPmUF8Lz3L8EaW+WjR83dIh4="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4", "private": "^0.1.8"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.3": {"name": "regenerator-transform", "version": "0.14.3", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-zXHNKJspmONxBViAb3ZUmFoFPnTBs3zFhCEZJiwp/gkNzxVbTqNJVjYKx6Qk1tQ1P4XLf4TbH9+KBB7wGoAaUw==", "shasum": "54aebff2ef58c0ae61e695ad1b9a9d65995fff78", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.3.tgz", "fileCount": 19, "unpackedSize": 127873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearjSCRA9TVsSAnZWagAAd2kP/0+4vZoVQHZgfh3ixuE+\niZX87zFaTnxu4ZyCGjfhZgkyZEjvGe8bZHx1fejMPTpoSuaO9BaF44wy3Z+L\nIN/XmQvMy8yG0+JSSkjUrgocXpa6zMDZ6Dus5GwMdQUUnQFb7UA7giXQhFt8\n/MCugWflzV03SrOGqYcqr7hyp9hg32n4PQ6obGKmcO6scV1ZvnPqX9mWDbeY\nGmmMCCD0OPFy1ZYXdxWoJ7Gxo+973x3Nn+ju759aBHlU7r1i9r77jNgPqRPV\nwEaNDexbTGGOy9W/eOCc255fs/veL2GtWvpR1z+Rb7J7DW1TRuYgO5VT4GY7\ncAqLznhRsa4uF5V8FoZRHmPvEOa/azQKU2EUQr9BJhsSKI08PLH9I5FSG8eM\nMWQcuHzohWcmFMPrFOceoIQrQ+i6/0W1Y7Dz4CV5+pTWXRYQn2Sa2HY2lX2+\nQrV/Z66VdQjDFJ0yGIQ9lKKoeqj+kdD89TRbzyMn93GoGTnehaQMZg73ZKua\nPO2UqZoGA/QlzUtL+E/WCS7QQabSr5UUBKH5dIsjZV65ofgznww/zwaWYe06\nkmHW3OR2MKw1spkOqyxps1s6DY+n4eZR9BLgHvQfClMV2xnQWeE8yjkKFuSK\nmlhP+SpMNvx265PMVtVB0h/EVHrbowoufi2IzGJ8QJRO9uDYp4QQ/sk/cJ6z\nQDe0\r\n=usc3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJTPHALf5hakHRhnkKxwr7RhgwSz4E37XxMp5M0CB6HgIgKP9laMAqYcUgVfO9OkaAANI2Pgo+bC9tRUq3DT2Gh9Q="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4", "private": "^0.1.8"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.4": {"name": "regenerator-transform", "version": "0.14.4", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-<PERSON>aJaKPBI9GvKpvUz2mz4fhx7WPgvwRLY9v3hlNHWmAuJHI13T4nwKnNvm5RWJzEdnI5g5UwtOww+S8IdoUC2bw==", "shasum": "5266857896518d1616a78a0479337a30ea974cc7", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.4.tgz", "fileCount": 19, "unpackedSize": 127493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeck+XCRA9TVsSAnZWagAAMSUP/1Ey9b0OuQW1+ZnQVU3H\nQHOVyuDgTVMuEKNXsoFFtoEawST1P+oUVvjh4NZGtCOlEYjvQQ9OaNBrdDAp\n4dEZ6FVXgJcJxorlt8B5Amaoz1cmlz7dohKYXPman9/obclhmJccFeXmbf5v\nhJrcwFi03vw65IkmMIligKJmjHsATC20hevNx6sQtKsy0U8NLwjsMPHlDO6B\ngMkMy8hHy/pCp/hvIQ95Jj2oqCi+AWD4Rk/KjJ3mcmjcMbpeEp+V8sMvnZOw\neduWVyTuGdTb6sy3GgQYKe6CVaYpXLI3fI8nqZE6Asp5gQCbgBjA9M+xENis\nK5EUGCIRoh6gI2celRjBllbafcWVVHbmFjnZVILhz7FAToumxczGR6U78+Ph\nX6jHJmbdnna2FL5m7QGjYmwe28T87r7EZYS5OvlA24/Ec4BS/LJSKf8ZAsCS\n7kv4SGFKg9e2kw9xlvq5qq6SYVAVHR/X+w9DfVAjBLZdIuKcnhcu+1CWQrL+\nD1LEhNX4wjjmn+XpqcGsJMmo2rFUE9G1XZ5KPyBf8ZS2WdXOhK+JmkVCYvET\nr4DsXMxvbB8+ctoEdqJhHMl8dCNeAPXt35engICgKBVG/6CJ977BTfqb4xsu\nRvgPNnlCwAP/wea355wgOb8GaSJsw2eyLi/gIFZSYGD7w3jflqf+B3YfnpK+\nYWx+\r\n=Q75K\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAw73OrIwaBRKSPPyW/RXHFwGW2SdbVfpkEPi7QFaq0fAiB0ow71jensMsViIfOOJA/4snvOzCnchJqgUN4/VXxARg=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4", "private": "^0.1.8"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.5": {"name": "regenerator-transform", "version": "0.14.5", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw==", "shasum": "c98da154683671c9c4dcb16ece736517e1b7feb4", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.14.5.tgz", "fileCount": 19, "unpackedSize": 127735, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+8eVCRA9TVsSAnZWagAAV78P/3sSGTcR5fCUx8pXlrCG\nu5lXih+N0d1IZq0sQqA4HjkfYA15bPx/SkhtGYb7L+OqkaISpDnv0P0lnui+\n+XghEVNudaQ4fwPfA590bzbghD/qqOKFJC+3U7CyBhjbQY4QThk8p1tT0/W+\nrfPGXzNvSF3GOoYda4q01m9TtY/BW+nJwsmC/wfmlSiqcPItQEuSWTWglVEA\nRRwu1cpJjykk7R1NQhX/lUg0G5OxK7dK/p9MczafEG2xXH0WS8XgsXEFFdeh\nn6cMwKhJl720+/RLa8fUSoUhoiwtq8NwEdrfAhhr8Ya/C58vX7VDp1kMe7dK\nFeyjgVBsAXT1fhmzsoLh9+I901VO+sjiaw7vMpRSF8vvAmNIsr9DWHiK2BM6\n8v6820+8IWtAR1QzL1/HrqxcC7s9anBc9U3+H3GN27gMzzGS4ONrI85T177e\nUs0BKyYUu9iI6SAqi0utSCuW7CFtC2xB7iD8tRqs/iZM83XeKDWAF2jpaEvN\nFFlgXFKJZX4n+CBJg6ZVK8kBj01zFsiB9JXsPnTKTo/qshKSmY4Zhv76jRUx\nucVaMBpVAlIZbOqzgdQ8m4Zcok81+Z9o9unW18jTWnf2u+eUos6J77FWVPBs\neuLSNfcDsVK76+820IKPzkyE1mUq/8EZlUn4poAwgIOfh3KdOLGaoHDPQobZ\n1RQl\r\n=Utyn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAV3uKdxmqCMJyRToBD3fZeN9WMARw4AezOSSQqOP9gdAiEAuvaP49JVFb+bEY4yZC8FRaPw8hw24fkZ3STv1PfqMzw="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.15.0": {"name": "regenerator-transform", "version": "0.15.0", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg==", "shasum": "cbd9ead5d77fae1a48d957cf889ad0586adb6537", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.15.0.tgz", "fileCount": 19, "unpackedSize": 134534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+u1NCRA9TVsSAnZWagAAomEP/1nP3OvO8seMJjiSwyR+\nMoGJeFm3Hdw01QNUqw6Ijh3inphHIY5mTdOPSHLay0iPuAqQnL28N6Q9VygN\nWrvcrAM3po0kk4oKjOVVu0t5mnqrGLn/oADytLVykzkjBzXPjWnamatbzxn6\nWsqB+pzB2eg/4x2lUetvrocDcjQZKhJOgOa0zwEgmItR4YWiu6mNG3uZvW7J\nFd1nJByf9j8HgI/MUfH3aaGwfSMMIgFS5GhuPIjjAMvCkuQ2L75U3avRvenq\n0W3k8h0M+8OU4mDqGcatw738hTjcgWNGwEy/RM97NOU5I9jYynTfm0+PKzkl\nLfDyKR3oYfuwTprN46RUfsHmiQcRkSrpwR5JO+JcFKuGmcNgfpF5CTTKJENh\nSfgueI8BCmljxVjVAljq5OgOeV/TPKqlIJaWRMsYYKlfr8H1BpL8Xp03nyZV\nP5KtcDMTfPr212VBvVoUvUPqKkd99sjAiisVasQwqGf7D3U7nDKlBzXwIVu7\nbBwYgyfRjeE0Fwob2fnoJT2T2ZxlES6qHJbm9IyIvd3uwM1jbhr/NftKQZWM\nZlTa/SOaYAQqJNITzKuORh//BI9Ejly1oz7pBUWR281G9bm5G8K17RxOJicV\njp7hDFZeUE4odiM3jLLddL4QToEnUisr0y9rjyZ6zPCIoBRPcPuGBMea7cH8\nJsaI\r\n=vrh5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZFIEs8ZXchtujzyXgz8QG8CIWVH84BjJyK9dB0nwd7AIhAL6BoNLf5C/LU6ZRS10azxoVroA4RDzgVhhPFaz67Qff"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.15.1": {"name": "regenerator-transform", "version": "0.15.1", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg==", "shasum": "f6c4e99fc1b4591f780db2586328e4d9a9d8dc56", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.15.1.tgz", "fileCount": 19, "unpackedSize": 134622, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrUPca/byOJGjt5VJxIySYbyOG4eQ53Wib4Z30jtQC7AIhALt5wiwk2tSqFjJOHhu7LDkmdUHxafo/za3iKV6MQ2+6"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcoYoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWHg/+IyGMBKKzNYTB5T6dIR7xqFw+pbhBdlDVktTvQWF2POwiinNl\r\nTfhtDj5hOEZlOrotZ+v0OeT8sp21CSJETFc9jtBEDR3RI0VmxGT7MHC4zEbW\r\nyNi7FYTJY3iCceqV10tWFhUweEBHUXHWrWWTGMD2Rui4jYO6HwTFLsMJ6rRD\r\nqiDlwEeFdhMIPeZEz2ya+7EhtapYrxNkfsHbX2YOfL1Edc6QBf3HW2Uk10ae\r\nVCC8mIEJGspPp6RQCCwre4s6I92og12os88STO3P/H0KAUXZBU8R5EdH1Gsc\r\ns5SnqfXT1n/B83bFOCq1C/bnP4asZSDgAC27NH+AV9dTY3mpAnxIXX+yVyCi\r\nmVRSRSh5a8Lsc/+0yhsPDi9xBaku4pCMKodRuzMRVJhjB9Tg3D2mcUkH2/Xq\r\nXB/w4XAaaFwUuOGp3JlDbWGy+nHqmVK+5AOK6e9KTnTkw0LLFZIzU8klqfZS\r\nIcjRxV4MQqZ68tlQ/wiUkcm7ce/X9nHPciV/lBkCTCQwLMjVoGRFqcP1aoJx\r\ni4FHaqv56nbPOJB6vSfmlSZ/leUg3JxSCu7MJ7mMz0CHmf7902HbuNVfFWXw\r\nilvh9YmFY6uI3TAtMFm1TYMk1kuRwG7DxzGlo9QRCvZqwylaBsIoF6Y6W6hD\r\nlpPmp931QlfQxhgZcByR/pi1eRwNek0NH3A=\r\n=dpw2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.15.2": {"name": "regenerator-transform", "version": "0.15.2", "description": "Explode async and generator functions into a state machine.", "dist": {"integrity": "sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==", "shasum": "5bbae58b522098ebdf09bca2f83838929001c7a4", "tarball": "https://mirrors.cloud.tencent.com/npm/regenerator-transform/-/regenerator-transform-0.15.2.tgz", "fileCount": 19, "unpackedSize": 135426, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH3jAUA4ssoSi8U9CXyPn1Aj1Mie4+qOVSTRCVq3lXA4CIQCcqgh0BFNq2LqDqAvOOu5IeS+iO8MzeHCC6V9g3qrdyg=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.8.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2023-08-07T21:34:02.619Z", "time": {"modified": "2023-08-07T21:34:02.619Z", "created": "2016-05-30T19:09:25.267Z", "0.9.0": "2016-05-30T19:09:25.267Z", "0.9.1": "2016-05-30T19:13:32.160Z", "0.9.2": "2016-05-30T19:15:35.990Z", "0.9.3": "2016-05-30T19:49:54.700Z", "0.9.4": "2016-11-21T01:34:20.376Z", "0.9.5": "2016-11-21T21:37:36.393Z", "0.9.6": "2016-12-01T02:51:41.846Z", "0.9.7": "2016-12-01T03:55:43.794Z", "0.9.8": "2016-12-08T15:47:27.010Z", "0.9.9": "2017-02-18T23:13:41.049Z", "0.9.10": "2017-02-18T23:28:02.894Z", "0.9.11": "2017-03-22T21:10:43.542Z", "0.9.12": "2017-06-23T20:22:58.378Z", "0.10.0": "2017-08-15T19:22:10.145Z", "0.10.1": "2017-08-17T20:29:31.049Z", "0.11.0": "2017-09-18T22:03:55.664Z", "0.11.1": "2017-11-22T23:30:21.639Z", "0.12.0": "2017-11-27T15:26:37.181Z", "0.12.1": "2017-11-27T15:29:48.763Z", "0.12.2": "2017-12-07T19:36:09.033Z", "0.12.3": "2017-12-30T18:03:57.105Z", "0.12.4": "2018-05-28T18:03:18.273Z", "0.13.0": "2018-06-12T23:03:30.233Z", "0.13.1": "2018-06-23T18:20:58.176Z", "0.13.2": "2018-06-23T18:41:21.778Z", "0.13.3": "2018-06-23T20:08:22.255Z", "0.13.4": "2019-02-21T15:13:35.388Z", "0.14.0": "2019-04-30T23:36:04.649Z", "0.14.1": "2019-07-19T15:26:00.842Z", "0.14.2": "2020-02-23T17:20:09.649Z", "0.14.3": "2020-03-12T22:33:53.935Z", "0.14.4": "2020-03-18T16:43:03.073Z", "0.14.5": "2020-06-30T23:15:32.637Z", "0.15.0": "2021-07-23T16:24:45.633Z", "0.15.1": "2022-11-14T18:17:12.957Z", "0.15.2": "2023-08-04T17:25:47.317Z"}}