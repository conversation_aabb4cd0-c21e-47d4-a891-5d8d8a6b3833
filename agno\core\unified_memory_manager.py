"""
统一记忆配置管理器
为所有Agent提供统一的记忆和存储配置，同时保持角色和工作进度的独立性
"""
from typing import Dict, Any, Optional, List
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.storage.sqlite import SqliteStorage
import json
import uuid
from datetime import datetime

class UnifiedMemoryManager:
    """统一记忆管理器 - 为所有Agent提供统一的记忆配置"""
    
    def __init__(self, 
                 memory_db_file: str = "tmp/unified_agents_memory.db",
                 storage_db_file: str = "tmp/unified_agents_storage.db"):
        self.memory_db_file = memory_db_file
        self.storage_db_file = storage_db_file
        self.agent_contexts = {}  # 存储每个agent的上下文信息
        
    def create_agent_memory(self, agent_id: str, agent_role: str = None) -> Memory:
        """为指定Agent创建记忆实例"""
        # 使用agent_id作为表名，确保每个agent有独立的记忆空间
        table_name = f"memories_{agent_id}"
        
        memory_db = SqliteMemoryDb(
            table_name=table_name,
            db_file=self.memory_db_file
        )
        
        memory = Memory(db=memory_db)
        
        # 记录agent上下文信息
        self.agent_contexts[agent_id] = {
            "role": agent_role or agent_id,
            "created_at": datetime.now().isoformat(),
            "memory_table": table_name,
            "last_activity": datetime.now().isoformat()
        }
        
        return memory
    
    def create_agent_storage(self, agent_id: str) -> SqliteStorage:
        """为指定Agent创建存储实例"""
        table_name = f"sessions_{agent_id}"
        
        storage = SqliteStorage(
            db_file=self.storage_db_file,
            table_name=table_name
        )
        
        # 更新agent上下文信息
        if agent_id in self.agent_contexts:
            self.agent_contexts[agent_id]["storage_table"] = table_name
        
        return storage
    
    def get_agent_context(self, agent_id: str) -> Dict[str, Any]:
        """获取指定Agent的上下文信息"""
        return self.agent_contexts.get(agent_id, {})
    
    def update_agent_activity(self, agent_id: str, activity_type: str, details: Dict[str, Any] = None):
        """更新Agent活动记录"""
        if agent_id in self.agent_contexts:
            self.agent_contexts[agent_id]["last_activity"] = datetime.now().isoformat()
            
            # 记录活动详情
            if "activities" not in self.agent_contexts[agent_id]:
                self.agent_contexts[agent_id]["activities"] = []
            
            activity_record = {
                "type": activity_type,
                "timestamp": datetime.now().isoformat(),
                "details": details or {}
            }
            
            self.agent_contexts[agent_id]["activities"].append(activity_record)
            
            # 保持最近50条活动记录
            if len(self.agent_contexts[agent_id]["activities"]) > 50:
                self.agent_contexts[agent_id]["activities"] = \
                    self.agent_contexts[agent_id]["activities"][-50:]
    
    def get_all_agent_contexts(self) -> Dict[str, Dict[str, Any]]:
        """获取所有Agent的上下文信息"""
        return self.agent_contexts.copy()
    
    def get_cross_agent_memory_summary(self, execution_id: str = None) -> Dict[str, Any]:
        """获取跨Agent的记忆摘要，用于任务协调"""
        summary = {
            "total_agents": len(self.agent_contexts),
            "active_agents": [],
            "agent_roles": {},
            "recent_activities": [],
            "execution_context": execution_id
        }
        
        # 收集活跃Agent信息
        for agent_id, context in self.agent_contexts.items():
            summary["agent_roles"][agent_id] = context.get("role", agent_id)
            
            # 检查最近活动（24小时内）
            last_activity = context.get("last_activity")
            if last_activity:
                from datetime import datetime, timedelta
                last_time = datetime.fromisoformat(last_activity)
                if datetime.now() - last_time < timedelta(hours=24):
                    summary["active_agents"].append(agent_id)
            
            # 收集最近活动
            activities = context.get("activities", [])
            if activities:
                recent_activity = activities[-1]
                recent_activity["agent_id"] = agent_id
                recent_activity["agent_role"] = context.get("role", agent_id)
                summary["recent_activities"].append(recent_activity)
        
        # 按时间排序最近活动
        summary["recent_activities"].sort(
            key=lambda x: x["timestamp"], 
            reverse=True
        )
        
        return summary
    
    def save_execution_progress(self, execution_id: str, agent_id: str, 
                              step_id: str, progress_data: Dict[str, Any]):
        """保存执行进度到统一记忆中"""
        progress_record = {
            "execution_id": execution_id,
            "agent_id": agent_id,
            "agent_role": self.agent_contexts.get(agent_id, {}).get("role", agent_id),
            "step_id": step_id,
            "progress_data": progress_data,
            "timestamp": datetime.now().isoformat(),
            "record_id": str(uuid.uuid4())
        }
        
        # 更新agent活动记录
        self.update_agent_activity(agent_id, "execution_progress", {
            "execution_id": execution_id,
            "step_id": step_id,
            "status": progress_data.get("status", "unknown")
        })
        
        # 这里可以扩展为保存到专门的执行进度表
        # 暂时通过活动记录来跟踪
        
    def get_execution_progress(self, execution_id: str) -> List[Dict[str, Any]]:
        """获取指定执行ID的所有进度记录"""
        progress_records = []
        
        for agent_id, context in self.agent_contexts.items():
            activities = context.get("activities", [])
            for activity in activities:
                if (activity.get("type") == "execution_progress" and 
                    activity.get("details", {}).get("execution_id") == execution_id):
                    
                    progress_record = {
                        "agent_id": agent_id,
                        "agent_role": context.get("role", agent_id),
                        "timestamp": activity["timestamp"],
                        "step_id": activity["details"].get("step_id"),
                        "status": activity["details"].get("status"),
                        "execution_id": execution_id
                    }
                    progress_records.append(progress_record)
        
        # 按时间排序
        progress_records.sort(key=lambda x: x["timestamp"])
        return progress_records
    
    def cleanup_old_records(self, days_to_keep: int = 30):
        """清理旧的记录"""
        from datetime import datetime, timedelta
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        for agent_id, context in self.agent_contexts.items():
            if "activities" in context:
                context["activities"] = [
                    activity for activity in context["activities"]
                    if datetime.fromisoformat(activity["timestamp"]) > cutoff_date
                ]

# 全局统一记忆管理器实例
unified_memory_manager = UnifiedMemoryManager()