{"source": 1096879, "name": "babel-traverse", "dependency": "babel-traverse", "title": "Babel vulnerable to arbitrary code execution when compiling specifically crafted malicious code", "url": "https://github.com/advisories/GHSA-67hx-6x53-jw92", "severity": "critical", "versions": ["6.0.2", "6.0.14", "6.0.16", "6.0.17", "6.0.18", "6.0.19", "6.0.20", "6.1.2", "6.1.4", "6.1.17", "6.1.18", "6.1.20", "6.2.0", "6.2.4", "6.3.2", "6.3.13", "6.3.14", "6.3.15", "6.3.16", "6.3.17", "6.3.19", "6.3.21", "6.3.24", "6.3.25", "6.3.26", "6.4.5", "6.5.0-1", "6.5.0", "6.6.0", "6.6.2", "6.6.3", "6.6.4", "6.6.5", "6.7.0", "6.7.2", "6.7.3", "6.7.4", "6.7.5", "6.7.6", "6.8.0", "6.9.0", "6.10.4", "6.11.4", "6.12.0", "6.13.0", "6.14.0", "6.15.0", "6.16.0", "6.18.0", "6.19.0", "6.20.0", "6.21.0", "6.22.0", "6.22.1", "6.23.0", "6.23.1", "6.24.1", "6.25.0", "6.26.0", "7.0.0-alpha.1", "7.0.0-alpha.3", "7.0.0-alpha.7", "7.0.0-alpha.8", "7.0.0-alpha.9", "7.0.0-alpha.10", "7.0.0-alpha.11", "7.0.0-alpha.12", "7.0.0-alpha.14", "7.0.0-alpha.15", "7.0.0-alpha.16", "7.0.0-alpha.17", "7.0.0-alpha.18", "7.0.0-alpha.19", "7.0.0-alpha.20", "7.0.0-beta.0", "7.0.0-beta.1", "7.0.0-beta.2", "7.0.0-beta.3"], "vulnerableVersions": ["6.0.2", "6.0.14", "6.0.16", "6.0.17", "6.0.18", "6.0.19", "6.0.20", "6.1.2", "6.1.4", "6.1.17", "6.1.18", "6.1.20", "6.2.0", "6.2.4", "6.3.2", "6.3.13", "6.3.14", "6.3.15", "6.3.16", "6.3.17", "6.3.19", "6.3.21", "6.3.24", "6.3.25", "6.3.26", "6.4.5", "6.5.0-1", "6.5.0", "6.6.0", "6.6.2", "6.6.3", "6.6.4", "6.6.5", "6.7.0", "6.7.2", "6.7.3", "6.7.4", "6.7.5", "6.7.6", "6.8.0", "6.9.0", "6.10.4", "6.11.4", "6.12.0", "6.13.0", "6.14.0", "6.15.0", "6.16.0", "6.18.0", "6.19.0", "6.20.0", "6.21.0", "6.22.0", "6.22.1", "6.23.0", "6.23.1", "6.24.1", "6.25.0", "6.26.0", "7.0.0-alpha.1", "7.0.0-alpha.3", "7.0.0-alpha.7", "7.0.0-alpha.8", "7.0.0-alpha.9", "7.0.0-alpha.10", "7.0.0-alpha.11", "7.0.0-alpha.12", "7.0.0-alpha.14", "7.0.0-alpha.15", "7.0.0-alpha.16", "7.0.0-alpha.17", "7.0.0-alpha.18", "7.0.0-alpha.19", "7.0.0-alpha.20", "7.0.0-beta.0", "7.0.0-beta.1", "7.0.0-beta.2", "7.0.0-beta.3"], "cwe": ["CWE-184", "CWE-697"], "cvss": {"score": 9.4, "vectorString": "CVSS:3.1/AV:L/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H"}, "range": "<7.23.2", "id": "hqamRkKotkEIG4q5tEjuQlVFG7+99LVnKPcO21ldHuE0xMPL/en7/R4soSCm+5yK8j7Zgz5dBAWFDcYR5tLU2Q=="}