{"name": "@types/clone", "versions": {"0.1.16-alpha": {"name": "@types/clone", "version": "0.1.16-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5ed50bc4a1dd2016dd6f03673a2fffc7624ac7be", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.16-alpha.tgz", "integrity": "sha512-koal29IDmK8oBCgPt2Zm74dCf0NC1mx0o3AsQ9A1SJXc2buPvtpKPC0758QD9UFbXpZrkEIF2P0+OoBvbA89Rg==", "signatures": [{"sig": "MEUCIQDQnNqbcq1KXB6LYotmHLvBcAn+sqwDtpq+9fvrfLnYaAIgf1TL+S7nuf3WJql9liBN+y0e85qbHrQLgHl274RbjBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "5ed50bc4a1dd2016dd6f03673a2fffc7624ac7be", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "_npmVersion": "3.8.2", "description": "Type definitions for clone 0.1.11 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.16-alpha.tgz_1463459912084_0.128535513067618", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.1.17-alpha": {"name": "@types/clone", "version": "0.1.17-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.17-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "138fe72869c9aa0fb150eb67558900fc61184d56", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.17-alpha.tgz", "integrity": "sha512-Dw3MM+3SgMP/XuEUxGPA0wbVn/FayCfxWePzzTWlvm4QYtl3d/6N6OpditgxvY4VIWaXzSdJH1UuHtdBvHx0rg==", "signatures": [{"sig": "MEUCIQClZHTlF6ZvILjfH71nj4A2IkSHJdu4c0fCkfOYuKpFeQIgEFIwNZ+I6RBl/iF4+cuhpMhXprJnZEjN8Gp7m/Zoseo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "138fe72869c9aa0fb150eb67558900fc61184d56", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "_npmVersion": "3.8.2", "description": "Type definitions for clone 0.1.11 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.17-alpha.tgz_1463689810600_0.5367426150478423", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.22-alpha": {"name": "@types/clone", "version": "0.1.22-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "3a3b6e1207b9f2d9bfaad947d06b151654453665", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.22-alpha.tgz", "integrity": "sha512-hO8Xf6wPnMLCuC899FXWh0RbRV56V0/1ICu9jDubvHMjSR1pu1AUbjiq/j+lVAiCIshkfcet1E1gBTuEZ8TH7A==", "signatures": [{"sig": "MEUCIQDy9NzbaGSa5YZhYoP3tpCBar66jy1tYwEdOtiNgFytGQIgWljAd86N0SkEFZJ30DzO+uowBtQcbaNPpUnWpthNA4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "3a3b6e1207b9f2d9bfaad947d06b151654453665", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "_npmVersion": "3.8.2", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.22-alpha.tgz_1463772012576_0.9452337166294456", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.23-alpha": {"name": "@types/clone", "version": "0.1.23-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "bf17b2e1cc84e358a9bb5d2c2851c6eb5d12d5c5", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.23-alpha.tgz", "integrity": "sha512-pmmmliSQcirT7Zyki2PxXiFIWh2ttcWb67XO4Xh0TDFuTtF7xCm7eQ6iv3E5XGY9xnrSI/9yEWP60iWke9Ty1Q==", "signatures": [{"sig": "MEUCIQDyO6SIKOO3M1C5OQaSWSuTnXsTPOHgrHpfclok3LpZGgIgMKqC5C5nlIp5vqmOKtwWLvAoOtQxxyjy41WnBHYiBio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "bf17b2e1cc84e358a9bb5d2c2851c6eb5d12d5c5", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "_npmVersion": "3.8.2", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.23-alpha.tgz_1464151102205_0.20481873420067132", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.24-alpha": {"name": "@types/clone", "version": "0.1.24-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "71111878e144e801114334377ff525cb50f37c48", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.24-alpha.tgz", "integrity": "sha512-Yv1y5TO+4H/GujxO3Zh5yUHXSarrj93ji1N3wQRxiYRwWysgxh+gds4xk+pKZL7HTtymueKJYCqXz3hNz6MVrw==", "signatures": [{"sig": "MEUCIQCTiU1bitvUacbxobRo2sNNajoomvjt6ahG1O/91QIv5AIgRRh5m9auscns+GU2q5Sd3bD0ytf4AW7XbFVAdyLCXEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "71111878e144e801114334377ff525cb50f37c48", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.24-alpha.tgz_1467399716435_0.3763869716785848", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.25-alpha": {"name": "@types/clone", "version": "0.1.25-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a73768ff3ed13b12d27670d9debb66c0cc81de54", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.25-alpha.tgz", "integrity": "sha512-hP6BrAvpUuWQOMwsoxesF2Z1p8KOoGOWKLK0Njoh6pT0aurg/4aSmTx7SuYarxIO7EJtLSqT7bx6Bjg4Xab7QQ==", "signatures": [{"sig": "MEUCIEtzRLv/LQ/R1druyKcZIr2NLjrDoQMswebUoeT7+AcjAiEAhbEe5lPnzrE0XEd+ZTrIiF6PeWMz667JmVKLk0iizjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "a73768ff3ed13b12d27670d9debb66c0cc81de54", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.25-alpha.tgz_1467412176349_0.9058316643349826", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.26-alpha": {"name": "@types/clone", "version": "0.1.26-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "dd2dcf854a41282d1c04c020147e2267c11b9904", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.26-alpha.tgz", "integrity": "sha512-KX4NIqvOBET88wuyqB1LTDt3HzX2iU0H+5rVEAuZQ77iuCLBcS3oR+0aBuSoHyhV1UebGLLEjE8W4ouB5o8MYA==", "signatures": [{"sig": "MEQCIHtM0fBq824JZmglEfdrrg9K/xwH1a3ZX+X+594kpdZZAiBjUJruYyUyRMUD0MO1sIK1OQuUutifamcwj7RQh//N8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "dd2dcf854a41282d1c04c020147e2267c11b9904", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.26-alpha.tgz_1467425308133_0.5790591372642666", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.1.27-alpha": {"name": "@types/clone", "version": "0.1.27-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6d93b0fbb054e4e2379f47ae820732818bda4aa2", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.27-alpha.tgz", "integrity": "sha512-60FiyWqB8XN4TpWG3ce/O04MGQkvB6JW3dDzlJypU95LayE7LT1Q7uE93USrCcwg/QQw7reJYqHxqH82SINS4Q==", "signatures": [{"sig": "MEUCIQDney0eTuMa0mnH2Ewu5ckt9iPcVwycfhtipJrcoN42YwIgTOX9nojMoefnvNz4g5KNigHUvjnYU70rgLzIcKkTECc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "6d93b0fbb054e4e2379f47ae820732818bda4aa2", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.27-alpha.tgz_1467590418601_0.7243966681417078", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "0.1.28-alpha": {"name": "@types/clone", "version": "0.1.28-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1897bf1b7c4785e3f84293f100b4ff7630aa14fa", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.28-alpha.tgz", "integrity": "sha512-Sg3aCFnZlpOwX8mcIs/s9IiBSH5W8W6mz/rHqIDGPuSueoxEm6CVjKKzzwWVGOZvfjekCWRVByqUOX9eQ5atOA==", "signatures": [{"sig": "MEUCIQDLi26MFw1sUNxrRFiWLvsQGlbTZTYt2tPlo+jPAQu1dQIgeZCHXmTUxL6zS5er2+1H7jZPGXj/3JHMLn6SIsdm18A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "1897bf1b7c4785e3f84293f100b4ff7630aa14fa", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.28-alpha.tgz_1468007474128_0.2384385927580297", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.29": {"name": "@types/clone", "version": "0.1.29", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/kierans/DefinitelyTyped"}, "license": "MIT", "_id": "@types/clone@0.1.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "65a0be88189ffddcd373e450aa6b68c9c83218b7", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.29.tgz", "integrity": "sha512-twJ11JBFoM6DuETE1QiNAQa5L6AizJuDuSthxiIfC3MByv43GmgK5ogHkkrdsXY9O/RrIc9+AOZZ2tWDyFnzMA==", "signatures": [{"sig": "MEQCIGmcA6C9+WsuvMQ417nphsj1DhMuT9bpksFRZQyiHAsUAiAJl96KPojsmAKh75ck0JIDg6gCp+VoHpf3IVyuNIO2zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\clone", "_shasum": "65a0be88189ffddcd373e450aa6b68c9c83218b7", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\clone", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.29.tgz_1468505828685_0.3918754083570093", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "0.1.30": {"name": "@types/clone", "version": "0.1.30", "author": "<PERSON><PERSON> <https://github.com/kierans/DefinitelyTyped>", "license": "MIT", "_id": "@types/clone@0.1.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e7365648c1b42136a59c7d5040637b3b5c83b614", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-0.1.30.tgz", "integrity": "sha512-vcxBr+ybljeSiasmdke1cQ9ICxoEwaBgM1OQ/P5h4MPj/kRyLcDl5L8PrftlbyV1kBbJIs3M3x1A1+rcWd4mEA==", "signatures": [{"sig": "MEQCIE+nqqYPKWzf4nb0uYb+p52ksd+/O/pjKb5RlbLimtfNAiAQ0HYBfdWkrqEGTiOZXcryhNxA5sVGfkA0RVAhCCnabw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for clone 0.1.11", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/clone-0.1.30.tgz_1474302779175_0.7775814738124609", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "29fd82aa066f07307712c9ce140436aba20b3bfbe9bf8f702f359c230c481f03", "contributors": []}, "2.1.0": {"name": "@types/clone", "version": "2.1.0", "license": "MIT", "_id": "@types/clone@2.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kierans", "name": "<PERSON><PERSON>", "githubUsername": "kierans"}, {"url": "https://github.com/DG-za", "name": "DG-za", "githubUsername": "DG-za"}], "dist": {"shasum": "cb888a3fe5319275b566ae3a9bc606e310c533d4", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-2.1.0.tgz", "fileCount": 4, "integrity": "sha512-d/aS/lPOnUSruPhgNtT8jW39fHRVTLQy9sodysP1kkG8EdAtdZu1vt8NJaYA8w/6Z9j8izkAsx1A/yJhcYR1CA==", "signatures": [{"sig": "MEQCIB/IVX6gVExIpYyeqHGoR+WarrqB47U9Bnba4Tfv07qMAiA8sntlQoQ+EW/NMgm7NihZrbTzuoEiYw+Cbz9+WeU8XQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLmiWCRA9TVsSAnZWagAAI6gP/2jYzFJXFpYax8b9BFt7\nsJAUop9zSsekv7eyQsIQTjNx2evv+B5EzW1TLcunxfhjOH+Agws8YAq7iaF/\ncJ6VmZ2SBT5j16WxO24NPBofQMYWDcJywvqBxgYVMVVuqQgxC/6a/I44RNX+\nqTXNfMD1uTJk43hDuiRCjNdJJq+ZzdKP47vDDn4yMlLm2WzlfVCJSriYyuhe\nKrtBD0hO8nkKovgKw1/llds1onOWD890tCla6PwCdqaF0YwUHBywAEROzpvk\nyPMfeU3Irtp7nsefp9eQClgZA1y+bzrqqgjbleFvZIgCEOjh9QyoX4DfSPGa\nzdikh1uMBTkm/UlkauAbeUSFAzSR1fdVFCGzvhOmFqj15MNYLvuy5klQQLeV\nzRj4V63ebAcdP3PM1CFvdeFewHrrNROo2KRCa5J4GT+s94H4YSx3mRVX7gCF\niG0XxKwBhdCKi2YNyIS23JerSYZBmrFEWGH8U9FlbaB+us8YARfRQjGXSqWP\nSUhMQ8LoO3aZmrCRbT1QMDpKGTnwSojI6eMhDzK/eB0GV2UWnkxZLJJKQqTI\ntPxyUfCXbswuucSRqioe4AGmAc7AC90eCuZe3ciXqwFHrXswtOg6IF1whZa1\nkTcvTpBUd6vxtZ31/Cok1HtrpnQ7zru39RoFpD+ms6OiuLDO9upHTHh3ZVpU\niB4L\r\n=3TTZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/clone"}, "description": "TypeScript definitions for clone", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/clone_2.1.0_1596876950453_0.71899065273541", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "875a3d68bc3518139eff190816f0215d1ba2475faf583db671dfcd364cce1ffc"}, "2.1.1": {"name": "@types/clone", "version": "2.1.1", "license": "MIT", "_id": "@types/clone@2.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kierans", "name": "<PERSON><PERSON>", "githubUsername": "kierans"}, {"url": "https://github.com/DG-za", "name": "DG-za", "githubUsername": "DG-za"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/clone", "dist": {"shasum": "9b880d0ce9b1f209b5e0bd6d9caa38209db34024", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-2.1.1.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>Z<PERSON>U34bSYye0j/BFcPraiDZ5ka6MJADjcDVELGf7glr9K+iE8NYVjFslJFVWzskSxkLLyCrSPScE82/UUoBSvg==", "signatures": [{"sig": "MEUCIQC8G32AfRVqvW6/9aEpnhnuUzwwWQwU7lj/jz6mpHI5/wIgDLBD3Go7YXMSUaOQefa+/r660RX8wV1QPCEPb2Sf7PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5KDVCRA9TVsSAnZWagAAA9kP/3bdpiD+wV739GHf0UPb\n/OXLqSu/lnR0NED628nJv9AS9zFMbLPWRg+5kdubG8Ri64q7w6T8CbLsBTm9\npfHcfnKWaTHrbCFTmq1vfXLN5WP466L0SP53xgaj+Jh7yroHJGQWj18p5SVI\nWohIjSXMEQkVNNPGhlaPVbHm2HJWc4GhTaKqJIcAqomYaUu6k9Lv+2mpEoAY\nvw9i4VvyUCx3QL390qkLH31OtSspqsgXCeMJCo74t5/UF0TtdPKF6dEEXuCi\niY5ToCXqMTUZEfceR6GDpG7ZcM6teu9eJ4V82H7gec6c82+m5/K63b+NrpFm\n8dvdNdnW+lzJxW1x3vInVCH1F6j+0ePzVc3UDb3eKHDmP0Onu3t5eHeNSG4w\n7ft5+dYKl6ybFvuy6g04XIOPYkTngBT118Dz+9J85b057XONqK4xz8Zfj7xp\n4Wkms+7NnI4xdOd7ZOyKXvtPTjfjmAWO0+BI/vOpY8p16ekLolK7TgENx1ta\nTRkucEQ4gwW/DPtZcJkC/71r+XnQzeMGmSzwBnU5Q4qVe6NcMmU6O99ipLwX\nM1pfnr/t9Ve1eVHPPswTVnPLCIB8f1rCrcxXYVJevT+SrvIwpUxX3+D9SlF8\nLlMPat3IwsTlOeNDSFqftUln6sgVDAjDENF2TdymarjA3QCTuJFMa2yhWGF+\n2pN3\r\n=jpxn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/clone"}, "description": "TypeScript definitions for clone", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/clone_2.1.1_1625596117026_0.7414778276425105", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ecbf6592193e4a9b6a79a2e9b9a787d58a0be7a75ebd40ac3807c9696533575b"}, "2.1.2": {"name": "@types/clone", "version": "2.1.2", "license": "MIT", "_id": "@types/clone@2.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kierans", "name": "<PERSON><PERSON>", "githubUsername": "kierans"}, {"url": "https://github.com/DG-za", "name": "DG-za", "githubUsername": "DG-za"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/clone", "dist": {"shasum": "94ad2e8909eb214e0eebac3b350a5c88118dc76d", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-2.1.2.tgz", "fileCount": 5, "integrity": "sha512-fKXTsQvII/8K7xSxQF2OEuN6Pt4/PDasws60s/qeqoIOaBX7Aoevy5CmaQ4fANbvOo04MN3kx3xUIc6ZNXsmaQ==", "signatures": [{"sig": "MEUCIG6DC61rEOv42WI/J4sqHDKdSl7wfHdtJvhKnCVT/ac9AiEAybOs2CDTZI3S9zSC37fL1lADYhxE2SyTNaOQKgXjSVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7084}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/clone"}, "description": "TypeScript definitions for clone", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/clone_2.1.2_1693843360895_0.8688549810184365", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5d1a2b5bd37259ebe7aaae31d3485bbed74d1e67a9fa33fe051982e9395ea436"}, "2.1.3": {"name": "@types/clone", "version": "2.1.3", "license": "MIT", "_id": "@types/clone@2.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kierans", "name": "<PERSON><PERSON>", "githubUsername": "kierans"}, {"url": "https://github.com/DG-za", "name": "DG-za", "githubUsername": "DG-za"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/clone", "dist": {"shasum": "91d16af4006a24fd3d683f5454d48b29a695b0e9", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-2.1.3.tgz", "fileCount": 5, "integrity": "sha512-DxFaNYaIUXW1OSRCVCC1UHoLcvk6bVJ0v9VvUaZ6kR5zK8/QazXlOThgdvnK0Xpa4sBq+b/Yoq/mnNn383hVRw==", "signatures": [{"sig": "MEQCICoexw9dwQJDKn02Iu4ZOu9o1x6+bkVK6gJS0kTQUoo5AiBRC4buT18IfSUZxC1PrAGS2mfx5/n5TcJgCrFBT1JolA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6530}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/clone"}, "description": "TypeScript definitions for clone", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/clone_2.1.3_1697586913696_0.17176816249684124", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a8a6cc7a719c58983b86976190db2a5d050af60f0869e7530e0dcf657a9f9758"}, "2.1.4": {"name": "@types/clone", "version": "2.1.4", "license": "MIT", "_id": "@types/clone@2.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kierans", "name": "<PERSON><PERSON>", "githubUsername": "kierans"}, {"url": "https://github.com/DG-za", "name": "DG-za", "githubUsername": "DG-za"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/clone", "dist": {"shasum": "9680f886c935dcf596273f1218abb71efb01531a", "tarball": "https://mirrors.cloud.tencent.com/npm/@types/clone/-/clone-2.1.4.tgz", "fileCount": 5, "integrity": "sha512-NKRWaEGaVGVLnGLB2GazvDaZnyweW9FJLLFL5LhywGJB3aqGMT9R/EUoJoSRP4nzofYnZysuDmrEJtJdAqUOtQ==", "signatures": [{"sig": "MEYCIQCq180bLOpZ1MskbGRe4xSdyeZPiFLKV1cwO9gmrBjlnwIhAM6Z+yXYteHDr1zmMAUl21TV8NaoqJPYPsSPfeSk9h7y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6530}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/clone"}, "description": "TypeScript definitions for clone", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/clone_2.1.4_1699317753588_0.9509563171042623", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "12d928d0ed5d27aba064c75e6c852342b0efade6fa1a012feaa0af4f9ee7c7df"}}, "time": {"created": "2016-05-17T04:38:32.698Z", "modified": "2025-02-23T06:31:55.599Z", "0.1.16-alpha": "2016-05-17T04:38:32.698Z", "0.1.17-alpha": "2016-05-19T20:30:15.470Z", "0.1.22-alpha": "2016-05-20T19:20:13.544Z", "0.1.23-alpha": "2016-05-25T04:38:24.671Z", "0.1.24-alpha": "2016-07-01T19:01:59.980Z", "0.1.25-alpha": "2016-07-01T22:29:39.959Z", "0.1.26-alpha": "2016-07-02T02:08:28.711Z", "0.1.27-alpha": "2016-07-04T00:00:19.127Z", "0.1.28-alpha": "2016-07-08T19:51:15.515Z", "0.1.29": "2016-07-14T14:17:10.850Z", "0.1.30": "2016-09-19T16:33:01.115Z", "2.1.0": "2020-08-08T08:55:50.548Z", "2.1.1": "2021-07-06T18:28:37.175Z", "2.1.2": "2023-09-04T16:02:41.109Z", "2.1.3": "2023-10-17T23:55:13.946Z", "2.1.4": "2023-11-07T00:42:33.758Z"}, "users": {}, "dist-tags": {"ts2.0": "0.1.30", "ts2.1": "0.1.30", "ts2.2": "0.1.30", "ts2.3": "0.1.30", "ts2.4": "0.1.30", "ts2.5": "0.1.30", "ts2.6": "0.1.30", "ts2.7": "0.1.30", "ts2.8": "0.1.30", "ts2.9": "0.1.30", "ts3.0": "2.1.0", "ts3.1": "2.1.0", "ts3.2": "2.1.0", "ts3.3": "2.1.0", "ts3.4": "2.1.0", "ts3.5": "2.1.0", "ts3.6": "2.1.1", "ts3.7": "2.1.1", "ts3.8": "2.1.1", "ts3.9": "2.1.1", "ts4.0": "2.1.1", "ts4.1": "2.1.1", "ts4.2": "2.1.1", "ts4.3": "2.1.2", "ts4.4": "2.1.2", "ts5.8": "2.1.4", "ts5.7": "2.1.4", "latest": "2.1.4", "ts4.5": "2.1.4", "ts4.6": "2.1.4", "ts4.7": "2.1.4", "ts4.8": "2.1.4", "ts4.9": "2.1.4", "ts5.0": "2.1.4", "ts5.1": "2.1.4", "ts5.2": "2.1.4", "ts5.3": "2.1.4", "ts5.4": "2.1.4", "ts5.5": "2.1.4", "ts5.6": "2.1.4", "ts5.9": "2.1.4"}, "_rev": "7179-65a3b286500e4713", "_id": "@types/clone", "readme": "[object Object]", "_attachments": {}}