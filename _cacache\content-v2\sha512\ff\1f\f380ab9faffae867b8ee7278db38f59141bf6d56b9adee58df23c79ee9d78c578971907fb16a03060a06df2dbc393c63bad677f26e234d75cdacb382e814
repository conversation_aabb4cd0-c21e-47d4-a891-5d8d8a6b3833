{"name": "babel-plugin-transform-es2015-modules-amd", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.0.2", "description": "## Installation", "dist": {"shasum": "4249a205b99b9dfb815d0cd07326752de578f35a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.0.2.tgz", "integrity": "sha512-6h/eS/HgNe+Ldp5y6RxyshFgmgyAoHs2beEXaRgczFxsauGLhsA2ijGmBWOPjsOjcHfOoH+yw0eTMulSEMlHxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxBXvLgWDLqvO89YnvWW7B2+yhCotfIV/f735kFeCUxQIhAOqwiTcQxZPw3cef/GJLXq5FO1dJ+usUEXkKU1S+Bgz4"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.0.2", "babel-template": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "4bd5e2ff565859d952356bb4ebaa277be5a877b2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.0.14.tgz", "integrity": "sha512-klrmACnBwc99QRP6+8DXH+9Gh+IZCg7yMYzvrZ52BPSXfiKlZPg4MblgWUSCNM3RbSsaQedylcpoy2+P7nQlhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiOcVrsEi32X7Osg8wBm0LLjBWswZdqEQJj+7PSTeQogIhANDAJRz0BpuTAimSIKHo+2E+cxS0l+K0iHoEF0xwJk3M"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.0.14", "babel-template": "^6.0.14", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.0.15", "description": "## Installation", "dist": {"shasum": "e56b535202b28014bdafd8656fd1be4d76c55c00", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.0.15.tgz", "integrity": "sha512-7UbOgHJiNlieZ03zwlFmT7A0jAqOSbXoH90gE+8eIr08GRjvM4RRWAKC5V+DHiOxf0NOsaPamv4cKJS4/fqQfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkfSkaLEoKZGfSEMWIyqclMdoRAAmO6CJnb9xZyHmMyAiEAw9QGOQYHhIjFZuepMEpx7uPWNx8U5XBFd663M+5NHJs="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.0.15", "babel-template": "^6.0.15", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "4d411f0013d64fde8ff154a1a10db9fa2373ef4f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.1.4.tgz", "integrity": "sha512-L56tSsSKjlCA1KCdyRxRCKp9Y9MsNFUCIywf3uCAgPlWAyeMU0wNbfIK+lw9Io0vKdCeNviTHaXzDCU5ahPM7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMibbYoVB+XTJKht915JeiBmdCEKnPIeP3BHb6IqeIAAiAbwhUfwMGnB0GHvJ6oTm9cU+Yn1DiyZOLWTrzEUB/SUg=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.1.4", "babel-template": "^6.0.15", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "6e0c6bc97c407f0bfe17a95f25cc406b64eb2239", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.1.5.tgz", "integrity": "sha512-oDh98rwl3VlCjFhq1U6fQzgO954HFzNEWb9nX7cexoTfM4vbJIHpgXLtmlOYg1xiB+XyAednMl7IJK3Ms7uqlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdAYKn3jikqF0jiIY/wRB4HQwjtDPG8Ue2Bp1QjLcyfAIgWweIok7maNdnuVJm/4HpGWNOSXTl0im9KwUj4EiQYJU="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.1.5", "babel-template": "^6.1.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "c088ec7cf579f9f7eecd6e2a0c0de51948333cdb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.1.17.tgz", "integrity": "sha512-WtchxDNYNkQ67kOdzOSWNC9u2v6qwY15qXs+QEu46APcG+eIC68SgSQfdNOeDl29MGPAqjF2SDvGMIeuiXGI0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKBatbXMz/cHV7OongYRaY4LMIo8avvC8z0ETAxauX2gIgTDjQUg5Admo8WvHp+E/icbjGaRUunxHRQ944cncfOPw="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.1.17", "babel-template": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "886102034e249d6425246e1b71e5ed54bebaf148", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.1.18.tgz", "integrity": "sha512-K3E7JivAkbO5WcUq1Z1hZyXkSpfhU673cnlzw5q24dK3hMFCN8KNngKDFsWGPExsHx/aos0S+8VjSgGFCz4oFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDlD/oiJw/bR3zyy6nr5lSbVIXctBaefkNgamlrDd8zAiEA8f7VKmzRGwUqHbslXcKVX/wK9ZUwofCyZDQFRRTA6o4="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.1.18", "babel-template": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "f8e8978b8832264ea1bb8e747dc5f70df2a9ce67", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.2.4.tgz", "integrity": "sha512-H2/5peRF1rvheBeCZBPccUHk3g1vMFfFBCGip+/hPB3zjTLznIj8zFN+sdPH/X4EkcSX65tC5Itc4sZkG2OH3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE4+NOVkC3cxOdUtq77z+bwDx0oQ2uVxX/SlihPR+TqGAiEAwEAuPZy6KULLK4I4wuXwwfYEa3SsZPkHjr7FapHDfQk="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.2.4", "babel-template": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "38c932f1123c21a41d30f46188961515e90ca4a7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.3.13.tgz", "integrity": "sha512-Wf+Raub6ZQ8BIuKeW3ystLnZve21swWlocsnLbR6I8JVP8y3my9hrIJVXecNHle9A+1wzL9G0yGX+l8fuqmmfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICg+78zwkduf5OqO8o5cojQ/sa3+gQ3c28zJ5bWBC0UdAiB9IW9w6FjDDtQr1NE8WVoQjMvPMvK6ydfD1bNJOy8HuQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "3e53b7a1636e1b0287e0add0a7af3dcefcb6d285", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.4.0.tgz", "integrity": "sha512-UokE0+SsvSnycvLYaWyvR3kjGXr6LzAJz5s0AcjFuL78au9bUEltkwmuIeRBvsYIfzZQ4AorietcnwtRYRrJxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGY3IF5B8fScoZKx68gWnkH/5O8FluNZhu3a25OpSuZBAiABN9UYBjZ6iM57dLYXNZtYccbl9tNfxDo2/8yjb5zu3w=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.4.0", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.3": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.4.3", "description": "## Installation", "dist": {"shasum": "fbe261f89c7203c0696b40827326864e907a5841", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.4.3.tgz", "integrity": "sha512-RJD1hj9MVG5MNHOUJ7JlMqE3CXfatReBFRp3hkLKFPVbB8OqkulHSq8SqGDmPb7ZyCANHdwq1sR0R/HVv2fdWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHKnx1e3lzjeRqQssLI270wa8xpyW8ldod+h6GaS+s0QIgTzRvbmOwgJY0zGauaPqZ/0Bk8O13GeNGpbmOA/rjlkg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.4.0", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "e170d6445b7da826ce8003b537478877527d64a3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.5.0.tgz", "integrity": "sha512-K1CYJHJewnaLodgwE3z9vfroWV9HgVTvQZXKf2lqttQrQi7vEOfZLTMMD1jhOJhGN/7vdVX+jk5xL8+Uosx0Mw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7LDdPGl+582aqLFXIUx7AZyIdVwb59ijd0Dm+Ld/vEAIgRG1lk3wUaHCu7NS0P9Gv0OI7t7Y8Go6PA4eXwNiYFsE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.4.0", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "8f6af6ed64f03b0eca844007a0beeabb6a4da032", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.5.0-1.tgz", "integrity": "sha512-rzRz7HZCQlplVsdAmJ1A7z5seNY1tzRDdhJB/BvYNCQLOqpEanCoxo9fN2w3yVFNxzBCaXgjfKdP3leemgFsAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzrDULgzKSyKSw2tVHfrkpDWzNIhjAOgaT9NHa68NbhQIga7+vGtzkh+pGfloe3oWrSSlh77/oGTcaUWwzCjQZhdk="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.5.0-1", "babel-template": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.6.0", "description": "## Installation", "dist": {"shasum": "ab0b0a478abe9f86178dab6522699011a309754d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.6.0.tgz", "integrity": "sha512-l66CBxNsgzteu0xaQhTNyxajncwBzCaUHgBBEEn0q43MOWNi8NvJNkEirRoaQAShdyKXwIdwt6zOQtErlJTgJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzuU0egWqvynGMICPdjTqksShjoXqJ6fqg3Zm1vGNjxAIgM7lCmzGtTuuf7oUK/fJudKW4zT8Kmcz07IXHXFTlWvE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.6.0", "babel-template": "^6.6.0", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.6.5", "description": "## Installation", "dist": {"shasum": "ae1958c034a0a8f54ed0e788c7ce60c0efb849e0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.6.5.tgz", "integrity": "sha512-uw0YUrCwmcaveGqoNEogLynCIPA2CPhQag3yG1LkL1Hmmygo2bt/lo8icYAyyP+gRNzbVD08Vckh6FT3DXel6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPdtEBZUBQIFJVXlWbpIa5pjW1KHG3Cls7KPT+PA5wiAiAEneBlOXhGctO80384FLO8Hd6PWDB1x1GAAn+EVlfqJQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.6.5", "babel-template": "^6.6.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "25d954aa0bf04031fc46d2a8e6230bb1abbde4a3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.8.0.tgz", "integrity": "sha512-0Bp2TVoLXFDC+MPhb4B/o8hDYIExy1QOfVU/teExRWi0aPHqnSkLxSA6nnCbF5xALvy4j9d3B6QiYhwzOrqFYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXJzFdXxyz7vyxr6xSHp7ShajmdrLfuCQNq0Qa4CdmwAiEA1+jJeBLAFw8WL/xlJBrlO51Cs8AUjMVAWp1+adjqHkM="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.8.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.18.0", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "49a054cbb762bdf9ae2d8a807076cfade6141e40", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.18.0.tgz", "integrity": "sha512-pDwKChyXNv7F1onUGaLXhdYlvKQJ4rreiO2DmItWlJGqt1H1UYgKCA6iGdM27Gif+AaxOi7UfhuCLHw6B+BiUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdOAQho+jy4qFB/1jKZ2hkYgz1j6EsEMXGQeVt2Ojd+gIgacJcrLHxpm1pJqRuoUQGiKyc7gHu8E+LMlHJcilUwTA="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.18.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.22.0", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "bf69cd34889a41c33d90dfb740e0091ccff52f21", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.22.0.tgz", "integrity": "sha512-IyRFOco6CgLH5cXE9wW27PeIZ50FC93ETYNR0kT9LTtfKv9YISMMwQFbohaYtVUSKJyR9wqbTVec8c7YN4FLCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvKmjhJhbF1SvHj7b3lQGlyOf4jg/qLZYKp9SIHfvL8gIhAIbwB1wkCdhVQP9wl9YmU1XP87yQcBaTC9aChWSRf53S"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.22.0", "babel-template": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.1", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "f8bf8fe6c80f6afa0385904c41fc928e343be782", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.1.tgz", "integrity": "sha512-EanT32UK3/Vwqmscr/j4yK401UxuGICXHdykdvQM5Vn59wvFMw0kArFfZJkNHk/7KRNrGlolvpA2f01rzwc45Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA2yN9fVLRkH64CJfLCtt4BLk6IrtLnbuoMsxiht1nMyAiBk1fHN2SicK3TRjv3QLgjSQxFBOlZyZrbaKPYxb9TAig=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "6.24.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.24.0", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "a1911fb9b7ec7e05a43a63c5995007557bcf6a2e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.0.tgz", "integrity": "sha512-1y5Fhg8dz4H509qc2RfOXndCof+daeKOGm7P3IitsqMpEkd9DiesLMP53mtZuKNFWDOVcqhQZnyB/1uT0uYt3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBYYCQdwqCIPAOyYB+pxhXl1g7g2Z+l+yY7xUL5GbgRrAiA3CPqQXrrVJj/1HZ8d7a2RKsAxGm4miyWtjbE1DO1v5g=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.0", "babel-template": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.3", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "960aa59d67e76948beea0cd2a5bb6771cea36c7f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.3.tgz", "integrity": "sha512-zgsk8QBbsp8HOP4Ek7FA8ElH+4KAr6RKiSx060Uq9NJXai19veAyCOBlcGIQSGoduGupqTxLktS8f+C2q+28Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCp1Yu9pOUlQOM4qf27NLysADtf/8Nmv3JEMHt4jMAZlwIgVcKCcRhY76vFoKizZhIRujREPzXPvWOvcVJ9/zeRcos="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.7", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "29a01b92303036eabe120cfa8af1fd8a23fb41b0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.7.tgz", "integrity": "sha512-LHNjGJJNNNgL+XxioxdxwDFWftrU4o+2X4WmfaHTxJ3HPT0GtyFwGZL1JosGVZY3Arl+fb4tCPTdl60Boary8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1FLr8ql4O6tSRPolLksvtbC5fgSwNMhhZhTM+0cCb9AiBh50kBw6BjR71ky1eB45xPmTxAChAeM80undCr5yCcmQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "6.24.1", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "3b3e54017239842d6d19c3011c4bd2f00a00d154", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz", "integrity": "sha512-LnIIdGWIKdw7zwckqx+eGjcS8/cl8D74A3BpJbGjKTFFNJSMrjN4bIh22HY1AlkUbeLG6X6OZj56BDvWD+OeFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEHPie4+rvCfSyKziRdXkeQOFvSn/vtVePdKXkbddwg/AiEA+szhNk3RzMDJFU1Ibs2pMATpMa+OrR4gF3oPBFuwoWE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-template": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.8", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "77451a4c5a4744f55586e5f147756f29b3b4d851", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.8.tgz", "integrity": "sha512-W7vNdUoEV3sS3SE4Fp8nQueIzyoT+EmJZDSiHoUHNlEvJmuk9sVrPnCMQMgFNBsvn597yb1uBU2Gk5QcUwbjCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI+39xueITvXtjWgYxh8axPFXNV71H3owEvFN9BFw0MAIgQhlw5MLQUdViCwpSBobx8NtPxlTRWpSAsKq1TEZ2WzU="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.8", "babel-template": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.9", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "427375507363280ebffdd32da42c3044ddd067a9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.9.tgz", "integrity": "sha512-03Gs8NpE7cOI/aCUjcjg6UywKeCUymw2XGzTo5D3GS5xZ1oqDcHz7mjyKajwYkcbNdjUGHhNwbCs8iW3YtQ/5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDm+q4mDigg2jOnKQEhT5fCjEoMkUdGeu/rQLjs1Ua27wIhAM0dLKYD90TJGh27nbwwDvoxAQylZdkORfKwHRUh8QCd"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.10", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "3ebc1d2f0a411e231c15f64777843c03eacd05cb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.10.tgz", "integrity": "sha512-eM33lqDAUUiDmASCIWOTvcWmLMTWP+qdLXsiiZIYbr1GjP8GhSDa3te2/9qpOl4ooJo3EnJF7OiOsLJvoiqGuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcDJRv8TqX9zSIjVbw1N6n7aW4NmjzBMq6hAZdpPoeiAiEA2Zr6uLBm2zGPm8CSRyhum0HqDJ6+/tN3Z5gU5S+Lt1w="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.11", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-xs10VfBadnkWa1okW4lv+DvxJ7zZg2Y0wCR9L9BcbqzK8dmBbi+75/C1B+PYuOXOKkMF4sYheZBh5AJiERKS/Q==", "shasum": "a236ae74f090da49d104fcf1f52113094e8e1a43", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG7Y1tpr/0nkLkXZAZ88KALU4+Hc6JJOLarEK9Ts3ZS6AiBOxzH1vqxDexiGOIxidgiFfxWak6aJRsKSGIrD4d7y3w=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.11", "babel-template": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.12", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-OvmyuLDQCLy2pHw7SmPIrt+1z11oHfAGIvCPJe+nBBjcwrJWA05w1RMzGouHwHwhpuNkyUsXo0biYYKxT+yj3w==", "shasum": "7ae180ff063d53632c6b2f7cf8fd1a7cadb4da67", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4ryDDzZAON2iSY+10c16qHfdQMh3qP0ADp0m7hB81bwIhAOByDtVfbPo6SKG1E8WF7RIVsJoG3k0+j3FJP3GuFOlA"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.14", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "705390c8b3e66ac47bb97947ae47f3347c152653", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.14.tgz", "integrity": "sha512-ahtBIXyJz61wG1p59roxw56hA0GipEFCKPrh4dP0CUIK/bQHjWmOPe2Vka0IXvVEF2zq2mfY1EmtM6drUqB3qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7wlB47G0lONU8Trm3g88CgOBUwETJKf1uDUyJAb/sRwIgB1t7sFesGgXJrRQKYL2FLoMiiD9vXhiYI/k9Gq7MCBA="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.15", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "7dd1fd4a6c0fb75b2892b68f1c3a4d0a5c918842", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.15.tgz", "integrity": "sha512-kZIrkuo2JAoujPIBwJhQG6pYwgU8MEgqOIgAcEY6QBzObuPccZOkrVN536Xgoy1WjS53zLpFyD8uzPj6Q8sksw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyNTISoLd7+u1BnIG7cHGMacU9EMgJlM2l16rWpyznEwIhAMsPuOUHIVTI0G3RatVGZtP1KZ7suVHcfZFKqrufBp0O"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.16", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "12ed969ec84354b9aa75380e1b6c2c42d2f70617", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.16.tgz", "integrity": "sha512-m1FMfjm5EIjD+NKwVDyCkera9IrsIReaZUMeBqK+wkQBgx7YAhskxYL0HMnqhnAOmdfOLOKSWQUMq32yZSkmmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfZcekmHETVMarrumuey3t8sSu31swJ8+5WXJQ6nOFsAiEAgu+rVf9tNCQ5BE2SSHQeGSP/h8Ky/XoNUmKIq32k7fE="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.17", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"shasum": "3fa4857d48bdd579c307e4de667d735e9e40e9a4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.17.tgz", "integrity": "sha512-IDnnOJbGqwYffDTSgQXxlVQ05mdcxWQHzEh7SnvH5zlqI3S2/gdP2aXewfRq6LYH5mMbB9z589gZh3h+ip+n8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7chvG8sBH1ofKQnhZZ/h8mB9xwizkDr8hYxhvh5hpnAIgAdupEgmOtAds6khEw4McAr4kuUo40jvOr+OM/LaMQS0="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.18", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-MyaqpvLZiHDKvnq5nOAWM6Vjen8vnrP9sMp8Ue1Y0XwRwjEgHtWEcsESoSUNYumlpN6cH5Pvn5jampCo7j724w==", "shasum": "b261a4a95e97bfccd781a57fe06cffc6a765bcaf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN4hYj3h6uDBimAqf9h5GgM5jj1lFnyktbcGlZ8p6CwwIgT1NbeXooiBjiS+48++M+0pHORJxm4hmhIsNaHLplFZg="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.19", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-vFrFDtXn3iyHysYwXpaNJv3xV4YSWT5/LAxZTOpJHisZTEyueNe7akIrsZdWR0S3PnVBMOZVpWS2uD3i9Fyv2Q==", "shasum": "db7dfecea5956e7f6766c7ef6a743c855c618af0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGq+cKd+sbsj2T1aUg9Y43MOjjCSk5qCQQTQ7V/sRHslAiAj387/4i8UODgyWDWDiQvKv6SzBZY4uiSRcPr935ofHA=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-alpha.20", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-GX9P+S/jsRFn9tHyLAwzbLqkuIf1QWvNMs1jS6NXlL2w6p3yEmvIi1d/hzHYtp1vAVHU6wEeceLD3uxaArtkYA==", "shasum": "b9ef5a25ad7011a7c63080cbe3438224d43b2dca", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGnj+L8+t+WbfVSq0xhgUPyU1D5YBnl9JI9koUdCxGynAiEAjAIEWEJpX6j8Zv1UtZzHhM8YAbzSxISHqtETyOND76w="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-beta.0", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-DoxBHobgA1v27zh+xP6QjBYxWLxPbVFp/h3BuIfLPAITVb+HYnBnvzjX+JO/tqWAiEFFqW21uolBHBbzPBPnOw==", "shasum": "10c92f743533175f8b992b5db2ffe5feb78589a6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqA5dTkaQ5Y+9U+dilyTVNQ4/CQgTPgHXKpCu78a4+wAIhAOFVoJqPK8dPb23lNdH6FNN2fd0e8m+oj1NnBatIMN5i"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-beta.1", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-jJXRGqQBSptM0rC+p9unvHyjRs/FUcZU8Xik7j0XoNKV3uroXkqaB+eSYRwRBBloFIwlf32AkQDu5v5SHt1h6Q==", "shasum": "a51571809e00daf71e8f26ac34fe93fcc405285b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZpNzhXP3NeeEvaXgSVm4kfJVWYF3QON88Qd+ioObDBgIhAONyJzyrY4VX5OJPraI53RyV27sFwL+wX6eg5TQhNCjo"}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-beta.2", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-mW+wa+/3W1OGlokKBKJs0EGoMxS0/giVJr1HECQOwWTqJbp3w/VJ2noomWl5RK8EM1YLiRTdqXBjEGiuTSnA7Q==", "shasum": "e05baf478f3c1e9b28eb31eb5c34fec8f3a3d7c8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3DqU6+sT330F2HJxUhk5b0CNok/mQQLeyNbGq1YiM/AiAZBry6SJlICmMG8AKiu4Vbc52ema1iJ7XzJBBbYzr0xQ=="}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-es2015-modules-amd", "version": "7.0.0-beta.3", "description": "This plugin transforms ES2015 modules to AMD", "dist": {"integrity": "sha512-Fm1mLWiaDy4457O880czMMpu4IiYOdjr1Hy8gz39BvV722QPo5Y2mITo6vO4oNYtaBIc3L0XlwBWavXEDe8iRA==", "shasum": "ae2e560f707bde99fb9c919380cab16e5a968948", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0C9YxwyRSaRm1LSnwn3q7vbTdqdIUa9L3j8VCfEq8gwIhAKtx/FMhv20vzrDTZQDpYIj+LxnFMjs1PUlMTep2aP30"}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:20.408Z", "time": {"modified": "2022-06-13T04:04:20.408Z", "created": "2015-10-29T18:14:33.012Z", "6.0.2": "2015-10-29T18:14:33.012Z", "6.0.14": "2015-10-30T23:37:48.459Z", "6.0.15": "2015-11-01T22:10:11.411Z", "6.1.4": "2015-11-11T10:23:43.285Z", "6.1.5": "2015-11-12T07:00:10.221Z", "6.1.17": "2015-11-12T21:42:14.025Z", "6.1.18": "2015-11-12T21:50:04.701Z", "6.2.4": "2015-11-25T03:14:27.277Z", "6.3.13": "2015-12-04T11:59:15.905Z", "6.4.0": "2016-01-06T20:34:48.010Z", "6.4.3": "2016-01-14T05:56:31.049Z", "6.5.0": "2016-02-07T00:07:37.907Z", "6.5.0-1": "2016-02-07T02:40:46.192Z", "6.6.0": "2016-02-29T21:12:47.452Z", "6.6.5": "2016-03-04T23:16:56.223Z", "6.8.0": "2016-05-02T23:44:45.156Z", "6.18.0": "2016-10-24T21:19:04.479Z", "6.22.0": "2017-01-20T00:33:56.634Z", "7.0.0-alpha.1": "2017-03-02T21:05:57.962Z", "6.24.0": "2017-03-13T02:18:13.429Z", "7.0.0-alpha.3": "2017-03-23T19:49:53.922Z", "7.0.0-alpha.7": "2017-04-05T21:14:28.796Z", "6.24.1": "2017-04-07T15:19:34.489Z", "7.0.0-alpha.8": "2017-04-17T19:13:22.024Z", "7.0.0-alpha.9": "2017-04-18T14:42:28.097Z", "7.0.0-alpha.10": "2017-05-25T19:17:51.400Z", "7.0.0-alpha.11": "2017-05-31T20:43:59.578Z", "7.0.0-alpha.12": "2017-05-31T21:12:14.541Z", "7.0.0-alpha.14": "2017-07-12T02:54:09.869Z", "7.0.0-alpha.15": "2017-07-12T03:36:27.213Z", "7.0.0-alpha.16": "2017-07-25T21:18:20.196Z", "7.0.0-alpha.17": "2017-07-26T12:39:52.125Z", "7.0.0-alpha.18": "2017-08-03T22:21:26.016Z", "7.0.0-alpha.19": "2017-08-07T22:22:08.372Z", "7.0.0-alpha.20": "2017-08-30T19:04:24.004Z", "7.0.0-beta.0": "2017-09-12T03:02:52.994Z", "7.0.0-beta.1": "2017-09-19T20:24:45.388Z", "7.0.0-beta.2": "2017-09-26T15:15:57.134Z", "7.0.0-beta.3": "2017-10-15T13:12:23.128Z"}}