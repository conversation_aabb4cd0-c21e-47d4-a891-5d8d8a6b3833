"""
Agent调用工具
用于统一规划Agent调用其他专业Agent
"""
import asyncio
import json
from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from .shared_memory import get_shared_memory, get_agent_storage

class AgentCallerTool(Toolkit):
    """Agent调用工具 - 允许统一规划Agent调用其他专业Agent"""
    
    def __init__(self, **kwargs):
        # 初始化工具列表
        tools = [self.call_agent]
        
        # 调用父类初始化
        super().__init__(name="agent_caller", tools=tools, **kwargs)
        
        # 可用的Agent映射
        self.available_agents = {
            "xiaohongshu_copywriting_agent": {
                "name": "小红书文案专家",
                "description": "专门创作小红书平台的种草文案和内容",
                "module": "xiaohongshu_copywriting_agent"
            },
            "week_report_agent": {
                "name": "周报专家", 
                "description": "生成工作周报和周总结",
                "module": "week_report_agent"
            },
            "daily_report_agent": {
                "name": "日报专家",
                "description": "生成工作日报和日总结", 
                "module": "daily_report_agent"
            },
            "month_report_agent": {
                "name": "月报专家",
                "description": "生成工作月报和月总结",
                "module": "month_report_agent"
            },
            "schedule_agent": {
                "name": "日程安排专家",
                "description": "制定和管理日程安排",
                "module": "schedule_agent"
            },
            "composition_agent": {
                "name": "作文写作专家", 
                "description": "创作各类作文和文学作品",
                "module": "composition_agent"
            },
            "official_document_writing_agent": {
                "name": "公文写作专家",
                "description": "撰写正式公文和官方文档",
                "module": "official_document_writing_agent"
            },
            "new_media_copywriting_agent": {
                "name": "新媒体文案专家",
                "description": "创作新媒体平台的营销文案",
                "module": "new_media_copywriting_agent"
            },
            "xiaohongshu_creation_agent": {
                "name": "小红书创作专家",
                "description": "小红书内容创作和策划",
                "module": "xiaohongshu_creation_agent"
            }
        }
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取工具参数定义"""
        return {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string",
                    "description": f"要调用的Agent ID，可选值：{', '.join(self.available_agents.keys())}",
                    "enum": list(self.available_agents.keys())
                },
                "task": {
                    "type": "string", 
                    "description": "要交给Agent完成的具体任务描述"
                },
                "context": {
                    "type": "string",
                    "description": "任务的上下文信息和要求（可选）",
                    "default": ""
                }
            },
            "required": ["agent_id", "task"]
        }
    
    def call_agent(self, agent_id: str, task: str, context: str = "") -> str:
        """执行Agent调用
        
        Args:
            agent_id (str): 要调用的Agent ID
            task (str): 要交给Agent完成的具体任务描述
            context (str): 任务的上下文信息和要求（可选）
            
        Returns:
            str: Agent执行结果
        """
        try:
            # 检查Agent是否可用
            if agent_id not in self.available_agents:
                return f"❌ 错误：未找到Agent '{agent_id}'。可用的Agent：{', '.join(self.available_agents.keys())}"
            
            agent_info = self.available_agents[agent_id]
            
            # 动态导入并创建Agent
            agent_instance = self._create_agent_instance(agent_id)
            if not agent_instance:
                return f"❌ 错误：无法创建Agent '{agent_id}'"
            
            # 构建完整的任务描述
            full_task = task
            if context:
                full_task = f"任务：{task}\n\n上下文：{context}"
            
            # 调用Agent执行任务
            print(f"🔄 正在调用 {agent_info['name']} 执行任务...")
            print(f"🔍 调试信息：Agent实例类型 = {type(agent_instance)}")
            print(f"🔍 调试信息：任务内容 = {full_task}")
            
            # 尝试不同的调用方法
            result = None
            try:
                # 检查Agent实例的可用方法
                available_methods = [method for method in dir(agent_instance) if not method.startswith('_')]
                print(f"🔍 调试信息：Agent可用方法 = {available_methods}")
                
                # 尝试多种可能的执行方法
                if hasattr(agent_instance, 'run'):
                    # 使用run方法获取返回结果
                    run_response = agent_instance.run(full_task)
                    print(f"🔍 调试信息：run方法返回结果类型 = {type(run_response)}")
                    
                    # 处理generator类型的返回结果
                    if hasattr(run_response, '__iter__'):
                        # 如果是generator，收集所有内容
                        result_parts = []
                        try:
                            for chunk in run_response:
                                if hasattr(chunk, 'content') and chunk.content:
                                    result_parts.append(chunk.content)
                                elif hasattr(chunk, 'response') and chunk.response:
                                    result_parts.append(chunk.response)
                                else:
                                    result_parts.append(str(chunk))
                            result = ''.join(result_parts)
                        except Exception as gen_error:
                            print(f"🔍 Generator处理错误：{gen_error}")
                            result = f"Agent执行过程中出现错误：{gen_error}"
                    elif hasattr(run_response, 'content') and run_response.content:
                        result = run_response.content
                    elif hasattr(run_response, 'response') and run_response.response:
                        result = run_response.response
                    else:
                        result = str(run_response)
                    print(f"🔍 调试信息：最终提取的结果长度 = {len(str(result))}")
                elif hasattr(agent_instance, 'print_response'):
                    # print_response方法返回None，不适合获取结果
                    result = "Agent执行完成，但print_response方法不返回内容"
                elif hasattr(agent_instance, 'chat'):
                    result = agent_instance.chat(full_task)
                    print(f"🔍 调试信息：chat方法返回结果 = {result}")
                elif hasattr(agent_instance, 'respond'):
                    result = agent_instance.respond(full_task)
                    print(f"🔍 调试信息：respond方法返回结果 = {result}")
                else:
                    return f"❌ 错误：Agent '{agent_id}' 没有可用的执行方法。可用方法：{available_methods}"
            except Exception as exec_error:
                print(f"❌ Agent执行错误：{str(exec_error)}")
                return f"❌ Agent执行错误：{str(exec_error)}"
            
            # 检查结果
            if result is None:
                return f"❌ 错误：Agent '{agent_id}' 返回了空结果"
            
            # 格式化返回结果
            formatted_result = f"""
## 🎯 任务执行结果

**执行Agent**: {agent_info['name']} ({agent_id})
**任务描述**: {task}

### 📋 执行结果

{result}

---
*由统一规划助手协调执行*
"""
            
            return formatted_result
            
        except Exception as e:
            error_msg = f"❌ 调用Agent '{agent_id}' 时发生错误：{str(e)}"
            print(error_msg)
            return error_msg
    
    def _create_agent_instance(self, agent_id: str) -> Optional[Agent]:
        """创建Agent实例"""
        try:
            # 动态导入Agent模块
            if agent_id == "xiaohongshu_copywriting_agent":
                from . import xiaohongshu_copywriting_agent
                return xiaohongshu_copywriting_agent.create_agent()
            elif agent_id == "week_report_agent":
                from . import week_report_agent
                return week_report_agent.create_agent()
            elif agent_id == "daily_report_agent":
                from . import daily_report_agent
                return daily_report_agent.create_agent()
            elif agent_id == "month_report_agent":
                from . import month_report_agent
                return month_report_agent.create_agent()
            elif agent_id == "schedule_agent":
                from . import schedule_agent
                return schedule_agent.create_agent()
            elif agent_id == "composition_agent":
                from . import composition_agent
                return composition_agent.create_agent()
            elif agent_id == "official_document_writing_agent":
                from . import official_document_writing_agent
                return official_document_writing_agent.create_agent()
            elif agent_id == "new_media_copywriting_agent":
                from . import new_media_copywriting_agent
                return new_media_copywriting_agent.create_agent()
            elif agent_id == "xiaohongshu_creation_agent":
                from . import xiaohongshu_creation_agent
                return xiaohongshu_creation_agent.create_agent()
            else:
                return None
                
        except Exception as e:
            print(f"❌ 创建Agent实例失败：{str(e)}")
            return None
    
    def get_agent_list(self) -> str:
        """获取可用Agent列表"""
        agent_list = []
        for agent_id, info in self.available_agents.items():
            agent_list.append(f"- **{agent_id}**: {info['name']} - {info['description']}")
        
        return "## 🤖 可用的专业Agent\n\n" + "\n".join(agent_list)