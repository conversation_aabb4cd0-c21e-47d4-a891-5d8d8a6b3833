{"name": "babel-plugin-transform-decorators", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-decorators", "version": "6.0.2", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "7041a4e97d3eacb3a9325bccf264da48a5299f02", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.0.2.tgz", "integrity": "sha512-fywBgyWuYaCS8I97KCNC6JVbDIw9bUmGqJmwvjTUG4fYIo6xEwurICss4xK2M2mBUjkO3fCTHRtitrLAICfn6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzdIn/nnBZNhoHODDhZiMnfNoVkNJCJRa300sqTHqt6AiB7FbxmPsHRHOc6Xyk/KPs4Cy22Xyprmfw4p1EXkTpqVw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.2", "babel-helper-define-map": "^6.0.2", "babel-plugin-syntax-decorators": "^6.0.2", "babel-helper-explode-class": "^6.0.2", "babel-template": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-decorators", "version": "6.0.14", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "2d65f881d672f21092a29e0985e9fb4c82872049", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.0.14.tgz", "integrity": "sha512-mIDv8MN3c5kAkGCjcMsgwhHoEStA5WJ1eyUZVLA8cE5+IqtG5wQW3DQ06AXz/Zu42cEMLmiU7woZmjOtl2fBpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESm/8q3X5gH7F3IuumnUMfrDaDUBXuSnu6J5zZV3LtXAiBjUkC7jN9/+AksqwmZcjtYPaBNNl38XTeFkxkbSoOkKw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.14", "babel-helper-define-map": "^6.0.14", "babel-plugin-syntax-decorators": "^6.0.14", "babel-helper-explode-class": "^6.0.14", "babel-template": "^6.0.14", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-decorators", "version": "6.1.4", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "ddf68067f47f7b433135eb86e43510072f361657", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.1.4.tgz", "integrity": "sha512-mN75wa2/sZNI5Dt5LUr9cAyx/A14FdwCHT1hOjstcf/hxH1GnYHfrUabXRBUYRP1punhCbMLb+iRmPgCwMrO3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiZzCYeK7YM4Gb3XvQUvhS5j8SIKw6caqy6QwiE6yv6wIhANxFrui59gTkheEbmDfoVipTMq/vSivouEhSXGiPHcUo"}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.4", "babel-helper-define-map": "^6.0.14", "babel-plugin-syntax-decorators": "^6.1.4", "babel-helper-explode-class": "^6.0.14", "babel-template": "^6.0.14", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-decorators", "version": "6.1.5", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "0422b6d79d1c5c794b1b4b153b51f98532f47bcf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.1.5.tgz", "integrity": "sha512-09nGcwSiD4DKMx6i5G1eiCvuu1NGVHInAVtocblV8wR/KBp5FUTYdXG0+bQqFzoCo9PLSfYwFzZ92wCL/gFPuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDkEtUgAlQpzgpUGPj5hefHQtrkHRhvDhtNfLkuzYMbgIhAN8Uk49rCgIRMISWob3ZzTSwPiV9ihGkIeSyiO6G+HZC"}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.5", "babel-helper-define-map": "^6.1.5", "babel-plugin-syntax-decorators": "^6.1.5", "babel-helper-explode-class": "^6.1.5", "babel-template": "^6.1.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-plugin-transform-decorators", "version": "6.1.10", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "2d5ed0ba59c1981865d52aa24b3d9926af754169", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.1.10.tgz", "integrity": "sha512-R8+h1EbxPRwm+3oenPPsv8nCLcuBiTAnM31s72kDBzsz7Jpi3oJdonE47OIo0hK/jBOdsu7aOajmaOU8fX/7Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRs+yRm2xfwxFP1lT0XqMtbb8YZUdiOnzSaf3bapKMbAiB+KB8qx0RmcS3bzb+w9qaDMooEcrzCuh1Q9IjCosA+NA=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.10", "babel-helper-define-map": "^6.1.10", "babel-plugin-syntax-decorators": "^6.1.10", "babel-helper-explode-class": "^6.1.10", "babel-template": "^6.1.10", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.10"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-decorators", "version": "6.1.17", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "704cb08921213a88c711aa92a744c26be994f606", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.1.17.tgz", "integrity": "sha512-hgnIOIpmgokUNO/+PSmncofBm4lIbrOfq3OhM6oxPu7xt3fAOCMNTBdqmF0hmlZ+zt+D09FxQjaBTaRSTZdHxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHgpzC0FGEEb/riPtAFHdvGxYJbEJanK5QiGtoOAUNtGAiEAp+qgAfoZYrMIZ7XAIdGH5cPAlELQpvTYQa0fvBEWU2M="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.17", "babel-helper-define-map": "^6.1.17", "babel-plugin-syntax-decorators": "^6.1.17", "babel-helper-explode-class": "^6.1.17", "babel-template": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-decorators", "version": "6.1.18", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "b86a2271779a2ae0f1c9d57298cf4e498309e930", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.1.18.tgz", "integrity": "sha512-tDzOszuZ2sZPCgCxOnGbhMfuSfY0Vzh7vuyY7GW5oriUliRU1+YiyHHE9Bw/PlgKjSJ6pvkc/ohk8J1BIaZVZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuv/LQ99ow2TKT6ZGueM29ZE8biFdqBpzl1DHDR6DnBwIgeltzBtyNVfZotI3MH2J1dTSRuWT18RLznoV3+zQp7d4="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.18", "babel-helper-define-map": "^6.1.18", "babel-plugin-syntax-decorators": "^6.1.18", "babel-helper-explode-class": "^6.1.18", "babel-template": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-decorators", "version": "6.2.4", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "0f95acffd8c39d3bfcb543546fad407307884e34", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.2.4.tgz", "integrity": "sha512-UA1Xq37WaMnYXDDWSm2lxZaJdvgcc8vmerei6rei5qsZUlj9QCZXfv9dQ4460cub3nuzzl1VRNc3pkQf1osIZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7w2yxak9WtxIhmpONoah8sH9HAZAJNj8GlxDitGVf8AiAXaxsX1OWLM0nb9GYE3OAqMJW4KKh44hJWJjdaClmaMw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.2.4", "babel-helper-define-map": "^6.2.4", "babel-plugin-syntax-decorators": "^6.2.4", "babel-helper-explode-class": "^6.2.4", "babel-template": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-decorators", "version": "6.3.13", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "e1f168f10271eb0cb5c13da661f06d4cc48aec60", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.3.13.tgz", "integrity": "sha512-fUk3hLxV4D1FNVJnh3T0L8hqH+NgSC+q1scXFW9YWLaNAzY+12cm0MBJiOzQTRz+kRhrO1YwywlpWqRyUnq+Ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHnFWvCrIFXget1VK6LDcvBMUZrp2Igc/0mEQWGPVtoDAiBesy0t5JnFIpNz/ZgGb5oXh7xkA2v9LaONpIDed/efyA=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.3.13", "babel-helper-define-map": "^6.3.13", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.3.13", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-decorators", "version": "6.4.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "2026022b8f9cd9d1cdebc21aab09a1cd2c151d82", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.4.0.tgz", "integrity": "sha512-xUKo6u1H6t6PcwQuFFFX/2u6Yb7NswlcFU6KWwgp3iF0Fif/50ezG9mxg7MzofgFrG4FIJwwV1EdbuDLg3nH1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgFVfpW+bMAreA5hcZ+w/r0zcRUw6jpUdIdjz6wS8cxAiEA/2OKOYFXlG+IpjvSnHnWQvtZXYwCR2X46K5TESlIDn0="}]}, "directories": {}, "dependencies": {"babel-types": "^6.4.0", "babel-helper-define-map": "^6.3.13", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.3.13", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-decorators", "version": "6.5.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "614871a98478dc13c19c1205aa5ccb4bab45da4a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.5.0.tgz", "integrity": "sha512-OYzzRmEc1T8rhJyowPqCjIf5q9TSvLMjqNDy932UJs+wC3Yapqkollwf5vj1cZz5XySRMwLg+JwRfkSFQxwKtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNtbTzoBnu9SsTF02+Z1qEV0oVtZC4XJTCeFvDnp/a/gIgcP9Jhy50JDpNqc8XShFexWig/ugWxQ7YPo7hlqdUY1g="}]}, "directories": {}, "dependencies": {"babel-types": "^6.4.0", "babel-helper-define-map": "^6.3.13", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.3.13", "babel-template": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-decorators", "version": "6.5.0-1", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "82139a199abedf9d50eec0a8a73883e97f63dfc1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.5.0-1.tgz", "integrity": "sha512-qZOXOqOF+VIry33Xu5QeoXcxw2+D6PklR5+fxOf9ZKruVs1Z5byydcxyjQ4hH5JPyVFYKE54flDlzRj4KROSIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHzc2o50Zhwuo9Esio7BI1PEhL44wD6suhvudiAkkQDAIgC1WLtqx6VhHF+XpvFugtEs8AHzlRvnGl1vZ3bXNpHqA="}]}, "directories": {}, "dependencies": {"babel-types": "^6.5.0-1", "babel-helper-define-map": "^6.5.0-1", "babel-plugin-syntax-decorators": "^6.5.0-1", "babel-helper-explode-class": "^6.5.0-1", "babel-template": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-decorators", "version": "6.6.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "3ab5c2e71f99ee9e0c9ee36ad40dcf3b7a641503", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.6.0.tgz", "integrity": "sha512-zo2FQthiLZOQjEsrwbB7ZVE87BHoArS9sybblQS25vAsC4bA56NyPseglwwRbPpzqiB3IzKONILFoeUvEWH0Eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICgbLMah8dk2o45EZiIAodFVzt0FX+UkST0mqYZTZtNtAiB9drFWNPwbPZoK7ARReU+y7pwIn5Dwnx9b3c9AtHDpXQ=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.0", "babel-helper-define-map": "^6.6.0", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.6.0", "babel-template": "^6.6.0", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-plugin-transform-decorators", "version": "6.6.4", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "1eaf16790f3bd2a39c230b7eeb874bca4338c1ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.6.4.tgz", "integrity": "sha512-o0zzN6DPc3lw7tOhoZfoRGUBX/7TF8+SDaUC60mcrp7k5tqfgtIVocz8o/rIv1w7QmGvKwNK4NO/Pae0BT5HUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCa4zRKYBOjuL+4iYRPjLKSug7r6KcL5scS4QQ7li1NQIgUYXwPusfKLnNYjdzdG0ljw7p1pBrcKZ/gDBH18CeK6U="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.4", "babel-helper-define-map": "^6.6.4", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.6.4", "babel-template": "^6.6.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-decorators", "version": "6.6.5", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "ab267da258a4ecab61da8d80cde9d1a5aed075ab", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.6.5.tgz", "integrity": "sha512-jeYq7XykzIXCgRPhKZujPW9WqC6IimP1xJX55pkkyH0NmpSXNYJtJXl+F/vZqd1SBXFlpQfOH/JpvJZXLYFTAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICpeVzGWvcaT0UQBQ1NUxJ2m1a/70uPsxLdwm6fUziLkAiEA5+7/S4w3lMQeewrtyEf0c5DCSifp+ARqo8Y61XNK0ps="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.5", "babel-helper-define-map": "^6.6.5", "babel-plugin-syntax-decorators": "^6.3.13", "babel-helper-explode-class": "^6.6.5", "babel-template": "^6.6.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-decorators", "version": "6.8.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "b91a460458f8e7e9a9c0219281af26658fd90bb1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.8.0.tgz", "integrity": "sha512-9d80DSVVOrr9B+kswaMSsm6b0yVp602hB26NrGEL6hnLIQRXesAdKSsmbeKL+lPCCruUNCKGERahDkhzM2Lh2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAunlwUGM4sACYBI2YIIb1Nv12YwLN71fZo5L3svDRIvAiBC5HWEy8/pYtBZFRryebHWLfJdnZ/JCUbNFd+9E/1ByQ=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.8.0", "babel-helper-define-map": "^6.8.0", "babel-plugin-syntax-decorators": "^6.8.0", "babel-helper-explode-class": "^6.8.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.13.0": {"name": "babel-plugin-transform-decorators", "version": "6.13.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "82d65c1470ae83e2d13eebecb0a1c2476d62da9d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.13.0.tgz", "integrity": "sha512-qHXS/u/tRXs4JqKoRM8VPQ5jL508bDhkEJOSd/8dYVEb3bOXFAWw0iv58eqxU5M7aeLejmgpM/Mu7LOQ5+uAvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBVQXomo9djsNEuk1m0gBhlWUpa0baKFFREmQmM+cyneAiEAi1Mga63aIcJCWDR+QogDbhD2ieAi5J5GayPuEPqFv4k="}]}, "directories": {}, "dependencies": {"babel-types": "^6.13.0", "babel-helper-define-map": "^6.8.0", "babel-plugin-syntax-decorators": "^6.13.0", "babel-helper-explode-class": "^6.8.0", "babel-template": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-decorators", "version": "6.22.0", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "c03635b27a23b23b7224f49232c237a73988d27c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.22.0.tgz", "integrity": "sha512-HeuZYYB3RVGJIOFBQSkmN7LUZ7z98+SCVrHY8j8fhAyiWftkH0TYFlsttUaSp7Cz1dTrfvfbkuyJo7Bte3X4gA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFRIHTb7BDkzsxYONI29HFEdBEtlQSmmrQcWTIyZ8v9AiA5EW2jUttpo/IF/VEUfDyjz/eQXwdvlgf/2IzO4aLMuw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.22.0", "babel-plugin-syntax-decorators": "^6.13.0", "babel-helper-explode-class": "^6.22.0", "babel-template": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.1", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "b0a89f4dd0e10e93ec77b298c2dd68c89d95b113", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.1.tgz", "integrity": "sha512-0R+HrrOVjHzpoTL4eoU7e+ZMR8V1HrC00h32wJ5fjS6XrXZs60bxlMUpbXfJxIUyL6Nimrk8kadgQjwXttmJZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtYfcauDmOKwqOEuWH04Lr9nKO96GXwBbLOinTLWgPsQIgAk7GocBRzm11i3xOhWR5lISE2wr6iJD+ZA2UDfokVaI="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.1", "babel-runtime": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.3", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "a0d3586d399d59d8b43d14c8f5b87efe9f5d0ae2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.3.tgz", "integrity": "sha512-V9Qo+lkuK48RmYxVe04M7Qmj+ui+NY9537GRrscUt8mIJf/O7lZWYYNwgouWUbN0O1tnTP8ZHAs+HVeOdNps8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXBJFs8JPfFYMZFTlfR+BDTbqPaK9KdTX+T90t7SHWVAiAVVKczZYjR1LwC+6l9o+w2KC8rVb0SPc9EQUb56bulnA=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.3", "babel-runtime": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.7", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "baf4136bd6ac25534cfa5291dba397fa224c6455", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.7.tgz", "integrity": "sha512-oSODQv39RVdb6phIxIFP84guZSz6JSg+WViE2Wvf7kgF5PjJ5s04yYopKupNmSOv5OvyNQJ7DEiiWLCm8bc7Vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+ri1h0dlDPuVC9oM1Qb+bVfPkGcyIXH7pclL6oeBrPgIgCqOkITSyo7kDKwW2DiUdOlPKTraOQYlKfg9K3Xg1yRY="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-decorators", "version": "6.24.1", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "788013d8f8c6b5222bdf7b344390dfd77569e24d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.24.1.tgz", "integrity": "sha512-skQ2CImwDkCHu0mkWvCOlBCpBIHW4/49IZWVwV4A/EnWjL9bB6UBvLyMNe3Td5XDStSZNhe69j4bfEW8dvUbew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBIwnDLDd0rR8wkD+xmXPsCUyp8BVCA5IC0E76V5p4ssAiAMItxQ8bo/dC2xuVKdzeHzM0XDoixNzyvc2speVxl7Gw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.24.1", "babel-plugin-syntax-decorators": "^6.13.0", "babel-helper-explode-class": "^6.24.1", "babel-template": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.8", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "c1761f70e20fc832ff6428709a7de1a5f68264ce", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.8.tgz", "integrity": "sha512-FM780KOJWqRLxQVVnGl3B/o7/x28yjiC3jkQdIsyx2JeYgKh1JeH9mNye7Sap/7lL7a+8bstRWqdcEtUfD0pOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh3LYni4R5xMuOIQNdPlYh1MVp3vyppIkQczqApgUjhwIgMem5t88DSUFWIfnUWCD93gouzzoU9bkDQrwM6m/VOoA="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.9", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "848a3d6cc58360fbf3bb27fe6ce56438b4b7fb2b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.9.tgz", "integrity": "sha512-eFG0lhwjGiUWsvw8+9kNP9SxRrzCe9s8oUDJDlyenHxqdlYoW15N58zkCzVnymy94wyox4jsd3Ae88A1GrFtyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDj7+xNygPcdpRlPgvniyfxaUSOiybXdIYSyWrH7jmvHAiA66G+kQ8DONSKjoYKgz/LKqOlvQZ91C6xVv0vpDExgUQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.10", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "46bbdcf014cbacc2e55c438e6baaf8244b5b2e5b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.10.tgz", "integrity": "sha512-cDy1Hg37v1qNVju7lPMf2ZblZ2v1Npp2fzhihIOsuYsqZXZvJdqxud+3fl2zpTRmPBlzJauhqd0+hSdToS6Uug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6bT1LzUepWUXgn06mSS/HgwIVGi/MM4oTkWkHW3LwdwIhAP6a82Nw/HutSpNHhOBWKq/XveI9xS+7TijcZedYplYx"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.11", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-0jNFM1qtfaa93cW6OnuS8+Mt0CzsA/m1Rf9947Ih5UzRRLY3KQCRnXXi6Bp5rTjpKaeOx0AQ0KzFLihCuz+0IA==", "shasum": "aeee539cf730e0ea0e5b1fedc2fafa3d5c2b8d63", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuSO1hYPb5Y928DmQGkidYAx6jxDnmZJab8vyBNASlGAIhAOR0arKwYd/8bxKwlHbsgxFl9holr/UV54ORNbsD0+6i"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.12", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-FMo7qibov/NWLLwWuR/AJ46wbb+TOStl0m+U8IB+3N7t2k3VzHFuQaHg0Au5cPv7m7lRPPpJm2wKstYZ2cNkew==", "shasum": "dac49553e91df8572d0454dfb666f6aed3d4f377", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGRfZ4XxDR6ribMcfO45nsNubGnuuLeipJ+mxX29sXAIAiEAqFj0qzlvxU1swr20Xf7plONv+kQNsgUREPPGtV0KiX0="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.14", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "4c5f414cef0609d91f23c2300ddf7e055319b66d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.14.tgz", "integrity": "sha512-9IlJjFawdvUVGV9Y5XizOhldQp0W22zoVVI00rR2uTfdO/SIUfjKiwos0YpJMC5z+bjSys9PTMRorh3dQws10g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmZxEDaabJMVRo3777BCP3pBROSpJrXDD1TTZCJVs//QIgKI19iKfVpUkkQAke21RxnaTczKeSJWtL5KbuuGn4/98="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.15", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "88d726436882a782ff13ac9dd5ca6af6918c4e96", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.15.tgz", "integrity": "sha512-gALy8bIoJII/IYJszn5L7M/DwljwFaxtuykAvfNLC3BkoOahyNs6jWk0OmdiOpqoLF+XxNTPyJmMc7IjAvDxNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCC6OBTyMzRkpEi0ffPFJgYmHj0dcxnf4L6TDb0xP4zLwIhAJzaoDojkX32nlNYvd2cLf8Tj4JDSYl6lA8lXKh4ELZD"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.16", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "802971154344105acdbd49a401ec5176dc9e1566", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.16.tgz", "integrity": "sha512-zmumtdxZQHOuSoQxuE/+S8atGqvhxxfrhq29dsmFDJa4zZgIEVRSIunuklADJYyjWvCoBVM4fjSaBQKvXck0mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8bDpVlxtiTqU7Gj7bqwUcj06GX5XBt1WlxgHgK0fgLwIhAOsquRWZyW1ac4iyI734FzN+SAddNnlovgrYddGTua82"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.17", "description": "Compile class and object decorators to ES5", "dist": {"shasum": "0e2cd329c5bfecf059aa4061e945ea22a7bc9c5b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.17.tgz", "integrity": "sha512-XhjvOUwxfUApqI6W1mswOPVey8c8n3rvrtJ8R/hd+9HonxWlfYhvIb2mlrksC2jY+mw7hObgUsFUwc3IoMyh3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCX0UQiqaap8qa3/yDYMXx1GFhck6GWVOMvmoHEPsm7bgIhAOs9gbfclvk7LDa7SV6FtwBVi6lLrx9Wvxaq2kFwtpjh"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.18", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-Gh0Z1R7v7UYvsR+iiaVdHFyxz0+u9qjHsHTG2ZpOdgUgxC/HBUzR9h17Drhj9m8yl9/UWtI4IkCw2GeQ2Tbp5Q==", "shasum": "2546cb48db4f7a040a2c237159fc29b41ba2c8d7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7BF/OKVMODvqxVZzLeKALqKxBke85n5C+7ATQWTOpPAIhAIKEQfBj9QwDOnFpY9hXra+dIl1zGvVaSyOaeYlFd+fH"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.19", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-gJY2KH5Szxmmchvu+0gIVQ2AGXchI+9KRZeIzjUhkFxDdaa96rVS+gFiYdQnMeK+qxI5Q7x+k7jtfmNO4uko0g==", "shasum": "9bf13c2b99dceaa637e8ddbe6755612e27dbf66e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXzBSZfOus0q79J9abheucHENvbzlZrVTbcBLN6F/V3AiBMquOl5gIoTZUCRpKHX+NvjeaoUUvSS1wDWb+mnW+JlA=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-alpha.20", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-FbZFvBYoTWouEAheGKut5pXlAqBYbk2NIK/iWtoDmedTEWGWmlX769HWwO1K4/CyrLQlWtSXdKwT8kslEfdOQQ==", "shasum": "a79a9e76b0895f526ca215c6c268dcb9ddc514ab", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAT7dp2z7LKkcXghxylJF6Q26qzeHgExU1/e3P5k8Ar6AiEAp0Z3DbdPW6068lq/30BvORLAVBBNUkeEkgpsWDTrk8c="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-beta.0", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-hMijFq9ka0/RoM4X4g2goQd4yGIwEezm6aMczYdk6Zt2GUIPidnzBdqvz7bNmbC1RIF4//bUU6n4xQqIx1ZSLA==", "shasum": "1085771d6cd2b040a432280f35cd674d010d857c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbk2K4l2o6TzMNUPiopEljL4/biGfvS+xbXoAAaKX0bQIhALxmMtt0AVvNl31cjVul5iWCcVJ7YGziETIk7We6+Ofv"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-beta.1", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-0mhEEEZJs9Lh+T6b3b9Xk9bOCouCNGmTZmqGFYD7QY2QvdbKRZXJTh+iLRcXHqONBQAcyri/J32XAh4BRloQtg==", "shasum": "8c2b0d50e95efbdbc6ee9e1cc3506483b6590d6f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH4ILutXq6H+zXyxkG89DXsQfb+UPZd+AX7nHPipUbqYAiEA+BrC9TOuEHnYbM6ysNMraNvuhd301Cz+lxV5h3PFCuk="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-beta.2", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-8gEqc8GFT8vnJ9hPPRxBp+kjS/FnWuPWI1bsNGPlNtlavc9WIDffElXCy/TICPt+7FUwKNQpwTBPsUAzwk6t8Q==", "shasum": "b4645276266a0965cf552500bcc1c2c2b205940d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPA3uEh0kp7vxZEIJP1vZj/LH7erzbF/q+FCE1M9cMdQIgYiE3Dtsi9AF/a5iMt3gfCc0bSo1noBi+TzU8HN7MWI8="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-decorators", "version": "7.0.0-beta.3", "description": "Compile class and object decorators to ES5", "dist": {"integrity": "sha512-L7YZwBi5ortn+ehGDn+sqyw0pT6bLeDh/S41wv2E0lMXCj5mzZu58vE0p6+SCaV+BAmBje+fGe7cp2l1M0HXuQ==", "shasum": "a4f7b348e174d665b1d474b1261f95c794319c0f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC12VFnqvLoMdfir5ab+8a4c70eQhCtUzLBYzKIKSPjZAiEArBbJbf6znXkkP1rRXyUaaAZc/fjotK7o6txGTwyReTo="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-decorators": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:12.956Z", "time": {"modified": "2022-06-13T04:04:12.956Z", "created": "2015-10-29T18:12:50.605Z", "6.0.2": "2015-10-29T18:12:50.605Z", "6.0.14": "2015-10-30T23:36:22.549Z", "6.1.4": "2015-11-11T10:15:50.037Z", "6.1.5": "2015-11-12T06:55:09.117Z", "6.1.10": "2015-11-12T07:54:46.337Z", "6.1.17": "2015-11-12T21:41:54.829Z", "6.1.18": "2015-11-12T21:49:16.377Z", "6.2.4": "2015-11-25T03:14:07.069Z", "6.3.13": "2015-12-04T11:58:46.725Z", "6.4.0": "2016-01-06T20:34:36.194Z", "6.5.0": "2016-02-07T00:07:29.471Z", "6.5.0-1": "2016-02-07T02:40:23.180Z", "6.6.0": "2016-02-29T21:12:41.363Z", "6.6.4": "2016-03-02T21:29:40.414Z", "6.6.5": "2016-03-04T23:16:48.397Z", "6.8.0": "2016-05-02T23:44:33.456Z", "6.13.0": "2016-08-04T23:35:04.028Z", "6.22.0": "2017-01-20T00:33:48.103Z", "7.0.0-alpha.1": "2017-03-02T21:05:47.363Z", "7.0.0-alpha.3": "2017-03-23T19:49:45.536Z", "7.0.0-alpha.7": "2017-04-05T21:14:18.524Z", "6.24.1": "2017-04-07T15:19:33.316Z", "7.0.0-alpha.8": "2017-04-17T19:13:11.368Z", "7.0.0-alpha.9": "2017-04-18T14:42:19.324Z", "7.0.0-alpha.10": "2017-05-25T19:17:43.936Z", "7.0.0-alpha.11": "2017-05-31T20:43:54.071Z", "7.0.0-alpha.12": "2017-05-31T21:12:10.201Z", "7.0.0-alpha.14": "2017-07-12T02:54:05.145Z", "7.0.0-alpha.15": "2017-07-12T03:36:20.022Z", "7.0.0-alpha.16": "2017-07-25T21:18:14.969Z", "7.0.0-alpha.17": "2017-07-26T12:39:47.076Z", "7.0.0-alpha.18": "2017-08-03T22:21:22.933Z", "7.0.0-alpha.19": "2017-08-07T22:22:04.562Z", "7.0.0-alpha.20": "2017-08-30T19:04:17.882Z", "7.0.0-beta.0": "2017-09-12T03:02:47.847Z", "7.0.0-beta.1": "2017-09-19T20:10:09.267Z", "7.0.0-beta.2": "2017-09-26T15:15:52.802Z", "7.0.0-beta.3": "2017-10-15T13:12:17.352Z"}}