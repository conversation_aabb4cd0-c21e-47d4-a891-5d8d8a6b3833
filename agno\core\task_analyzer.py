"""
任务分析器
理解用户需求并制定执行计划
"""
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TaskComplexity(Enum):
    SIMPLE = "simple"      # 单步骤任务，单个Agent可完成
    MEDIUM = "medium"      # 多步骤任务，需要Agent协作
    COMPLEX = "complex"    # 复杂任务，需要深度规划和多Agent协作

class TaskIntent(Enum):
    WRITING = "writing"           # 写作相关任务
    REPORTING = "reporting"       # 报告生成任务
    SCHEDULING = "scheduling"     # 日程管理任务
    ANALYSIS = "analysis"         # 数据分析任务
    CREATIVE = "creative"         # 创意内容任务
    DOCUMENT = "document"         # 文档处理任务
    GENERAL = "general"           # 通用对话任务

@dataclass
class TaskStep:
    id: str
    title: str
    description: str
    task_type: str
    agent_type: str              # 推荐的Agent类型
    dependencies: List[str]      # 依赖的其他步骤
    estimated_time: int          # 预估执行时间(分钟)
    priority: int = 1            # 优先级 1-5
    keywords: List[str] = None   # 关键词

@dataclass
class TaskPlan:
    id: str
    intent: TaskIntent
    complexity: TaskComplexity
    steps: List[TaskStep]
    estimated_total_time: int
    required_agents: List[str]   # 需要的Agent类型列表
    user_context: Dict[str, Any] # 用户上下文信息

class TaskAnalyzer:
    """任务分析器 - 理解用户需求并制定计划"""
    
    def __init__(self):
        # 任务类型关键词映射
        self.task_patterns = {
            TaskIntent.WRITING: {
                "keywords": ["写作", "作文", "文章", "创作", "写", "撰写"],
                "agents": ["composition_agent"]
            },
            TaskIntent.REPORTING: {
                "keywords": ["报告", "日报", "周报", "月报", "总结", "汇报"],
                "agents": ["daily_report_agent", "week_report_agent", "month_report_agent"]
            },
            TaskIntent.SCHEDULING: {
                "keywords": ["日程", "安排", "会议", "提醒", "计划", "时间"],
                "agents": ["schedule_agent"]
            },
            TaskIntent.CREATIVE: {
                "keywords": ["小红书", "新媒体", "文案", "创意", "营销", "种草"],
                "agents": ["xiaohongshu_copywriting_agent", "xiaohongshu_creation_agent", "new_media_copywriting_agent"]
            },
            TaskIntent.DOCUMENT: {
                "keywords": ["公文", "文档", "正式", "官方", "通知", "函件"],
                "agents": ["official_document_writing_agent"]
            }
        }
        
        # 复杂度评估规则
        self.complexity_rules = {
            "simple_indicators": ["简单", "快速", "直接", "一个"],
            "medium_indicators": ["详细", "完整", "多个", "系列"],
            "complex_indicators": ["复杂", "深入", "全面", "综合", "分析"]
        }
    
    def analyze_task(self, user_input: str, user_context: Dict[str, Any] = None) -> TaskPlan:
        """分析用户输入，生成任务计划"""
        
        # 1. 意图识别
        intent = self._identify_intent(user_input)
        
        # 2. 复杂度评估
        complexity = self._assess_complexity(user_input)
        
        # 3. 生成任务步骤
        steps = self._generate_steps(user_input, intent, complexity)
        
        # 4. 计算总时间和所需Agent
        total_time = sum(step.estimated_time for step in steps)
        required_agents = list(set(step.agent_type for step in steps))
        
        # 5. 生成任务ID
        import uuid
        task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        return TaskPlan(
            id=task_id,
            intent=intent,
            complexity=complexity,
            steps=steps,
            estimated_total_time=total_time,
            required_agents=required_agents,
            user_context=user_context or {}
        )
    
    def _identify_intent(self, user_input: str) -> TaskIntent:
        """识别用户意图"""
        user_input_lower = user_input.lower()
        
        # 计算每种意图的匹配分数
        intent_scores = {}
        
        for intent, config in self.task_patterns.items():
            score = 0
            for keyword in config["keywords"]:
                if keyword in user_input_lower:
                    score += 1
            intent_scores[intent] = score
        
        # 返回得分最高的意图
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            if intent_scores[best_intent] > 0:
                return best_intent
        
        return TaskIntent.GENERAL
    
    def _assess_complexity(self, user_input: str) -> TaskComplexity:
        """评估任务复杂度"""
        user_input_lower = user_input.lower()
        
        simple_score = sum(1 for indicator in self.complexity_rules["simple_indicators"] 
                          if indicator in user_input_lower)
        medium_score = sum(1 for indicator in self.complexity_rules["medium_indicators"] 
                          if indicator in user_input_lower)
        complex_score = sum(1 for indicator in self.complexity_rules["complex_indicators"] 
                           if indicator in user_input_lower)
        
        # 基于文本长度的复杂度判断
        text_length = len(user_input)
        if text_length > 200:
            complex_score += 1
        elif text_length > 100:
            medium_score += 1
        else:
            simple_score += 1
        
        # 基于句子数量的复杂度判断
        sentence_count = len(re.split(r'[。！？]', user_input))
        if sentence_count > 5:
            complex_score += 1
        elif sentence_count > 2:
            medium_score += 1
        
        # 返回得分最高的复杂度
        scores = {
            TaskComplexity.SIMPLE: simple_score,
            TaskComplexity.MEDIUM: medium_score,
            TaskComplexity.COMPLEX: complex_score
        }
        
        return max(scores, key=scores.get)
    
    def _generate_steps(self, user_input: str, intent: TaskIntent, 
                       complexity: TaskComplexity) -> List[TaskStep]:
        """生成任务执行步骤"""
        steps = []
        
        if intent == TaskIntent.WRITING:
            steps = self._generate_writing_steps(user_input, complexity)
        elif intent == TaskIntent.REPORTING:
            steps = self._generate_reporting_steps(user_input, complexity)
        elif intent == TaskIntent.SCHEDULING:
            steps = self._generate_scheduling_steps(user_input, complexity)
        elif intent == TaskIntent.CREATIVE:
            steps = self._generate_creative_steps(user_input, complexity)
        elif intent == TaskIntent.DOCUMENT:
            steps = self._generate_document_steps(user_input, complexity)
        else:
            steps = self._generate_general_steps(user_input, complexity)
        
        return steps
    
    def _generate_writing_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成写作任务步骤"""
        steps = []
        
        if complexity == TaskComplexity.SIMPLE:
            steps.append(TaskStep(
                id="write_content",
                title="内容写作",
                description=f"根据用户需求进行写作：{user_input}",
                task_type="writing",
                agent_type="composition_agent",
                dependencies=[],
                estimated_time=15,
                keywords=["写作", "内容"]
            ))
        else:
            # 复杂写作任务可能需要多步骤
            steps.extend([
                TaskStep(
                    id="analyze_requirements",
                    title="需求分析",
                    description="分析写作要求和目标受众",
                    task_type="analysis",
                    agent_type="composition_agent",
                    dependencies=[],
                    estimated_time=5,
                    keywords=["分析", "需求"]
                ),
                TaskStep(
                    id="create_content",
                    title="内容创作",
                    description=f"创作内容：{user_input}",
                    task_type="writing",
                    agent_type="composition_agent",
                    dependencies=["analyze_requirements"],
                    estimated_time=25,
                    keywords=["创作", "写作"]
                )
            ])
        
        return steps
    
    def _generate_reporting_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成报告任务步骤"""
        steps = []
        
        # 根据关键词判断报告类型
        if "日报" in user_input:
            agent_type = "daily_report_agent"
            report_type = "日报"
        elif "周报" in user_input:
            agent_type = "week_report_agent"
            report_type = "周报"
        elif "月报" in user_input:
            agent_type = "month_report_agent"
            report_type = "月报"
        else:
            agent_type = "daily_report_agent"
            report_type = "报告"
        
        steps.append(TaskStep(
            id="generate_report",
            title=f"生成{report_type}",
            description=f"根据用户需求生成{report_type}：{user_input}",
            task_type="reporting",
            agent_type=agent_type,
            dependencies=[],
            estimated_time=20,
            keywords=["报告", report_type]
        ))
        
        return steps
    
    def _generate_scheduling_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成日程管理任务步骤"""
        steps = []
        
        steps.append(TaskStep(
            id="manage_schedule",
            title="日程管理",
            description=f"处理日程相关需求：{user_input}",
            task_type="scheduling",
            agent_type="schedule_agent",
            dependencies=[],
            estimated_time=10,
            keywords=["日程", "安排"]
        ))
        
        return steps
    
    def _generate_creative_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成创意任务步骤"""
        steps = []
        
        # 根据关键词选择合适的创意Agent
        if "小红书" in user_input:
            if "文案" in user_input:
                agent_type = "xiaohongshu_copywriting_agent"
            else:
                agent_type = "xiaohongshu_creation_agent"
        else:
            agent_type = "new_media_copywriting_agent"
        
        steps.append(TaskStep(
            id="create_content",
            title="创意内容创作",
            description=f"创作创意内容：{user_input}",
            task_type="creative",
            agent_type=agent_type,
            dependencies=[],
            estimated_time=20,
            keywords=["创意", "内容"]
        ))
        
        return steps
    
    def _generate_document_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成文档处理任务步骤"""
        steps = []
        
        steps.append(TaskStep(
            id="write_document",
            title="文档撰写",
            description=f"撰写正式文档：{user_input}",
            task_type="document",
            agent_type="official_document_writing_agent",
            dependencies=[],
            estimated_time=25,
            keywords=["文档", "公文"]
        ))
        
        return steps
    
    def _generate_general_steps(self, user_input: str, complexity: TaskComplexity) -> List[TaskStep]:
        """生成通用任务步骤"""
        steps = []
        
        # 对于通用任务，选择最合适的Agent
        # 这里可以实现更复杂的Agent选择逻辑
        steps.append(TaskStep(
            id="general_task",
            title="通用任务处理",
            description=f"处理用户请求：{user_input}",
            task_type="general",
            agent_type="composition_agent",  # 默认使用作文助手
            dependencies=[],
            estimated_time=15,
            keywords=["通用", "处理"]
        ))
        
        return steps