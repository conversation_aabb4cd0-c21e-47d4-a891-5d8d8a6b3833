{"name": "zip-stream", "versions": {"0.1.0-alpha": {"name": "zip-stream", "version": "0.1.0-alpha", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.0-alpha", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "74bfdf3be9453cbfad41dfd566104deb5bb52743", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.0-alpha.tgz", "integrity": "sha512-Hm4l1zkCtm3qO94AXCbwcT1zo+PQrMaMv2r7ZAh1qLfOj4zpB2V0J1K/fcY5gowKtHafb7HHy2PFrtmB1KAwsw==", "signatures": [{"sig": "MEUCIEK0clKK0mPdz6NqptXOxmHtKoAK/SbnjZMGci+oPt3mAiEA1OdjaKgY+aazv0mXgcVwosKF/G+JTHBdwtyAQcdYKEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"iconv-lite": "~0.2.11", "readable-stream": "~1.1.9"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.0": {"name": "zip-stream", "version": "0.1.0", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "19e2c972e7245f0f7d273504b8171aae40518945", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.0.tgz", "integrity": "sha512-4WEL6ovij1xnXWXI8M4JanwiN90ciFS5+1FPR7unujW7ZA8MhzeWbL40N4SThb9o8cUeTC9GIxnZ19dSEhzWxg==", "signatures": [{"sig": "MEYCIQDmYbUrxSl5wqQPJ97FdYJqwcsGTEOjDv6rVcBgt70fNAIhAPmlYYQJjZ07k1M5loHSDl3JovENCXHm7xv2KbecA7nh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.1.9"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.1": {"name": "zip-stream", "version": "0.1.1", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "99e9ef9a8a9ccee2ae589aa679ab9be1414e1d1c", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.1.tgz", "integrity": "sha512-9UA8XDusUDFwuxL9Ykg6Gp08lAPwHds5FwCFi5ZPXBQQPpYoT/G1dI/viqc/NlnM/7p8oXW6NUI2PmwM1AUX7Q==", "signatures": [{"sig": "MEYCIQDultP3uk7EgTlrgM8YmU7pcGyvkhPAiqVBO+/HjBoD2AIhAIszxmSb0lV9VGTxs85+IHqzLh+xkj0qGzYZ3OcRiefb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.1.9"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.2": {"name": "zip-stream", "version": "0.1.2", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "a7d0dfc82c265202b0068be4bbea09187ca5361c", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.2.tgz", "integrity": "sha512-/6l70Cx28+v7wW7wOJYpw/j2aHp9R5RSZnn7HhJkzopxIAnn0cmMQMcJfJenQfLEy4eTYnE65B8VOVb7Dccmgw==", "signatures": [{"sig": "MEYCIQC+uG2Wijg1cyCoVzR6oxgUNwgPlL57M9VYNlVfF83hkgIhAOWgklLtMI2yfZRXXxORQcdAJfEGgKnsww/xjaZ3Fh+6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.1.9"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.3": {"name": "zip-stream", "version": "0.1.3", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "06d8787133ab397ab60ef6ee688d76dd5fb10de1", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.3.tgz", "integrity": "sha512-FKXs3gW53YETSGX158c0t/oFnd3gc8FuD8fTG54x5Fi06kSC/ojLc8DtCyuHTkl1OFLnDWjzOPpg5zKALwwavw==", "signatures": [{"sig": "MEQCIAYN5Yh4ekbzZ/PshU/OA6Z3zPPKLmvKDrELFkCzJH4ZAiBxbBcU0Jrk2UrN+n3EAUj0Z4afmRL9qKQiHJVuyhf5lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.1.9"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.4": {"name": "zip-stream", "version": "0.1.4", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "fe5b565bc366b8d73d5d4c1606e07c8947de1654", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.1.4.tgz", "integrity": "sha512-pGMg0aTsZZMX4siUiB76DUYnQNNicM4RU892uSKxoU1KeFGWg5kRa0qqYwObO7EqjnlHesMATQ16zPW4DOqa/A==", "signatures": [{"sig": "MEUCIQD/TCkGsJAC+ipnWNp/zVuwpUpef7rt80WuBs6zfvUUPAIgC1v0r5Xa6y+tVksvu+8cUga4E0NqSBYXEJaK3bOaDjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.2.0": {"name": "zip-stream", "version": "0.2.0", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "c002329e0e47b5e41a5b0102770eb8ae0e8e114f", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.2.0.tgz", "integrity": "sha512-nKQcfEkyWxOr01zlQANBn890zp2wesuaMXtswu0Bhre0LwvNMi/Y6F/p8Zsh55MYCSBkJX41DkmsSalQSqfZjQ==", "signatures": [{"sig": "MEUCIQDvP7O43HtL98kcwOk/CiQ0IlDKLpgMcPYzo9xsICV64gIgU00sHmo+WUMXy075jrLUcVzb7hy3VmY4kOR0Btkj5To=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.2.1": {"name": "zip-stream", "version": "0.2.1", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "fdc4928c727471c77567ed629c9342b5515c77c2", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.2.1.tgz", "integrity": "sha512-JgLC+o+n8BQeVrLCpLTfrQKnYaPWzzK07htBAN+ag9peKGkiUQsf/0lx+u/NbPPEn3t2HPcUiSbGW3tMfnKvRg==", "signatures": [{"sig": "MEUCIQD5HJGecXDRy7tF2yWMWM2ON/dbfPcaVUBlG+FRfDzmFwIgHw9tqlwspGCdKunRywE9QRDv081Juql0PQmHK/DDuVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.2.2": {"name": "zip-stream", "version": "0.2.2", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "907657be30d543d9e0d9a1b3f22785254d106c6e", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.2.2.tgz", "integrity": "sha512-WqRTQtbvONz3LsxdUcly3K2HXZBbO5sxxmTJNBMnkyCD/IIRrkbEmcOALob6oKvbtJQ/edeCMHjj0/pmhEBeTw==", "signatures": [{"sig": "MEUCIGn8bCLCY2Zcr884XscGB/k5fOa93mlmKD6SAbTLx2SKAiEAoICMgdJhPuFZAFaHfSrjgkhMKWAlIJUxtI//Cza0s0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"debug": "~0.7.4", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.2.3": {"name": "zip-stream", "version": "0.2.3", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "aef095376cfe138959a81341981d26338b46d8d3", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.2.3.tgz", "integrity": "sha512-tQ2w0zKZoGasBEO5x4xkBMmPLJUsGouHRApTbJ/vX4O0vr1G5X1IW3qgqS79XhVf8ACOSUG4ypckVwF1AxM0aQ==", "signatures": [{"sig": "MEUCIFgpn1M5LwJtb5FsWmB8r9BNgYiaZ2lNwVBG2GknkPdsAiEA8Jm25BXITX8jqyyjjAw7IasHu2peKviXNxMxXrdl8xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"debug": "~0.7.4", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.3.0": {"name": "zip-stream", "version": "0.3.0", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "31797f3e45770ff837e1f33da1a31fd55af847ea", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.0.tgz", "integrity": "sha512-Lb+CgI3EzV6GVBSeQkAbVCA9+ZkRlo5sEGG5M6Ha9UV73z7BfnV9mACADx8HXVUseglyKprkMlxI6YRO+nYqng==", "signatures": [{"sig": "MEUCIQDlOWt/LlZTk9BMvfD8NCJtCJ7vcTbslE8xNdigWFRqSAIgdOjHO5JPP97BZ9hk/my5HwQ0iBNuVkys+nN+sMoMgvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"debug": "~0.8.0", "buffer-crc32": "~0.2.1", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.3.1": {"name": "zip-stream", "version": "0.3.1", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "b4d76e63200a2213e2807f6e79ffa7d23d9ead96", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.1.tgz", "integrity": "sha512-hHbcUFddY6cv0Rso+a3nUnGmv4mFocGy/lp5xQOB6mUksrQ47CYrUKJcSU87OO2CarbYt/O07WaycPIxDncEjA==", "signatures": [{"sig": "MEYCIQCPe+YG2yEkQSNrA0Qd7RTE3XCOCXiqAXyk+oxjwAJbaQIhALmGBTGwuk35DhVBPPrMntJtUi1kEn1RnkQUX8XGzGnm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming zip generator.", "directories": {}, "dependencies": {"debug": "~0.8.0", "buffer-crc32": "~0.2.1", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.3.2": {"name": "zip-stream", "version": "0.3.2", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "d5e88facca1481c09e0fbc9bfdd2957d63cffd5b", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.2.tgz", "integrity": "sha512-wRvQMVsEXobuLhbWlV4lHi+P3gd8wKSH4as9i97ijWk2JgO4IwwYRCJzQ7okQ4xGDqFqSIKN7y20yJRUzIPpOA==", "signatures": [{"sig": "MEUCIEv347PTJHW7OUKqzEsQ5/pTmDuKlhHI00Tpg7szYMyLAiEAthRO8BIRDHQNWsbF+IM5vatiJFd2twslhD9tH5qI+D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "d5e88facca1481c09e0fbc9bfdd2957d63cffd5b", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~0.8.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.3.3": {"name": "zip-stream", "version": "0.3.3", "keywords": ["archive", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "52de70345da3bb290c509e0c6c6a0a05f49cb846", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.3.tgz", "integrity": "sha512-B9QzCO6FH3Eb0zKsv8HM3JxnJwztuD7EyBd4f3YnXie87ommtp/BSM7CgMX2m+kkn+Uanq2+YV6NFHIbo4QE/Q==", "signatures": [{"sig": "MEQCIGWfeCuM2QG3GVMwSljcJi0xZwajsu4J3U1ovVsCzk5pAiA6qdK9p2izOnl8kL7ry87s73bRmKvAoMquLmi0MGy30Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "52de70345da3bb290c509e0c6c6a0a05f49cb846", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~0.8.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.3.4": {"name": "zip-stream", "version": "0.3.4", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "1b7dc01591300032d69699fe66962bf52a0de91c", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.4.tgz", "integrity": "sha512-ikHV31lkMsdzSVI5KBZ/txqoeGlPWVojnv/ajWJGyPq+2D4ra8dz9l13AptbGiOCwDovZTGxnIiKyKD+6xXk5w==", "signatures": [{"sig": "MEQCIAFLo+GwLQlA8mxR2Ds51m95yvGfzMuXD0UA5jiOmCbTAiBPujKUINM43jFfCzNh66oyKN97iw0LcNhoGWyz16yfIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "1b7dc01591300032d69699fe66962bf52a0de91c", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~0.8.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.3.5": {"name": "zip-stream", "version": "0.3.5", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "b240ab74e63db910c074910af3ca9904f45a8986", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.5.tgz", "integrity": "sha512-u1BGHftzxjAvhuOCtzcVTOLZjK3M4khJRfW6Qi6d4ORFRF57Jk5/eAWqbRKd+87dM5VgFEWqMr7kGnrd69RAYg==", "signatures": [{"sig": "MEQCIEtYSYPXII+46LnLOP8co8d00WRWhk/5AhExX+Owkii2AiBgr3M4NMCMduLIq4glpgxAAKJY3VM5pTy8QM+giVMOOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "b240ab74e63db910c074910af3ca9904f45a8986", "engines": {"node": ">= 0.8.0"}, "gitHead": "8457f45e08cc217d8499a812ac58039310fafbad", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~1.0.2", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.3.6": {"name": "zip-stream", "version": "0.3.6", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "0f5396e003d40c5052bc53357354d75f96d9b217", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.6.tgz", "integrity": "sha512-ezyPy9gK+/KqfA0Xf/7N2nwrtdZmVcA2XZxO3FXj5jtIYEMKXQpZtQJP5zRQjKbiIjPkMNbWHlvGeBbcDScCCg==", "signatures": [{"sig": "MEQCIHFOzDCveUtlWYETiJUHWb7Qxus490xvwPzn28iNT0V9AiAeaVoombDdcqlMNruIuNesn3jIxoTekqJ6Ea5VzQKjKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "0f5396e003d40c5052bc53357354d75f96d9b217", "engines": {"node": ">= 0.8.0"}, "gitHead": "c82454762f471453c3f0e43ff7a70a87d581e0d4", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~1.0.2", "lodash": "~2.4.1", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.3.7": {"name": "zip-stream", "version": "0.3.7", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "c84d057eb0bcc0139747bd3c6c97280bcf5f2bb2", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.3.7.tgz", "integrity": "sha512-Lpm2c6fb9/XCuPnIR1fS8jUVCJyL+sdAuzRRQFhnQN55rEWZ1YIs7C+8DbSbASQBy9o7C86uvv7Xx5cUJArmag==", "signatures": [{"sig": "MEYCIQDE83llTuAjXb9ibYCBCn7g5HG3h8YSlYtY6+gfRZ+hTgIhAPG+2UqjeJ6ceHzjn0iAfvHge76jfiFbCYrDSGHpPche", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "c84d057eb0bcc0139747bd3c6c97280bcf5f2bb2", "engines": {"node": ">= 0.8.0"}, "gitHead": "2dc92a49fe54c165092d4cadc6ab483968e74ad8", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"debug": "~1.0.2", "lodash": "~2.4.1", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.4.0": {"name": "zip-stream", "version": "0.4.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "22259146fa3d46a3c3f342d2f42cdac3a37c03a0", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.4.0.tgz", "integrity": "sha512-ngHUPv6HoiK0rv3YfG4JwrDgHlSAUjgyw7uL2tuJenERpj1pSEpbPtGzMOhRenZV5h8/KL5AsGnXD7pcwucjzQ==", "signatures": [{"sig": "MEUCIC5GpOJWJPHuQSLKYqRqwSxlZ7f8bRnkMcHPGwmUM18wAiEA3dvT6ztcpu1XCEgd3s1QbjVwRgT19ODDDLUQ8GHrydo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "22259146fa3d46a3c3f342d2f42cdac3a37c03a0", "engines": {"node": ">= 0.8.0"}, "gitHead": "ef1cf4fadb3de10d7c9afb54d82a69e45a507fd0", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"lodash": "~2.4.1", "readable-stream": "~1.0.26", "compress-commons": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.4.1": {"name": "zip-stream", "version": "0.4.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "4ea795a8ce19e9fab49a31d1d0877214159f03a3", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.4.1.tgz", "integrity": "sha512-besxwBaXruvWoMXO44C5SKtmJ4XQUZGs9BoHW4E+FNtVkuGHqtUL69r0s6RKSA9a0zQs5XwQvGsD+JBaC9/2sg==", "signatures": [{"sig": "MEYCIQDBtL4IK3I6KVO1nRYsqpCzEdknYCc2sHnpNZPrCNs4hAIhAPKurRcm7JYaYfXhJb24rGfxehlTsTja0Q+U36Wk3Yy9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "4ea795a8ce19e9fab49a31d1d0877214159f03a3", "engines": {"node": ">= 0.8.0"}, "gitHead": "e877312d0d92ec607482e4851058a50e67c6f597", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"lodash": "~2.4.1", "readable-stream": "~1.0.26", "compress-commons": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.5.0": {"name": "zip-stream", "version": "0.5.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-zip-stream", "bugs": {"url": "https://github.com/ctalkington/node-zip-stream/issues"}, "dist": {"shasum": "49fdff9aaf27cf0938f7bd57e275f44e64b74c28", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.5.0.tgz", "integrity": "sha512-oztWXvcGc9JPH6Vdhaz+X5a2znLeQatB/2XVZOJuNF6KZLyR7VtGbWP4X9nMgcGQ5S6xC+CX5/dZR1TC2QgrhQ==", "signatures": [{"sig": "MEUCIQC3Mvmx8QBaRv4vK/I+fiW3NVf+LkUgz6pUscUikk1XTQIgfWG9MGLLxbSCjzg2yho7WVfY+1o/ct3g0Wz5wuHJ+fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "49fdff9aaf27cf0938f7bd57e275f44e64b74c28", "engines": {"node": ">= 0.8.0"}, "gitHead": "d54bfa99efc8525d59c0f6d1d83483981f9ae321", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-zip-stream.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "a streaming zip archive generator.", "directories": {}, "dependencies": {"lodash": "~2.4.1", "readable-stream": "~1.0.26", "compress-commons": "~0.2.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.5.1": {"name": "zip-stream", "version": "0.5.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "zip-stream@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "1c85540abcb25d70342875529949ef244f3dfcf6", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.5.1.tgz", "integrity": "sha512-D2w0O1Iy4dd05aTEvmF36dmEacyC3I5mhupEeunfiZbg2R9H8S1KC4K+/m9qkFc53+MoT57Aw3C0qQnmRHBcbg==", "signatures": [{"sig": "MEQCIGNAEs9tSVGdD1Ts+Nvgd/0/XdD/rurkM85d6huKxDyOAiBYQ5p8T+mBD/jBaK4JyW87f+srcyZVlwdZX8gxreoRcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "1c85540abcb25d70342875529949ef244f3dfcf6", "engines": {"node": ">= 0.8.0"}, "gitHead": "b2c2023d355886a5dff8da5907064c70bf27d290", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/archiverjs/node-zip-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"lodash": "~3.2.0", "readable-stream": "~1.0.26", "compress-commons": "~0.2.0"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.5.2": {"name": "zip-stream", "version": "0.5.2", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "32dcbc506d0dab4d21372625bd7ebaac3c2fff56", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.5.2.tgz", "integrity": "sha512-cmTLLn+kgmAabL0sXKFDHh6ARY2mpm8X52iIMvOKqSHXKS4fsht9/axUdJYp55V7qRgZ8jfvzZdo6oV00uZ08w==", "signatures": [{"sig": "MEYCIQCf0LPkQjmUq6z97E25YYngTeaJlJ/E/pj+brE6zl3OqgIhAOze+noKM7xA/MYQ/eCnZKBay7Y6LaaelRIZldNx/G1p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib"], "_shasum": "32dcbc506d0dab4d21372625bd7ebaac3c2fff56", "engines": {"node": ">= 0.8.0"}, "gitHead": "e1ffab4835feb864ec139493a8a92c7d9782f4ac", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"lodash": "~3.2.0", "readable-stream": "~1.0.26", "compress-commons": "~0.2.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.6.0": {"name": "zip-stream", "version": "0.6.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "ee933aed996fb18b344a91ae3b5d264cec5e812b", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.6.0.tgz", "integrity": "sha512-hCumeI4xfnAh8aZhwne+3PCyIWWUdBbxKD6ZLtzrg+EnANh3XVZppjnRIUMUP/LKBvTpYfZSpjnH478+R+YQBA==", "signatures": [{"sig": "MEYCIQCYb15bECxRGT/EmjYEoO/JZ2CnLPLLPGqNTTf629zKBAIhAMoAf3TUZi9rY579b/h3MZqGTM1wlFTKAXDHfdm3rWkb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib"], "_shasum": "ee933aed996fb18b344a91ae3b5d264cec5e812b", "engines": {"node": ">= 0.10.0"}, "gitHead": "f8d9458886a2fbe9baa9612781afa3b5cd65ab62", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"lodash": "~3.10.1", "readable-stream": "~1.0.26", "compress-commons": "~0.3.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.3.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "0.7.0": {"name": "zip-stream", "version": "0.7.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "349272f7217d0cb7c7721dc0afc3b9b8faa07b75", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.7.0.tgz", "integrity": "sha512-w/8e3MWd+H4SUJrGDCUK4NNrmTFTp1btqG8Mp6s7DfVUbJc8T+vm1UwgljZ6C7yjChAwL5Fnb4nlKL31lNR0hQ==", "signatures": [{"sig": "MEYCIQCWUaIHz/tx/aEAu0vfNz2U7bEYSBFjiOt84hihThtDDgIhALWoxC1JG2hguVMJI6R63UX0TobDCQHUuX6iyjSEVRTN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib"], "_shasum": "349272f7217d0cb7c7721dc0afc3b9b8faa07b75", "engines": {"node": ">= 0.10.0"}, "gitHead": "a960366cb949dc613996ee2da6ccdd7509a808d4", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"lodash": "~3.10.1", "readable-stream": "~2.0.0", "compress-commons": "~0.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "0.8.0": {"name": "zip-stream", "version": "0.8.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "236b2fe25823cb4f48e8336f5bfa743aa5ae9dbd", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-0.8.0.tgz", "integrity": "sha512-2P80o9jHcTg62vUxbwY1DLoH6sZx75VGaoBbx/BLyagk/BA7KpaJ8DnmaCWiyIWL7IDXd7iIA0FtLgHTgPCafw==", "signatures": [{"sig": "MEQCIDpZud2gQlPYHBtgd376NJFHsP9NQfYJ+cNtZnoNdIsBAiAmSxv5aegPSvNEGPc3sd8dx9ttSquAcvnC1ArmFyDA6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/zip-stream.js", "_from": ".", "files": ["lib"], "_shasum": "236b2fe25823cb4f48e8336f5bfa743aa5ae9dbd", "engines": {"node": ">= 0.10.0"}, "gitHead": "14b237e991ee64173777ac85c23e6c3bb43b11a6", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"lodash": "~3.10.1", "archiver-utils": "~0.3.0", "readable-stream": "~2.0.0", "compress-commons": "~0.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "1.0.0": {"name": "zip-stream", "version": "1.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "e7130637d5b037a27621557e419ed3d33f7cea13", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-1.0.0.tgz", "integrity": "sha512-aSxIjA1K6DSHIwOvAmcA+F7IVEeEmjfb5lcXunhLrc0/QAh6VyUmRJn4ePeyy1uQDfkiV3xNASc9wjBYVAuKuA==", "signatures": [{"sig": "MEUCIBPRuTLiiaYIVdT8n9tsJZ0FJt3zEFjhNXFz4o+XQ1KSAiEAgpqBBG8eE8R5ip1/o0MyzXKAK/KGMP2ben0fa24nIAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "e7130637d5b037a27621557e419ed3d33f7cea13", "engines": {"node": ">= 0.10.0"}, "gitHead": "580763e0484b136e8eb05fea1705624a1143005b", "scripts": {"test": "mocha --reporter dot", "gendocs": "jsdoc -c jsdoc.json readme.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"lodash": "^4.8.0", "archiver-utils": "^1.0.0", "readable-stream": "^2.0.0", "compress-commons": "^1.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "jsdoc": "^3.4.0", "mocha": "^2.3.3", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream-1.0.0.tgz_1459915954765_0.10562265454791486", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.1.0": {"name": "zip-stream", "version": "1.1.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "2ad479fffc168e05a888e8c348ff6813b3f13733", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-1.1.0.tgz", "integrity": "sha512-i7SEKvFC1yGW8GpOXhYuoJb/REf/Y8JQA5ljYLeF7IsOtW/j9cVVSjXd8Ww27pNqt0TXgB4LmulXdAC22de7YQ==", "signatures": [{"sig": "MEYCIQC+iK5eoMkXU5w8uV/CucNG79pkb8v/33gDwozBZdypPwIhANJLmcBz8J6mgByaT3WuNYdBtdScrCq86444KOoY97cO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "2ad479fffc168e05a888e8c348ff6813b3f13733", "engines": {"node": ">= 0.10.0"}, "gitHead": "01200700dc465cff1980908f14e93d1455f7d771", "scripts": {"test": "mocha --reporter dot", "gendocs": "jsdoc -c jsdoc.json readme.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"lodash": "^4.8.0", "archiver-utils": "^1.3.0", "readable-stream": "^2.0.0", "compress-commons": "^1.1.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "jsdoc": "^3.4.0", "mocha": "^2.3.3", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream-1.1.0.tgz_1472324082028_0.17289348132908344", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.1.1": {"name": "zip-stream", "version": "1.1.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "5216b48bbb4d2651f64d5c6e6f09eb4a7399d557", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-1.1.1.tgz", "integrity": "sha512-jqAmAaOnZDOWANGRoLxZocWMn4o3qDPFZ8cpXFpZtA+oifoifpT3tmDGCizkHL2+dGSQDczvVR8+CkiiCXj8Yg==", "signatures": [{"sig": "MEUCIEUzhFVMnv8SZMhlrxXNiVM6MK7noZzGefrbmEmzBgUPAiEAll8tHkQtndAFWMgVPe3jQcXWIhvKOVNJsparScE/y6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "5216b48bbb4d2651f64d5c6e6f09eb4a7399d557", "engines": {"node": ">= 0.10.0"}, "gitHead": "d42ef2bf0b2b5d625e1df620fff5f7a355145d23", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"lodash": "^4.8.0", "archiver-utils": "^1.3.0", "readable-stream": "^2.0.0", "compress-commons": "^1.1.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "jsdoc": "~3.4.0", "mocha": "^3.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3", "archiver-jsdoc-theme": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream-1.1.1.tgz_1484267383139_0.9497187850065529", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.2.0": {"name": "zip-stream", "version": "1.2.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "a8bc45f4c1b49699c6b90198baacaacdbcd4ba04", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-1.2.0.tgz", "integrity": "sha512-2olrDUuPM4NvRIgGPhvrp84f7/HmWR6RiQrgwFF2VctmnssFiogtYL3DcA8Vl2bsSmju79sVXe38TsII7JleUg==", "signatures": [{"sig": "MEYCIQDL3luLh3web0l1nmt8UzUstT8L/v0i051OFhsfEz5kewIhAJfH0WedrtKsGb9w44edInYhyERN1XU39Gq7VUWgt6Fo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "a8bc45f4c1b49699c6b90198baacaacdbcd4ba04", "engines": {"node": ">= 0.10.0"}, "gitHead": "94cf3e8ca8333b3e28f131ef52c70f05135b262c", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"lodash": "^4.8.0", "archiver-utils": "^1.3.0", "readable-stream": "^2.0.0", "compress-commons": "^1.2.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^4.0.0", "jsdoc": "~3.4.0", "mocha": "^3.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3", "archiver-jsdoc-theme": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream-1.2.0.tgz_1497655252376_0.9827396376058459", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "zip-stream", "version": "2.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "82a024284b4d56dd7964c3ebac0aed12f14c0800", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-MCzqD/9WMyv7RQTXHMVZLyU77KfuH2T45bvh71iDxFnT8h8mpTYHxASJ8buCYOv+FBkYxyaqoDHKe6iHulNGYQ==", "signatures": [{"sig": "MEYCIQC3J19x44UUAqGrQOabhqlIwRTvIOzu6kskh6azeqKEAwIhAOTvLLC98fJQbwe9OAQcEd4cGpEGliQIMCcV8kcQN6q2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfeEvCRA9TVsSAnZWagAAcgUP/iIRdGZtW9xB1jKAskO9\nvZL2e3CtbVZXZpyrU1FVhpJeyr6hJR+xuwSV6h8dTnXQsfmetNDWHb1V26qV\n+MBC5iVRD4zIiQ+yGO0KtAJ6SigF2iInq9lrwFIY0WMQBShDJhoayHdnEnJE\nWzS4NXHHzajhkKloijf5rtnb+t8I6VIczPWZGok17hCvbXpcRoAF4mXIU4Rw\n0bbF9EZEqhD/dNPrinv8Fz35j0kcEg06cz8p/6Fnl0igvVUgTWYfAc/iv4Kg\naM+gQPcsEneK2kTdgspR+gXaBWEokW6NuxKg3Uakt2+NcqYk3B/RrIGWtl57\nsNcJcMJEYqFSCz6MOT49A2lv95J3bYzdwC68J4cZ1dQxEZAUOPGmuHWTtPIi\nk6GcF1skg7h3hEdBWPhq06mu7WDszp3sV4sElMcR35s3AdtGXMB+7s3xOHwv\ngehNlCbkumEfoRUBq+UmTwMXx1hhpqit5Zm9LPRrkcrwtIpryDyfn47fknWJ\nvFMAYPRb0PVZsgpO9t6FCffNXqobvWvZZlhqNUGlDKe/V5tf2ihWyKfnXDPO\nLGkxtCGodXADfqNFIPwToPyQQH1imV2zyh7p2RVbyQA+1LZJFn3KP6MgWZED\nznXh2urF/05GACofBBYkfTygZENI7Vv4iBeR0OCfZmRB7IT6E5QFh20DuaEP\nUYmz\r\n=jEpJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 6"}, "gitHead": "d9a5e9a1955ab4a48d9621d6234b80b89a8e1a89", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"archiver-utils": "^1.3.0", "readable-stream": "^2.0.0", "compress-commons": "^1.2.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "jsdoc": "~3.4.0", "mocha": "^5.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3", "archiver-jsdoc-theme": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.0.0_1534976302722_0.4918194223370276", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.1": {"name": "zip-stream", "version": "2.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "48a062488afe91dda42f823700fae589753ccd34", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-c+eUhhkDpaK87G/py74wvWLtz2kzMPNCCkUApkun50ssE0oQliIQzWpTnwjB+MTKVIf2tGzIgHyqW/Y+W77ecQ==", "signatures": [{"sig": "MEUCIQDRDHCM4nPeOw4/9HeYqC2Qrj1XJto2Tm6YoWFXhPMOYwIgN3O5aBcQhlalI1KhrqdPZPRKgr78t4+X4B3SwEcO+4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfeNqCRA9TVsSAnZWagAA6cEP/0SKlPeHwsbGXz0UR3ZF\n6GVwTlPEzqEykUO8hh+L2uYhCRSulASjE7MLKfTT0do9HJ6TKRhSZUDR4v2i\ntGVW4672nTOkcR/fPEI7d/B+ab/VhacXQRm3CYtCDfgm5uU38GEh1TTOYPwT\n6NZvxyEQuDB2V47r4Ke/hyPVf0Vy8BLOPd9ZDscuoRYY6BAiVTOeQv9cCWGQ\n5gIs7fxqs+ul9miy14ymY2714Y8/6p4R8oNu3NAfKl4HsSMuNNSm3ZudX/yg\nr1Mt2YM1akVCXfrQBStt4/Wfhs9/E/A/K74CUT7KlB90l2rOLk1l3O8q6jEA\nNCin6zhr8239RvZWOhui0L5gUJhX1RgAHVKwLx7yxUQ1GqCM8Y3FGV6NVff9\nVK5nQ5LgsGBHaKqUnM877H2FdwVVmggiTeO6lNbsC09cos6NhhYdrBNO+2E1\n70RiftDcbPS4uhuXKl40tGPQ7hRwlWw3t6ZTeLAmpWtkWSwH2Tddan/wb7DT\nCItwJEeDCUgkw0GxrfnItWV0Kn5R13exEYlUrBAAxmk5bnNxnXADgJtMwpLp\nIc28a0adiEIExf3VdLX0gSLTHZ0en4iZGYOmlRUm5k4nCDWKg5RlNkJRE4IP\nqYv37dLvqG1eFF9ArU+v50yjD9xEcd0WQdEMQmd4tItxCAvCo9+tKtXkxAd3\nIk8/\r\n=GIkr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 6"}, "gitHead": "b7510f66c0d5998bb45398466dbf84bb35b528b6", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"archiver-utils": "^2.0.0", "readable-stream": "^2.0.0", "compress-commons": "^1.2.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "jsdoc": "~3.4.0", "mocha": "^5.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3", "archiver-jsdoc-theme": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.0.1_1534976874348_0.09160902802566095", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "zip-stream", "version": "2.1.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "4f94246b64341536b86318bd556654278812b726", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-F/xoLqlQShgvn1BzHQCNiYIoo2R93GQIMH+tA6JC3ckMDkme4bnhEEXSferZcG5ea/6bZNx3GqSUHqT8TUO6uQ==", "signatures": [{"sig": "MEYCIQDogpJWZv1Z2CmBUEXujtSwOBiEKFuocxlC3x4TJtQXQAIhAKFHQF71c5J+B3C2L1AsN8/OWDjeLjMTlmL383YWrPej", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMlvTCRA9TVsSAnZWagAAPDAP/AyNTzkok74J/hOXK2SV\n2WjJ5Q74VPM2DrPM+4uddyyKak0W+4VpS4oxP/ApV8HJUSi1M3cgYjpL83Oi\nTC8XrBnQC9+dGnrRL/XDjg9xCGPhHt2NN4Z/lEoH/tua/AOtJJkyjXhDUJnf\nnB+DttrMRdokn+JvC1dqyZWltg/pJhDU4da8WltssJ/8QO2TYeWFgyvtKrCy\ngnl85HTIavTNzaauqzNd90P6gcTLi83GKDP52nUOOynsXa4Ip5PPztCLgoJo\nCvooRhVT2NTkpXFFZD0gDeQl4VJ0M8aBvNEH4SiPpyBZ+4rMUsNs890BF6Pu\nV8P0ZFdCS2AwwcIbymwT+XH94KaN/CHD0PM5kVqnwY+yMp3SgHTo9D4afGsl\nCJrRDESTfHGnBN5vZ9h6olosx4J2MxE8camJ/2gqCsb7eLhRQaT4K8WSQhgE\nIX9p4vjphYWDqCKsVJp81Obu6KMU1SS6dToy7Wd6fo0sSX4+KMifUglEhDxW\n4jQKxVwWx+nTXTj9uTwTChHu3qjsfsRtvB9+IVTeCJli6PlnDMsM0YFft/Ho\nRskLCDTjX9XvDkyEuBHjPF6ZJmnQrEQDeBpXIO4EUEwESsY06tWNNPyxAmot\nx/BunDlkzE0wd4wQy/lverSmrpHCfEiP+gRpOy6LJSFvV9cu4PVVkkLFOi/n\nXXdy\r\n=ZVMX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "49acc473ac36832d58b642a7e06b55628688218e", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.4.0", "compress-commons": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.3", "mocha": "^6.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.6.3", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.1.0_1563581394943_0.6307095472631923", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.1": {"name": "zip-stream", "version": "2.1.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "7fad7f39395fc2be0ef50fb5303ff43ad049c25a", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-v54puzKSjCN5COeMZizwIfGh9vJ6V1GR8BiRgr5Y9zZ9VngzRt8EmZHsbzCb5ktZT1JqHlt9xuN2HRv634SRPw==", "signatures": [{"sig": "MEQCICxc4QSFnavXDF6ozc78tNbFC3z4FnvsYzIr4A/9tYy3AiB2AkQyE6qaAOCMcCWCfh7Mz8bsjEZxJ1Fp+b86FEghXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRF+lCRA9TVsSAnZWagAAZpUQAKHFj6qKPOiZ3EDCRF9F\n3eWKZRkhChXBJA1H4/qr4yLB80lFPqKsMzHaQX+NSKw2uagfq5TElYiQ716Z\nVZIehOpEBaA0tvYOFJU7+OTlQw4ZTEnSPw2TfigyUo/Y5a5+CgjpC3eqSqad\nXeE+7IjmrXUiGWzj6D7vrql995wiw3cJvXAYhgPgcouUuBCZ0P1vMaXwclOo\nkEHGeIAEGfp1B7HYdbwo513dyEloVUArKcPdRZMngiBheiXwKr3Z7nDyxn2T\nq/UFYYjMinU8xhbqSo/3Sijj1peDUf7F2OluytazTLGji/AHOlatD8Bj3BIN\n8dw0U6SG2saH8VT+vIEjyXkhd/+ZrhFaVFaQHJ7/kx7wvvw22PCWVvqds592\n5fF7QYP4lwe3tF+tTjitaqoJtPWg+1KBYyFS0Tw+3YizA9YLlxHFbOZoCXgK\nivkg3mfJmQSuBfeYo1cEYtLIdaGNlD/8D+0zkIX+QdhypWLL6qSzkEO5EOtX\nziENGgxsRuPWB6pQ0bsZA6mr9S6foMhjGpxjvhmA6d0o65BTzXK6S7rYkaaW\n0LjjfLYUGVm3OmvTTrM9DYc9TAjFqR9bqTDUYQQmvrArGaLnWPLIbZg4KR03\nor/QXPw0GfNSnzvvARw/WPTaVMAqp2MdJNTWxRK+LcRtMscNzuRkvfYTW42D\nbBKv\r\n=jTQL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "01493162c094e4c5ad80c411ec1087bd0c9f21ad", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.4.0", "compress-commons": "^2.1.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.3", "mocha": "^6.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.6.3", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.1.1_1564762021275_0.9106901483586056", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.2": {"name": "zip-stream", "version": "2.1.2", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "841efd23214b602ff49c497cba1a85d8b5fbc39c", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.1.2.tgz", "fileCount": 5, "integrity": "sha512-ykebHGa2+uzth/R4HZLkZh3XFJzivhVsjJt8bN3GvBzLaqqrUdRacu+c4QtnUgjkkQfsOuNE1JgLKMCPNmkKgg==", "signatures": [{"sig": "MEYCIQDwNQrXqcSvdjDGAiUDezRSQYyhJuzRhlid06FA0RF9lgIhAKwrv02yb0HorumwBMfHP8FcXp9Lvqkmc3quwgu4nUp0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRL/+CRA9TVsSAnZWagAA+/EP/0P7rI2JM5pchO/6h7hT\n3k723AR8Rs2PZMDd/1ekMhqtfiVyY34y8stVp76utCwwSNCzugB8+phwI7+p\nSsRQXivVqoSt0FkJWLEl4xOryC5qgQI4suISMrojo/SfPT2IFBm83owdbJ4x\nBZWZAQHBug+KZLN2BsGFBetRROoNk6g2ozdgiOm3W/IymmpbZXJfpGQX9LMA\n1CZi8J5JQYofFTp7qff1T+UVT9BJHb/OKiDtwhiNJVpyAfuM22r4E3rv4gNw\nmlgUqaqxLEBu9yHCmZ53E7OSqwk0Lq67viSvApO6NglwLz3d3cT9sKWt0Zyv\n3XCgzlUFUVrku1CbP0Y5B9YM8Tmi4QoJkmcRUTYp3UyihiV6Z1FS1i1a41r7\nKloZL0zdf3NYpsgzil/aJy5Jbn1tu1jL6o531KppeX4fTwxyP0P9Whlb+eT7\n0imtNmnEZEPIPxgVL3wJ1qjiJAqkU6Fgq61Lh29sKiBevCtIFBxnG3owGUH4\n4+CLSoFFWTbj++b/0xkjeEypjhBOykK7CwWogePYs3PGI3N7mx6WoP4nc4Os\n+AkxRSAWdsr1z3EZiZVQInYtYpxmtLTNDsxuBAjt3u4Cp8h37s9nAMTDQ6Pr\nmkA3lylZzOQRAGGgEwf+gPFPoVInG5WKksr3SBS4wut3suohCdyOgc2Mg1S2\neV5v\r\n=0c8D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "801a652810950cadbca882277b3941d51fa640ba", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.4.0", "compress-commons": "^2.1.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.3", "mocha": "^6.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.6.3", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.1.2_1564786686325_0.5111033695290936", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.3": {"name": "zip-stream", "version": "2.1.3", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@2.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "26cc4bdb93641a8590dd07112e1f77af1758865b", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-2.1.3.tgz", "fileCount": 5, "integrity": "sha512-EkXc2JGcKhO5N5aZ7TmuNo45budRaFGHOmz24wtJR7znbNqDPmdZtUauKX6et8KAVseAMBOyWJqEpXcHTBsh7Q==", "signatures": [{"sig": "MEUCIQDsxjKFZu7hRPMtlakyZD/hb5T0gBJDaleB8z5bXL9NrQIgNv5RlSAj4Nl5hySO6jMa8aHguOvNqXO/U0O1FrxhLD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFmmdCRA9TVsSAnZWagAA+DAP/15ARBfZxVoWGYDaBecm\n5C81H3B/wYUmXdaZMtU+rVkfs0KsEiWb+LsL91BXNfsOngE2WFG6iqqmlwdE\neT4STRsmmw5+6PG+sqToXlYv4F+AxMO0Ye0uX4ysQ/3xieZFoF8GlBoJZv0X\nlysMFSEruz/kzDyn95r4GcgoCqL70oyMyVxHHsq074WK5si6hQ23C4OkCQPd\nSL+c600o2gMucXNmI3sqd3vkHnK2j36BUVD1fpsgfTAcO0ZAMHoMmN/jhqdD\nfYE6ucv8xNvBnwOutUMvkEBVh6ReCd0VVHCwVBKkDRdiE1Edqr9g4YL2YPxz\naQ7fWWd0Nuk7ioXJh6ozFy/JRzKmojSuAL1Lr5aFQZL9v6dFFNxEE9uTjpWr\nV2CO8etlBz7c1YfzppHW5iNPYIjgnjvOz4inssrow1+goq/9Bqf/dK60Mjst\ne6rR22oQPGHDYMohdW9FF8ozDbO8Ti1yEof4hjLFezdofIY9vntgIR6W7OY4\ni0Zsiy6O5G7y/nLCm3g8ZJ5LR5cQBs78cOJ6MBUhzoJAmLpeYPYssTT711eG\nghr0YbD/9iB4toE5E6Yu7iinsLxsH7xkQ58ugo8kayG7tGvMqJ9wntW3HGrL\nAhgm6Tc7sTkSWfc/KnTetJb75+QJrITWl1H+RPKvy2FPIU+2qDt26eK8HMiv\nloIs\r\n=QIg2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "f321243198359eab0dd8377f1f8af0ef90724a43", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.4.0", "compress-commons": "^2.1.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.3", "mocha": "^6.2.0", "minami": "^1.1.0", "mkdirp": "^0.5.0", "rimraf": "^2.6.3", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_2.1.3_1578527132660_0.10218885748206996", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "zip-stream", "version": "3.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "54b23ffa92cb45b4654a6a6a242d33c23e9f27e1", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-mV9SJdhCUNDhv/qH70lcGLSU/tFV9DKWrASXVZ4ZQfw+x6TdmXVEza17bEZn8kHre9Eoiocf/EqWbZDAObs4zQ==", "signatures": [{"sig": "MEUCIGRnOWnggXMz1BxEGxeViEe1WfoCVFckdf9KvfVi8W0LAiEAg7xGusmKCcPNWx6IpL8+KxE3zyiMyDRRoinZsa/QsRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeliUACRA9TVsSAnZWagAAdEUP/2cnjDJeLZ1WeSL/N9Hm\nlBr7DOuJLoMFo3HvTWzMKSeKmXNA0AXVBUknPEpEH1ScVGLRHI81cDc9mrEz\n+h3zisn3QLAaC5Y2vMvy8cNY9ecYzPszyBnasDRueFkOrHEdXclL2l8fBEol\ne1CSNeMqsERJ+O1q7i+kVKLSfzTqVGfqgNfAGHg0oyOuYnO7wCE5M/9GR5d1\nPmlmvfEfTRAamZT7UmCxcVoRIcvYmrbDdUiKCdWFD1mLALJWJ5UJFxgdB//q\nq/xF2FAx7WzhyzanqvPj1C8vm4BOBBtcPMnugKbIPGTm7rmKfxLzdECB+HOm\n7GHeNRIsFrEBEu2UiKjjx6cCMQXkiBFxW4sUW5OVIJ7XYynOeEQFOOSTEE6p\nmvUwjIGeyJefWry7H5JRmO5/NT49mcql98zK3dFCuDNPogNxONe04k8XEiMb\nGBsQYL/BaQDvk3d19o4lbRnYnUud6fxNlswYgz43ctC4rpKMW600m2tsbXZ7\nuqeBHHEY5lpbvlQClDL5F8nY4kNvLys+b7ztcoq/NnFze3iIbH09SBUVIpES\noB0yaddYe+bwBT8LDX9VBrTYzz1jgFqhG8ed3TFu1z02u5nhzEeavCmreZNe\n5ZUSkHhrSG42xZVgv3VYp0ZbnqlYSGD+AlKco+albgWBgEjfoSrwuA4bqIek\nFu2Y\r\n=X/8v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "87271cb2a38c7a9e554bb4e56d738c1ccb8aba52", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^2.1.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^6.2.3", "minami": "^1.1.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_3.0.0_1586898175867_0.7915681521185163", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.1": {"name": "zip-stream", "version": "3.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "cb8db9d324a76c09f9b76b31a12a48638b0b9708", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-r+JdDipt93ttDjsOVPU5zaq5bAyY+3H19bDrThkvuVxC0xMQzU1PJcS6D+KrP3u96gH9XLomcHPb+2skoDjulQ==", "signatures": [{"sig": "MEUCIGexhIdba4THYQ6/ESE1MJ/dGk/yHVFkYkGKU0QOmPRTAiEAii/miTZLrsKl7ycoOjOd4PpM9CnHQvvHVaoLqzYHUrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelijHCRA9TVsSAnZWagAATs4QAIVKy1vM3bnUe2MHlVCy\nbSXAJ2ZftK3P8/dvzwrwGQmK+EWkg14qdZLNNp1fOyteQUANjF1W9L3wgphY\nkoxeAPl0pS39HsC+XXhx5mHiJH68UN8NETnDqCcVbEgCVCaBKNhWlgucHTwL\nicpmWJevwO+s0d0eZjSjlg05PXAA2V7rnvhZM+0dWq4dg8WHhtY0/EofjV0i\nLE8/APf2lTjYbuWRszSueYjuwPAEP+tQP/9X7ahQxa1FZte+pKYaoUvw8wrh\nNWMuGyaABLzh7jBi7OMFQg2hjkfGuBaQbkyt7RqCNCz3jHmb4UflZTQ3hGvo\nKZQUkPfXDsq7MDj8+NmEFNbX8wE+BCkmij/aw5Zu7PWn+3JasbPaqQT7Q39d\nkFWiYPViqDrHGUui7COOB1oFmA+Txek5EkDj4B8RkiAh/Ys9tCywVHLSfJLc\nkaNdD/mUz0GVjXm489PqlKdb0ErlhKmjlJsf4PxsUceYfVdBcKI6/FB4/qZq\n5qsmUx7LbwnyaLDET0CEMaGaspJkAs2deB48Z1S5VLzHR8hpsXKd5KXYwmVF\nZ8+s/fp27zvyzRnF0Z6vtGcPm0a2Wl+Pg3hf7CaZrrZFo1Q7NtUydfQbCddf\nAWF+WsWortNgvXOYmyVcaRFYUzeI3QroKU2oLbwJPhE4F2uvw03PZOMrrkHw\nbAFy\r\n=KzKM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "c2555a1301af69448eb668889044d8be1ce4724e", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^3.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^6.2.3", "minami": "^1.1.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_3.0.1_1586899142738_0.7680704633184041", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "zip-stream", "version": "4.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "b545cc4efa8645ea47b9aeb25b49bd282c135582", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-JhB4OLtlalLz1jXW7o+HcpEZA0LMVL2sumEaCJKrbRyug2z3Ki1xuqLg1yGUsnFMFdLmOkbwT4a67UavcgcThw==", "signatures": [{"sig": "MEUCIQDgOsTwXBileKAtVavDn6UsO6s2SpzDOAh8VOMsMNJTFwIgPQLNGmxBWmE67ObAqlrRapmXl2Ceq00Ab/X+hAjgCe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEzxjCRA9TVsSAnZWagAAd54P/iPUcSHFpjBpERElD9BV\n8irlK9+E5dGv7BjgxLatkKi86z/lbJ9HdFaM1q8AxRVC5njpc6R2ju2lup6n\ndX/694BgUyNTiFXEYtSRr/UZBxy+zLr46P9vXniiXubXeI+ptautNLoz5EkU\nhjg5IAEJ407ZwHf1Nk5ydQjdX35ZP6g2CL455c4zu6zLnhzj9J953sjVDfmU\noprxTwYFaXj304OUm03XMmp5g7CDb7jsFsWQLyC5FP6LCkt7m0w3/diMI2JZ\nBMagzZYBQ0cNQn6X4UbqVmYxzIiQNmAi08WDyrWe/kLBJGj9fEFwEGVtNDXd\nD4ucX9GbUJoDRVXu7wnGMPP0D/drSbPeHGnvA1/VlUNnsR475CocIOOBXaEZ\nP0+a3qFbzdGY2kZUs997cZjX84ywg7NLNCrxwYpU18DexnNuReQ/hPvocWhQ\nbZHxrl/ZDC4X/eMJfm8ojzmUedcUKDMw8j+FpqApEElu7b12WafIBr1QSS08\nL+yqSq2Ip/TiK/g+e9gIFlBc05N+LgpY0jISF87z5KO0vGpEmOniiRVmTDVQ\nGnID1uvjwRSEO7nLgH9SxbplxJ0z+J1GJK9v2G7hrlryBkqtXyeAOWNN+S0O\nODNEzA4goryRZu2BTwoSyIuf0wakY+k11ciARtNll08XVBXfLmwtnQu6UdNA\nQhC0\r\n=5SsJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "29915539f7aff3477c173892a12acc578c60c677", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^3.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.0.0_1595096163202_0.5919363861060891", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "zip-stream", "version": "4.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "f5ccc7208cd305dcedbe4027a50d0d53614b4fd3", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-139r5Y11dZXFRB2p9EPSe2Lcs/tIc1qgdpVXMfMFMmd5+YW86AVFTub25dymsb/nghD0RZiJfyqnm/v/OLlNOw==", "signatures": [{"sig": "MEYCIQCNV2hQhir+GpI/l9p58hQjlzQB4CR5fGziL3WAAfaLbQIhAPPX1n31Yn8XSf4IWeaIdpYhfv6Iho7VuAjcKgiv3RLK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFlF8CRA9TVsSAnZWagAATmgP/2SIZdcFqVai7A/AcLXf\nOEjUjb7C4ti0Pm0rFxe8SCUClUHRg4B88b7FOPHuliuk+xvMDFpRpt2t9Uas\nMD6laf9zfghWu5uff5dHNRwRw1ZvIOpLfmpFKH679pSU2SuSKt/+elSizW9t\nJdzpVRQNujCL7loza+y27MZktMxYx5Wiw9phTFDNY2A8eyyfcLsWBkezhIvm\nMY5gyTvy+0iXKqi5va1rBXiJOB08UiDeZcEb6z1RsIyDERMyuUNntJpuRxJC\nwFELT7+DDLqAOZcD5E5n6/VjvP9JY7fY7ePoiN0aycM6qjreck/8Yfetqc+N\nKYKTm1WtesgAT9Zg3ZQIACbRxBUL8zICtxnaAjNOP7i86EAYCXEVLNUiK9jJ\nVqleF5HQPJyutYDB4XI51epZ5pYYUo8jNpntT/zVS6JyYCDQBagx0woiIxUs\nVsb+iW5EwR5ucbliQX2fUpFDmfIYNte4zQoNKpy6yUtQGeO8+qI2/nBNWf40\nc8FwbVrgsbHtG9zqmU3zIvMdSBKYD0IbQIZ4+2ChqURyR3W8gSv9h0WfXTId\njIipIJaezT0X0rslEk41ZrMN03/W7icoNnXDBtN40CTf6TIl11czbzyeKyjj\nivL64VolemhlbbM/2S82b2YbWDN7Jk1S8YfaCzxJ4OzSQcRbBJlNvie9NIAU\n0IzQ\r\n=y3cS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "ea718e1455e3bea997b5d2f07174d86ceb794a19", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.0.1_1595298172002_0.10053333960502808", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.2": {"name": "zip-stream", "version": "4.0.2", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "3a20f1bd7729c2b59fd4efa04df5eb7a5a217d2e", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-TGxB2g+1ur6MHkvM644DuZr8Uzyz0k0OYWtS3YlpfWBEmK4woaC2t3+pozEL3dBfIPmpgmClR5B2QRcMgGt22g==", "signatures": [{"sig": "MEUCIBLr09/Jt1WrNzixUnwQs8viyJssooVP0qfCTxDFOGlBAiEAhw7yVYrxotvMnNcS8K5B0cz53wBmGWqNtotv9sStm9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFlScCRA9TVsSAnZWagAAmZ8P/R4oH++nNxZAtELIYkz3\nQqtQi+TiZAuhFqokgQotJGqRbvFxIsv7NXnhsi24pLMtlIgQbG7JQomeCHOJ\nnuWURcEHHKCpVdxzq9yQM8GkQrqICCe+zmsNFkTMRBIIfZNc7a1a+uZRMTPK\nq60iaPrRhXWvYQE1qoqRFLd2N5SA1t5dKqxxwqKEPwt+p6cpnS8ks9H5Ugvr\nBAfRn3m9fjuTeQ9nZ+TY1fu7CGg9+VecVC/hydGFzefRC5xP7/4psQ1y9QPC\nuMlup6mn/AfaMToDLdUI3XKt3IeRuuUGqD7DyAOYI5EONd4Tdc/HZFaClNlM\nPl3yCchDVs18vKqcz+qTbDfr8ga9gSxGSPD9kSuw3P/45YmOJNGZ8E1nbCUm\nNFufaA8ECA+zkcwJTcEKMGijFe6Lim3ES+j9dZklc2VnTTe+jckZrqyJlZVN\n2qP5ej4NYjq1M7xPz8YnKs+DTNERiHU6FB/W4arbrLq/BgP5U4eqOY4is+/i\ncBac5+LPWIlEHVdyhtJYopNV0Bcjieeks3oaGgBvhb7h4hj8QnyP9wxcztFF\nxptx6vDkOs7tDFF/CCceUMbAY+2Gqg7D7trJrEMAQO6bWEpf1l/XFfwh6EtM\nLH46+V6kxDVEqF6I4oM5+6DZ9Eqh1ZwueLmIRhHzGqYuIek7mLwg/sDSZTKV\nlXXE\r\n=FZMC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "14cb1728134478805591447450849952b74c63a9", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.0.2_1595298971959_0.10679006825111292", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.3": {"name": "zip-stream", "version": "4.0.3", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "3f62440b6e4735fdc80dcb8510d7385af28b5686", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-uE5SthyR1p/iso9UOhhAHSmKcLnaYyVchE39CkiSug5zPL6NYuzSY2dxpy63SbBhDRBi5Ybnuz9Of2fqyppIYQ==", "signatures": [{"sig": "MEUCIQC0rIM98PxvBQLc1/4TGClJ/A9DW6bYZg/WrOItIT/neQIgNCG4nvflU4mDrAM9/uMKAUzSJuxo+g8uXvv0hiGACNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfteLvCRA9TVsSAnZWagAA0TEP/0vfVtnsc//cfKmn5TYP\nOAj+d+cpSQ51oIGmucPiFiF2ZYPqdqR7Rzvw00J4IOkBrSOyG0ABCvSv/VkD\no5NQgP3vqhDKCdnfVz+MTMGUxVGt2vsfGUCUG19pZ2UjrWjkwAcdVRPeYCI/\naVt9aozKY44QeFDE6GAH2v3yVUAVKahOYvF8K5kZgRGgzitWhcNggGvRYS8p\ndHWZdfa2PQ0HCQUHh0Zclcg2HjI+pEQsSDTtIuXHuLTEh9Y6KWukBu5aTovk\nT7UqGRx8RR55+36g76obTkyIL2P3id+vpNZ2pNTre4knACYIGiCVcJvbVo1B\n0mzWGZzxn/5x73oNj8kfyNa9z5V0a3YqvNp2mdRZbed1UCMWCjsUErqgQaR5\nKuZVKicOyVarUuJGkk6LAiIY0a4Ks1TbuX4JTuU1uP+DNq3hskYuz+fQ2KTb\neM63/DPwnm2NWyYQK3KOX1o6DsH7Kh4mDCwjhDSrWyQm4moXCNY+IlKCzw1l\ne66LoFC4pM7b2WbKs49K3vihVDwf9I8I2NqrxtlcLrLIYamOoZFJjInniSKq\nLh8cmopG6vmc7o0ql116rhYvEmeITq56Usx5NaAKfxKERWXuZZi3WQrZ46wd\n6tNZaqtgHbmxyikrlkuUFxfB5QMscrFrFIV2iwtXDIOZ35D8wXKTmy9aEHBd\nnH2p\r\n=yclb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "21613ef2e554db863ccdf91085cc7f85f3b13685", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.0.3_1605755631260_0.3501700776503598", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.4": {"name": "zip-stream", "version": "4.0.4", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "3a8f100b73afaa7d1ae9338d910b321dec77ff3a", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-a65wQ3h5gcQ/nQGWV1mSZCEzCML6EK/vyVPcrPNynySP1j3VBbQKh3nhC8CbORb+jfl2vXvh56Ul5odP1bAHqw==", "signatures": [{"sig": "MEUCIGk9FL7p/OW6hz+PQgR1et8KprA3A9ftFP/wvEmqzdVGAiEAtWUXsV03lo9U1oe8/O47R8klDrt/MXZdcEPWo8fjtBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftfNCCRA9TVsSAnZWagAAMukP/RyngwIwkOIZaFMG9fzT\nSqp6YaE8izh53hHucdp/1gA25dvynuKGF2erDMZuTpUgQXOnZlHjUAt4tmc1\n/nAEgFdR+lh5WF6deQas+ijQQAe66icX3ymur0mBR2vHv5D0khuP7fQfazfm\nayWULuu+xTHxvdSM3I0CPXRW7a+zuBeckRncEU+ygAvvR8WhJ9GI4PsFuVD7\nVVrGUbeSiDF8DPhHq6VFvbbGv3ODPuOGxWujG1AXswjFsT4YSQoIDnMtvv4q\nIHf4h37CZslxjVczIckn0ZgORk+ZGLXx25wFy+FT72OgqYc9Jagsh7KQtNfG\nNFU+vt0G71J0sfdctlY5gBMXd0AaOmHeSn3rYQo06f0RUM60vwYdHmq52Ow4\nmmAdvNEp9h//OfvlebcLF6klBgynNKoRRnMQhNBlwuhWp5fvlE4tcopiWkSy\n8syh5OkI8MMALSDFpIV3ScuEWGoi/AkHVNIeBRoWa5RBuj49VLad6GBKtBZF\n3/yz1Dib2Lb8fZNHV73CNQoZRVYHTDFmp7wR44mvOt8ARLw+mOscnAleAuzU\ng8xvGsVCmWG7fMIDDtG6Z6u8pI1Ddm99cWBfJbSD85u+g/6AiEcLVDKGFiiA\nVdclcWt8To5U3izf+n9P7pt4rFtjih29XZkpTz/o1cigHdoca0jA5CpKsrAq\n9p5D\r\n=4LZ8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "9ce26404902b14c56c8cbef4594bfcd438fdd55d", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.0.4_1605759810175_0.5472169800165823", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.0": {"name": "zip-stream", "version": "4.1.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "51dd326571544e36aa3f756430b313576dc8fc79", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-zshzwQW7gG7hjpBlgeQP9RuyPGNxvJdzR8SUM3QhxCnLjWN2E7j3dOvpeDcQoETfHx0urRS7EtmVToql7YpU4A==", "signatures": [{"sig": "MEYCIQC0q22JgKo/ARRW63T6OubDWPV6EC0mo1X3B1p1ZY15lAIhAIEC69TsSADMvsHtzDAawZVZLyFbxI/MZ+AeMfUZFvlh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPx8pCRA9TVsSAnZWagAAAlMQAJl46AhsXawYCgty6i/K\nelP8+Lq+dgPCuu/qTlabYWhfpX0s6y8kTPPX7lnAR+kQS5+cBwl09N6gV1MB\nZDr6J2NR42cdx5Jua6qJ5e035i93ThnR/ORuZlukrJo2fkgk+EpmFB7v2S5p\n2Qoi22Qi3R2BacksEkzEACjTXyipaLkdHgkq5J7UMNvSBSMDHSlSjurGnbm5\nbBCEfxGD5hXmfmLZDi+i++LKFH5nK/dQzl//blBHRlFZd747bYa+of8FV8GT\nVwUiU2irK7EWspMiFBoBWLXSgz2k4saoRtAA9MH5/2Oy6+af50b40t+A56yA\nB7Lv/7VRvSIilYeR81KqdY0cM1oBn5lnLP0Tu5Yx9OLtBIKNAoqyVwCnLbDO\nZ9aNDKwqDelfIco3PFHnXPduqNimOAQPeoE0bZQfZWRxV8znDiqZulxSJJqh\nQdF+D2s1HgTmXoFNYxO37+cefeZ9IfM5T4RIqqMg9cPb3ubyCbI11bMyWpvz\n7kotHrRG82/qMS0XeZL7hxC9xjzNaI6IZhu2vDrQOqkEAVhy+kstrW2OJaTU\n+bg2KzR/n2YtBpDc3pY+i2yi6yxtVWKU/uMNy1oYQSES2O8ZoIyrEu0mE6Xq\nTQDPSaWFYx2RiP71Sgss1W5rhIH0IeQjhoz76NOIHuM2zsiWr8gR0Eb+E5ql\nzmt1\r\n=GKWa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "8e4fd96dfc61ae7d69db584587ee7eff0c3117ef", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.20.2", "dependencies": {"archiver-utils": "^2.1.0", "readable-stream": "^3.6.0", "compress-commons": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "jsdoc": "^3.6.4", "mocha": "^8.0.1", "minami": "^1.1.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "archiver-jsdoc-theme": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.1.0_1614749481124_0.19641033321661294", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.1": {"name": "zip-stream", "version": "4.1.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@4.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "1337fe974dbaffd2fa9a1ba09662a66932bd7135", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-9qv4rlDiopXg4E69k+vMHjNN63YFMe9sZMrdlvKnCjlCRWeCBswPPMPUfx+ipsAWq1LXHe70RcbaHdJJpS6hyQ==", "signatures": [{"sig": "MEYCIQDbH/5jrOtEUW/R0hd051VfZHoFd2wDXz/KeVoLLltjogIhAN30utpBHCXxVVS/hED93ld3ywLANpyKJ3EHT0I4XW4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13742}, "main": "index.js", "engines": {"node": ">= 10"}, "gitHead": "189475437c6ce7c275c470a8fe8466dd168f009b", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"archiver-utils": "^3.0.4", "readable-stream": "^3.6.0", "compress-commons": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "jsdoc": "3.6.11", "mocha": "9.2.2", "minami": "1.2.3", "mkdirp": "2.1.6", "rimraf": "3.0.2", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_4.1.1_1693695709759_0.34936977988903206", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "zip-stream", "version": "5.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "ac4527d81bff5baeb33e641fbfbd1ebcca34b890", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-Kt1NCLviVx/PLyN2DsRdiNJ+ebOEiAdhU1dAZ1Ledl+9nfAG3zJFx2Z5DXLoHn9oxO7fXbGEXPi4gEcSM9Rq+Q==", "signatures": [{"sig": "MEUCIEaBb3onREVIJ3sFjT1VbvDELpOMF1XxhhUY3QSxemTpAiEArZcrJQGlQm/6eiVJk3YQI4fRsiDcFoFqFln+1QvVCns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13872}, "main": "index.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "5fca026428e37ff78378a222d9dba2e8e084ed3f", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"archiver-utils": "^3.0.4", "readable-stream": "^3.6.0", "compress-commons": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "jsdoc": "3.6.11", "mocha": "9.2.2", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "3.0.2", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_5.0.0_1693699895019_0.14098401697890606", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.1": {"name": "zip-stream", "version": "5.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "cf3293bba121cad98be2ec7f05991d81d9f18134", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-UfZ0oa0C8LI58wJ+moL46BDIMgCQbnsb+2PoiJYtonhBsMh2bq1eRBVkvjfVsqbEHd9/EgKPUuL9saSSsec8OA==", "signatures": [{"sig": "MEQCICl+bUEuiTrPtpnsJ3uUmsqrOt105/cq+7HUlOlOiIviAiAaX2aC1v1PWW1LM0i3y76q5WDVUIgYWtXR2uW6Nkqa0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13998}, "main": "index.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "00aff33413f291b62074a35f3115e927d3d72b36", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"archiver-utils": "^4.0.1", "readable-stream": "^3.6.0", "compress-commons": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "jsdoc": "3.6.11", "mocha": "9.2.2", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "3.0.2", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_5.0.1_1693776642617_0.8282003649010492", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.2": {"name": "zip-stream", "version": "5.0.2", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@5.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "77b1dce7af291482d368a9203c9029f4eb52e12e", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-5.0.2.tgz", "fileCount": 5, "integrity": "sha512-LfOdrUvPB8ZoXtvOBz6DlNClfvi//b5d56mSWyJi7XbH/HfhOHfUhOqxhT/rUiR7yiktlunqRo+jY6y/cWC/5g==", "signatures": [{"sig": "MEYCIQD3F368/2PTqbkqz+8nhJ0opw1m4Wzw1OyDq73uzYc62wIhANZxbTiN6ZgvIwTVSx9MUXNRTfFgxFq/Q0/uc9ERrvPW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14124}, "main": "index.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "3b6ab62ab90eac042cda53819a3ae3d2fbef92b7", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"archiver-utils": "^4.0.1", "readable-stream": "^3.6.0", "compress-commons": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "jsdoc": "3.6.11", "mocha": "9.2.2", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "5.0.5", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_5.0.2_1709015201862_0.6241951769558374", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "zip-stream", "version": "6.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "b2514478a5f37b63c823bb5c3ba0f19b20e5d98b", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-X0WFquRRDtL9HR9hc1OrabOP/VKJEX7gAr2geayt3b7dLgXgSXI6ucC4CphLQP/aQt2GyHIYgmXxtC+dVdghAQ==", "signatures": [{"sig": "MEQCIGGGwvcVDQZ7vAOsWnvCukyvqlL4CBIvkEZJKb3Coq7OAiBE21rUmvehZg8Barbv9q+T3AG1BSt9cDI+K6WSpZtOpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9145}, "main": "index.js", "engines": {"node": ">= 14"}, "gitHead": "de27ffbc6422ab313968a75be3ed32f114afba67", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"archiver-utils": "^5.0.0", "readable-stream": "^4.0.0", "compress-commons": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "jsdoc": "3.6.11", "mocha": "10.3.0", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "5.0.5", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_6.0.0_1709047883143_0.13114128799828118", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.1": {"name": "zip-stream", "version": "6.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "e141b930ed60ccaf5d7fa9c8260e0d1748a2bbfb", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-zK7YHHz4ZXpW89AHXUPbQVGKI7uvkd3hzusTdotCg1UxyaVtg0zFJSTfW/Dq5f7OBBVnq6cZIaC8Ti4hb6dtCA==", "signatures": [{"sig": "MEUCIQDraQpNxi9BtEF+JNHjWbpCIfrNhHNrOTBlNJUMHIdxZwIgTfX0YPqf//pKQQiYKABoDGqfnjNWlBWzPNUxdvNQguQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9144}, "main": "index.js", "engines": {"node": ">= 14"}, "gitHead": "d2669793807f869da07b38e3e53a088e470702e2", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"archiver-utils": "^5.0.0", "readable-stream": "^4.0.0", "compress-commons": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "jsdoc": "4.0.2", "mocha": "10.3.0", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "5.0.5", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_6.0.1_1710036213967_0.06725927864953518", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "zip-stream", "version": "7.0.0", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "af0ebdc7f3566564e1362eb16cac9c458c635eef", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-Ykb3VvI1Sa1fxDSGUDL+8/G2Ja4TriBpXtlxZjRlRM09R+pvMDP4SBsvHJPptJ125wlqCWuUUc9WPqL1Nw6neg==", "signatures": [{"sig": "MEQCIHuWA0GlJXs8Xv8phhSep7itMzVhpqREGh6FvR3tjL5yAiA6cvQCo0V3sZl2cVBWVhN2d6hyFhNjvwtkyk6M5XgXGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8860}, "type": "module", "engines": {"node": ">=18"}, "exports": "index.js", "gitHead": "bb8a66531c47053688bc8db5278cb4a9fd14f4b2", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"normalize-path": "^3.0.0", "readable-stream": "^4.0.0", "compress-commons": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "5.1.1", "jsdoc": "4.0.3", "mocha": "10.7.3", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "5.0.10", "prettier": "3.3.3", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_7.0.0_1729050517007_0.5866015204110335", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.1": {"name": "zip-stream", "version": "7.0.1", "keywords": ["archive", "stream", "zip-stream", "zip"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "zip-stream@7.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-zip-stream", "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "dist": {"shasum": "e172d297b708fa9516b08316fa68d075492c1a5f", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-7.0.1.tgz", "fileCount": 5, "integrity": "sha512-kdcmiel+FWPNhiQgcVnd7neAG3BtxQswBu33EidJxEfaRudi2T11xBYapPlOc9i4h6WSNe1HO4DTDiBS4KAhRw==", "signatures": [{"sig": "MEQCIBaPP0J0/JZ4k+OInG3iXsjBBnFpEGXo2f8vn3DXKgphAiAvVS8NP/qEhT7b2eAOAm7FdjDMW3qUl2ye0ItEC+Vctg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9332}, "type": "module", "engines": {"node": ">=18"}, "exports": "index.js", "gitHead": "8dcf08f10cb787f70228ebb31796f73ce55fbeae", "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-zip-stream.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "a streaming zip archive generator.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"normalize-path": "^3.0.0", "readable-stream": "^4.0.0", "compress-commons": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "5.1.1", "jsdoc": "4.0.3", "mocha": "10.7.3", "minami": "1.2.3", "mkdirp": "3.0.1", "rimraf": "5.0.10", "prettier": "3.3.3", "archiver-jsdoc-theme": "1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/zip-stream_7.0.1_1729053875138_0.9906304156052377", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.2": {"name": "zip-stream", "version": "7.0.2", "description": "a streaming zip archive generator.", "homepage": "https://github.com/archiverjs/node-zip-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-zip-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "license": "MIT", "type": "module", "exports": "./index.js", "engines": {"node": ">=18"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "dependencies": {"compress-commons": "^7.0.0", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "5.1.1", "jsdoc": "4.0.3", "minami": "1.2.3", "mkdirp": "3.0.1", "mocha": "10.7.3", "prettier": "3.3.3", "rimraf": "5.0.10"}, "keywords": ["archive", "stream", "zip-stream", "zip"], "_id": "zip-stream@7.0.2", "gitHead": "5dc0f9a5a8584cbdb292645f9bcf503da234ffd7", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-g1TjcvzTXLWwDDyZSdC+w7tNdeNCq/qA8Amm8kxGBldyW2yxtSHHlYinxTRvlcaE4Tt3l1ZPsWSA+P9sn20MRw==", "shasum": "a9b654f7cd82ffb6edacf3285bf1bd7fdeb9537f", "tarball": "https://mirrors.cloud.tencent.com/npm/zip-stream/-/zip-stream-7.0.2.tgz", "fileCount": 5, "unpackedSize": 9334, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIENBY/AQf9pkAf0Rs/yOM86Ju3AVQc5gTSbIsxcr9JtDAiAcKylVgutcAItj0KhDTtFngUEOHDPtYyTwvEepOjHW6w=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zip-stream_7.0.2_1729054078417_0.2379205267621538"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-01-06T00:56:47.242Z", "modified": "2024-10-16T04:47:58.833Z", "0.1.0-alpha": "2014-01-06T00:56:47.242Z", "0.1.0": "2014-01-06T06:00:01.728Z", "0.1.1": "2014-01-10T14:25:59.800Z", "0.1.2": "2014-01-10T15:40:39.976Z", "0.1.3": "2014-01-11T05:14:37.025Z", "0.1.4": "2014-02-08T00:52:21.023Z", "0.2.0": "2014-02-16T04:51:18.379Z", "0.2.1": "2014-02-16T05:18:48.599Z", "0.2.2": "2014-02-25T22:13:42.021Z", "0.2.3": "2014-03-30T14:02:29.465Z", "0.3.0": "2014-04-01T16:44:29.612Z", "0.3.1": "2014-04-15T22:56:19.969Z", "0.3.2": "2014-05-04T03:39:46.560Z", "0.3.3": "2014-05-04T06:34:09.426Z", "0.3.4": "2014-05-26T17:04:18.633Z", "0.3.5": "2014-06-27T09:10:47.686Z", "0.3.6": "2014-07-01T20:49:39.963Z", "0.3.7": "2014-08-11T03:19:01.142Z", "0.4.0": "2014-08-24T11:52:25.260Z", "0.4.1": "2014-08-24T12:15:03.453Z", "0.5.0": "2014-11-30T19:35:28.660Z", "0.5.1": "2015-02-15T01:24:41.363Z", "0.5.2": "2015-05-20T15:09:55.244Z", "0.6.0": "2015-10-17T20:47:57.251Z", "0.7.0": "2015-11-24T22:21:08.356Z", "0.8.0": "2015-11-30T19:42:00.087Z", "1.0.0": "2016-04-06T04:12:37.040Z", "1.1.0": "2016-08-27T18:54:43.961Z", "1.1.1": "2017-01-13T00:29:45.066Z", "1.2.0": "2017-06-16T23:20:53.308Z", "2.0.0": "2018-08-22T22:18:22.841Z", "2.0.1": "2018-08-22T22:27:54.461Z", "2.1.0": "2019-07-20T00:09:55.165Z", "2.1.1": "2019-08-02T16:07:01.393Z", "2.1.2": "2019-08-02T22:58:06.452Z", "2.1.3": "2020-01-08T23:45:32.796Z", "3.0.0": "2020-04-14T21:02:55.983Z", "3.0.1": "2020-04-14T21:19:02.880Z", "4.0.0": "2020-07-18T18:16:03.353Z", "4.0.1": "2020-07-21T02:22:52.134Z", "4.0.2": "2020-07-21T02:36:12.076Z", "4.0.3": "2020-11-19T03:13:51.379Z", "4.0.4": "2020-11-19T04:23:30.304Z", "4.1.0": "2021-03-03T05:31:21.293Z", "4.1.1": "2023-09-02T23:01:49.964Z", "5.0.0": "2023-09-03T00:11:35.176Z", "5.0.1": "2023-09-03T21:30:42.798Z", "5.0.2": "2024-02-27T06:26:42.044Z", "6.0.0": "2024-02-27T15:31:23.294Z", "6.0.1": "2024-03-10T02:03:34.117Z", "7.0.0": "2024-10-16T03:48:37.165Z", "7.0.1": "2024-10-16T04:44:35.361Z", "7.0.2": "2024-10-16T04:47:58.655Z"}, "users": {}, "dist-tags": {"latest": "7.0.2"}, "_rev": "3518-67514ed370f106dd", "_id": "zip-stream", "readme": "# ZipStream\n\nzip-stream is a streaming zip archive generator based on the `ZipArchiveOutputStream` prototype found in the [compress-commons](https://www.npmjs.org/package/compress-commons) project.\n\nIt was originally created to be a successor to [zipstream](https://npmjs.org/package/zipstream).\n\nVisit the [API documentation](http://archiverjs.com/zip-stream) for a list of all methods available.\n\n### Install\n\n```bash\nnpm install zip-stream --save\n```\n\nYou can also use `npm install https://github.com/archiverjs/node-zip-stream/archive/master.tar.gz` to test upcoming versions.\n\n### Usage\n\nThis module is meant to be wrapped internally by other modules and therefore lacks any queue management. This means you have to wait until the previous entry has been fully consumed to add another. Nested callbacks should be used to add multiple entries. There are modules like [async](https://npmjs.org/package/async) that ease the so called \"callback hell\".\n\nIf you want a module that handles entry queueing and much more, you should check out [archiver](https://npmjs.org/package/archiver) which uses this module internally.\n\n```js\nimport { ZipStream } from \"zip-stream\":\nconst archive = new ZipStream(); // OR new ZipStream(options)\n\narchive.on(\"error\", function (err) {\n  throw err;\n});\n\n// pipe archive where you want it (ie fs, http, etc)\n// listen to the destination's end, close, or finish event\n\narchive.entry(\"string contents\", { name: \"string.txt\" }, function (err, entry) {\n  if (err) throw err;\n  archive.entry(null, { name: \"directory/\" }, function (err, entry) {\n    if (err) throw err;\n    archive.finish();\n  });\n});\n```\n\n## Credits\n\nConcept inspired by Antoine van Wel's [zipstream](https://npmjs.org/package/zipstream) module, which is no longer being updated.", "_attachments": {}}