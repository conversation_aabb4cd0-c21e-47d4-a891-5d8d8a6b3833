{"name": "http-cache-semantics", "dist-tags": {"latest": "4.2.0", "beta": "4.2.0-beta.1", "next": "4.2.0-beta.2"}, "versions": {"1.0.0": {"name": "http-cache-semantics", "version": "1.0.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "c9f9238f3e4aec9fc4b85140dd1b86975069e5d6", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-1.0.0.tgz", "integrity": "sha512-YG5oFYOJ7BozRq1HqRgUt37qQnnD9sUMmyv7+iTizfQo8lxnj4rgQc4nfSxMCeGScSaBVucwzIBjVGDB/6p2oQ==", "signatures": [{"sig": "MEQCID6VWwzFzG1uuSCJYEdt0W+Xlw/OhQpYmv28V15yto6mAiAPCfAmlH/o3AdWewjP7vXL2Xo3llwMwZB12N0de/ve9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "2.0.0": {"name": "http-cache-semantics", "version": "2.0.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "8852f4a5049d0e80e566bffb645f57d37900162e", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-2.0.0.tgz", "integrity": "sha512-N7xTqNoLe5lLsqjmENuc8ij86GbLbTPFxe2Gvo4Q0tLG0avsBORgiPhdaIYd1wputaEhwYRUIAMemE0tlECrdA==", "signatures": [{"sig": "MEUCICDz6zaMu2CbLrjXPvSjKnzhujkyiVVRhIEBvrOhvbAEAiEAi4U/gQISUdFN7mbihbkMZdPGn7UYq/LUvtq/jrgfQbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.0.0": {"name": "http-cache-semantics", "version": "3.0.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "aac9e9b024350356e4bafddb7df10423680fdedd", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.0.0.tgz", "integrity": "sha512-A0Kd6lnDsFOzxYa6V4Wu+1fECW/K+IYV/zivye7WYnWJQbfne7fkqQFiut33vHn8ZV5uC/UTdgUiPYxloaJJ4A==", "signatures": [{"sig": "MEUCIF7uxrZBcr/aVOdXlMCeTmBN+EZf1W/Pk31wuglnv49lAiEAmqfjBURcAZBB+ccAv1pUtLrosdF4Vltpf5RvCLAYzME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.1.0": {"name": "http-cache-semantics", "version": "3.1.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "a6809724811910664c6666292159c81908bf3918", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.1.0.tgz", "integrity": "sha512-Up3SbTBhVljDpJv/+NYv2uMuQyllzgISTdIwvGJEOlPGNdFi04yFnEJocoP899E1b5lcVyKmRNas4WkbBRB19A==", "signatures": [{"sig": "MEUCIQDjSs/jgtq6zjxiZDnk59oSYP34IGqXPPjMxuckw0r4AgIgO2BkKRO/Rsep2jqOI6x00XDO+ZHPe2pDKU/6XsZHGQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.2.0": {"name": "http-cache-semantics", "version": "3.2.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "ca6bdafedfe84b8ac7561d9a9a069415da69a6f1", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.2.0.tgz", "integrity": "sha512-y3t6nEIt6GsJVZM4VEcAd1+Pz59YKayv3+do6Q0yo/4TNIW3gmi1H6/dHoYCHaHA0fpTBxSn6GqRnbvG1SHXNQ==", "signatures": [{"sig": "MEUCIAmzRVyuzWKk/K4rZ7Unn8Ke0sBwo7SOjWNdiwOFmp7cAiEA+cCvODxfVI3yymefk/jCcXKOh+vWeqex6/lhH8NW5ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.3.0": {"name": "http-cache-semantics", "version": "3.3.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "a88e57092b8bf57830a3546a091499bcc30f39d9", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.3.0.tgz", "integrity": "sha512-nqZFVId0D/bLYwdvQuQ16fu4UmLLFzPuhd/KWyT+1F6Y86c25wZXCv59DFllSDydgM9Jfq8Bhr99tkVPK5T4Bg==", "signatures": [{"sig": "MEUCIQD1YL2oHsQoBncz8M8zASItlHY8sOr4PfG+V9EjEGtEWAIgOUymRr+0NcxCg/6XoTJ0PocqBXWanoBwm2+Bc+gpaeY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.3.1": {"name": "http-cache-semantics", "version": "3.3.1", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "6d66768eefc6770e24cb67d623523037db40a7c9", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.3.1.tgz", "integrity": "sha512-TrE6EMPKguXDQxQMVnWvYVMOVx7KtODzye1DcH2zza3Y/iDY5YVlSusHhQAAprwd7bIAdoUF55w7ng6qRrTxzg==", "signatures": [{"sig": "MEYCIQDTsJYB0Qaxkz1oLMTjV2m1VY3GgT8aYn4zCyrgL2xuggIhAJ4U5B1Q3JA/CpJGOJ1udiGdYlFlIvKBh0EEREeUezCj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.3.2": {"name": "http-cache-semantics", "version": "3.3.2", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "7e7ad369228813be47b1497434b360d76a48d3fe", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.3.2.tgz", "integrity": "sha512-yKnYBVRaslVRzq0pKPTmb5YtASw8wbmo/8E8LhoRky8OmvUtMqh78g0QwZ5vTaggkqkeU3mgDgPrXc/3NAgjmg==", "signatures": [{"sig": "MEUCIDyrt9x9wICIbT3UJQBktiZmWUKfV0BNSErjaVAnARkbAiEAh1nEQpBGYeur0GW8btcos8Ytl25tcN0WDysmQZGJlFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.3.3": {"name": "http-cache-semantics", "version": "3.3.3", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "f0b8a549e7259bd3990886b14bcff902b2f8c731", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.3.3.tgz", "integrity": "sha512-LHX2S9eVwRNlQauQYgOhQ4xBG6sPp7YGWHYsHSNV94dgSJ7RxYCO1CDvl+JdeQ3V2XE1FKoq+qVH3Hz6k6KIWw==", "signatures": [{"sig": "MEQCIDegr7tIpuV5xHv38rzwr5YwrXH39xa/0hYtgPl2zaCUAiA3D30k4xKiXszA4JecxYqxXeCYnSDwkK4o4o8AcR/nFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.4.0": {"name": "http-cache-semantics", "version": "3.4.0", "description": "Parses Cache-Control headers and friends", "dist": {"shasum": "e6b31771a6172640b97c5b9cecd38a071385f96e", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.4.0.tgz", "integrity": "sha512-IgjF6wFoUCRIhU7vD4zxuEFOzCta17PAvAiAkoim6sVY6+Injtw7FcMr0LhurvXlgxrjoR+KdXtW76TkqoJANw==", "signatures": [{"sig": "MEYCIQDy8gFsrqO78Oyh5WB+7+uZYoHx2wfOl2eye/Fq0O9r9AIhAI13M7+GLGLE57ECgQq4nBbVr/sMMdp8qJiLaYJV7Dcc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^2.4.5"}, "hasInstallScript": false}, "3.5.0": {"name": "http-cache-semantics", "version": "3.5.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "ccdb954be509e386e301766ad89aa041161b7b14", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.5.0.tgz", "integrity": "sha512-xPV+K6HcE6apwcMgAFrcfDyx2xQSWRb4ZRMko4tQ+saZqOoCCy/zB63eHaH+C0e+Z/5O2Hp537wx87HhFV9F3A==", "signatures": [{"sig": "MEUCIQDDlhHsvjII2MXyrWwKTCTWS08FNsLA7rS8LZ52ALaa7QIgPhW4GE35se3FMpVqyvl4bp90wsx9LxFjdaDiMQtvBrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.5.1": {"name": "http-cache-semantics", "version": "3.5.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "6b91e9f183671db99e4506fb41f12da9e89da679", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.5.1.tgz", "integrity": "sha512-5LwRvYJFru82+5PTBA9/V4HcVMcDm21L0YPOkp6BocL5cwWKtuuxPxFSrOSJ99jopCLQlOlH0+sm8Y2KV/kSsQ==", "signatures": [{"sig": "MEUCIC5Rcdj7/7KS11Lbs4AtiB1Rl29bv0K177J9X85T9fDZAiEAuOsc3i9RGC7QaV7qbceBuv8IgZiNStajCd5B37ENw0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.6.0": {"name": "http-cache-semantics", "version": "3.6.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "bacbc1697b53b5c9381c4ed226a60f57cac4cee2", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.6.0.tgz", "integrity": "sha512-WQ++x5agkxmlfnl4sJoX9WhT93MNM739i4JSTPbpH+cCYA3OzKM8o/ow9RWv3zXgXRHdxkSTvKbPAYyUR+NDlA==", "signatures": [{"sig": "MEYCIQDsqL2Ye7Y4dvzpuFcYqfQMVCiPOE8ikCy8cQvS+s8W2AIhALBJ2KYSMDRkuCo+eqop+ah/FtKh0zSgTey8bkXJHSTy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.6.1": {"name": "http-cache-semantics", "version": "3.6.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "9d10aa3d70d8b91fb31dd0d8b2903d97e1045d3d", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.6.1.tgz", "integrity": "sha512-SePGiU+jK91vGI4CdDABjQ9/6KcHQr8L5vljIBiL28ZfWznj6ZTPlSOfwh6GlsoTQYFpLQ4lldMTPzT+Pg9big==", "signatures": [{"sig": "MEYCIQDB9qrDI+XnBTsRGkdx/r86RFDdUQdKPBx7xV6J8wanMAIhAPR8M60feX7ixXJSZHU5tuj30H4MNATbRaUPeSpHmtOb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.7.0": {"name": "http-cache-semantics", "version": "3.7.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "d7b0e325f791c4f44d385574cbc3e6fbb883f7d2", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.7.0.tgz", "integrity": "sha512-ElUFlFZtoB3sTregxQ7aNadZKeFCofwXZIrbZtcQasbKPXQurNuFqU2riL0Cz73lx+IrUBNo7KweTObN+oso3A==", "signatures": [{"sig": "MEUCIETY1YjT0HERLxEGKLCbZxGmoCbOykz0VsgGxF9VFx3zAiEAogUq7u11ruUm3sy02zE0Z7/HEDNJeQx9efMsipDiLh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.7.1": {"name": "http-cache-semantics", "version": "3.7.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "1419405bb48ae5ba709ee554e657ff9caaf2f940", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.7.1.tgz", "integrity": "sha512-ev6T7BQpGGydPXyazmZ6jGOaXpTcDQi2Az4oUeq3HOxRcf3tjGS1jRtBU8zoQ+ZrAsnXfK0wtTqzo8d/TbKGew==", "signatures": [{"sig": "MEUCIQClZCnmQCrShaZ4B9UAeXxRBSz7UeaYdYQxeFfbpHlP6gIgPo+PBtPNV7pYMujaA4DvNW9jKgiydCmcB78BLWfgNaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0"}, "hasInstallScript": false}, "3.7.3": {"name": "http-cache-semantics", "version": "3.7.3", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "2f35c532ecd29f1e5413b9af833b724a3c6f7f72", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.7.3.tgz", "integrity": "sha512-OUh7WWLxe9wzlisiDVNwclT/hKU1+wl4zYhPHoYoLmGMc0rsNb10ZrVr1gaG6m343kl6zVlCKBWqtheN5dEyaw==", "signatures": [{"sig": "MEQCIFrOGK0mSpT927/h+OLY3oT6QBbdQfbRrISncASSXrsxAiAN2bMy/43b9xpEtOv7iTalpqZmA0ufN4cU5QK1F1g2KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.2.0", "babel-cli": "^6.24.0", "babel-preset-env": "^1.3.2"}, "hasInstallScript": false}, "3.8.0": {"name": "http-cache-semantics", "version": "3.8.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "1e3ce248730e189ac692a6697b9e3fdea2ff8da3", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.8.0.tgz", "integrity": "sha512-HGQFfBdru2fj/dwPn1oLx1fy6QMPeTAD1yzKcxD4l5biw+5QVaui/ehCqxaitoKJC/vHMLKv3Yd+nTlxboOJig==", "signatures": [{"sig": "MEYCIQDstYF0/mGt++IYNNqX23REvBBgAa67VgM3igaKV1E5bQIhANruh1ukvlL1euYaXP9VrOJEuN0MzODiFh9jkxgUSLYh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.4.2", "babel-cli": "^6.24.1", "babel-preset-env": "^1.5.2"}, "hasInstallScript": false}, "3.8.1": {"name": "http-cache-semantics", "version": "3.8.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "39b0e16add9b605bf0a9ef3d9daaf4843b4cacd2", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz", "integrity": "sha512-5ai2iksyV8ZXmnZhHH4rWPoxxistEexSi5936zIQ1bnNTW5VnA85B6P/VpXiRM017IgRvb2kKo1a//y+0wSp3w==", "signatures": [{"sig": "MEUCIQD5Hewr2AiUCtlwgqyfIJ1k9/DAuneHP+TdnOCEnk93xgIgOOQsoZUZoIX/8O+afjvJcXTfFn4zC9MzOQsZpAuHI2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"mocha": "^3.4.2", "babel-cli": "^6.24.1", "babel-preset-env": "^1.6.1"}, "hasInstallScript": false}, "4.0.0": {"name": "http-cache-semantics", "version": "4.0.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "2d0069a73c36c80e3297bc3a0cadd669b78a69ce", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-NtexGRtaV5z3ZUX78W9UDTOJPBdpqms6RmwQXmOhHws7CuQK3cqIoQtnmeqi1VvVD6u6eMMRL0sKE9BCZXTDWQ==", "signatures": [{"sig": "MEUCIQDulridXss9RbczLJ2kPETrb6mMU3iJnAgAJqTGIwe9swIgGOnvN9TEvlQdL4htdoAXMREDiBCVKnIMc/bs5vbU9gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2195CRA9TVsSAnZWagAAVwgP/iCMlOox7b3Yq01QoT0r\nUw4pB+UpZCvEnL+BiXIWdJqbSkGbXQkrOv4KcwIHo307eN5W3A1B7kwoR4SE\naZzohfUnAAnsT3UyUTVNsVMIVKfntV5OdtYRlIDZ9HBUGZ3z0j9QkqoHvUzF\n0Hc+odH7CGNVLEaFKCOKn50XXhkcRxaw1lJYEIZJNz9U9W8XAA8pGMma33l6\ngAel7ipDkwO4rYrmsGb+JZkQcpogb1jy1RdaDlNduNtDDrwN1lc/P/Hw5G6q\nxSadvFicQTOj/C55n+fPYoCFEqLf4sWbIIG3r9oe4+4Zm43uWUuh6Np/DIOX\neuX0I4om7iIOpsKczbSOdpOryGLiis71pb9IfOq3cfAGRxe5RULbuv/UHaO6\nLkbtYSGijLdXNS9L2zGGLzTXMrTDDG4ETvOPVDy4va48mk6Kz1Gd88sJUA+W\n1H6HFsPGi3fwm4CItR3eL28qSbJz5OY6Xib8bnUTpZ0pgIp8n1YjSTroakd0\nHIwJSaO6CLigGDwqEsz6K3nZkSYN6HZgJ1fCy7FEA+rAS4/Rr4bu34FKi4Rt\nMj11kWUmlqD/41lP5LxlXORlGF7Gj0C4Wbbcx92fMDfELDwT4DsFP9oX8Sen\nNaLMvatsJQk6JEVasKdtQxTxuL0umpMtSYd5ajkGeZmHytIS5/b8bsjCtari\nYYR1\r\n=mOpf\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"mocha": "^5.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.1": {"name": "http-cache-semantics", "version": "4.0.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "6c2ef57e22090b177828708a52eaeae9d1d63e1b", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-OO/9K7uFN30qwAKvslzmCTbimZ/uRjtdN5S50vvWLwUKqFuZj0n96XyCzF5tHRHEO/Q4JYC01hv41gkX06gmHA==", "signatures": [{"sig": "MEUCICblYrFmLrkKGRXSuxye6l8b66tcTpfUXPhashGeZ7shAiEA+gFuGolx0tiCy/OBYEt+fdP+T2baZFo7z9OCk9RAGOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/SP0CRA9TVsSAnZWagAALiAP+wdeQjQ4BZ+NC5x7Kp26\nctPgdVjh575yYGyPhtInKJxVgaHJm6oHdhn6b7yEzRVfpPUdcsh3rCYq6UcW\ncv/p+Zr7hQRAII7LH6qr4yBPrE7qn0jfHAcwZVUDUxHzZrXb5Bye4xTNKYek\nUIRbUvlBP/egqJhC3lQyyDBm4UwR/XyzYZaB/y0kNrpKew2S+L4fUeeTrb9q\n8d5ey+kbx6KZOmidhypIXgIKikJOy98Bf+qFolQ7KkCpyevgIUojktkqZjxQ\nOdi5i8qoQUxGdM+PZT3HvxOjqfT0F0BtTXZxSv3/V0XOGTEkVYKCDG84EuXV\noPL54wkwtQJWVQR5C0UBv/hlpoQFkE5MLf5II6QbeyF0fRy6cs7yAk0uLq7Y\nulGCptnTcnKWYSLm3j+qBaVkqRd6JPVCyv3EBwILPJxqELVQDCIBXZ94TNtn\nsz75TER7h3z+8Yj945Ujirgq0XOt8i/hhYaf9o/If/ZHV+UOLGFnPmP9xpnJ\ndC6wBduEHi+UW8Xe/WiIhOR0thuIjIZJD3i651HIml4SEaA2APSE1F0fiA/Z\nf0av/Wh98cm6nhPb4lvhGyj2TDn/Eo2AYLXNhxggKrhknFaQoTvm6L5A90Qg\niSwzFjPa+YVb8oJr4UfK19M28qH4AVRB5ctghTc7pqN3wQ07Cn04g05nkqtE\nT/IF\r\n=TOZa\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"mocha": "^5.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.2": {"name": "http-cache-semantics", "version": "4.0.2", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "5144bcaaace7d06cfdfbab9948102b11cf9ae90b", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-laeSTWIkuFa6lUgZAt+ic9RwOSEwbi9VDQNcCvMFO4sZiDc2Ha8DaZVCJnfpLLQCcS8rvCnIWYmz0POLxt7Dew==", "signatures": [{"sig": "MEUCIAKKVQp4AbCw6mdjMdU3TD78l6mSNtLntzNMTaMEjQIOAiEA07wT9JCHMl7y0N9dn6f1y88H4MEsH5CrJSNi22hKmxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNf/SCRA9TVsSAnZWagAAetMP/jbicp5p7Gu0Q1Yz6Hk0\nt8RX+kW080CkFmGYdpXYHnSl9H6Bjfl636Vp9b2FYpa/fUyztD5Up4PL99W5\n25L5wX/mQ7/r52VvpQccoPtuhjNlm2/9WnssmT5CuussIlwD75v80UGMeEBm\ntQjPJ3Wc4P+/q0seqHY8QfUq2MuQ8aTAvULNIe5K29KdhErTP9UBwC6GIkL0\n7WOa7w7F09zYF+XglWU1IGGywWsC5ni9wkWwClZDIrslnzQ/eI0qMG93IzZD\n0QqgOqq76kR+8+BxRdzUnxSjWsr7TtrISCGRhVPgZnUA7c1kL5zwov3rqwZr\nltQwQsJfsoxB1Hp2oDZfPImlFk6kBPcNzq0EThiJmgR2pLy0y1ONRa+npw13\nJ+j1vuKRqSy58eBhTHIU4Ucmd5cMkzbV5XTMfLGzqOioWGcnn8yUtRoY5lB5\nTH1GBIkEHyte2RQabZMvG4JduHt6tbWaXwmACmEzOy8jtID9qF39IppeEtm2\nr2ub2YxlyJ3qyqjdEbldEBtgpE1jXgc47J+v3A5mZ1w+S/tA9CVkSbiPoalV\nOVmqhnNecvu6WLBLx2TYmCPNr8j6o9Ti6h6PNyyzMR3BkermEueyttEg+Lw6\ntUu4H2QJ3txOwDweDP2DoH6+7VJ3522cVUu/mkVpOfMpVhaEauH0SsIoOzPs\nT63T\r\n=IW82\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"mocha": "^5.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.3": {"name": "http-cache-semantics", "version": "4.0.3", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "495704773277eeef6e43f9ab2c2c7d259dda25c5", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.0.3.tgz", "fileCount": 4, "integrity": "sha512-TcIMG3qeVLgDr1TEd2XvHaTnMPwYQUQMIBLy+5pLSDKYFc7UIqj39w8EGzZkaxoLv/l2K8HaI0t5AVA+YYgUew==", "signatures": [{"sig": "MEUCICo1/hwmGk1B9/tGsDwQs8Qu38isAVal08mwk49hJM3AAiEA4wJCrIIldY6TMp4QOPoZt+YJarGg1wdl9YhGetK9R08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYBekCRA9TVsSAnZWagAAQhUP/0k7JbEfBIcpBeH1T2Ar\n0mwvV0MRR2JU3pb8VnED00PgH4NnzjtSD+csbqvNsaQPW3Rbf+vU1MKLXwjE\nuwxPazMiyhtAvLRbQ4AoZuhSI0jU1OOTlxEJ2lf1DRLYNbuL4hha5+CLzAQu\n88D2IMDFeAy8FBwh4Ly1D+RmvoKVvW5PMcx8LQVNSEdKQLxz4i/f/4sKTlCb\nM2ZNCPFcFwDG3FCseOzvEvWHAcDopAsvEe7FlUx91xnc83yV6J6f7zq/iuqA\nF3HheCJOoW2CtlA1dzMpVDBKHLqHkikwAqFJiynb2wmR5z3ftvP/x5TKIQdR\nunW0Qm23lr411n+1qbPEwB+TtSkE3gw74mgTufKu2eeYDxVFTBTQqWQDBI2k\nAlsEktNCighnPvDtZTyj4iO2sxU2ARIpC+gM4hm5Jj45Du3S2wfXotzZg9aH\njKhSYAlxel531VmNiNUMIyKxcZ0aFZxjp/DinDK09lJzKiT9coW9hxjU+F8c\nts3742BJstA1n0HkL19BZbJc2deCozMRcFNlu4KRut3dIqGplH4Zk+NlIZdZ\ncL6HXo9czy3Suo83G0katAgmYYXXmDFBOmtl/FM9f1QmHGp1+sZ5iWXG3Tl/\nnBn1XMfKbXUy00Bmde/Ci+VMilNjnBu26hXgQvwrcZc2KoHOZHDH5ArfVcdF\n5mkN\r\n=Hp5l\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.1.0", "eslint": "^5.13.0", "prettier": "^1.14.3", "lint-staged": "^8.1.3", "prettier-eslint-cli": "^4.7.1", "eslint-plugin-prettier": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.4": {"name": "http-cache-semantics", "version": "4.0.4", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "13eeb612424bb113d52172c28a13109c46fa85d7", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "fileCount": 4, "integrity": "sha512-Z2EICWNJou7Tr9Bd2M2UqDJq3A9F2ePG9w3lIpjoyuSyXFP9QbniJVu3XQYytuw5ebmG7dXSXO9PgAjJG8DDKA==", "signatures": [{"sig": "MEUCIQDBvMTyOtO5BFLxvzNGCzND5hLpHmol0D4u9FI5f8F1egIgDCqimRQQKYLvACWZDfrmrWFoDWKY5/BGbMlvDQVpACE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQdYoCRA9TVsSAnZWagAAwKkP/RhDB2PKVqOrP/A4hhAQ\nAzmTOV3jAOQIXOFfK4AB47JtELdqRBMozFfXdvSqX6lXxYRwYiGXCar8oDST\nqpGNMu5xy4tZC83OsFAYlzK5OMK+7sHINWQ+X2ty4+ZgDTDx1WF3Y2zpI5jG\nu8ACS5i3J/fIrsgnLUQvuMBhuNGmg31IrNHGlt8ZyGKTuC9ZpnwCwS5cUu8v\n+uXiwBtdLDQyWMhJZItND+iuhbiLVICzoCnaKY/SamVIXaczg55tCcMI00G2\ns1y0JkO9zcVFEkjTzIB1G1ivf+wGRL12ChPyzdenxcESd2heRlAEBOxHDwuK\nalbUHWdOZZl2KaZc8WH3D396FZNfQNWVK0jyooNBKr3/jWrA8oPmY6+tH7KW\nGmkUNkwKk/dA8MjY9GmG8zZs29VXfLMO9YwQSaviVRfoV+ruUqye30fimiSk\n3qo1I4VjIhzAa3R5v16TnWOUx/spgMrQJYyTPYfp/hTOQeQs1itY1rVME1ui\nSLb/6itSAdzVvPxIpUDMD7N9mQOL09CuvDhAGOYgOaYJym78JTCLzp/Fc3MK\nV8B6hmOhJWd0/s0kV0C5nqWixvZqkJ2HsJuVwd70LMrXwfDyRWQYEkkY1nWJ\n94b3SeZLhlPap1IJrijsDZF292JI1JPy7KBS9K9VL5J1ZF+5HyipfcGAczz5\nC7o1\r\n=CK+n\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.1.0", "eslint": "^5.13.0", "prettier": "^1.14.3", "lint-staged": "^8.1.3", "prettier-eslint-cli": "^4.7.1", "eslint-plugin-prettier": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.0": {"name": "http-cache-semantics", "version": "4.1.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "49e91c5cbf36c9b94bcfcd71c23d5249ec74e390", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-carPklcUh7ROWRK7Cv27RPtdhYhUsela/ue5/jKzjegVvXDqM2ILE9Q2BGn9JZJh1g87cp56su/FgQSzcWS8cQ==", "signatures": [{"sig": "MEQCIDx/oVZc5L4YTCjuuf+auSDvzeLKp47NgOxJdWghXGtdAiAnTHNRJp+ESI6n3LixIU3mP6hbazLFcyRM8bWHHJthlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXEtPCRA9TVsSAnZWagAAzA8P/A3TCoWR9tWontLwY1Ea\njchpeX/hZAmzO91LrruXgQ0A5a7RH72q/nGQDxnCR6o/3ukv08sAb+H3jG1p\nxqz0euvGSBipxQXFei+V5lkxmK0rDZX9/Q46wXSE/Ja2ZoGeQAMHmsDydFN5\n8Z3Ebqx3Fehx5++tgy/qn1eqUw5YOfuC4J96PXeamqbEkJmrMWywJ74fi+6c\nU2QRK6UlN6eiAmMpvOgviOEjAPYuKs7a2wyckBTwlMNaBRxgzi/gsJjnNLbV\nxrOnxOWiCkNpv2xqN/tbueBz8JzDGCrXY2nTrLBeeEY3QwIkXGnAbpbZ1jfE\n7w42nmQYkW1e+BCxiezUWoQp+Gl16sREcTlCke2cuokED34shNf2mweNWgUz\n4mH+9zPc9mcc5aE6Odk/QKscbe1p0gFAhJ8QyrA8oKk3CZApyR5ZNcR/lxjr\nN602I5IA+JMsAxQzV4ohHEs0gkK+0M00GqP0fEpoon7o0qdZgTW/LhZ1bw+7\n41intFH10aXPn4mmPsMXN1svMzC8V6mx8ZqMal2YsKufMb3xOaGddGV6WaVE\nGr9iSkFXbtIAmbCMN0vs2qT8dB6gWFTkWj9wqGxS5HsFuE/dEy84bPeSlgli\ncSWDov6PNMUEkbTcPhHMbsDqN+GkQhrNB2FSLA1p6exLzikTttQkjtDyKfVR\n9MV/\r\n=1tny\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.1.0", "eslint": "^5.13.0", "prettier": "^1.14.3", "lint-staged": "^8.1.3", "prettier-eslint-cli": "^4.7.1", "eslint-plugin-prettier": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.1": {"name": "http-cache-semantics", "version": "4.1.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "abe02fcb2985460bf0323be664436ec3476a6d5a", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==", "signatures": [{"sig": "MEUCIGygvN3Se0zxQ/mTxHNc2vU4yMZ9gjqZ5Eg074xkMbAVAiEAlQ2uBmUo2WnAkHmlZ9RWL4swb0m5SqfSyDDgvOwZIcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0yjnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7VxAAkpq0uXaMxwzz6Wmq1wgvIbx80vTxTRni9wrPU/0YAJHtsU/R\r\nUcZ/ypDzvJaSSrdjUXZcWhb/Md6Gfzq89AA4aqo71pzRTEMAu/9LFAU/NFRR\r\nWc9RrzC9LyRM+Sn6qaCQ2pBPyyqo8O2hVtbuWaHuL6XNk0HpCNkBfhEJsKM5\r\n49vZYiGEXvmSlHY2VQ0GSbNcvLyIQ28pjC0mOcdJ/l4OUqSzcXpxmYPzoNLq\r\nL7yVEKCpdykEt2INBA+G9Px3ixmc6HsCme6z967gC33dKnOYKtewCquHhMYo\r\n6dublLQ8ulGSkJTnknqC3dx5yzh2I4dAe2TLFFrDfpN3jLShaHhjkJTQ3omc\r\nukNmNU41dyJyLj/IwXvcyYy4Ec6dB59F/ctnEB6Fg8xES3AYrYSF1bqV4D39\r\ncRS8k+N4bq5l+2v7yuOnkHb+qAAgCBcC3qsYHGwwXPaXCKRwxhErqOrQCBl9\r\nmICS9tm8Ft6+y17DW7Mzr2lhdwiHJAepUxpVgSPf3JOSwdrAYrNU/KW4b58B\r\nJxUPeS9KXfCzjxhXNVt5o7r7hyobkNU1UzpapO408C4Sl50GyRxXYL27r7Ps\r\nhodtirKJSkzYSxamQmwrrQ9AdD9QYKEXIxJ8UX1TN9tSa1Xpysbe+Ggt6Tob\r\nV+CE74/nWjnTlfg39VSdnYPU1eXCyZTJmzw=\r\n=hHfC\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "devDependencies": {"mocha": "^10.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0-beta.1": {"name": "http-cache-semantics", "version": "4.2.0-beta.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "0c9284ac4118646cff303c69f04f95c1ef05dc65", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.2.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-B1i8U97RQid/XhsLa94xPxwDqNgroICdxLaENBcNJ0+o20SLzQ7ODTcO1Hny8S55hCqlD1VnLrc8hdBTgjKbQA==", "signatures": [{"sig": "MEYCIQDMS9SGw+HWD2mcku63z4aFN8WRttxH8rsupvqfgfjOaQIhAMfyaHDZgQSfSbqz2dLFJQ0jFx9SRNz0w/31A3vZ695w", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 41627}, "directories": {}, "devDependencies": {"mocha": "^11.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0-beta.2": {"name": "http-cache-semantics", "version": "4.2.0-beta.2", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"shasum": "64d68ec08d4ea65f771a172e117f4891d00901e2", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.2.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-aG2vtbcjyrIlepLt5Jhnu9eL41caPUBxAa/8cyYZ/ZhTjHI1u3Gjri/XJbGV5edew1R3Uo2+39b7TaF3ML57JA==", "signatures": [{"sig": "MEUCIBX7RPD/pk/oFUSUeNzFE2vUl9HOsFOiSISQm47NhhRdAiEA4yv2/yT7bgIz8h9jhjmutGM+GYKCkEETHZYjUXeYVyM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 49931}, "directories": {}, "devDependencies": {"mocha": "^11.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0": {"name": "http-cache-semantics", "version": "4.2.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist": {"integrity": "sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==", "shasum": "205f4db64f8562b76a4ff9235aa5279839a09dd5", "tarball": "https://mirrors.cloud.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "fileCount": 4, "unpackedSize": 49973, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBDylgJy1goQjk/YkybVnYDfrvUnbHzz8tdqOJPdjynvAiEAxYJ+Q4Vb6vXYQwFoq7tZTzjUCqQpCwlWDDHZYHcFisY="}]}, "directories": {}, "devDependencies": {"mocha": "^11.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-05-09T13:38:12.033Z", "time": {"created": "2016-05-31T09:18:22.371Z", "modified": "2025-05-09T13:38:12.033Z", "1.0.0": "2016-05-31T09:18:22.371Z", "2.0.0": "2016-05-31T11:43:36.739Z", "3.0.0": "2016-05-31T16:12:09.950Z", "3.1.0": "2016-05-31T23:24:25.659Z", "3.2.0": "2016-06-05T12:52:13.188Z", "3.3.0": "2016-12-08T00:52:31.311Z", "3.3.1": "2016-12-08T11:44:00.624Z", "3.3.2": "2016-12-08T12:01:36.963Z", "3.3.3": "2016-12-08T13:40:57.678Z", "3.4.0": "2016-12-09T18:18:31.748Z", "3.5.0": "2017-03-21T20:21:13.644Z", "3.5.1": "2017-03-21T21:12:19.930Z", "3.6.0": "2017-03-23T13:16:37.192Z", "3.6.1": "2017-03-23T17:21:42.476Z", "3.7.0": "2017-04-01T17:20:53.954Z", "3.7.1": "2017-04-02T10:48:02.423Z", "3.7.3": "2017-04-09T11:36:54.801Z", "3.8.0": "2017-10-12T14:33:28.299Z", "3.8.1": "2017-12-01T12:51:19.985Z", "4.0.0": "2018-04-21T15:57:44.396Z", "4.0.1": "2018-11-27T11:01:07.864Z", "4.0.2": "2019-01-09T14:06:10.008Z", "4.0.3": "2019-02-10T12:22:59.672Z", "4.0.4": "2020-02-10T22:16:08.024Z", "4.1.0": "2020-03-01T23:54:55.142Z", "4.1.1": "2023-01-27T01:29:11.933Z", "4.2.0-beta.1": "2025-02-03T00:45:29.967Z", "4.2.0-beta.2": "2025-03-19T11:17:03.197Z", "4.2.0": "2025-05-09T13:38:11.855Z"}}