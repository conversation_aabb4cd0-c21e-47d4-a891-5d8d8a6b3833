{"name": "crc32-stream", "versions": {"0.1.0": {"name": "crc32-stream", "version": "0.1.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "51cd0c0966dbfe471ed8f3689234a75288af5cd4", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.1.0.tgz", "integrity": "sha512-RPGPdd41uWHL3tZvJFgPhzDXN9fqDNJh8JqcZCEiUpX7UhCrI64I8SOLeJ+oASYYBxhkgnZ9x1d9HFqorGNuvw==", "signatures": [{"sig": "MEQCIGiRZyTOyaQRAq1F+1L+iMc45rF08rZ14ga5iZa9mhG8AiBf39ETi0CJv7Mhh1R5v2q5Y9sMjoUoVm8/CmqolawpWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "lodash.defaults": "~2.4.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "mkdirp": "~0.3.5", "rimraf": "~2.2.0"}, "contributors": []}, "0.1.1": {"name": "crc32-stream", "version": "0.1.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "06ab698d880fb033ca9f0ad34f2564cb5bcd9518", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.1.1.tgz", "integrity": "sha512-eEwrxo86F5JgNI0SV/WKr3A/KcqE6Hw2VVwbhhpqp/88oMZnhzxfAJTByaJfEmmEoSzqy8jmYkcdmRS7z8/1xw==", "signatures": [{"sig": "MEYCIQCqqNI0x8m+kp/XAr0ZJPmQ+mhOePrwwDJ9G4nJaEL9fAIhAP/inGSam9YKk0RViBmClnbTs0fgMecXUZD/gVjEJ8uz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.1.1-1": {"name": "crc32-stream", "version": "0.1.1-1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.1.1-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "fcfd1a4a082cf53a657dffa208f545d6acd87905", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.1.1-1.tgz", "integrity": "sha512-fKZnAghCVPxZjvFVomyxYN52u8aDPCBfsonu0EBz+bcOb2IzGX1aZ2jfsGx0K87rWuEEq3p7d5/7LL85FOIk5A==", "signatures": [{"sig": "MEYCIQDdxK0DNbNR5v99+xSKNNd8N/8TLeUlD9XFwm6ChiandwIhAMt3zL9Qe2plV0Wmarp7EZpVEqe0oihRFOsVczSmFhHL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.1.1-2": {"name": "crc32-stream", "version": "0.1.1-2", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.1.1-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "57ecfee0fc38d939e9c3ec376b1b1cd81896956a", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.1.1-2.tgz", "integrity": "sha512-emGh2awWynlYQtfL+/h4TA4hoscRiPl/Ggb0K4Vo5k/7lT0piGCP53XVN356Tw0XDDjmjF3W40cWIutLNiyK4g==", "signatures": [{"sig": "MEYCIQD9/V2t62BZK0ZNFfPxPQsHfvmGWe81yWu+NsRBxrenegIhAKdd0f3AhRxUHqm25GFltdcRVWf/a5vxO0eRC8q24aN0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.1.2": {"name": "crc32-stream", "version": "0.1.2", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "162f2ca7b1092c271f795f461709ccf4a3f43caa", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.1.2.tgz", "integrity": "sha512-C3Th3jafTIEWN444zZWn1mva/2qlgRZp7W+7s7Rh1j+ml9eQ90u0DXA6GVN08Zb5wl7u3KoB/QustIEQkkNyZw==", "signatures": [{"sig": "MEUCIEtCK6GAbn4Y+N0f5JFDiGOgFErnpP8896m4qDsgbOHpAiEAg3HPwHLZJxdlNo4VoC5LrJZyuV4QQpidalJ1pK7juzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.2.0": {"name": "crc32-stream", "version": "0.2.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "5c80d480c8682f904b6f15530dbbe0b8c063dbbe", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.2.0.tgz", "integrity": "sha512-fw+nCqnfNlrqy5OEAkg56NOIgsogrLGWFX0oyFxBHALVpqkijPnjV1FUq/5SgbnyCbfJPxKk4PVqTkui3IG1uQ==", "signatures": [{"sig": "MEUCIGTloEVRjNEYbOHI7ngMvbE0UxdVgAQnHum2pAQ8BBRNAiEAmAQcaUnPMovoeB8HzPW9q2G5XuxuzPoKkKvSvj+OwQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/crc32-stream.js", "_from": ".", "_shasum": "5c80d480c8682f904b6f15530dbbe0b8c063dbbe", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.3.0": {"name": "crc32-stream", "version": "0.3.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "6342a0852543c847c04e0b17176ca35fabbd9b40", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.3.0.tgz", "integrity": "sha512-AwzhnA+u4z+ekxOnSI0iud+nDkx+oFKPOJxQux5GLMyn5cETo3O9vYvl7lLMXkE8WGrhRI3mqo6K5R0QxJKHow==", "signatures": [{"sig": "MEYCIQD7VjWqtDASJLMqxdPfKmlrmWcV7iUi5Ig9yLDAG8NH9gIhAPcOUm4534vodYAbmqKW1p8/3Uj2DZpwKKDvEUIYAL1G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "6342a0852543c847c04e0b17176ca35fabbd9b40", "engines": {"node": ">= 0.8.0"}, "gitHead": "93c656c7445f7c04d276e3c7b2475f00d31de40e", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.3.1": {"name": "crc32-stream", "version": "0.3.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-crc32-stream", "bugs": {"url": "https://github.com/ctalkington/node-crc32-stream/issues"}, "dist": {"shasum": "615fcf05ed08342a3d1e938041aed84430ce7837", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.3.1.tgz", "integrity": "sha512-9CAUbTPBE565wfy+eJB44RbY0NU72ZABIvgti52EG6Y1yf8V5S4+qoZjeG2w6DSFLQ1K23dMuA6/VJMqcJNHRw==", "signatures": [{"sig": "MEUCIQC79TtIZHeEOUgPcS+aeiYx0sk172SM7xHAlyDBykQWEAIgHthNgPlRnWpxeZiJr1TwZN74IVyAcfWsGm4EfHq6Ma8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "615fcf05ed08342a3d1e938041aed84430ce7837", "engines": {"node": ">= 0.8.0"}, "gitHead": "72ed0a3d6d2737897b96a7b015b4e1cf207fa971", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-crc32-stream.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a streaming CRC32 checksumer", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0"}, "contributors": []}, "0.3.2": {"name": "crc32-stream", "version": "0.3.2", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "8c86a5c4ed38c53e36750d662784ad8ec642e38e", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.3.2.tgz", "integrity": "sha512-+qI6LCK40WFMS7K7RisD4oVF+vtQlOgimjwkHqYD5AByJyBlZiNfbCNBX4Ytx37Hc1502yuXns1b/NURBSjd8g==", "signatures": [{"sig": "MEQCIFA6tvoOEqHmCS/Qzr+n6iquJUHOR+X0wFcmQD2lNOBqAiBOp7RTZCZydcfj/LjWum6fWSVGMm+rmP9eE4SPjjuRyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "8c86a5c4ed38c53e36750d662784ad8ec642e38e", "engines": {"node": ">= 0.8.0"}, "gitHead": "5fff7a70787e28b634c166ecb4e6184ad0efca66", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0"}, "contributors": []}, "0.3.3": {"name": "crc32-stream", "version": "0.3.3", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "crc32-stream@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "27cdfad6eec97a139820e3bff2b4aaad82e85e19", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.3.3.tgz", "integrity": "sha512-GHh10q/UtTvrgTMMyc1LHEp6yo3/dfk//vCNLKEFAcGNV2JDVnEEjqdLd3QS8cd1pFQ+LMJ2YnuPbU6cADbsYg==", "signatures": [{"sig": "MEQCICe5z1zVLjlZDIap6p6YH5wK/an4EF9yPv86LzvzjI8ZAiBRads9KB9phSb2X+oFWlFp/9D7tZogjeCYeEPdfJ1eqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "27cdfad6eec97a139820e3bff2b4aaad82e85e19", "engines": {"node": ">= 0.8.0"}, "gitHead": "c66070c342fb9ea2493576e64f450914b31f84f8", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "devDependencies": {"chai": "~2.1.0", "mocha": "~2.2.0"}, "contributors": []}, "0.3.4": {"name": "crc32-stream", "version": "0.3.4", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "73bc25b45fac1db6632231a7bfce8927e9f06552", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.3.4.tgz", "integrity": "sha512-6hJjO1uXTBSnY14XjxDmK3xm9AG8KjwjJAFkVfjcR2IMu/hGz73e0KT2aEIhh9JR36jHUXuMv58xfNC8lwBJnQ==", "signatures": [{"sig": "MEYCIQCZo6Nn+XvWdX2vEIw3XnxfroFvNkrGSw9r9m6gAF+/UAIhAKCvGf46ay5KuPyf91iwq9yELZKHvkea5EvqpSIDnNQc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "73bc25b45fac1db6632231a7bfce8927e9f06552", "engines": {"node": ">= 0.8.0"}, "gitHead": "2e31a846a5bb2db3849d85d3865dae837bbbaa3e", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~2.1.0", "mocha": "~2.2.0"}, "contributors": []}, "0.4.0": {"name": "crc32-stream", "version": "0.4.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "b54d4c6eefd35b53e653d062b306edc6316ae26d", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-0.4.0.tgz", "integrity": "sha512-2uuIt6ngLnR3wQwHn8Hj2RoSiAE7mGVETAuMouvNZoxClWUcSKCJOv783jh/o1AXmMkJfj7EUo4gS/x0gXcWyQ==", "signatures": [{"sig": "MEUCIQCosYUnWtFyfxDJUar4IB64M1f27dN/gw6BYFrW6T+mpwIgQtQT9k8Y9Pq3xtyRQtVXZfNonB865tsfMmnVy/z63Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "b54d4c6eefd35b53e653d062b306edc6316ae26d", "engines": {"node": ">= 0.8.0"}, "gitHead": "1331842f2c975d417183b2f9b466aefc3b4e6d9f", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.0"}, "contributors": []}, "1.0.0": {"name": "crc32-stream", "version": "1.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "ea155e5e1d738ed3778438ffe92ffe2a141aeb3f", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-1.0.0.tgz", "integrity": "sha512-LP/XL3xovNa9MOfZCdA4j0nEMrRibcP63vVbuq4FLb2jsMKc+QS7x/qoZOPDgDRxjDC+a5hk/gaYBM5kqVSGNg==", "signatures": [{"sig": "MEUCIEIfuAN1H2dY0qb2RtD6STKvJRCkWE3LfCHSYlQ2foAiAiEA1SP+gLnR2pg7ZUbmDy2iG5nrUmzlhywNoMj2W1lbfz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "ea155e5e1d738ed3778438ffe92ffe2a141aeb3f", "engines": {"node": ">= 0.10.0"}, "gitHead": "d2e2d0baacb5a0007334c1d3ccbad0eb74baca7d", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"buffer-crc32": "^0.2.1", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream-1.0.0.tgz_1459915512214_0.3119235001504421", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.1": {"name": "crc32-stream", "version": "1.0.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "ce2c5dc3bd8ffb3830f9cb47f540222c63c90fab", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-1.0.1.tgz", "integrity": "sha512-268ICyK7W4/rqhDe1LOQIVdYk8mAi5NkASgNDyu21jdadLDTa/PZlV+N2VF8x61d8m9Wa2AzCaxk/LOVRzy8sA==", "signatures": [{"sig": "MEUCIE/MTRTSjcwY9xkP3Na/bP9ZP1RHZmwAJXiwvxBxV54+AiEA8Nd6QGKxOOl+Seo7glNcemaPJVR/OAS+1ChSyHfR/Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "ce2c5dc3bd8ffb3830f9cb47f540222c63c90fab", "engines": {"node": ">= 0.10.0"}, "gitHead": "a523c88a9b9892eaf1f8d895ce5d286cb58f31a6", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"crc": "^3.4.4", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream-1.0.1.tgz_1484267977067_0.21828423161059618", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "2.0.0": {"name": "crc32-stream", "version": "2.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "e3cdd3b4df3168dd74e3de3fbbcb7b297fe908f4", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-2.0.0.tgz", "integrity": "sha512-UjZSqFCbn+jZUHJIh6Y3vMF7EJLcJWNm4tKDf2peJRwlZKHvkkvOMTvAei6zjU9gO1xONVr3rRFw0gixm2eUng==", "signatures": [{"sig": "MEYCIQChoh36nd/xiaLzWvDgHbA8W63WYbHMHQlxoRSkEgz1DwIhALsQLXaMDiNbB7NKmA57WBWmCCcFXft6hOYkp5CZeTch", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "e3cdd3b4df3168dd74e3de3fbbcb7b297fe908f4", "engines": {"node": ">= 0.10.0"}, "gitHead": "7e5aedadc39b9aab98f4b7a8de7bac82a7a12be1", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "6.9.4", "dependencies": {"crc": "^3.4.4", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream-2.0.0.tgz_1487027750129_0.5272605300415307", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "3.0.0": {"name": "crc32-stream", "version": "3.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "2e5551880c706defb62b54c1c38f9fe077d664e7", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-YNfMEGXjA0FsUfVErKVfzPWnN3jmmVOYX8KgWA1HWpqbKb2WkFNSnFpjzoSeLqn0a9himUaUEBY7vnA++4omFA==", "signatures": [{"sig": "MEUCIQCtxuLelGX0zcqnxJltWfV2Ppn3XC0TAKvEgkiDD1AdQAIgJ7bemRcqglqTDGLDko7xxgk89i37Nz1+x/U4/mUaDts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcx37kCRA9TVsSAnZWagAAvRwP/jaBoM9YM9Lk3M0uTAOd\nWeroj9iI1x7i9y16eTAqc3sJVnMGECU5lq9bAD73muMSEt8TN9NFI6RKs5C5\nW7C4EM5BH4Nrwz2l2qTSqj/TIargw7zTj9EGjnTib6uFFXWhRkGRrOGW+uYL\nFpupeR0nLNnRcZ3Mou/qS2Wxg9GGPqj2nGEs5o2vQ/9B0aWPunZwa6XFXSGe\nYNcrQJSnb56KZRIE8NnEYuWAjYR2JiA2oFMMkw4SjCGKuuIFXz0sLWz0vZtZ\nsbVuLp3l4NFtCHsTYmx/+KDfKA0Z+O8NFlNeL821lhZ+vobEXOA0yC2G7nWd\nLy4mcpSobt3MICB9aV4HbAMZla9jWaA5CNrxtFzPZM4901s3Hd54dBhsxX66\noz9GPHhamXcaUszi+wTmOpxSQfi810uUW6COzygNSdVIle/NPyObGl9sJSns\noMY1vGMRfSy8KfHxDg/fQH7aZqFyk7VQiLoHTLR1PTZEZT0RxpkIBNZAfzAI\nFlEyTFAGNq00ZsTjYlaza8sC080TexAdUeCrOa+BKN43l6b1ekP4OlCjtaln\nK4VhYmxvjPk2ZjT/SfAVRxio107h4bX7DiXbVjKvtywAQka/gY3JBQtKSnNW\nuX1u5KBWvTt/cTsbyCD5etmS3Nekcy0t5vRqlzm6JZADmCIfCJkGDvHHHaOu\nAzI9\r\n=Id9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">= 6.9.0"}, "gitHead": "6e0066d56348a778374d57dc8d4a13cae6feb5b2", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"crc": "^3.4.4", "readable-stream": "^3.2.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^6.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_3.0.0_1556578020071_0.4053417579123446", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.1": {"name": "crc32-stream", "version": "3.0.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "cae6eeed003b0e44d739d279de5ae63b171b4e85", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-mctvpXlbzsvK+6z8kJwSJ5crm7yBwrQMTybJzMw1O4lLGJqjlDCXY2Zw7KheiA6XBEcBmfLx1D88mjRGVJtY9w==", "signatures": [{"sig": "MEYCIQCudDy7QagOZ6T4PLs8AMXtjWZWKYs5T+zHS7PdX7HrlgIhALc6LiRhunaZOublOJxUK0pc5pHLV9+dSUnZ/Ix73uFo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRFzICRA9TVsSAnZWagAAd+gP/iFY5gJoUEy19PgmhSr+\n5QgJV5td1P7oO4A8gTDDmWWyOf04AwPCfprYDUMJRfRRiPNR289n1vQ54i5l\n6lQmfREu0hUb/eBfQ7BZTu3cN6+Nyhfnlko2h+I5hw7N/KUURtQbf+8Pfmim\nRiN0cIk4bnLnqUfc/7laslRkSm2owFLnRIiYzmEelTG9K9fGRTCkQE00Z7e0\nF/BRc1AIxUYORAMvWwZ+x767FQTh+ACpTGhmCWLqEoN3rQOPbxRcCm4f0kDZ\nZCddgVjPQ0D4QvcGx1xi0K0Yyi9rT84IQgE+MIjeoapY7W5mLrRA9adtSTqt\nHK5m6+0/QBADeWNi8DTi7KEoj0sSTyaG97wLlL7AI4CBrO/J7jZxEpKjFOB4\n1EW5EQZq0z1WUDImEbgBqaSEYOYnK6iRvW6zzBuXXLi2PXIZEdnmi1SwPSQK\na6lVvz+VZQxUUuDzqJCWMXyjwJaCUMIJZBoWZ4MuV8q4q/Qg0sUs1WLi0+44\n0OYIDCqNPXXl559piwfPNah7xXJtGb5MEuDTfxanVdP6N0GpuRlGceZCFtpc\n+8J4xd6qoXJiEDb30eo18A2F021r1qUukzOn9YgfajmmFtlst+0uwTzLXnkS\ns+i1jG5twe1xypJcKCp3KfN7e9d6HLel+RSHTRMcALhkBjm7I4BFVHLQVMhy\n9PCo\r\n=3F+X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">= 6.9.0"}, "gitHead": "e8c573934fd0135088c760d986348d0e45973740", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"crc": "^3.4.4", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^6.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_3.0.1_1564761287906_0.7261181255195746", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "crc32-stream", "version": "4.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "05b7ca047d831e98c215538666f372b756d91893", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-tyMw2IeUX6t9jhgXI6um0eKfWq4EIDpfv5m7GX4Jzp7eVelQ360xd8EPXJhp2mHwLQIkqlnMLjzqSZI3a+0wRw==", "signatures": [{"sig": "MEUCIAN/WkkdrvOZyCkM5QgfQLzU5XqJnX6r1CI0DFmXoThTAiEAi3GzZpyepnvWxnTbxvbIN7pfCH0CtYM52ZEUXnJ/mNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfE2SSCRA9TVsSAnZWagAAjskP/RUeIvoXyBYF7InEwNmY\nzB/LCyG5aP7tHl5CwebdgoxZqJ62f4ElVrQ4lPfz90RVax1LLkZGyGLFFP5B\nWjN0OhroFx8TL1fh7Y25b3rhDFYjfm3a2AXOS9VZoriRWH+s6DuDJagjz457\njo9/Hsr9zv/WYrb8CfLCAKpZHe1RGKyK3muJkK63QBOqsn8rvPojHXMp4hVY\nRzdRZ2sCflFf/etGL2662LYng8Ld5gNa2Vxkq87xOeHNCf9Vw/JKttJps9/s\nyxE4Fk2SfdUCKkq6c2nNyPzK3dsM+CT1JygYPy4/wUBRuGbOPz94/pjpkQC4\nbhW1/cNeYZvYNl4ziToMjr5bRg8bjm4MNu23zvXjjymUmLMsF1Sshr9R0Y7N\nwJri+2k0AYQKffYQ2oQxG8oxnhiNvPzyW70kgu/mxeJzr6kqK00v5Skth50Q\n5G/bgPQQv3XmVtIiNvVnRg38R4xZgSX71oHV4EeH4TCL36UPdGxZ/CBRgY+G\nvnu5T1C2bOjp2DYg+xWhIhJ98toUnnR9baq6tMmhrX3U4NWMv5PPNlL0x1wb\nfGUmQTi3kK1uNRtLxF32YXtXDQrVuL0LxYDLCerzeaLtSu8MxbDSn6VQCvPN\n8icdN/4PeBpVxLAuFs8sz/r65zVtZmFwhQfd77uIULf296atpSchoBHcDFhq\nEXeL\r\n=caAS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">= 10"}, "gitHead": "ab295105a0181117e5c0782fedefea9557a4932b", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"crc": "^3.4.4", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_4.0.0_1595106449943_0.9879789307427258", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "crc32-stream", "version": "4.0.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "0f047d74041737f8a55e86837a1b826bd8ab0067", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-4.0.1.tgz", "fileCount": 7, "integrity": "sha512-FN5V+weeO/8JaXsamelVYO1PHyeCsuL3HcG4cqsj0ceARcocxalaShCsohZMSAF+db7UYFwBy1rARK/0oFItUw==", "signatures": [{"sig": "MEQCIG2qXzVBFpOMXJaxCb+3XYjF2OOkzWhZY0JPDMoeD+vtAiAGV7FHG0752tAOSrBJo1pCuIoCoahj57fJWTUpwwBkdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftdkMCRA9TVsSAnZWagAAXfQP/2c9bF92PqAJM6ybtMhf\n7FBbYvMYV9fGwKpLAThXGDkH/zqBNOosc5F5reo0bfEQBnd6jShcKXnlJ87W\n6y1UQ4ROAospgwBrHq3jxWEElEYdTng1+4ycUS4GJxMIMixRK8ZtVvx6SsiR\nFYeKK0AKoK7qpXO66FEWelZfGRSKAUUftSa7BCrD639CN0qOL52SmWTy6gTa\n8a29vuVl/XYi1d8dkyJOFQ+1QeFkzli/4iQEGoQMt9xBmMwsM5OS9dzF4YGk\nEWTMLFnV9kDGETNNvnMOVmKVl5nwIqbDLDPDpH14pNP2Lum0XcAIrUZ+YMAj\n4Y/B898Va5TXUhs8DOVGhp6ewfJF3Lqnip3Ab/e/H1VMhrli5+7s2XWSB3O4\nl4B5lzBRKMtBi11eN1TBWHLRmxJBdx2MTOYBEJdLQjqpieMj1fIRjNoAwR5Y\n20YgHfff1E2I9Zi9ofFP8kVQTbbW96tQi0a9buMc1ehNJ1enhq+pEfX8cCWL\nf1x7XRcVng/Teo386jFLS6hHZfGgI1QGWxjUGp1Wkduo0t1h8l8IZtB0hzln\nZqyr00fEmwdafD4j/eSTbZ5udqzhFQqRKjetMH3ZAApcpQ0eFztqHZpKPFJR\n6ZxXjx+EQ8sB+9H0TThwuyBJ4/AuOn01Htu90aEqzXLfT7+8hveHZcycXZQ5\ng+fE\r\n=lfEo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">= 10"}, "gitHead": "841b57e54b4777dde48259bcc2b8a53857edee75", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_4.0.1_1605753100538_0.26994729908161696", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.2": {"name": "crc32-stream", "version": "4.0.2", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "c922ad22b38395abe9d3870f02fa8134ed709007", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-4.0.2.tgz", "fileCount": 7, "integrity": "sha512-DxFZ/Hk473b/muq1VJ///PMNLj0ZMnzye9thBpmjpJKCc5eMgB95aK8zCGrGfQ90cWo561Te6HK9D+j4KPdM6w==", "signatures": [{"sig": "MEUCIQDHUM95bOJj0wkNRV83YYDmggImxtqIxlCZQndOSflzWAIgCz9ZI0uUMfI0fImCawZPrzOHNCE1lyKmzz2FvOkq5g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgG1sYCRA9TVsSAnZWagAAh50P/i4xgPi+l8J7a5Zg2tIo\n6B9nGhLWyzPs/4cO07kauHwPqAJ3bWi9q1Imu1G0TD6HhVlJ3p4wucYb/0gW\n3Li5GDp71s23PPt1rhA8p55kFOAHtIZI6zlBUDdgwXOKaowsdP9UIbryOqRc\n5uprtSCEMUbDn70jwUz8Nj4+8hIm8nCc3GK+fVWwNh/c0r2WJeUg56AvWLlS\nfJvax34d5jdlGEIIw6mDVlcxQtEkT4motqdctR7d03ezSHWdIi2Gqa5AwfqR\nzL7iQJVk0ozYBGBJa3h1lz/0akLrqc2aktRGn3lmWBrNYi6yIu5zzKgECAvX\ntInDvxWnmXbuyzOkahKNKinxwtU4HiFd+YvHhTXUQAQqW07WlBqnJrCwHHfR\nT5pyhtYb/MiMonc7bb+ifmDc1ii9yoiffqxmG0koWbBTruanPJdBOeTH54//\n+wkaKYDAJgPCGplR0Xt62HATza2DWm+2k8I73ut0BodFTA2FowibOHwN4Yav\n7bDB+dhXqU+UjmcHyig6ALQQjRLkdqyBWUaZkBou+EGnnOzjIG/g6VX5oGNV\nJr+3ZFPfnlpxje9SnHEmV76amXcnAD33JUWf8OXGgTR+yAtwvbwMjvAIREfV\nc58ePZPakB+hliQxd0KDCH0g+ipr/e7vq2I9J+1WjJOunbGXqYFeTP3Hahuv\ndqLV\r\n=eYIn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">= 10"}, "gitHead": "43fc6b2ee2b1b89f702882ee68c213acb84d14ae", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_4.0.2_1612405528548_0.9781394538649237", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.3": {"name": "crc32-stream", "version": "4.0.3", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@4.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "85dd677eb78fa7cad1ba17cc506a597d41fc6f33", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-4.0.3.tgz", "fileCount": 7, "integrity": "sha512-NT7w2JVU7DFroFdYkeq8cywxrgjPHWkdX1wjpRQXPX5Asews3tA+Ght6lddQO5Mkumffp3X7GEqku3epj2toIw==", "signatures": [{"sig": "MEQCIGZrEv8wgNqqtB8WVdtMNq6vCKZgg2ptL7tM+oi2ccwMAiBakhpUIT52g1gLXSEmJun8zS8w+GQk2N1nig+fM0SvdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8937}, "main": "lib/index.js", "engines": {"node": ">= 10"}, "gitHead": "8fce55382663ba6aec4839998b5880a6610df4c5", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_4.0.3_1693692885958_0.45581100110459394", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "crc32-stream", "version": "5.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "a97d3a802c8687f101c27cc17ca5253327354720", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-B0EPa1UK+qnpBZpG+7FgPCu0J2ETLpXq09o9BkLkEAhdB6Z61Qo4pJ3JYu0c+Qi+/SAL7QThqnzS06pmSSyZaw==", "signatures": [{"sig": "MEUCIQCN+ykxJJ+BWibzSd3a84ag7TeGLj7bCT8xz49MZF2wEgIgab6HqtjmmVsslxX6Nw49MSe8djqjNANnsHh2OWYbS6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073}, "main": "lib/index.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "a5eef871f4f16bee43dc182687d21144b6e2df8b", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_5.0.0_1693697538104_0.7681758397758092", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.1": {"name": "crc32-stream", "version": "5.0.1", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "bc1581c9a9022a9242605dc91b14e069e3aa87a5", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-lO1dFui+CEUh/ztYIpgpKItKW9Bb4NWakCRJrnqAbFIYD+OZAwb2VfD5T5eXMw2FNcsDHkQcNl/Wh3iVXYwU6g==", "signatures": [{"sig": "MEUCIQCCH8SIOjPbjL1J2p5svR45bo0gij0SqEQYocGlh4Zf/AIgViBlDYIqBEv5rHQcHj9EZv5nLLW3ujscP0so9s1WLYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9205}, "main": "lib/index.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "efbb815623981d936d0ad09696c0cf529293a733", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "mocha": "9.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_5.0.1_1709165124001_0.3059268991660571", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "crc32-stream", "version": "6.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "8529a3868f8b27abb915f6c3617c0fadedbf9430", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-piICUB6ei4IlTv1+653yq5+KoqfBYmj9bw6LqXoOneTMDXk5nM1qt12mFW1caG3LlJXEKW1Bp0WggEmIfQB34g==", "signatures": [{"sig": "MEUCIC8yuEUMTUgkgMzheW+DmTCHFYiAck0hpZ9lkCMsRhNSAiEAwsvf6YGe+W2A8+YnvS8115BJPQJeeHowkFrUSwOOf9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6598}, "main": "lib/index.js", "engines": {"node": ">= 14"}, "gitHead": "2e9e384b5a47fdd6afb41df9069425f99bb68570", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^4.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "mocha": "10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_6.0.0_1709166459438_0.7559236539875644", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "crc32-stream", "version": "7.0.0", "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "crc32-stream@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-crc32-stream", "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "dist": {"shasum": "f8630d972b4c36211646ecea7946a6bb61acc55f", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-AmoDf2Lqvp3Eyq9LQszEcIMqPVNogMQHgSN1NbV70S+8NbDstYKW61ujNrmjiCgNpJHNpZ4bnOuC3yD6SUo7Vg==", "signatures": [{"sig": "MEUCIAlOy+9K8N3Fmoj4Erfaj94L6eKqc9N+NKkX1ub2hGhYAiEAsjIqZXduHPiInhtMA11g5C19SNDsJdbVwDdJ5ZDac6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6316}, "type": "module", "engines": {"node": ">=18"}, "exports": "./lib/index.js", "gitHead": "d797b1d6febc3af36a998589c3528b86f58e9d97", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-crc32-stream.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "a streaming CRC32 checksumer", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^5.1.1", "mocha": "10.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/crc32-stream_7.0.0_1728852066046_0.9935453603113669", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.1": {"name": "crc32-stream", "version": "7.0.1", "description": "a streaming CRC32 checksumer", "homepage": "https://github.com/archiverjs/node-crc32-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-crc32-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "license": "MIT", "type": "module", "exports": "./lib/index.js", "engines": {"node": ">=18"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^4.0.0"}, "devDependencies": {"chai": "^5.1.1", "mocha": "10.7.3", "prettier": "3.3.3"}, "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "_id": "crc32-stream@7.0.1", "gitHead": "58ecd525468bee292d28a41337fcc9b9f2462f61", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-IBWsY8xznyQrcHn8h4bC8/4ErNke5elzgG8GcqF4RFPw6aHkWWRc7Tgw6upjaTX/CT/yQgqYENkxYsTYN+hW2g==", "shasum": "e573ff657b789e8b5a131cb298f731d7d12e0f42", "tarball": "https://mirrors.cloud.tencent.com/npm/crc32-stream/-/crc32-stream-7.0.1.tgz", "fileCount": 6, "unpackedSize": 6148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3LPoyObTcjEt1f0Wff91fkacC6xOouH7OfJv4DMaUIAIgEfIn9y1ofl6zuz0wJA7HFQKG0lPvsqLUQg/3ZIFGg7g="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc32-stream_7.0.1_1728865194521_0.057222983037656716"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-03-30T17:20:32.021Z", "modified": "2024-10-14T00:19:54.995Z", "0.1.0": "2014-03-30T17:20:32.021Z", "0.1.1": "2014-03-30T17:48:50.341Z", "0.1.1-1": "2014-03-30T17:51:45.972Z", "0.1.1-2": "2014-03-30T18:26:44.848Z", "0.1.2": "2014-04-19T02:57:19.529Z", "0.2.0": "2014-05-04T01:36:18.325Z", "0.3.0": "2014-08-26T12:40:48.050Z", "0.3.1": "2014-08-26T12:48:46.536Z", "0.3.2": "2015-02-15T02:24:19.539Z", "0.3.3": "2015-03-25T15:53:30.841Z", "0.3.4": "2015-05-20T15:08:38.642Z", "0.4.0": "2015-11-24T22:18:35.731Z", "1.0.0": "2016-04-06T04:05:14.702Z", "1.0.1": "2017-01-13T00:39:37.728Z", "2.0.0": "2017-02-13T23:15:52.164Z", "3.0.0": "2019-04-29T22:47:00.280Z", "3.0.1": "2019-08-02T15:54:48.043Z", "4.0.0": "2020-07-18T21:07:30.061Z", "4.0.1": "2020-11-19T02:31:40.644Z", "4.0.2": "2021-02-04T02:25:28.643Z", "4.0.3": "2023-09-02T22:14:46.250Z", "5.0.0": "2023-09-02T23:32:18.296Z", "5.0.1": "2024-02-29T00:05:24.193Z", "6.0.0": "2024-02-29T00:27:39.613Z", "7.0.0": "2024-10-13T20:41:06.241Z", "7.0.1": "2024-10-14T00:19:54.808Z"}, "users": {}, "dist-tags": {"latest": "7.0.1"}, "_rev": "287-2e6ef4901894fdf0", "_id": "crc32-stream", "readme": "# CRC32 Stream\n\ncrc32-stream is a streaming CRC32 checksumer. It uses the [crc](https://www.npmjs.org/package/crc) module behind the scenes to reliably handle binary data and fancy character sets. Data is passed through untouched.\n\n### Install\n\n```bash\nnpm install crc32-stream --save\n```\n\nYou can also use `npm install https://github.com/archiverjs/node-crc32-stream/archive/master.tar.gz` to test upcoming versions.\n\n### Usage\n\n#### CRC32Stream\n\nInherits [Transform Stream](http://nodejs.org/api/stream.html#stream_class_stream_transform) options and methods.\n\n```js\nimport { CRC32Stream } from \"crc32-stream\";\n\nconst source = fs.createReadStream(\"file.txt\");\nconst checksum = new CRC32Stream();\n\nchecksum.on(\"end\", function (err) {\n  // do something with checksum.digest() here\n});\n\n// either pipe it\nsource.pipe(checksum);\n\n// or write it\nchecksum.write(\"string\");\nchecksum.end();\n```\n\n#### DeflateCRC32Stream\n\nInherits [zlib.DeflateRaw](http://nodejs.org/api/zlib.html#zlib_class_zlib_deflateraw) options and methods.\n\n```js\nimport { DeflateCRC32Stream } from \"crc32-stream\";\n\nconst source = fs.createReadStream(\"file.txt\");\nconst checksum = new DeflateCRC32Stream();\n\nchecksum.on(\"end\", function (err) {\n  // do something with checksum.digest() here\n});\n\n// either pipe it\nsource.pipe(checksum);\n\n// or write it\nchecksum.write(\"string\");\nchecksum.end();\n```\n\n### Instance API\n\n#### digest()\n\nReturns the checksum digest in unsigned form.\n\n#### hex()\n\nReturns the hexadecimal representation of the checksum digest. (ie E81722F0)\n\n#### size(compressed)\n\nReturns the raw size/length of passed-through data.\n\nIf `compressed` is `true`, it returns compressed length instead. (DeflateCRC32Stream)\n\n## Things of Interest\n\n- [Changelog](https://github.com/archiverjs/node-crc32-stream/releases)\n- [Contributing](https://github.com/archiverjs/node-crc32-stream/blob/master/CONTRIBUTING.md)\n- [MIT License](https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT)", "_attachments": {}}