{"name": "babel-plugin-transform-exponentiation-operator", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.0.2", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "96ca6e77f6add2ae49dfdfe5b786fc6ab8fc784b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.0.2.tgz", "integrity": "sha512-8SXftQVWkZrcmSE8Q24rIkwz0SZzZppdaBssUrXNgZh6eUL6oMhsTi1Z864RVR+b+yJQVMwM4sc0Tuf4H2SRUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDViI8Ri0AunMjWlI0UVWO22NGLR4NohkGxbavnDo1hawIgJ5yUK1IAEjoPu2KJ4Fg/qZvWVNo10/Sqms7P+wdT2gU="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.0.2", "babel-helper-builder-binary-assignment-operator-visitor": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.0.14", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "ce287d402e2e8c2134295e545b446e8913931e96", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.0.14.tgz", "integrity": "sha512-BPib7KqmHVaSkXTimJUwceGhAKO2h2GXm7riIGw6zatUdCMFnPAXvzzncrJWrRypuDz2AA+AxMe8Yvg4bZM4lw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDuce/XyPwseVaGQZ9v0XeCkKFGjEDZDYC8qgqkQs9BFAiEA2JBfoqjNPM88ID2DXKwB9QY4kYYoV+lqlPAELLmhnFk="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.0.14", "babel-helper-builder-binary-assignment-operator-visitor": "^6.0.14", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.1.4", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "4ac3b598cdeefc39d72639c8a35ecd21acea85e8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.1.4.tgz", "integrity": "sha512-vAdyEIRd6JIJF+cEBMt371mqBamPT0GRT9jPboJBperDdcPndUN2X6XDWlKd+vOsvdvx+FEeAFCGxKIZJIa46w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPkAZyzYlFunF+avzMONnhl2CBJTLqbAbyPiNflnSZQAiBcfgd9pYHRtTna8Nrj0OFJS/zW1QWc3pL5JdMUrzEI8w=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.1.4", "babel-helper-builder-binary-assignment-operator-visitor": "^6.0.14", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.1.17", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "b8fdab8d82d95d2be51b1cfa996602c663fde5ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.1.17.tgz", "integrity": "sha512-n4yksRYJmzZbiCapDWHLxhuAPOXgADRAevH4mzC7AeDn0uAEoxHzqAfMJg307ZFve+AyZmW7Ivk3yOACMhxvjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMudyHH5Tqs3vxpWbT2bx0f4EvXevuq8lgQgnumxSwpAiAC5QD6WHAOmyRMm1IkoT9rB7/OwsxVB8u84RSHzJX0Xw=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.1.17", "babel-helper-builder-binary-assignment-operator-visitor": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.1.18", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "9b17f51829933a685b75b271ab8f57c622f19c5b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.1.18.tgz", "integrity": "sha512-TSwt26Qwxe3hs3cUDtJNpNVl9ZAdFV/T670tGDHOHoAi5Oq0UaG8Og3TwGWT2on8IuU4FEpp0jE2DUFIPkInAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDi819LwnEQ+yrlOEWLVAxuj2gal7emDiTU38SpvedElAIhAOKppC4NnHvH0EISI6HiPyZ+0CiDa6Bj46RASJey9aVU"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.1.18", "babel-helper-builder-binary-assignment-operator-visitor": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.2.4", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "da12c42403f37234df03eca0e99b12898736fc71", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.2.4.tgz", "integrity": "sha512-h6hGr+hwAzIyvvifeBCLy5FqoCmYnuhdIcyzw2+c1Z27/+6GxuoJkLbzrJjzUA5kmfqAiJ2zoPF5YICW801GSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICICkqmXzdMdzReAEBYnYQq02zAGZC9mp8W+yhCfW9LSAiEA9noaQmb+3SBXYKGJ5hBLCBNS/qLbptPMgB29+L6oqyo="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.2.4", "babel-helper-builder-binary-assignment-operator-visitor": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.3.13", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "211a9591fe477fb291af402dfd695a99fd26a578", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.3.13.tgz", "integrity": "sha512-aNwpjpn4ZJ7aIyE54Jyy7WL/dPv5MT37MUPcUaslJxwjDM7RziZ7wd1T4KdTn/TSyT6ENYFrBZ1+J7dsR0OMxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeXWW6AOWyAE0kqcAf234yezv57O2A7TazNUlXGRYqpAIgU8ur9W2BN174Co1vutYjDWO1Te1OXR5+pcSxz/37no4="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.3.13", "babel-helper-builder-binary-assignment-operator-visitor": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.5.0", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "caf0ccf745d71b1d91a4e082edb83d147a1a6253", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.5.0.tgz", "integrity": "sha512-J+GK6k0mzCNJBeTT+LtxyGglHv5OIoem0WcHz1V9SCwpxHsZxtR5ixayHJ9e34srOzzS16xa44VcWJy/xn8Kfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwhm/v+lR9xZ19kk3l5lWughiFW541qk27e6Hjb5hnlwIgPzE73l5Qi+37NnIvkFf6xbTOus7QiHR60Vl6C7CZxXA="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.3.13", "babel-helper-builder-binary-assignment-operator-visitor": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.5.0-1", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "8e19ed5595dc85895fb6c67438f87ed4cf1d6707", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.5.0-1.tgz", "integrity": "sha512-utJRk6Pwd6G4BaUHC3grVU25WtDiiPvsGXaskdaYxWVlGyp8oNrxWuBG/z8GTeaFElc74D9GK6/NNcD4dtgxlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgMuhlcnnO64bD4WZ5K24qblGCIxdbsw9DHmAYh0tR3QIgU/y8LDL/oAwQZ3FmedeCU1bB38MLt6stjxBF0KnP5F8="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.5.0-1", "babel-helper-builder-binary-assignment-operator-visitor": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.8.0", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "db25742e9339eade676ca9acec46f955599a68a4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.8.0.tgz", "integrity": "sha512-BxuIwJY0s0cVAavDy9VM36AMjSCJdOThnvgZeLO/3zilth9r3Z/WysBpOksADyklGTqi1omO4XeN7ZbCwwc02g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFCc222ilH8kjHChIYrdXnSlsqNcTjcLWju5q9eez4mtAiEAoPj83o6lFIEKFFrHzZiHZLXmhh1Qz0hk6DZlk9fn+DQ="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.8.0", "babel-helper-builder-binary-assignment-operator-visitor": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.22.0", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "d57c8335281918e54ef053118ce6eb108468084d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.22.0.tgz", "integrity": "sha512-omiIQyXGfTfMLuENfUw9WqUv3PDmzZQ4MJXB1GafFWZWtpXDrm9UPXQ3rXuL6WmVX6FfOegwzpF+oBOgfZ7KwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWNzLa4dj6qT5yw6Nm3z9igjvlYmc1SPz1/CCLfDzs4AiEA8OyzhZNjfcjNCEwKkyZqCviuQ6RYhTr5ez59tuUkk7Y="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.8.0", "babel-helper-builder-binary-assignment-operator-visitor": "^6.22.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.1", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "6d02a227356f3db9067fe362199f6480be9b3144", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.1.tgz", "integrity": "sha512-<PERSON>vyZiYd5hSrSu2kqx0CsDC7NIm3ftAYwJ99aLLRBtvDE0hbdB6rjYWldzpJHRWGGYl1UjQmG7vu32wa8KeTaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB+ORPMBDpSwa/TxeC/lQA5SmlgMH4nH9oSWt8cDBI4yAiBenwfihoJr+0MK2h8/vDXcBSb56F04nPlZg5/rL5hNig=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.1", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.3", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "d22d899d0489573dc085be75f5e4638a53debbd6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.3.tgz", "integrity": "sha512-di+LpWZihqaZX3tMna4Fd4k2RTLJNmHQECunHNcYkdfdrHErmm8fBokYEkgWgIn65OL+N8I62Xe+n/cK0AMICQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDA4Z3dpeSNM/wFPci4xYraxgjXaOdM6uyZelRA48KtYAIhANcsBqYXvth+yEzmZWPU0veu+CPEmVvkB7rUYc0wI7hJ"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.3", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.7", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "14d02d8c8d4c3dc7bf3114342c9f5a7185285ad2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.7.tgz", "integrity": "sha512-fx97+Fiz5oLrd92fWdbP3FOY7YI9i9Cf4N/S+tZRVJZG9vrYO6FIo7nLEWiaQO87QrbfmuEKuB/S6feutjQsJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVuYYFWnuLi6evi55aYuajHkeKDg/ShIqmpxNkBqG4SQIgeK3RAy9QR68QrDWsPNsbapTP5uaTwjIRzmDQBZKCjBE="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.3", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-exponentiation-operator", "version": "6.24.1", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "2ab0c9c7f3098fa48907772bb813fe41e8de3a0e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.24.1.tgz", "integrity": "sha512-LzXDmbMkklvNhprr20//RStKVcT8Cu+SQtX18eMHLhjHf2yFzwtQ0S2f0jQ+89rokoNdmwoSqYzAhq86FxlLSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKPXnE/DcLg4+ILmxnPwr9mllzDA39f/jzauTFPMKjvQIgRBviwOUpm41GimOimmLRCNRptQLGh/Fa6QhTQoEhrLA="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.8.0", "babel-helper-builder-binary-assignment-operator-visitor": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.8", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "a4fd185a83284be4858b72fca675508ae87ea96c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.8.tgz", "integrity": "sha512-HGGxDgWUeD0HX//Ij+yuzg+kixO4EnM3D8X+T/oCpqEbrmI2dLmPYhfAOQDINnAInDYsbu6AuoTBFuIcjSn3TQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb5AYIATT7bcXSnK+T/lf9sinA+nBtph0AdjFa42Dk6QIhAMpSVVXpTCRyxF4JdAOFScDK+XcmxMIypH+U/ULZ4lTK"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.3", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.9", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "f0b5b03b1e37cc58e6444326e322b88186893e37", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.9.tgz", "integrity": "sha512-dPI9K2FS26Y3TPOy+UWd0Psb76ggFK+/FUaLo4h/v11GO3L4HDpCQlkBXxqBTwVmSjb3JqHCx/K7a6KbVwxK1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1rkDo8CAGPaMTxFjzGQKipuW6nOZUv+/KkGiHApF3xgIhALQjhv+RYoEhCjH8ObUM/dSQfQ2BkdHy4VMg8NgrmZqG"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.9", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.10", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "ee102cf3e04d5d26842dc32fdbe5f73c31901f0d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.10.tgz", "integrity": "sha512-9jhIvXX8xCSmASqE5tsAOko+AwDFzJh3sfbQWUEjoDn2YYnVA7d6ixW39X6qGwBauPaRjx4gfWkVDlWIZdddRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDttdnE5ul0vGhbozVX0lKTOrymOqR0bEdNLvJCvhJlkAIgDN+8bQcLTkm9ChYK2c7vFOd4n4GG9SA95imScq0Ed4Q="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.9", "babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.11", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-++ZXTis9c3CbPF1TXXFipCdz+pYTOIC23eJqwDRad9bIsrnrS5atAnGqLEyKs+30uwsyiB+LVK0Nw8KMVJdW4Q==", "shasum": "432898b47002b2789f32e16dea8af4ebe9569b50", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBu71sIydkDmNaqvj9eSC0WiGqGK3aERrougJf06GxyMAiBWZulSFcXv49Pgg51jdIBGxkBqfPEd3A6eY1A6jFy+Cw=="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.11", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.12", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-RArIpmkmd3DJ5vANK5GyAGy+Wr3PH8JEErjXPXmOtao3zCAR14GEtRfSsouRRiw6zcRxfMO7ZdpofrNGkwUxEA==", "shasum": "1a5cc0c6f40d723ecfdaa2bf406a317a4803f4ec", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8e5mmZldCgdZGHZise5RTy2Dpz879QsQe9C5JkP+dWAiEAzoKEcf/6ZhDkk1S/zq0JmgKu/uhl1ZPDhfcJv40vTjA="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.12", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.14", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "acd7d5d1be64c76cd426abb40209d414d3c331e2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.14.tgz", "integrity": "sha512-53dVEyMVQdX8NYZkLLiRFKey9wLu90LofMd0pOdGPZnFmAo11KxZYEAbX/rBVUptW1nrAsxz0SFGBz9tt+D6AQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAnSh/pmYfQC/7StQgZHpSkTbgfIJKpdGb7eNAEH+C3QIhANafUvzzHEvY+6BwY7kCNzP6UbaDXfHYdmVhR3tWSXxS"}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.14", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.15", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "8c094b1c9393050405f9d7245c7ff98b3f8c10d1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.15.tgz", "integrity": "sha512-aWB5SSFyN/Put6fSqpAb8+saBVskNITRZhEX2aUmG+SDs41ueFcx+2lYZfucTqSj+zyJEyYLykGKDwYJm/OE/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGBfdQh9t1gTK7/pekIvDYhWf6aPMXOnyQRXakusgqSQIgM0uUtKh1tGYZtheOY6TrFMqDYBt3x2d7irWlxviolRY="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.15", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.16", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "dec03cdf702ebffdf6fc8e84f328c930748b56e0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.16.tgz", "integrity": "sha512-Lddv9BviRojfRLKhcWuKioPuaCxU/AATASy4RjDyJ3yHjk8S5f08bOucRfel7+HQosWSfgGVniaL03UK8xoxsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjb17DubLXoZxQ3dFVV/R+aM64GEyh0n5NGok7bwGB9wIgerQ21VkLa3z/wu7XrRF0W1BWGmnB/Ex4PS9VQm0tOFY="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.16", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.17", "description": "Compile exponentiation operator to ES5", "dist": {"shasum": "69bde32e74dc8c3055965256e6dbd3bcd078e324", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.17.tgz", "integrity": "sha512-CMTOpCTar3g35P35LFEwx/lRLlbPess36tfYCNVNRsjGpde/QXn9fgd09gYUYG4Xgs+2FUNVzRJNzLK+Dxk48w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHs+CPSIFhY5eDtWsUDidX1WIXWJMiD/FRpzDHrKZnWgAiBWHWlUmaASKVKcmZ78BlDoxhuhQ8PhmitxSK8z/4yDrA=="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.17", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.18", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-1belDESWzuPaCgj+YOX+C3VVmiYFL1lix3vRC+k1D19Y0lnsnXMyGX1fZnA7A9cSv9tYNLCV2xvY9UMAjqm3Ig==", "shasum": "165e694152d22ccf160c96ed618b670fb8c7a298", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcOqj7SNx/2fL+xlJOV2X6YDTX1ePFkxRCvwGGiCygFAIhAIbyOaiaYkxyZjzprSqSEPydvW4v+y8iBrceEN2TzOCU"}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.18", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.19", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-AA7SV7XYusZtbxd4wc7ivLdXIZFVmjKBErZVN6T0Iwf0ah0qJHXEIaN0K0yt+lE2hCYi5IWCOESXZf3H4TB+uQ==", "shasum": "512950d6c2b98eda4cba109af59cb625e8b85465", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBuxIPilv6IbYEygldG1RLARz4qF7s5bfFu+pROvCM47AiEA7LDmDla0MBOVW4z4ThIHFxF79aLDw03DG8GLd630BWU="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.19", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-alpha.20", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-Q1O5zy5KuFh5EgsBLS598qHL7VAGdwCypNXFKusA8c4yrmAVS1nHT5L+bjd05yXsVhcJoAA5o8MEqmSP8EgUsg==", "shasum": "a09f8763303ea17da99a75ed73027cf170080512", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFrTy/baIF6xhqL4asDOWs4ZsiLv0EqD/LmZngLrIOvAiAteeVse/bTypKltWDegaCZuaYHF0prjM7zbVAjPc5kvQ=="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-alpha.20", "babel-plugin-syntax-exponentiation-operator": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-beta.0", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-w0Kf8KM6qrPEFZCrscOi7zeIjahzEjMMbebMWG/lgJ1BGAr0TLY2XeUN+NNO0ldC34Ox+UyN6NdRcrRo2Lhogg==", "shasum": "a559cccb54303e321420fdcdec941029f41a2e37", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwl8aluK9c1BbAvQzTKr6960d+HA14lkKXtCUaMespvwIgDOTGve1ZMSokyfgP1tlDfHNcov2AI1PJiPIB3S3CGxA="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.0", "babel-plugin-syntax-exponentiation-operator": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-beta.1", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-bsFGE3Tpdte9d3Adq9R5XoitAV9CrTl0aPRjlmtC9707BeROUvcopNqCZxTk2pGsmluqobW6JyZgiZ0HPm1Dyw==", "shasum": "2fc0644310477bc7f233eae8304b1f83217e1f7d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdfFTqY5v2WF5MfxhMIR2lT38gmqhJmJdJPb776oUA+AIhAN5dzlyyZOiz4X8+Sqb3Esq5lbm7keZ3WXVgQJjaNXT+"}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.1", "babel-plugin-syntax-exponentiation-operator": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-beta.2", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-MRikQ899wx9IyyAAJI643Bq8M9Opp9AFouVAmhJ9p+A4OjGdBK6YPMpeO4eChL8pCAvc2gZ0xIQJQ8E968wabQ==", "shasum": "50886381b685e1196d0c89965ca35ec8e6b42710", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDEbmYI8O3cpz6dHQd++knOc210dTDBCMHph7wS93zP0AiA7UdKy/RRtlFSveWJkZ3H8VNhRmo6e1RHrsmzlUvRMjA=="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.2", "babel-plugin-syntax-exponentiation-operator": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-exponentiation-operator", "version": "7.0.0-beta.3", "description": "Compile exponentiation operator to ES5", "dist": {"integrity": "sha512-5RaRPN5I+vyL6gyxriauB8wOncBUmDJbcUUGy0V8agBrnakCG5Fr2uiLuVPzKXAoac7CS3dtmTv+vObyvcxAMg==", "shasum": "f7399d3d73dbceb6a6df82b4824c4719c6e21ad8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIwldEayGdrKMT24WcThFYxzgQ5bEiY+V7y0rOl0A0owIgd+xcGHJtw3zztMQQWZKei3K0v5Xee5jRmJOnGORCVQc="}]}, "directories": {}, "dependencies": {"babel-helper-builder-binary-assignment-operator-visitor": "7.0.0-beta.3", "babel-plugin-syntax-exponentiation-operator": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:30.485Z", "time": {"modified": "2022-06-13T04:04:30.485Z", "created": "2015-10-29T18:17:23.604Z", "6.0.2": "2015-10-29T18:17:23.604Z", "6.0.14": "2015-10-30T23:39:47.008Z", "6.1.4": "2015-11-11T10:31:26.959Z", "6.1.17": "2015-11-12T21:42:38.564Z", "6.1.18": "2015-11-12T21:50:55.017Z", "6.2.4": "2015-11-25T03:14:52.332Z", "6.3.13": "2015-12-04T11:59:46.867Z", "6.5.0": "2016-02-07T00:07:53.985Z", "6.5.0-1": "2016-02-07T02:41:15.262Z", "6.8.0": "2016-05-02T23:44:58.307Z", "6.22.0": "2017-01-20T00:34:07.703Z", "7.0.0-alpha.1": "2017-03-02T21:05:58.009Z", "7.0.0-alpha.3": "2017-03-23T19:49:54.294Z", "7.0.0-alpha.7": "2017-04-05T21:14:29.387Z", "6.24.1": "2017-04-07T15:19:34.566Z", "7.0.0-alpha.8": "2017-04-17T19:13:22.414Z", "7.0.0-alpha.9": "2017-04-18T14:42:30.144Z", "7.0.0-alpha.10": "2017-05-25T19:17:52.016Z", "7.0.0-alpha.11": "2017-05-31T20:44:00.112Z", "7.0.0-alpha.12": "2017-05-31T21:12:15.203Z", "7.0.0-alpha.14": "2017-07-12T02:54:33.356Z", "7.0.0-alpha.15": "2017-07-12T03:36:50.199Z", "7.0.0-alpha.16": "2017-07-25T21:18:42.265Z", "7.0.0-alpha.17": "2017-07-26T12:40:16.016Z", "7.0.0-alpha.18": "2017-08-03T22:21:43.219Z", "7.0.0-alpha.19": "2017-08-07T22:22:26.793Z", "7.0.0-alpha.20": "2017-08-30T19:04:58.723Z", "7.0.0-beta.0": "2017-09-12T03:03:15.997Z", "7.0.0-beta.1": "2017-09-19T20:24:59.687Z", "7.0.0-beta.2": "2017-09-26T15:16:22.704Z", "7.0.0-beta.3": "2017-10-15T13:12:46.209Z"}}