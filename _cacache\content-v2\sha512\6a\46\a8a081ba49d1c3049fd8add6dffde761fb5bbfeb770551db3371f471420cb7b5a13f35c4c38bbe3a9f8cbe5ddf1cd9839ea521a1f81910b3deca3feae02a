{"name": "@cloudbase/cloudbase-mcp", "dist-tags": {"latest": "1.8.22", "beta": "1.8.23"}, "versions": {"1.0.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "ab1c5415cbf1eef41cb2a5678c8f1fadc1a1ca23", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.0.tgz", "fileCount": 1, "integrity": "sha512-kqXPxLItQcqsGEFUK01ZHy8JQTKIGJOaEiZTmp6TRma6ij3RiygWxPfCYUtEW+o4BVCy02niE0wNLenfJU3xOw==", "signatures": [{"sig": "MEQCICwFcFVTcF0QXzk0anb7JoUg7tLWtZacDDFQ0iZ45GRMAiARaUJolVAhodezxsxLcyUnMynRHUfVrd0utXKg7pW1VQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 708}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "11241341ef23acb2c6d7939ecfd5be246dff85ce", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.1.tgz", "fileCount": 2, "integrity": "sha512-P7QT+WwGsLaqm7JcSd38iO7GmNMsnQUilYO9XUjCHhUf0FxHYSYRUXZ43wn4DnDyn4Gf1I7/M7mWJXsvzStlVw==", "signatures": [{"sig": "MEYCIQDjaA5M56pQd1feQfs8V/mES2xafJWzTJcGpbEmYxJ8SQIhAI84S0fyS/tlg1xSApsPPElhsixFuo7t0jPAWti7P8wR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11557}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.2", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "90342a6c9248586f81a86597f56e10c56923f292", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.2.tgz", "fileCount": 9, "integrity": "sha512-3SVSfNFeyr2SyBxzCoka5Rdm5W3//SRTvoMOQ5JYQAo/9euBAW4mwDTqwC4LypEItSyl2/MowJ/uDBAPbES7GA==", "signatures": [{"sig": "MEQCICwc0FTkmBYWwufJdrruoTIFnk2iVl2z6UI5D3wchmVkAiAz2kf0q4x0ftBGXwNcTKvjE8vhMjEd7dniYVI5A3ujlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71596}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.3", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "390bfc0134442602f87fcc5212291609a3c26601", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-cQvGptIC7wxn25mviuVVzWtQFCK5RoXL66VkjvXGxqPL0kqhRVUVyU71sPMlvzr89o+hrjmgNJDPfvir+h33gQ==", "signatures": [{"sig": "MEUCIFTXB+9Fr0u5AKj1jaVRs0kp3LmligFNueDzYrjcJLokAiEA2w2ELS4ToSfWsk3GtLat8pg7puEp4nVHm+haIYXuZ38=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 111348}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.4", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "301a82dc2417aae97acea7f8e1f47ab973e4a012", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-F9nqOFh9K4cCH2en/O9NGCihcGq7sCCrLTq1Gr/zT5jXuV1KA7k+82gEodLSJIKMNt3z2HYZ7mKQKB206qbmdw==", "signatures": [{"sig": "MEYCIQCJQDPNz/oeHVNvjKqGeJ3RdNEMtObTNvr3hlTv6fmT9QIhALSbhSPUAe9SdVIUKlFAWN7H/b6xVcKpG3vfprwIaQLg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71616}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.5", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-JxgmfBAhCcf5FgZCJ2Ey8Ko9uoOcNn0FVyAlSGFBr3+/m+PCC/0ckboIEmppsUItDE7UwB2lqQc8Ns0qSpjYqQ==", "shasum": "371701718771d5cfb8dee45f9c494abc8e3eb10b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.5.tgz", "fileCount": 9, "unpackedSize": 71692, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC4coYfr/ptvFDumakfUHAotPoMPxBw07klxitupefWbAiEAgUzxHZcNoP2RcIk0UfTZr240KTK7uOqBKq9DChAFkCQ="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.6", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-+TSEgdHdysBWV1t7mmJCn1p8oF36YD0Urd7Zc5+3GRRJ5banxY8eLG2W3spBQ1MWqWdFz6ugxACyxDhy3L5viw==", "shasum": "776b947b699c14770e1877787f47fe4cd4f366aa", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.6.tgz", "fileCount": 9, "unpackedSize": 72340, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDMB4DALZkHteVgdf/tL4xVKjdHt8n5/JBoesjNoL4ATgIhAPRXG8tk2DPeh26B7yPGn6iMr8jcLN8IdwiNiwzRqdeB"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.7", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "06016b496225edd61caae886b603144575286fd5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.7.tgz", "fileCount": 9, "integrity": "sha512-rtkEuoEbTQCp+nkdxCw8BvVyAWrxdAXKWW57jxJPmVPcFyXtnzggXurTGg0pujTVxaDrBNmmwfig9ZT6P3R/0A==", "signatures": [{"sig": "MEUCIDJZPAgFgSxTp9hgppFs4TkVBKFETSfFIZiCZ3+Q7kMRAiEA9rs9oDNoey7Gq3fq1xYs30PlA3paj6FffUSUxfkoMQc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 72736}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.8", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-eR4HkQNdgPB2G4Jb6+PQGMmwCYoXDAyfO6Imr1ejAc7nAYMR2dOsFDN/7Y8w4fIf03gIxSRxtxWBt01ZBPd6Fw==", "shasum": "475f3d652126babbe5cc2cc28d3ed5ceca9d26c1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.8.tgz", "fileCount": 9, "unpackedSize": 73024, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCWWCGOluvnYp4IjWWongUExikWCkfBdbqIMSdA99mTuwIgfQ5MtVDTMbuvBTRH3pYUojjOXmauacL+WWHtrHX1kVU="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.9": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.9", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-K8Yw0eZAeF8NP/7/FBRsqfcqXusfWyyGrjgCO9e1F8vY2Yk7WHVuboCuNa2bNfeLMgJEx/YahxlMR8f5eyQCXg==", "shasum": "3b876e03e22c87514dc3e078fd0bc31e742396e7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.9.tgz", "fileCount": 11, "unpackedSize": 84011, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCS1qYi8zO8IrtMsDzvjiX182yVZ3zGnyDf2n5M0aTqzwIgIF+uCkdayS6I7UsqFrrNHfPUA6U0qHWtpfJp93cemI4="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.10", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "aeb892781af278cf0ccfcd3bfe24a99f40345f15", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.10.tgz", "fileCount": 13, "integrity": "sha512-K4EO6urK8vB4NXBIaBqfOr+6RpamwQKt7wiAOBIEEm6EaL99t2NstPUMDM4g8SGbWPex4hmwbxvvfQ56eaAtOw==", "signatures": [{"sig": "MEUCIQCFRTdIEdP4zoIeS4VDDd+tI5XJ/s7UZCZSBpPL7syKEgIgGHct3GoVlIbz9vCBzvNCmV2PZ1CXMv/RR6BlVugP7Ng=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86617}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.11": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.11", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "dcb21c8c6e7aac07a564ec4ee7fa14e8e0bcff2f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.11.tgz", "fileCount": 13, "integrity": "sha512-NxQs7UeiSafGs6r/jHe+CiHn3xMPqdXNFBvhtyNIkvkp41J6PsB1XBPCHzHvZMisHFlngs4oQXZvYa+PiTLRng==", "signatures": [{"sig": "MEYCIQDqKThPL2ZPHruXb3ekn96aSlimJ75MnBtv6LmxoRP/6wIhAItNRyvHMN7RpXdSNHhrRfJOI7aiCKEj+mlaz8/dRNvm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86662}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.1.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "7773a9d0072b4124aba79b2ec6417c029ff6e44b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-2wWu/1Ayxh6qQE8tMWhgGvxdhSleSpHBrIcWCeqz/ILV25ntf1EUDvbXCe6g2FNv9zDU8unHJ5Ss96ewm15+MQ==", "signatures": [{"sig": "MEYCIQCsyKlkADdMvSV/IvzzQKPzwHVCiHOgyynfJzoqH6m0ZQIhAODgPvfKzgsUK2N+4oHFme94ZmyQiAiSZXhMw5ai+HZC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85389}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.2.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "efeaf763517c08744bca5a330a07433954ced516", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-hbubF5QjN3BMR9lQJ67Keg1vNQLkUBg5fyyBfyIsfVb2Qmmg2qW5MksvJAhELk7tQefouSYZPPri+5YAhdwNzg==", "signatures": [{"sig": "MEUCIG+4AlCSWbG5ogY4LHzrQK+F7Jed7ruQEbBmPi8l2K7BAiEAhWPqFOnF/2D25ZxhT7YoFHE2hEIDqRCgW+C79wssw+Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86384}, "directories": {}, "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.2.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-zRwtkjfFozw7SAzPX5OLBdkf2Vf7hkeuSTUhaz5UPJeyrqE2JGlJJWNKm7nZKmvFnL6JF30Bzk9Yg6cI4qBjbQ==", "shasum": "f667f5d2a9173126f31c3a6f11c9419e45169ae2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.2.1.tgz", "fileCount": 13, "unpackedSize": 86754, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCbBLGyZBAyULCODQNvQMMFgUH7FxssXPiR9DmrHDbydAIgZPB4IekCrA9FtymABHJErOHzrV3d4HAxKVnHx4ny8Ac="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.3.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-ZQCLU9NSG4pjsuavIU0CjDibSDPVoJoucjBv9WuuYKpgKS7uDpBRYHjYrrjZ0V1cksqjXSLKq/oWcY30vsXFbw==", "shasum": "544d53ce55b1703f9b3664d4e55d5208c0d251d5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.3.0.tgz", "fileCount": 13, "unpackedSize": 88465, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDMBy3JBF2OZsxOcdV0chaAiGbz7O0K69SIVoAoxJU9uwIhAKpF7FigpaCJrVMZqAcN9NrjmfPGYJSpUobXgEWnyA0k"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.4.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-iCjFOkI5PlObpzGcn6h+YaIWlHqmORz1Uc1+DjFPQ4CpXCSIg3y9C1Q2S9cCwSULE+JeHvwNCFUqMLAggYoOEA==", "shasum": "ae1b8f9618ca328d2c9d5250cd0fef01146e8642", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.0.tgz", "fileCount": 14, "unpackedSize": 92558, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGOlnscKEd2Jcrf69ZRCeukISXpadzBtu/ZbzmJdgiDmAiEAp+5JqooocUb7LzL8xne9T4QPD9nFMWXzb/CE6VW2M5w="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.4.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-j17GGk1NmiPVETViuheyhPaHrGl/R8iPQGOZrbSUNY5SBlGJQXCih7w99hLtjTphTT/jNEXYzdlyAvoJ0iLrbQ==", "shasum": "1d883d47899d0bc17ea57fa73e390db659c10de7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.1.tgz", "fileCount": 14, "unpackedSize": 100545, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDze+/SRApTkkVJS8wJvxF79uF9jHwoz9ETtkVJZ63j1gIgAQFyR/L7TCiTcVkrPh6tUxKu9mluN4M2NEtpUXtEhyg="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.4.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.2", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-GybbYC2YW9twmzTlXYRnb2mYwUC8xF0Z987naN61ex2zX28XFO4sITnC9RAkDjdL65MgU6gdzI9Nh4T68e1jHQ==", "shasum": "780e3d330c44334fc53396fbe34523d92cf056f6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.2.tgz", "fileCount": 14, "unpackedSize": 100752, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF9PDC3vRCKDsx9uqr23KyXEcibqjxYklUMJ7T90QJFMAiBHifestb2brM+m6GUa/Y0LnzcYQAOAALDTVqjD1e+Zew=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.5.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.5.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-0oyx/XRSTVf+7thc4J+ECRlSx4WWXew5YIB5MJkFVSzG+wPiXkUJi46ffnh6Jf5ytcGvu7qRYPR62/Ezby24Cw==", "shasum": "106b70fd37ba5f6cbbab8020fb6d658006f128bd", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.5.0.tgz", "fileCount": 14, "unpackedSize": 100748, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCfAIebhkuJHlrBJR8T8gNdRThbbtFgyvMqSZxLNI+OywIhAO5IHSxQBVGWaBE7RrgecEQhGK6x0Q6UD3uSDp0B9aT4"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.6.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-CKzeETKBRK8WcHcCY2OMQusadDmqKDBDRfg5lV6raVxU7gaK2+yBlYhYsGOrhqcUNHyZCCc6l/PN1woB5RfL7w==", "shasum": "b5d5bfde56afa30ac2e67298c4ab0235c84e4428", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.6.0.tgz", "fileCount": 15, "unpackedSize": 112533, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFkJ3dD1RSlc3iRlt15VOnl4socELx19zQghu3bBlaHcAiEAnj0lvf4gPNPfzB3iZQUZlXvfK3sxN76L8JHZ71roM9k="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "unzipper": "^0.12.3", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-051UXDkh20f4AhAVEqMh/qpM0WY30QZNurh6xwXExD8kWfRKNLpQ/apFmfw6EUfMop0h4Rs6LpooEgaNlH/uOQ==", "shasum": "936d589b5b1eed97c53b16bd7698e207191e3969", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.0.tgz", "fileCount": 18, "unpackedSize": 187663, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDVr3Pb7E6c8SvlfO1R6i8Vq+YMw7PwX8IZB3A9akh0TAiAvuBQXlHMukfET3yL0fpGMq85P5w3ffwoxwhC0ChaHng=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "b843bab7b16f49dda09433100fda4149b2027d1f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.1.tgz", "fileCount": 18, "integrity": "sha512-BF6CA83ga2+ZgdLSEVZGvhKJ2FYFqCERUDvbOaDG/Vp4e1fY5hw7JXjW7HQGon9sGzBLs/OT4wmfBN2rhKxI0A==", "signatures": [{"sig": "MEUCIDikyw988VQf8kTqpPJpDmFYP3wpX9O2N7VnWi8n8TyQAiEA362Q7CpR6Em7QJE72N8oOCqkRqgLUN6H3zJ1bPCZmBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 218220}, "directories": {}, "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.2", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "811320d47e3ffdeb49390d15549cee4fbd491b65", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.2.tgz", "fileCount": 18, "integrity": "sha512-FVT+cOZpZiK3d6fZi64yFbX1zbAD8T2O9WMHohRIT2f7kPVZfydFiGOiD+URa7rVgpQjL7xC+x4QhsJ/9QIYrg==", "signatures": [{"sig": "MEUCIF7vTUP98RDFa/oazuYcBivvELKtMZQqfwqzJ9PJP0IXAiEA4TlOq7bgSnOKqugAQiqN+YNvv/JMbhL+x3wcIcFqEIg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 219599}, "directories": {}, "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.3", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-lX/7dpIrgA+odfpKwQrjA8AS2StqFnzeHO4YIIoxtfB6b9rP9KvXwtE/PwCqV13womXPINySsXhKcLj3eu7u3A==", "shasum": "2aeac2ae81271f098f5fe2d8f8cf8b619c348d7a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.3.tgz", "fileCount": 18, "unpackedSize": 219660, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICAQwD3LlwR8pw3S7tTxyKJQ9GzAT81+JH62mvIMKF5ZAiBvDyJUiou4byQy37ps0lYbkTz66qxaGVzrt8oIhf9vZg=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.4", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-6oPsG4+pGVOqNwweVk1o7lLy3//xErXXyEiyrqX1CWXFA/4b8vl00T5oeX1tG2phd6KfiR8qTNMQ51ordE/Pag==", "shasum": "c83cba032189568ab6ac20f1c18b22a386b3393d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.4.tgz", "fileCount": 18, "unpackedSize": 219882, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCSdAE+Y3KnRlaeTkLKpqD7RKM26WlegUfn/8vYLm6N7AIgOHtwciqcEY/VeWVbnu94vtttX5nP6ISIhgW37sOYBH0="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.5", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-10NrD5gy8cTMPlj9dJ+2V+scwCgD7YnQTWDcD3ZwSLYOr7M0HJa+iML4zwpw0aWIsz3PdHMwfKNVCVTnUehtoA==", "shasum": "d74b8bef106d2b4fc419d21eb9ab158d3ce075a6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.5.tgz", "fileCount": 18, "unpackedSize": 224073, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG7c7NZn5THQDXnzRLNlx2fP953+oRqe7VAOgiw62TxtAiBNisqXMNOmBk9IU3TdFpar/StDyBc297F0N3pttYQjFw=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.6", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "78805ff77b7ae6a8dfb3e5f8b12fc6cc1093b53a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.6.tgz", "fileCount": 20, "integrity": "sha512-7BrdNYwWsqr/k0hIWl/EP03mmshhZJ2KDI/R5U12EerDZTNPKeUnRIbXkZ7VaKJaN319UieCj0X3JwvkWGNwaA==", "signatures": [{"sig": "MEUCIQDl+W1gIGYaW75jSP6QWobOCAdJM+Y5z6jx9eUqvp0mgwIgSBG+js1tpCjH+Vi/xFooPzQywrFkxwqd7dyWv2bP0+I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 259701}, "directories": {}, "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.7", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-ilzgJp/BF70ywsIExYpxVfp0Lhu2fWg0Bf0UA4PArAjXqNJiR4u3yCq08FkA0QRnriUA12kdQco96udLbTLxiA==", "shasum": "0a28eee73537d434e9344121a1ad22cf455b79e3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.7.tgz", "fileCount": 18, "unpackedSize": 224073, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCeP+19cn9oueyH8CmKoiW4KLz9b6/iHh8zWNiCp/zA2AIgMsyVRQo/8hPuALDg077gy6iPAS1a72Gep1OfEh/ENAA="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.8", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-gB5/4Umb2wFHdH7rr4L/LTtgr9Fqk725hAIcVl2iCDDWywSTPDuEs5d+HxQrtTjRpxbopY04IMYscXlBWfJHjg==", "shasum": "bc10d70480d40da8aa1f9c11cf3e3a4cf7ae5ad2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.8.tgz", "fileCount": 20, "unpackedSize": 263291, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICPEiiM+tmUGkQfa+mImMou4lb+xwFE9ZjgR+HrWlYhJAiEAiDFsnpFHhQCllmCFRLkXRl47yp4x5zAxeHfFhYh3RM0="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.9": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.9", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-0jCpGRuvY3dJDgs/x5abJdUtC+8fxfRqbyvJvtcVMOLFIo32F/fw9QJ2xdoMMS57EQtREm0O9NKa23ieVRjWDg==", "shasum": "07bc2426ab8afdc7ae3d76f3216c32c0e2028bd8", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.9.tgz", "fileCount": 20, "unpackedSize": 263284, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD0FEuuhIQJNublsLjlcDQ5dubUy0JZheK9PgT4S7RLzgIgPMxVlJ2w2aoOx9B1MbFAKeEnc6AWR8j5TI1lXPetf5k="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.10", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"integrity": "sha512-/HlJxEQ0tA7uKvBijEhDKLajvzlPPgF6Q6+KlAoQOYo4nAJ5sQZsKW/ihW46lfdQm6PZD1q5+cpDasvtHvKo3w==", "shasum": "8bed6438bbe1eaba1add7baa698f8076aad9643b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.10.tgz", "fileCount": 21, "unpackedSize": 264361, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCRHpJwkoZzuokskN2yF9lrcodQN4pcleVFbRkoAlJ09gIhANJFarOIKfLhlY5/XS01F7Cr7eJ0rrhh/eZsd1xv380n"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.0", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "542a9d8fa456a7411ec249210bab6c7b9ef00b26", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.0.tgz", "fileCount": 86, "integrity": "sha512-mTbXvxrNjVzI+hJ5135YSYDSiQLWmZKpjkBNqtQri8Pf4v7qk6qU0pc2jn6IhF8WjWxHGZlJ9LRS3vGFU6uKJg==", "signatures": [{"sig": "MEUCIQCAP/rB1wuRO2YJt65JFB7VT8fGbrKbO9KWUrtYHQvnMgIgW2/XiY3Up5Zvu1PdzgR+RJzEM+G6vS0BJiGc/gJ44uQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 410541}, "directories": {}, "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-LpOwEKAzuZfHzHEcPXp+5Vuywjrads0m5TyWjWfnL3jh3qnGM2c78l+sfQD6RHq6a5Wyy363iw0CWrKiFUOcSw==", "shasum": "b2bebb8a6d22185e41873397bc93bd2d48a0d412", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.1.tgz", "fileCount": 86, "unpackedSize": 410577, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDZSUum6InU0D0TfELHrwDOtKzHMWzCKIgrthoE2UGVkQIgHupsLmkHSCFw+0B6eeVZGx76WHVe78V42uG325RDwk8="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.2", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-ytOkx/i6H4yOfROTJdueofL3bPAQzArb5IJU6FyiGl4lbtUHoAj9JdaQbpPqW73w9eLrwXAl2DQGyR5OxkhhxA==", "shasum": "ee9340b5fc27b43c07714f863ed9dfca84538e0e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.2.tgz", "fileCount": 86, "unpackedSize": 432718, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDkDq31BtxbuXM8L5zWeu/16rXV+orCHwDE2AuH58VDlQIhAMalQhPv11/RU5CpP+QowgmrGGDrRH2aU2kU6qlIV23w"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.9.0", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.3", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-9yMFUCg94jwEVV2f6lQNE52arMhrQwnDT3p0t8PLF0e0reb9KiCrDl7tg9paqcQ7w/tAJDXT5H6ZcsjsoLJjAQ==", "shasum": "90de03919d23de7ede36a850f16e500e091210f7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.3.tgz", "fileCount": 86, "unpackedSize": 429912, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHv988rmJCqpRhPyoAE6ocB9fxqqbjrDi/lqBruuwZOvAiAbqW24tFoy1MTatw8/9PubCqJAhf/qWRo09EvgJYkKWA=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.4", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-ZPDsY6v9uX7NIkJDKDq3PBgp1+zJZ0lWNKjKtXF/Y0xbLP8x/tGyPPvsgYLCXBEFUI/BNPNKArqw9Rme6inzLg==", "shasum": "5e61fed5253875b11bc03cde954886aa9d3bdfba", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.4.tgz", "fileCount": 14, "unpackedSize": 2216414, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHjK52QRespnkJy8GOyIFgUGKlbARVGuP049W27WX7ycAiAWFWtxIxGuuuZUcQIHKM8HRqourwR9lGAqsZP12v1suQ=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.5", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-cIW1RsA6V0JfDDAhBrSbctGsl7YuEZTr9XPtYrZceHVpbLfiLwmGGw/fCu1YL6kfTt6Z1ptHZjqFgderVqH2Qg==", "shasum": "83a5e1aeeffaed644d5b8d30b7e3f7d1fd60b09e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.5.tgz", "fileCount": 14, "unpackedSize": 2230148, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDwF5JmIw+b9jSLa+gmai8Wjq4PvtrhsZKKdTKehUzcVgIhAPEhJudZhPjTFGKgwjVa02KH5G9YhlNryf9De1ufmgpw"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.6", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-YKiS2uY0bnvLoueiyAJjNX+QB5Py1YKY4C5w8OIwZTknHpWuUv8DzX+lsmuQ0YwFj7mG8t3bhdD/V0WJHsaRdg==", "shasum": "89b248df572526e1847f032fe94bcb7091bb5e00", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.6.tgz", "fileCount": 14, "unpackedSize": 2232712, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEMCICbkArkBELRc8XIbItKEPmt74GkQdY45z80b7aRWKg8RAh9uHeqcr4NzaeujuiDoDLXgKVILbXgkoS10+6C2Qy4O"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.7", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-1gtvzpx56EnSragzJ6Gr+qQ+W+eEd6erjOL4VJxIcRb/2ftWapwFMHgXo1kjhz5IclRGNJKlQUMHmXdAaXjXfg==", "shasum": "7bf5f345ca1923f4c1be9e57175dd3ab487b65ba", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.7.tgz", "fileCount": 14, "unpackedSize": 2232812, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDRMRCiMKMxWpyaeVXn/mQf8Xte2qtSnvtA0Dor/xGjjgIhANI6Av2eneuN+H0HSli9/oPOCXkh9mVE4Wko7+rMAkf7"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.8", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-1Jpi7Y5O2koPHl9fGi2x3tXtzyCVGSHPMQusBOIqN5V7mv5Cr6ANZ4q+nHq0ESkrM8sKOZrpag+JgqLhgj2K0Q==", "shasum": "5cb12d882d5588fc3a0e9c60d6421c46fcb5ccda", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.8.tgz", "fileCount": 14, "unpackedSize": 2234208, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHrMmychbN06fhM9AOh+T1c0sDvcMgqk5TZsfSL5l3pkAiEAw2OkUCFxJ+seifaZIYalRBx/SEJQALGPHkJ3mRXDMXk="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/unzipper": "^0.10.11", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.9": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.9", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-s0zP12GXtWCZ5fbhUk8+FhNbemufXnWGgiTNghprzV2sYq7z901DudbDsHBuD57iTyW2JgFhaaE3sW8zaw3+7A==", "shasum": "e26836ed055a8eb7f5caea1d20a005c7062d8a00", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.9.tgz", "fileCount": 13, "unpackedSize": 84358608, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDXRZwZktGFyR/p9X69vCGNelJWOxsexQ5pzbZRN17aTQIgNJGTzl5e1hg34TjrKii1KgSljBJgBtIqOIrrP9g0qcw="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.1", "@types/express": "^5.0.3", "@types/node": "^22.14.1", "@types/ws": "^8.18.1", "rollup": "^4.31.0", "rollup-plugin-dts": "^6.1.1", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.10", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-9HL/Ks4sDWhLfP9C4pgn0EbD+hD6t8wa4KeMM1fpbN25dgMTR0yCCKLPnLGLW5o879/dx+u5NYzcCRxWwatxEA==", "shasum": "4934f1246d0b6f36eedbdec3ad5526b5fe9a69a4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.10.tgz", "fileCount": 9, "unpackedSize": 14999126, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAf3NmuwdYL6FKbLTmd0Z+O39tYrtveUKtslhKe1iDNXAiEA1ac6SXtxRiWroYWTk1K+CPxC0Jw+/AVyFGYBCimzlms="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.2.10", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.13": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.13", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-Ac8CpaFOpzroZldNp5UxftHSNg6hRhYmLg+QnQVmxO5aDflIo10p1OOhDfxuH843o6Ju4AQrlUw8aDg3t0hasw==", "shasum": "56beefab5542aa351fba3e0f4c8dd4f42898e15a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.13.tgz", "fileCount": 9, "unpackedSize": 15552658, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHCg4OYZwZc/lU/Z37OTJxfRKceKldVHS/bJzQjGBGehAiAlSTbUPgd5/UjK9hGZ1H5N89z5PLXvxpNmGnddE/KMSg=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.14": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.14", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-zVWeEAad6N60rNsrHQtb4OLP3K0PTuHGfpLcj9PvBaiC32jY+IUfFMrbMHoXB4r/5L7kPB0TQWqgEJSjv526OQ==", "shasum": "c76be61da97265bac1f180577c24d70971e5bda6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.14.tgz", "fileCount": 9, "unpackedSize": 17149518, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBAvbvk6jIWUMqiBj416WHWZbsGZgn4sykQ0rcAIOIIBAiEAj0NSoAKdMh06hmL6YP0L3AT6LcJfaXgT1lXFf0emh/0="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.15": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.15", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-Q7znWNdOk+BRtFD172Vi+9nGY9+mJYMwkwnK6rSVQazJ+PEnvX+0fQ4zxeJEbTHap0bn8BfC4Y5sfKt3/BszBA==", "shasum": "0ba9ab81aa3db6a2842787687365fb2e3cbc4d8d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.15.tgz", "fileCount": 9, "unpackedSize": 17162602, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCPuRl221u5ty9H9lOjh2jBdQUmvBNMa7A+VjzcNGFv0gIgfQop+KiYdnTswIV4qLdndL1rBfw6/95MRLjpAeG8S9A="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.16": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.16", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-3y06SB/4rGPxZWA9rKjo7rMu6ZIDEyjAau0/+qp/GO1LvFAPGj6LcM/uwGR3hkPISdwdfrQufV3pBKjdCIi7Fg==", "shasum": "7cc825f5861b312152b8de6aabdacbbc7c656c7f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.16.tgz", "fileCount": 9, "unpackedSize": 17169566, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEs2SWiMU0HUgc8pDS62IDF1QbDosq2M7a5EV3kwzFjiAiBiwQibz8rrgQ3nIUAP1HSi8gyC7JyQPudLV3Ma1fai6A=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.17": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.17", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"integrity": "sha512-VqXxTFRjheZZoVkJ6RseAazGDYWfY/0uzveW4NBMiYvNfp6fy+3IK7PzDUybMvwEus7SCAfjFTsKph7ibIL+ug==", "shasum": "aae85bd29f1c2edca0d5343f18db0ebaee4c6024", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.17.tgz", "fileCount": 9, "unpackedSize": 17169403, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDwophE1/qHSGN96EQfDKZvBGm1eARstmdASTBbR4ZTQwIgCiofviWP1bcplh5c/IJ8EQZ5sn4AyS+wN9eXoapZSKo="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.18": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.18", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-cAg+nl7GvJkjSfSuRT9FqdD0hiCzuB/g/uy0ONJGgHSrbLdtUyAeEBLeiWbwYy5R+G0SAIjB+YV9LXd5H780cA==", "shasum": "737ef1765746f9c9a86b78abe5950adfcb7493ac", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.18.tgz", "fileCount": 11, "unpackedSize": 8230229, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCID/YWJsdlROWHhQ4I9gx+F2UDdP+IoUoWdZqAdYC1uIyAiBHSnPfnhPvKeDOL9T3QiriL7VuZqiKUDh5BbU0rkCoZA=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.19": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.19", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-uQhA0/cqkB7p830dn6OLwwcYeY6IZnM7u/dbJ3+rsxrEuCMjZDODaveVpAh9MPe7QBoNN40qALovpqUsYc9KeQ==", "shasum": "4a95e73f2c81e9d5f09baf894717360cf2034852", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.19.tgz", "fileCount": 11, "unpackedSize": 8230268, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCcpP06YKIJnxLAgISBRbRAZpWdqz014UGUTn/6J2kD7gIhAIEXOrlORH5B6jpFKjGZnMgC91HVavLcNCXcfLhILFhB"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.20": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.20", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-VfmLlbyP3EYgO3JmnklBow8RyZ3MhoyvpKZoI5O7O+6gQ9Gs9JfC3R4iiyded5nbJv7MmyaUF3lLouy05kobUg==", "shasum": "9dcd4d87dd93fbffab3eaee4c1ed0fbd4fe736a9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.20.tgz", "fileCount": 11, "unpackedSize": 8229546, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHrunq2KgB2RhzcpxRfS9iGBUDIFQEuFIn90VaXmhFX6AiBzHadf+H7zC3klyngs6/H4hOZv3dNyY5ZhbwUSoogAaA=="}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.21": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.21", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-YMcXpPCkxk9zZ7q9p/1rgxldd1F2CJYKiMGJPWEL0kxrwXcBB+vEUM9ooCL/olLabcwWTr3zNwNv5HS52iIa2A==", "shasum": "ce3a65204653eaafdc824d40838d1c48bb309cd4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.21.tgz", "fileCount": 11, "unpackedSize": 8245727, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDAwVQdB1cGSxQhG6d3T7G+jVZH7qyoyTA5JceX1xQrOQIhAMtPSnBlQf5dMWAc43cRmojPofmU991ffn3C30P8IJZZ"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.22": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.22", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-c3e/L/aHd4vjassLg1eJpSdzza/6iU5SDAr5L5RVY35SW1gdMtndlAyfZat2WjsHpNvY4Sp3xcxDY9uwyWAAGg==", "shasum": "7428119aac442b073bc3435a4628750e65c36dec", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.22.tgz", "fileCount": 11, "unpackedSize": 8203141, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDxHc4BnmpQO4T2P4vXSYEN3WiyjuW/HLm134joBeQdfQIhAPitgvZFWS1jEi+nSqqRSQ/gIptWj1jCYGlRqssoAc+k"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.23": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.23", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "dist": {"integrity": "sha512-yk/q/TXEdwhf4FUzo+tv8u+xkLu+P1zwz/3F/wN7bcHQlIwwohjXEiVGehIHlwhf2rOwKTbwVaKu2D9JnRdIKg==", "shasum": "c5af28993b3d79cd23690935aa147b74100bef73", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.23.tgz", "fileCount": 9, "unpackedSize": 8201925, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDziYdb04iTR7hzvzuxsjYy50nQ5WYPSV0ZoGSwbEGD/wIhANPA6gRtc6AXjYPzhSeeyzzekw/ZdVSx6cxy6aFM/vhZ"}]}, "directories": {}, "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-07-26T06:07:42.974Z", "time": {"created": "2025-04-17T11:49:12.575Z", "modified": "2025-07-26T06:07:42.974Z", "1.0.0": "2025-04-17T11:49:12.817Z", "1.0.1": "2025-04-17T12:07:52.498Z", "1.0.2": "2025-04-17T12:25:55.454Z", "1.0.3": "2025-04-17T12:27:35.179Z", "1.0.4": "2025-04-17T12:41:08.176Z", "1.0.5": "2025-04-17T12:47:26.737Z", "1.0.6": "2025-04-18T08:21:29.539Z", "1.0.7": "2025-05-13T11:59:57.670Z", "1.0.8": "2025-05-13T12:07:12.193Z", "1.0.9": "2025-05-14T09:49:28.102Z", "1.0.10": "2025-05-22T02:19:03.049Z", "1.0.11": "2025-05-23T04:27:17.008Z", "1.1.0": "2025-05-23T12:52:33.975Z", "1.2.0": "2025-05-27T08:59:56.110Z", "1.2.1": "2025-05-27T12:55:56.704Z", "1.3.0": "2025-05-28T12:46:44.074Z", "1.4.0": "2025-05-30T09:58:33.189Z", "1.4.1": "2025-05-30T12:49:00.939Z", "1.4.2": "2025-05-30T12:51:52.692Z", "1.5.0": "2025-06-04T04:15:54.350Z", "1.6.0": "2025-06-06T08:38:53.902Z", "1.7.0": "2025-06-10T04:14:21.475Z", "1.7.1": "2025-06-10T09:23:13.251Z", "1.7.2": "2025-06-10T10:25:01.594Z", "1.7.3": "2025-06-11T03:43:14.704Z", "1.7.4": "2025-06-11T08:35:43.327Z", "1.7.5": "2025-06-13T03:49:47.116Z", "1.7.6": "2025-06-18T10:08:31.663Z", "1.7.7": "2025-06-18T11:26:18.910Z", "1.7.8": "2025-06-19T08:08:05.632Z", "1.7.9": "2025-06-19T12:01:19.555Z", "1.7.10": "2025-06-20T04:30:15.611Z", "1.8.0": "2025-06-23T03:41:01.131Z", "1.8.1": "2025-06-23T03:53:02.791Z", "1.8.2": "2025-06-24T06:57:16.783Z", "1.8.3": "2025-06-24T07:18:21.316Z", "1.8.4": "2025-06-25T04:01:38.859Z", "1.8.5": "2025-06-26T08:35:22.947Z", "1.8.6": "2025-06-26T08:58:59.501Z", "1.8.7": "2025-06-27T02:51:22.086Z", "1.8.8": "2025-06-27T03:06:54.470Z", "1.8.9": "2025-07-03T03:43:04.630Z", "1.8.10": "2025-07-03T11:38:44.587Z", "1.8.13": "2025-07-04T03:51:59.630Z", "1.8.14": "2025-07-04T12:38:16.195Z", "1.8.15": "2025-07-09T09:25:33.748Z", "1.8.16": "2025-07-09T11:57:37.419Z", "1.8.17": "2025-07-10T15:31:54.515Z", "1.8.18": "2025-07-21T03:36:44.191Z", "1.8.19": "2025-07-21T07:29:52.891Z", "1.8.20": "2025-07-21T07:43:42.260Z", "1.8.21": "2025-07-22T09:40:11.979Z", "1.8.22": "2025-07-23T09:43:11.664Z", "1.8.23": "2025-07-26T06:07:42.610Z"}}