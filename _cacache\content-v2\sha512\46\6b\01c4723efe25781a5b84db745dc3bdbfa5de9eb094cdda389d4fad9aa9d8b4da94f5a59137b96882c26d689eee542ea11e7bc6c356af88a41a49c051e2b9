{"name": "address", "versions": {"0.0.1": {"name": "address", "version": "0.0.1", "keywords": ["address"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@0.0.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/fengmk2/address/issues"}, "dist": {"shasum": "2e32201de7ab7d2bffb3abeef19763497e224a89", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-0.0.1.tgz", "integrity": "sha512-1V4Aw9ZjeVVtshxZQAXpaVGTjd8LNwO3xMX8xL+wVzQ66kAimYPxfjwj159rChhgVivAh+As/wD3dlw4/QYtMQ==", "signatures": [{"sig": "MEQCIFBg5d7AIJjBIkKMWNMlwKlE7ws8cWk6X0MYxcM0voK/AiBgCWAj7aMAoyuOa6vjTh5SXpfNCObIsAWAMRuCTy/n4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/fengmk2/address.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}, "contributors": []}, "0.0.2": {"name": "address", "version": "0.0.2", "keywords": ["address"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@0.0.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/fengmk2/address/issues"}, "dist": {"shasum": "961c202a217a742291dfdd590efb9eb99bb66ae7", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-0.0.2.tgz", "integrity": "sha512-47zW50cHjwra5fs9WXMnO4PksN2Jr1dxEPrls5ayuoMwrJs+bmwEGWVg/c9Vo+jleIg8NYLqWnlA71TQ6nYfrQ==", "signatures": [{"sig": "MEQCIARZb1ZcVPZKdbfU46LzbggnWOjyGVWseyoDscWBSdIJAiBv6osSkTS737QeMJdmLLBPBv2ndYT4INS+P81C8KU28Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/fengmk2/address.git", "type": "git"}, "_npmVersion": "1.3.6", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}, "contributors": []}, "0.0.3": {"name": "address", "version": "0.0.3", "keywords": ["address"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@0.0.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/fengmk2/address/issues"}, "dist": {"shasum": "81c73446c8f5f08758ff5ede5cfab74e4d348f16", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-0.0.3.tgz", "integrity": "sha512-O2RnN+S2K+TsilCeJ8ajLBarYPFlKZZLLAeKfXOQlOk7elarJny8iFg/HiLWcUka/bR1C9GoxToHVAoVXUQbiQ==", "signatures": [{"sig": "MEQCIDzlNx3lzxmMGF2xbt0xcXajzXQu4aNblGDFeFJMB5oxAiBHLlF46x5JR6J2Gnyklx/4fwMxO+PQFks7r+g26jS0kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/fengmk2/address.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}, "contributors": []}, "1.0.0": {"name": "address", "version": "1.0.0", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.0.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "20200f7900eeb5f13779e768785c78c32ce2ce86", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.0.0.tgz", "integrity": "sha512-qkEcDuq3tAWu3cG3skPW5utFx/Fh6Hr6BsZIVfrBzM4LmF/HE4zzpcyvQ5VRBKqxLbZi2/nskDXO17d88C663A==", "signatures": [{"sig": "MEUCIFiLHBRr1Tg/q/FwyIjBVVxdkzRM3LdSxrr2JZQyErxbAiEAoth2Nmt2rMD6osmQnuhyTUTqotwr9jfUK98ysbGc4X8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/address.js", "_from": ".", "files": ["lib"], "_shasum": "20200f7900eeb5f13779e768785c78c32ce2ce86", "engines": {"node": ">= 0.12.0"}, "gitHead": "4af8781dfb49f7f0682879b0f176cc9fb82d0fc3", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '~'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*"}, "contributors": []}, "1.0.1": {"name": "address", "version": "1.0.1", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.0.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "363f5d3f2be26d0655d8afd5a9562e4fc2194537", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.0.1.tgz", "integrity": "sha512-TWVEdi8eS8tF+NPiCgwI+of8mi9Zojposi26Ase0Ei8TErWDnLSCxH2kvwH4tELCDIiQhimnm38aIg+z7GnjGw==", "signatures": [{"sig": "MEUCIQCVVvPPXbk7lOYatk8tC74crceqwjKmhR9a7CPorVM8tQIgXSYVVJjtUGcv3edLRM4mm3l0l8zYEWR/69FYb4/55tI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/address.js", "_from": ".", "files": ["lib"], "_shasum": "363f5d3f2be26d0655d8afd5a9562e4fc2194537", "engines": {"node": ">= 0.12.0"}, "gitHead": "fc3536a8f2a3862d3c8066178b3f7d60483192cc", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "4.6.0", "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "contributors": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address-1.0.1.tgz_1475221883259_0.3445093466434628", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "address", "version": "1.0.2", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.0.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "480081e82b587ba319459fef512f516fe03d58af", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.0.2.tgz", "integrity": "sha512-uFU9NQDu9Rs1N/1Cyah2qeyTMVqag0+HPdM/57i1Q7dLPMlKQrkDT7f7x/ROIDB7/5f+lv0r1IvQ/4wZFGKSnQ==", "signatures": [{"sig": "MEYCIQCftyr8YXxyGNPA0blj6FeWL565x9lGCPfYjTM3vXCUuQIhAND2ZvYRcAFyKtQiH3qlvuEiUq/dFhf8EUxd4FYOETIU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/address.js", "_from": ".", "files": ["lib"], "_shasum": "480081e82b587ba319459fef512f516fe03d58af", "engines": {"node": ">= 0.12.0"}, "gitHead": "f5cee41a0503249a02876a82f9e4f7dfc1d253b7", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "contributors": "*", "beautify-benchmark": "*", "webstorm-disable-index": "1"}, "_npmOperationalInternal": {"tmp": "tmp/address-1.0.2.tgz_1495729717962_0.17448946181684732", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "address", "version": "1.0.3", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.0.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "b5f50631f8d6cec8bd20c963963afb55e06cbce9", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.0.3.tgz", "integrity": "sha512-z55ocwKBRLryBs394Sm3ushTtBeg6VAeuku7utSoSnsJKvKcnXFIyC6vh27n3rXyxSgkJBBCAvyOn7gSUcTYjg==", "signatures": [{"sig": "MEYCIQCMgBSiYni1jRQ/pvKmWwKpU+QP4uxTzS+oQ4ZD3fAtOgIhANFOK+ePTR4m6DT+N9H/PlTgABerKjgSpF5eZuyF80td", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/address.js", "files": ["lib"], "engines": {"node": ">= 0.12.0"}, "gitHead": "f17f63e7eb38e45abf64d90612774eb98b59cee2", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {}, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "contributors": "*", "beautify-benchmark": "*", "webstorm-disable-index": "1"}, "_npmOperationalInternal": {"tmp": "tmp/address-1.0.3.tgz_1503567347805_0.4586020016577095", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "address", "version": "1.1.0", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.1.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}, {"url": "https://github.com/ali-sdk", "name": "ali-sdk", "email": "<EMAIL>"}, {"name": "tangyao", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> TUDI<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "ef8e047847fcd2c5b6f50c16965f924fd99fe709", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-4diPfzWbLEIElVG4AnqP+00SULlPzNuyJFNnmMrLgyaxG6tZXJ1sn7mjBu4fHrJE+Yp/jgylOweJn2xsLMFggQ==", "signatures": [{"sig": "MEQCICNr9GBH9XWaEkTU0uQTsS+Lf0S9nDENhBCyyckKJleZAiAmhxkLQENirNvWu3LOrMPgaPcKIg1fzX0dcmKTR3T1fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwG0vCRA9TVsSAnZWagAAdSYP/1/DSh0LwPJizGXNWwyz\nY6NTog5v1UJZwLlxsan4aP6PkLfYo3aF4IysAnTGNvBO6AdiywMCvjeuvKfn\n6L9edinsi/RqxB5RQFfzi5NyqVv73W/ZPz5Z9Yj89NGDQLnqnSRTgMFtnOpo\n2smlyADxni9OZTbg9AtYhXjGYKYHX+BYxLLfOYfmVt8+xTMHIWaOA9eediDF\nU/Rm8FIhOpAobQKPXGlg3Ac8w/x3nDyCBpiisyrsvJ288kjssgMCOelmwqxr\nzHfiy8WBsaiHO3AzaBWF6YaxV5ZIstnPh1D266FS2L9/BUKt2wGEu3pa5gOo\nZglM5zz4S73icvXwUybG/32k2Kjlmlv6WfxB4rz3F3/B84E9hELoN4gZ5mQm\n4DXfA2N/+N+Lguf0PRC2z2bL3YOAlbCc4habwob2DArGlGDY89oy1CtZsKnM\ns6smGVrifzA5FirqabizF8CHraIQwJ6ra7tiC49QH6wHMO/i8qsmgczPN8Ci\nXA1MaQbMLlJmEIXVY9WPpDkQMhJig/Q2/dWRPxI6k4px4VVlGPzJpgXV+eWK\nj4fisJ8gk5se4jM239KJvgu+Q26rud5PfmwtR3+q34vk3GXW4XSmUIU2lWsX\nhw1bu885L5v1OzZOOck4v1KOynX7WX7Gm08znNvDqYUxhlynWh92PIIdYufc\nK13l\r\n=rAnY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "d5c17a733d5b53942043502833dc7fbd6dd0f33d", "scripts": {"test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "contributors": "*", "beautify-benchmark": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.1.0_1556114735239_0.9934551439081425", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "address", "version": "1.1.1", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.1.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}, {"url": "https://github.com/ali-sdk", "name": "ali-sdk", "email": "<EMAIL>"}, {"name": "tangyao", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> TUDI<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "9483d6464788dee73e9a0f9f5d57bedf429b2a70", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.1.1.tgz", "fileCount": 6, "integrity": "sha512-srclZEseI6OB0MgWYWuYpr2F5fHMxh5lgWF72CeUFm5QgJyviPA64Q7PzJdBQkFCJibEycqsZ5b9L0l/J8SlyQ==", "signatures": [{"sig": "MEYCIQC+0CfAXab300QV9/DhvZQQYXjsQsTkao5+/gcUf06q3gIhAMLClIcd6MK8xYpmSRXMkhtaq/nsRe1lJSFzgyB4ENTi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXoQLCRA9TVsSAnZWagAAqJcP/RoQLXkPLLfA18jOvH2u\nSaq08wWmIdZK94Bm+hYkpDUBtaQnenGKB7rJkzMUFfQpwNUFmLAW20eHhaXy\nKiUlrZOhodlrJZGoKwQYJQMxra9vvGLbr1AtdsuvJsEHFXoUPNZFxtQM1hPL\n2irhl4V2jIVtMIt1oCJVX4N0FizSdP3crmF3ZoKHOLqYTzBIxP80/GKcQnzl\n1XTq6HTFvuDkpllCn3GgZYwZbo0OzEOY09UH6qpvyDVkMO/hs4bF0KLNg+Wy\nwKizQKQacJTsHreL0w9Fz+xSTfiP6oRLXoBC0q2TmtHQoGNYbcHjtzneaxSV\n153xFo6U4NLcNmb5nejhvOpTzr6mQsl8FreYYo8dR4AdlPxi6iYVQiTJCf+Z\nK6kZQq21oGZff2NCUEXHAx1yB+yjDkmpN4axs1sGN1h2hgqUslONZKojv1uN\nRk2e3/tsFT5hf9vOUFfw1f5a/OjoYwwX/67XiBFe9quWDnAjQZh6cKSy7yVH\nyGpm72xokjvPP6x4nBGc+e2ifzLukrmExg21enQ5DVrsCm/blYh3H5rGGU3L\nCSlR7IOlHCZrT3oZbaxI5eTpDxB8BVCx2nASxBgzUl5s5u0Xezdlj85KxiJs\n+EWnob9REUxBWRelm54gmad+bcfzs0TvISw8Y93uCSyzAxFzM4S96wcv3rON\nfJnB\r\n=xNvB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "9aa2ca133f114edbb043177fb49523ea200f1720", "scripts": {"test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "contributors": "*", "beautify-benchmark": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.1.1_1566475275127_0.570037106856988", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "address", "version": "1.1.2", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.1.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/alsotang", "name": "alsotang", "email": "<EMAIL>"}, {"url": "https://github.com/ali-sdk", "name": "ali-sdk", "email": "<EMAIL>"}, {"name": "tangyao", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> TUDI<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "bf1116c9c758c51b7a933d296b72c221ed9428b6", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.1.2.tgz", "fileCount": 6, "integrity": "sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA==", "signatures": [{"sig": "MEUCIG6up1Is3CkAayCw7bC9Rx3zLuTC9R0cXcSAk8+urQLcAiEA3pEEZvwm2f2GC0YUeXV91TKvDRX2PDd1uG/37BvPlXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdY5ELCRA9TVsSAnZWagAAOwoP/ivTNEecPpqvwotzbRKR\n8imo3yyXDIezvxlQQqtgpi7YLZUtXLKWi5Wtk+KvSfqqLwxvdyk1GAkqxhxx\n1Sr824J7mMSGg4WNIKWkePlmo4jbbYySzknU43yznpPMO6EMuaKhTjx0VbDE\nXNTakyY7JkYBdlD6WKTmdJrupDppFDUnmtpYwRysZasPOjCq6XWpPqh7e9nU\neo1nFD3PyD7/UlMWMuUGIk5WiiIGtNrKcPi0pAdrvg3DJcfwEYFv5jNPue8t\nsoPWyble6gudl7YuWsITcj1bLr3tGKgUtDazuPYRxgVHrqFOprhQdAN/s7Hn\nocl6hbr1HlC8305VDd3EmRNzXtuteqLAiIPttXM6uwjr3oI3Pc/b7VedGqY1\niJ7O92yxDiCHj8druFuHBapKvReG6Aud4WzhrjtCKVK7Uz/a5BTS/IXth6aY\nPiphfppyU34+BzBLbJ5gHLpilta1OAdCM5TnWheo6uOF/5oAfZrO9yDoy+1V\nXbff10MD8qqq28i+jF6k5b3rPOwyB9Url4qGC+CPaY/y7aL9GqXUl9jgck8G\nKmLoM+QlEtR2Vcm9iTlpC/o4iEsvgaX3J3U+N8zoW3gPCQknjY60W1/eCbCI\nUeR5/gZASOxwDr9rVsVzYJg1h/GKrrUhzytBgxyUiQvjUfX7XJ1dRswVQwtH\n0FEq\r\n=L45o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "05b5f072d34f7d69dc8281bd86f9dd09a9618391", "scripts": {"test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "runscript": "^1.4.0", "typescript": "^3.5.3", "@types/node": "^12.7.2", "contributors": "*", "beautify-benchmark": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.1.2_1566806282805_0.26064372915585543", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "address", "version": "1.2.0", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.2.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "d352a62c92fee90f89a693eccd2a8b2139ab02d9", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-tNEZYz5G/zYunxFm7sfhAxkXEuLj3K6BKwv6ZURlsF6yiUQ65z0Q2wZW9L5cPUl9ocofGvXOdFYbFHp0+6MOig==", "signatures": [{"sig": "MEUCIGwtFkUEdLB3DQx56lpiI56Mkk/3Zth4xmKTRSt8vimuAiEA0SZywR+JkRpivNPnDv+kfUnj4hRy3yoonaSC1WXHykE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia+FTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnpA//f+WrmLs7Thu9suc/T/6ebkAthwx1iznOBwQKjoPqtcZPkF3f\r\np6jAOhKGw8Iy2hVQ/EnUkDZUA38a9UyHbQ/C8GqVMNkch/7isjIBa7GRcEP6\r\nIn3c5HsORKmhPqR2v7o3QGnIa3XdBtf/vd+65Zh54p1FRdFmmdynH2zJHL+G\r\nUeITmb69l8uFw2BzL9nD+jBbW81nHmXe0x8OiYLNxv4FwT3SGEYF8pzo5deX\r\n9mIPbJ85XPJlqnsIivKxIBc4IeriIis3Fq8m23NAl9i2b3BlYKbAoUBwdM6P\r\nJHV/am7IMsLLxV7WAO+c+UuKawHqt/tx6QIabSNGdeX17Jc4JY4/8k8uyMTl\r\neo0RZsf2MOpXLiTg+ezeE1Ujf7iWy2o+oBNQW2UoWc/ydLO2RjY3BODe+GYM\r\nwaKfcshmi8yXAnBCsi1wFTfb7NXb0pru73opqRdYk+o0ofuvIShPND0pT0Jz\r\ng02h09/5qutwq66Pf714f/k6euorEfAPfJVtSOPSHHcCadB5P/2xnjpgcBar\r\nkJ/RRtjp2YGDXwLzV8HOZ9Gz5YdMiePagXkIukNOp4waxszfZxRQMyN6Qrqn\r\nLYtS/wvxvxt+UguJGXSGcGkYCnImJQ/SONLnDXxbTxVVNIb4q8Q6gfj2VzMW\r\ntpQuCKHZE1vmtWFP0LMy/AHIw5H+AzWT8n0=\r\n=imnh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 10.0.0"}, "gitHead": "3b87462b17ba34215ff129229db2a769bb6779af", "scripts": {"test": "mocha --check-leaks -R spec -t 5000 test/*.test.js", "autod": "autod -w --prefix '^'", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --check-leaks -t 5000 test/*.test.js", "benchmark": "matcha", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --check-leaks -t 5000 test/*.test.js", "contributors": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "mocha": "*", "matcha": "*", "should": "*", "pedding": "*", "istanbul": "*", "benchmark": "*", "runscript": "^1.4.0", "typescript": "^3.5.3", "@types/node": "^12.7.2", "git-contributor": "^1.1.0", "beautify-benchmark": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.2.0_1651237203312_0.9415215271704405", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.2.1": {"name": "address", "version": "1.2.1", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.2.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "ci": {"os": "linux, macos", "type": "github", "version": "14, 16, 18"}, "dist": {"shasum": "25bb61095b7522d65b357baa11bc05492d4c8acd", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.2.1.tgz", "fileCount": 6, "integrity": "sha512-B+6bi5D34+fDYENiH5qOlA0cV2rAGKuWZ9LeyUUehbXy8e0VS9e498yO0Jeeh+iM+6KbfudHTFjXw2MmJD4QRA==", "signatures": [{"sig": "MEUCIQDqH3Aqm9iUz0vY4W61Dc/UWhPCgxLjbrg4NY7cIdFgQwIgAUvuqxysATssOlOK9jJ6LuEKVnoAh5VAH1EG2lz0mRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH9hcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Vw/+J+9554stkTjSRS4dNgs3SUs1SKx/0n3yGcSqdAAjaxETqPI5\r\nCnH5ZHhh7f95uc6/ik+5x3FW++VO7Eij0uhya21o9FTLqf85pR4j6ReZWJf1\r\noVmfyAeTq6Ur+GVZvW2TEX7s9IKgMbQvmsJx5tNIB3KpLMXLZNqcXijs7Pis\r\nYtbqnPO05YuqxztTmeTvKz20isKH/+hA8p20tnK2FCMGOcGHGWukHnj9WM/F\r\no+MnOplw0pBurIxhP78kzcgvPvtY4Ip1rzgEMCsgbCoNTlcqZzaHeApf+WfD\r\nOqiYMH6k+La6fOh9BWbaak0M8u/rfOPxXqBmqNZt5wd1jHG1yWs+2o+o9zbF\r\nwvOXtS8sYr42vUZniZ8L8o7yLdEJzvfqy22hGSUXDcKkwexfi5yJfCQLfzE1\r\n7aYaWfxNQT5T2B3MpsUUWDjbu4a8iBV7CrFmWxCqB1d6BQv6tHMxnRYWu7s1\r\nzlKJP0TmnYpcxZWdvLxRilK9qevLvhZhngqg26jf8P6mQ89lTAxXNcJboT45\r\ny0bvqGVY5N0JjtFzqlwF0HBl8lyxuPVwFdPd/GhvQmFwL73FPrlrCodmBHO6\r\nkoG9aUUBu48nmc/vlxF/GfNlIsd/b07Gfb2TwU82mI2rIChTMi7J+ZOJNo9V\r\nikZVSBfLy7WZusohSWXk0O0/df5hyegxhso=\r\n=QnKV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 10.0.0"}, "gitHead": "d57cd9022ad87865c257dfc67cd68aa19f1fbb4f", "scripts": {"ci": "egg-bin cov", "test": "egg-bin test", "contributors": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "egg-ci": "^2.1.0", "egg-bin": "^4.19.0", "runscript": "^1.4.0", "typescript": "^3.5.3", "@types/node": "^12.7.2", "git-contributor": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.2.1_1663031388446_0.6536124664476417", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.2.2": {"name": "address", "version": "1.2.2", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@1.2.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "2b5248dac5485a6390532c6a517fda2e3faac89e", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-1.2.2.tgz", "fileCount": 5, "integrity": "sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==", "signatures": [{"sig": "MEYCIQCaoiY3GlMts8ZjTG4Fm1eTMSPICFHQPIPC2Q6xSpt/2wIhAPNyixHsWR4Xpv+6qUfppdvhmhX5ZgHop5kPCr4wUHiI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnd+RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoq0Q//Qnuy0B5oFw2638bYdTuCmgJItbVdCD1KU0Og/yA/EM8Ib3qV\r\nr72u7O/nZpkOl5mJqibj5hmr6ROnlakR5CcYxehX3I0I4IC5iZJOqCAJFryF\r\nDjwdqjQEfHK0NuAyX5J6qQoRoxF04o6tmivVam3W3Dh3csP81EATDXExyEDt\r\nqQMywUbHqa+/mftx6UIaXd1O0VXrU+7upbEk9QIoySJOh/GPAx1LdnbX1xhe\r\n4Xz0gOld602iswEGr4JHkz1LUCsWVZyTdxeqO50N50oInbvcApDFgE87Wmc9\r\nNMA3pjSxAcSVI7rCbYTlJU+lKIWupVN5xX7ndm7d56A6eHh0778F3gWCLn2K\r\nF1XPyZgGjYnV1DIF2E097lXmX1pkP2pLXxnR/YRTGik9cY54oIl0lu93dDxn\r\nvXOt/IJ5t+8ldE9qCefwv+FYmJCw3zKAHl0OSAF3zM7dIf09MpbHAoBj7MMj\r\nKUdie2Pwy1hegbw3ujfLlCFYY16NeYTiMTqnyrUn/HlQH73xmoxuyRUsrouX\r\nFW8Zs7rcXhjR84HhZ7AHYuOBw8Ej2CGRz6iutkECmOb7m6WuHWthD0CiSEpf\r\npJidNbWnTqwNrN5Kirfo1Y8SlW1SWI/vpqvbErBaTDuwwGrW73aNLRcFMMAs\r\n9W/lNVaODRdOUjC5wWEAPsFnQdaukoMsmDw=\r\n=BTwz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/address.js", "types": "lib/address.d.ts", "engines": {"node": ">= 10.0.0"}, "gitHead": "a8f8ebd33f173455d09d76ef8d8c6dc495ccac9f", "scripts": {"ci": "egg-bin cov", "lint": "eslint test", "test": "egg-bin test", "contributors": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "16.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mm": "*", "eslint": "^8.30.0", "egg-bin": "^5.6.1", "runscript": "^1.4.0", "typescript": "4", "@types/node": "14", "git-contributor": "^1.1.0", "eslint-config-egg": "^12.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/address_1.2.2_1671290769328_0.33254514806014046", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "address", "version": "2.0.0", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@2.0.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "3cdc8dd2026d4fedabd3b42a0bd6e4669b941048", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-2.0.0.tgz", "fileCount": 20, "integrity": "sha512-qLEpeGxlKnHuNiuUFC3UMOBRqCSzzNzidlssCKyR4xc4TJiPHnFcJikmVbyTZC09lDhn5oi9DFWAwkR7hh9Ang==", "signatures": [{"sig": "MEYCIQCZDYSClJzBoPCySZjQTVFlg/04/CSBA2b3N82NnLQRcAIhAIhOyH4DPh15HLE3yNvMpQ4j1+k9sVi8XkEK9fG1rhSM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/address@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 58281}, "tshy": {"exports": {".": "./src/index.ts", "./promises": "./src/promises.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 16.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./promises": {"import": {"types": "./dist/esm/promises.d.ts", "default": "./dist/esm/promises.js"}, "require": {"types": "./dist/commonjs/promises.d.ts", "default": "./dist/commonjs/promises.js"}}, "./package.json": "./package.json"}, "gitHead": "9ecca412cd54d9eb611d5dcad0de8cb748e0a1d6", "scripts": {"ci": "egg-bin cov", "lint": "eslint src test --ext .ts", "test": "egg-bin test", "preci": "npm run prepublishOnly", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "contributor": "git-contributor", "prepublishOnly": "tshy && tshy-after"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"mm": "^3.3.0", "tshy": "^1.1.1", "eslint": "^8.49.0", "egg-bin": "^6.5.2", "runscript": "^1.5.3", "tshy-after": "^1.0.0", "typescript": "^5.2.2", "@types/node": "^20.6.3", "@types/mocha": "^10.0.1", "@eggjs/tsconfig": "^1.3.3", "git-contributor": "^2.1.5", "eslint-config-egg": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/address_2.0.0_1695387598075_0.5019266348515476", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.1": {"name": "address", "version": "2.0.1", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@2.0.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "7740c2c46cd3f76724fd9d85396fcb01a7f0d414", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-2.0.1.tgz", "fileCount": 20, "integrity": "sha512-Fd3nPUe+kPZ0P70sGkV8dAUchT3pQCyTPQO5xW9580oqVBPJe2PtWtK7KGnQBvgTrloWAOvOoX8DksyuIWssvQ==", "signatures": [{"sig": "MEUCIQDmtg3xxinNOiyrL5M2sYw0enuRq7Totohfnaeona3uXgIgTBSc3/QTmpSClJibq2xyQSQhKbhT9dsLZBb15DKxZik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58281}, "tshy": {"exports": {".": "./src/index.ts", "./promises": "./src/promises.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 16.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./promises": {"import": {"types": "./dist/esm/promises.d.ts", "default": "./dist/esm/promises.js"}, "require": {"types": "./dist/commonjs/promises.d.ts", "default": "./dist/commonjs/promises.js"}}, "./package.json": "./package.json"}, "gitHead": "96f778abbf8d6027c0b7ce873e31131b339b1d6d", "scripts": {"ci": "egg-bin cov", "lint": "eslint src test --ext .ts", "test": "egg-bin test", "preci": "npm run prepublishOnly", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "contributor": "git-contributor", "prepublishOnly": "tshy && tshy-after"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"mm": "^3.3.0", "tshy": "^1.1.1", "eslint": "^8.49.0", "egg-bin": "^6.5.2", "runscript": "^1.5.3", "tshy-after": "^1.0.0", "typescript": "^5.2.2", "@types/node": "^20.6.3", "@types/mocha": "^10.0.1", "@eggjs/tsconfig": "^1.3.3", "git-contributor": "^2.1.5", "eslint-config-egg": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/address_2.0.1_1695388016881_0.33695152974467146", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.2": {"name": "address", "version": "2.0.2", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@2.0.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "7c1063d26bfc647a695f3bd7bdb29d688b5fac73", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-2.0.2.tgz", "fileCount": 20, "integrity": "sha512-u6nFssvaX9RHQmjMSqgT7b7QJbf/5/U8+ntbTL8vgABfIiEmm02ZSM5MwljKjCrIrm7iIbgYEya2YW6AaRccVA==", "signatures": [{"sig": "MEUCIA7FqndMod05niNKVb8Y3RRxTGVfN+0nJH8FQ4osIPKDAiEAxbSCrc5A8JC6Yle5cDl/ilnN4HYQKpUCv3CyFfza1iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/address@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 62346}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./promises": "./src/promises.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 16.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./promises": {"import": {"types": "./dist/esm/promises.d.ts", "default": "./dist/esm/promises.js"}, "require": {"types": "./dist/commonjs/promises.d.ts", "default": "./dist/commonjs/promises.js"}}, "./package.json": "./package.json"}, "gitHead": "dcb8e8a9fb4910bbf5eb32c37a836fb60954be11", "scripts": {"ci": "egg-bin cov", "lint": "eslint src test --ext .ts", "test": "egg-bin test", "preci": "npm run prepublishOnly", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "contributor": "git-contributor", "prepublishOnly": "tshy && tshy-after"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"mm": "^3.3.0", "tshy": "^1.1.1", "eslint": "^8.49.0", "egg-bin": "^6.5.2", "runscript": "^1.5.3", "tshy-after": "^1.0.0", "typescript": "^5.2.2", "@types/node": "^20.6.3", "@types/mocha": "^10.0.1", "@eggjs/tsconfig": "^1.3.3", "git-contributor": "^2.1.5", "eslint-config-egg": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/address_2.0.2_1709183476895_0.7875943395045555", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.3": {"name": "address", "version": "2.0.3", "keywords": ["address", "ip", "ipv4", "mac"], "author": {"name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "address@2.0.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/address#readme", "bugs": {"url": "https://github.com/node-modules/address/issues"}, "dist": {"shasum": "e910900615db3d8a20c040d4c710631062fc4ba8", "tarball": "https://mirrors.cloud.tencent.com/npm/address/-/address-2.0.3.tgz", "fileCount": 20, "integrity": "sha512-XNAb/a6TCqou+TufU8/u11HCu9x1gYvOoxLwtlXgIqmkrYQADVv6ljyW2zwiPhHz9R1gItAWpuDrdJMmrOBFEA==", "signatures": [{"sig": "MEYCIQCEk/03JdBqOMwwi+MDxhTJP56pQvBeiVsLbA6fKuWB5QIhAL3+7tKOXbEx3K0seVjRFV/VAn5apZDOTL//084OLNdT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/address@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 62590}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./promises": "./src/promises.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 16.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./promises": {"import": {"types": "./dist/esm/promises.d.ts", "source": "./src/promises.ts", "default": "./dist/esm/promises.js"}, "require": {"types": "./dist/commonjs/promises.d.ts", "source": "./src/promises.ts", "default": "./dist/commonjs/promises.js"}}, "./package.json": "./package.json"}, "gitHead": "75fbf02740f8880949ef76b4b3563bcdade6c77d", "scripts": {"ci": "egg-bin cov", "lint": "eslint src test --ext .ts", "test": "egg-bin test", "preci": "npm run lint && npm run prepublishOnly && attw --pack --ignore-rules no-resolution", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "contributor": "git-contributor", "prepublishOnly": "tshy && tshy-after"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/address.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Get current machine IP, MAC and DNS servers.", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "devDependencies": {"mm": "^3.3.0", "tshy": "^1.1.1", "eslint": "^8.49.0", "egg-bin": "^6.5.2", "runscript": "^1.5.3", "tshy-after": "^1.0.0", "typescript": "^5.2.2", "@types/node": "^20.6.3", "@types/mocha": "^10.0.1", "@eggjs/tsconfig": "^1.3.3", "git-contributor": "^2.1.5", "eslint-config-egg": "^13.0.0", "@arethetypeswrong/cli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/address_2.0.3_1718764313903_0.8801896303815124", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2013-07-30T15:10:01.881Z", "modified": "2024-06-19T02:32:57.117Z", "0.0.1-alpha": "2013-07-30T15:10:08.745Z", "0.0.1": "2013-07-31T09:44:48.849Z", "0.0.2": "2013-08-08T06:21:39.984Z", "0.0.3": "2013-11-04T08:17:10.694Z", "1.0.0": "2015-08-05T23:45:45.211Z", "1.0.1": "2016-09-30T07:51:25.004Z", "1.0.2": "2017-05-25T16:28:38.130Z", "1.0.3": "2017-08-24T09:35:47.914Z", "1.1.0": "2019-04-24T14:05:35.454Z", "1.1.1": "2019-08-22T12:01:15.262Z", "1.1.2": "2019-08-26T07:58:02.946Z", "1.2.0": "2022-04-29T13:00:03.497Z", "1.2.1": "2022-09-13T01:09:48.614Z", "1.2.2": "2022-12-17T15:26:09.503Z", "2.0.0": "2023-09-22T12:59:58.256Z", "2.0.1": "2023-09-22T13:06:57.229Z", "2.0.2": "2024-02-29T05:11:17.081Z", "2.0.3": "2024-06-19T02:31:54.114Z"}, "users": {}, "dist-tags": {"release-1.x": "1.2.2", "latest": "2.0.3"}, "_rev": "3594-8e68dec7c025a85c", "_id": "address", "readme": "# address\n\n[![NPM version][npm-image]][npm-url]\n[![Node.js CI](https://github.com/node-modules/address/actions/workflows/nodejs.yml/badge.svg)](https://github.com/node-modules/address/actions/workflows/nodejs.yml)\n[![Test coverage][coveralls-image]][coveralls-url]\n[![npm download][download-image]][download-url]\n\n[npm-image]: https://img.shields.io/npm/v/address.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/address\n[coveralls-image]: https://img.shields.io/coveralls/node-modules/address.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/node-modules/address?branch=master\n[download-image]: https://img.shields.io/npm/dm/address.svg?style=flat-square\n[download-url]: https://npmjs.org/package/address\n\nGet current machine IPv4, IPv6, MAC and DNS servers.\n\nDNS servers receive from `/etc/resolv.conf`.\n\n## Install\n\n```bash\nnpm install address\n```\n\n## Usage\n\nGet IP is sync and get MAC is async for now.\n\n- esm & typescript\n\n```ts\nimport { ip, ipv6, mac } from 'address';\n\n// default interface 'eth' on linux, 'en' on osx.\nip();   // '***********'\nipv6(); // 'fe80::7aca:39ff:feb0:e67d'\nmac(function (err, addr) {\n  console.log(addr); // '78:ca:39:b0:e6:7d'\n});\n\n// local loopback\nip('lo'); // '127.0.0.1'\n\n// vboxnet MAC\nmac('vboxnet', function (err, addr) {\n  console.log(addr); // '0a:00:27:00:00:00'\n});\n```\n\n- commonjs\n\n```js\nconst { ip, ipv6, mac } = require('address');\n\n// default interface 'eth' on linux, 'en' on osx.\nip();   // '***********'\nipv6(); // 'fe80::7aca:39ff:feb0:e67d'\nmac(function (err, addr) {\n  console.log(addr); // '78:ca:39:b0:e6:7d'\n});\n\n// local loopback\nip('lo'); // '127.0.0.1'\n\n// vboxnet MAC\nmac('vboxnet', function (err, addr) {\n  console.log(addr); // '0a:00:27:00:00:00'\n});\n```\n\n### Get all addresses: IPv4, IPv6 and MAC\n\n- esm & typescript\n\n```ts\nimport { address } from 'address';\n\naddress((err, addrs) => {\n  console.log(addrs.ip, addrs.ipv6, addrs.mac);\n  // '***********', 'fe80::7aca:39ff:feb0:e67d', '78:ca:39:b0:e6:7d'\n});\n\naddress('vboxnet', (err, addrs) => {\n  console.log(addrs.ip, addrs.ipv6, addrs.mac);\n  // '************', null, '0a:00:27:00:00:00'\n});\n```\n\n- commonjs\n\n```js\nconst { address } = require('address');\n\naddress((err, addrs) => {\n  console.log(addrs.ip, addrs.ipv6, addrs.mac);\n  // '***********', 'fe80::7aca:39ff:feb0:e67d', '78:ca:39:b0:e6:7d'\n});\n\naddress('vboxnet', (err, addrs) => {\n  console.log(addrs.ip, addrs.ipv6, addrs.mac);\n  // '************', null, '0a:00:27:00:00:00'\n});\n```\n\n### Get an interface info with family\n\n- esm & typescript\n\n```ts\nimport { getInterfaceAddress } from 'address';\n\ngetInterfaceAddress('IPv4', 'eth1');\n// { address: '***********', family: 'IPv4', mac: '78:ca:39:b0:e6:7d' }\n```\n\n- commonjs\n\n```js\nconst { getInterfaceAddress } = require('address');\n\ngetInterfaceAddress('IPv4', 'eth1');\n// { address: '***********', family: 'IPv4', mac: '78:ca:39:b0:e6:7d' }\n```\n\n### Get DNS servers\n\n- esm & typescript\n\n```js\nimport { dns } from 'address';\n\ndns((err, servers) => {\n  console.log(servers);\n  // ['*********', '*********']\n});\n```\n\n- commonjs\n\n```js\nconst { dns } = require('address');\n\ndns((err, servers) => {\n  console.log(servers);\n  // ['*********', '*********']\n});\n```\n\n### Promise style apis\n\n```ts\nimport { address, mac, dns } from 'address/promises';\n\nconst addr = await address();\nconst macAddress = await mac();\nconst servers = await dns();\n```\n\n## License\n\n[MIT](LICENSE.txt)\n\n<!-- GITCONTRIBUTOR_START -->\n\n## Contributors\n\n|[<img src=\"https://avatars.githubusercontent.com/u/156269?v=4\" width=\"100px;\"/><br/><sub><b>fengmk2</b></sub>](https://github.com/fengmk2)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/1147375?v=4\" width=\"100px;\"/><br/><sub><b>alsotang</b></sub>](https://github.com/alsotang)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/10237910?v=4\" width=\"100px;\"/><br/><sub><b>jkelleyrtp</b></sub>](https://github.com/jkelleyrtp)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/63956?v=4\" width=\"100px;\"/><br/><sub><b>slyon</b></sub>](https://github.com/slyon)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/1409643?v=4\" width=\"100px;\"/><br/><sub><b>mariodu</b></sub>](https://github.com/mariodu)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/11351322?v=4\" width=\"100px;\"/><br/><sub><b>mathieutu</b></sub>](https://github.com/mathieutu)<br/>|\n| :---: | :---: | :---: | :---: | :---: | :---: |\n[<img src=\"https://avatars.githubusercontent.com/u/2139038?v=4\" width=\"100px;\"/><br/><sub><b>zhangyuheng</b></sub>](https://github.com/zhangyuheng)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/32174276?v=4\" width=\"100px;\"/><br/><sub><b>semantic-release-bot</b></sub>](https://github.com/semantic-release-bot)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/1400114?v=4\" width=\"100px;\"/><br/><sub><b>coolme200</b></sub>](https://github.com/coolme200)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/5856440?v=4\" width=\"100px;\"/><br/><sub><b>whxaxes</b></sub>](https://github.com/whxaxes)<br/>\n\nThis project follows the git-contributor [spec](https://github.com/xudafeng/git-contributor), auto updated at `Fri Sep 22 2023 20:49:32 GMT+0800`.\n\n<!-- GITCONTRIBUTOR_END -->", "_attachments": {}}