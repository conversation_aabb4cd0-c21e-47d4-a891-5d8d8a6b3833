{"name": "@cloudbase/manager-node", "dist-tags": {"beta": "4.1.2", "latest": "4.4.2"}, "versions": {"0.1.0-0": {"name": "@cloudbase/manager-node", "version": "0.1.0-0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "3cffc7992473bdcce6527d6946713123a5a9c5ca", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-0.tgz", "fileCount": 120, "integrity": "sha512-vDnKmcQYVD0zC/BjVTLK8ciHN1wdMBkItmGK6RPwejNVH4OLbKe7uhroABfUfVIWf4uidBAwAFPSknVlQmc0IQ==", "signatures": [{"sig": "MEYCIQDJkQB/v+4YLSFuZBOGWW0lSQVi24lOkwTJpMFHEE3x3AIhAJD9gbiAi//k+rTXw0Rd3imrjIC1KA2P7BXJZUzl18WH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdg2hVCRA9TVsSAnZWagAAco8P/0nrKx9mWzqJqLm1Ux8z\nYoxHEGp8ZF45E+3+URap3xU9uCpYbtI0Tkpfe5O/vCRVe0X9wxlOaVO4Maog\nOjbepRrwcD9VZhxfeSkbxDOaL9/Z7PpFTAKYfZieAf5SUpTIW/LfOaC2kuU0\niwrq3FRLWdMVqG+NGanhfZ88DHbMOHFH61LASCG1Jw+GwZ/dz7UwucdrdUq6\nfhkKnRu5vUbLe9L7QMIonHVdIK5sEZ5lPa5Xsar2QmX3PNpJtqDGUHmzc81a\nG+MZ9jJ4PHrBjrQnSk9ifsKW3AeZB/DXMHE1Oa0DL/MlbyP+ypdkwAN/BGp8\nKWnAY4kCj3MJwmioNhXOjob7M7b5bQbylAImf9hpgJr6BYgGkBu3wgmZvHe+\n/RvJRA0c8Ow0Dg+kmFxL0mxZ+xPW9vYR1worwGuXibzVNmNygXGIabP9AKl9\nDpfmSHlg48WJloOgLoS0sH+iR6HNUU6zP9fs9d+uEtM3w/ZQ8xYSH0U70/tN\nKJ8nFvwqttFqFdp8+bJ3UzLllgajQ78pMs9deE/gzPY3Ga2WqF7PYnchywht\nStFiuy+GIiNLMuhg3wteNU07B9Yg3ekFtIoAMYSaVXv3hE4RY1SGGr5l76l/\naQm6vK7+fYB4gyf514YU9Kk9EuRQWv47pj+K5BFQl8rOkM2Yr3rUWSnl0ERf\nVKgf\r\n=2O0m\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "jest": "^24.9.0", "ts-jest": "^24.0.2", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"husky": "^3.0.5", "eslint": "^6.3.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-1": {"name": "@cloudbase/manager-node", "version": "0.1.0-1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "dc3be96201f563366d51c54da48e0b226ac8ce99", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-1.tgz", "fileCount": 120, "integrity": "sha512-4G6Db6IhxpcIWMnNi6qECQwnn6J48kQxZpbj2bYClVPqZqb2t39U12RQqAfHfBzfiY50csX/SSt9BTNUNmSc0A==", "signatures": [{"sig": "MEQCIE6RpfnQYW5nfXU7KRuMpylxJWKQJlyt196l+TFNbKdNAiBu6kmzG6zQVMWr/9qUhilSWVKVRubbh+sLajUga0AyHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhD3+CRA9TVsSAnZWagAAkOEP/08rQoD4Xw93fUQ3s9h7\ncXL1WLRYNoZ0XnOfiedhQbPcURvMNRNBPoIQowvi2MP0zpxiNDpyGwd+L+Hw\nZH+P4aZmn/IzPxOkn62qCFxXUG2lzyCjC/j+H7+vu5BmomLdbHLBvJeQXeHx\nLoV1XI9o7RhAwsyxUNQLD8CNv2fZB0tPEouyeIGn6nt6rN67rs2D/QzntoOn\nFpui+dQ12Wpbh2aEj0CfUg7yaUOnXrR9zZDD0tSspb1D+aAtOROoPvpcVzhF\nfW6pwFOloG29ny30yJTdm17Mdux7QKcsboD4QJmBKQ4erbElydV6aoVrsmmy\n+XGfjR6o6GWv3PhUiyHXoswzFu4M9jvtuOsN0H+fPbJhNoP90Ic+vwS4+BqW\n7Id1tINQ/uCECdiNjsyKyzeGNcjtFI989orixzEHW4DHFUsUNiIFfL8xBjGy\netq1jkf0WG7HauU28ZDKeKFSCkW8ifmWsTdapQ0svxc/qaycwMKT4bmBV6L5\nAvxXJcv6Gu8lORUbczqTIRXC7v+cSiVNK08mnSO3r4eDMrh0Bui7yHU0QuZp\nhaf3veTTEAUNMh7pdGLOhKHzCeMcGtO07vQnaxZOHG/jYYF6B37t57ALMfgj\nhvPFjD8iWcEWs2h6ytVQKQtyOVjVw6JzRVcLAxjxV9mutn/P/uPnlq8905M9\n9KVI\r\n=Y95z\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "jest": "^24.9.0", "ts-jest": "^24.0.2", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"husky": "^3.0.5", "eslint": "^6.3.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-2": {"name": "@cloudbase/manager-node", "version": "0.1.0-2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "9750b39d27b2f0693f591a92baae58c3d806fd66", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-2.tgz", "fileCount": 123, "integrity": "sha512-jvwCJDKf756k3dj5x9QoEGsqHghgR2ddkv+DonLf3nd7hbY+2VjAU096ZmtpdIadtYjS62WAJqINMP156jon+Q==", "signatures": [{"sig": "MEQCIETUg9+RVT3aRdaetLpnH+K4nIToln4Kq0tNfxfOdMBHAiAlQMSMxGLpOsemu2lS7vpOgNQRRfqiiLfzEuNvia027Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdidI2CRA9TVsSAnZWagAA3awP/0TDzSd1ergXUSaY9w9O\nad7xG27y+W1CSsiCWfaWOuIhNtfe9W6pMrX2MqEF604In97MQYN1v9HmrzWw\nAkKqU+L3N373mc9co3n5IDVKXKbeXZVUZxhFTWX1XvdDkCxlxwhISFQ3+szW\ndmgUMunw8JsbfAnXuXdVgknf3Yn+Ju1D76ZaRRp+tSYdGDF1q+E6P39PrUSj\nvzdhukr1rKWt3Ai+NK2hdueJKIGsSGEDGIaV9d4BDEtasKAFBJSf1xXxR97K\nM+Kzlp1tMl6Iqs8wOajPAJTkZ5nKark1HMGhI6w/IOyXfsmnL+iYuM+jQ7rK\nMrlcsP+r4hJKZ1P2UUPxGjoyYHVhUL0FkxXPgsibhP+rnxKw+HAd0croWHur\n3iC+yhvrABeKDudbsNWN40dhCAwi6j6ELrYFMi08l0Ea9QVrr7jN74wBXyIk\nDwITNx/Gm49hd9iBeGertDUS9u7nq4bMtJXw4weabNHoTnjQc6JgSJI/8j0G\n1h1Qo/W0k7JESYpUWTEY1vup05dkKtGu03J9Xqw62psGgkFBDXHkdmFF1On3\nyGmENPfX5kzksfUTnpg0L9ow+3ljCu4grHu9z0rxWfqTgnrWEgfnA1or9xXG\nABPGb7VN24fNFrJHuk22D2qlffXDDfftcGfngus1XPmZ7AGJMmq4mIj75zpZ\nc8VO\r\n=xPV/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "jest": "^24.9.0", "ts-jest": "^24.0.2", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"husky": "^3.0.5", "eslint": "^6.3.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-3": {"name": "@cloudbase/manager-node", "version": "0.1.0-3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "83eb58f57552cb337953b7ecef89eff481aaba94", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-3.tgz", "fileCount": 103, "integrity": "sha512-AcKhItW75My+z2pJc9l3hSu8LrmM959mJ510PSus459cnaxrhlHM1uzPp3vf++DIiGpwrqU3iHxqsMesjjM6UA==", "signatures": [{"sig": "MEUCIQDAAIMlEfIp+BzvHywgZOxavydTKMESOLXCgHkcSVsopQIgRqSAv1KgYuAkabb9tkyWD8yNttzKP6WYl2G2b/M2w5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdidSiCRA9TVsSAnZWagAAr4oP/2TdI5vOuEUl+Jv2EAi+\nbIqKJOUxRCEd12AlRJR39YAE6P3Uqd0eAi/UZ2ojGWEuL9KnWQYlo8J/nCNL\nOs1z8jtmlYKJgcLeB5bDNt3xDFiiBR+EK/F7tO7PsRNhqHm0OStsggDRbjIo\nwdYc1BWetrdPTIVfw11/O1N+X0fBqoX9zTzHZMcAnm7kb3KpxyVDw3O8Anu9\nNXDkrD7yKqkCgDJq/fD4y7qkXTfHsDzlyScPD0mQHAxz10Nyg4FrI1htP8yw\n8qF0JMSSFRxOMQKWYQyzWuE6cnqOGQZZ6rfoWMswH4HfRjErQYP31yQ7KfU9\nYNxSitgSL99toFJ8zuzuHbDosQoO64cpCHrCIDpEHpaElNNeb8MYVNH8zSId\nwJYNjxAO7X1QHVfKU8p/pnVmzo46CRXie+WwusrBMs6KV+UaFk0QZUptXJ0O\nBopGlZj9NbBTi2biQnIenE8Skx4IJnVAqdqbnMh/GfVDjtQz3IkfonjUU7lU\nhyia9lfNzL2jUKX0oaU5JIl0lGlF8MGVpcMIX/w9IHH5Khvg/P50wLPRBjfu\n/lrji5OPuJ/tKPpz/jlAkHYNBaRk1Qd2gPTD2xB28A5mYZ53mREvRBJkzKTA\n+WPsYf+YkC0dQHg1aHfL7ocCmivz5azlG0SsAE5+I3Atm7jCar4chrhG5nRc\nE3F9\r\n=SB4r\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "jest": "^24.9.0", "ts-jest": "^24.0.2", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"husky": "^3.0.5", "eslint": "^6.3.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-4": {"name": "@cloudbase/manager-node", "version": "0.1.0-4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bf981ff4eb985d6899aa404a4829d0106d72c20f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-4.tgz", "fileCount": 85, "integrity": "sha512-UZicDbSmvTNL27AzPpFSM17B7x/bMQqOMkPV2dfSJ1kM0hI1vEYu5VFV6LEsif3RXhU627LSlNKrey1hcMEP9A==", "signatures": [{"sig": "MEYCIQCu1aLUHkGIdqovs0sCdrIL7jmSIxosKyihJWIhkImGowIhAMuKUM5k0Pz1tUMOZW396BRD/Q7qVik9CLJ9kU40H1aC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiw2yCRA9TVsSAnZWagAADP4P/2RbUohBTykljbfSo0jZ\nQTj3CetDFIcULafPpTIwpGe7F5OdeJMeOdLhOwjV2FgoVolCXyu7eI6Oq7gS\nXKtKjt+FBfo2mYY12U+oH4LEEwqfUoIm2iBgkGoHERzyoo8mzw3G2p87wqCy\nmRiNNr0N0Nz10Ufl90gedjR+4GBFw3PLMNEg9TXlnhskpKijvlyOMmWUmKd3\npizVGotzoSeapjQMXIKhbAKsfIfXlytoPn/zUOFQz/FJEs1HOLu19WRVz4/D\nmTwimeEPH0DMW6s8fAdbAa0CqG6IRlDJLUAmUzueboISI/ynN0xP0Kqr9gxp\n+2VNUVObyPb5CfAzHWsbkVrqfGsmiDuvTJZtUykFZcQq5RyBbkYkIY/UMuiH\nk/+pur7PHe7eZ+UEOlDeZeznvhrswmEGWA1FGrB2Ky/5ihzKyyE/toUPof+x\nXcgPegV4d+vGy/f2CZS6t7tYFzX/bpdpz9g6IzFR6hYPLiyrKkH8qILFgNBM\nNMasyCg7VwLANjwv0wcyiQwGDaEF6r/BdzG0ojxcyQvJOsnC1UWZnepCDzR3\nFrA5eNU3ETjjjgwpgqS+R7lmEfOIVT1q/A1onqE3GS0/vffKwcy7HNskd6w4\nwJZ9iK1DvsvIsk2yvWa1zDuglQdWBlRb3bxWd7vCkQuUg+ATPfcaHdmt6ZR2\nbmoq\r\n=bi2u\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-5": {"name": "@cloudbase/manager-node", "version": "0.1.0-5", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "478d7cf366fad7033decbfd511c84f36e1bb49e6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-5.tgz", "fileCount": 85, "integrity": "sha512-pKMoZnOLMBkZfK3hEQ0uKMhc1lwhl3SCl9J52JbAlmMy6jconGvbEyAq+T8dT3P+X6c5kzE734I0SnpRjREi9w==", "signatures": [{"sig": "MEUCIQC2bmGLqa/dW2q920a+c/iC+WU6+fjtbiZz3ta3XDsZXgIgJ5EwX/vkK3zv5HYnAq4oNb6mtL8EFmSstElkPWLT9hI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjgGECRA9TVsSAnZWagAAjjgP/0reRTfG58v2V76oVJ1G\nl9ODSIqt2vJHC/EQSJHFDAisG3WCGEYP3fHbm1wanwl97IKOIGqXt8AvjDjN\nMY2ce0qPZ4uvtGEUfpD8RB7Ppj8atVQb22eFWXc9oNQtHdpexx8HsJJjQ5rf\nC2Shb6WvX6p0qUnXdkPIE1+LwlEJMITNdqMvyGHmvNEnOaABWVZVEd5kSITO\naoywrcfaLb1c2I/4Xi7WdJvav5Au2dKRS/xqkIbi4A/HMsc10tyd3AswU88w\nD10EArusViDEEPBYNAxvImxzLrSYH5yHrlNrgPlL+3Fu88ri/o9ihNZ8hCO8\nh8wKI8FjZZtKzIpI+r+A/dA5RDuaJSA21RQieo9KVkS5FuYOudr7Uxc69SQh\nyKkVyc/Te5mOh2iR+Sjfel+/B5CebKqQscFf+P/7ubYdtRMLIzPm7Cw/MKes\n/iMgZ06oORArDG/X3b7gfNrtP9LLaTPaV3Pss6GT3G6HNSABHCZNszodaVu7\n9GS6wQ6Vd5QUpum1gwh9BQ87cbeqE6ldi/NSckAnp6OwmJLgygboWa2gpRUx\nkF9ek4XZjzujyZg+JHNQO7wxRBhFXI1ORWxvl3bgeGFNP2+gvXBILz5uG+XZ\nkG4b44J8LxXrc64Wse1TjyZABUTgBsmnDwEHJX3/U0oeXQ312FOtxpabGtt1\nFBSV\r\n=66VL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-6": {"name": "@cloudbase/manager-node", "version": "0.1.0-6", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "475e81994a3da07afbfab79a2e4afe51896f15ed", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-6.tgz", "fileCount": 85, "integrity": "sha512-YFLMSVJDm6V18QxlUCF+k1kPBMXEU1QOUcF4iG/LCcSEknGn2XiL9TsUecEDuvMhhNmVOA6OXJjZlrAKespwqQ==", "signatures": [{"sig": "MEYCIQCdsDDvLM3tGL/0L1fL/mEriRSgMEg/1hknzfvh9bYVnwIhAOXa4CiVQttgVQn7E1T9oxZ/JB8Q9frNv+gtJL9paLeC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn/MiCRA9TVsSAnZWagAAMS4P/Rksw7Nx7pJcmqBrG3NJ\n61/TGZr6pdHxH8a9LFlqB/8YfIZR4/ueFzHHbj3egLyOpqlM78tJobimii+W\nVCdSAfacMrL/QkjHS8Sv4LhJ7AqoGLN/nL1iOK8kvrW6dxDtK7O19MY46C6A\n4D02RVXuFvlS0RfN0ymkfa9ufsWTnD4KhX0+3RoEsMN7crJR54ddlgwP64eM\n2hxcO5UBW/SsKqHGW5QOb0mGMwskFP9O3oY6pPK0SIuUKVORiNpPbRTo51Jc\n5aIZuGqXVw850mGLV2fHRB0R6N2eX6diKJ+gOgVECOK94q/yoiNotEYiz5x3\nlAt9CqAQj1Ax65A1253oECWOED7fWnN7dbcs+71ar9LRjEOlfEJhC221hgBd\nEvmSZi7m5kRKG0Jv6IJIqBO1m3i/rWehwDQ75oaMyjVY9ty2lK2QduejsKjh\nMyHAXhdvI+CcruWq+0xCrxL1XO9DrfEBUOsVZaFVYuQhfYj8rSa/o0CttY2G\nes6IRniBTQGvybUYRW5+wbXGd55ttGJjTFcwwyO8QjiBG6qKtz0Rqt1bfDsF\nh8YZgCuS/aqzTc3ul6u2FEOF6zTISPIir6jVVMR+CGa2ckaWUZy03OyykBu4\nDurwZsJulsDb8vC4FMmWnMAnFD8rK/7GHkFvN8e/mGWfb9YNPHJe52u2jslq\nsVIc\r\n=mzWp\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-7": {"name": "@cloudbase/manager-node", "version": "0.1.0-7", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "6cebf3082473c6dc35d52adf2cb7f95a926758ea", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-7.tgz", "fileCount": 85, "integrity": "sha512-C2cr7MaIhIJ0PQ9N/RlkCRldZ4lWmdTG+jJa436WyH82rm197AEdzAqwyQYwxORAE2lM1cu4rf3q1vpG0EySPw==", "signatures": [{"sig": "MEQCIHYQ6ckv4mOjynnDQO8IzbQhAsHN5EFEKm+sd8oB/mZPAiBk38g0aY5CYl31PpRrNNbvY40UZ1Q6OxWCb/Wbmd42SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoBMpCRA9TVsSAnZWagAAzT4P/3Fo1p65eS6gpaifvwnH\n98sT/fROtADzxbNUdnbk0eO6BEqPGsOcuNa5Af/nq9xUJ5/p5+6t6ECy5gd4\n8UxyloKEqQo9cDTVAeJsZO9sEEBG29IXWqoO5sNvUeXNKPzQexkU1SkTMXES\n26u5QXuJS+jgAjlixSVClRJ4g0OhK+2vXlJ4zrO9M+H8/6Dlur66bTTadR9I\nLrPjRNP5n+l3ADQQTVv7rV5btcKfXUW+D6bCtWeD/K0zclkkM8q2nQcXiraj\ntgd0DzxEZX0KnDWAf0110NgR7gZ6Krf6/O9lUEehSm/JGNU8FzDLkq735A24\nK4/+shOkvQmIxva+HHjGvnlMcH8HUuJGoOKh31Rh9TJjv/d9uc1wwh4mr9+8\nS9hGi3HdsQKC64w6pGzC/k0OM0sqkPUZzywROETedITqB7xKOxAu0aWA0nG+\nIQYS3ZWTGqdjzuog5I9pPTfj040Iwrxw4n1MAJvSk2T4DTI2dkxrj3ZX3Ygl\nCTs8WRu5I/+kEaRvhcGcVauiEzT7biYm33aV1ZcszL4RKLyT65MtS/e21sk0\ndQcaq6NVgsjzaieoijc4Ri3fziU9ITd7l69rGg1kMlTHsaYxeQeqqqKnsBfT\nXu158r0wRzU+eNkgqUjcYLqdpDZ8k3G7X9Oj4Qtzr3piXHzCbf3UoNYkFmru\nLs4o\r\n=pe8B\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-8": {"name": "@cloudbase/manager-node", "version": "0.1.0-8", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "f9b4a99600d504da32fb618cbfa2fef1cfecf304", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-8.tgz", "fileCount": 85, "integrity": "sha512-Ews8hLTMgriZNcNBL3S/uqbJ/EJj8ZsRlbOftdl5dJJHp5dAaQ1uPEi3oVrT2pnL4jyBVJnG263zeshoX3+Jwg==", "signatures": [{"sig": "MEUCIQCQOYskK3HMum4WPoseK0R1wXLuWblopR1RKFe1tv2qowIgWjmXm239eNhYxRO7KeA88mbBYxLS3GYhIeRgnE7zhRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrmUuCRA9TVsSAnZWagAA718QAJmbMRsmu2y535SP/PYY\nYYT7Wwy8f2Mnk73UcaUrET5+gK+7c2kOrTK/ngKfE+MYY8qB9zHseU1Y21U/\nsKTXaV6Q5yaK5ovefpdi3E/1x/64FlHF3mRXEWN+nwl7iB8mbGS35erxGLzj\neIMitABKYQqkCTtdhvHFzIeraiDb2fzm7QU7Zguy6FcEt60SM5amG/hmmHdh\nv8QbKi8I6SsTCQCNIvy2HZ2qTSAm2UUL/YuuhFrzenx+b8T/QvK57qfqxSt/\n3dvjOrr//1QJeZJnOJFq7Z8kIzd/WEqXlpw1e1yYIZ9TYKKH9LbQrBMd8JHL\nBpsH+BvYkjM9V7AN/ca+YUsskj8rdomb9rHoLOkOzw5zhqGuZpIRKIjFlqGk\nFUgoFiNM6aScWUr+6rMseJ+wbntPCMJHj+ipjNnjYnfU0UWS0Er8ydNHYjqz\nr49Fr8FZzoTlN98OdcFABEe92NeYMKJItD71JXc849alHtEC5aWDj9YlDKNM\n0L+eVTRGC9lsqeT7Usz6T3untVNuPVgZUplpqiRwCljF1swxBTuHBuxxoR6F\nosxLAbsrG720A4fCtb4s/nWNPWe24eM8JAlAeax2je+nfwwk2yoPLP9cDWrl\n2kOLnVv/G7bcLY/JusDlFU8Slrdu5lLiKkgBQmdQC0ZkCLScTUT8aDrYEXOs\nhGl+\r\n=8Pc5\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-9": {"name": "@cloudbase/manager-node", "version": "0.1.0-9", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "00453df69251c620b937a8e77a78540dc41fa1ec", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-9.tgz", "fileCount": 85, "integrity": "sha512-dAMfr+j/v1supjwDvuRligHbyYMs5H2BWqaVh9+TjmDtPIUBDDsKgD+3mfanO9vUsGB9uYiUzbN3o70TXY35Dg==", "signatures": [{"sig": "MEQCIFReIfnI9ZRFqKsrkNYJc4Dz1NzvR9Atz3M9eMvTl26AAiBn3OGJzDOCDLTYV3vON0zzOeTI4Pd8d8bmUqAbgP8F5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdroUxCRA9TVsSAnZWagAADjoP/Aid0oyBP5DuD/qOYEd4\nZvZXaOqDy5JlUHtpdZMqO3VSE17cmPaW/UgTVxXEFt/jp0oQJhO59hNluhN/\ndYwPeoBX+d8xxb+gxvS+SO9Ox9ERH6NFzPjlYRZQ1PddPqUOPtGkOYoR0OeR\n4p/rmFun/TBa8Wk2Jrw8VYTFN8E3YLL+ZALNE308e+uwxuY/kOz4HGVIOu03\nk6Mu4OoUFVVMcS7SsTqngXp6z0sfAXvXZmqfH8uAIqXezvb+NUegxnXky+Ke\n2WNCsWDBLSzJ+RCFKO5zD3j4Wm6p8xKNUHHRPU5uLWnqRNM4c4eQWfUSrhsL\nOeiwkpkVWwi//76EIHRYQhuwfclGRBFFuagBJILst/+3cxO6AO3v8yLfw+Ak\nZsuUcBAhGADsDNNyHrwDVRMkhlZw9dmPHlD3g4sERDXYnH2iHzOLzAdX90SO\nFebFOORf+vzhAbD+Qu0bPu/YR8cJphjtvQHV9t9IHd5tWe2vI2z/OC92t9As\np0osQealGr9WUpASGch2SxsqP2KE1E0uHPqGHwvrUA7LOgHU8NtOmJtEVqgC\n2xvrwt1TcW4FVL9eyqLoS9bgIlhc5U4YCHSRs96ZgN9oAxysgs4HO1Ii5W7W\nxYfBlELJXKC6W56f1QO7V7hfzWP/PkTaLQc1a5m2/FLPFX4/7ol1rB4MP5M4\nrYTw\r\n=xucw\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.1.0-10": {"name": "@cloudbase/manager-node", "version": "0.1.0-10", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5e6c1f59730b167385da549b1a3561d678d6efbe", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-0.1.0-10.tgz", "fileCount": 85, "integrity": "sha512-2cbOYz2vd6TcDzyH4cQQGaH0jUoWicKSdpubY7Q9yGEU+EQKP7cFPy7Bk0R4K/p7PdDHP+3wEzDxRJ1Rn1yeIQ==", "signatures": [{"sig": "MEUCIQDWXP+fXpQpjUtXXTOUipG2foPVPN+xdIRKUI1iaBc4pwIgWUoHJQIiaf1xCU0T6S86fqcUFVFjEGjP+Li3g1z8Wj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrq+bCRA9TVsSAnZWagAAKSAP/i4rG3TgSIiqU6GXx+yz\nArcNCNcE2VVHZ7Jd43/HOTvLcLdHiHqX6LOIRWT8Aoe/vOTjZElILeI/RXDN\nv1+ZdY9nOEjUelTXSukWambNixUXbmeQhkleNucIa1e2ujsCha28MJ0St31b\nA3Tds6a0A+qHcRtp8M1wFikvJC5fpZDt8sUWA50oHXhICqWan72wFHOeQs8m\ny3CPEngNiGZa7kCuwUxCDFQ+R1QXt8rt+QKdBVOXBAdKZiCEqm+F7n6k+2Kz\nixcLF7jQkZa5r0dGK8HMbh66fRNbLm8afr+49TrU0cV8Y2VNu6KKVUIHLvAq\nHe8Yglb5/LqYdKdem9RR5BI7jjr7YY25AHndZETCTVODElfMiWpSatUhIzKX\nedSxch4d+TlCbo1vj5tdhGXSFcvLrxHH+YbPPUurki1J8bupo9bs8Uu6MgoK\nkdvXSl7aL6UZqugFjtZSpKqgJGt8u0OzOqKN57aq+1LbNypj7ObjDUzLT32i\ndBu+kH1C67qUpG16sL+zQrrSBUXE0y9wESAKyYJTGt4T6noIpgJV6pjwZ18f\nQkRgn9B5lLce5oiygXq/Qc5h3eU1YvKhFko+eo4j5klLXRM6LJadE3VZPVCD\nuaEwuZ29ePcRsy+WtyFG9NukvLIlrVfi1PY5FDnGdwlRsfDNyKWju824j2FT\nVw5j\r\n=oVs1\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.0": {"name": "@cloudbase/manager-node", "version": "1.0.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "c8a043650abd189edd421867b1c93726acf5d742", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.0.0.tgz", "fileCount": 85, "integrity": "sha512-GkpGEp1vys/CkL+TnD7CUQkgZNCIOpiYbxLGFOGyY3IFVEOZG7BGonYOw4e9eTDMnoaWMhM/Mbad15MgBM7yPw==", "signatures": [{"sig": "MEUCICNZeYEW/wre/6ZDthTHMZvUlC//UATnnKHQjS/M5dJwAiEA/3qlWY+eoaAFCXWAkmV6IovF9HLa9OZbuNB5PzNpqKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdukTuCRA9TVsSAnZWagAAvXsP/RGI5ENTvT0r1NOZEdUz\nvGLEHKaeq3Iie3yUqsjDPdcEkt966bZoUpwlD4Wm8Nd1aG+1zjxMcKI7QEea\nMor4c3P5SvQ+da5y8WoEecYdInpItkgSh3GVwb5IQyXtedd2sfxKA9bYoFsp\nqgvg/WvhM+YVGytmDgvGyDnUS4yA1RqUO4WeKDBIQHkHiVvPU6VxtusXVlrn\nFFnsk46dBv0WB10PZGkrNO+dSxUIdrx+TnNjXE8XDu6IoKT16g2po3MA0Bg/\nWXJ6TzQH/kkqn9rd5n+FalPTiafb/cXpto/aB1Z6xxu1YjkT/0Dv8Wo/PBkh\n5LBcjOHxNee1JB/JQ/V7wokTeShCf4ukRUwowWpE/LYXZC4mz/LXmfKSxHMc\nls9LO6RoJXdYjK+7bD3o/Oa+LVFBKO8b1C5QlL+KP3hO0EkbBLSoLyD2aid2\nB4oBvBo5rP2nduUqCKcjG1nxDuXrDXEPaABB56W5PAPOyxUHamDnsrL/JXyk\nUPqW3cqB1qlxGv2LeUJwGgrxFvClzvi/6pyS4j5QkwysdrMrRTncdrJO8EOg\n+mE1G1J6JyC7gOknzbwpebnzemBY3TjsvBIPev3Zxs4Cm6tAumAJESY/jqdz\ne/lzE4BloF+ZFOIUwhD26VVU+M+NycU2dLGnNlUNOcEXGroOliQXhs67jfDu\nZWvU\r\n=ACOM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.1": {"name": "@cloudbase/manager-node", "version": "1.0.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "6862f93426b6b53a28002d41492a21f01f5cea5b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.0.1.tgz", "fileCount": 85, "integrity": "sha512-euKNjdBmrxZyUgkZErgYvkfoxHNlP5WeuaB00DpfxkFrfEhy0lo49oxtLW/EB9Wxv9SFXLAh45O+dYOGO35gww==", "signatures": [{"sig": "MEUCIAzBs1sHN7W9q7FHqV90iHKE8Wgwj2x7hMKGj4SAfgTgAiEAmfRb/ugTWiWhTvwugLM4BNm59oBImqKwLkOf1gNijUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdu7zoCRA9TVsSAnZWagAA3o4P/0cdSBWxIsAPT9Kqi1Eq\nb9TLKoLkOLVy3iK9jtwLVSzyaOQ1jGhNebHFyneAiJWfqNNBgWHYsQulDf80\nWAEa5TcTQHDrRxXVx+2J9h86uzH2DKzRf8v6JW4Iler+aSb239bhrnzkGwJx\n6+zVeeMgJlwqzqm8z57YkdLRjt6FJsMwqrARLvCGIf0t/skkjWBWG3zZnwIR\n1QEjsQuhygiSdxfVupYRCFeeqikhe6Kpnl/TdUgRLHPD3bUmxBOXiuzQ244a\nH/plrSoTEjwGBQ73HYHGW7mXJYyNxTWBO8ribWnsRdSCCUx0yRw8fjyUlVnz\n3Qj0D4eW79l2W8JeNQsGJrUofmCUy0lQiWkELqDGScCK8E6UYEBu9lXrXmZy\nXuKEWv6lEksaALvKMRRzJj7SC6sp2oazRWoNr8tR6vUTS8SVMgp437wNbvOk\nwuFn04DmViq5yZwwHIZ+ka9wIMT8OQUuHaJ8ZoZjui4h3utF6Os/v2ayhxpJ\nAbXAFv7VLDH8vg5ZkZMQGB86E/I4/ZvZaCjEQLlAs0r8A0yvrZk+N6ZUtzHX\nS+h2OpVlM7DYR+7B+60NfNQiykS08UTzw3q2TWyLH2z17Zvdjfrj+nx50JmS\n71efF3CGhxK0Jdl8+wQ6goACPQEkbCriJi/M1w2qjbPcICYzlxXArtu+fKaG\nYeXQ\r\n=rrEj\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.0": {"name": "@cloudbase/manager-node", "version": "1.1.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "8a1d66fc60060e873689268d8bfef9cf9f45cbcb", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.1.0.tgz", "fileCount": 85, "integrity": "sha512-6P/kZpiQFcAFi3UuDR6xiEd6MPYmc7O3Z4FX+zaGp46eWMxkY6fICFnjCcjlil/FT6hTro+oXC3vj+WnXENyIw==", "signatures": [{"sig": "MEUCICnn5IhYHMObvvqJskp9AfsC8/Pvdavz3jP40XlYGBoXAiEA2hUxJT7kgyPdlI++K2emgZ8BENWEAKo5uJ9QSojlt3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3JHPCRA9TVsSAnZWagAAfZcP/Au5IfpXiG+Qps90SS0o\n4nXd0P+MfXiMmIR0p8e0ehWHPstQ2ovQHHiorOgBd7mDiOKHNFJu7/1qf7JS\ncKoB5/kiqVFXHmDuHsD5KDibneXzq2rIdH0Ws19RTAxtfUExMOUjl2FxTHbr\nyZtSAB1Wd6YD5PZfi1fmfEGcUPHVPr0J6ApGIB85T/wKLB7v2StLYI+85dp3\nRYA5jvW1/hUghkqhO5JiYcf5vnfViuf4cyezryQyhj2sOr7WqS5FxQUTZqg5\nkUPXoC7pxQPqI+Jzd0xHSnoggWywrNN2W5D0JbssGTn+6+QsC7PFO9+3al7O\nxRYcxeZhHGi8968jlRFYhkdToOa/OP1aimyTRUAWieFRYVrp+6xnUG/bvvyU\n/lkYO9ivcAb784EBiCgxeUj7THcE3nixPUUhiVyR5mcUzuwaBE0wHwEoqaTs\ncFE5Y8kV+il/KIdrdc5y6CREnnFt6noerHynObGpue2/VFlY5n1ePlNevFC2\njhqfuqB3o77+RMvjqpj5kC0wM3EDCEpemlnuqJrcHEMqdTpTJiiAqcppZEWr\n8hx56ZP9w+l1jXyMViAUveS8SNfvX5pLjCNI8Np/DfYTbJrjhbbDHWzZjhAk\ng74U/bJv9gkjkC7aOeaIca+xs51dhA/RcRm5zO8ooWsHsyG3ByGjgg8LdJaE\nBPqL\r\n=iZ2I\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.2": {"name": "@cloudbase/manager-node", "version": "1.1.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "1cda9794d6569cd7ddaeab37a7b6744942ffc9a0", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.1.2.tgz", "fileCount": 85, "integrity": "sha512-AQaCi1t+B4h5x4vO/uYjMI1EjoH78n8GEiGGcSjkPnXOMPcKkt0R1rdmPV2SaHGTUSC3xlR3HVY2LPZq+x1fJA==", "signatures": [{"sig": "MEYCIQCz8WyLyoKr/xd9riswxdx2qEjeDzfNZNtZab+xfWZHrwIhAKqRP257Vxr4MuWEHd5TXHwPDZ1tZZM0bWEsED7F2+2b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3PhcCRA9TVsSAnZWagAAPHUP/A8f6TgxeH1gAZTGZYN7\nHLxeQws6UKM6LshYkce+YgpjBh715x04P6HLzDx3kXTM66vnEqjnpfqHx+yd\nVlS5Hw+hYvIaexVucX1XYUZMQBfrDQcpWrbYQ8uPC24eViPXRaaL/r5AhotJ\nE3X7KaWqgIlhYkMxf+CcVuzrEDyCejwEGdLomLzfu7WtJzsJGjhekluwyNYz\nqhy3WzNuRYmdgAzQ/R+20ZRoC5N2N6HMSE6gzc0PIPrfR8e1GEdBc14hK869\noFveI9gy90b7mc2OhxGr1aFwm+88r+l0I/74go21Gnlqxx/GsJgAjM/3S2ZQ\nkDaGmVYKP2mPVfIR22ld8U0gq1G8wPL0zpZHnkq6pnE6VqpOM+46eDh7xP0E\np5qZiQB9ZjG26QUbpHjpf4IQYokqRtsPGjBnetWGcOTfiJ6sn8Rf4xSR76Ln\nG15Uhllt2N3GLol6MTuWWrznBKbujUM753vZBIahNRToop7jhcQHUbgZl2Vg\nB3rYYn6YUIJQO26s5B2hYbIcuwzvIb//0QsLOFafH7+phPz/rbrRC3g4fv7R\ncqWD/boKBZggC4VHNDvdQHGs5N+puMcQnhTr/sW3Q6GwhH0Uhw2+1yXlOUz9\nv3ewNFxifyuW+CwuEWuMRnkSI59d+sWvkySYHhKKQyqEaVxFmOXafT1Tx1Ev\n9nI/\r\n=dJas\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.0": {"name": "@cloudbase/manager-node", "version": "1.2.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "88b7c3c8a48a3c133516be7f894900d420d459ac", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.2.0.tgz", "fileCount": 86, "integrity": "sha512-GXoC15ib2AZlifvWjaMSjeTa/r3syKLJZtODbtwm8cbRLVqpK5V7Jcb8bkdmC9BA/aRmrQ/nU5xEKu8tu/QGpQ==", "signatures": [{"sig": "MEYCIQCsYyxmPbEIm3aQieYB4+/0wGc2l6sEQLnA4kFH4+N5tAIhAPISwJw2uY1Jx93j893nDvys3vYdL+MIGBefP3SHP3o6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3fuMCRA9TVsSAnZWagAAFOcP/11rMIPNZ10m2R7EG3e/\n9KTXCVQhgvJKz1OD5vfG892JGt0z62IH1xFW9uxbwqUgWQtTYNKBT4JeonFM\nhuhBAALeORwX/9z/u4Bq6JkkVt4INjhG41h9r0QsZaB6WWcnBiM6hKHf4Yab\nGttd40hKS9q0z1OG61o2PQiMuJ+rYEdZumtmHSUwqUr2AUm3uGlYK5SRro3w\nX/MShwRMudMvkVnwww2ZW2PaD3U/xCBeOMSnnEzjHjj2PrTsccSQmW+P11Gw\nN4TpzNqAKweXT2TlQoaS4WwE/YAZfc6ruoZWNHFnHga0u2Bf4V8nHsxk0RCX\neL/8QE8rviMMKNiG92AiZJqq+4zkC72nZg6xepo7qmQghjdm73YQbOEGFs+N\nTOtBllBMhCWnHlhjJIgmJMUH1QQNH5NJuzGvNvvjdZT4F+l/IgXqpYDAUMv6\n62UmMIwyFMnmbqDG3TLmN6GvyYYjras3+vYNeFWbdcd/K9YHQiioDBNE9481\nLSp9dTJhI4+WrwYJTcyd8tinW3qi5fXS90tSIOhWHjWDW2/qYxQvSrm7md5G\nxZ4LkFsxsEc3iGl4nEXv4XXbdbNMj1p6aXXHtKocirmnEQUAuuT3elsileVD\n7+USs0Uj5imkId+MjR1qa6Icf1HbcMfs3y7wW0QdQr/Y4ieMTLP3M8IglaSk\nlvMe\r\n=CNfa\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.1": {"name": "@cloudbase/manager-node", "version": "1.2.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "f50324190dfac3f8f9657f77a7f91a572e94666b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.2.1.tgz", "fileCount": 86, "integrity": "sha512-XKiRPGFW+2rZfS12+MAEoTv7OMmvLQ17Ra4gB9rXuY+u0VbGqt4G5ae3gpkRzCPhvUBKpHNuGyw/udYybpoW6w==", "signatures": [{"sig": "MEUCIQCMQL5C9piOEocnNq7Z7sTW5vVoJPI2QShqzsftaD6YsQIgL6Bj+1dBCtp5xW+/DT2F8WE9y0+ZefLYlQy1lbdLHhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6KPPCRA9TVsSAnZWagAA/rwP/1w7KuHhiaMALR3E1hjU\nCkXqtk5IvAzjqTsvPMzWi2VpPIqjIhpDoypu/OUiuP6AxIS9719yYagUJ6E1\nIKO0WOGSFIKi9MXH9l/m2NbT4jMpNqHjoogk1F9E6B+CWiSdMgN8WqjBP9II\nbNWjF5XaVeOMEpX4d/VpVCOeJ+ieot53rMjkTDoy9Nblv8X5w4YvyQJg9olB\nsNnkM0AlR8FFveVJyNx/R8/ANKE3HBldhkXL62wLJjnRy5RvWIe0StKoVJ2a\n+AwCRkRx/kGqBUz3jAGaKtSn6g3MoHSD/4tPXq5XI1MNuSNxjSDv801emSYx\nZyd6u7H+2XA3L9j1lH/cK6ZJEUFgtcMw35tT5oKxMWtbBa+kJXvWmfzeWydy\n1nSyRgj7Qfnsp/O65FB8fgKPOSZSlaia9tKx31gq6iqOlhwdAtct7m9/9PyE\n3GzwS+FCtc1ipyfywIJiZm3DUf+6THkfsRqmQZO20ORNojQwxBAjJehuXiAR\nE0/aUOZ6cX8Gs79xwRynKx0v9srP1QG7hvX5jwAx+3JRUXkfgZ3Vz/se/x2j\n6V60deq7um5DVJKU+zUUJrkoTyebq6/uMhE7vl9D5dkbVH0cuwehj8aPQ/VZ\n6iezPsr/+43q46ch7eWsFQ46b9AzdFZYYYhMt7GFiovyncSysLJEFd2x3c68\nWocZ\r\n=dpsD\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.2-beta": {"name": "@cloudbase/manager-node", "version": "1.2.2-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bcdf7800a614c806eb5d522f670b424b850f2687", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.2.2-beta.tgz", "fileCount": 86, "integrity": "sha512-TckkxEC+KMCQhj4TF/lcNyU8vr46Vz3uIVMF2tRI47mXMQWTuO0f8QbZrg/6h5O6hmJlrvyN0x/2o9XDOLkEzA==", "signatures": [{"sig": "MEUCIFuf2hjEahGcNrCyVnUPbGhZGFlDSt/na/paWIJXst42AiEAv7+rcyRlqg0XUnGTEA6KUqNPg7bdj/bJpoPXqML6/84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6LFgCRA9TVsSAnZWagAA6mMQAJNhheieyIC3rBh00wyo\nhuH4nb6TK/cw5zVTqoTWwpcdOYFLtkl6D0k2PDQHpXfh8RSbphNUXhTjUyOd\nz7wx/sjGhE2DmxRC386UNYDfrRIIoPdx5EoipRYQAVFmOM6HdTZEOl6rI9T9\nycrK4CmOen2ZRZ9dovC+XevEXwTinba06oP5PllYG6hfe4pAZkyi2/gk+9vn\ndChahcUZdDev+uGg7FTh9sXFyi2AKZq4kmo1uY40Tc102f/YL4TAj6shjDm5\nlPPbKemXSi4dTv8JxX6pWodLpv70HS9qqCRyo/bv/yHZcQxwwq8/k1Kn5Nc5\nZo0LhjTIpeVsD6MPJyXvbtNqx+Ldg7PNySLhBtTnsPN9yIsBzowCHpZKeqUe\nNumdxCxGx2yrdKpMGUf2urKj4eT83fK07orPcP00qn4gOHISIb4TDR+ulaGB\nmwaHtRGMUV0OqdrLLNTRrLHHVzz+5uhJRkvfQuhIs9c4K3XLKYpXSGJ6g1Mq\nVCZjbfge1yDQpDM1mGPZc6McEiczMpesehqtPl15K0SrV3TOt0ptVygZn8aW\nwzvS7wLDMcPicSuygNwD+IuriLmmeMZtPoWmmlqazSTiP7ododzov8aBe8sA\nx0dzlxwUJooBb3hPmjYf9g9wWbFMUHovDQnRs41er9/XfE8S8zmNj7rUW+cP\nu96v\r\n=hvNS\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.2": {"name": "@cloudbase/manager-node", "version": "1.2.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b890afc15f9518dbc38ee764f6caed9c66490b43", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.2.2.tgz", "fileCount": 86, "integrity": "sha512-ym5CCA32r2r+CA5/LGZ/+lqSL9Ne7+o/cppKP+BrZXPH5S76Vnlt5H09+mTWaf/USEC15VOgGlcesPDYpqSDbA==", "signatures": [{"sig": "MEUCID4j6B7SMBxKEMXCaJzk0xTtvTywx1d2RfNP3Gu9vLQiAiEAmzAt/8wbq9JDuLYoIsVHwJvF1ltQPGwXRjPwm8QmjhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6MRRCRA9TVsSAnZWagAAhKcP/AlGYpyX0OMBZSSBus3u\naAUaAn/3GICG37uMCWjdgd4TpidXqUP71ckOMyeQnY0TN4JPsGcl8iLCHoS+\nIJh4Pi4foPi3w5Ia9N0F3ZsT/ZdlY4gx0TLFPR7DEYF7WIEDGQkO734H8hYm\nPxmKh2axrf37xhiNw9P1pVCPe/sO+ZygP0mpuAqdf2E0K7YCUCwXskLXzcQ1\nV3VNe49p4AECEF5bSTrsEjDQermq4/8DFGAPUaWHX+/2FXVIDo11Nz2qwZ4+\nLBDvkbq2SYuxpWB1LoPJwd1lLnY2+VFkF319vp1tZWecWzlAbdF68zS6VWUz\nP1uvbx+9q11UmlAEFUGxtDNuujtNUHPxBdp1plaU2912dYn6WGAW8mc7Zd68\nMF/Bg6wFj0Lk0oLXKUnBnasuwsMPpDT3JH6CrRiVvKJ1qWonhUMLlr3DMhzE\nFito5C5rm4iXpIpekMTNn/L5yEuyrEy97787fSopN0HVL3L7RJXkkxsTrjeu\n9Oo2oNuycpTg0gAOfilLtki1TYFR4llPACyvmaPpbBilLAPBIiwI7Cv8q10V\nhgF7Twf2iWhMbhdDzOZlqGAGs2vm01MGLVaIv/i9Z6ce4DXxeZIadfgX6c6f\nZKi9+mgwl7Obk9+NZonh8PCrxSoZFBKIBZHtRPHk2nhGF3PoU+X7gQGo7Tdv\n/gox\r\n=jQZM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.12", "https-proxy-agent": "^2.2.2", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.0-beta.1": {"name": "@cloudbase/manager-node", "version": "1.3.0-beta.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "c7cbd53039e8cba03a6700b0183dd7f7683e0e60", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.3.0-beta.1.tgz", "fileCount": 89, "integrity": "sha512-7v/joG3NWhMr2YzOUrs72r4H6VSK/mN6k5JH9YuzEJXa5sX2GBCqFmCfmV3mcMREmQUcY1TMRC/mpozcJ3idCA==", "signatures": [{"sig": "MEYCIQCwsMuWRN3rcVqhEqCXHUK4Wh94QohCePoywlTGNCXuiQIhAJbAEg/Ov2wyccechGFJPEncXEtcNjLJ0IrPj/GLDiAa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7fQ0CRA9TVsSAnZWagAAzl4P+wZ1pg42W/sJokicWccB\nzT7P3LQaozxBFH4KZD7M8bXETzqF0ZYXGnKNH8Pwj/eJyXOBurdgLJ/9/ee1\nkP1tiNKHGh73Awi/IPuLzOSdgA/lsiE72zj0fqmNjAtog11OhvhjnlEHTx+C\n0G7LK174bNQ6CTj1UGmVvpnBdwnSMktsUaODFSRXHDrnyzast7/x/UUKftdg\nMhp44EsYqbVOOp3uuxtcLUoRMpEDqyyXl4Rv8NjLI3JtlJLPzq6riE44Uolt\nVYDUTMf2nErk6RpcVQbTlkqqlPG9kwhAFenfVpKvp5r4Vlh7I2Lu5O+pJFL4\nJT6aQzBmyGWbBzqrMZuFwQTaEdUSJvnwhPuqAHXNoJMRWf1VgA261ScuPIQO\n/sy/fTV+Ji8zj6nqtdlcE6pac6zB8KpMVzGrkCnKdQ8Tge9ShA1HLey3sZU8\nMuLCbYQy/TvOJ6rXXA6Kgv3PZd6FUUcFI/ti1BvP0dL765zkliVoVsNX8Z+r\ndsuKaIGaZppcnbZcRZKmGGdsr1Ml5JJlES8VkqeQFaEdITM7i4f+j1lFV0BY\nCz+YXDteCxRS0N7GLoRBZ9saJOlq9+8i/eZ/8KbIPmj2fNmJFZpVye3ItBAv\n6W/7GOoWTSvI0cVAerm5u5eUl896cD4hzDYE67Yc7Pqr7Ixz6u94HJpMlri+\ns1oC\r\n=AVDu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.0": {"name": "@cloudbase/manager-node", "version": "1.3.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b00f964b04035fe2f3ae6635c7a299636872e0e1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.3.0.tgz", "fileCount": 89, "integrity": "sha512-IOKkA3OgfCsekIVyxK8tNr8YegO2gO+av2SMd3rUw+z5wmXwjLjhw2UXEgO3SNyfZeWajJNkxPfgk5lo/7U6/w==", "signatures": [{"sig": "MEUCIGFzNlp/553sgtMNwpBPS9xxkbUsUT7jnWFXiWUdpQuoAiEApYGs4fOKEqbs32WVm/DK5S7AmGNytoLwMhpZSMcUMBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8LeTCRA9TVsSAnZWagAAObUQAIDCeuwlhusbEPVuXVte\nhQK3+Gg7CR3HGw/a4IcaE4o/lWRDmVP3q8d2pYdsSNgU1Z8x4xOIfQnYl+bf\nXgDKOuvhZs52Vj4yN52tX1LU7yixcVPv1vVjQ1ZZT4VPZfAISTdJvbHavD6z\nmqRj/Q8Wld8QaKaNz2OzpClGq6LrAzU/sBvPRhFPGxfOLFPYoRi4Z5E7NdPt\nr0BdZHWZ5sb+n+7GGbzA6o/bSb4PxYP/SkqPWgZE6fTg9+ZDDYWWzyQiRNnm\ntIvd9BDwkbl9IGoCPT1OBk025515Jx8Y0nNeU0TZZ87wbdghN6l8uJgZJyYM\ncZB8GQLY8QhCFbDF9zBJCL6OA8BlObLL+Otknv7hE4IOJHrniEKAQrZvhBg+\nZ6jSHz0lrKgMjJMkatzBpHrdL2EbYOH8wCo3gXFbHFMCiHlwHu8DN3XVv4wA\n3menTal9IdmWuDojt1ZP046Gg+tEUoNa/eEzs8uBVGX7nTj9rtnwo0JchbgJ\nJF9FqzPyz1vUVP19DhJMjevaQ/63kusmRiF3a6Dka+qoaoTYmGvbg6JNaFY0\nxhCKJqgHdqtFeEJkYy+NqWOdpbAGGd7kzN9yMVrPfnFXFKvK44weVDe8cr7u\nwcEJltPMFR3awppTyGtIPr8yL6uDoT72SlOKJnbsmPaHOyOtRr/obHNxcpYs\nCSYu\r\n=ZEZp\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.1-beta": {"name": "@cloudbase/manager-node", "version": "1.3.1-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "1ea1fe25dbb46e5badf9d821b91c7a301471c67d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-1.3.1-beta.tgz", "fileCount": 89, "integrity": "sha512-tEPWjq+aRla9S2WNJi9qAGGBO+zk2S9rFhgYEX/fAlPHY1MC2NsoEXRqCr+e6Kltl2WgdOMDDQSDpbnOs5281w==", "signatures": [{"sig": "MEYCIQClP3bJgRb/BhfwVvm202CArj+1m0ly3cxdK/1bTmQRIgIhALPMu5PoEg/TminJxg+eMCjYgAFkMyEeovdjpH0Cvnhp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8My1CRA9TVsSAnZWagAAOIwP/RZ5dx0AVnPbugJGLKTz\nbeeEn2RrKH6zL19PZmPqVZsG3AaiBBMto60zuh3oLmyI/BPuAdMoEtjddT2j\nFnrsWncYZEd2ucAr6XM+UU4XBt8hkuFJEr8gB0Dj2Nmmro2jtszjZAy4WzYB\nWlZLdG//3I0gUVuTXEUTar+BT2cdfAe40onYMT0Sir4n84QMY7MBKm5Gy+T4\nP5Y6lIMG2VPmZM+8/Odea39a5oc8ZGS7EunSjqbe6nHFHrEN+kNITikL84QG\ndtlkvKet9utoCbIqGTkzcx2yaEHwh41NizmEvHsPIEv7XJKFnZ2V+ofXI3VY\nvWt8e56PHGMHH4bsYQjTWdftXnknDzccSS6cwQ4hzwOjjo+0zluVe1t2Ypy0\nVvmfHsK41u1RF3NxJWZhVJj9OoCq+ytXDHNdoyKR/M3UZ7nbu7LePOEwDGxk\nRbPdVC3g0zyGWj0WGeRTlcnnU/Mjr8Wv8pWyDHvNXZhetQQCrTYX9R+Opvh2\nZ/iME0bsym195MfQakZb7ovntxQ929JF/YkwL49l833mzEECqwXgjbgeoIiD\nemNn70JZZ3JvY/5U6IvID07wWyC7Sx+qhIXZKwpnEb12EYiU+Pw0RfVGMMlK\npWb//TpfnIT/38chIclB5CYKWn1vXdaXTbQ5sOt1LyZimYU9sgvlDG11eN0S\n5rMO\r\n=BqqE\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.0.0": {"name": "@cloudbase/manager-node", "version": "2.0.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "09fc8376d0967cc8dd0095bf00c750a02083f705", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.0.0.tgz", "fileCount": 101, "integrity": "sha512-G7uuhvEyy2eg2tplfzOB+QH2HwtXrW5lfSFiAg7n9VjYeWTCjHeUmVkquQpWKNo6hRi7iqv/PjSZri/c8tIm+w==", "signatures": [{"sig": "MEQCIFxhg+eS858MbuQkQaAwkYCdKTrmqiyF27V0Cm+qI3VeAiB80edjvV0pz774s+CzkJ7mzSajRW7NKXSt3xs2KvDU6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEvxfCRA9TVsSAnZWagAAbNQP/18AyR14N+ZIEJbW/OMX\n1nvRmzyrfT6K8RROF0jJGTc3bq+1RB3rkV/XrbCwYhYbeEhflSfVZWcG0onI\n2eDN+pXdtCMPw4aw4MaxA56EX0L87wtg4Dc4y2YCHylf+ROyf3G1knO6JjpQ\nV+dAV2MwARM3GAb2ErsdPYXAGhusIIUjKpZWskQ6+kRfk8gEtdOUOVnr6cYK\nCVzGaha/98zrms+h9360a4T7gi0RnHsNWMgAplH5K1xVP2s8t1pl6BdyauYx\nlwsGagL15gr+pY+K90UYp2qP0Zekb1Bby/Dx96PQI38OaKq1t3xk8q/nOlic\nSIwElyQrM9OVGH41kjokVsLrZwpxNg3oPDVpi8vVEJKM0Os5tQDIEK+qlaR2\nHMEQ52sK4LZIuD8dftYhscZ+chYrAKhnG2POeNA9dOuBAocFXJqRxMVYseYx\nvDjd+v8vqUOU+Gioh1PRZtr7Qyqbn6OuEGDwc/EruQEt2pmvQpWY71AqlpCM\nMlw2lzA0iEYzb4pdOS/z25Xs5vEp3TClNub0nL2cCT+vX+9KwXTUZJlfs3DT\ndLYpvk/G/26Dc+/Y7j5EhqdHOASMTqM/IxIIBNwqj1RUA7A3Fjz3/B/CLFsh\nHM4FxcpyG4gfmO4uKyqzSudGVq+rYXbQ8kcO6Qcfqqgypz+cphHzEMTxGZTT\nvWBl\r\n=Bdn2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.1.0": {"name": "@cloudbase/manager-node", "version": "2.1.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "855f8345e833067d092347af155c7c81a18a25de", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.1.0.tgz", "fileCount": 104, "integrity": "sha512-mhjDP8Kon2isP9j0BgDSwMKyOL+O6F4zTPtUCxy4KBEXQgJRRbbRFUEA1UpBM4mDBiQiKtsln+4CEoKSsbKtQQ==", "signatures": [{"sig": "MEQCIByh/ihcajV8C7dfQ1sJsgB2E5wUhDz2eULegOTWPF6VAiAtQ9pho7LX4LwMJ2nbv+ECcbeuGBHtif0nzkU/GA/z+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHBGiCRA9TVsSAnZWagAAQyUP/1112V7BUXrjlwsjrH59\nQR6DFeN6pgjgDD+B3fDuQC4uy3jOcAg0orsnTuFqb9Hb73wIqiuz2omi8yde\nzBTKQMbRJEAcUa1awVvAUbhzKfluyxYZZD6lfrznhmjXpfjoMJ149+C4m3Yh\nV/h+OPbTV9VvXEK/0Kz3Modl4i5nciwLRDcaphq96z6Wr/x59ymHlqVR69kU\nBsK9j389THDJHKrrac9tOhmaHIaDWDXUFeeN+9ilhN5L7Wcxq+TifSdENOJH\nn4gwiqD1068C6gk3OwC/d/iWqPyEkbEx+IXk3PcN5gFfNstwx2qnf74/NSlK\n9RkaiP0L9v/Nw92bCyk2GbVBI1K0X0Mg4FpV9A/ew4KTvw9TYhvKxczdkfI/\nMS2da+cOKLa/ItAdem/7ERvZB1BX5hzPhg1vzbKAJFL2GCH5/i1BM/RMBgsX\nLykfNv7Ya+wFV+E5cH6IgSjXql56EQU6id/sCkNCb7JX+4jRLBdsA/vNMdFY\nATIW3o0c8j95oYTsIPnNbMWMLmZ5lgiIa+CCS7PgiyZirmX05sO+6CfQ2WkM\nn0ba6R0cyxbPePelV6kZSUIixBMchcYX0vuOI0OzWC/2GUWG0ctcxTj0BzXr\nDke4cPljI9gIfYQpmswRbrVwS3vetWxPCce2fC809XTc7oDCVhHFwyfUPQZU\n9FCv\r\n=VZI6\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.1.1": {"name": "@cloudbase/manager-node", "version": "2.1.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "2d5fbc0631a89e462461ed05ba10ed55b28ef84f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.1.1.tgz", "fileCount": 104, "integrity": "sha512-r8flR/S/gqz6tUeFmB67/gI/8SeqFisfbPqNJwnJnx+yXHPWMePaM8LOcaB8Og5/8f9xkEC8l8IsFEw1IgyEnw==", "signatures": [{"sig": "MEYCIQCNncrpMd8kNqNFup0w8FY/soz8jA2ZjO++K+FQRJo2dgIhANadEEgki/Vr5AU/GCUMoBpKFSp2qVq9LoSpm+op/j+Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHt+iCRA9TVsSAnZWagAAkh8P/jRy2h9le/zQnNMCqQUQ\nppRxReZrkSg3HsfIMhDIj9hTA9tIsPxUZXhqmSQruKsqabWTwogEZ6v3sQOs\n3wk2kzg36H7TGQUfnJc+dyqfYYIl5Sj4hNSjEoDdGyrjVk4pF8iLiciCG2+d\n19lSP3jTqSYYro0J2j3seJZdiACrEqmHhRda+qpEQKF0jKl0g9GbmqGudBqj\nCuTQE1MiyBpeM/69HoCjXWJSGp9x1liX7HUoucMXUnmseAJCCjRjN2bX9Pqz\nq3XzH5bMs6H80R7VXjhzzkIfRXtbIEmnSJFx0apDtCqTUD8p7grVbr8IIsDA\nbW0S1iAuca2T8MboIc048MmIB12ygGtKLbWHqmpM4GYHgItKKk7lx4ZM4InM\n4EFwMkBbNnVPdAAee35jM6dldPqij3jvb9qQWeeOZSdh6SNaJqtjlyHVgC8k\nRcC9HY8Bo0ohBuMDbAYgjMWyenxLt/c0Ugr1JR6fQKpP0pmQHSNHwfQI5oxE\ncKO0DN+uPcVGg6lpcc1gmhJeRZJP4AT1OWMySCY9NzAofyKtapwL9XIezHTo\nWPU34fhuhbhuXD0kxNQLJ0ocB36keYunz7q+9IL89hhuz13HHeV1a4+rK3c3\nWDer40MJ0D2D3pILHnO3XzhD2HAHS5Kogye0MXq4etHEJGPV0tXxlLVaHYe7\nKsfJ\r\n=OsZ4\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.6.2", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.0.0", "@typescript-eslint/parser": "^2.1.0", "@typescript-eslint/eslint-plugin": "^2.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.0-beta": {"name": "@cloudbase/manager-node", "version": "2.2.0-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "54812bddf0f368bcad351c34e977dd5b7a25a37a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.2.0-beta.tgz", "fileCount": 104, "integrity": "sha512-5NeDlHEEhSZWXw+lBSRYJLb0YOPyw33wuYGZEon9HVbck2hJftzKeh/n8MRyyyONjj4qnltXCyfUvH7izbtDqA==", "signatures": [{"sig": "MEUCIQDQZRAYm2dtj2mnVQL/yBtUBwX32QHn1ekeyuhLBXSVJQIgN0ZX4C7QeODzSQ8Qm5Z+Efw4SoZ6uleDRm1qCSZ/fxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 298798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIYDhCRA9TVsSAnZWagAAXxIP/RN3m4N722ManRJWhjjc\nGJWqhOrM2yDlXakYIJBX3pMxivTMhq5a2T+hCqlEREUx7DsCcLAJb9O4XxZ7\nRy3+nen8iopsnzNDEo7a52jl/cKX+fM9rkknQvH7NSyyx+5TsdTJXQGSInjo\nb5Y5g/DXsDoACaP4cKvBXGQoevayVzbma61X2rguOLIWhPOCi5ExLsiymvMF\na2Mfv3ddu2jn84IXEIR1nAKMuyOg084pmtAEBfBgnbIF1qVOn2IHakJzNen1\nxX7OQEi+WSdsKYb9o3EicAYfwOGebrVKWzM1Fm+6nbZb+gcc45Etq6N9Uu5n\nO5Q8vWLy5oUAW23+LC3kOF17i0AkpWaYYnwkhFdnD/UelXoH3k1EYTKBCXNo\n7G+uEJTg4eVmFMXoR2Qb6S4YbZSfNqlFqalVOrA8YusBCfnX+Pp+GQ9BUwOl\nPIzpEcFVdJ7o3rUdgxswhywahsLEra95vZTt0M5v/tz0tLDvS+crUVTxpDgK\nrCNmeNI9H+gwWBZv34yKSU39PnKGP/Hdm+ezZ5Izr6TV7+2SOfBIcjL3Sqgs\nqPB2zpvYTBV5paiOF1OfRpJlhq6w3VoT7PezG6HerkXmzetQ5gsFGh+PcUvd\nZc32pMbM7fzOxDW9dtRm3ihw83nf7pfCDL5q/zWebte9ZPAf5GW8jKcDZfXh\nuuAV\r\n=IRi8\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.0-beta.1": {"name": "@cloudbase/manager-node", "version": "2.2.0-beta.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b0ffb228fd67aa63c4f3bf75ed6550ab9f091af4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.2.0-beta.1.tgz", "fileCount": 104, "integrity": "sha512-eHz8+QrOC0a134E5u6aBsXxArIosj4oJBGPVlOCADyf0w/yKH+7i3CC9R91qINKJklzuu/4/Z4h52T10BYjsbw==", "signatures": [{"sig": "MEYCIQDpCNr1bZcZNjRxPGQJirKQapU+Yb/phtlQ30lr+/q0YAIhAJabCv2/mAjvsvlCZ4VhBzTdFKB+p6KJKO/aL4O1+h3w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 300157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIaBqCRA9TVsSAnZWagAAvlUQAJ/7TzTYPwQWLq3GSqjO\nyJtggqGtCEmVJ5olOej8AlOcwkjTAXkL0Y/MKen+n/hDAYDLxoXh4l7zH9G7\nSKejuHeM6/oZZtUjVbnm4mG7IndMuAjTzDfrvgxMh/3olI5qrWT0KwN1RAh+\nLNp1NtjiBX0dWchIMBrAJZiDRxwQDAIAS1pAw0HruMKO1y6YmQx/nirEbiCS\ndkO9SEBESggcmI/yyzNmBaSunsv0/agQCnzxEAKprwpLi7sYROMJWxEa7L2q\nL7eyon/LdbyqcaS93bvIDdxZuzdMXt+j8UfvPb04FBD8BbBOwTYfSyAx1c1v\n8DL/ixkPdrO/1XxkEWf9mvn68cmxYrGCqqvBnJZPqZrAgClTGaIDmCcGsKk/\ntdhEj1TYdSI2cfYQMoDlnwyi5leLmORq18xNg0MlD8xAU0oj5a8iTP4oWjvp\nRXrNjM+7DkRQVjpuMSiaXv1bwakUnRYcwscLHawQgPAMcJ0Ty6JJ8fAQc6gG\n0FExHFtw7thZ291BePgFxTzQhe4cgx5P150+3kGA4rgl1sUVoM31GChg2e9q\nnsT/cgrmiBzHWK2jpd0LNzSII01mDvAL6Rl5JIjQzzB+7W+6Prw2QpMxdv8I\n3E0rXQFIV3dhrJ57RJO74NhoWda+2YIY/l18QvBFBkjNe22Cemzlngdx6OzK\niRbE\r\n=B0Rc\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.1": {"name": "@cloudbase/manager-node", "version": "2.2.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "558bb15abfc65a54bd6df72f197f12fd1c86b515", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.2.1.tgz", "fileCount": 105, "integrity": "sha512-a67zJ9DeDJPhxt+bqBj3SJAfJYDEvyyuYjQANhR5DoDXt4bh5p8wgCRbOFb2udt6wlNlxwkjEZ5KWlDVq7M74g==", "signatures": [{"sig": "MEYCIQDy1wSpSGIeUtBBf6rrI/AlIS+mHy+uLtKoN6LZDmDG9gIhAMSBBG64Vqs7deDEOjrMMVMfpO/zZTPLuyPJQQ9DcGFb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRROnCRA9TVsSAnZWagAAYboP/2r7fmlUHRFPtBjek3I9\nLgjocVrATz6PZZ0ORzZAu9QfM4cFyHcomB7HDUCSTkuwrVnMAQ03qBEJmWT9\n50DNVMDfaVsXDWirChmWYpCplizxj9So8PSvCYKJiHXamDcguLN6C2GyS9XN\nXhj9MzybZVSpf4d5oj2e2+lmykHwpuDvYwP0XA3aHS3Un3csl5H7UK3eEFBX\n+nIHJMw5Xj4Y7na63R/R5ruEkQD6csrze0ryMQeCMT0P6IeumMShJh0ZEAny\nDF0jscx3nHaZZ7113bulCkQUIZUckNXQ3yGpSuOCeugg0DYzTwoUNG+zj<PERSON><PERSON>\nbcQajZgHgjcODEjYQJ84o9Ff41GBKSCv1GKd/i0YtF4aeirvqAgZcAnOtMXS\nq3161NyDg2DUkqWyO5QLOIeHq/wMODIhY0zlKb7K6pEe+UOe38EXhbqBx5YJ\nI4LBcdayyGAYwCePajTFHvhMjFtTu0cnhLmlDmdvk4tfNjCNQ2oYepG0dWJy\nyZ4TlNUCaWzucMzFkvCc96iZSOLY6Q48/BhYe8WUk9j2sorNlc6h1+KkHobt\n3KrJxU+J3FlO+z+6EC59RpUsO8CikvtEHejiDZuGIRo54bNK/HO7pMYopIbg\n2hVTOZLXYBtcy+DO3+/lW4kkekTMMq4xZ6UaflQ7DtsMJ8hsxhj+S7v0642l\nMn13\r\n=Z245\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.0": {"name": "@cloudbase/manager-node", "version": "2.3.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "6e7d69b23f80ba67983b3121ee53462c92d6bc8a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.3.0.tgz", "fileCount": 105, "integrity": "sha512-sA7/ZqPY/swBDs69CWmjlBkfnWuevt2Fopjg0MJydzxue/yCLkMiwbA7FIrXXLFSUjShAp/hgZLkSfxQLcP2Ww==", "signatures": [{"sig": "MEUCIFhjutDiLY9m7wuNJkMMoGKr2Ox5lB0yLDADlm9p9x8XAiEA/SV7lUwCDdMmXnQEogqK/Co0PSITQ9+/UUroPoC9h6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXldoCRA9TVsSAnZWagAAElcQAJ9PiBu6ppPrkpKBWE9O\nN7sTt7VikjliqFLk7IeXGx9PK8E40JX07JvhZwNYcxFsAsfkNjQNNGoIfKHK\n2xDzkaQvdrEiJTG4clXjGdPIe6N246QudHrNP2I5AvS9d53GG76j2Reo/TWM\nAT7KkGT4bBdkQo3hvNrXhwI1jPDX3v27GQ//3aSz/vxJFo3fMGS+4D2D+Qds\no83fbHbtCb6yqp7QECrnCyJ8N6WI4ifdcJf4L5sPRg9neTWWAOkUzVnI7Q4R\nk4fl7mbGpMBgeFozBdMguiPObhngImr45NWyeqTDhkrQoFngLC3eJD+tDcGP\nzOCteyeXcwF23jxupa5wOcxJrux4II9euXx/YXuaDofdCkhR/T3NWMSPZq7n\n/QGbztCCE2sWL9nFlElNFJLxez077PpmlMpalvT7z1/tngQ0DZWzuyHxQOd1\nSgLzM0Vel54t4yj9FvjCbXpVtDpB1Z9YDdhDYweXffNuS9wLwHwBn0yS/bGo\n0LH/gln+mhIL7veXBdGYvZo2rn0CPM+B3zsD5OX+fpfO+7j9TsH3Simr5kyz\nxhn9SpgLnMJS45SJ0nEF0CDc0o2l52xYdtjzxIzGliKcxCnSIW5lt7uwnZV9\nIbeKuvHqduZcaQcb9HAIIW+faRIpgfVOxBd2xsqgnvSEJGoe2SgtDgv9nTaj\nOnFs\r\n=snQf\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.1": {"name": "@cloudbase/manager-node", "version": "2.3.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5ba234d5a116b1b1b8a5dd41932f4e186d6a278a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.3.1.tgz", "fileCount": 105, "integrity": "sha512-iMUlaXhVIoj4F7nbD13Nio+O8Wksxu+FcACWJv6RMYTQYHcFKlmkDRs95umdWWH5/zXfgjfAZTjkDtmnRzqF0A==", "signatures": [{"sig": "MEQCIDKUFN/wANowvdFfqfZDxPBNlkT64YqF21nfdKTc2iG2AiBx8/n21xZ+jsvQa+aRqrueDGftL41lNLpdPVNoiu/Guw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYfSKCRA9TVsSAnZWagAAxdsP/jlOfyFG1wtAiDLDkIfg\nk0hnCZlNQ2z243njt6uxJLRHPYJRr69AbJ/DOkYekJfUvvWLEYq+7/7NEzre\nndFfRQF7olLhJI8nr7UljrKuG0ud+ryUmuip7QQNFY1/pP4cv2PoEhtru+ff\nIuKOwnGkZF7LP0SCdzkxtb0N03QV3cw/CaWkOk1dU+zcBu2tL2MuDuazzdA0\nvvzg5gkzU6BFN/w2T0sqdL8SO6mHIZdtecz0wZKcpO6Xo2JzNhbRAbK9b14V\nopffNoVrilPuoQTJ0mZQKUYRsSB7atNlBVB6gQd+F3puDFkw/Pxc0sUJi6hW\nlQzocEwFOmTb0iUBxm6TF7Dpinw3HoGP/oQPrvKrSYEsLVN5o7zkxV4Kr6og\nFSS7zK25jMgIvHra/IS+BU0ca4ULDRWzEU6vsmEiMQS6rjNcbtKg+Nu8zpJU\nxv4HBtCRce2803uJBqiNzU5qbgNJTWXqP6ni7CK6KQDMCArbx4o+YvlByjZ/\ndrFd+cwXwa2i0USDBh6IHA/OZzUeIwPkwem4dXM/aFqiUe3svkud3RtxCNM0\nj5XH0u+xHkI5osjVX/2QQXeFLTj+3gILUUmreOVuV26FWS25EgAgPbqnvHKj\nv26lNuuk16LCj7tIkYeGXcZHly/yExTtUpFx7xhbO/B1mNkmBWhUiKhnKF0D\nnalG\r\n=/ASF\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.2": {"name": "@cloudbase/manager-node", "version": "2.3.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "31377595b7ff5eeff91317dd4a3bccb6ef684107", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.3.2.tgz", "fileCount": 104, "integrity": "sha512-8k1LbVeJqTxW5GvxBKiu7tDfKy1+6Do3ewdC0gyvpPAqf/e+f6g3mv+Pw08bezZVmv++kUnMyxnuitDeczC9Sw==", "signatures": [{"sig": "MEYCIQDObNjLDtvqwisI4NavNuq4atcKq/r/q36TIfMK1w7L9gIhAKZb/RsCnLKX8cKeUR6DiYbQFEHogdmC3AykmTi2Qojc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 316164, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYhVcCRA9TVsSAnZWagAAxAIP/iGJd/wCi0RwaAFuzVVF\nJYGmj4/juN1ZNQWyNQmkxUNrgCrRQahwUOtkoIcM8U5FTNVEpYQZJtYC1/UT\n5yurebz+OW6Cu8gW1Iv30YTIjIrokstnn2Gw1l90hUnM1r5vDMWgoVuyzsv+\nHJlLTckMDRfBd/AZDhZOJJhxMUKP8h5eAAb6V8fn/aa4hqQ18YGDAvkVmoe7\nafvMaiU1ljDRuLVUkOZDNtSMAcg+xM3Z/Ntr0ZmaxluFzPhTTDBWBQj25CNz\nzSaVJw5qrH0RgOS5BjhA+rFOsH0u9cehauE69ud70LvhkvoI7wdArRdZ5So3\nh/XrpQCw95N+YDya7NxJv/Mp0e1POiX399b4J+a6l2Rx2oCj5PieTYd0UC3U\nAGD6zk+vUAVn+KC3o7M5fgWduK0YaKkrO2kAXIoYQLzewR0JURNHzRn+wMYG\n+vQLWNAfiX7nBT1VqrxEC9TJgOWXDAKnn6rx3mbnU1ZSk7q0KpANOHity2+f\nu2LXLUhQOVOfVXrM3lzHdqajOOudl6EnaoldR1wPY+rtPDIfXQU18rGJVey4\nK8x9jTaWRAKMjnZxBdFseNZxPuOrqfgt3PodJBfv8pAINIamX4E/JB0LpstY\nezTLwE4j6cmwIyG9ClDV7nMHUru93atb+3Xn1BmzV9guVp5Ox34aHTQxgZ5X\nBmic\r\n=hRo2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.3": {"name": "@cloudbase/manager-node", "version": "2.3.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "446e7bf4af858e2f45f0acf93a7c99b31089b831", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.3.3.tgz", "fileCount": 104, "integrity": "sha512-YuC4fL66nAY7IRhnfv+7FT3rz7NImaE4hUHA/ZZzgi5xYdMy4zY4fs1TsPRi0ZWo8MlJCdpalf/4YqKodmcDrA==", "signatures": [{"sig": "MEYCIQCrrOiJ88EZFOADBfbZWUHDOZ3WniqDtAoJ3KzndwDSHQIhAO3Zw4wjJ8Zzz+u62BmRYLcmZ4PBHGjWPWC0yjUDuTpK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 316230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZimwCRA9TVsSAnZWagAA/7oP/Rae9ueAC45jxWz+nATf\nYaaLmkHN5zrCKWRJpJw4YeLALGlYbvyJALa1umCoCW4dCx4om4XrxO/CnmWI\nKVrGcl68w6EKarlRxKBYQJ0h7dvngoXF4hszFOe/6irkhwxJ3+JkI0y0fxI9\nX1UIdWAbsdbM+hTTGv3kUhA8Hn3mHts0eHdGlcKid1Vxq7kWsC478JgTNIaP\nT5uJutKJSP0WOUrYRZ7Cwuo4ARfqq5DW+GZenJ8HyDIeCAhayuKm1AtTe6oS\nRznPmxZn3qD3VZ0YPRJqARWBhozxqN0FF3VeOexIWGxvZXQ5yf+BsFWR9TyI\nNFaGDjJgm/c6EEP3Yq8w+ZdqawSigTqrDw498EGNW28Qt9iQXBN9kViSoxUL\nTi/eLZKmav5ObdYcBUPnTQVyCHF0+Tj6uvnoafbgLDRPjhAW3gCSC4HQrEbW\naEqwSkXYpwDV77o1h6dAxz6U4bvrinTYWbuqtnhJEcFxpV0h3j4M+0uMCE1A\nHCnKPxqJ+8C3LO+cI5v+MM4EcfNXTd8cLyNCW37X1omJQ1snyMEN5Kwx4wPz\n6Xs9HQAl7T3APJbplfcNO6ZvPUwk2+TwFBB8Zbr88FlpMbQIg6+d3TIptFO/\ns7Ghy0dGvhsv8eRR3iUzcdWbpA9JY1gqQvMaDtLUUzMjU8BCVNq/EOGMnMNg\nzPj4\r\n=7KbV\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.4": {"name": "@cloudbase/manager-node", "version": "2.3.4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "a500bdd3545b1489d642f37055849c2453bb0433", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.3.4.tgz", "fileCount": 104, "integrity": "sha512-Kg3eJ8jnSuo8304Uku3Z+bt0m3PUWf8EbE58ppzc+tmfChoIGkhiJzOsbwLZ/6XW9YpHPf3ytuTpJ4p/zEozMg==", "signatures": [{"sig": "MEUCIBbddzChwN+PaqzLx5QHydrnwvmYkNz4e9zZm+Y4BoxuAiEAvHmIKhfpVK/********************************=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZi0YCRA9TVsSAnZWagAA4IYP/jPgHitbLPwUyTc048vs\nu+fPUny62SC0aVC1tFFxmOLnZ7k8aoFcI6swhMlW0/kf0jP6QRN31gmsFw6F\nLM0V1UyTRkrFEGOXrIv+EJGgaDGiSOfNtozLJm+VLVHqzZMKNKRkqq2XU/9v\nx75v6bhYhUENp8kkIL0ABUnAusJrsfmxa0MrBGS/SC09tDflBQ5ITH7y4XF9\nhY618pkqOHpflKt4+wrXqJcJtNxZZ3jdX4Q9hxFvl2hLyxF5VqECCDAYuOmp\n50yFW6mltWU5ir/+gxYKodLYVjM4cHY2f0SsfzvVRTlWsCygpbxd5Q+YZF/V\ni/51CwcknttstjAz2J9BxDGU8Ig4rSljoW7LuKmV96VcE/EtmO6YAU4+Zy+X\nW39AWlCMTvbCHgQUFllfSZzMf3rXwJzrvqYc0BxG7pYctGhiEioRauxW7XVS\nThuT9AG4aCGADdpML+ymvICn3nx7n+o0IsulnBlPezmXgQTQ/7R78w9JLeQJ\nYs10qq+9nbUuqHJvfiC+SMhPPfxAvf2lKzPa7EhaBugGeShY5ST1iIpW1Bss\nZGJb+CFyqHnZcAscQvqMlUd8WJQx2HokR8Lrzd1Zgvyo3sy6YT+zn8UEKi/S\nR8omNy8kl/zTy2CgcZ3rSVTMEFGbov9L6yb0TQTgJHNSuBZGSah11mV/Cw4J\n3+h9\r\n=3NmM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.4.0": {"name": "@cloudbase/manager-node", "version": "2.4.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "e6bb83db658e39445f0e23914ac0504318579f12", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.4.0.tgz", "fileCount": 105, "integrity": "sha512-9Sri2u3QCRALw4uuF2ztC3STAlw+QOvfP19il98uYkYQS3vOKrHwea0xeQrqecQskkRdKTF1o4Lzb+ptqTPB5w==", "signatures": [{"sig": "MEUCIQDZ8ArIkpObF1MhTcJQ32GnHDWslg5oL76uOuh+dPCgNwIgaTaYaCBId/N57k8OLmwxV/pF6zOHsChp9u0VMEndDQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeb3lICRA9TVsSAnZWagAArawP/i0hjBy1WJzhk02ZSz81\nKYFwa8kqW/N9aMak2VvUUoIqIFg63E2o1TObtWzNhHCq09VeTaDfCgkCVTRe\ndYVQ71o9rTObG9D/EFYjpi1eL7oFRuZKLEw1/DV3lN1cYopALSFQ0Hfu8iNs\ntpuSMfQD1kh9s2i6vvBvgyA73cEMkqieCtvN64FgixCmKXS7yJAeg2e1Z4H3\nJ69gUc/Hobfshsbx7kZUiNit0+FafWPyXy+FQfdVYGedqSQmvxn0u7wevhxg\nQ5nXnMcNWLxKphyp9G0xmNn7KbRAbt1PELrOf0TWdnQFPMVCb/WZs0Y2gQBh\nBpemsBHpdtH3dDf1el+3QzAGN9nw2pqLz5cgcspJQnn6gCfVVloJuaMxP0/K\nooEjwFZI7oo0M+EFur/hA+XmNpnMMuMOf4TWYeTuOmvN1NDOcX6GrIVyl1WB\n9coDHnI4vpL+rxUp/cnTm70Xd62aWatIrmwuzkZHX1pJPYitBd4OmzTF0IhL\n93H16SxSS4JTh6its66wMSP8eOw3sUuwwUY193PLOGkNgQqMPpLwQvgI4trb\n/qHxhl1Pz2akIEP6WfrrZN0IgfcehF6hb3YE6MDbrzla9TxcJ3hcf0s1r6Ir\ns29KSki6dvfZGMfzrkWcb1AQviX2FPfbSDOuGWVmQijpV8zaBmnD93a7Jk3z\nOhpu\r\n=FHZ2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.4.1": {"name": "@cloudbase/manager-node", "version": "2.4.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "eca2afe5442f2d3a9e40e61e7a6565e5acb524eb", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-2.4.1.tgz", "fileCount": 105, "integrity": "sha512-4MektSSc9WToxObAoppW1Gun5ij13LSC0KB68h2PPzjQuZRsSHiIQk2gtFB86YEgwWaLK0Ux6OAdDBmuh8W3rQ==", "signatures": [{"sig": "MEYCIQDAdT6Sz2JySnOwJjmYb66iUKii66AuAqVcuwp1Bm6eQwIhAIm5W0LWehgfY4nJql7B3lZI++PY0nfNrDQV+LdbqCR9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeW/8CRA9TVsSAnZWagAAUW0P/j+JK1GJsHJb8SKBwdjU\niAHBY7gxBG8M5RPdnmqgTCWOUtAT2M97qYsdNzxxCTVv1TbBiyvvNTre9LhH\nvw1nFEC6PBGyib2BmsTcqLX2PtD+rkrJpnM22FBVtAWF+1h4KqeJw7qatOIB\nmFm8Jljd+s85Yu9iGLK/vTJAN1L5+UlSYlUA55i2WOA/I7pY8r7nPByQrhrB\nrEknccq1dKq2kK5CIvGMO4c5/1b3blzyP4v/fpDKKDpjQlYh5QV5tNIcQ4mK\npu/qBCGYwwyxLKDEejuYAsLmG+oVEEsWzfXzsuxsy3EMfZkoWlhtoFR8aU1z\nU8VqVoX1ePevjqYyiE754Nl5GAr0Tlmo23663jq0orpZ9qkn1gut6zabPXSH\nyvGiveeCMLok2WhsLgyeoIj9rYGLTLj769HBWCoD8vIq21VDmhFvY7C9P2Bq\nTLs+Nfwe7cUBA4nRtIecv/8swkSf6Eqz4WIbG+x4lohe5DLD3CVnmsKQaDwg\nwsXuqFRbka5jxItdmLIOHGBxzvHplWM7M0oUTcb9E1dgTCvY2VPdiCrpZsbk\n46geoucQVQbEI5sZmb7qfNoL2dYE2SYKx859wjFhG3Xg0fqN4Bfy07UYQd4B\nANlHnYEoJGlHGoo9uvRSQM6Eh4D/lNhyo7eE2eT3uL0EqB3tlXj9RH/4KgCW\nYiks\r\n=+umT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-beta": {"name": "@cloudbase/manager-node", "version": "3.0.0-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "623d8066cba09388b0f981b504353494c962511a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.0.0-beta.tgz", "fileCount": 105, "integrity": "sha512-nONeAJanE3x8QPJVPfut7oQqK92frFP9bOc0WSbBIALgAgG2c+sgLpekmTuPIbLrNYw2Vs8doTwCPs73FYIE0w==", "signatures": [{"sig": "MEUCIQD8BiFd+d1v/zy0oiTDmp1CW1lkT03QUjdKNjsam7qF3gIgWIm10c4QMPVs0qARi31W9+o24xvySR1a92QRNVYqVOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegWc+CRA9TVsSAnZWagAAsZ4P/jGBikgWlbKllazDzb4Q\nvGaJVRnPZuUsq0+PHik8o1D3U3iwzszu2oBYiEazv5e4DBm2CRnaXBL8sPVm\nQTkXWOXOeu7KzOpszw11RlOT3UWr8VDbzbPg76vyRF4vGT4IsjC47b7DMx9w\nrZpT2czM0V4HV6MguqflYev9Yi1Lh/ddmEfAOLNwAEfemMvHFga23Vhmxouw\n2Cjf+PlMX2YbCBD/4mkoEphrA2eP0RRaulLxSmUNbNjXsBUGS+UUmg0d7OVW\nCKxDMJ103FNEC2K//vekn2mGJDfOesHK9HOhDvarwWoJUQTTSA9HL2XqKB9E\nx7Y2lrGr+Ham61PntcWqTwWt+e67X464A3uQLUERPYzgWkO39fvJs97U/DK/\ny+q09bNN7R1xdHgOVJxoGE16rafhSJdh97jNg+OU9cuDl4fHU44M8Gw+ytLv\nkcYMWz6pVZRvbW+UAj80IRJJ8VKm9obbddeLNWNY8OMNpdvbRlFXNKnIQXKT\n7G7oNBYyQ3XVhzmAO+ydHJ8+C1EOvj/37T3dmXJ5i+Z7ybgOnG+YeUFS+GCq\nxbpNAGbHIGarTpl9iVEm34FDzfZU+cYjRdjPIK2GpvzeKfuFqpOHwhGexwMN\nZZnlMoa3a0oAN+0DRaqk6bJtRn2lp68VNNNvwxXCor3BFdC/RX3RwQ4+Ei27\nJDzY\r\n=2Q9l\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-beta.1": {"name": "@cloudbase/manager-node", "version": "3.0.0-beta.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b62f0448b81c36c9dec95582ebf720f1ca1734ef", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.0.0-beta.1.tgz", "fileCount": 105, "integrity": "sha512-CKeaPkz3FAmyo/fMiCGkcYHOFWadFpe8GyLgfoBEn9NxnjbbvwlWfWsW33dxxD0Xdr60soe+3VavUlNqiNY+cA==", "signatures": [{"sig": "MEQCIEJEFV9n6YIcuVSuvkzhCCKdlrNDTJdf/37M3grqnDjXAiB2t8qCD/6NCHMKQz8nLmMzclNLMUz46BmWDsW/hGG0Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehdYBCRA9TVsSAnZWagAAVYUP/iumMWa4JjRFPAihgpnU\nCpH5r80dUGu3Hp4Zl2cSY4l6qnfqordI3CWNRdSNbPaI4du+M+Z/cFEZR0lY\ncH1IxeXfwW9FSpP6kbdXlfho+prEdvXvPtxZWu2JjIQBN/mLBXtgp97pbrub\nnYQ+F2XJo/LARx3lG5uXxXAUvIijoztYdiaNCmqJu+CCi7psWSm/afWia40B\nr3H3JenyWg434SmxklKLmJpaPRfBjICMlZ+WyOnqU024bFvZVh1hGYTqQ/wF\njro95dcycNX+g+HYkeUx55xNbivF3o8qHpfuPqNCUkQHBddRDm2ULBX68PUk\n9oHtV5gnDxZcaZwu6FUWCQkibCJC7N194Q2sJv7RNEC1U83MGhOGnQ+v5upd\n3MqHoU/Pk4MT7+6QIiqrl8WGqVJir4hXItDIi3boB+5k6eVcjtrCKvuy2HSt\nbvepCXzQ9YtmeQrk7sd3D1K9qpG9qpoBTjS6NwCnZMr4jG8V8CratqLt7/4X\nLNf3jYUYN6NMxT/x3vTJ97CCJPw7lxxbFPesYS2PxZDSmBNJLSidP5tkU+z7\nIbxeeDIj4VwolNl/q2bfxIRQO4k7tdjZl0T+Fi8eiljdSp8unpcbpX1/yuTQ\nPBU6+EjgmvYHz6Ts3rlc2TndFo3bQTNqe38qW/6asjz1Qnj0cHt/+CcGSmEi\nBiKn\r\n=tjCt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0": {"name": "@cloudbase/manager-node", "version": "3.0.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "0580f3039699b269785be9adf7eec9f5528b3be5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.0.0.tgz", "fileCount": 105, "integrity": "sha512-vlQEGJXxlzSUC/Jbo3+n11nkEKeZH7QvEQw/dmeLe7L+qHiTlycGQSKavwniTyW5QYRdiNg2CM1HHfGiSvWC2A==", "signatures": [{"sig": "MEUCIQCpCnnzN6tow/+rn530pKB6Upxh0pTgD1cJje/+Uyf8zAIgHNUNDaZweoTrIBnFVFITkB+Gbg7Cv1hOAJA9gW7+YCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejorvCRA9TVsSAnZWagAAFFoP/3isgnrQulY5WfNdaDMg\nwayNNug+HSx2sTG/nEeMv7PyF48fS1099hP2GeLP5ni2hB1neXzhC3ALI6GH\nXdq5zWBvnW/VA1VKpXYUn920PNAEfOKuD5XdbYnjkneIxVNVa+5xfh0yY+cO\na7HLgY35dxrTscqrHWWbGTtIQgVk/bm9JAbM7sXAL4FiGKcfz7M5urArGodJ\nuNHlUtNYhRWLKZKiyzohG/Zb/Ish+SSDoT9QBMFU+RPHzz6mYPt5yrdy2UyM\njtqn+Wo6QdGCuCuvaw8DM3TENCAn28Eaad4lOCGKCkw+VAET8O1gSejfmHuu\nBulr8aUNs2Usb7uw4HscXitIDIW51lkUmeVdJ0Bk1r4OSmCcBBQ0CfEMkYe2\n0YUMLPresyYl3GMVtWppUl4JL4T5I7Hkbgy9R0bQOsldjYllHL5SX9eaZe6F\nJjQWqL1Km6+KHPdB4rXfVGxRVvNjSnZVC9PLTMvDZyskCvjyLcpDQ88ygQjO\nDwlwcuJtJI9s/XSbDwvRMOSoCHt7XFCEYoFKbAruFB9ftj0BeKm3sehgNJLK\ncLzcHoJBs072p/ED7D3hc4QxcTG1svwVbv/74D2L3EL9mS7TuIsRPNtvXQxp\nP5eFUZHLxuR85mz+GGCQUoncRTwjVfZAO4MGxeN/BE7FMypzeWfvX92gsuTv\nZk1Z\r\n=cyBT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.0": {"name": "@cloudbase/manager-node", "version": "3.1.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "58b19a1cbe61164e5c2d7c2e104eb04552b5077e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.1.0.tgz", "fileCount": 111, "integrity": "sha512-Qbp6mnI8G84Cqay5ZT6PWIwPqk1uyb1hbExw5eRbfoS5zzYZNBwU7Y9N5gru8YENByQMavX2XSHTVa+Z3UOTKw==", "signatures": [{"sig": "MEUCIQC9hzxVVXyogC3DwygaUPCUzsoewYyAg2PudmFqXcGuqQIgWGAwNK74KTXLXLlRBnC9QOLiQCx85XwHD8qGxXr3x3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelWPXCRA9TVsSAnZWagAAKNMP/iCgYr+Li3aKhTJlqngg\nbY/wDJ2x5YQieTn02uu+xKdy7vwbyc5LBaiPFo0KXoxQkDMZHyACEwSPoLok\nARwLTOwsesJCVuCeuuvWXMpHZOq05yMAGIdGLWiXsXxLKV5JhVbMRCknRPw6\nGbnoDY0cO/ZLdmZx02XrtX3nZo9ATDyC1fWHkyM1KH/NE5Z02yesmO1d+EZF\nOM7Z/GpNZKXfrMOP2bKKatezsrUe5W+2W3TLHLQS1EpZ27cWRosVXhRVim/v\nMnZ2j4C3InZ6H05Wg6N5AwuFCeCD7H7awmm9P6tZC2RNJhqOol9GotMh4/gT\nQueWMQW42Mmen2lSp8mqmGUtXv+vLTEej6zw2q0vwdWcyDM4e59ZE5P2D+xO\nuOr6BLBy/mTwB74NTzLYdtpC6N1ym3BCZzq3v9AH4rOyywvbYIXjLd2n+9xk\n7h/U5lSqdEZcepX9G6djUhHKdIV1bYqa3TAoko4/NwhcYAhJmrdmX1P1lmDv\nIAP6VyoHVQbMprlGFW1J/N7qcS6iC9mdxzw45uSN23COtzIuir2Po3Ee8SDC\nOOgLIQtQl/gqKxauQO4vZoDJgOir9pZDRSGLANZWkwj5/bGd4yJdJdlQkJvg\ndOgI5mM0e32Zg1DPRuJ7JeGWD4ODJsD7aXACIG0zjM6A7lGz4XHDjIoqFdHj\nW2W3\r\n=4v6M\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.1": {"name": "@cloudbase/manager-node", "version": "3.1.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "9d040dd6a9860118248d7f17c48ad768d50791e4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.1.1.tgz", "fileCount": 111, "integrity": "sha512-n0JMQH6gBpplYK14p63ZHIgvMLvgf5HfNZOSOzKPk8RlZ6KFZTpjy9eF583vxVUcr2UeWFtdGLkSeTrJHtPb8A==", "signatures": [{"sig": "MEUCIQCek7tw1u57FNchWZdEeQiuciJdReEJgGp0nPrvImQoZwIgAWXjN2aOjM0Qvr4yK1Qy6y1aBmROab4ATxjmOI70nyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelnmvCRA9TVsSAnZWagAAlgsP/2r4j4JFsi2+aJ1gMaN7\nr7J/OXtI0e2ZogGeLVQyUsQq+SCCoRYL4bUwB0pNfS5GMSKRW2AstO3HTk5V\nanjgfiqoEkwj+NrbvbHGNUAWgv7j8ccfqjyGWloipn5Q5V4eUUw5lYzwH5q2\nBh1elXgX6ZHvFWfD6MKXxVlEJmqIM5wU8R1VEtHntnoZLUSASVFhiWk8ImGW\nHJX6PdSYsLzVeAVSpEHUZUmyg79NHsBpWydGrUoN5wMU0j5GfPxnITTIEvKE\nle98Wx+SUG9dAJPIseHX66YIA448Z+MtrOTpRAHaaxMNKeBwmdmbPjUvvDRW\nWEkY8U7xo3KxqVwehcVgV824SMHg0eZ+/I22Dm3s8O5w4ke5kJT/+HVsOZ13\nTtGeW9kgIg6S3oWndZfug7MqB1Jfp2FvNbJjlynC4D/Tha6Q46GUBDeNGJHt\nmX2B1BK1yY69ToI0y+P4Gyp47J8BdV5agKMTdV7KEVgP6Uu+j+RxaHvXYUtX\nDVrU82J5WgfrRtQfirhAkXPL4mGZzcB6TecqsiwGyxg2fT3lP1VV0hVlYFiT\nOLMXOezRQDIGpdo/5lxImZ922rcLEPflNv/wbzfyJaya6JtufoKItT/+uXNq\neZ6s7Ozm+qOW52WnS3GQFVa0n/6t992NCumPsUY3mG7mgVTArKRpdMZLxst/\ntUF5\r\n=ofSF\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.14", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.2": {"name": "@cloudbase/manager-node", "version": "3.1.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "42aee9f72bc57bfc7fbb30ef1586063f2d630d79", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.1.2.tgz", "fileCount": 111, "integrity": "sha512-4CJ9rZN1r2r8Ei4QQjZVEfI3oS/kFtz5/R9lIt3Y3jpGLmZf9OvbeAlsniFaQMdyI8DhyZe8gZvG5T0F81b1Ug==", "signatures": [{"sig": "MEUCIBc8Eelao/s3RoV/b7/HPLqosmMK0QHoNNCCdXGdCVilAiEAlO1N4JaKtUIy8846bLVXKHs+eB55unJiqiCvoSqO+t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemBIQCRA9TVsSAnZWagAAlSIQAKCFH6g4UyUql08ZOm0J\nOGnFFjihDD+tpYItn297SmmJGfcX8NfkgSlujB7zOo6r6mmsTT2DFBDdRtan\nXORANdcRhKGTanErN4YyX/bsAQFcq0yVEAzzBHMyzir2tnWMdGuoivk54KjD\n7n8mb24L/p3kLPkw2OW+/YpURYrLihZjpMx90BZ2LL/D0m9NyR7cGLBVmzEM\nskkAgwQOrQxE2xHz3vbQgOS6xW8XZ1RBthJF4zTY/30Fc2H+0uJl7s6HfzU0\n5tgQHpVLwKAfVVgLORyAeTPnbhBbyQ9KxTsZKDBzKVQ3quSxY//2MO6/k6bP\n0T6CYFC81dWD4ZHXV303++sRDD5C0NA0EzPEnYY7AIgN935MXpPH+NaXQyZH\nV3dDU659x/iZ7XVO7+W/c850DbUDCwBcxKfs8/m0istuTMB20CiU77TNlyoe\nlCeATy/ttCJ6iLhmmwSffriUbzLFj1d/Tz+au771cB2U7M+SVZW6FgRGQvAt\n4lrOKy279bfrVl8mSo/ihrQrj9iMKlpqc9GtZ2OBranBnTA5wvb+lNPs9kR8\nVD2jGw+lSCFYtmETz3nwxuXcvdKXHmhoxIDIjbADrFJoD19rv+aUtDoN/x1X\nhYOlya0bO9i88ShIUL6Pz7FVNmsjRcn8kSEK8FV+kfGriTBaHWHHOSct8VD+\nvebj\r\n=vOiD\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.3": {"name": "@cloudbase/manager-node", "version": "3.1.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5334d961eafbbaa97e943638737ab05364649b63", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.1.3.tgz", "fileCount": 111, "integrity": "sha512-Kn1Vq9yM28z5fOlSdTn6cWGF/jOb6x/uFMvZOuV+1c/PJs+KGfU7VX3Kt6VVjKZGacodjlMzIRwwIUzlrL3FiQ==", "signatures": [{"sig": "MEUCIQD5nNDMI28BURNle04zlF/IuGxcciVz52tyzwgcKFeBmQIgTZz8MzBOXiqrme5WFCh5FKUSFwrT4W0Hjgt7STezWMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemVigCRA9TVsSAnZWagAAT9EP/1Els0GZ8ql9+z3GiTCN\nkC4Xqbf4H5gH4OTE7b/yUvIalvKN57F8/seBJM/I73g9fnRTwYxNZ6NcdENu\n1CRfsjTNGtVk+vQIFJ/HEg0UN4CFcZqZ7nJIaDp+TvD3xauYakTW3jXLCxUP\nkdA6CWEfIkJjlxRwLQOCfJ4soDxoG6oIGrJ3O47eh8BAv033wIZae3NbAaGA\nRjQLDfCS6fx2ddX4eunf2oEN7wxjUBb4qEE+mHJTJpLjSh+cRKUNKuFwHZkd\nPlkjAUp1Fa1DC2Gqkgf05yVpm8v3pU8KS/UPxn66r41FaAhztqzoYpfpSFge\nCHb9iEnNQn4Rzw8NiAP981HQYHHCe3+9ZcJ/4Vod3KAIq9XpTzy3qG5TuOro\naa2vggjF8tFBhNKavi1YUhxrbPkdJybGRZChB9eWyhilt66Bqnt0aElCoO1A\nkrCdGGg1zj+RvQVinmQcT8tb/JP+PJep1oa8KsEZaJoFw9OXwZtLiuSK2jkF\nK15BS1w+aZ2Mn8Q1CeYHlnGwTeQ1NkcE97IP/PuIXChJdi/lxnqxF7CT3EFJ\nNJ1zcwL2C3Uc9lnoG7ks25XX4MfGEyFTohy1u+njmPy2g/BTcSdjsmmySsgF\nkJ57gESNMgdD26MAXRUlXpuuu+myjqSKBjZF4lNx3KPoajG3sFBMwvaRQ4XM\n4vfO\r\n=v2dv\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.0": {"name": "@cloudbase/manager-node", "version": "3.2.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "aafa4e5bbcf4056fa229494f711dc2c963f93c13", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.2.0.tgz", "fileCount": 111, "integrity": "sha512-yi45Xj1aigWainYkSP8ZFxM8DVLzWcDwf+ITvOlpco8RNJ7jvCoytGQtz/AJDD0oxHQ6q918aaLaMW1BSIw/YA==", "signatures": [{"sig": "MEUCIQCUqhWBTaGrqzqvXBo4SsDHD1lWjoyeFs9XIIt57sVfcwIgT0PaMrSiJvLAudqqF3B17EsKf6HHzqvjvQBVSI4Be0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeond5CRA9TVsSAnZWagAAa5UP/iaNL+IQraZbBI5Wkt48\nWzlMcRqvRLeWVcRmQ/SGjcEjeZuw0atZlPswsqJjYOV7ysacaNQSLkLu/lM1\nRhoRNqQHje+os79Y46nLpB7a315vfWgx223U68Y8ABurTKIzoDqTQqw8P4v3\nPo9BUd3KuRJTXJhA+4PAZTMGXgC0Rc//P1hPlKuz++IkPaTeF5vBm0UHuDC8\niwuTifAw1p3qDx1ryrPcJr2CmAsCPgDt7nVPO+vq8XGY4Nzzpvj8uYntWvBv\nl5SwXsYOMJJpGTS+Waf3J843VcugEreKkG7QNl91JLFziLolDyoh+OW0JH8q\nx3oYGhTpt0n1czFo7O52hf6zkVBljFHjCyaYIw2+B8z3au6zSLIiuBt6lHyZ\nJmAlGacP+edFJq9h9mLp0tp+5SOzpWFe8LtKeo5LtmL679AbYd9OmTP0Ofgo\ns3RTiCJgNxCNJGOkAzbL5f60qJMKtHQVfVZo5pvWu0AbNnR6XZUv1cez6f89\nMEsEp+jDUQNl07cTppwHFEhEOYL2BCAgr2G9xWTaGT6DVQqPDRrA55p4j80t\nwCiRyUo7mVVu/6+mYwfzyU1QDfgEsWIhLuOj7C+JRrLDxhXTasHrwqqeonJP\nXs9L+Nwg7iFDRwoFvqfvq/2qBnYaHlg+KNmgKMaKjjm+zXSvezCWnYTE2UkF\njuRt\r\n=un4m\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.1": {"name": "@cloudbase/manager-node", "version": "3.2.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "e7c70886ac098c3265466ba5759d4c44d80db762", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.2.1.tgz", "fileCount": 111, "integrity": "sha512-PRUWEyXOWolZXW8Y0Wy7B2sGz6VF440ICcFdj8yNGLbSkv+qwGDKO+1t4v85O2zYNXCy6A+KM8PC3vvTQXCVBQ==", "signatures": [{"sig": "MEYCIQDk1oTdICxaPcZqEi/i3WJsRwVHN+kIhUtHWKWF5Mtm/QIhAPEaG4Yd3kHn1ZpBVdz++/oDXIChCgj361vsFiqfh3gj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 367290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep5aOCRA9TVsSAnZWagAAb7gP/iWp8e8zbSaQuwEnvybI\nfYJk/6abUyLJAoPH5pFKg2/4imHk5ozPeBzTNsEKko51D3qzo8w0V9XtRs+v\nAqyh2W5YVTRjlNnDbQjjzLJv93j9o4bc6zCzAaAPvsEH7M8CwXBKEd0OkU+H\n4Ru4d2wR5nVHYg3qPVV4IxfCfUuUTYX0AD41Culde5i3rJQI0z6exvyGyag7\n//lP/EDPh4N6MmVI4yMUBTMd3wrvzfDp4Xp4mNGQqpPCtleFGq5yyNHAJSao\ntMVfkdStlTElbXxR7sVtiWtPwQh4LBrc9w5YjBv3Ss2AmG8Y7rXceLiJhugj\ne1yinSgJ3oU8KAQPmLRWdVAeR/52A8L1Ko+gUPcCxefZbF0a88d1avU5e8lF\nXKcNLYVXp/u9z9vM5J/WwhkwCRbpgUEJgZyIAQHpFXuWXNgpSuv0VSD/hqXs\n9op4xN8hkf7AtTHruft6sG9Xr3+jOxNztw/4WynuZ4m9gHeFLvVPPVasI1Kj\n2J3DrDR46aGmMOllSOc57jr2669wUNlG//fM7scZvLJfl+fwnR9Im5eQtvqB\ngB2WUl6RLueLMmT0KbRGo2KjGsETcfbxKQsuVrCm7gsU60KsVghtKlwexJ4M\nE1Wdwi+Ty0bltchcI9PtAewpwSV692iXuC5BPHqbswbMOsrEOLhYlWUR6FhZ\nRH22\r\n=U+/X\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.2": {"name": "@cloudbase/manager-node", "version": "3.2.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "56c68f3d77192a67b05aaf5687c2530b21e6eb91", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.2.2.tgz", "fileCount": 110, "integrity": "sha512-daqywj0h8VT1NXIYKLYAr11KkwZ+ZDlA2mHIUwVVtDD1eR1Cf+0RBJ0BY4i2Jhj+JORMsl3koUP5ojbyrmQDkg==", "signatures": [{"sig": "MEYCIQCbt3upwxyny86nacy1f79ZpZ7hZlol6L3qNR/3QSS66AIhAOBY/3D5feSj5o9N+s9QN+FShAJ0kqoYRPFSp88T46T+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 367209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqw8yCRA9TVsSAnZWagAAVuAP/A5Wy7pRk1Gj8w1l/Nx6\njCcCYD42Iy2Z9WP4lgMx3iDlQU8zMK6Mk9t57OXB3KQMEJbU0fgn+CJEN23c\nMWju0WCROEurmmoB3OCI0Jct0Jfp/kian+kZ0iMITy5+KLWkdbTQC02cS9th\nwpPer3TG5ScD2l3O3Zgi1kAqsqwrnI/QgzMR34i1ga+iOounTEf2UASIgMwR\nzLjgPMsApAsGSKpPUnqizLmeA9YhFL6EWXP6qMS0moF1qmn38PdDTVx0eUR/\nLTkX6g7QSAuAcAvlDkPmhUTySJLsQzHd1VMtChzk8eRx4on5R/kuagxc+O0L\n0SxP3pbkvk+OW4D+FYDP0bSgR4dcpR6XQ2U/PF63TvA9i4NVOAes1Nca7kKy\nKmG7TKBvvBkQenBymeYSvSoEbjS9bMQHdfuWV9u+FkASK/nGMZT6f1FFeo/c\nfoSf4swojY6YLysHRyiCtXaoqlDK1NT3/aahH+b1B6gP9ZPvT2cjo7R8gPP6\nzAZiIk3qGwi2c6fJibKVsscqgeIPpsKWcDEwE6M/c1s1dcA7GEqg7MPuO9Mp\nfNj1hoe6Ms1qEgt2GQmMT7UHjbiacq2ejCSfAARHwJm+2dQfE53VWkL63KXP\npLHWimCfYurSOtyEATjdGLBNqYlSzK+PpAT23admdjZ7A8+z8DpHVg2SsUb1\nLZIm\r\n=4BNf\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.7.5", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.3": {"name": "@cloudbase/manager-node", "version": "3.2.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "c79e68e677c349466365bfe3bbf7c1eee5cb877a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.2.3.tgz", "fileCount": 114, "integrity": "sha512-aFUT5NAwxdQhQVxicHwerFlZpXL1Tyl6AmUP7WPJG1sGMW15wlfnFCNKpP2oF+iTA9Nc+5C02LMUnQpfEPRKQw==", "signatures": [{"sig": "MEYCIQDSdZTFLUvQZFDu83O0sACCMgqPv82ttfec5PSVF41OZgIhAMVqOjWzVW8PLKDXU+x/yhOEOOg+kCUtTMUmql5RFFyG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 369605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuLKaCRA9TVsSAnZWagAAwx0P/0pYFn2xN9zEcGmhLVYM\nizuhX4IwA8JOJ3U7aTn/m8pgqfqDHB1uL0QiFU6zHb/PgVvzKg9Ws/cXmqZT\nEDqCEmLRd1+dRSC3+q+wtOVJferWKtycipT7pfCqGoIfTRcKHwrWDjplAAhj\nQjtxledqVuuy81WPr7cysNzSa8DI72EnzmMH+Jw5UZRrHlkuFAFeD+PAShL2\n2D2Ba1oIGF9RABjl4tegUfZjIb493g+V3W5OxR3FRUZC9CtpxLLj97YJUWTf\naYQdSWTC8mLf5yo2QBQjlqNYX8Sb3cqjn6HzHuUHqp5s1dZ5ofm05/g5uvkt\ng6ifEJryMC+T5/LhVEuTwzTvrBZheaSWCD+l7KuzYcGY06zitUw7gyMkAN/Q\nvurSVGu7khxfbQ1zjJW6pS+OpG0tj9PvNfjVHuy56kDAMb5Pb+ihhLqLvjpP\necloDF/q7VHbH2fxbBFmfernZ6uw1ktSiWfpnDHcVwpkis9gmp3DAfdJheOn\nP79wA/3f2ojBix9DujQUBemjb1mPnN7CzMXxaDuXukGmk+5OB/k59Z/p0PUJ\nSLYq7x/fsec7KUylrNIFf0PhRlcm6VpGde2Qbf/O2f+5X0vaOO5sXiYeeziB\n9UKBd44W0YqjfOK1ADwYyXzce/3rCKLo5a85U8rUu3/9qdbMpCzNR81oZolX\n+uJ4\r\n=rUaL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.4": {"name": "@cloudbase/manager-node", "version": "3.2.4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "37432ced10c89caeb468dcc9ce22252031a0be4a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.2.4.tgz", "fileCount": 114, "integrity": "sha512-4g5dhdff+z0S9FCY5Q4LZSV5SsCuRE0257xvnBRIov4Qwrz65J3yqJtvKPHDszZNZVZ/qPWOgPcgAa6Qujkp7w==", "signatures": [{"sig": "MEUCIF2TAlhOfOAT5tuaJ1Ynl/OHxOcGVD/UtSS8hTtFr2UTAiEA0sGHclSzBwtuGaDZjp36Aj4Gwsbm4mkNzJO1nSQLLXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 369852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu13CCRA9TVsSAnZWagAAqvcQAKIOICGU0hvpOU1YfiB/\nqw/8zu9rEXIy9pC34xDmC6RXFoinf4cNDfo6ZqeJRs3LgqlH+TFEImh1r7Eb\noDkD0eOT+hbmFjt6LpG3qpCMWaTFQd3cphtsQycgO141X826ntwQBvrfv1b7\n3Oe7S7Vz8zkk08r1FUGSi/Wlw+2rea3p0K0tItwTAuGIpAzeZ4ugfw2tnA97\n9o2R4YdgApBiTQ1yk/DSGHwYgPoK+ZWn2ZgR1pz56KTfMlb7csCrgLo/qEWl\nCIFH4rK3CK1w7ReS5Vk6ok6Xv/mzqnsjuONS7yfqRSjfjtKUOTC+hh4ruNLG\n/bfl3fEmvaXGAZqTiCtkU4zJzucsmdJvewIIBqDY5M6WBHp1cDqRkNOmibLi\nAWubTQJJdj24zTcuqclpmwjKvln7PKbsF3rJaKcSzTBXyp7LzO9Z3vrXktNi\n/vVzLIWtXM+DPKoPOrDwbldZuPIGwubA99fu133A6hbohcTsd4ji7Pt7V+nh\nD0QjH5e+vqnnk5zRxajCD+HM+x9eGZ2RIJyzqDpq2QZg8vhJTWb+D0qIYmj7\nJRo7DjwwmtbVJho8u63G7aJ6B9XAyUyruWIXSX5aL3QSuJ9gHCkpiCslVQj0\nXK/0Jh6gvCMycgQhlmHerdyPvU5YB35jYGc92mQ+4q2WzecK0L1I/xPLd3y5\nK3XK\r\n=+9Mo\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.3.0": {"name": "@cloudbase/manager-node", "version": "3.3.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "401380bd730953e58ec7c9d3d34b5786c9fc499f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.3.0.tgz", "fileCount": 114, "integrity": "sha512-RszDczHC6ny7au2COhXC6bwM+DpoYgnMVn65wKbSCn0pxqsDQToLWYuqchImuE8muAzt5DEDMUyofgiVKIfepg==", "signatures": [{"sig": "MEQCIHdDSjiS8VaAdr3plg9u1t2qpQguc9S2lLrqcFsCXXVHAiAB0pbY+uJgsV+Co3mwP+cldMRzRx5cai++qjkduEU1IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexmDZCRA9TVsSAnZWagAAYYAP/2M5Q7Po4ikuRKjbfuRY\nbbVUMSIgdUlDWgq4TaJbgQ2VFrj0wjr2oboaUnN4H+yHDw087Kp3YSZPFqNv\nZm6+aQ8VXNb7jOn2/rxk8OmeLXzuDBjBnPC+FEh3bmjHaRtRdiYj3JuhS9mI\nNX0z0vJfMDk0yhysqPGIyfz4CiU1iBqP2G3S8EV6eyJdHGNI+ONIM6WwRb04\n/MpTGjNCcxy7jTYiOwHIX0b+qbgbl8gqese+9biNXHQwUish/c8UblFZEXus\nsYw+B6uILZ1AjvDzL9+PMDlKWKc0xCghXyR4+Hnvpt13bMal4uQlOjcy/Nsb\n2vD6TZsMY7FoPYoqEXppVlkAO7tbCt+n53U5Eo0g0EMJHq5HyaByihoD8Af6\nuIhSZCkrbmvaByIWjjTdlFMWTwNomroF0kKRKcCOQdmJnSccDGoJicsG+oeW\n3YMYN8tIL5MPacvprot9YcCqWZvnxJk09V6cCrW84paGHJB/eWNXmklMfct+\n+JvQVD3BY3o/tEdX6UTmQJ7ZwrN0sk26j7sVaXq6YQWs2JdplWp4ZAHxkbpF\ncvwsd/2oiE4uOCqvxFLACBc66D2eDQIzShYwr13wPzrmSVcBz4MNMk8rAPhq\nXFkpDqFJ7Ya7AqarPrh/opidD8iBbs5cSt6xcQvVBMZpUCtLZrXEhKa7PUjw\nlzM8\r\n=4L5v\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.3.1": {"name": "@cloudbase/manager-node", "version": "3.3.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bbbc86aae1de4488a767813c1d2f4e346140a06e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.3.1.tgz", "fileCount": 114, "integrity": "sha512-plXqYcD9svuGSE1BIxR8bDB2U0VbTSLdu94RK8ReeiAOqao7ncBiC67wnfnwLEM4xk9KEj7e+de1P5LpHoqTJw==", "signatures": [{"sig": "MEUCIGKK93Us/ho2AUbiGTRLLTchi+3/KaLc5WrRc64tB3fPAiEAgz4U4nOk1gZAOMnX/rk/dkbX5wzk2IwHbbLk9NlRAWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezjTQCRA9TVsSAnZWagAAuxQQAIiG3LDZn4PS07U7dM5Y\ngysecSBUxO4d/hX5kbsRTjs/mLnPeRCUa+RrTXDautYp89Ik67ZPirwtsKz1\nQTi0TRh8+E6k8ieh+VMDrzokH38sMrdmhcV49XawUfoPimnAwfMsximLcu/x\noPMhYZFsZi3ZF76dYCyiUz0flQMjXAHN9TYdtST7EPdKmSmuvi/k6B5d3EBT\n6mNH5psUeTLo6G+Mql9Jjokwik+oxKsY3bVf5ta+SZrORRBORq/SsE3Jo+84\nSrk60eqXEV3lbKLBiCeRcxysLgSWA1YPkSVqQoQznXA7m+5xiFjZZbAN1l4C\nwwdHBLfQAeYlQV+Z5S8fVamKuIXHrOmq4OFgHiuP8W5/80RJeAAPdfhn6llh\nxwjML1BHrFvt1NbkHJhq0/T2s/FyTe5HUHnBaMaG3Pg/JalNa8RECKtalGpa\nB9Kv69ZBkMGqF9pC4St89D0J4D+dI6n8PbYOIje2og3F3gDLGS3/a2H1iMxc\nl6t40Mx7PFhtarbPgHxL1btKrmeiEtCX5JgfnAzBT1cOSmddaGsvNHcLfufr\nqwLyjs6tZRXrhJu66Yj8LdMEyt8s25PYNDD8PwuvyoX2LMe9TcTjUoHFGROl\ntnTguGkCpqkUypU8HIpPJYKgydNcSvm55sMHLIF3VaMfYKp3ykZqadHHI81V\nv5Ck\r\n=Yfjn\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.3.2": {"name": "@cloudbase/manager-node", "version": "3.3.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "23f35ec30bdb6494758110c119c743e35ccedaf1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.3.2.tgz", "fileCount": 115, "integrity": "sha512-FWMXq9HwkUHaCcChj5fXi5Nww/I3GX0r/NLdvhm9MHIUmwnCjD4RXAC8eVLKujO+Oxqi8QdS3EYE5WLXTcc5Iw==", "signatures": [{"sig": "MEYCIQDYo7leIeWUqp+49/mLc5rhUXYDJiXC6VKCfSjBc4C2YwIhAItCTKgCir6nBO2EcMh7W2cymB7+uaVuFJmX6ERtSt/P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7IjhCRA9TVsSAnZWagAAr0MP/RViwQdx31RyQqqamYKv\n7yQulZY/9FTILUtLDE1varipa7Kfh19pDUxJYQI7u6l6T5m8neR7fLuahnxl\nfbZU86m8nWTHrrzjufihVjTh4xoaMdrdVIg6fGlN4rFut26I7jhAoKNYMO8y\nN3FurrN8SDCBo2WIW9SvYvzDq/Iamb2p+48Zdx00DMpL2tsf66zc1q6ZHP43\nUCVxpDBltOLP9zIw1BD9O1yFL34bx4d2tT7/utjDiE9QfxUHU9Io/1W+qWN3\n1Nz4SOd2SZhmGN8FIAzUIT5PGoTdpwuXUV8Mt4aECSgEjAKGIf8mtSCaIfJM\nUJmYamt3bCfUFzMXcx0IIer7n7hKAyAE7NajGTRRsjsrE/TnDndxkZROEmSk\nDaaBgYhyZMrE4HfFN7HZbJNH+LTCZO60QQtoEKgMnrmDaEVGMKph9MGy681W\nrbGgnalYBWKIgooTjNOSiIuyNviyHnFtPtGpeFKUE204W5k7+dWXGNn5i0Mr\ngFw3Uf4wdVRYvDQC+wmCIjgwrqULtDsSysTQSEjLWTa4jz7X/aBcTt+Dwn9m\nZJjLO9DCnt9kU84mcek5IrFjUV4W89TOzOSIwF4IUk1CjMngPfg6n1Y+RwLA\n2KxTh0M/x8qTrvQklqxV29ZUA2DTKZEs/f9aHb76gK1WadMVdpVmtmcz5nbd\naRhr\r\n=wh9U\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.4.0": {"name": "@cloudbase/manager-node", "version": "3.4.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "6a42fdf166c72003aa937b3193b2de5f019a501f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.4.0.tgz", "fileCount": 124, "integrity": "sha512-QjIVgo/c7/VvACvWVWmEE6T2mo3LDd5tOoEl8aKr+VAEWvdyieShQUF4ixTRk7BbKv2HIolwnE5EE+GifyQmMg==", "signatures": [{"sig": "MEUCIB336DnuIYceO15TGGDVIvrv8OX79ReCjaDmsatXpg/pAiEA+AsL/OGHUUOMYsXe8pJJdHllb6bb7RRQ6bSQibN7LlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 585761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBtKsCRA9TVsSAnZWagAA8eQP/1FDBKwUPrLLquwU6yK2\n1RI+JhXQmCGBjaCct+HFU9A4BmkAEEfzj03TVhnfIhXYA8hlED/haa+pt5G5\nR61dddppyoLp6IOtRaRFUrAj/yP4ra6NQIlygwMfaiTZ0hn/CrL/H7BFS7OT\n6wwZv/yE2cdfaB9S6hVxo43xifM6jj3H4FBkBGjAjWLnA0/gXbLtxDo1YYFV\n3b+Jsv7rnmD04bp1Mkzmz+OTRWtsqnv7Kwl7rSgQw1bJ4veyBcIx9wqMHPIO\n2aoJ/pf0EmaN+ybg/pzlhOauuZNPi5yDot+h/HOvIZ4aEpkRAIJ6++7KrdeE\nSCIbtZdx2q1o96dNJNWbB6IMBHnOcRk+q185IBLTGlXsl7xTcmGuFH4DXOix\nAJHMShuR85rwjhsU1joMBkxMX0UDjkzgYeuD/PsWIfetR9fhkSZePI1pFhyI\nhHtmWT73v4IFMOlfOjTIdBtX/5a1ivEmCoj6qM7Dr0pEzm0Mf/n5HP3C4YxY\nrIXro3wsF4xUTQ5vfFT4ZGItJHmOJe1aG6TvKYcoPKwTOK30hbr4JHJKCpzl\nR1I6CbyHMi2fQyRywIjz1a8pJ3/EkQ5vBZqReBe4WjMrTf9jcHORMECr6rJB\nBznNmm3mFLwDEOqak1QUhgZsoLbf+G1OppHLwzSLDC2e9yIUFPb0QHsnVlnV\nDlGH\r\n=gXkg\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.4.1": {"name": "@cloudbase/manager-node", "version": "3.4.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "56b236b71ef019227c38dd137eb851b338d2da04", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.4.1.tgz", "fileCount": 124, "integrity": "sha512-0l+FIsjlPiXxyn7pbSdDLAKwSRBNK9HHWmeK+iuZCT5meziXKD9OvsUUTx+2xKsDv5u+MRm6iMyRnPPZuy0Lfg==", "signatures": [{"sig": "MEUCIQD4PDxHtmELF5qRR1z3lElQZCU3Nugg8d8bBAOiy4pRRgIgAkLn8l41aZwTiKC+GzeQqPHDvOjP/et0hcXUGzNQW88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 586638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBv35CRA9TVsSAnZWagAA/iIQAJ0jknJqz5AtH7aHdy8q\nnRJ1YKVtC01uo1PVYF8WIOLernAcE7HgIDVrKe5UmCAWIrwKqPxFbXmtbHlS\nh3OCIa1HstVsJFD0apADcA0LwffQNgGMHs8Ui3wkP9MwtuvSNzm2+282vN2p\n1hLrwLhhwnD8wGTmoqlTczyZoq2KPgTPpSZ6OonQrCWa8wAgdRJASczC9BCl\n8+lsqdv9lDjZk84KPmFSFDL43rQkp3oDt+MZ5vie53N8AbNNWGJ8GMyL0e8y\nWEXw8U/ndaqEvjuQXjf+h9lv3t1XShhw2eMvbTBEirsnQMFFxa0cM+dRyiyl\nsMJOsJ9G5KLlHRVm8UeJ3BWPqvP8TmMmMtXMbg+yIp2/xHzOl5LObBeu942x\nUmReGbRRPc5ga5fAOuy0y+mkbpa1exniEjGU4cMb/Pk+LJTheszJQ9jfF4Ok\nArVmOk5HLDfsM1R4Y2kiph22/22yjeXnUqs7aqzmpf5VYefzQlseNugFILin\nXJloEYhCD/C0JMYYdaBEBH1f6NUuy3FH7xDzAMsZxPnQ50zXcnJTJ2J6997n\nGEjOo9hlIUDZoAvE4zz6XQfmzycIYvCaPc67ueVpfkq8vnroqHadwwHYwi7T\nne0QK/0lTUaMfZ/ajwTQ72QpZW+lhhSCGSeEnF7iharzUpg+BH/xqkU4cNac\nMH8S\r\n=cLz1\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.8.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.5.0", "@typescript-eslint/parser": "^2.16.0", "@typescript-eslint/eslint-plugin": "^2.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.4.2": {"name": "@cloudbase/manager-node", "version": "3.4.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5c1e1c0605f879280675bc2052a0a5989f294924", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.4.2.tgz", "fileCount": 124, "integrity": "sha512-PsGi88CAp+6thGfgb16+DnO4rQoNIagat7vkQ0RuLbJPPTIDEfgSdyWSYg/lZQ2Q3jGaPvezM1vmRJpoSvqwcQ==", "signatures": [{"sig": "MEUCICmN2br2Z8TmH6/blFUWqsVH1wxDEc4b+00SFnkHTgCcAiEAw5FkXR+/oFfUMk+GpEpvdOkqkVNKpJ7B8M5799FaTko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 589567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJ8MxCRA9TVsSAnZWagAALysP/0z4+qf+xRiSXJGF5SZC\nDucA/ofFqmZwXoU+IuYX8uuFhvY7IAHwQW4eYLNgzT1QmqMSvzDeN9d8SAiU\nggNaUg61SQTlG//YZbOluLFyPzdQBodw/6v1hb07WS86dyfXeMyfC9CjibWU\nId7bzOMd4EMLmpGwg5olfT7JBbgQ49l9dh0Rlzywkjodo731FLeB9BSuHXt7\nSsImb/nZD+PgqwefVQqs6gYlarv8aS+9OrRIV0jZGHQW08kTl3Ymv0CEPs2p\n47dgDF+tKC7DNEzWgnpm1qIA8ZE0jOluK640DPKZqNtcjAIYrL9E9F1U04yP\ng+3fGSmew3pxL6s3o/VljQ/DGcfSYjcyZ3NhAw6cxLPWtP7o2gEv7JsRaeo2\nNSNkZDi0hULbzV4jPwnOS4wxrNANGpk21bACeGL3VS5kKfDzMmXFHIVLDYMk\nQnm6ByuUqqZVBRq22xtCntHC0WM3rwwUaNVClKyw+SYQz6a2BJb2t13IQUx2\ndLn5hpJCa7oX48rD5AxjH7F82hSndGV5umSNDoOAzMiYyZD6BWnJn6sPmYQA\nbM5Y0ypeMrSGriTTOT+oFeKOPx/j2DaZo68mr9Ye9fJwvLlOlBACEkTg8ee6\n17iDxHIgyrgB+8jZrx1EVK2FZSDeqa1am8JfV4oi8+Q5S6752Ch7DuxnamFO\nwFe7\r\n=4uhF\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.5.0": {"name": "@cloudbase/manager-node", "version": "3.5.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bb8d7244bbdbbc7b196eab99291c2d63c7bcf234", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.5.0.tgz", "fileCount": 128, "integrity": "sha512-iVlAJ1te1buP8UyNNAWNLrusLDJKz0OLWp3Durb4nYPVM/7b4mQQYlbn/2F2jS6LzJMIewnAp5YgUaYFGi/aLw==", "signatures": [{"sig": "MEUCIFXN7k8ePoF4/htR6RbYiSXbLKTisQFjBWOyTdeaALU3AiEA7dWY1YKhgFP7+lDQxQ/pCIPaZ98jmY3IXRW+1Jax/jA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKNA1CRA9TVsSAnZWagAAze8P/3rexuzBZ81bIYxVJLZ3\nP/6sYU8X4DD61twgctIfdENZj93R8cxan7fV45TkRJL6p5g6AW/RTIo1YSMy\nQV2n5NtaVZWiXxcam68gwpVP7Gl0n+q/kkjF04nrFTN38SxrN4LPHtBvWtYy\nAyBQHMH9528UiiaqWtHFIQgjKRmNN7mEcHXRon7ghySfmo+ZApl6Ub8lzgQs\nPQM8P7d28lsyw5qh/l9l+OuXte5Ay47ZlDvLf3EqEklt+rFhnCfDx30h8VlJ\nZZxGzUk2WMvwshzwXlJ33b+p8S+vVztmMX9ofv5Lvpfb3ESxhNP9CyKQ1oK2\nHkZg2WvJFXafd0IuTI/8P0dyP6qd372B3QeyFXZTB759y1PRuHIftsl6iHp2\nTjj74hOKlodY9PQp+KCCccJJeyh7Cnqc1+Hzh4QS5niuhVSNEQRF6bysYRHb\n0gAOUqBFPw7BbelzEdQtdKQ51zdx/83a5ah1EN740FctHr6y69unOhsLti4c\nv8T1Mg6TU1pxZxIzlBKXeXnheQqcsuiL3eWa9zwMVrxTXoijaz5YN6RzhYwT\n5o10LhkD5n4kmq+b3hGAYL50ChisUAaJakPHQSyqVhAtXH6bAMEksFE8QsmO\novdsk+9831adaOjl5MehCeY3MifsWoc2/P5MGHc+A9oknxjA9wl+msJNDdJw\nIsML\r\n=aIHM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.6.0": {"name": "@cloudbase/manager-node", "version": "3.6.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "31152af792bb1786ec499c698085f06a80ce7e8c", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.6.0.tgz", "fileCount": 136, "integrity": "sha512-J<PERSON>+MZLz11Lr2foR8OcBpfv/kAezMwsxKv1lGH49dgQb2udWUZc6oM1mlOAHO2RBP5NUoVl9wImobVUpEtcdXKg==", "signatures": [{"sig": "MEUCIQDFTPptspWlqCdzpuf2ongn/T3sWBnkM1mvVsBQ74ov3AIgUwuD/pL/s83JNGPPTJzoVW022dBBxpB4Vz5Pdro/88Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNOpjCRA9TVsSAnZWagAAn5QQAIJhccl5A6+ptTv/e1na\nY+2dZgzkyezk5Ac2xnKsL9QVInU8PnAbF9S1zmIdH4TZ9Gf88GPfrQtI4blc\n7fbDkIjqxWxNEiPvgYzxHApRnyd7PZZwKxeVgDGxaYS+/P4fpkCe2bU9IZsY\nU+zYAzqGOY16Z3xDJ9UEE0ENdWrBdoyilPpZynL4wvgh4m/FroxJMkhQFpVs\n4+ZpPCgAGlwRqmtNnAQDA3ZaRO0TeDEACY8+tafdQr9Q4k1KBqGsQ9ElA7hn\nD+ZxPk7gBY5WKCVnzL/7B2dI18maR3Qr5/0YxfmPKp4H0HA2gJ7DTyJZpy1d\nlmspVmbOa3ro8iQ3jqd1dXa1/aayI3uXdu53eZeegcOPZNlgwRHCMneBL+bO\n8nFO/mpImwdc4/m8OzAZX5ptr+emkEdY8biEwX/fwiRgUkIEzS9OXxBE58cb\n3AvovxnqMMzJ5+d2EXV/iIRa96V4EoKYuddB98KKlIfcCkLye0imP+u1RU6Q\npLwkPv4y42Y89Y6pvisQiPH0nuGHE0XXGgfzKazpm+63r6S/8f41QCilhNKY\nNj8d2AIXi0/jAXOowi5Z4It1zw+/NZNHoG+igg2n0LFRJDOn5VfUosEls8fW\nFTqN/42K5cEVCDvLXHGF80A1GtUN9EEYMxMzp83Xdd7jkNLK6IO+rLXsBK+G\notfL\r\n=tN3X\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.7.0": {"name": "@cloudbase/manager-node", "version": "3.7.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "f00ea264cb5fa1fd978480794a40bb9b29d85892", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.7.0.tgz", "fileCount": 136, "integrity": "sha512-aM7wTMcG7v/VKOCcN8eptuuTez9ustCtIygMN5+0gDSi3clBHdpDD+bMn1sFpZ5IEvURoEEb6EVr9OZrBb1dqA==", "signatures": [{"sig": "MEUCIQD5diya6+R58/lKUuxN7UwytdsuQzw4qRLEuMobbwtepgIgU+mVSZgUa8ynI8IV2nmyt1Jya1BJfQ2n+6fi4kGKhaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfO3o9CRA9TVsSAnZWagAAU2UP/0JMo8rsLwZabywHqjTV\nEwWy0izvA1PgSHupZrLJIcPOwk8ee4hoeyH8PFLR9YMOY3PzDnOPjXHMLcLi\n+9oy3nMv5ECwcGorRYFsGAe+eYONalePx7SlFWjBR0mdjQs//xCA7kJy5Vn/\naSjFSzX8OHLQ3JrNg5MY1YglWkBSiHVHAcYSyAlOOOVP9hvkuVFXzpK+9EdE\nVigfSKhG8yEkEVmt/zl6Wd853FnQV7tfa4NB2jzjiT1+cCCnyrGI9Y81SWLV\nSRW/Ozh0j6jUtFBllYR8t/qP2puQmTrNQBTXNFcWnjPmrFD5E3mSi5jG0KnH\nZS2F5HvVsivOB8wJXc43GainlfrrL7quD3p/77wQC48qRSfL6Oox6ddw1BdQ\nSNXCMP4b4Ii2OcRRo/zqxRaM6NQ37c8KoGyt+/J2XET6i4OH8AhS1iDX8A5j\nVwmdSu0odAaehrRPuvW7Clqw/KQ4cZZBXQS2xR8VPDqE01wTRoHJ3HjUJbxb\nueVOF6cXDmdfqu9lQftOOp7f8tOzJnv0OosfbGZQm71T7rn2UqLUuXBGGBn0\njTy7JTPXtwUF2gONUxd5L+5ZlR8LfxIFwfIFyRmZ1/7lOz0KcVdbbbMLrDC3\nQfyaYkFddP6sqWZXgX6poRe+sLQG0Dmx0qZmYZsB512o+2IxyVT8ohPCEQGX\nSYEx\r\n=KNwT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.7.1": {"name": "@cloudbase/manager-node", "version": "3.7.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "8d6f786f05a08c50962369f4163fe92e05edfb6a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.7.1.tgz", "fileCount": 136, "integrity": "sha512-5PK1zxSPiVAfnWXU1MKGqWtnyNNOa5QQV8+nRpsmy/Z4GaL2MdOLifF59tVM3fR+N3SkQaZ6fMqbAQhDsYupmA==", "signatures": [{"sig": "MEUCIQDGmJ5Iod491KVnV3/9fCXiYF5BPaD4yogY258sPnpe1gIgGDd/kT0AhGw4HtWV19PKr6ufffF7crXja3aCZZvUQrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 606890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPRmiCRA9TVsSAnZWagAAKWsP/Rv3jsZg6OHU/Sokqwd+\nbTTj5mJcD7MPxfhz8QSpNFwDzjvfQc4ROLSypiLgcTJM29Wsv1ujQxmWgfGs\nsBFdhAaRHS7mFwsE1U2lzItiOjC8uiwyMAOdekPAimmIHmahGAtWGP2t3fPc\n41OkvO+ZR0QmHG4hafF8C/MtrkaP2ILKKuKBCoKQOvY7eS+m2NsxC+EnV95r\nFu2HgoeNpWPmuhZYo3qXF7ZnFXqRdVjw9KvRGKvG+VjWh3PwbvPkj3YYRN99\noBaDEFlrljr4mthztZH0z4T4LIYkeEUb1xJqgkTx6ZwoGkrkFCRxBV0m8k51\nG3e3ptSk6/X7MusL+Rq9XSmjFRrwpSd5UWNN9ixKsNtV58BOhi+hU22X8xMU\nbcZ3oL0n7C6Q/JPU2pMRfTkKL9N3nc1xn6U2oKNpVsbo30V4/CYF/IdkZBx5\nEQ18585wVReKub2aJyPPctcyJ1gdjzR+3nInmqgeLvjp0I0aaveNzNMwx2+H\n8kr6BxhU1d3h3uwMERs1LPLgVfi0ReFwN2TSz0QGQpiRjTjuzO4XCDj4S7jk\nQoAYTOZ3Wj2laUpBEjVgxM6B6rOBODMBFXNJfdcPvLSvwz45c/ABRdmX4m5n\n9cwgDDfqeUMuSs6TpCE/5fctTzB5Rac07SGBxXudUPfnYiTUs+6JmEpD4ZOP\nEaPl\r\n=IwZm\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.7.2": {"name": "@cloudbase/manager-node", "version": "3.7.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "55313124dd40f2d97040a808277221b689950135", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.7.2.tgz", "fileCount": 136, "integrity": "sha512-bhG4GbBwwCpQeqwDNvOjdsdYPEEKKHzLJa/PpHCwweO0TMqVCs70jjAB9VEJIbMlucEGkZaZNu8zeZvrMhhqzA==", "signatures": [{"sig": "MEYCIQC3ynS+hC5R/hZDvgYGJipS44mwrPtKTE3NsVbjcfruGwIhAO3O41KN5NvYva1Lucn4ZfKtrYFJkjj0Qep0sxQWAeqY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 607380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTwc1CRA9TVsSAnZWagAAIkIP/3RYd+xTyzZhunotiox0\n1YdYSAcU1bklVQVsK+JyxsJYuQXm4eklNLFn0VninWqpmjVjGlOHcgR9xSTq\nERfhjlXba+Knxmod8uttfmUeFVbz7PqGJkCbMEOiMxjgwOJ8kLVEGyqQD26C\nZnAnZ7TeynNk2uVZTBwg8Ket2qC6csqflryB/UF/0/6yp9o0Ocgq/pJNYo/b\nDR/axgxCeQGi6FQlhrffwIJ0KvfOyQSBYRuArdNZVo44rAgTipNDl3W0vqMA\nLpwMeXf/yDMxjUrjy3OXN9WsYjv5WT9IrRN4C9EKR5iPpvcTfFDmaMDxL9Hc\nqJFzkYkFLZys2CtA7Q4Sr5NkRc3hM4XvPRnzXJ105KqEZyP39YCpQmeYyuFC\nDkvg/xUqfqJrTRSCbISXS1lsRj2uQ5buqfczxKMLo2btNKs12J7SIoIQoqCf\n1JCH6Tz2NTq2gWkNYYZj3yuuphhNY9lWAD+2G4RX0e24uNNVek7oQ+zMlNsj\nPCmeuz5Kc6/KoDwtQR6iqstaYsSa0Hsdz/ObFRbnwoiSpNiIlY03LjLxuZQK\n1mMHvQ6Qfm6SQL6mk6ljxyfFvY04PIilpj4o7JrzUvOGj8eZ39E5kR0PiDzy\nWJ+sE0h81rmY4fv0G/RNILWjgqd3Qi1hdQt8cZktDtemBEBs9fN9bSYjK4dQ\ncChr\r\n=yCpk\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.0-beta": {"name": "@cloudbase/manager-node", "version": "3.8.0-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "8d6de56bf454af170a3b37b539936c29d53635d6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.0-beta.tgz", "fileCount": 136, "integrity": "sha512-QXKhsnQZun6rql8yMDq+TnKqJLZAeQRKNRtgCF80GfU56WUu3/5LxtDIpePK8IK/PHCaH+KxcZ8CJe35gcLVZQ==", "signatures": [{"sig": "MEQCIFbvnwhrDjqZKdnMTBX55oOaqnPMcXZ7hL1YSt+vAl2BAiBgTcUgwLxB0VziBmt8h9v9KiY6GBOXF8Jeaf6dPSgG/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcdjcCRA9TVsSAnZWagAAFpAP/0bPRCivcOUrjG2hi/fH\nndIRrzqqUZtMoLvbo1m0jU2KrQW59VSa+SHTrZOpEldmf9pTB5kIwmEEXIjZ\nmD+HjDWmuEAg2cdipXR+IwI6yTdCvVQwU+SO7qt3/+mYvY1tebeHLJEMDNT8\nc+Nmo/F7e8Qih+HHsg6E8JsmRhyGmY0GZx23D0zV+YIKvEgybLlwCBVoYmiI\n1bS4ajOYPXqwJYDhGHYS6Mfxv8BhRd4fDhwqOdDt26IJjxI6MvGhuPBwbEhc\nMMDzcvl1+IFk7sb0KPjXzIrXHeUSq3fekQXhW5D9eHAU3IyhHseBYdZY4P7I\n3gl2vdjdYyP8JJj8ymelnpx1c4UmYzHOgOCaKFKuR6BbSMK//yvMiFsH62Gs\ngwBgZyUPvrKeOZ3YCV1/Cf5zF6y5PZst75kvi/ymYBxAx7wiRo896m+9Qe6/\nVtQ4KGmLhGdYDnP84ci+DnqxcSnQ9p7l/Zpt0UvIugWgTz/+QA4zj6zgotcF\nM+xGKAdVPE6QIjt/nsBYslvYZGUWm+MzToxsmlO+8aYg3KmJyFAuqGLJnId9\nWvWutB+t6WRB2DgaV3NsMmKUV4vqZpu7VfilHwRPsmjopimUtWKGk1Icpc5e\nJkV5924wA14S1T2IlFW/O4Cw1YtZ4PTLtETvTx4mP/KzbeQ+dNsG+mqNuaTR\nuR5H\r\n=95ao\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.7.3": {"name": "@cloudbase/manager-node", "version": "3.7.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "68cc70fdba6d6e3ba04f46cf2192b5550edf11a6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.7.3.tgz", "fileCount": 134, "integrity": "sha512-gTrkyvftCPYzXT/MW7kR9K/uc14DZW+/W25PgqYuypL+zl4TT7/CMPVspxLVCip8jEUu7rfW//COEOcKSkPpXw==", "signatures": [{"sig": "MEYCIQDygrBkMB4hcaSoGu+uI7fS4+h4gv7Vz2u9uYl5LX7ofQIhAOt3thAp/dtgRcHmjwXJ0XiAgGndLlI21SbznwCltjSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgoKjCRA9TVsSAnZWagAAUOkP/jEbiTXlzW9JoBCLnqMZ\nunsD7ST22w1+v8xZnYqR+gViG0TDzBlG43Jeont0SRbjcVWKGz76F3xRoMST\nm59LHObkdoQqT0ZJ4O5EKToH6Ixx6CCuSTyrGFjniTP7gO6rY7mKZagjTqTq\nT2tzLJajKfZi+B1KQmUhKhz/aHCoBxfXDe+HZlviQH24qYT8BwGE22JRiONl\nAGs24Wl/nfnHtw3pfn8NVJpKmw8pho8QVnRtCTmz8ADbzKRKkwODbMgWbglY\n4pb63Dy9PcSxeI0VPfNH0Sj9WegGFVQiq1Ud+DnWx2n4za98+U8lz+Zmja3u\niw9Go4Lyml8uQHCLRy7DcX0oBfOrKtm4xVLqwTFEAwavbkOnbsGOst3lt1IJ\nzn17FiDj19opaYbF7OZDWonqamH1trtfXh30EEGWxfC4FZBvezABVHLi1tgb\nWCpw5dPxK2AK9D83IMefccRe1xFmf646riGNK0twMTU6EpjfqQaj0axYBDzP\nmDfdQO+OXd1rnYkVnRcECcqLopdHWJg0FpQIVsRwEo8DTfjyhnOvyh7LrB6G\n2YML7Tmn9tZhigDAp/k5Z2cQfSX0MucCvuXi4HJDjZD4W/hSUa+oiZ5abquP\nMSb+ISnB+JD43ONQ9KUUrPRog5XW+lzKfZhhXZGlZscaG1rImEml3+qpspEL\nDyEs\r\n=5zpA\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.0": {"name": "@cloudbase/manager-node", "version": "3.8.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "677768ffbfa871b7746ae55113564e7340ecf979", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.0.tgz", "fileCount": 136, "integrity": "sha512-3tm7+PrH4xrA6ZqYsISZdT+H2gQIUZRugI3EIiS3tJVwNk+ukam74W6IREOKkzrmq+ib1081upoAXHF1bCufMQ==", "signatures": [{"sig": "MEQCIFX+IbBf43LTuApVFBy34e7pgVrXi6TTG016TxO09PEDAiBl9QG02Gc4X5cDIiiDSCiJrdcgTCPInx5NBKSx3wHN3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhRedCRA9TVsSAnZWagAAXzsQAJBqs8LXBgFsRoYwElpx\nuA+sK7F0Aev+V+4fGTvj2lYLLBlJsn7Sqb/YvF/0mpmXDKG/ca9OAsAHYfKP\nIiFUaY+suBbU2A6ywcXyJ1F9L4m2AFVjUZ/WGGfPP+bE7gmuYkBy/eQAcRgW\nHkYxfxaLPyL1NvooenSnRcmheguvSX1W/kRdhTJb8OWRhNWb2KdAgZpijWLB\nFTzU211dS9z50CtLJ6sIF/kcInJ1z6tBTX7XfzlTj5A3bEqSPbcoZdaq5AXO\n6dRUaKDTRUBB//BTgmROqp1Dq+7NQviG59PC2muhSgK5NAGwt0OAzWtfnuuc\nwlkka4eadfe6DMew6loT7fgF5TPtJJZ8xvjn/5Yu/EVY3R3ye+8CPfyRXRpM\nIHqMODEZmia1/B3PT2ujaB8qVbP81qzwFhjTlfN5gXo8fkvJjmEtHiHeKFYr\nNwHqCjpcA6urb+X8Rd2XrglQzLzv5bkC0JbaZFP6KkL6SPc0RxMUBtzopiSc\n3bwft0/q2eQUD/tHq4OVeScM1BsMDY5SUo7mnXrvlV4W7zHUEctVSGbb0xt0\nyvUbv/6wk6ee0y1B0nzJ5+Ggx+NeuF1j7sktUt29kBVPWiwbzfBdwRkv4mUl\njd9oZg1tka6tYxsex5017hLnmSWQSnwVH3fP3nFbgZTjJOmnC6MP+iPKe1R2\nhLI4\r\n=IYnn\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.1-beta": {"name": "@cloudbase/manager-node", "version": "3.8.1-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "7fd3bd317c073a33d2a02696f3228106cbdce9ed", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.1-beta.tgz", "fileCount": 136, "integrity": "sha512-+9DxgfvCHRN+yiUjFO1+yZAmht2W9XRWf/knDHzYkDa1COY3AFvZ9VXMDsrGn1OEPrfVlTQhlQLFtpFAMannjA==", "signatures": [{"sig": "MEYCIQCYHK0l9FdSgSbS2FY2sATm8z/wyfckjrWWmd9rlxal9gIhAOXCdPZmrHsft1p22hFsLgpWSumpswo/iXF7sHrDMzEd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 446064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiEwqCRA9TVsSAnZWagAAUfAP/i2P+FOu/uutCi9b6LrX\nOykojXqL3I84GAgT419BNQkjGkFj7+xo99Vn8NHWt/jsnmDwQClvqJBlG4EU\ncZAT/Dajh9JI7rewwHoV9Z0h7hXqxh3SDz1UryPaV7VeeeNIMKQvXOGLX57l\nV5BRHHdBaSbx0WWLzW9/bNWxYHLKwXjx3ADO04Ya3Awp+c7g3UtiSsQaC5Oa\n7Sp7lN/Ej89hL9gVfGPXyYSkMyTKKhWsioMMPj2olrcHbCJevMBst6rkz8Wt\n48yAGlEQ/22gUGaSxGTG8+PVJjI+AiOQX9ATWa4rGfmOWJafvinOsnO+6tLO\nhwJRytvNmwKLEy/rLa7GVYAb0olFBkIr4odWUNsV3b636Ld8v30OsEm2AcaD\nbmo4/rl4+eAyphOmQ3KTHBeZmPlQkkWV+pChAkTS0jJWjgz74d3PMvF1UYGb\nVIJhTzvrGlY6LAxcBGoAXe3ICXp42Ay3j+eHnfYyXXG0pPWc+1Jv2eniBcs8\nk6u9pK6+vJo1AY41rgbD+imLQCsTFBKf6m8Akrn739jTgnnN1pEQ6TfZ0a11\neF/TbYdTQcVCWnTevgl1QrPngY02bx6sFunx9YCMT0kQTptGnsG52aNdsHBK\nyJPALDbP3bye/32TxSzbHtUUf1KGarXGwEQNQGC4wjb1gRR4kKwib9YseDl6\n7agW\r\n=oMRK\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.1": {"name": "@cloudbase/manager-node", "version": "3.8.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bac396da8e811b1c974858e6265da77eb623eecc", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.1.tgz", "fileCount": 136, "integrity": "sha512-QTwHaGZzRlewPIDHCXUXP01jdceGgKcClh77xBnRDYUurCCQpm8pcSHDuJ2CfnpIVSTTl6ne5cDITp/5x/lpvA==", "signatures": [{"sig": "MEQCIE91Gob5kqA+vDJLz6Ouwcq9XoPeDalNnQQcXh0kqEn2AiAgME8cYbZuZj5PPiBDdGmmgtUsRjlSI/0RuaIgU2pTjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 447781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjQSBCRA9TVsSAnZWagAA9PsP/08cRDqgMzCoeJzcHvMS\ngshsHv7E789995V4PaF+5cyszkC/lVxGn9c8PIKVYT4/x/1tV9wGwqLkuMcG\n5pO9+2Y/175DWU4z8Dtn8YlSOBQk6yPW0vClPKIiKerCCqfgC2MyBgAo3FuV\nIHy4oOqAX6+OlxoPqVbCOGq81QW/Hc66Uoz+qzCif6GxFodl6nxh8L+ELxbM\nwgdl4OeDNUWPcuQb5r/F2biJkh8RjsHZUzcWAADYEME39C0PsLSevkqO/Gcg\ntlXJ72PenfgWchnBF1a9gO+1QIGaKSSZktKf9IygYun8jBJHBabzzxyV2SjU\nAgnBBR+Z5BabjUW3iC9Jxkw51Ks5TFHrcxI97yNu+ZQ/wgdv8zFSjCgJM3bY\n8c1XZdYFhE6sXx5vPLXzMUtRkJqLiab0HxFEAJQ2V/Dv4aRX3+xI0uuoZQZU\n5raxRU1y+tqGjoLcsZhmmezS8X8ExoncvCjw0/NN0q92WLxd053fA0h82jmN\njuDY6fOMNC/bryOneqgzk3p/7hYXFi/pvHBi9ByrSxYmtHBvbtNLY1f9H1T5\nTyp6r2zwP6idA/tKsPvhuKocgh6OBGTwrCd/T52uz19Fszj9LblxMOt7LZ6n\neWo/arA9wR1tchHLev85oi7cWMresBlUVq0mOwTkNsbA2L1s6V/tfv8AT+UX\nILRc\r\n=6LF7\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.2": {"name": "@cloudbase/manager-node", "version": "3.8.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "13c30018672feaaea0b6a129e4473872f5a44427", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.2.tgz", "fileCount": 136, "integrity": "sha512-oqhI632yASz91DtklB2mO4hdgrC6ylz+1pHeWQzP4+bpuJmS/uDpLiwnNmcEMcJCknvTqFQ1h9rMHv5hbniJKg==", "signatures": [{"sig": "MEQCIFTYKfj5RFGeqymvIh54hmPXp4l8hvpBKivPPDsHMlXtAiAdqYKmxKa0JkU9lyVq+rTsC7ycQpMvCmQsGNQYbmkuLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 614614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1uFJCRA9TVsSAnZWagAASiYP/25HlesqGfNviRqzwmlE\nVNj/yVfiY7sfNn4IDEgH63IZJ03xcs8U9gtgEaxDf+8QqYipLWwLCXfwU/Xl\nleQhwGQGG8KmBXjXFZQef2ij4IMcNJqz8z94g9khvC4PuyISb5d3sYTwRRF5\niitAsj2Q2QrzlEjBXbx7Q6u/q4QjsYuvkDT5iPEMQYACquuctIvBsHkGi4wZ\n7eT0DxC+m/Rwn6qOZIVD0vyxGExJInAhPG0Ux9HslGmANYwN7HXaZOxDX2b/\njbICIu2mYOMz/s9ph8zXNYn7aXhTjaHgby0MNB6mlNucXr2qI20cupQdvy9J\nbUHOG5dBk5Y6yizoGMY9KQsqglQsqUHC68DHNJm2RDmcqRuBR1rIRsy/cRFv\nlHhFCXqZ6EsreJSojReyuzbhNJgZMJWf4fmxzEMfVtLSdZow1rMieaBWPamM\nA21ulmMbdBWK+g5JW4xayAYOAQO2JcCZOEHU0r7kwSDujttuNlTspO2QROgZ\nK7REYF6ToShSNhrqwQsehjimv5piY6VI5+tETm7JdQg2J3Y3m/jAwtc3cf0R\nGDDJR9NT5fq3SDnWqKwDDM92udVIAAHk/lousXhLE8Vtmls93jCzTGXsTDvs\n5esJBjJdxjKb2AEyxTprOqx+XmfPdc+dcvW11y+lnnwLyOOVxrFrEBA0tvfG\nAJq/\r\n=XjTg\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.5.20", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.3": {"name": "@cloudbase/manager-node", "version": "3.8.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "27abd374df420b281d572dc72942bff86c67b54a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.3.tgz", "fileCount": 136, "integrity": "sha512-0FxKCaB7S1Hhq8R4s/uv79uQC+hvYFWoP2xaQO8DLZ8nx5+RuPGG2d/3TumBDwLhUEw6Emn4TZu5P0OEVTFT4w==", "signatures": [{"sig": "MEUCIQCh+YcrZ5/f+7ZMvpaUPECl4VPL/6vGgCgyBiEH6BtEXgIgMcB8r9PF1QEmn0kll/NoUIG6CEEUKF4wDu4D5ZqEsMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 612993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCjcXCRA9TVsSAnZWagAA0zwP/0xf2/wbY3liWoCzcBsz\nBbyanZ7Y1qE0iSTkDeZkhYEYmRwKZRiySdPdJnuUNgOK6+8bDTS0PO7oe3gF\nltzX5/GdIdyDzQHxTQAa/vL0JI2jB34vaE0SjQIvbD54InBUvnB8esSEQeHN\nmYXaZfjyxSxLN2IPsgd4yLLAieQlyGKrXCN9W+0LViYaZIRbcJUKNpZfG3tW\nnj834F2kdurcQiWAd7C1IXPIuWKZHiLbQQ4Ua2sSbL2ZIUFA07WX9DZVGcqO\nQuV9benNSPFyquIS18Gf4yELbRpfAXU8urM9qndt6LMw+eCqubGwzSMmTHMQ\nPfEo8KURaVeuDuJeBMicYUtMBsgvx/m8DDLyC6lEY2rPFA/j62+hRMQ9wuEN\nAbtUu1sCXk0XTfWBBni502o1Vnz1GFwuZpYnRmkD53MRpsqQr5+LuMvv1DNG\nJIFLdONksmBLd+IxSsjyb67Z2EaFHvkwuRTcVRw3nMICoqjS9+qPKrJ2Oab8\nh1LKdFeWJT3AetGOyx86h7WukljErnytaJJEY3wQ9UEO67XLFmQ40BgzEw3/\n9eNvFMO3fMdYVQ/1+c8oE7itAuglm0+LGNlwNrDBfY5bBnZT0w76bABEj9wy\nVHaCyB6M/mRl+CB5yBGOvYR1fzKUOPQg6fJZeYrMNj81uA0M3KjIVmHwvN7/\nyVRZ\r\n=iudc\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.0": {"name": "@cloudbase/manager-node", "version": "3.9.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "f06a1751d2ee0980615a1427776a6f655c202c46", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.0.tgz", "fileCount": 136, "integrity": "sha512-qFYcFIYZ8Nf5Q705MfG4OgTUaQiU+ooI5Mu+CVS5ehJhhGb/4mmRR+UJsRZUmxME6PmmfgDFCKeA5U03XKbhbw==", "signatures": [{"sig": "MEUCIQDdLhEJGTjEswaGrdd0pmimuV1Ksnt7q5tl6l4CbNga/wIgJXF5tM9MxAhyyqt8yWVujmqlNFN3lo0bWSRwCOXTzls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 622266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgD9S5CRA9TVsSAnZWagAAuqoP/3/Fbjm4LfsX4q8xjrfi\nePVcxv6afSGNbT+P/1Iq2M+KZtB/btLHBLUPOjy7oxjM6wPW5MuN+wtSXqjQ\nZ4V3bhiWWlzf6SbAJtq9kUida2IJ33hGcxpU28+2/EVGr6n25IhGLz6CFa9Z\nA6VL/ipm1zHjioRsy9d4VVaELd9SMAzeWpCJaxAcMx5OtQtoYnNFbWTfOe/j\nTYL2vJ96uFwDo9yUqfzfGm9HChHV6gW9vRq0syB6SBwDAwdDLVXR2sRGalWr\nCbPvpvfpF3EgzI/vYyAymT9H0cqSegnAHF9hJzeYlgNEF4E20yaQhOFm6AkI\nOU2PRpbOlmmrosIvMfaGbC14Vrrh8ZlpRGpjcniDFYaDzQwg2U+6TQdN7155\nBVVc5MxBQLRY3ygRKtCkZOcAopN1dlO1rDSBGruUpMh01Z8h5xC68S2JKbOJ\nbUEaZP5jXxfokbu47VIcFSVj4SOl366z6jRflbCyBzxXXrVRkUMMAkyyZPSV\nVVEW3Dj5qCGYn0pNFvSDtoQrJgLT9j/q3jqmjbHxBkK1548UxOQPyNclDV2/\nXJBfzRqUY6WrPjiBFnV4FfDmvCopxrV5t3Te+QfUXk8CvKYnGlTZsY22R6wK\nVQvCG61dqlXsmNR6grKuMI0+Xd5Ur1lxncmjxq1/vi7brs9fC25oriWkRvqD\nuIFs\r\n=PQq9\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.4": {"name": "@cloudbase/manager-node", "version": "3.8.4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "be0dfb19ca14c25f0fd0cbc67efe419876286db1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.4.tgz", "fileCount": 138, "integrity": "sha512-iLHcoqCV1mMbanf7wNaRRSz6dYXib4AXTjB18jXaTC6cVN0xuIuFJ2FbTEqdW6DZUANcyuUEuk2IDQytorNMVg==", "signatures": [{"sig": "MEQCIHDXh7NzUttKznJIXghne5HX5n1rzCrH0rwUhm2i+zU3AiAdaG+0YLrUcoAfgB52KvgSVyUo16ljjOgmy1MbbUFhfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 462144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdA0jCRA9TVsSAnZWagAA1fgP/1s/fI6fXHYu9+Kfqc5e\neRUIDUWdKP7Ic8QepDZeuln299jC6PGEUw+cHmOFx5yRB3YF9pZitSf/IQIB\nAEGmKb5dQEhpky07Wgopi5b2K6tuV0qSpJL0Wz5uvGQmwaRbmW+qGGxwUGue\nkgJbrh2poKTOevY73p/KV9QBOlAbE9aVpZgRXoTx9R3CRkERn5YrLDNHVl9W\nsa6/rbbe+QdK2jFVgedOkue9aRfV271zM664eA5nshCWuZPCHcx1LeqY+64D\nrarD9/9eD7olIZqY1Wb8Ih4Aq6+w2XdbWjhKiQvrCe4FDou398hkguFMOSUR\nK/Q9zvL+owlQfM0mBOjVUmpIpw4g8MZncJiQ6zJw6nFub7/fPj6IVJ2jrp8Z\nxCf9XvSNd94H7LnqXZANjMWdIjxtuOF8WF+dLmvKHqksmrvj3zr5ru0loDCx\nRHBO0QVjyV/YV5PpZtbpRfYvHROe2FOySuyH2DkBBwFGPPZtmBMQFvhO7wtU\nj2O6v6Xnk1Wk4Vpm860vBD/GwtrNidu0RstZ9+tU4o+n/XqsX2GVFB8TBplq\nZb8IJ5jRUtbGfQNaZlISfBSV94NbY6IYSgY4o0Dg2Big+rPSo6FXQ3B9nog1\nkjE603w4WeSgmCxU14qyn99ObWK8ZZ7mXE0e2drLO/msUutLVF6M/eqiUNHc\nf2i/\r\n=uoZ3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.5": {"name": "@cloudbase/manager-node", "version": "3.8.5", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "596952cb6402ff73f502bcaaa5e0e05f173ddc4f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.5.tgz", "fileCount": 138, "integrity": "sha512-iWKMaLmSD8Ek80bPQXnXsIv8OTgzuWN51yOf1jB4J99f2d6SFfQJ/D7s64mDtDFBX6sYzuSbTpYTs6JxXgcLgQ==", "signatures": [{"sig": "MEUCIEfqdSugPSEJL1cdOECYULVe7LqSlWuZNLyKXhicxmqoAiEAuayXiDO0+67M0XneOcZgRqWw1rsv8O0IYrwlJxUMHmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf8v8CRA9TVsSAnZWagAAllsP/0XRsp0P3sKnZgvcIk2i\nkzTlVIaHPzRFLws31UTFbts/iQWmI1OU2EEc+Jl8TwFSvDVSmf/Gu0cEhIgm\n6YpqJs5NvIp0fPBTRiL3FCoGXfWnOcPlTdYI82nKnecBVEoMZqXu71Cp/r0l\nSviFQn581LZGMema/UkSKkJE54wAQoE1uIZe1RbOLtCBYH3w4y5eJECCg7vc\nDfic5ZUT2bXDWGRIePDPs0drpAZMFC32s7S37k0gWddlgyoWSK4VLcl+GTdp\nhIdoGah+voXH0hT7MzfbH2mgatvd/Y32LMNQ037C43v5wUAGL6B6nTzGTQ/T\n8x4KJyKrd8HXIa6xBiQS88j+tKwDVevzkQ/iVdCZRb5dPsAaEMw5zgvCOoKJ\n1IKHKSxJtq+uR0I06sAnICh1ZZBgFGVH9Ef/NAF0/FPV4NWiXSYzN/24ttpg\n/yXepAkrIXEcf3p37UruMaAAJ/5I8TNCOmsGtxFK8TlVfSXrWI+oxlFzK41p\nc5df2a8iZyceKV5z9A51dHuOQibaH8/ZvcxCjLiLXbaYJ6W1Lg8ePddsCQyl\n1k2Lbd5n66yI9yexdJHP9tii9x/EW1x8y2tVLFn2HQtRj/EpdJhnXxjT1qnq\ncb8lczngY7ns5SFMkdqFu+/95ZHZIanoGMGxhkYhs7Dnuz/hFJp2RwjVQTmt\naY/D\r\n=Zr+/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.8.6": {"name": "@cloudbase/manager-node", "version": "3.8.6", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5f714c927c2bec263579c62e469e7442186fd555", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.8.6.tgz", "fileCount": 138, "integrity": "sha512-ELgXHdl3d+xSqaqtDbGBoI4eYMX5CLvcA0V+81LCp69HlNFUSNAnZjJVS3GXOr3hrtUs5AsRfBixm2sfzgn6Pg==", "signatures": [{"sig": "MEUCIQC2n2a8PsMUZsc2QcZeuNXGfFbP2h4tkb9hEVdftMPdLgIgC+StVyUBOGuH6BpfadlW4NqP83QyukBU+BgQk1AdvUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf89TCRA9TVsSAnZWagAAbA8P/1S+vGUKO5Du7WBf1ZB1\nqyM7ZPnGUrbG/HA4/lrZYb1cn8n/zBsmeueSVolnSKf5/x4UkPTUbw9ReS88\nvFZpEstHnLkX2C1FHzn704HUrtnOOTssV1Nrr8uTqwFexy1bUCf13irgNzra\n8H/PZLg/GtxL6R8NVdNOcjwmbATYtmImBeaawQwVqIX6gnzEser7xxPEORhu\n91bx/iBpdujjwkMRuCcSYj5CwlBHAMWLLWsVQ7HgXtOTGX9/Iinvy09Idzf9\n98c1OihWhRtefekNeMc5zTmTZRzj5hrdwAFlwVl2QNvMrprg0J9uQhPgZ0GW\nULWuIk8DOySCc/y4RXPcwkBXlJ6Twr5Rtct5LazuaRx0W4N1695LTHpAPxAF\nsZWlkJ713khtr6074xTfO7HBbqkV3g5sSDl6UaHPEakBbxUXuih/AIznKahj\nwSG3B0MyN09pR8O6p5k6TBDN9HEl4FZc5WUIumLNJf96Nzag5ET2hkvYm/U7\na7Za2ZqxAo7m+ZqxpfJSjLrsYe0vWySoDjZLwwilTOvuaSzD/BMLJ21M3TiY\n6o/gwTK/da3NrJGgYW9GqttdFJByA+UlHoXgJD94peyNt8+0dvoMxJt/Lzs9\nAbLPLo749G6wUfhfpJagDLLBQnu750pvT7lb2YI/c34Orgvdb3xE6cQZ271L\n37TA\r\n=jf6s\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.1": {"name": "@cloudbase/manager-node", "version": "3.9.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "51a198416369f12334f349a2352d1234fc70f0b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.1.tgz", "fileCount": 138, "integrity": "sha512-zEIGDuMphF6rPMZprc7yaVenF8rclZUcjh/sIssDccGhxRZCQiJO/9enuWNPP/QNhDN7flGAG1pUKnprXyP3nA==", "signatures": [{"sig": "MEYCIQDEVIopsceuzMNWgMnmIlX1q6rpXnKpOo6FJ21QQc+jWAIhAPoMCS2bROgQhfHkctDC0ec3SrS9+WF6GA/8zd0Thn8M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggT80CRA9TVsSAnZWagAA3dcP/jv66+RqVJPKEZ+Be1xX\n7KzkcSOcPfeRKnWQc7Ynawclcgk1sEJQB+YEUBaXbzPSPsmf4V8LwAFYnCmh\nd4doK3qxh7FIeDly2fZstIbY7EFms8i+sK9WPEIOY8zpQawnJlyC5CU3cfyq\njYpL3pD2XchlyPxZUJY5lZaps6lIBVWqCDLMwIGRUUashIXvOm9TQ8vB8qxv\n2T4EnyjaB79exW3bwTh70eLfiNG8AlB2m0g7/5usoOgo1IYoKvqboVw3ViZB\nlVsFGwBdck2WAwGGZL95B3hKID8vflbbn3EI5zLkV6v11u5wwgkKH1935PDR\nfPWsIt5/rPH7et0BvuA70kwYUGUy73w2ZJITJpnlaC1FRhCManZUAJ3vbf80\nzP7SrOQpILXvZU1qLqw7KRvshnGIe1STKq9DwYlHMtXkyTATBkR6s9DGNBxR\nQN6tRxr9r4207rZ/AMVp3+Ei8iORzjsH9tlujt7pVFXbt8i6bJ/Iz+wpqw3l\nereL0fRaeMROhEndLNGkuXQnjRCrAYR+hzV3t1g1JJPz40l7ZT/1BJ5Snd5I\n/5V+l8IEq0rYjYCp7Xd+nlO9gjI6g3ShGzYSKde1+Ta40s4zdKQi1En4JI6T\nUlaqkrdRJdZUcIx0pqkOM97jvo1GlrvFYUlvNEJGKY92CnrY8XNucJVkDEkn\nLiXY\r\n=ifYj\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.2-beta": {"name": "@cloudbase/manager-node", "version": "3.9.2-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "16fd090e7c5bc5506059764a852a67a82d76dfeb", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.2-beta.tgz", "fileCount": 138, "integrity": "sha512-Ke0FUWhIiv+THB0pTmDvmCZAtn8Ulabx6SaatEvj300m8tv0Z9VFRnVs5tHpYkREQbYlsgObTBLVmM5dEyW3lQ==", "signatures": [{"sig": "MEUCIQC30jocao5eTp8dMO3H2eE4DRbRvLdloFjh/hNjIUVPkwIgDzSjgSddjWIbzmWynBfDnzJaE3hmu8Zmp1D/35MuCMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwygICRA9TVsSAnZWagAAK3EP/jqYhe0vCkkXS5y1Sn9K\nsyvviPXS6l+Ku9vZcR97w3vazryH/wkn1EL5S793vMjK9O39fajNeim/BAdc\nF2B4DrzsWEBsdyWOXP1Az7QuTjHhQxk06DLfcZPCyBJb1UTT7uhfwDB8qWV8\nM3ZvaFp0Ezho3QIdAqZ18w7Uk8ljooowe84XPRo8CLXPrF/QJ+UlM0NbKtyf\nJS1QGr1fYAwabpix61UNNed2fQGO+LYbtM9L1gGgOFqV2W4ZfatcrJCpxeEC\nR/iOe63eyQQWcj/6F4ahGxMpeP3vawFQZJWgUOV0JX80+S9szpKuDdHb2ciy\nmmu8IpM08YR4LBbD2SIQL+adaLAPtH0m5AWeQF+XRSgolfpXmCJqjDT0nLQv\n9D6qEXYQz9CXYh8giTiK4JC2Yey0cgz7FSy+Sh8ZvFLWSuCzpmVl8MxF7vvo\nqBXDWIV/pdcum1Rakirb4jZQLzQ0IWS4neqS7ysJkRVXOfWqVarVJSS6VJO2\nB7luUMfUOsFAEGQ36UkVhfbn8e2Jr4nhSEU8V2tcAtv4KCuRuP1ELetAq0uk\nlQCPxq1TurwuJ2f/NoBM0rN+Ocjy6HebDo1e7TMYZq0eHFu1zLBy/8yWMvUY\nTix/mfxpCFKnfC0RF/F+fUq4+dsKVDuwP9dU8Ib2wWYUud9TFZvsa9etYxRh\nPjL0\r\n=daCB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.2": {"name": "@cloudbase/manager-node", "version": "3.9.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "34f94701f8afdd37d6f683ae7a8c18bad30d9b01", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.2.tgz", "fileCount": 138, "integrity": "sha512-63O7TLgR3FOMkWaN8po59hBrAz010NrHiU4h8waxo2FjTPh/ET6dSNvDgCLXzR4lOo4xG92scBu/s1P5gLumXQ==", "signatures": [{"sig": "MEQCIBj0FG7o3nzeSL2IHzuvuRmf5irbLQclca3S85vvJ5LEAiBwMjLCEHil/0ObPmwrBH1Vj3gFai9CqTcozUb4uM57XA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyB7mCRA9TVsSAnZWagAAa2gP/14vOzUhyHZZ9WDUN7pG\ncQ0Q/b5M0pLmvM1fJ1gwS/XfI+yaPykWSsFaycBQQWGEsLeaEm9DnxNT/wIQ\nPkOKY9vX8YJsfDk7v3HRhaeJyacil/wNBq0svfHysM/o9+UVtv067JSFiYWV\n8CYhfYaVHCZljcTvlTdkpQCX+MIbqpxytM2coIRZqZY9UU0Cn0Dz2oON3LhJ\nb1FdGFEjt2qGt+p2xV0U2GmmMz6VTwUsd3FMjSJpDtbCqjho2xpdLJMRuVLc\nLHjgTpPxD8qJUeXpNLs88NlR698DgVyToanmRIuXK8/1BABlX/ui16vLuXRb\nmnIT01FwbrQ29Xn9MSKmDTbQ+dCmnXOJdiw70Ea77f0cA5zHrz/sbbRDSP4e\nv32w1YKkesZY3c9aOpWKj5OzepHBZZ7K1zkDBheC2wNDocQIfzVgMTjDWaNt\n0n46ifW6lXcO6OAmaszgVGSS26AEfH138VIm4wLYnI0y3hczqu81fOOefd4Z\nd+/zAzTKdWqR1Qz4ZIiHkz268hVlKFiC8/ku/6YsrpE/LrQuXiHaKGgURfgn\nc1f3xcVydLcazBe7mLj+1V85zk6wEjjVFECUyKKzVo+/nuhf1iU6o92jACFc\n+jgukR+9uyodvXolNWe3RC8MLfKdSBZH4n1KkQfcdAaOjR0Us8hwjexTIcUm\ncuyh\r\n=CyVJ\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.3": {"name": "@cloudbase/manager-node", "version": "3.9.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "2f7ba324adfe286129cee50ceb1eb5f38fb4cd50", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.3.tgz", "fileCount": 134, "integrity": "sha512-xA4wUYKy0fiGowPBAgRF9gzJ3UxzSC/oSdRuIz+8LxGEGbJLrBLOhcsS0DyojxxbZhBXVNQMgDPMZLamWnEkkA==", "signatures": [{"sig": "MEUCIBefPDgCRHzJptAbmxcL2qOSQwN86xNwiwG8VycghH9IAiEAlLMA1L7KKkshYY9cEihgLnCCgJw7+hgG/M0506j68aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 450261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0V1OCRA9TVsSAnZWagAAs8sP/jyyWaRrP3iRbwuNLEBZ\nUNmrK+dEePUphKPWNF5FNM4pHGhqEhlCxZumf8QomK8cxVbmXRt0NuA8UbYV\n0drTxlKScRC1UU3ThESm/YfJKmogBeHw1zmll9w7VJF9XXg0f7gBSM3/wx0W\nB31BvK1DOJRciipejrcWMJJzt+RkZeV9HR59Hxz5NJxglKiHDfzSSm0Wy6PY\nmjKFL+HBsYXgebpt7PYwDqgN9OidFB8XAWFOTnNSo8OxvlN+j+doNj25f7Dz\nOFrvQG60d16INOoay9Lvc+yVze2wg935DDOgHqxysUPPqJ2NuRng49f1K8Gj\nLAOW72b063GJVRzyjlj3azF6ZSMFm418kJ7FuSMgl2DdIsOCtZAS4wexTgL5\nCZLcYVM5q4sqDftG5AzUg+4HL24u/M6y9uW/CMLPir5SkYH6OdHrbn9YyVbk\nhOpgTdx3uX9WXUB9ZylMYNs2de2IcNj1oLSO34r9g+BLe6q8VeAyuMSADSBJ\naOOAcJSmyklJUm8usv1HLoTmL/z+ytbB29brmJgjkj2EKymZX9RTwJD7JNwN\ncmwcMrNdO2p0g2GtPBab5qcfLUdihiFiYBlqXIbSErWMooFIENu4tY+na02P\nGlfnc63ZY35PBwQIEwNyfDPybKF6Rx3Dqd48YKiUsGWqPhG9Xyy7ZFti/RLr\n/JYK\r\n=aPbL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.4-beta": {"name": "@cloudbase/manager-node", "version": "3.9.4-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "2bbe0c2e514a4a1c4eadfe657dde6e4fbf6811d4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.4-beta.tgz", "fileCount": 136, "integrity": "sha512-R5H/YAkOGftwmu2qhjMirv9T4s3qaGt0JugeEbehX9P0kPM1ACaKiwP9Lry7RpSutI7DtC/jO30gYCXXMLNMBA==", "signatures": [{"sig": "MEQCIFjqEcdcd8L5Ibi51hdwe6gE9ZBSgNMHRTO9vGzd40K9AiAr/9u8h8u8fBMtTFki0MdNGl1kjAEd770av+AFZntrDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2tahCRA9TVsSAnZWagAAn0oP/0nyTULFIi0y+/bUrYcv\n9BP422pkOf4URpttpbtPfhHagdYf7wXg7YhnQT3RVGlr9ZfSbiOXlNFHtx+1\nfg7z0qQg7YyUcVKP10fohNvLb6WXeq8coAc/54MCRkvLfmR5YLekJnoWRxea\nEccntDTGhStxMma3viAEgXk8sbzD+vNywVbf4agZCa2ybDZjsBTOfqlrKyYs\nnlmEyQOWyyvmV8cSS+Jiiet/CPVfXV8wcTi0jtoP2ODo57pBVyFBrOfX26VK\nQuW7gl0Dcy04vQ3DjqNdc3Nlj12KDf5eBwrjSVV4bJWNyJ611sBFHheIT3nh\nJek44b/2Btx4UTFsrqzvzuyU9KQolSAOnnLYT29B2e/qg+2xuzPzCH1NSvVN\nZsoQVWcp0iJgbnX+OaSWCR8kk7UR/fL1wj9xHSK0El/7DP3PLwKWSymAAYr3\nsZSWMRimqwW/T4Bx6suPXEp0XpintI7NZ8C1BPHMnS1DtBFMRVN9NpdkxxDW\nW7Fye6Eib/u4Q0HEYHo92/26GtO0QLzsvG1yV70et69/CTQlCghFJHIHmVNc\nJgKoXgmsMxUB5BMtY2YZuJ1PQJlq7gT2kA3zMdCS7kNsdjhrj2dRC3ipYA90\n4JeGwTj4R7aIYax8GTl3GWq3C1hb2JRwVPsX5zHCm1xDpsvf5kS3hD45T/Vm\nFzdn\r\n=yedg\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.5-beta": {"name": "@cloudbase/manager-node", "version": "3.9.5-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "30ea78481d5abf926627607167a357762558296b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.5-beta.tgz", "fileCount": 134, "integrity": "sha512-7r8hOW5wxFr/4N0+bHAleyy2E/dV3RRLt/yIDqm/BkS7HIfkYnhBDojBGu+jBMtRLw9JYm/g39E+dEGTNTPngw==", "signatures": [{"sig": "MEQCIGoOpZwm+cpSrqr3LV/uxsCdqWbyXsDvOAIuMBQfxgp0AiB33ksCRKuaw7bXfJiY6N6MEmlAedZKTTpbC6H8rH9+HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452011}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.9.5": {"name": "@cloudbase/manager-node", "version": "3.9.5", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "ad9d7cd6864429ae0c89397e3824bac829f4add1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.9.5.tgz", "fileCount": 134, "integrity": "sha512-grvdVGSr2DjJOaaJMOU+UdrctQywhloCEfWFbGgYYAGKl4D9v9LxtNT4LeTPQAM4jFxPScXE5q101+AI10lO9Q==", "signatures": [{"sig": "MEUCIQC4RdWk7wJ0uIEO/6v1g1bDK8MwQKt0ubXntRkjyD9YCQIgfgIImoMHQwbbl2k866tp7s29FY8Y+/5D02NZI3Ec3kM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452006}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.10.0": {"name": "@cloudbase/manager-node", "version": "3.10.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "bdbf11fc1d8c6f737d4a91ebb3d64826e8612e8f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.10.0.tgz", "fileCount": 134, "integrity": "sha512-k5jhoSAAjQ8ZiqZcqzNYUCXJzuHSwUORvCOVzyv4grZJEwpQwpeHvtdElG4jOqd8o9TbYg5o8wYDaSnRRZ7vgQ==", "signatures": [{"sig": "MEQCICTBYAt4Sei/YUuzatSJegqb22vEvsQDyYeRi4mg4KIrAiBYIgvLm6Fr5/3obA4XUcbsXML5xQQYoYLodB/H8CZ5JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 457184}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.11.0": {"name": "@cloudbase/manager-node", "version": "3.11.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "2add06f881b7d6239d2e19d9a9040488c62e83af", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.11.0.tgz", "fileCount": 134, "integrity": "sha512-CZgpOGx3LMjf6Q29YlVvw6au/UDLc2a76hRUElYV0sbQX8lTKsOybG7J+iHoli0mVryhhzuez+affBmF+TTo8A==", "signatures": [{"sig": "MEUCIQCa2Bo+5SqPeoUG38c4egzhvdA8OxVtqafJ90rJ2VAi4wIgHu11IIxodX+8ajvctJJRXYVkD5Wb6TS5pxidgBFjiQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458722}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.11.1-beta": {"name": "@cloudbase/manager-node", "version": "3.11.1-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "ccf8309159c06ec82004f80e0c79573b6c019210", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.11.1-beta.tgz", "fileCount": 134, "integrity": "sha512-Sj+i4W0Z8RU7kGkWPvW1Zc9jZ5ss06WeyKQ2WldKO24Qei9l2DqkDytRfi9CZI8D44ClXQLg1HRRkkqMXvifmQ==", "signatures": [{"sig": "MEUCIA4yoyDdAmD7pnIwANUiXtqIxDpSeryr5lQRekiyElX3AiEA6/LpCMtbLfFIbaCqVlsMFnZFG+6CeNwaAFOYjrb8w3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 462190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk4jSCRA9TVsSAnZWagAAQacQAIalrgLRExMZG3QQAb/x\nKaXOcQWgo3yT7zG/Jh8eWJnqRvsYf3VLraaaqJrR0P3pyhgWXpJJqyyGA8Y7\nRhgIQ/jtfzq5l0u0dYKRMgLq0O6hwG1APsTqoZMAPwkbjvGlxLzvsO2izHG/\nRphJe3rxp1TG0HTdpNk7vONbpRnLP4XHyS1VkprAqI7+5UoICReeuC/1rgjQ\nWTlE0wiZ+GVwLVzXFWzv6uQhLmtWX1eTsxzte2cBDsxqr+BdrlGfC4Bdjiea\nuc3DTCd9Oe7SJ4+mzmT/uAwVil7NGrC4Mh9eYhOaFIA1ygEN8ywp5bPzRUM5\nUYhdtLzwekQENp3DB1WhnqF1XOD/dyyfcT3BzsyIDnC+1/7JOsFKir3oz8ns\napVsiMbh7e5EoHZqkcrxV1YLS2vkck43OPFC8h+DaftOVZ7Rch7KVxnqrgCL\ncCYhTdn4gqJhP/iYdxTNCS3caMuksnRbviLkgUG6IZe5IsECV6uBSSxcbds3\nk+2Ud9F89zLZvFtwFpVsO3eBwaxHH3pvXNvwcz88ffq530wlmNzUjX1mv4Td\nMLh5tneFXKmaIXtr0zIa7td3kR3OiVmcYAPLYqsuH3mvc6lD8Kk1qrPKvZB+\nKNjDymOi63BkPgz6UINY71bgAZILv+FfdskUbMaBYGJCOgAL83mXylPaxIep\nexPP\r\n=cc5m\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.12.0": {"name": "@cloudbase/manager-node", "version": "3.12.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5dac37ee4f4a73fef39dae569a334e69b2d9d2cf", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.12.0.tgz", "fileCount": 134, "integrity": "sha512-4GSM2knlUDf0v/HRjVYmC536HElt9alBWMZazKpEpwHXev7Ng6vNr14DqzWmvs6fVLjq10teYyB/d4GoxrZvLw==", "signatures": [{"sig": "MEUCIQC1MujzvYPC/1uCTIZkT4u7/eKVyeSIG81l7dNvjD1SZwIgGK45di2kc7b3khkk4sowFYpJNUewjFfegl4/LgDnQoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp1/8CRA9TVsSAnZWagAAhu0P/0e7HIMxqJQCY0035s9v\nkW2bHcCmhFGNnAuyR3GpJzFvcLGsV9j0lMleybjVCCgSZl4jpaRoNkYVhCIy\n7o2g4XR5EGNv80BUB7W6XeNu2k8lbpQzpXU3Yp5vycAjpycuhF83oZFsqDYm\n1CP/ocSVdwUeU5kiI8+CGv4RlXpvMsjUM2xkdnTqZn2FSsnl6o7PqDQZQ3sN\nyyYuIUM2Dfm+z5+E4HnBi7apzKHrFcsOXpe/N0OjC2eaGgzHC0yFABQoHgSQ\n5E2bsIY+8Vxmg8CLTBhbxm1CIiFn00HAt+/VuM/Jvrlazm/pXJZNcyIX5B+7\neWiQzldGIoi/9WH4Iq9iIMWEgNFkwHIt2sYKDxLm85gP1/anXjSwQm5WYMQj\nWkNtWH2cwSOkNLdOWdwY3PeTPp36iirssnT9fnWclNCve0zBoEwF0CHZRxit\ng/L+L6I1ywSaX219VYlJBWMvwtIBA6EDUbXNAXW59lMzs992VR4VswhLN4G9\n2EMzlj3hbb+eRoAC6Jqq8n+AcNR8Q2BbZZ2tw1UGIqMD1XewebA1ibTxeA27\nel66D305+M69j1s9DIgV6YJMnwgZzM/rvrRv2HXVHcPO+OBfyqNHfEBlhGl3\nFu1SdPUvGmO7ZJfnS9m8Iu3U3onJV5llz6bztFdAPeMQNmx7/R6IfWJ5bPP5\n7+Ys\r\n=vvYs\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.12.1-beta": {"name": "@cloudbase/manager-node", "version": "3.12.1-beta", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "7eddfbb5ca4859427c4fb15cc2e692560dad05e1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.12.1-beta.tgz", "fileCount": 134, "integrity": "sha512-r9bJxO87OoyMUdFY8QVQ2IVs8KbAwp8atmezD74cW6BLxk3GG/QuBEaOLY1x3m0xX1ARThxAEbMgZeaSYscOhQ==", "signatures": [{"sig": "MEUCIQDKb/p1MyVJPacFmWFyN3dT6lJDfhhtIoBj6OmJVPzkbwIgVsps5V35aXyxY+u6nY1yIyJntSR+YG27RDdgATITjds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw9egCRA9TVsSAnZWagAAc0IQAIDGGbrt7lTI2qRRd954\nkrRz3FTqTR/pgxgofh35+gZBYac6aK+FfmikCjRI3/3an322hB3m8TrToXBJ\nYe8E0rjKvNNkIUQSmmtItQN+zTUu9U4lJMdg4zC0CR2/ZXrr6PBrB7EDVjxz\n0yyvLjdzZov1oVdK0m+l38QqFZC4zz3bUBJYHNjGS6V+xWMCo5Xe5Cg5v1cf\nCWqSvItSydTJwcmPbxyQFQ/69eXfw9OAkJlis+i44pPHAeiUiv5BQ7/DCUe8\ndmQ7q6LhA6msGKk4lL+nZ9ejKg5dtZ6XxjOEH8WZkW/MNFeY+SNuK5O5Dbka\nhsyTAz7ESyZtiKUmqUs08g7gG/QNOcUtPvsVR0SvpfCrZJzlWtjeuvfhykXv\n//LZdMpt/dVvAe2vvuH2yxrDOK7GrxZGFGhy0QRJH7meBDKWHx9yhFeWrb69\nlDQtdwbiBycAx1v1uiCnb6ZY1Kk27gsckuPDlvFaUVvSOlomUwdwF31Oy1wF\nuCvjiSzDR23zQ/bnFP0w3ivVW2IcqH80GTs826tQnp3AlflXyI4SDihl90vd\nAKn9GNy2Iz/STCnrnXIu7Ci02/K1tOCBPCz8PP5SmfLmcrg4ql+sAIhCVD5j\nKmPQfDn7ZAt6CN39ioJCLVNxPwuqeF8wmPLoW4rj/e1QwRFKRIiF0D1FX2MV\nws55\r\n=aQgE\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.12.1": {"name": "@cloudbase/manager-node", "version": "3.12.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "9a9fa9d44d165311bddd652e839c4de9e4f7d1db", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-3.12.1.tgz", "fileCount": 134, "integrity": "sha512-y1N9c1cdVc8PAGDqndV3y+ta621RcseG4mXtz4vbCLeMYznC8dSr4zxPf7seE2uqHfI3KBQSChztPGyTB5khbg==", "signatures": [{"sig": "MEUCIDZTFLAqslJYx8U2TsR2gLvY3GG8fMgta3MIWqLGfmApAiEAvF8KP4icrNxUWD1U2RlEfQ8qFlEqMWO0qGOG0xodn24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw+03CRA9TVsSAnZWagAA7oMP/0cvJWFyXsDYUnhto3d0\nOZeqnyC7XWN+th+yertB0TNFsU3FSVxMrlDCQjNY4DhlAkZMsrjKz0Vi7Y8R\nb6kwfLx+XuVDuwUj1ZJ+W++sUeLKEllZql+R6ruhH+ZY0T4i0fEcwBgPquR9\nRI6N95oie4uueLlgwVeuj2gHU06uHkdb8X/U9so8NckJ6bYwTN/i1qtw3AzQ\nN31rFEMFf0Y9HCW03QTwtpbdln861mkRZkqjs3CUTRya5gBzGspmW8v+IGh0\nHyo0/23ZnCwz+C9baG/5xqSBFxwjJN0Vz/SVORljeFJkorELP56MXvo6IHEv\nzH4srgFx03k5MkalAQKn12iXF71OnzD9eEpSLkMpSRXoB0skU3UKUS61q2As\nib2iyfcVjQs7Kt07HpH8v50ORf+Pswvt6C+Jva+nQJwjFOQKwqPl6MRSve0Y\n54ZiYHS0+o5qYiP8hdAfryLueKsUOYNMGN6Y4/pGiv+lbZwC3BtwYVdZpCkG\nGRh9xLkYeL10k2Ye86vZQHB07hRfE2bFJf1wQKK1TSC4yHYMTLpt8lQYy+Kb\nD1m8TNgeRC2aN6AZEMm6y3aPmix9yaWSdlcf9STi21+0+Drb0c3n6V1RkmSA\n77n6dZ9YMPjrJMJ9ZKtOWP9gf58J5U2qlD4fOWhxvjMjjM2Y2mkQwMkSJvcu\namoH\r\n=hM8M\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0": {"name": "@cloudbase/manager-node", "version": "4.0.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b4aae003400b29f687277a156786db74a4b21e94", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.0.0.tgz", "fileCount": 134, "integrity": "sha512-ARj/Y7jufcyFqCmQLSm5m6DEIBpUtYnbHI/DSzrxyPoCCkTLc+JfHlll7bIaH0qhvKjl4u2dnjHQdHGkcZ8uDA==", "signatures": [{"sig": "MEUCIQCoUmlESufGdzWQGH/cqoteLk1j9jaERt7f4KvpjZhzBQIgEoqex12mjjIn42794laOt5bCvrue6L/L0X7CiqiK60o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimD35ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1jA//eQ8AYFgzcnhsWa0QbNv2wdCMP+cM0M7lQu/+b2G6ONM1xL4T\r\ngLrvvBqBGaNVpMgpZBb2tVhkVV8+jx3ZE5KDuCrFcMFw4QCG56UbG/Bs4AM6\r\nKEz9IShi5XIfsABHBJ8NLnkCRWR+90fEw2/xn847mFbFsUbBRJGVdoWh25bx\r\nWLqgy7IuYhaKKclH3MiB/vvwhrNeQgMmTtkvShm9i603FJIkljDT3cYeDbh2\r\nXPshCjFhkpMwDlk5hYrWelEH3pUV04ygKlbT0fxo2Ob1AWrmKIlKPYJFoAWq\r\n3f6q40PwsTaPd3fGcIG4DlEaDC69mPHXnvWl9YXjkMwy1rZJ5WJhy078J8ge\r\n8EY9PdcfGJJXeiGwIqi8ZehqFYyxpHpKAY60UT4Jrhll9xDeZyhH6DcEateL\r\nxGbTs/hvawbU4aBIbmS+q1g53WF0ExzHY+uIReDsA1bYfIbJTXuoxax39MNd\r\nj83e5sVPbSMw8ZHMZPZ03j7kHyQKGyKmw1b6lSjwdic7FJW+N8orOumReEuv\r\nWvQxxNXop2QS7GLh1R+GAYn6ZcT89oUgsSbLcNUtVkb+Z5jIMiSPji+WHYDh\r\nFwkg3ieAAUjpY61Nrf4XegcJDUj4Wm3TPCirlmFOUGQAlhlGJldkULd+l0xT\r\nIYztYEbIrJAX8GsVXzw/NRZ4i/qotwfiVe8=\r\n=Y6DV\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.1": {"name": "@cloudbase/manager-node", "version": "4.0.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "4baaa460b7ff9f75713ff7bdbc8af9c517c45ad2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.0.1.tgz", "fileCount": 134, "integrity": "sha512-yh3p08HXsEqiSA5TyZj0Esgb2h3GChkH3rBpgWvZWOy8nN8EvtZnHWwgsYtYBvtdAYExTQkyyJz1WSZWGl4F0w==", "signatures": [{"sig": "MEQCIH5/yCuKpA5whjYDH3wDBDDjV1b7cCSKYHOeDpoofJNjAiAGXbEiijty1mTiBFS44s+cNkLSsqFx/Lbl2YlEvltF6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 481083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJist2kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzNA/+K+UgkhOsaXOMPE3cGWEFtG9gFidfuGBFBvwrMcrnny8H+KrL\r\nQYKW00caMFQKpGLExzsyDIZmx62XJiK6ujRcN+ovDpfp2MRuHr/ieNMZ5xPK\r\nN3VLh9n4MkcoIUMYr1xMtgo5b1ckNw0/TwEfBy1YUp/LmYti0yvu18AFtLXO\r\nSOpzGIISnOgdI7CMdkhtpiNbrVEmz7O9sMNCzcqlQbUCiHqZSNga1j/L0iXx\r\nptLTNgiD2VSMIgOO/hWjlSthxdxBB/HE+LoMLIvw35T+sgd/DNViib2PpzQf\r\n3h8agpKoid3fsJ3J0D3YG2/8BdQv8On6eaRq2Mvy8C+jb+BVMhY/T99p1NXl\r\nk1cKJM3YxFpgu+yowO6BtCvf0VYYFwP5ayHDYWrUY5bve+EFqjBUTE4jbYnU\r\nERaNLzQZkfwrxcxlM+3Dc7LmUGf+y4VyyncDYgpwBL4NcvEpaQ2568dho7UW\r\njLhts+nosVvNBMG+aZ8AIopuErgoXCZwCjsQgD0Cyfz7WS9W+AwKXXQr/mzs\r\n7CpGMrJWxg+Wy82NBZueIxcTrW/miT88g2BvU7ba1LAgjQufI47JN9W+A/3G\r\nNbpV5cWP9mKc4HCl+ui0alnT8u7kMb696BqsDPAPS2EkTLDWJ68oP0/sBj8o\r\nK+UecQJN+2SB9fZKKE8lUxYd52smBTAlkX0=\r\n=+FpX\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.0": {"name": "@cloudbase/manager-node", "version": "4.1.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "7406ff971d58411d52018c3ef38d13a8841162db", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.1.0.tgz", "fileCount": 134, "integrity": "sha512-3zwfrcc43ZiSMW7J0kDY0qZMc+WiG8HA+LIm8lF5I+8kTRGEUdL8LxWWS4HwbaXWgxzBhPOpCki1fE6tdEbfow==", "signatures": [{"sig": "MEUCIQD7f7PUdPB/Ga5S6SzRU61E8BTCYxNfL9EmSgLr/a41twIgA9M6/wTE9/ulHRShGEPGC222/gKnRjdJOAd4qzCYz38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+vIcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHoQ/9HpGqOHGeucQmUqhBXbkp85UwLiH7GX2Cy9oSyqv1L0Bns7Od\r\n1qA9PLBO3LPW0xpaAaU1ZYoJAp64gDjONVpLXvcJDMn928JOznPjO26f7/Np\r\nw7AfFelx39a58gT8e/g+KnrW+itCNifMoQVWXfbKpxaGMWXUhCe8Em616F9X\r\nzxP445zo8PAhxwdvJdKcYoCPZMUT0iVGSXTzF2W47BQAZrpQzLHgb675SaTw\r\nBE2jwjwBltjPcD+VlJ9866cMIlaBG0gx/FjlOt+r46bBMTDL6TI1+b6RY4V1\r\nTNK4HffZt2BgkM2zSBdoY2r6LU2DEn6njco81yxvHIjjFT6+kR3ZAsoOG8aG\r\njAgFfTnfMS+wiizoyNjURMTSONiLeKxQL3w7wV8pEi94Ag5IaxGgUuxX+rw+\r\nyWCnb+b3ZXRnZE4Es35Ie5IJ395nUe2oD7Fsaqvwlwar3tfDEiP2jscfXTR3\r\nmQcKMDBVDbyTCY1PEV8fC3WaYqHvEUbFZeRp4REfNXpZ4p0rBc3bj0kZOlHG\r\nWC+1Lq1Q31rz0fXtd3V6Qi2Oc6BRFsf/EYLpWl92X7sl0LQ78Yo0Cucwp+D9\r\nhUZZrq9/9C2QdAHA44kcVC+0FWnTxN2oCFglnBXvIT/aa2qkvt+qHJGBzUtX\r\nB/GfnSx+aLUVFCCya0h/ZnDL71zj/kdGifg=\r\n=9+Uj\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.1": {"name": "@cloudbase/manager-node", "version": "4.1.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "3312e1244f049c03c98a493391017918eb2ebf68", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.1.1.tgz", "fileCount": 94, "integrity": "sha512-6SE0TzUIXRFlLv2e7SGlu6PnvzaavjRSC3kQAutZKIiF3pCN0nIjL/FSp1ZNpjkNFSvShCdkugZE+VYmPmf94g==", "signatures": [{"sig": "MEYCIQCA2EV/L0zV4JxF4I2ZGrFP/sCcWBi2lpq2bB6v4gAg7wIhAMcUPzaqXeSiKaZ8qSM5KMWVKYRBj6vQrNCDdd30Lkz9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYHyKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1BxAApPzdqFKAweV3E7Gl4f/Q9r1Io9ZlUbn3QR54VC3qXriMynqq\r\n+CzpdfmhOyKk8jDQEyOtmnXlTWyzermwNT2wsS9MsNUwWhXbzsYHjvArsucY\r\nBJgJpDAM4jFOW56PAl8Fioa554caMMMP/QvzdQLTX3jFQLcEJyCegj29Y14R\r\nIun7X8XdIHjxD45khaDOvx8Cqia0Jx/g/qUhd0Y343Cmupf31QLgtMylSa05\r\nloP8VEpgbvfpiG4vxjMFVONFI7kkA3t5EVq9MeQIsKUVlNSVK8J600lEzPwK\r\n2Bzhhhig030AJkUk/VdyErpLOlHZhIATH1V1bIbcAiVdyEcoOD1IqNrTFG5P\r\nTUJMbd3gpf6WPAVgRGt7OskQ1sPNmPzuSxhW+xpW68XuQvoxNVkoli9fsNf/\r\nxTGkkDn6iVvKGQbPaXuYK8ro5JVT5WaNXyVKT5l63DIg1+pttfe6n6k2in7z\r\nDgd1LAbJf0q2AJ14tjWBF+dok+skvf8ECLM+atvTNy0hDzCOdBV1vvCg0/WM\r\nNB2/hdBDht+BSBELCEILrb/uBI9GDn6Bg6EQWRuOxTr8zE8nbyP64j+e3Kkg\r\n/Mu7ju+i1pAg4Obqk8rzw+CDkGh9hgNGpzjtrHArzzQvDnPfK+ulb2FCS+FA\r\nOxd78BwTaBM45Fev4wzpKadMWHYh2sVdFY4=\r\n=a8wJ\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.2": {"name": "@cloudbase/manager-node", "version": "4.1.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "cd3ef4bd7719c7efe65e04b854b697cbe966240b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.1.2.tgz", "fileCount": 94, "integrity": "sha512-CMvJmMAclq6gDjrqOwLN+XM9keysAByiOopwZCfplRj63ilot64816IS3QOxfK8W9e4qhfigwWIP2LC/bbVdWg==", "signatures": [{"sig": "MEYCIQDFH5E+plLqThUfLtDR+/vCt2IIIQDvQkqaz0sz/54L/AIhAKH+viyvbvFw0akSd9ohFXwt3zNCmgkYy9uWIFmLTFUR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYNKyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDOw//UjcmltJ5D1Tf9ICKL50jJ5DaRqSihuCisX/8je9Lerl8G38X\r\nexLiHsEe59iBAs/HO65rralERh5AvMdbBBGN2QEs9Dltb5VTkaJj9jY/pwXO\r\nGrV+5FZ5JnU3PRC3eUDUBMlbe9AqD8BWGGhaXMA7YJ10pCfHIkPFC3zaPajg\r\nSADNR2OCEVXxg4eSZgA/TGqz83ZGTPc5LTeNw/7c/ZHBgwuDZ24CcXAZzhlY\r\nW0YIMmmBj/pN7uklGItsjwp0gUf4lcnpkiOZn5fiB3peIjlZ4mbEs2lzFnFL\r\n7Yu6KlJ908d2L2FK041HwEFwR9cvLKt/LkkvD9UtbGL8DZsDTiLPumzF+GkL\r\nRcsjq8S+fxFBlL4OVIsoE3BzuU+BFTzgOMSj++/sdid0CS/qME9Zu81j9+Ib\r\ngs5iseIF6SIj0nhu7kXjpNP49fjjhss4ArftpxehOgewT+9ZbY4lUpTxP+Qq\r\nxjP18Lysz/wyIVdnGIbogt3KVQIS2u3peqVqFk60tyRazN1YzIaVrQ62Tnt1\r\n4vNx0kL1GtBMFioE+tJYiXyGKMiNhvCoC0ogrruaZ+j4vdL+bLQx67f6Tqiv\r\nBnnIa5PSRqTuh1t545yRUgVznAJCCEJ5DjSa7Lo+EaypFZeP5cx8NFiTnn5q\r\nKUoZ6A7Kphrkj+xIWO+yUhMRwnO/xlSRxCs=\r\n=lMAT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.3": {"name": "@cloudbase/manager-node", "version": "4.1.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "7319669dc67bc4510d2293b37a07d868b5f32b44", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.1.3.tgz", "fileCount": 94, "integrity": "sha512-5b9jP5ItPETunw6uCGa517MQjEwe/+RZEZRjDLUbjFXDp18yKi3ur+newO1vJS7pAChYO3EBXwjgzAj7f4IWmg==", "signatures": [{"sig": "MEYCIQCjT94czKpMqUzaOqS+Ormvk7YdfnAy/ujNbdYfkNE+eQIhAPV5z3qEG97ZhTZ9ZBFsG79DQONOxbZxwysfurdWAWuJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYdHpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBBA/9Ep+g2Kpa3ftLn2dTsaQDY1RIZAvRYc5Zzb9DxVcaJrosV9rz\r\naUmG0C4yZcW2RVlCsIX/01/tCo8TVH00Rn+R6dO4/EgzJDYvDIIOJwkoFKJc\r\n1uyw0K5NmswcZ1Px0igkkRC0bkVur18m9/svyeFCBrIaSzJD6Na+1LEvLyKJ\r\nmf+Em3fsL0iQpyG4NyRejyWnRGAX36U5cQvH93QZwqnombbYdpLKjOp1OxlR\r\nO4DLnN4O2Hko2DhciG9pDW50Kh1MkJS3aCW2hvBZgwlsDvxnRLSOxsBIrD6e\r\ndDxcPRVPJ6BUdl//IzwPKWCpvNmZzQLyTVdDoQIgbday4H/lSvoWxXIfNm+H\r\niVgSoGk8FHPsWC83DevntTw3V8xWB11xAC2Ab4hzSQZT3GViy0klEH8LAZ2l\r\nNfxd7t+dEWg6WkhUwlxhOmnovGT6OwQOrkDP02YYpPCzZtDT+C4YI29QqKv4\r\nBMnpBMFQjX/3q0v8DABqHMtyxzW+2IjWfxIDmsIsBsAd9njGRs4TT8lEChLq\r\nIGhYE4+OSqjPu7PTd3W7VTIJBjMQbb67gAMXaA+LpNNwi0hDx0nsPhEpMgXC\r\nK+W5F95PIkiSOPpx+Nf27hOebusCAoko2QMHlWxRE/St/78DFAumZOi0mnmB\r\nS/P02rqYNN3oVypRfs7M2wuAQTP3pg6LZd0=\r\n=6J+f\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0": {"name": "@cloudbase/manager-node", "version": "4.2.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "cbedc098389adf6b0846a30da82dd6cebe2e414b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.0.tgz", "fileCount": 95, "integrity": "sha512-91QKpu+qhrkEc+X0kIy+rUTzVyfjGb4sAqvs23f3HkwzVm3A+5GQafuIF5dtep9BrxhYBFX5uMY0t1B2VgSnog==", "signatures": [{"sig": "MEUCIF9ONjug9UJBKdf0M4MF4DWst023sntAU3D9ZESn3itnAiEAxN0hvHSDVtzvmlVSWAnK87+gVrbwlSwsiwynf+Q0CZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiBcAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8KQ/+I979t8CU0Iqt3Zj8dK4NilWvT8BUm3drXZ+UBpdhDb2TwvZJ\r\nNIzYjBnxv99/pYuF/m+uJAKnHBMLJ2fZhTJ/dx4dL2z2XOChhRNyvjRj1iEI\r\nZOBIWPdaAjiKwJG1nmi6WHEjIrEYmak/5CU2VM2bHas7GKZPANIuTv2HbW3p\r\nwQe2g8ouWjSo5x+kW1aOWQ0Xe94rosBbRbI3WUERXoBwQCAcmOB7cdDAuAHw\r\ny+9vqLPyfkI+pgBwL21Lm9A/cP6lY7lrirbyYQc197JpUBUNFlP6REHyalA/\r\nPe2tc4vJ3NyUket8f7AWvAAkoz2QCCSlP62rMv7A7kp8REcXGON74ZnEf7lV\r\ndBzdGK1BL6urE3/jc2ueRrC4GCV89Yf/nTvHrF0KP+Mjgoe1acHT4fOyy4ME\r\nFLyd2s5dWjYjf0iGbhYuLxgk8Owi/LVPUq+YlO56BooPMvZLLmO8z2MyzNDb\r\nolecSzXpkwR0X6uA+C2eZEhqlsQfaJt7nKCi3nBFnj3bn9wWBVYFlYRE4bhG\r\nMdTVWe1jYK4adqLg9+wsGe/6mNNtD8K0YmZz8Q4n3qjup34QjT02rXGAROod\r\n/obJGNBfhAkHYyck1sSTTUn4gMB8Qxw8jYF4ytBMjxVg2sDy1X7OD4UT2jVI\r\n9z3hBa3oJBHoR9z2cNYUvkdojtXlqf9iLHA=\r\n=cMLH\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.1": {"name": "@cloudbase/manager-node", "version": "4.2.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "fe57fdaa250da07b16affa1109c72a8ccb226747", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.1.tgz", "fileCount": 134, "integrity": "sha512-z88zzSHp8YdYdXYJqOoFAijilckwLHOg9dCM2lV/xo3UoQfdWAwvnLewflecnWPVHipyzjzUIedCnbJ9Nrr4mw==", "signatures": [{"sig": "MEYCIQDdgm1TJ9Oh/kf3Nj3gyLBsh5CHbR7EJxCIYUIYUl/FrQIhANBw3pYROqhRZn1t8FN0E/mX1wIagTr98lFd2NUtFIoc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480726}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.2": {"name": "@cloudbase/manager-node", "version": "4.2.2", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "b1da19f539c31286d1bd49c0aefcf59c47e8738d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.2.tgz", "fileCount": 134, "integrity": "sha512-VihAleNKKA1dK0QcpAeGoRqnWO5mxVl2urzORGlS/GEpV8Ewa2Xql3YyDL/6OQ8DPUQ4t/d/3osihxDxJSthcw==", "signatures": [{"sig": "MEUCIQCW/m65NZiE0YgeW6kNGdPMnAJeSclzY7IIAv1qL3VkdwIgPhHJ4d0XdUcPV3YapAOQCvUqHvJKI/B2tLJH0RyqIvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 482886}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.3": {"name": "@cloudbase/manager-node", "version": "4.2.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "7da8719efff09504ef70a294ec72cbfe9a8bb14f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.3.tgz", "fileCount": 134, "integrity": "sha512-6uQ3sActPcfdIkLQVYeZOd5x2wmOKosJAoHC12gYxSAI9N+TP57/0lTf8rv+tW63cdtK5IckEZGaeqagKLO6lQ==", "signatures": [{"sig": "MEUCIHpGQOP4t5WiTmFvd4CNzDUvuiSQSskf5arUZxoqDfCBAiEA1I8UTIy42xY+8/saqhHRmklsudxK0no2fz0r+b/6RSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 483555}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^2.2.4", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.4": {"name": "@cloudbase/manager-node", "version": "4.2.4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "e03058e526a7cfaed57651da106dc46ea9678818", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.4.tgz", "fileCount": 11, "integrity": "sha512-a6h83ovm5Ax14/fQ7BbMiPhbMaOYcKjm2vmm8h83HvB+U7uRy411mSJqnztna4Sk1pnX3NS5yx3C05bm3jYemQ==", "signatures": [{"sig": "MEYCIQCiGCkGEyJhd8Cyof+NaPhrJp42uBEOkEMli+MDdvOUvgIhAMZIZpCqVDa7CDlQYHsB0fpMN0dsB8Nq+KUsXtIdejMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9324}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.5": {"name": "@cloudbase/manager-node", "version": "4.2.5", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "2aaf81ecad719a02e957f85b822cf181df54c7d7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.5.tgz", "fileCount": 95, "integrity": "sha512-/gs+mO7kH1LSK/gPSPd01+fAovXoQoU89WRacFiRa3Q/bq3mKnOtmfixlMjYMwQ7Cr3cQ0/hpB2uyJkOO1CKFw==", "signatures": [{"sig": "MEUCIQDBd9x+zJLh9xzGMBJZKGuBkw3eaoKA0KZeFGQbv+jQMAIgX6AIgVmD7olIxgAXimPjYm4I8j0HPHD2u+S5Zb7Prpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254558}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.6": {"name": "@cloudbase/manager-node", "version": "4.2.6", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "1ac12db109f322059a320d6cf120bc970b53ffec", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.6.tgz", "fileCount": 95, "integrity": "sha512-ztvSIfx+NnPofsvDODPI2ujBEeNYbHsDheEWe9kZZ/He/k2Dt1ayyOeMtCO6MM9l6knB/18aNEdAiReYKV2R7Q==", "signatures": [{"sig": "MEQCIDVecDcTvHL4+3dOuxprcQm3xiAVvACBBzjyWXFcaruiAiAhBEm4qbk5woNNKJi/azTWz3M9AYcJ1DQr3+W9Bk8XKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254651}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.7": {"name": "@cloudbase/manager-node", "version": "4.2.7", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "cdaca56dbaca52f2d673639360cc54324d98c470", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.7.tgz", "fileCount": 95, "integrity": "sha512-XBAnNIHkaFSqGOqlLK4FMuMckxlkRWv50I7LyfBmPRrsJx62ioE9dreeae3hcknnp0x+8sPCkFG85/rd8kNfvw==", "signatures": [{"sig": "MEUCIQC/Yi/e5Z4D2oM4jLWZZ+8g2AzJn/AXveTMd5KQBk0JJwIgGeFjLmx3fclovYxdJu/8/dkrn9hq3jcDp8IbXSb9zmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254744}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.8": {"name": "@cloudbase/manager-node", "version": "4.2.8", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "94df52ce812fa59dabb2e04fe26d81a4a19e78a6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.8.tgz", "fileCount": 95, "integrity": "sha512-TLtF0gzaFDkCrrAR09E/F0khk36guikUJTqgmxy+6C2xbqmDc3lw3uzetj0Mu+0ZixjOPxFhUBv/kFr3t7ikEw==", "signatures": [{"sig": "MEUCIQCOz9IaMoGOggtvCd5JHFWuUkhizBjCMjSIkR0Q7t6wMQIgMY3sB5FsP6JuooOnY1qn3O7swylC7tYJGISsGLTNqiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254760}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.9": {"name": "@cloudbase/manager-node", "version": "4.2.9", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "3e9aada75e69d240afee1ad69afffbfe5a4f752d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.9.tgz", "fileCount": 95, "integrity": "sha512-LnT0Ry/fjRTH7wu+acYxXtOYpPLENl4PVF7zP/EdIzfZZ7K24mgJUdRLsRaanigOlYIZCwgnt9VEDqYe0b+/Mw==", "signatures": [{"sig": "MEQCIFdbChmDMMIvfbbptPfOAKOmGDENMPXQHPmcljuPOkCKAiAq/1thE4rNunX+v4XMg9sFlUVq343md1t9jZB9pQbBeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254953}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.9.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.10": {"name": "@cloudbase/manager-node", "version": "4.2.10", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "69044daf60935963328747e49d7eb3d669036cc5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.10.tgz", "fileCount": 95, "integrity": "sha512-paVJR0JQMCjmFyF2pFGUVFG6S3EtkNGeM6R5zd1C6vT9iashYuLMmcWBpiJXhYRo+Wls120pyi+n8tmqHWFvWw==", "signatures": [{"sig": "MEUCIQDlWxxtL0pE4SVbILut7SmlRJf9SdrL3mlUemv+lbZgdgIgUtomzduQz6mVr5SdKo6dT4NsSjWDCE8DgMMaMNjOsc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254998}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^3.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.12": {"name": "@cloudbase/manager-node", "version": "4.2.12", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "55a328a161de6ae32f8f6c09ea9e5a6c5230d0c0", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.2.12.tgz", "fileCount": 95, "integrity": "sha512-zzvu20SkJhCDnHn6zPO5ZAH+fn+El7IlwIGEwapOrmY5tqfTTEUWl2kLnnMKPyauddGd/OmpaA4ZdDvNz1ImzQ==", "signatures": [{"sig": "MEUCIQD3QBUVoTSD4rKMojXgEEeO7bpybuqO9QgCTPajv5HxqQIgKlZMXbBl0RTDdwCGki60hsytPKTKZO+tqqqxqi8zwl8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 257053}, "directories": {}, "dependencies": {"del": "^5.1.0", "walkdir": "^0.4.1", "archiver": "^3.1.1", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.0": {"name": "@cloudbase/manager-node", "version": "4.3.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "780a6545b342b536c472f0815fca902096a85616", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.3.0.tgz", "fileCount": 100, "integrity": "sha512-bd6DX8fbj+YDdZzkjmwC/oThT8ypUJy68lH+wHLdu1APZa+HkbU1/CgueIFvaSnM648ASmMBOs2UpGdRcg08Fg==", "signatures": [{"sig": "MEUCIGXqaeE4+vlF+qEJtruDPF/chHpprP4sCTusrBuvZgxUAiEA7uXYwBCLYRLda3EVy+Ln2TSaXObebCJSadTGflPUcoM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 280515}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.1": {"name": "@cloudbase/manager-node", "version": "4.3.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "789fa36c5ff7dbe9997dd0209ab574862c26e56f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.3.1.tgz", "fileCount": 309, "integrity": "sha512-kOb1/ykjpAlqotBQ4iu6CthzffF6/cohZlKD972VHPYxLnC3PNbqSKywJXpm9Q3egU0IdOV7otqSNHVBgoA+yA==", "signatures": [{"sig": "MEYCIQCLwQJJi/IMNMapw273PufhFEOl5kvLt0fBqZM5iTWyQQIhAIYgrxqOZcV0Y0UsuKCbzhyClngaTE1NAHthHYDNGPGc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3908177}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.2": {"name": "@cloudbase/manager-node", "version": "4.3.2", "description": "The node manage service api for cloudbase.", "dist": {"integrity": "sha512-B7Lp12WKTFlfHbYAx1A6xEr9oRrQBnAuB3oPYLE332RjyTX1lEQ0nwk7EdSrWqJSVLSmAWIGQrPbz31I850boA==", "shasum": "1e17459906de1349b6b00d1f3860d48f4db86a95", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.3.2.tgz", "fileCount": 309, "unpackedSize": 3919391, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBt3B6a0PUplYIUx4RDCW0Ml5eTfqzpYuOiIFkEcfBAGAiEAtR74qNbwbbbJ1ULINNO7KbldrL3Sjmc5co3herUmGpw="}]}, "directories": {}, "dependencies": {"@cloudbase/database": "^0.6.2", "archiver": "^3.1.1", "cos-nodejs-sdk-v5": "^2.14.0", "del": "^5.1.0", "fs-extra": "^11.3.0", "https-proxy-agent": "^5.0.1", "lodash": "^4.17.21", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "unzipper": "^0.12.3", "walkdir": "^0.4.1"}, "devDependencies": {"@lerna/create-symlink": "^6.4.1", "@types/fs-extra": "^11.0.4", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "@types/node-fetch": "^2.5.0", "@types/unzipper": "^0.10.11", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "eslint": "^7.6.0", "eslint-config-alloy": "^3.7.4", "husky": "^3.0.5", "jest": "^24.9.0", "lint-staged": "^9.2.5", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.3": {"name": "@cloudbase/manager-node", "version": "4.3.3", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5d2012c31303f0ffc8f7e76757c725dad010b780", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.3.3.tgz", "fileCount": 313, "integrity": "sha512-LzykC1fDleczhzom4tcQAEh/5byDqKEvGYHS/RwuhE9f+2GMIuNWFkpteDV+llCNcc21uAUx/fXuKJqaYmp4Xw==", "signatures": [{"sig": "MEUCIQDiBv/pXwmMLJiICAqzbKiEN+SA9UKhOS6xyCDGBsVI+gIgT0UYGD6Ss9aEoGU6naPI7VpHwwPODQqERk0JvpIcYTM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3925505}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.4": {"name": "@cloudbase/manager-node", "version": "4.3.4", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "fb4dab5ecb7555f238915ed590f1e5eafee06049", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.3.4.tgz", "fileCount": 104, "integrity": "sha512-kVu/rJ+17917vxshoIdrfjUZK1hT1jz0CmCGzTZZHAiyiwhxZEzq6rVmyOIibypAKSZbaQv9ppV8jznoYmsUKw==", "signatures": [{"sig": "MEUCIFvsyPF6cZlVv87TjJF/+NIerWHgSfuGSsh828dZdnIGAiEAm5/sxAcjg+ROkjB/35c8LbNr3OgWJUVgby5YABW/93U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299781}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.4.0": {"name": "@cloudbase/manager-node", "version": "4.4.0", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "5f98246b71852199c1e53232ebe8695a443a5b74", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.4.0.tgz", "fileCount": 104, "integrity": "sha512-PkApGSymMAhuO/T+7uSi/nhZm93lcYwVH+iY4znREdubZS375bcj+BhyKAcRrgLJ8I0xlaPY+H+/47Z+yEMpkg==", "signatures": [{"sig": "MEUCIGBXZYg2G/rAO9meY9oeZBf0+tal2OylaUe17bPKNNb1AiEAjrfVx1SgJfBUPx1pKTjnz9OzfhXIr7BKVIXcKWv/Uew=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 304643}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.4.1": {"name": "@cloudbase/manager-node", "version": "4.4.1", "description": "The node manage service api for cloudbase.", "dist": {"shasum": "f656998d601bf9095d0641c1f62acca25e032204", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.4.1.tgz", "fileCount": 104, "integrity": "sha512-gQ0/fTCCdduAZ4bu3ttNCikp8E8uYo7U5pWrql6OE9Ww1OozQ0XOMsSBKzDTLFzoV01VgSJA2dyuJQAtL6QTXQ==", "signatures": [{"sig": "MEUCIQCGQIsH6tcBeTLhoQw6SwKQrUlFRF138el2jt1tKgKRcwIgFO+zvIXJi6YQ0Gu+z3e0rTziRN3HP1ATIeIieTXZFy4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 304655}, "directories": {}, "dependencies": {"del": "^5.1.0", "lodash": "^4.17.21", "walkdir": "^0.4.1", "archiver": "^3.1.1", "fs-extra": "^11.3.0", "make-dir": "^3.0.0", "unzipper": "^0.12.3", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "cos-nodejs-sdk-v5": "^2.14.0", "https-proxy-agent": "^5.0.1", "@cloudbase/database": "^0.6.2"}, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^7.6.0", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "lint-staged": "^9.2.5", "@types/fs-extra": "^11.0.4", "@types/unzipper": "^0.10.11", "@types/node-fetch": "^2.5.0", "eslint-config-alloy": "^3.7.4", "@lerna/create-symlink": "^6.4.1", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.4.2": {"name": "@cloudbase/manager-node", "version": "4.4.2", "description": "The node manage service api for cloudbase.", "dist": {"integrity": "sha512-Cz0VfqTVXNyrclAf0uS4Bxfj8s/jr8288r74bcF2p4GkfuoMnGYxABhvwTdnSQybaw4ZjzRQgS/tpE/JgayEqg==", "shasum": "42c418685eaaaf44c010aa8d9dfb4c16ff53ff05", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/manager-node/-/manager-node-4.4.2.tgz", "fileCount": 104, "unpackedSize": 304582, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDn62/csiKaQlNCno8VbFL57Vq779odQsQuCaSYonApAAIhAKjZTE4+9gun1vfHJcpJKZAODiU5uohOZYeT4a+un40u"}]}, "directories": {}, "dependencies": {"@cloudbase/database": "^0.6.2", "archiver": "^3.1.1", "cos-nodejs-sdk-v5": "^2.14.0", "del": "^5.1.0", "fs-extra": "^11.3.0", "https-proxy-agent": "^5.0.1", "lodash": "^4.17.21", "make-dir": "^3.0.0", "micromatch": "^4.0.2", "node-fetch": "^2.6.0", "query-string": "^6.8.3", "unzipper": "^0.12.3", "walkdir": "^0.4.1"}, "devDependencies": {"@lerna/create-symlink": "^6.4.1", "@types/fs-extra": "^11.0.4", "@types/jest": "^24.0.18", "@types/node": "^12.7.4", "@types/node-fetch": "^2.5.0", "@types/unzipper": "^0.10.11", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "eslint": "^7.6.0", "eslint-config-alloy": "^3.7.4", "husky": "^3.0.5", "jest": "^24.9.0", "lint-staged": "^9.2.5", "rimraf": "^3.0.0", "ts-jest": "^24.1.0", "typescript": "^5.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-07-22T08:26:52.712Z", "time": {"created": "2019-09-19T11:36:51.454Z", "modified": "2025-07-22T08:26:52.712Z", "0.1.0-0": "2019-09-19T11:36:51.895Z", "0.1.0-1": "2019-09-20T02:48:29.962Z", "0.1.0-2": "2019-09-24T08:22:14.062Z", "0.1.0-3": "2019-09-24T08:32:34.484Z", "0.1.0-4": "2019-09-25T06:48:17.344Z", "0.1.0-5": "2019-09-27T12:33:07.516Z", "0.1.0-6": "2019-10-11T03:12:34.470Z", "0.1.0-7": "2019-10-11T05:29:13.209Z", "0.1.0-8": "2019-10-22T02:10:54.170Z", "0.1.0-9": "2019-10-22T04:27:28.930Z", "0.1.0-10": "2019-10-22T07:28:27.237Z", "1.0.0": "2019-10-31T02:20:29.849Z", "1.0.1": "2019-11-01T05:04:40.053Z", "1.1.0": "2019-11-26T02:45:35.224Z", "1.1.1": "2019-11-26T10:00:12.170Z", "1.1.2": "2019-11-26T10:03:08.579Z", "1.2.0": "2019-11-27T04:29:00.148Z", "1.2.1": "2019-12-05T06:29:35.197Z", "1.2.2-beta": "2019-12-05T07:27:28.197Z", "1.2.2": "2019-12-05T08:48:16.910Z", "1.3.0-beta": "2019-12-09T01:46:21.699Z", "1.3.0-beta.1": "2019-12-09T07:13:55.851Z", "1.3.0": "2019-12-11T09:32:03.103Z", "1.3.1-beta": "2019-12-11T11:02:13.251Z", "2.0.0": "2020-01-06T09:22:38.946Z", "2.1.0": "2020-01-13T06:43:46.237Z", "2.1.1": "2020-01-15T09:47:13.933Z", "2.2.0-beta": "2020-01-17T09:39:45.455Z", "2.2.0-beta.1": "2020-01-17T11:54:17.959Z", "2.2.1": "2020-02-13T09:15:19.432Z", "2.3.0": "2020-03-03T13:11:04.328Z", "2.3.1": "2020-03-06T06:58:17.773Z", "2.3.2": "2020-03-06T09:18:20.235Z", "2.3.3": "2020-03-09T11:34:05.363Z", "2.3.4": "2020-03-09T11:48:39.907Z", "2.4.0": "2020-03-16T13:04:08.448Z", "2.4.1": "2020-03-24T02:27:08.242Z", "3.0.0-beta": "2020-03-30T03:27:58.502Z", "3.0.0-beta.1": "2020-04-02T12:09:36.434Z", "3.0.0": "2020-04-09T02:39:43.392Z", "3.1.0": "2020-04-14T07:18:46.897Z", "3.1.1": "2020-04-15T03:04:14.688Z", "3.1.2": "2020-04-16T08:06:39.606Z", "3.1.3": "2020-04-17T07:19:59.511Z", "3.2.0": "2020-04-24T05:22:01.042Z", "3.2.1": "2020-04-28T02:35:58.478Z", "3.2.2": "2020-04-30T17:47:30.398Z", "3.2.3": "2020-05-11T02:04:10.137Z", "3.2.4": "2020-05-13T02:38:58.482Z", "3.3.0": "2020-05-21T11:07:05.137Z", "3.3.1": "2020-05-27T09:37:20.558Z", "3.3.2": "2020-06-19T09:44:01.370Z", "3.4.0": "2020-07-09T08:17:48.557Z", "3.4.1": "2020-07-09T11:22:33.254Z", "3.4.2": "2020-08-03T07:56:33.554Z", "3.5.0": "2020-08-04T03:04:21.217Z", "3.6.0": "2020-08-13T07:23:15.292Z", "3.7.0": "2020-08-18T06:50:37.010Z", "3.7.1": "2020-08-19T12:22:57.538Z", "3.7.2": "2020-09-02T02:45:08.608Z", "3.8.0-beta": "2020-09-28T12:36:44.447Z", "3.7.3": "2020-10-11T03:57:23.074Z", "3.8.0": "2020-10-13T02:57:33.424Z", "3.8.1-beta": "2020-10-15T13:18:34.324Z", "3.8.1": "2020-10-19T03:14:09.403Z", "3.8.2": "2020-12-14T03:51:37.416Z", "3.8.3": "2021-01-22T02:23:19.418Z", "3.9.0": "2021-01-26T08:37:12.793Z", "3.8.4": "2021-04-12T09:04:34.998Z", "3.8.5": "2021-04-21T06:53:48.090Z", "3.8.6": "2021-04-21T07:08:02.724Z", "3.9.1": "2021-04-22T09:17:39.692Z", "3.9.2-beta": "2021-06-11T09:08:24.738Z", "3.9.2": "2021-06-15T03:30:46.312Z", "3.9.3": "2021-06-22T03:47:25.464Z", "3.9.4-beta": "2021-06-29T08:15:29.366Z", "3.9.5-beta": "2021-10-14T07:18:45.552Z", "3.9.5": "2021-10-15T02:51:39.804Z", "3.10.0": "2021-11-03T03:11:02.059Z", "3.11.0": "2021-11-03T04:49:28.797Z", "3.11.1-beta": "2021-11-16T10:32:50.256Z", "3.12.0": "2021-12-01T11:43:56.378Z", "3.12.1-beta": "2021-12-23T01:57:52.588Z", "3.12.1": "2021-12-23T03:29:59.619Z", "4.0.0": "2022-06-02T04:35:05.510Z", "4.0.1": "2022-06-22T09:15:16.254Z", "4.1.0": "2022-08-16T01:25:48.267Z", "4.1.1": "2022-11-01T01:55:22.630Z", "4.1.2": "2022-11-01T08:02:58.307Z", "4.1.3": "2022-11-02T02:11:53.461Z", "4.2.0": "2022-12-01T02:52:47.969Z", "4.2.1": "2023-08-29T04:46:42.943Z", "4.2.2": "2023-10-09T10:10:08.061Z", "4.2.3": "2023-10-26T04:34:36.951Z", "4.2.4": "2023-11-06T10:28:27.349Z", "4.2.5": "2023-11-06T11:09:15.461Z", "4.2.6": "2023-11-13T04:48:10.298Z", "4.2.7": "2023-11-13T06:28:51.772Z", "4.2.8": "2023-11-14T04:34:00.167Z", "4.2.9": "2024-01-23T03:51:07.892Z", "4.2.10": "2024-12-09T06:45:18.236Z", "4.2.12": "2025-06-20T02:30:29.822Z", "4.3.0": "2025-06-20T03:21:28.018Z", "4.3.1": "2025-06-24T08:57:30.299Z", "4.3.2": "2025-06-25T12:49:39.695Z", "4.3.3": "2025-07-17T08:59:11.831Z", "4.3.4": "2025-07-19T08:47:16.218Z", "4.4.0": "2025-07-21T06:57:37.679Z", "4.4.1": "2025-07-22T07:04:46.477Z", "4.4.2": "2025-07-22T08:26:52.398Z"}}