/* 执行可视化组件样式 */
.execution-visualizer {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 执行头部 */
.execution-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.execution-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.execution-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 进度条 */
.execution-progress {
    padding: 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    transition: width 0.5s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

/* 时间线 */
.execution-timeline {
    padding: 20px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f5f9;
}

.timeline-header h4 {
    margin: 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
}

.timeline-stats {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #64748b;
}

.timeline-stats span {
    background: #f1f5f9;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.timeline-content {
    max-height: 400px;
    overflow-y: auto;
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: #64748b;
}

.welcome-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.welcome-message h4 {
    margin: 0 0 8px 0;
    color: #1e293b;
    font-size: 18px;
}

.welcome-message p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* 时间线步骤 */
.timeline-step {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.timeline-step.running {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.timeline-step.completed {
    border-color: #10b981;
    background: #f0fdf4;
}

.timeline-step.failed {
    border-color: #ef4444;
    background: #fef2f2;
}

.step-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.step-status {
    margin-right: 12px;
}

.status-icon {
    font-size: 20px;
    display: block;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-info {
    flex: 1;
}

.step-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
    margin-bottom: 2px;
}

.step-agent {
    font-size: 12px;
    color: #64748b;
}

.step-time {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

.step-content {
    padding: 16px;
}

.step-description {
    color: #374151;
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.step-task {
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    color: #475569;
    margin-bottom: 12px;
}

.step-result {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.result-content h5 {
    margin: 0 0 8px 0;
    color: #1e293b;
    font-size: 13px;
    font-weight: 600;
}

.result-text {
    font-size: 12px;
    color: #475569;
    line-height: 1.4;
    max-height: 100px;
    overflow-y: auto;
}

/* 结果面板 */
.execution-results {
    padding: 20px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.results-header h4 {
    margin: 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
}

.format-selector select {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    background: white;
    color: #374151;
}

.results-summary {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e2e8f0;
}

.result-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.result-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.result-item h5 {
    margin: 0 0 8px 0;
    color: #1e293b;
    font-size: 14px;
    font-weight: 600;
}

.result-item .result-content {
    font-size: 12px;
    color: #475569;
    line-height: 1.4;
    background: #f8fafc;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .execution-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .execution-controls {
        width: 100%;
        justify-content: center;
    }
    
    .timeline-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .timeline-stats {
        justify-content: center;
    }
    
    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .step-time {
        align-self: flex-end;
    }
}

/* 滚动条样式 */
.timeline-content::-webkit-scrollbar {
    width: 6px;
}

.timeline-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.timeline-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.timeline-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.timeline-step {
    animation: fadeIn 0.3s ease;
}

/* 状态动画 */
.timeline-step.running .status-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
} 