"""
共享记忆模块
为所有Agent提供统一的共享记忆实例
"""
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.storage.sqlite import SqliteStorage

# 全局共享的Memory实例
_shared_memory = None
_agent_storages = {}

def get_shared_memory() -> Memory:
    """获取全局共享的Memory实例"""
    global _shared_memory
    if _shared_memory is None:
        memory_db = SqliteMemoryDb(
            table_name="shared_user_memories",  # 所有Agent共享同一个记忆表
            db_file="tmp/shared_memory.db"
        )
        _shared_memory = Memory(db=memory_db)
    
    return _shared_memory

def get_agent_storage(agent_id: str) -> SqliteStorage:
    """为指定Agent获取独立的存储实例"""
    global _agent_storages
    if agent_id not in _agent_storages:
        _agent_storages[agent_id] = SqliteStorage(
            db_file="tmp/shared_storage.db",
            table_name=f"sessions_{agent_id}"
        )
    
    return _agent_storages[agent_id]