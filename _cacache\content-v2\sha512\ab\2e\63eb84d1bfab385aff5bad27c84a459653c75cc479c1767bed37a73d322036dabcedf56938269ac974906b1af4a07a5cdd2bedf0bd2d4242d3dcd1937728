{"name": "pify", "versions": {"1.0.0": {"name": "pify", "version": "1.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.12.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "9f9082534d0eff1f0bae57501e10c820342a4259", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify", "_id": "pify@1.0.0", "_shasum": "ef1490fdd87eb5f84c411507d72e2b535790bc4e", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ef1490fdd87eb5f84c411507d72e2b535790bc4e", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-1.0.0.tgz", "integrity": "sha512-mPFNR6nxZQGq22n4dAFswgfzD5D365Xe/qaeqc42YIvTNE/EqdIkEDzo41JLLaHKHbW5QCgXavhaAdep22YvjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG9ACk5YHw9hbV3bdE9TmvGHr5GpTccdckMyh9P7PVrPAiBRzBKB9hp0s32ZARJHl5VzQJFSvK/4X81BC323OgKlmA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.1.0": {"name": "pify", "version": "1.1.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.12.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "e8c4e6f46cbd20d4886da5ef748c2c8863fa8767", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify", "_id": "pify@1.1.0", "_shasum": "0b89b22c63d089de78f9668e2148165e85b10674", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0b89b22c63d089de78f9668e2148165e85b10674", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-1.1.0.tgz", "integrity": "sha512-Rgl6kNkUrxL97DzFKn4JNKiHgsM9tXERre4sCXBAr1bgXw0AJWG1azU80SB3zlZETK0eRlw1vwJ8EK0Wr/wrRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnIsiIIS8a+iF1c5Y2uhAQgf709pDb1w9W0HOHmjGEGAiEAhTrd7UO8be796N/sdUxdYIVH4gvqj47SumFe/VJr3IA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.1.1": {"name": "pify", "version": "1.1.1", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "e40e7d47d41acaf51dcea2299e583fea6c0157a8", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@1.1.1", "_shasum": "d06afb0e685893d6c16566d1f819b5ede2b33a29", "_from": ".", "_npmVersion": "2.14.1", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d06afb0e685893d6c16566d1f819b5ede2b33a29", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-1.1.1.tgz", "integrity": "sha512-GszOBZhe2498Hdi/VjUV8XpB06jmfGNoFUwDMjU7l0Tmo402oR7j8feEovEWrByf/dqdCwkJhgDcMGfFUjnx+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeBrWWUsKVqQKTzGMXwR4U1as6jCsFZnC+bIDRhokRrAIhAKnNAEkpy8nQEqy+epIDnosDGjUz0nb1iEEC+MSnfb0W"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "2.0.0": {"name": "pify", "version": "2.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "1498ffad69776d3906f0ef177f861d8e82ae8be7", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify", "_id": "pify@2.0.0", "_shasum": "7caa2a72d2c4a5ebb9f4db1cfcc5bf5ea0c10264", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7caa2a72d2c4a5ebb9f4db1cfcc5bf5ea0c10264", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-2.0.0.tgz", "integrity": "sha512-n2GR424pXP+xN7dic868+znTmaIztPwoyC3esfAX6klEimLSGp3rH9DLkNKlsACEH5ajeO273Q+fwEKWITYp4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw7EaNnyHG/4xg9l4XaLqib00nInYlnncxrWsbrU1NBQIgZPk6bWnom2IRWGMiScE1WTVcEqCtBjvVseCSaxXz3Yw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "2.1.0": {"name": "pify", "version": "2.1.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "9b0d12e83b849f4b75264cf08e0bfcf93c85d346", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify", "_id": "pify@2.1.0", "_shasum": "093996c0190b737612e3519fd24c7147166fcb6e", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "093996c0190b737612e3519fd24c7147166fcb6e", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-2.1.0.tgz", "integrity": "sha512-mgpNTdpuc8Hn3hibH/BPHxKIDrn1L5o90G6PPO+x5maveGsJvzACg6vf0dftPoGnvmV+lBLdBRVTktNV0mYcug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDXbfEGDhiPgn8pEQZ+YVY96KEfiuj3CCmPRuwvEaf7QIhAIKjHD89h3CuGRyWfYct9asBD8dqCnJ9fiXS5Zv1W3Oh"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "2.2.0": {"name": "pify", "version": "2.2.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "xo": "*"}, "gitHead": "665ffa9dcac562c59187279b0220300aa21740b0", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@2.2.0", "_shasum": "c65e870246c78b5a4ce6c0a6f35048c9aecd6cff", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c65e870246c78b5a4ce6c0a6f35048c9aecd6cff", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-2.2.0.tgz", "integrity": "sha512-HeG<PERSON>JkLHr5tzSSlVNuG6Wqe4X0OPmSBS/N3RlbeU62UNuASJoUFEbQAOURN/+Orz2NqlsTJVm+Xu+DESKMaeuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCL20atvczQ3QI0e3mDJudzWwb1WdN/Tbcl9hxMiNvCggIgU9TVLN55P3fdWObWX93di92M0uBdhvq3B/1nAvVuSYg="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "2.3.0": {"name": "pify", "version": "2.3.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava && npm run optimization-test", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "v8-natives": "0.0.2", "xo": "*"}, "gitHead": "2dd0d8b880e4ebcc5cc33ae126b02647418e4440", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify", "_id": "pify@2.3.0", "_shasum": "ed141a6ac043a849ea588498e7dca8b15330e90c", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ed141a6ac043a849ea588498e7dca8b15330e90c", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-2.3.0.tgz", "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCu/g9lrEE67d2v3wLVNvkStknaDFen1TZkm16zscpLWQIhAJG9ayk5Q8ZydAWSwau8QTxBpGdfV8ghHm/hq3y+hjvX"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "3.0.0": {"name": "pify", "version": "3.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava && npm run optimization-test", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "*", "pinkie-promise": "^2.0.0", "v8-natives": "^1.0.0", "xo": "*"}, "gitHead": "e64328eb378e2ecd6bf8c0eb40aa3277680aaff4", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@3.0.0", "_shasum": "e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-3.0.0.tgz", "integrity": "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD02HA3YTJXEO+WOnfLqGICFJ9/urzFKbElq+xrFhoHSQIgalZWixlwutJw/7Ef4vwzBx7ORunkwe7LNP1ZfGngfSw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify-3.0.0.tgz_1495952727749_0.4502641425933689"}, "directories": {}, "contributors": []}, "4.0.0": {"name": "pify", "version": "4.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "*", "pinkie-promise": "^2.0.0", "v8-natives": "^1.1.0", "xo": "*"}, "gitHead": "67f6c41e8deb55b12ff7bbd96a689eb464a8a0ac", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@4.0.0", "_npmVersion": "6.3.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zrSP/KDf9DH3K3VePONoCstgPiYJy9z0SCatZuTpOc7YdnWIqwkWdXOuwlr4uDc7em8QZRsFWsT/685x5InjYg==", "shasum": "db04c982b632fd0df9090d14aaf1c8413cadb695", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-4.0.0.tgz", "fileCount": 4, "unpackedSize": 6765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbEZxCRA9TVsSAnZWagAA7D8P/1CZgTqUCZJomco/fQ+y\nQA7/2NQ72MV2DHXmSBo/2E4VrP3U6ag8hnWYFuT1HLEfo5xXyf74QNQulHU9\nx/rHy+ZzJDDePTW+T1cXz+bcP5mo9knZf+OsLJN18Cy0n+C+DA6TO2v5EsWk\naA+e765et0VYh57RY93+90pZChNnBz838nHxB7YIaD95vftsbhV5hYNYrQaV\nRqS0PfARYewd04nCTWvP95b1iOhiJ3yoO4XQE+hdI4s+3VTqVniey3ccBkUe\n4E5MbuJhcQzOq1lA9MGftn05EMCLwA+ttAF1+V3YA1tG8wn4qpwG6rMbGKna\n3H77dQ/MdgQhsOBf2FMCkWVW5JTNnAecXpJ5jlF180D2OL7jB/K6f8TzTmUh\n8vd49L97uECzp/TH7EsqRIofRTXssTTrTWP1/uIYEsS0VjpqMr/JF6RXw7Dj\nlwVgQ5JkrkQL8wV5q1Li3+yQOIsVlIQcu+JnK98rVuShzSCWNTHyI48yN6Y0\nkShA3fQnFRRCknOBnpa5TMjYwzissxWa2NQjn0mF5kFMd3ehM2pRByWHzVZq\n8LZ0KJPBwrcx7x9NtwxO786ZJ4KIJLK8fEkqg0GUsppb0eHmxHTUVODQhp1u\n1EI7QUMBPd4fY12gkfHqYS5dXAhdPBqfoZ7Rms60dC1o8vopNQuhcewQt+Qp\nbaEX\r\n=kvJa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF7rJnJM5rD+O/uO9QbNE+AaZY2hPJpBXbOup7NYQ+TJAiBW2Ms3GX0LEZ0vQ6CeV7HjRDjDcnbGaNCLUe4KjMwIXQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify_4.0.0_1533822577421_0.49626587296341373"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.1": {"name": "pify", "version": "4.0.1", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^0.25.0", "pinkie-promise": "^2.0.0", "v8-natives": "^1.1.0", "xo": "^0.23.0"}, "gitHead": "ef763593863d2a55df41e7f25147fa989f958fa1", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==", "shasum": "4b2cd25c50d598735c50292224fd8c6df41e3231", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-4.0.1.tgz", "fileCount": 4, "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzcmUCRA9TVsSAnZWagAAIPAQAIXn1sdrK8mkb8EfIf/K\n/BtGC3tAjzE09DbHdPlCJCEAkxX5S/5CChu1CXGp1no/zG+kZ2u3GbQzc8uN\nlVDwO8XvYEeTKZQAg5q0uhoNuUbq4aRFlNHqDtsNBHM9yv18MqzET8EtGS8V\nc3BedMSqgjAEGMysAwp3JfcoIBACzScnWPsEggGvtCyKDV52cZU1B/eid2wc\nO3QSa5oViWsUOhhBYYrj8wjH+MbGkN+Z7QjqEnPVmAAYlMkXhJv16K6dRteT\nx0BeElBrhRmCuGIEnY1ChDqdumuzkes1hoBvyQY7TIQVcQ06gV1wxYSXzjYz\nZLptbUKafCnyvAlAD6auQrVlCpp2zU62CfE/AgOt9ZtIuAGP6h0fSI7V4Ja7\n/IAAUCqkGrPlzgg5RSm+pbQ9x5jg0UtjKGMR6tg+OfYmNFVc1IeiSFo/DT9c\nbVCGOu2KPF9Ks7mg9j0LgnQsypakopWLhXP+Sxv7R2onHXaGiHSMdK9MzihF\ngBTk8iWzmcJskLCUpsAbsKnrsTU6KpeEvPCODhOiDnvDYEEvg6JsGSO4DNNE\nYGY/Igr6cLInyUnOo5zcjzMFFoXt+IZ2dbiJS6mV/fraVemYPNvsftUdBIdO\ngUix8lHeWX7K42O/VIY6KzowXzCt0F8b4NsNasNW2Mfx7Ggt22cIrbt0Jiga\nUK6i\r\n=IG39\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwGtirA8BTw1bf3RkyrgDVDhVUiVKMcTMTyGyJTgL9FgIhANreP746Z5O+smhXLVIaE8CwxvAycVs7Ckv+dxlWsi8L"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify_4.0.1_1540213139409_0.804611630545673"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.0": {"name": "pify", "version": "5.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "keywords": ["promisify", "callback", "promise", "promises", "denodify", "denodeify", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^2.4.0", "pinkie-promise": "^2.0.0", "v8-natives": "^1.1.0", "xo": "^0.26.1"}, "gitHead": "5ef286ffc5310e7dbe99eee1891378ffde962ae9", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@5.0.0", "_nodeVersion": "13.1.0", "_npmVersion": "6.12.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==", "shasum": "1f5eca3f5e87ebec28cc6d54a0e4aaf00acc127f", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-5.0.0.tgz", "fileCount": 4, "unpackedSize": 8867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRso8CRA9TVsSAnZWagAAQnwP/1dkXjy00iCHL3ql+7oi\n8yNz+NolZs+hxeo6mgJiwoOd1NQKwDrF1aKuzzjVWK34LtjejHjadhHPUeLt\ns9mA8O62wtE+F8Q2JDsRcUcsc/uVkncJKc74n0x5oPoyqk4qhfWgV6YlOfWx\naPkzncS+uzzaYcUfI+m4EbOX32UEzTt1ljCL1WVOcRrt8jCQMfndfNDRUAjl\nAEaMroH8PgEbVA/dwM1D+oSWSKSbtvz/Lyn+F1T3hv0AmLBGD0sXoIi82teR\ngWfi9+f/l1wS4ksD+ahxvkHKITuPKQ5BR33bJDz0rKcPMQShJdLSDqhI3Ud4\no9Rs5rNrkiM/Lo0LNDzL2/DK/mvZk6YoHGgph8ziDuhRHUhcMVNO+cn6Eop1\n2smCE/0dHPtAh3onY9NP+96U0yvqUMKZOHbfqeY0JBErobM9alj4dJidKAA5\nUG2CCscVMwjUpJxLJmBqW++eUU/nEPYMnYJhcvLDyNp9PHUxgi7QBf3buWam\n/zZqXxnkgaAQPenZH1y+fWdR6vP4XJfThKgAuZDOG4B1mEekDPjEX2rHeLp2\nXmIVJVg/wU2oB54s4Y73ABlHnBCqXpUf767ue6rgEqQloYu/zw86yROr/q10\nGMtfhLlyuYq3BO19/pEWh8VqQJ8iN14HSuDkG33PVQPG6muTJFMJdT+Wt//y\nL3ay\r\n=EzUm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICd5Q7swdR+uozw2AWy2HkuLmpUgJsZfg+1aIbjxQOiGAiApyQvEYszkrm9Wc5iJFDdDxxGGsfkLpgqD4B3KFlkBZg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify_5.0.0_1581697595574_0.40959682391034624"}, "_hasShrinkwrap": false, "contributors": []}, "6.0.0": {"name": "pify", "version": "6.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "keywords": ["promisify", "callback", "promise", "promises", "denodify", "denodeify", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^4.3.0", "pinkie-promise": "^2.0.1", "v8-natives": "^1.2.5", "xo": "^0.49.0"}, "gitHead": "513b0d57748e6f0a3eda4b14cbd0710a7fd2147e", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@6.0.0", "_nodeVersion": "14.19.2", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-eDlMwqmYtd+rZMU6rCL05NT47Desr/jjfPkBPyqCg8PAyqbt+wxFLz1EQ5XvsfLFNlUEFmBj2FkNUvGF9BJLkQ==", "shasum": "b7e212f97963ed9b78653fb3c449801861a9f0ad", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-6.0.0.tgz", "fileCount": 4, "unpackedSize": 9536, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCR0btkqnwpn4WZQ+7SGwdKpIYF9xdSQpuVgvZAVJa9BAIgb0FUfdNPjXlxgw+vrala394eQFOUK41/dYKHgelkxag="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinfSNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXkQ//Ta0FPEvKSoaLloJ+lutWnq72uNM0X+ippaJo8IG7XGnDAb1g\r\nFMtPPY9j7glu4Bfna2IMPTZ3sL1r0YCFncsmOsefNwBlNcUkX42QkVYvhkad\r\n3Pc4SIrf9Fd148LPtXCTqp/6IafANyvM7dhdvWZB/NVZ0cYCNZ29B7TwgNJm\r\n2UiFRy4Nd3/skq567n4FZe8EAkB+WYs3Eo9oXdi1hTYkJJPrVFUQy+mf7zr3\r\nfM4rh3iksUEuAOY5xBNR+BFS/kQqVlBWqprrSFsFhR5eoHzgV0OoAxyX13bN\r\nL3mrTS2qT9aenfEk7OD3l/DvzQKYoRFU4gV9Q/4mmhlXdkmKehFWzPwJ20ip\r\niEzO9q4nji4h3E1xpTZHfJ1HQqBPyuy62V5OaDnL6nWWIcg42O4AeLJMyOZ0\r\n5haRoBo2+PouypI7B2oVZLuBYss4c466MlhTZhNfn5kKCEv54UAmd8Ee6NkM\r\n6edH+o2eiS/AVQ9xy1NQGqsKwMDSmAkbqdqqgpkFEpEzMK7zbVozraPf4BfW\r\nJYZE8YFoj8hastKOFV1DTY1W/MlkwGjS2svDVKcsvjjV2T0YG4AY1h3Ojnyh\r\n9zzKkSxEFPKgaSZva8xNhjZ4hbiQuOP/nkWluitLa//Qe483s+wiggmqw/YN\r\n9o1jt+9OU0/yuAZmsceLq+C1/nDa9S7dKXc=\r\n=uDq7\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify_6.0.0_1654518925633_0.21225741790643826"}, "_hasShrinkwrap": false, "contributors": []}, "6.1.0": {"name": "pify", "version": "6.1.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "keywords": ["promisify", "callback", "promise", "promises", "denodify", "denodeify", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^4.3.3", "pinkie-promise": "^2.0.1", "tsd": "^0.23.0", "v8-natives": "^1.2.5", "xo": "^0.52.3"}, "gitHead": "749ba6ad3a5bf746dd9a780d914013764515e554", "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "homepage": "https://github.com/sindresorhus/pify#readme", "_id": "pify@6.1.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-KocF8ve28eFjjuBKKGvzOBGzG8ew2OqOOSxTTZhirkzH7h3BI1vyzqlR0qbfcDBve1Yzo3FVlWUAtCRrbVN8Fw==", "shasum": "db9f2ebfba65f0bc144db65ae84d1a486ab72909", "tarball": "https://mirrors.cloud.tencent.com/npm/pify/-/pify-6.1.0.tgz", "fileCount": 5, "unpackedSize": 13592, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoz147EB1joEfrp8pw9UEcHXok83Lojjol0/cebn5oPgIhAL6YMZBYE+wCAvcF/m/KgCMe6GwbRhYcGoMR7MNg7aJJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEf05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFJQ/+KJhPA8F8f4qV/ygTyykKJTzqeNwkWrSpErmvE+HLxLbW4Xhm\r\nocc75pBwu2CpZccpCdVZ4iL5Ym21lFByTWMNdbveLQjgOAGR/8Y9I+xnSRAN\r\n1RMU7SP38g0cksFIzmpsDJuWMAawUlSxQLQVb2XXKqmL+x+HzZ5V5hVezAhz\r\nmUj2Ph8wliFpWXUAvvJcNELs+sJykL3xuwOvsBWjKQAft816XR+1IAqK/kJE\r\nByFIwaRkcoR1W0oLwNWDfA7lmrcqevAz+HTHBMIvAhURs5pJ3RGT42XgObXQ\r\nOBohXf2M/VxCl44HknG/MKRA6zyqiTfWRm2UNXgGwexNurymxKFokd2mAMjw\r\nEvTGa6HUENOrcyfUNY5YVaIWMcSHroEZ+txAjA/2gOYwKmMzmnyIKDpfZqsw\r\nFHynjYf15YS74xHECk8Uy1YcOvXu2xK2TJSE0HnUoVT1Rw8f/5xdYcti7yPO\r\nuA1JDigduhnfYKv9UQ7FvsQC1fFZuEkHG8I1ZYvei5w/m6M2ud3ZLjKQ70qe\r\nZ0Y5FZBDa7NTo7QKY8XLHOF9Au8jEsi6O4GBD7d071Lt2/85fZoWVoHTVXgI\r\n//yJoIJrXf+xYEuP/WnxV/7vZtBbzn4NpT2U3Hc2/cBN6KkrTylfN/N1RQGu\r\nL8TpG7+KKTSwAiXhuGnnJ6KNZYxitErNQWc=\r\n=iIRp\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pify_6.1.0_1662123321806_0.7100689797782123"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2023-06-17T00:08:09.166Z", "created": "2015-08-29T08:15:22.672Z", "1.0.0": "2015-08-29T08:15:22.672Z", "1.1.0": "2015-08-30T04:05:41.513Z", "1.1.1": "2015-09-01T16:53:09.168Z", "2.0.0": "2015-09-04T07:45:20.325Z", "2.1.0": "2015-09-08T19:54:14.378Z", "2.2.0": "2015-09-11T07:09:26.197Z", "2.3.0": "2015-10-26T13:16:18.238Z", "3.0.0": "2017-05-28T06:25:28.083Z", "4.0.0": "2018-08-09T13:49:37.494Z", "4.0.1": "2018-10-22T12:58:59.524Z", "5.0.0": "2020-02-14T16:26:35.672Z", "6.0.0": "2022-06-06T12:35:25.821Z", "6.1.0": "2022-09-02T12:55:21.948Z"}, "users": {}, "dist-tags": {"latest": "6.1.0"}, "_rev": "5339-b2e7d3eb1a3105a0", "_id": "pify", "readme": "# pify\n\n> Promisify a callback-style function\n\n## Install\n\n```sh\nnpm install pify\n```\n\n## Usage\n\n```js\nimport fs from 'fs';\nimport pify from 'pify';\n\n// Promisify a single function.\nconst data = await pify(fs.readFile)('package.json', 'utf8');\nconsole.log(JSON.parse(data).name);\n//=> 'pify'\n\n// Promisify all methods in a module.\nconst data2 = await pify(fs).readFile('package.json', 'utf8');\nconsole.log(JSON.parse(data2).name);\n//=> 'pify'\n```\n\n## API\n\n### pify(input, options?)\n\nReturns a `Promise` wrapped version of the supplied function or module.\n\n#### input\n\nType: `Function | object`\n\nCallback-style function or module whose methods you want to promisify.\n\n#### options\n\nType: `object`\n\n##### multiArgs\n\nType: `boolean`\\\nDefault: `false`\n\nBy default, the promisified function will only return the second argument from the callback, which works fine for most APIs. This option can be useful for modules like `request` that return multiple arguments. Turning this on will make it return an array of all arguments from the callback, excluding the error argument, instead of just the second argument. This also applies to rejections, where it returns an array of all the callback arguments, including the error.\n\n```js\nimport request from 'request';\nimport pify from 'pify';\n\nconst pRequest = pify(request, {multiArgs: true});\n\nconst [httpResponse, body] = await pRequest('https://sindresorhus.com');\n```\n\n##### include\n\nType: `Array<string | RegExp>`\n\nMethods in a module to promisify. Remaining methods will be left untouched.\n\n##### exclude\n\nType: `Array<string | RegExp>`\\\nDefault: `[/.+(?:Sync|Stream)$/]`\n\nMethods in a module **not** to promisify. Methods with names ending with `'Sync'` are excluded by default.\n\n##### excludeMain\n\nType: `boolean`\\\nDefault: `false`\n\nIf the given module is a function itself, it will be promisified. Enable this option if you want to promisify only methods of the module.\n\n```js\nimport pify from 'pify';\n\nfunction fn() {\n\treturn true;\n}\n\nfn.method = (data, callback) => {\n\tsetImmediate(() => {\n\t\tcallback(null, data);\n\t});\n};\n\n// Promisify methods but not `fn()`.\nconst promiseFn = pify(fn, {excludeMain: true});\n\nif (promiseFn()) {\n\tconsole.log(await promiseFn.method('hi'));\n}\n```\n\n##### errorFirst\n\nType: `boolean`\\\nDefault: `true`\n\nWhether the callback has an error as the first argument. You'll want to set this to `false` if you're dealing with an API that doesn't have an error as the first argument, like `fs.exists()`, some browser APIs, Chrome Extension APIs, etc.\n\n##### promiseModule\n\nType: `Function`\n\nCustom promise module to use instead of the native one.\n\n## FAQ\n\n#### How is this different from Node.js's [`util.promisify`](https://nodejs.org/api/util.html#util_util_promisify_original)?\n\n- Pify existed long before `util.promisify`.\n- Pify is [faster](https://github.com/sindresorhus/pify/issues/41#issuecomment-429988506).\n- Pify supports wrapping a whole module/object, not just a specific method.\n- Pify has useful options like the ability to handle multiple arguments (`multiArgs`).\n- Pify does not have [magic behavior](https://nodejs.org/api/util.html#util_custom_promisified_functions) for certain Node.js methods and instead focuses on predictability.\n\n#### How can I promisify a single class method?\n\nClass methods are not bound, so when they're not called on the class itself, they don't have any context. You can either promisify the whole class or use `.bind()`.\n\n```js\nimport pify from 'pify';\nimport SomeClass from './some-class.js';\n\nconst someInstance = new SomeClass();\n\n// ❌ `someFunction` can't access its class context.\nconst someFunction = pify(someClass.someFunction);\n\n// ✅ The whole class is promisified and the `someFunction` method is called on its class.\nconst someClassPromisified = pify(someClass);\nsomeClassPromisified.someFunction();\n\n// ✅ `someFunction` is bound to its class before being promisified.\nconst someFunction = pify(someClass.someFunction.bind(someClass));\n```\n\n#### Why is `pify` choosing the last function overload when using it with TypeScript?\n\nIf you're using TypeScript and your input has [function overloads](https://www.typescriptlang.org/docs/handbook/2/functions.html#function-overloads), then only the last overload will be chosen and promisified.\n\nIf you need to choose a different overload, consider using a type assertion:\n\n```ts\nfunction overloadedFunction(input: number, callback: (error: unknown, data: number => void): void\nfunction overloadedFunction(input: string, callback: (error: unknown, data: string) => void): void {\n\t/* … */\n}\n\nconst fn = pify(overloadedFunction as (input: number, callback: (error: unknown, data: number) => void) => void)\n// ^ ? (input: number) => Promise<number>\n```\n\n## Related\n\n- [p-event](https://github.com/sindresorhus/p-event) - Promisify an event by waiting for it to be emitted\n- [p-map](https://github.com/sindresorhus/p-map) - Map over promises concurrently\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-pify?utm_source=npm-pify&utm_medium=referral&utm_campaign=readme\">Get professional support for 'pify' with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>", "_attachments": {}}