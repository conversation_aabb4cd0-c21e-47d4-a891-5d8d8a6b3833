"""
统一规划Agent
系统的大脑，负责任务分析、规划和执行协调
"""
import asyncio
import json
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from agno.models.openai.like import OpenAILike

from .shared_memory import get_shared_memory, get_agent_storage
from .agent_caller_tool import AgentCallerTool

# 暂时简化导入，避免路径问题
# from agno.core.task_analyzer import TaskAnalyzer
# from agno.core.agent_selector import AgentSelector
# from agno.core.execution_monitor import ExecutionMonitor, ExecutionPlan

AGENT_ID = "unified_planning_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "AI智能助手",
    "brief": "智能任务分析、规划和协调执行的统一入口",
    "category": "agent",
    "avatar": "touxiang/997.png",
    "tags": ["规划", "协调", "任务分析", "多Agent"],
    "capabilities": [
        "任务意图识别",
        "复杂度评估", 
        "Agent智能选择",
        "执行过程监控",
        "结果整合输出"
    ],
    "supported_formats": ["html", "markdown", "json"]
}

def create_agent():
    """创建统一规划Agent实例"""
    # 使用共享记忆系统
    shared_memory = get_shared_memory()
    agent_storage = get_agent_storage(AGENT_ID)
    
    # 导入Agent调用工具
    from .agent_caller_tool import AgentCallerTool
    agent_caller = AgentCallerTool()
    
    return Agent(
        name="统一规划助手",
        model=OpenAILike(
            id="Doubao-Seed-1.6-flash-250715",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是智能任务规划和协调专家，能够分析用户需求并制定执行计划，并实际调用专业Agent完成任务。",
        instructions="\n".join([
            "你是统一规划助手，负责理解用户需求并协调多个专业Agent完成任务。",
            "你的主要职责包括：",
            "1. 分析用户输入，识别任务意图和复杂度",
            "2. 制定详细的执行计划和Agent分配方案", 
            "3. 实际调用专业Agent执行任务",
            "4. 监控任务执行过程，协调Agent间的协作",
            "5. 整合执行结果，为用户提供完整的解决方案",
            "",
            "🔧 你拥有call_agent工具，可以调用以下专业Agent：",
            "- xiaohongshu_copywriting_agent: 小红书文案专家",
            "- week_report_agent: 周报专家",
            "- daily_report_agent: 日报专家", 
            "- month_report_agent: 月报专家",
            "- schedule_agent: 日程安排专家",
            "- composition_agent: 作文写作专家",
            "- official_document_writing_agent: 公文写作专家",
            "- new_media_copywriting_agent: 新媒体文案专家",
            "- xiaohongshu_creation_agent: 小红书创作专家",
            "",
            "🎯 工作流程：",
            "1. 分析用户需求，确定最适合的专业Agent",
            "2. 使用call_agent工具调用相应的Agent",
            "3. 监控执行过程并提供进度反馈",
            "4. 整合结果并优化输出格式",
            "",
            "💡 执行原则：",
            "- 对于明确的任务需求，直接调用对应的专业Agent",
            "- 对于复杂任务，可以分步调用多个Agent",
            "- 始终向用户说明执行计划和进度",
            "- 确保最终结果完整、准确、符合用户需求",
            "",
            "示例执行过程：",
            "用户：写一份小红书文案",
            "分析：这是小红书内容创作任务",
            "执行：调用xiaohongshu_copywriting_agent",
            "结果：提供完整的小红书文案内容"
        ]),
        tools=[agent_caller],  # 添加Agent调用工具
        memory=shared_memory,  # 使用共享记忆
        enable_user_memories=True,
        enable_session_summaries=True,
        storage=agent_storage,  # 使用独立存储
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )