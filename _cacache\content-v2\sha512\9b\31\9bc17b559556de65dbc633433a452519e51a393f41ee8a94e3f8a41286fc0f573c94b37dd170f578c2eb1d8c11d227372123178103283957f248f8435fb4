{"name": "babel-plugin-transform-class-properties", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-class-properties", "version": "6.0.2", "description": "## Installation", "dist": {"shasum": "a21676b561205c9a6ae95767a7f32de551872272", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.0.2.tgz", "integrity": "sha512-0HQs+RQOTN2s3/k+GAj6PFRkY/AWB+GjB7XPH10/i1aLy/QCANQw6JHtbhw8V9cZyktNxWZirMGI5zU/SwVppQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBeNf4F2zTWqA+hI8l1J6wApvNpZB4lPlkLxhrPe0bQ+AiB/lXgeAOkJTa4L9zW8Z/aeYQ1S/cpz2fG2Mm8tkiOgcA=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.0.2", "babel-runtime": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-class-properties", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "8644ebe47bd30df7a268f850d16833174b2fb5c8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.0.14.tgz", "integrity": "sha512-6gj1+bKqqYpnXRto+cYpLNYpc2QIoiQj/Yu67raxRi7BFAOas+dR0wYRxbEuaouyvW+DB0E1Q2Vvbaw3KqvzwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDp7sK0s1Q/MJIhEpHz5PGDSzi72q4xS+meE8kywylCsQIgRhUYrZubvCFblOeQ8dU8+9jedU9JQtIDGe5bANvfwBM="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.0.14", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-class-properties", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "602a4e3be59d46ff6517b456b8712dc2dac25785", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.4.tgz", "integrity": "sha512-POTsMOiPNcbsTCsg728nB3r00ylBh5ptCccW5JaOSSUErMpebEZ9kcGfyjV/zesc5OY76hO7g1m5OzWcyQRw1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO7EUfikCW/L2HcDGhwb/MDPAzyzrezY7VwgDjOnBztAIhAODhKT+ORYoPJj9lmk59nRCChc2kqq4c4oEEw9k6Xy5a"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-class-properties", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "43caaf0068bac0fe66e69e374bed61c781dd3058", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.5.tgz", "integrity": "sha512-JOK5O1wI2S6bpgy9io9k1qGBiVJDSxM67i/AfzOtss5GuTrB0D0FNOGX9tcIh9WbnplOoePDEMfRKtV8NtDeaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzKswirMvH2v0s/WCMKQxpA6hliwvQaYMRCZsu7HXLyAiEA0gxhuJIGmHqLwoB3qcTYriifxu3dSq/FoPzJBt8CkoQ="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.5", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-plugin-transform-class-properties", "version": "6.1.10", "description": "## Installation", "dist": {"shasum": "f0135b15c6870f45865c0aec7129dfa88d8490c4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.10.tgz", "integrity": "sha512-gDGojCSdgRRb5OZV0P6WmGeL/sNAvGmmzgrEU2+Kpa6O9Wnhj/QJ/qSmnaEanzCW33K7/wywsZDNktNSMOuRZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLFdQyErz6RNThhIHfgErya98p9jO7dKQGTppOtDLjGAiAHHzCfW2lvW9woW5DeTHxvqh35EUQ/0XWTQjpGRogHmQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.10", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.10"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-class-properties", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "07eeecaf9e753f4cfd5ef393f58b3b73654dc6b8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.17.tgz", "integrity": "sha512-C8tpypMjfpDmDCx1Z1jUl28BHa+WjX/YaTtBNqfwIR1rQ7sNTRTqFn/VLkIfnl1d2xHqrFBs7Z06pZ36w4Ij+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFEffq4pTuI5SeiNO/2m47qVAjUKXOvq5lK4WhS04Yv7AiEA0Umy5oF+kwGrN+ebg+nEGKRhRVxdE1rlbakzQLW1QKU="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.17", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-class-properties", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "474c80dd0d1e1d128d81ce0ddddec7e98c7f1793", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.18.tgz", "integrity": "sha512-9VMKQuJFe4BcI3tkAAxSBncvpDZejJ8/sZbXvMv2LuH7hzjcU9dcYllc5aONAO+tH1318Mj1/2Z28egPWRi+1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnQfqWqWdpPT1v5AMPZ3BR02jQAErUbD6D9oV248VcfwIhAMCrMOaQ0jlnKt9c75o+c9E6oBztIV+18VY0WdGvUf6u"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.1.20": {"name": "babel-plugin-transform-class-properties", "version": "6.1.20", "description": "## Installation", "dist": {"shasum": "994133462b82803aab183098f166a41da6b2a67d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.1.20.tgz", "integrity": "sha512-bWqJZSfUcC6NHJ6xsds5j/qEd5iDyX/Cp6peHqP+ja8X4zI3L29UPSzrV/hIbCUhn2YMf8fJazvzA+sjtWySzw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGzCw4dF6EE+NgPNiDXAAexcF2mdT7vk2NAK6h4iRKSAiEAls84u8avePOidbHIjAgkSuBIRCDgFhyp2bfGFDFwZOU="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.2": {"name": "babel-plugin-transform-class-properties", "version": "6.2.2", "description": "## Installation", "dist": {"shasum": "4efdc1c908e9402962f5d07b2e2417ef83d57ca1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.2.2.tgz", "integrity": "sha512-tIJjZv5tcnbXPsQyjpvcVBl0uBNvbIA1S/6IGk+bIuxHmgm26bp6EW/0Wx2NTgWYDeDV8SW5VveqNHJHb70M/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCR5krRvJdn2A6T1uS/1IoYS90D6fmGdzgfmf4u15nqGwIgR3smX5M+N7mV/58ZlDjGWbDAx/XmodyWRGWdhugHx2o="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-class-properties", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "4d155c19756fb65af03fb61881056d147b29af01", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.2.4.tgz", "integrity": "sha512-6OyW++mCHyDshK5rxMl8PAV0RT46mi5B2XKZXSY192OCZ9+swbVluSs9F7ihD05jEGixFjgwcQ+iECm6SGPArQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyAq40wuiS13OCd/JT5org0xTVv8f1jKXpbeaRQn5oOAIhAJMudOH3MMqodMb/BX844o3t2u1/uXrWylraO5PdAOm1"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.2.4", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.0": {"name": "babel-plugin-transform-class-properties", "version": "6.3.0", "description": "## Installation", "dist": {"shasum": "e1f68cc37652a12893ad34ce629dd2f8e863a967", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.3.0.tgz", "integrity": "sha512-KqF34jGh95oibfF9uIAbGzUtCGXPvjz+BwsWNQr9rcuJYyQG6cWL5Yl7rsoroYN1GbdHYoq3kxFcHekPW+x0Xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgfkJ5OVoMMmAACBz3cqo8vQB3TAnCfG9zDRgusWpNaAiEAr6rML5qvI8YS5rrb1cR7PDRZlds/Odtkx050TI4Mgc8="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.3.2": {"name": "babel-plugin-transform-class-properties", "version": "6.3.2", "description": "## Installation", "dist": {"shasum": "ddb8a8abda1e8979505fee1e490eb63920af68f9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.3.2.tgz", "integrity": "sha512-7ml4Jpi8Qy4Th5MfO+qjGYS2MwiAo35BQTGADS4asQ0w5rEKJjN0br2/qcsuYtSvWOtcq1SnWP+3CLoc5Ujtqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5hUDPnXAtkrEB5dUzAGRQzNy2zaN70G6qadEB3n7PJQIgVays7AfBH22trskekqoy0Zxqmh/03PCvilV9tncRvOA="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.1.18", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-class-properties", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "fecd281d1a2f2866cc020b5b8ef43577922b6dd5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.3.13.tgz", "integrity": "sha512-gj+dNTpm6xabfdBjtOZtIddHnKEkomzJJfOAI6pm22h2ga8PG2UABCWrTEa/8n4vsDYQnJ5K7hz2s5qDiPkDOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtGTWAr4BY432zoe6TsdbZ7MaqDomzs0QooMdV56aCtAiAgI9+z8Gcl+6GLz8WkukEwMXnWt20AoH/LlsN2KuqVOA=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-class-properties", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "6a301b23605111bf7146dab17534a56b98d0b43f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.4.0.tgz", "integrity": "sha512-4DNZRE0U8Mly4QmS/5vVUys616ej8jeXEE/348GDbL/BPiTjhd1Nu7IjWp7nb9X/acvvl6oYvtxhTq+XvCkqXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFGtN9tRGYT1DxRHv1uxVgNagi+G0rPxV3SdC3XzQt7sAiEAjSpB5uedJBSiYN927EAwgSm5XPnuXsOiIFxvnOWv2ag="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-class-properties", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "cf5b92936e1fbf097f68d3353f677456c3a16bc8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.5.0.tgz", "integrity": "sha512-LDphc5cocb/xu/V8d6T7gqB3HO0R5/tD4qWzTNvlFrAO+AzBn+82AX1BEF3lXAD+QDRunae8WHim10k2p0G0EQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2pJu1QgUbmATmo6qpDZSWJ8330QBLpVORLWaqUITlqAiEAls2HZ9SUD/q0nJip/CgLr9i8kvtyuZrjguoUwsZGXJQ="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-class-properties", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "66b4cb6645e5d6e559ef1e5fa1bed456ac3f0aa2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.5.0-1.tgz", "integrity": "sha512-u+EzkHfKwbVbvHVfbg7F/brHar1HM+dWrrT5alnDh7b52JdUP8MGv2eDNQANJY37dJjRaVk8HmlIAB4OMz5Ebw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCECJbXNh6P/Anu9CBBxhEfnxGBly21GH1t1rogQ7CpogIgNA1fnDIMzwrTMDYFCe2S6oc3KRX6fgEbXq4ij/ml6x0="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.5.0-1", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.5.2": {"name": "babel-plugin-transform-class-properties", "version": "6.5.2", "description": "## Installation", "dist": {"shasum": "83e1827df101dae17bd4b2215fee3a20734e4e31", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.5.2.tgz", "integrity": "sha512-5IFq2m972WA307GkBqZtg6UO92mSaFf11kf2slZMTr/wCz0w3RyaEPZcRqpV+nnPcDUeMKbE0axdD+xREsA+cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgXln5iHlW+haFqCdUSE8dogkluaYPyaFZ37w33HJ5vAiEAl9Tn7JBbp6DOGqUdtFjbEw81b+DMrFYu+/8mU5q4x9c="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-class-properties", "version": "6.6.0", "description": "## Installation", "dist": {"shasum": "42341b72eb636fc9acd5837077b8dba190fb4271", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.6.0.tgz", "integrity": "sha512-fe/ASB9Nu3Yp7indmKmRcw/62jYu85xP3IR+m68lXC7QeVkMqINyqyTvbXKqNrmo80KsH52/Vcx2dKhi8L2cMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuEy9IJu7LXHcKeDJaCQh6FPXWFmeLS9uocY+f1kwfDAIhAJaOXfXfDUx4RMpkYGzcdyet5lY6No7b4vtAjB5K/kKi"}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.3.13", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-class-properties", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "c3a6e39826f5ed30009a843fd292790444a69f8c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.8.0.tgz", "integrity": "sha512-XaPQUd+uAmjUwARIJiMkl7WV+96f8JS/iXNLa9JO59xt8E8i8X70SfmzeLGDsQ1JAjdSIxjYaFbvbjKOtK3L+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICc5bEmMYrK1htYIFhpPAXilo95HJRLYSrgleORDLULnAiAjyPLyKvVSe3Ps6sqdDp1AZFtWpi2sX5nZQYD0EuUVwA=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-plugin-transform-class-properties", "version": "6.9.0", "description": "## Installation", "dist": {"shasum": "0f1831b8f78d72e4f816a0b8bd5934623e51266b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.9.0.tgz", "integrity": "sha512-o9LPoLeuPMsJsotlQ8nhdH0OQKEqew1lq3jD7uBEfPk4GKNEDVt9DIM8fbuY/BSSDg5dzYsu2XnO3IutiVjjvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpWeKuOIznep+/OUCpFUdSr+XyLKdfKbTKvIfDxk48cgIgR/XTh7irVXX1Sf7TYZC2oNsECGYQchrpGd0nP186jDs="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.9.1": {"name": "babel-plugin-transform-class-properties", "version": "6.9.1", "description": "## Installation", "dist": {"shasum": "d93847fdc6c2b59411054ba6d89bcdae3ab6b3f8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.9.1.tgz", "integrity": "sha512-mkqvVqKJ/db/1rHs68SsqfLTOLD1RQ+/rw4z/OvP7H4WX+az+EyO53iWZ0OYeRUtw91MTYiz1A7UMCAr/YhHTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqVQgf3gzUPlnBQ5h6p7KQDyeLqYCbL3YEB5Vh1LRYcwIgal3VGa/F1SCLBvupGvwjB5LUwsucmbH9cn3RZeBdusQ="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.10.2": {"name": "babel-plugin-transform-class-properties", "version": "6.10.2", "description": "## Installation", "dist": {"shasum": "849c20334cac2917267e03fcc37b88d98bf3de0d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.10.2.tgz", "integrity": "sha512-FZkrqWCkKNofzN2DsXnOV3y9AAF4JWmrC61ZhSY1S7wjjSLIDCDjY4Y4Z5PytX4HHmMuYTCvbmrEu0AlVHy0bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBP0q78mmsNmh+5ORdEDlDg2bewobKVYcJOgHuD3kn46AiBAKSAjZL9DuefUIrrht3klZGblbUQXz91JyAwvKIKung=="}]}, "directories": {}, "dependencies": {"babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.11.5": {"name": "babel-plugin-transform-class-properties", "version": "6.11.5", "description": "## Installation", "dist": {"shasum": "429c7a4e7d8ac500448eb14ec502604bc568c91c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.11.5.tgz", "integrity": "sha512-qpShiGIEIm1SnjrkV5Xn5SP/NASVHBkDDyuXfsoQ57gyrOhgdBkbHobu41jwr5awx6sap22L42+9z6hagmlTSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjPP9dMKqx6KJxugyTpzioMweMvCBcuNf0422HqZfS3AiBvVEfVXNudkuazl0haKa0nROQjdOEFxkoPN0qi3iljLg=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.8.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-plugin-transform-class-properties", "version": "6.16.0", "description": "## Installation", "dist": {"shasum": "969bca24d34e401d214f36b8af5c1346859bc904", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.16.0.tgz", "integrity": "sha512-OSleDNncM7gafAsZ5RN9vg/sxSBCmz9Zq9aEk31yhAD2mMMnmKomlagOSGjZnp98OxSNXVjgrCLB76HLG9uMmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcvTM1iZd/GNmQyYP1SQvtY7zzUT8Jm3RwNu1/rXi/iQIgU3Bb7sMcjkhT01q5gdjZXy5ah8uTg0Q7yVshXH/K3e0="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.8.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-class-properties", "version": "6.18.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "bc1266a39d4c8726e0bd7b15c56235177e6ede57", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.18.0.tgz", "integrity": "sha512-xD4aAe7vB4H17pm5oyBj4TMsqlINRd0Zda8CtdE7BP8UFaz4NOHq+Is4pVXpSHoAHY1jy/89s5Jpyo+sErs+Ig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJj8b95NwqLdvSIdD8p6LlaSKMzvHw3I/FI7cOvd/K9AIgG12pGLE7GUsqSyq360E7qkioQSom2YKLSgHLwVunkM8="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.18.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.19.0": {"name": "babel-plugin-transform-class-properties", "version": "6.19.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "1274b349abaadc835164e2004f4a2444a2788d5f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.19.0.tgz", "integrity": "sha512-XB0HO/pVZJKtxyfYZ3vfg0xyMBko3FwmdEqie0WMifIHqypENPFCPdfSEIuk3vRRJV2+qepS0R0p1gFjMaDaxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkdb6DNWR+aviMUYiZWZgvTpNS5piYZ1G28k7LQMaEnAIgb6t39n1/N2Y6c4jzcCCH6dZeXPIWnFXoMJ38c8piPiE="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.18.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.9.1", "babel-template": "^6.15.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-class-properties", "version": "6.22.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "aa78f8134495c7de06c097118ba061844e1dc1d8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.22.0.tgz", "integrity": "sha512-r/s+jbUtk8GKySZNlfE/B2mDtjPf7pmsGa5YLRGRH14+tPRDELPuiVWF0Fia21w5aS9EEyxSzItYegHHdg0FtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaPwcRHZW7uuB8e7+bxSMlHZOB+PTpK/MD2ulYz1BexAiAA+TQZQ1x3CBg7p8n/BhVea2o5r/IkDOcfZqvYsRSUBA=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.22.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.22.0", "babel-template": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-plugin-transform-class-properties", "version": "6.23.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "187b747ee404399013563c993db038f34754ac3b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.23.0.tgz", "integrity": "sha512-dPmfb1wE6Kk1UrFYZYm9tNWc9VCtv2LI9hW/+WIzk3mr3GK3+Pw9P/Goy6zjntSTCG1R48Xz9rt4ShEEk9Nx6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmqrDwrB/OPR469ht/SGpoGIHdC1LAUCCEuSC2w1vZRQIhAPUxFYnUnxu3AiIHuCSjpM16Tjv7gA81nj92+0NpScfJ"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.23.0", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.22.0", "babel-template": "^6.23.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "30042230d9b7bbf726d2021c5a71dba88fba719b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.1.tgz", "integrity": "sha512-TmBV75kb+4hQ6t4LdaKJBhMx/YuWVSij8yObekVgYdGA9VpbiqdNaqOZkYWipuP2z3c21NFo93TF4+wnHq9/xQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdMcWrG4cw4FEFzoUvpJDJ3eOB/yhQ2AjMnECrMzVY4gIgS+4zxzt0451uFUQFfZfWW7S7gUPVRg777l4dYxLLh1U="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.1", "babel-plugin-syntax-class-properties": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "74b48911411c2e000a74c0c8a08bb8e36afb17df", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.3.tgz", "integrity": "sha512-tE0ZqgybqGMS80JmTHu7u1eViSiZJKApO1iVva/vQAO3007JCD8wxuIvqTgQDyUOdWgjGPMjVfKVf5YyU2ma1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBrJlBUMywWS8diygXRbYa7QQsO0do+HSLGpQZQ7HpcgIhAJ5BwdL+TMfVVH1yh8L/Tx6NLYIwnJkzhGvfuy1unkmL"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.3", "babel-plugin-syntax-class-properties": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.7", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "001c0ec773e52d2203f088b7484363df259242fe", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.7.tgz", "integrity": "sha512-uV86TFn12ibmQHXV226ZIRaftKSKgPUcfe3+tgS9WHM+TKYVFyl8qL97UxEuKfROtxhPla7koJX7/Io+NNPYGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8V1Sir+rMPlLiUvHfG1Zdx7kCrJAsWtp5KmC/fKZIzAiBXRZh7gNcFmkUg22GgE5thoStmG0kFiopGoSFtZjiwsA=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.7", "babel-plugin-syntax-class-properties": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-class-properties", "version": "6.24.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "6a79763ea61d33d36f37b611aa9def81a81b46ac", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.24.1.tgz", "integrity": "sha512-n4jtBA3OYBdvG5PRMKsMXJXHfLYw/ZOmtxCLOOwz6Ro5XlrColkStLnz1AS1L2yfPA9BKJ1ZNlmVCLjAL9DSIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3osUaHBpIOcFLkuJRSJmSelm46wirUYJ/ET34Ejl8ygIgbaya90bp8i7yJNDOvU4lBVyLnyXIzoqsZRLEesyl8uo="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "^6.24.1", "babel-plugin-syntax-class-properties": "^6.8.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.8", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "a4bd24d02c76301492f91ef686a5fb8f0aca3c05", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.8.tgz", "integrity": "sha512-CpHqGy6bNLRHKbFWj0Ivi6M2bDPuu46l+UjAGeR6foAiMeu8LtLD2SwYPSamAfj6S97uY5mQ99J/KeQrgjSHFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwJsDjHd2KrOuAwD3P+LFp+nYT5G52zL7vkVD/74bEBgIhAIecVHiScOFGSFywTcJTy06AyLNuA20nxiH8vUkYNqOr"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.8", "babel-plugin-syntax-class-properties": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.9", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "2beb155476d7db0c26c4d548d3fe53ce8af1dcb5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.9.tgz", "integrity": "sha512-PlSt71P1nOApsD9rm+JuB6qMY2Q0sAqIVOSFun2OUjCgbf5OqrwsExsKstetSBrhJVsV7oPiqi4A6mIaUSsmSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFg9f4M64QT6XPWEqI4+Rym6HzAOwYEIwyoT6qLpLw8EAiEAyH66srqiEJMsYcP20FI+YIKZh9QQa2hrRPIoTVIWQXg="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.9", "babel-plugin-syntax-class-properties": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.10", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "97098a24e5cfc7ece9732217c9e1951a5f963595", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.10.tgz", "integrity": "sha512-ksUNhAIZMxGGYiMrdDUVnyiulmKyBnrUxkTD6u0OMsK4bTlQOdZG1NuVsv2KdILzS9TwCekrxzC6JL80IzZ5dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEj3UFOCSIxqS8tWwy+9IS1CHBnjgM0qaKqznd2apWPAiBsOSjeWIPXtlHjJT7SDOuoOEM0OfrQijPgfokTj3WLOw=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.10", "babel-plugin-syntax-class-properties": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.11", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-9lGf55ZHux1oKb0FIXrbSfWUzfoZaww5HKt+QmGpj6ENRAVrwahrawTY211apyKMQGjLQKeuPVkNMWfQjUDTBQ==", "shasum": "083c291814b0fb0036033fcb925ece03b9b3cbe2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaGhsN3xyBrzU/TyRkJpRnnOmg2ZhV7rX7RYzlGMl5+gIgUwejr8vmAAG3V3YkKgt1iRJjK+uPgxJdLzeIUlqqVJw="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.11", "babel-plugin-syntax-class-properties": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.12", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-hI/ZeGLySkxtsaZWIxESj4IryRn9Xdpd1AH15FltbEWvWQA3V+eSPX2bATXho4I4LzxPRFkJFVU8fYtoNEinmw==", "shasum": "f7092def18f154d1b299b8db4f3e8c74c41c97ee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDvB/gnc2u9W4REf2D3pXyPhCCrz6zoY4QWsOhHMmVwAIgDOBDSvt2igqzsIB0Sj0qg5PLH30T2W88TyvamRGQlx8="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.12", "babel-plugin-syntax-class-properties": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.14", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "31d55664041ea222b20639d89c27edfd08f76ba3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.14.tgz", "integrity": "sha512-TZpvhuoEjOXjeMMEVsrShFRfbw2SV21n4KfzjM9nZJIU9RSbkVFlxqEfu6un6uKeudoXnvfbKVVzp4X6i4jzOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7me/AXtj2e+3azTZ0tsUV0pi6gYfP9LXPhnu7/IbYUgIgNYvkJYXv/JUTARjPXnzp/SEY5T0Q+vzv9aayyMkGJJE="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.14", "babel-plugin-syntax-class-properties": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.15", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "d877e03c208615044f07ebc85f39332f0b46a6c6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.15.tgz", "integrity": "sha512-yD/k/Aa72u7LwQu+c2JEE6UisYq7VSKVxdQhMUuUPcM5GEiHXWLTs26LHqHW283l/M1CioTGZZm0aDDbv1cZtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIENYPfCj8ryZkLkkw4qdNpGvT2s4piri4dO7raqAchVZAiEAn4pL8mXD6aqgV9d8pwtnBVtklAnc3ZTSd9kHrVftuzs="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.15", "babel-plugin-syntax-class-properties": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.16", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "20096f1af814a3ccd1aa016428e8eae839c7d4f1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.16.tgz", "integrity": "sha512-oLKYT/TpyAsm4l/emWW3+UePPqectyclNumGtejlxi4OfVfQeDN9jvUwlC5WpzZ8GPm2Wiv8aLok5BuALKHjyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfRfapkKAB+qkgw0VWn6nfbnq9glzplCBrL13t7+vwEgIgV5QyP9uNCXb/u+qfDRtqcs6G9eMhoDyqkzRoHXHlLWQ="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.16", "babel-plugin-syntax-class-properties": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.17", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"shasum": "1d5a5b258ae0c1a70d8243c05ba1bc25f7cfa8c0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.17.tgz", "integrity": "sha512-aQ9Rpdl5n1NdYcQ56JTzRnhaqjesOfRYxqmzeOOUUFwn5AqiSwhKNQKYxkQPJHPG1kG3h20DxIvJX1ZJEVpnhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJkDNzpBR9feNCrHOqZQKOdumHJGuLYeYEQwpzp4mUqwIhAPeBkUZmLany2XPdkfxdzSe9MbQxGrA8YDc2iFT5TdRE"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.17", "babel-plugin-syntax-class-properties": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.18", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-zyi1KTVcQGMnT77ZpTjQmO7lbK2urSCke2M6B8G9y+w8wVPeKK3gRxcAwi9/sFamayjmRcVTYAZQV7rhMmdkHw==", "shasum": "a4f05e66762e2835f3d8e914840e55813c58f2a4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoKXMi2byv0Gr2CvUNUSD/lZhgJdSBLj1ATf5FQ8cuEwIhALn2zbeLpyMV1Sjz9FekAEjti5cvAgV/R7U9kAe0K6Q+"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.18", "babel-plugin-syntax-class-properties": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.19", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-fM4Ls8MMeiqIrEaLwIzysiZuXF7k1C1ra7QurlN4FtQYh6BGMB9u2+FvDCRaI2n+Nl0Q9Dawl5lRrjjoEazUVA==", "shasum": "4641c8f99649e1726207ccc671e78a5857bc1c1d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDe0oPVALwMdo4kTUuPV8pPgUB6hcGopfvfs9pC5cpmiQIhALAvH3fo8/81majSQ8qeBDlmlEuwMLujhBBRhf1lef1+"}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.19", "babel-plugin-syntax-class-properties": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-alpha.20", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-nmtsxvtYcV3sqJz0vU6cg4jljNsMx8zq/aJykEkS+7hrHsZHjN7eUNgOotofYGuvIxL0dv0YHSYsIsvOwmq1VA==", "shasum": "8d7d4f5c04af4981a66b62572205813b042bea7a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOuxX7D6A2pni8pw6/LgLwwHp0gkiNWVvHrclzImyqYwIgPuHKWfGsbCt/SExbi71t+UgiXgiysVE1ei7nEGh3jqM="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-alpha.20", "babel-plugin-syntax-class-properties": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-beta.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-SPH1UIUr+TI2VjPaKllaYGym7OuWOmI1nawSgRB2wmAMLvzv3WPP/KkfiVojPo/oFJgCNfT4w6X+WWh0PY2S/Q==", "shasum": "8ddce6f6e700c1036817d24703af6ff01e66d7a8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDz2PH9OcZyMi9oTrvzLf4C9d9n1wgbfjZ1vQFet65zAiEAywcP7Yu+UTthSN87gZrOyhWqY5NkIQ+knfnPd1zttYA="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-beta.0", "babel-plugin-syntax-class-properties": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-beta.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-a1l6Xy8Jvhpbwn5WNr+tXrN17XwQrWqHQZ4DL3KxeJNTy1GLqrftSl1b1u9rjhzcK0Y+DR3OQQmzVgVFjfXFqQ==", "shasum": "6d6ccecde9a15d1a482b30934b23a89c2c0d864c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGy600wvjOLjqCAwmLIXokLwQ02dQPocCImj0+/8nbf9AiAkOxM6nE9fichXZ9v6/4Q+s3wQNNNqPIFMiBMextYmeQ=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-beta.1", "babel-plugin-syntax-class-properties": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-beta.2", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-AKY8nX7Am8GiyYWc3Nayck2RDHOQeI4HyHyJOpXMnsRD6/aMV/mEhEq5lbIpvlJIhxA6Kyebs0wz4/bsB6HOAw==", "shasum": "1c8d8c76531b52aab1849d6b791329b44413af2a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEL3h6yVXUlAVPRoX/gwHDqt8v/evhXEPq/FwPcDmXywAiEA/AFFwNeaUMYOPnFklIGE8+Ouz9GesxLzrD5pJfvpV4A="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-beta.2", "babel-plugin-syntax-class-properties": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-class-properties", "version": "7.0.0-beta.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist": {"integrity": "sha512-12n5QjNKJlbhRmAfVcM5D+rGpxsDa0AP+ufa5Xylvwcgjpq4YjEQc8L5VBBFLFmByAcXKsknNsAVuSfOIGHNDw==", "shasum": "d7cf0e431512262499421d53582969503f24581a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/4fYRtawKgzFIfyqeGL3o9W0SrSKdCgOFLKSp02Bu6AiBq/1Ek6SPsom+nxcjKA1YtOhvx+rPerjj4Eq7nhRxvRQ=="}]}, "directories": {}, "dependencies": {"babel-helper-function-name": "7.0.0-beta.3", "babel-plugin-syntax-class-properties": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:08.134Z", "time": {"modified": "2022-06-13T04:04:08.134Z", "created": "2015-10-29T18:12:42.058Z", "6.0.2": "2015-10-29T18:12:42.058Z", "6.0.14": "2015-10-30T23:36:15.793Z", "6.1.4": "2015-11-11T10:15:45.661Z", "6.1.5": "2015-11-12T06:55:04.705Z", "6.1.10": "2015-11-12T07:54:41.460Z", "6.1.17": "2015-11-12T21:41:51.304Z", "6.1.18": "2015-11-12T21:50:24.201Z", "6.1.20": "2015-11-13T11:39:15.705Z", "6.2.2": "2015-11-19T22:47:20.441Z", "6.2.4": "2015-11-25T03:14:04.661Z", "6.3.0": "2015-11-30T22:59:02.314Z", "6.3.2": "2015-12-04T03:48:15.042Z", "6.3.13": "2015-12-04T11:58:45.377Z", "6.4.0": "2016-01-06T20:34:35.012Z", "6.5.0": "2016-02-07T00:07:27.639Z", "6.5.0-1": "2016-02-07T02:40:22.741Z", "6.5.2": "2016-02-12T16:30:14.734Z", "6.6.0": "2016-02-29T21:12:39.024Z", "6.8.0": "2016-05-02T23:44:31.194Z", "6.9.0": "2016-05-17T18:49:31.955Z", "6.9.1": "2016-05-29T19:50:10.100Z", "6.10.2": "2016-06-17T21:45:20.630Z", "6.11.5": "2016-07-23T18:09:39.653Z", "6.16.0": "2016-09-28T19:38:55.108Z", "6.18.0": "2016-10-24T21:19:00.104Z", "6.19.0": "2016-11-16T16:15:28.049Z", "6.22.0": "2017-01-20T00:33:46.107Z", "6.23.0": "2017-02-14T01:14:30.876Z", "7.0.0-alpha.1": "2017-03-02T21:05:54.801Z", "7.0.0-alpha.3": "2017-03-23T19:49:52.922Z", "7.0.0-alpha.7": "2017-04-05T21:14:24.661Z", "6.24.1": "2017-04-07T15:19:33.202Z", "7.0.0-alpha.8": "2017-04-17T19:13:20.117Z", "7.0.0-alpha.9": "2017-04-18T14:42:26.525Z", "7.0.0-alpha.10": "2017-05-25T19:17:49.255Z", "7.0.0-alpha.11": "2017-05-31T20:43:58.022Z", "7.0.0-alpha.12": "2017-05-31T21:12:13.162Z", "7.0.0-alpha.14": "2017-07-12T02:54:25.487Z", "7.0.0-alpha.15": "2017-07-12T03:36:42.685Z", "7.0.0-alpha.16": "2017-07-25T21:18:35.114Z", "7.0.0-alpha.17": "2017-07-26T12:40:08.291Z", "7.0.0-alpha.18": "2017-08-03T22:21:37.274Z", "7.0.0-alpha.19": "2017-08-07T22:22:20.262Z", "7.0.0-alpha.20": "2017-08-30T19:04:46.868Z", "7.0.0-beta.0": "2017-09-12T03:03:08.299Z", "7.0.0-beta.1": "2017-09-19T20:24:54.433Z", "7.0.0-beta.2": "2017-09-26T15:16:11.746Z", "7.0.0-beta.3": "2017-10-15T13:12:37.405Z"}}