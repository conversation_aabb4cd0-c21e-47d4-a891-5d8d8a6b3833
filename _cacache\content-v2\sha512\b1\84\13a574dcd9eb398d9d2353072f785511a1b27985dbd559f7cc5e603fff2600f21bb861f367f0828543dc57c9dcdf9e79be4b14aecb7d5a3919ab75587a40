{"name": "archiver", "versions": {"0.1.0": {"name": "archiver", "version": "0.1.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit --reporter=minimal test/tests.js"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip"], "_id": "archiver@0.1.0", "dist": {"shasum": "c0f36af0d6984361e560edd17a2c75e16fe51945", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.1.0.tgz", "integrity": "sha512-llUnlEd+l5QJe5hiGL08vTcuhckMWhA0ptgjlYJ2Q1qGP1tKJ36VYCMw3K7w2AO2P9J+TXJrCZUz4OquswfRxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbLllzODjX7VRxYn2JLchH8MNJZmUwt74gRFHLWR6HAgIgWtSzC6gACS8ifLba7WFU3x4Soy6kPCt1r3K2kvlPxLQ="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.1.1": {"name": "archiver", "version": "0.1.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit --reporter=minimal test/tests.js"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip"], "_id": "archiver@0.1.1", "dist": {"shasum": "5e80b5b216349d421cace3ac5cde0c19ccc829dc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.1.1.tgz", "integrity": "sha512-2EtC5m4QdSvIWoJrn04YM2Bzb8k5qCwF2RMqYbUFUO8FHCWenvjRnqNJiMHTbZkfeIvWom4jP9GP+kNHO62hFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwFgq2zic9cXpKFA4BgE9YuDWHkdk9sRbztdLNJfiDUQIhALChHyIEu8ZK+DwD0Fx2DRe/YrRgwvu8nAPHVYxg9/uU"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.2.0": {"name": "archiver", "version": "0.2.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit --reporter=minimal test/tests.js"}, "dependencies": {"lodash": "~0.10.0", "rimraf": "~2.0.2", "mkdirp": "~0.3.4"}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.2.0", "dist": {"shasum": "817323a1853f4380a935b904309945ac0eb3ead4", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.2.0.tgz", "integrity": "sha512-5rt8OF9oFV0nj9OfFAazWic1W+BmhXB0OZxZuxWv++Lta4SAkJ+3zrDC9TSfD3KbuvdUFWJ8yU6EigzeGGwK2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZlVyirZrOCocDdEwhkXuWHQmnJtwGmxkd1dYyaOkjRQIgWbHFzOmpNpokTalMaHkqLpMMy74fpo5ffqeQ5mjvYZE="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.2.1": {"name": "archiver", "version": "0.2.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit tests"}, "dependencies": {"lodash": "~0.10.0", "rimraf": "~2.0.2", "mkdirp": "~0.3.4"}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.2.1", "dist": {"shasum": "f49d5d8020398cc9410f4f8b3041027fe6ad7b07", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.2.1.tgz", "integrity": "sha512-zUG4BuYA4QkUv4dpRlgibE95VOKPe8eu4qB4O56GVBZPdLpOypPy5/v+MxJ2UXTcE5GaR/8JqKIiUUGzRlebTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAisyn7XEHUm2CPDD5slKHObfVIRB3b0KkNbBuICgCSaAiEAjDSpf5YBUAst+BS4gDpGgohdy99gHgcE7ECPEIz9bdU="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.2.2": {"name": "archiver", "version": "0.2.2", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit tests"}, "dependencies": {"lodash": "~0.10.0", "rimraf": "~2.0.2", "mkdirp": "~0.3.4"}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.2.2", "dist": {"shasum": "35c85daedb244f3e2c0ccbdf7711c41f4e480ea5", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.2.2.tgz", "integrity": "sha512-z+DKXpLGiHe0iM2GmfyrvBF/ydZO3ZuW0GO6gAJ23n8K55zfSmAR2Tagq5F49VD5AhlV4+E+9b/euemdtVDjgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2++qt/J75a+2wjVOK22dUZ0gajnCozXwd3FmI+0qeRAiBHDqW0HH/bD/S15uwi1PJERmmdFzyNb/C7mnXUAdlXzw=="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.3.0": {"name": "archiver", "version": "0.3.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit tests"}, "dependencies": {"lodash": "~0.10.0", "rimraf": "~2.0.2", "mkdirp": "~0.3.4"}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.3.0", "dist": {"shasum": "9b242ee8233cca3fcef233b8e566389b82b4bc91", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.3.0.tgz", "integrity": "sha512-5BR6WhXXTrLKIdtDvQWEZ2aXyTVJVAJmN3M+cNptg2RcvGaRnU6j3bH4jO7Fpno6oxvWONRhV7gYP96si+BVtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCunDTjVAnLNgBmZv64oK35r/MIIO/WMj1tfN795TS8FwIgenG/i11dvcew3yoFuqyvKXSL8O7ei6Q6KwugwYPwD5o="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.0": {"name": "archiver", "version": "0.4.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit test/simple", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~0.3.0"}, "devDependencies": {"nodeunit": "~0.7.4", "rimraf": "~2.1.2", "mkdirp": "~0.3.4", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.0", "dist": {"shasum": "c6064d3f71a3b1c3e308bae6a6742ca356b6f3f5", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.0.tgz", "integrity": "sha512-XVIgggLbUcWRukNLsM7BWiK3UXp8YPNISNLndT0oGAi3EifL3RsGEVMCnL02/MSnGZto6IJWK+B8CwVWW7p56w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzYZBlnpuTX3QYZSonsdPtYC+cN6iqeg48rRRFNfx35QIgaN/HuF8pLXY14zaVMQk/hDKlSUJfqRoRA1HiKpOEVcQ="}]}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.1": {"name": "archiver", "version": "0.4.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit test/simple", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~0.3.1"}, "devDependencies": {"nodeunit": "~0.7.4", "rimraf": "~2.1.4", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.1", "dist": {"shasum": "a8f69d23e80945769dd8fd1f86e71f0743a4d333", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.1.tgz", "integrity": "sha512-iw8Fgk5zhbRGeGEfO6Cuj99s4/ZFMZ2yQcWCqkL7VSQKaY/ehX09mmlVDRo42TPCDyeE7b8Mb5Aivxb8iJJvVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEeWoH3CW42457CsrRgiHeDkUPz+dMreY6IYFw4MYFebAiBfM+tEMFz7bfleyH8wvA0NztrHsxlT0jvcYV5yKiTFoQ=="}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.2": {"name": "archiver", "version": "0.4.2", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit test/simple", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"nodeunit": "~0.7.4", "rimraf": "~2.1.4", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.2", "dist": {"shasum": "9688cf93e6eb6a2217109e6e938171f35e349a1f", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.2.tgz", "integrity": "sha512-Zz+9Ewp4ffP4OwoJyz56UDHETxyCnwkFrDHdca9VnzXj/Cv8cYP2z2V4KkB0/79LU2JjHpJ7ux+wNqXV2AfTLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIASxvgOKHWvcbDGyfC5fH0rtJfIZFP1WQxd7+KGuMwlwAiEAlfV4OpLr1/SlndR5H3DdeO6W5hV0mRFmtswdzrkF6VI="}]}, "_from": ".", "_npmVersion": "1.2.17", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.3": {"name": "archiver", "version": "0.4.3", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "nodeunit test/simple", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"nodeunit": "~0.7.4", "rimraf": "~2.1.4", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.3", "dist": {"shasum": "570c819b8c4f7e481e44877dd8b09951b72b1281", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.3.tgz", "integrity": "sha512-6mrc/l3+y9YudHaZWdrrR0OqYcfoBpaBQP2c2/HTihf2sJY/4KZO06dEhx1IhpqGerQP//86XZV/houDE2I+LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHg9MHCyzny9Ou26pSoFo0BKj5qpQb2FIUV4F5GOeURgIgEhh3ACTaPYYtajBUWpDP/MdF7HkXIcP02zcexNHCEDg="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.4": {"name": "archiver", "version": "0.4.4", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"chai": "~1.6.0", "mocha": "~1.9.0", "rimraf": "~2.1.4", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.4", "dist": {"shasum": "284153d477f209a60c0e364311291836d829d27a", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.4.tgz", "integrity": "sha512-UcZBI/HUl2Na9EUN10nKr0sTX4yegdQGPLBgw6vlevQCDB+3ZhyEXfSL6eMnlTBpXMjsYku2mSwS8LuyNLJyrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDUWQxvTstOMWOTqrRdBLlmm+9vwJ5ywJiCZY+S8A+YQIgTuox1U0klNhLoXziBU/6EEFDbYJEYNDfIgjshUD4TDs="}]}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.5": {"name": "archiver", "version": "0.4.5", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"chai": "~1.6.0", "mocha": "~1.9.0", "rimraf": "~2.1.4", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.5", "dist": {"shasum": "e988437506deaaf22eeb144de3e283c10bfdfd89", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.5.tgz", "integrity": "sha512-+fKZjOEy/CbubnmYGeJkDqGrSbkz+XmNteiR7tvDekAieR0v9sZtk3mV801BfoPZf9OfGy6sQcfDyY37UwLHOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAhdrOzJ0mdfbbmWaj7HJj3cIAlGnrDGR4NhmRcXPMRoAiB+lXGp2oS07xx8s1XhDjsxQvRNP8qMii0nea2mJSOXAw=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.6": {"name": "archiver", "version": "0.4.6", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"chai": "~1.7.1", "mocha": "~1.11.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.6", "dist": {"shasum": "3ba6468c74a65ca9522ea8d36b3c925569c2529c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.6.tgz", "integrity": "sha512-c3a3UMyARrrduZvdsEt5YyiGPULwizv7uo2kEjQ1Uk2YNnjzBl1V9AGROkeZK3TLi32wHZIYVRko8ZINFnjJrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBaaMjpmYXO+u6B48Opz4ODrZrMDJyhVMAc5HTyqIxihAiACx2ZVSCrSncsDQfFWiPAXC67oSLxihyJlZ10B/Jk06Q=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.7": {"name": "archiver", "version": "0.4.7", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"chai": "~1.7.1", "mocha": "~1.12.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2", "iconv-lite": "~0.2.11"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.7", "dist": {"shasum": "824ede3181184c0b3f331a3cbecb226071567d27", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.7.tgz", "integrity": "sha512-DRux3hb0VZGiUuEcUAInlror5HO/DBtYImlH8Rwn64VmySXOReac8KUNu8HH0SPeXGdK4vNETjBOAW1dz980/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAh6Unzb4jVAUM6fFqup1sTKyM2h3/jtPmgZIQDHIm1wIgAsonZUtq9LlP27uTQjgLRCgQIJhEHhStll+vVoStNmM="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.8": {"name": "archiver", "version": "0.4.8", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2", "iconv-lite": "~0.2.11"}, "devDependencies": {"chai": "~1.7.1", "mocha": "~1.12.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.8", "dist": {"shasum": "da1563c46c4ed51bd6af9205efc0857ee94b1508", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.8.tgz", "integrity": "sha512-Ym+bUXG<PERSON>ftdCSLM4Ed39HENBDWaE60jgqLXDEJUed+EVuW0yox2l6G6uEJxVTwPZh5f9QkCIWfwsqTRLlyDsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3W8kfhePFsgwMkM+LJQyN+9xUbCM37vaV+KkeBSZV2wIhAN2An2cTcJN4rbUKVljWAyETxT6YZhChOJ60u6p0Snmi"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.9": {"name": "archiver", "version": "0.4.9", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2", "iconv-lite": "~0.2.11"}, "devDependencies": {"chai": "~1.7.1", "mocha": "~1.12.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.9", "dist": {"shasum": "7c8a5c8f186497b430698855b1a827af81ce94f1", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.9.tgz", "integrity": "sha512-iVn9fYLe0mBklqAw0++8RQhKS5LpAbQ7NGxGgo776j6oHbhJ2TnNem+tgvTagt3YTKloUuqT6GWWA0IuQi6CTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDH//CuGFUjWPWlOyMF4Xi5Lr7y5HVF2jX4yD41QmPMaAiEA5GGI5QtiIYtGCvtiOOgP/9/Vw4oAOmqUCvdjtg+V6ME="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.4.10": {"name": "archiver", "version": "0.4.10", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.6.3"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.2", "iconv-lite": "~0.2.11"}, "devDependencies": {"chai": "~1.7.1", "mocha": "~1.12.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.4.10", "dist": {"shasum": "df0feac8f1d1295e5eceb3a205559072d21f4747", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.4.10.tgz", "integrity": "sha512-kZfIZkWWptGDLj1IHt8HgOLHe4oBGPmHSoAJ142HVt7adzSO657//wLbrkyxyOT+IuGJCA1wMBB4U90MAEiUjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbJWtSlpYwjx/o+mG45IC1x/LfANf/xkT7z4+6ite5pgIhAMuLi601FfvPBTZIox5MV6DrSxpmSWPLqtpHp5kqdGKA"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.5.0-alpha": {"name": "archiver", "version": "0.5.0-alpha", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.1.9", "zip-stream": "~0.1.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.5.0-alpha", "dist": {"shasum": "8d036b1130a6d1146fdd499af4b2ff6c12df0d13", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.5.0-alpha.tgz", "integrity": "sha512-HQ4CUzPQfMXvQmDxo6GelmdehGa8uK4jeBzCJydDovqErntK4NCNB7CofzNc9l6ssGOCQtYDKluVweDf68VxuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFUWiXFiJhGgCoEfLiF8XMkEUn6XtdExr9g0ulCwoX5NAiB262r6QUfBiWsHHgu1cvudTEPd+u/VTLHQMJ7SBn4hcw=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.5.0": {"name": "archiver", "version": "0.5.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.1.9", "zip-stream": "~0.1.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.5.0", "dist": {"shasum": "6c3f0ff1b0329c11e05a19415b3575da5cf52939", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.5.0.tgz", "integrity": "sha512-fQys2+8N3Uh1TwLvqYASsNB2yw3CVnVHkXu0u+3wSshyL4xuVEOohjCmty4LQpApRRIAxjiZPdSaBbyZ5WcvQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnQ3KJcowdSHkJnOksZ2PDI/2Q/cdth0qGsWUJWU2H+AIgWey0yoqUXtLhaohbXssRhNOe3ltezcdevcFBVlg7K5U="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.5.1": {"name": "archiver", "version": "0.5.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.1.9", "zip-stream": "~0.1.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.5.1", "dist": {"shasum": "0ed2a882d9903e542d79b3131a4445c91167c84d", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.5.1.tgz", "integrity": "sha512-hUtkEacJU9QJpcMGQAlSJ4wSk7jJi5Zki1HGP05oX+3lcb0iYaMNh9ydWgdR23D1Awcb7o0uDuf7ewCbZ2Q9xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFvu7+W6bc083eWm/saHf0pbr4aQTQXs5/LtMb5X0yYgIhAOsbakkTQSI761RtJ+mDdKhTc+XtVsPhuCH4IClCzuuE"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.5.2": {"name": "archiver", "version": "0.5.2", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.24", "zip-stream": "~0.1.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.5.2", "dist": {"shasum": "4e021b1fea5d902201f4886fca6a19fcc760083b", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.5.2.tgz", "integrity": "sha512-JwCRqDtKsSfyYVfY8V4ASk25EOlECFL1XHsKrLv0/8mgPJfF1eyDMHouqly3wh+bWr8vULFZTxql20dwwb9B0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzO7If9q/h3CNaZXR/l5BCq1cRb6fdbgfmRkhrwM1tYAiEApNqt0QXItIChVqgOXjuMUKOqku40T2c9uPO7EwALizg="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.6.0": {"name": "archiver", "version": "0.6.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.24", "zip-stream": "~0.1.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.6.0", "dist": {"shasum": "5fa4b0bb0548259010c5042af948051e2fdada06", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.6.0.tgz", "integrity": "sha512-4+9lhicpff3naUgdsHi99/NGkBt2EFGAplPbMw7LRlNGo+rbvf8BCAuJ8AFezITXon6A/TipqkG70bRrPi4gmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7RfwpStucBu8QnaCFEB77F8XXZ9Ha62wR2LE8tlXHrAIhAM0XVp/KreEtjHMNbLda/tiMqB/5ABK2yO0xs5nSY6/j"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.6.1": {"name": "archiver", "version": "0.6.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.24", "zip-stream": "~0.2.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.6.1", "dist": {"shasum": "d402992571fd179aed672d9797f888e568e1dd5c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.6.1.tgz", "integrity": "sha512-e591pyVzFFDDzZI84cPMo9dmJp13lleWgwNGhPhHGKbp8bBAGFKX+IsEaLy3S2xQfzv4wriy9uHi+wJ1iu6GpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfm4QiaZHEz/8TlwYJYI0pufUaUoxiIptDI+DTcMSAhAIgdJ1dpg0CuxsswSskzvRe0YvSOP83iuDP87q5ZYn6yPM="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.7.0": {"name": "archiver", "version": "0.7.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.24", "zip-stream": "~0.2.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.7.0", "dist": {"shasum": "1d3878cfc663628f7bd64dbbb57653c9715f3c77", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.7.0.tgz", "integrity": "sha512-jV21iTeZO4T3MRIgT+PeUvvCJoS84qsdMyLpL+cnFe/m3SkwRb3tJecdLw4m8KbfKXF/LcbRRqVMEQ6P/0jMyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMFLe1v+sdKrGytMG7Wq1kM4ENnSrREVhIBZAIKwS3SQIgKFCpoygP1WPJpHePpNGthUIbIzPYIl0WNbsBAt/PZQM="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.7.1": {"name": "archiver", "version": "0.7.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"readable-stream": "~1.0.24", "zip-stream": "~0.2.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.7.1", "dist": {"shasum": "cf152d794f86bbd93f9858da60d36aaeabad9bbf", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.7.1.tgz", "integrity": "sha512-54lpi9umdhneo7/n2GCJCPuT8sPS07KDVymyyLhsqaZXWckV+Ajc2i+SjjG34UPCA4OlVEkZQhEffyVU2as7sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQjYQ2GT/SgGPBw9KJjyrGqgFSrP45H57COrTZLt52kAIgYycP7chXEWs727+bqiYi/ELTAnzroxnGnLVKqSNmDUU="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.8.0": {"name": "archiver", "version": "0.8.0", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.8.0", "dist": {"shasum": "52b25afc94d78eb18daecb45242eb0f60444720c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.8.0.tgz", "integrity": "sha512-gLbVjsjp+l18c5Tddfm3mm0L6AN2YpWTQshJXtqOPckbxin4mR7iTqzH2JGOku7Ob+twa1MbwgkYTfLqQ8NJtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIiLJF6rBbaV6umGaN/jvsrDJ0EYQTdlE6AaKFvZrVTQIgcEvsza1Q5WsRPIFSn8ipQaRiNTWJri0fTzQBEkPocOM="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.8.1": {"name": "archiver", "version": "0.8.1", "description": "Creates Archives (ZIP) via Node Streams.", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "zip", "tar"], "_id": "archiver@0.8.1", "dist": {"shasum": "a6fb7fdbc71600e68d89edcd2f55ff7d50cc6040", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.8.1.tgz", "integrity": "sha512-KueXxQtPxZeJIYKv17s0BonXzkUx2hMFam+PQN9zaK3gdOXMVCbxNzIVkHPnabAJnI1MLYQGwn63LSJVGnozTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFf5aO4u5Et3py/zQL8dTJz9sFvMhOqxrRgxqnbRCl4UAiBKPN1hwHDFmAhssfSUxrfMvICsKfdr6xHQopBsuX5BuQ=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.9.0": {"name": "archiver", "version": "0.9.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24", "tar-stream": "~0.3.0", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "_id": "archiver@0.9.0", "dist": {"shasum": "a2fd11057a588bc94f01e5eb7cd3e937ea4d8ce5", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.9.0.tgz", "integrity": "sha512-ag3uJ3emxD6b4mvfQKUljIcwQUZ9mdi1TYhoJ74+/I+kY+FF3XdFh2pVOuXOhBMjxj3kSWxDd6zZq2k69srbhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXtIW47SwR8PHIrz1Ncz5msfWZvOGoch1kElIXJ4t+9wIhANBGliG+JQuNiUeo6mZPY+4ksUXzL61M2OKjIxZ3MmQG"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "0.9.1": {"name": "archiver", "version": "0.9.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24", "tar-stream": "~0.3.0", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.1.5", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.8.1", "mocha": "~1.16.0", "rimraf": "~2.2.0", "mkdirp": "~0.3.5", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "_id": "archiver@0.9.1", "_shasum": "69ac9967b4297185345582daa5deefbfcb664046", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "69ac9967b4297185345582daa5deefbfcb664046", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.9.1.tgz", "integrity": "sha512-wCW2iPZn0v4MqeIIUmNc7LMjP4bwMwytTAkAv1hQbZx2Z1X1Dd2bzfSp+H/zkfKcJuzfdFVjxaLsY5C+1vu0iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJRI5HUxKYbwU/cAjRPNdFMr/VzeHBIjR4eKErmTSLHAiAQ7h6wtOrHpBenAwytUpdoHQndrMtFzdkYvfHMyQqwQg=="}]}, "directories": {}, "contributors": []}, "0.10.0-alpha": {"name": "archiver", "version": "0.10.0-alpha", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.26", "tar-stream": "~0.3.0", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.2.0", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "_id": "archiver@0.10.0-alpha", "_shasum": "e25690e2de78b0b148c4be97dfe7c4e383122566", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e25690e2de78b0b148c4be97dfe7c4e383122566", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.10.0-alpha.tgz", "integrity": "sha512-DxUE9uZJ5Gk99GW1doXx1iMeBKjvAKdB6QYnEr0NU+4nUxY8qMiTbGZsajJJvnEXQXcd3lpEHZAjFPinKACrMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDT8L3MpCQlNZf61zohA9QwclQVn1BAiPEaCuKJTwnQlAIgdG36Bv+jaM/OSLn5R0KINGqmEG2DaNSJUBd/AAgcbZQ="}]}, "directories": {}, "contributors": []}, "0.10.0": {"name": "archiver", "version": "0.10.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.26", "tar-stream": "~0.3.0", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.2.0", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "_id": "archiver@0.10.0", "_shasum": "49e84512f31e4ec1cff27d7913f58ab61b80136b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "49e84512f31e4ec1cff27d7913f58ab61b80136b", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.10.0.tgz", "integrity": "sha512-XL/eBFyzd0HfrQQSrZzcHGA7k9XpDuD8ZxDOKmgLvPor/S63ZOuVxvxwi+sdPqkKW46rj7NmiVv02vudnWmDrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC02+cKFVf7YQppxlVoWv5hJf/WZS+Dmkr2/pbND0clqAIgdRfEkz9oRrWT4jUPt3tGBN1+9XcIy0IqMnCeFNipCP8="}]}, "directories": {}, "contributors": []}, "0.10.1": {"name": "archiver", "version": "0.10.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.26", "tar-stream": "~0.4.0", "zip-stream": "~0.3.0", "lazystream": "~0.1.0", "file-utils": "~0.2.0", "lodash": "~2.4.1"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.18.2", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "_id": "archiver@0.10.1", "_shasum": "c88a50fe114f744d059a07dfc4690f3a204146e4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c88a50fe114f744d059a07dfc4690f3a204146e4", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.10.1.tgz", "integrity": "sha512-9fT2R3gPivhQPGigzNJOL3Xjo88SfvI4QdkuebhYr+vKSpf/HakXMg7qWQh0omtKscD970/liswlZ1iY44wHUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1Rx+74doPlL9uRLefraCANjHGZlNJqhCUX2ZwOo2aZwIhAKlEhwEWezyV0RLhAcJFw6SCU8IOW/s269SRnxxVWBP4"}]}, "directories": {}, "contributors": []}, "0.11.0-alpha": {"name": "archiver", "version": "0.11.0-alpha", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~3.2.6", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~0.4.0", "zip-stream": "~0.3.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.20.1", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "fc9937d0c62dcafaf78bfdf456669fe9de85f3dd", "_id": "archiver@0.11.0-alpha", "_shasum": "7ba132722723b02f975a5180c8f438aea8937a5a", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7ba132722723b02f975a5180c8f438aea8937a5a", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.11.0-alpha.tgz", "integrity": "sha512-xZxuziNdi4Op3kQJ0TFE4thOgvIL3VA8GYHXw8Fuw7tstVSepod3VqLX7p3ESK4DUjvxO0lmC7EYPtu8S3NH5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDadLqGtoYHfVXExMNQJqWglpe1V+AIHbUD/rEAZL2JvQIgHKccj+xLYeV3fOEx8aDjx1upAkiuguZmu6Gkr9dYo7M="}]}, "directories": {}, "contributors": []}, "0.11.0": {"name": "archiver", "version": "0.11.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~3.2.6", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~0.4.0", "zip-stream": "~0.4.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.20.1", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "9f0a2ed21fedca8aec48605d2b8d31cf841cbbc6", "_id": "archiver@0.11.0", "_shasum": "98177da7a6c0192b7f2798f30cd6eab8abd76690", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "98177da7a6c0192b7f2798f30cd6eab8abd76690", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.11.0.tgz", "integrity": "sha512-Jew2vT04Dc2DSR7NrfTLDpwoGYVgl9MXJu/BBAwdM248v67ScIGezA8MqHVIa0B+af+b0S3mBPUD2HNP3tM2PQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBM9zmNFkjCTFdls16NXSbSRDBCMuTC9aeBJjqiYh4obAiA1rI3rCBCu+8l5boexbSXlpeDH9qbCfg6pm7pbDCdBpg=="}]}, "directories": {}, "contributors": []}, "0.12.0": {"name": "archiver", "version": "0.12.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.0.6", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.0.0", "zip-stream": "~0.4.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~2.0.1", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "b7597891cf68cd16f0c82b97a4260e1eb07d2e4b", "_id": "archiver@0.12.0", "_shasum": "b8ccde2508cab9092bb7106630139c0f39a280cc", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b8ccde2508cab9092bb7106630139c0f39a280cc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.12.0.tgz", "integrity": "sha512-OkVn8XFK0m97RGrMPP47zkgJvOnusskNECNkKHg+3m8tSOp5Lo+SFh8h3ZURQDY8twiKdl2LxHT8+5+DRCKN6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVS9dJ2dQFuWzvhBF2AYHUIVfuM41tqRoEpboFi7gdegIgLO+cOgxeAk9uRGllCppFNQxgxPpKkdWFYJ9OKpJIMSo="}]}, "directories": {}, "contributors": []}, "0.13.0": {"name": "archiver", "version": "0.13.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.2.0", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.4.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "293b0df490db4207e590888ae5a5340af6aaa4d2", "_id": "archiver@0.13.0", "_shasum": "a1964b2e0adec409da37693d9f10fa3169ebf0eb", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a1964b2e0adec409da37693d9f10fa3169ebf0eb", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.13.0.tgz", "integrity": "sha512-4IB9npFZnIlbt14nWtCddlPXAJ/i1tEloLY+dv6JJjul+Ov8ylctVF6qLxFkTsmTVxWxFEdyeb5sNlNqvCBizQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHog1HnCSKVwhqfHvjm2lMMbWGJt7qFoXmVqX2/9SI/AiBCzUR4JHwvxk+Yq33is62Ur1MKBaH2VhpAl2hJAMMZ5g=="}]}, "directories": {}, "contributors": []}, "0.13.1": {"name": "archiver", "version": "0.13.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "unzip2": "~0.2.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "fb6b4fb3ad837ebd9439cbb0aa0d1c52d90b3815", "_id": "archiver@0.13.1", "_shasum": "afc6b3561a63643b38a26788912a83854ecaa0f1", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "afc6b3561a63643b38a26788912a83854ecaa0f1", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.13.1.tgz", "integrity": "sha512-gF3MoWmIXNY7jDt5Tyj2HvEX7239CQw87BWMtgS4qYLb01ru3ft5DPVTk+trV9uh6CPxuXQvEhYcpkU4UojnFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDplVIPz4k8hAYHwbMMvjrf9ZaPm6Ct79IKwRRcJjwOOgIgHCUbZKGyPFFZ7bu/3o+u4cECNXjRgwY+Af+9lRwd9F0="}]}, "directories": {}, "contributors": []}, "0.14.0": {"name": "archiver", "version": "0.14.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bencha": "node benchmark/simple/pack-zip.js", "benchb": "mocha --reporter list test/optional/bench.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "unzip2": "~0.2.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "63570aec004474bdafcb2b9fdb8d3784d0622120", "_id": "archiver@0.14.0", "_shasum": "207334218d3b9a2198f9f70b23c8b8f4f6fc999b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "207334218d3b9a2198f9f70b23c8b8f4f6fc999b", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.14.0.tgz", "integrity": "sha512-onwHSDHLxGtzNRRNQnLbztJtD9b9Ti4kG/y+h8nJmp32Qj+mifKCicFlNrbqefnC8NdmEWO+6xh6egNyR1Ngyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3Al/1vZA/prlazY/XcIHG+wMBIjSeLi9/eOfefGd17gIgUjjGQrLkckpuZswZFqF6/Cj8cauVHMuTrXeZ2zTiVbY="}]}, "directories": {}, "contributors": []}, "0.14.1": {"name": "archiver", "version": "0.14.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bencha": "node benchmark/simple/pack-zip.js", "benchb": "mocha --reporter list test/optional/bench.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "unzip2": "~0.2.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "5f25116e27e52941252779f4eb9cb8b861bad21c", "_id": "archiver@0.14.1", "_shasum": "df95973f8bb91cf41209c27a15fcfb156cd95cbe", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "df95973f8bb91cf41209c27a15fcfb156cd95cbe", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.14.1.tgz", "integrity": "sha512-84wFUYj/xtWz0oMU4t18mAdE5ZX71cx2XYlfUhiEcsmyThYlGA5qdpIIT+lgweJ4iLnFnSTB4jVS1SbdrsSJDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLPHxSdlruT3JB57tk7SOmD8zrPJK1emFh5OiQA8BTVAIhAPJFuPNanXDoFREakQ5QUyEdtizedhd5QBa7BgBj52gb"}]}, "directories": {}, "contributors": []}, "0.14.2": {"name": "archiver", "version": "0.14.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/ctalkington/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/ctalkington/node-archiver.git"}, "bugs": {"url": "https://github.com/ctalkington/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/ctalkington/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bencha": "node benchmark/simple/pack-zip.js", "benchb": "mocha --reporter list test/optional/bench.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "unzip2": "~0.2.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "eed61cb45f5f4384ad2d89b589569dcb35e235cf", "_id": "archiver@0.14.2", "_shasum": "1df74d263c05cd5e6c300295ae6a600bd0734916", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1df74d263c05cd5e6c300295ae6a600bd0734916", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.14.2.tgz", "integrity": "sha512-30ivFYJwLdMD+4UIWCc7s66Np5djOl7T7lldsAKv4V6EDUmv5+V+NKtjfnmZ7BTsmcZO8ERgHrIlt+QFbBgl8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGCyPkjA009mdcYDQSRw8cPLXZVwyoCF/+8AQtPimJ17AiEAruFBSDBlMaB9nOifbEnDPOliUxkQfVLpQyjlEqpBjAs="}]}, "directories": {}, "contributors": []}, "0.14.3": {"name": "archiver", "version": "0.14.3", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/archiverjs/node-archiver/blob/master/LICENSE-MIT"}], "main": "lib/archiver.js", "files": ["lib", "LICENSE-MIT"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bencha": "node benchmark/simple/pack-zip.js", "benchb": "mocha --reporter list test/optional/bench.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~3.2.0", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "yauzl": "~2.2.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "gitHead": "c62a470d7a22a018e87bc9dc1d1e3adf07ddffc2", "_id": "archiver@0.14.3", "_shasum": "e265f2af74df8f24124c01cecccc0772f57f36fc", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e265f2af74df8f24124c01cecccc0772f57f36fc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.14.3.tgz", "integrity": "sha512-X4fUJMkYcVfdT26m/GZd1iTQguHQod0+sw+hCc8naXueM3fOhmWtonFt6xpdp2cD8xURzOwpjapzMODkOYvehw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFvBR2byFJY1M9+iNYY7NiiH9sfUkygb22wdLfv99oPmAiEAydPYDsPmiooMWD9D/2BgJXCPrndu6JI5If6EhAr9N0Q="}]}, "directories": {}, "contributors": []}, "0.14.4": {"name": "archiver", "version": "0.14.4", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "lib/archiver.js", "files": ["lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~3.2.0", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "yauzl": "~2.2.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "1e55f081f0ad96622990da016e7f1ea091143c16", "_id": "archiver@0.14.4", "_shasum": "5b9ddb9f5ee1ceef21cb8f3b020e6240ecb4315c", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5b9ddb9f5ee1ceef21cb8f3b020e6240ecb4315c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.14.4.tgz", "integrity": "sha512-Ti9FxDEwyJocNa4RbIg/aJQjSJ6BSUBIzXM+h5wV+krl0205r6FbL/QLIZ0h84G4FUoO6JWXaTH3y4kL9r6lJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAzcZtAbc80pY6bysugFSU7JzdsmBW2hEMHvQ+MaplOZAiAPgbkPRpuQxvqAOGiywMTAAo3mA0F4UKiY3itXq3CBWQ=="}]}, "directories": {}, "contributors": []}, "0.15.0-1": {"name": "archiver", "version": "0.15.0-1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-json": "~0.1.0", "archiver-tar": "~0.1.0", "archiver-zip": "~0.1.0", "async": "~0.9.0", "buffer-crc32": "~0.2.1", "glob": "~4.3.0", "lazystream": "~0.1.0", "lodash": "~3.2.0", "readable-stream": "~1.0.26", "tar-stream": "~1.1.0", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~2.0.0", "mocha": "~2.1.0", "rimraf": "~2.2.8", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~1.0.3", "yauzl": "~2.2.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "3c08a692446c793f269a6c62a9b39c4e3e694ecd", "_id": "archiver@0.15.0-1", "_shasum": "343fb500d6957f8371d3b2dd5d494ae9e0dcbef3", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "343fb500d6957f8371d3b2dd5d494ae9e0dcbef3", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.15.0-1.tgz", "integrity": "sha512-RoPaFsbjv9Rv6o26aUow3XJ/kwA0+5kfcTTkRC2xq211BO3t2Ujtx9ETWmiEHqyMymp3pHvEFCmvnvZ2Rt1ArQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGCWWZU70RhZIlQKMgi+//Ls1REvTULWTB+5x3HL59TNAiBDjhY0TZnQzh0ybyIJ+zO7NoSrU6xRBdVHl7KkItMbCA=="}]}, "directories": {}, "contributors": []}, "0.15.0": {"name": "archiver", "version": "0.15.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.4.2", "buffer-crc32": "~0.2.1", "glob": "~5.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~1.0.26", "tar-stream": "~1.2.1", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~3.2.0", "mocha": "~2.2.5", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.1.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "13baccba8d3d1742495610bacca39be9842dd46e", "_id": "archiver@0.15.0", "_shasum": "ec7e60b6540a3b368aa78586310384ecb8283204", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ec7e60b6540a3b368aa78586310384ecb8283204", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.15.0.tgz", "integrity": "sha512-p7QuUsLLrm3+J6d+8V/MB6ScrbeHkTwVH6E/kHGi7yJxxf+MFgZIN+8GkVNjXMWduLqYkYz38nf5rV9HTKQ7pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDhKpq1MHApL3x6bHQ30sZC6RqYxFif1PGcE1PFW9Kd4AiBdUm2UM1tM0JQem19QQ5XtSW7LemaA2Fp7C9v+4uB7Eg=="}]}, "directories": {}, "contributors": []}, "0.15.1": {"name": "archiver", "version": "0.15.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.4.2", "buffer-crc32": "~0.2.1", "glob": "~5.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~1.0.26", "tar-stream": "~1.2.1", "zip-stream": "~0.5.0"}, "devDependencies": {"chai": "~3.2.0", "mocha": "~2.2.5", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.1.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "38da1216718d9d0df409810758fa5160f82c6cfc", "_id": "archiver@0.15.1", "_shasum": "11bd9ed5639b1e35ff5e95bf728d1faeaf2465f3", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "11bd9ed5639b1e35ff5e95bf728d1faeaf2465f3", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.15.1.tgz", "integrity": "sha512-OBMb8WDBxHtRXh/EjA9hoza5Ej51OplYnNFZaaXq+J1RbfnjIM40H2ADGxN5jRsazGX+QqTEO49VPl3yMyWWsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCV+Ek3p4Q6Djh02yRPISGyv9/WCyWyIy21V8Vv20j21QIhANqKrJEBlOYUSZluzkZktvJU7AOrywXRawL9A/B/WeQn"}]}, "directories": {}, "contributors": []}, "0.16.0": {"name": "archiver", "version": "0.16.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.4.2", "buffer-crc32": "~0.2.1", "glob": "~5.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~1.0.26", "tar-stream": "~1.2.1", "zip-stream": "~0.6.0"}, "devDependencies": {"chai": "~3.3.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "5a6c8cb63969c9003bb5134a9ead70ba6baf202f", "_id": "archiver@0.16.0", "_shasum": "bb570346899d0865eb77ed66727ab3c634fc1a50", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bb570346899d0865eb77ed66727ab3c634fc1a50", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.16.0.tgz", "integrity": "sha512-wZipcxc5koIYTju2m1Ky9KfcdleNBALGfUgAKrnT2dBFVaAEb6hSzmrOiWaWD7yVkJf5qh3RhiFYQPlglqFxAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFeTkRU4kFvHwSJ0TUc+EBC3LaatWJBpzsynhmbEePevAiEAsY7hzC57g5lqZwhHojGRLMmUWYEG9By+zQk9r1LyM24="}]}, "directories": {}, "contributors": []}, "0.17.0": {"name": "archiver", "version": "0.17.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.5.0", "buffer-crc32": "~0.2.1", "glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0", "tar-stream": "~1.3.1", "zip-stream": "~0.7.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "c7782e6e1c039316407f340aec820b8ba49411e9", "_id": "archiver@0.17.0", "_shasum": "eeb8044ff8f53171b9416de4cb171c0d508dc95a", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eeb8044ff8f53171b9416de4cb171c0d508dc95a", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.17.0.tgz", "integrity": "sha512-ZYzWj1IMLjsNM1J0rFAyjvaNDr6azSCI1wAaIfqAb2TBEzzy+2EabmckZjj/ehwtUM471B5sEgutEyDnjqsZNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICakLcj7/LwggQpyMXQgqzGBD9HE+tdhvZotnAsSYgnoAiAGo20NcUfU9c31suWrBaaSNRRpxUFOYVilUMWLMJ7cUw=="}]}, "directories": {}, "contributors": []}, "0.18.0": {"name": "archiver", "version": "0.18.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.5.0", "buffer-crc32": "~0.2.1", "glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0", "tar-stream": "~1.3.1", "zip-stream": "~0.7.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "335839e2d9e52ad887385b067de6ec5dde979145", "_id": "archiver@0.18.0", "_shasum": "10d41a6d30865e974f359594f241ad2f82df43b3", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "10d41a6d30865e974f359594f241ad2f82df43b3", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.18.0.tgz", "integrity": "sha512-D2Ekymvg3K7oCQJG5ACvcdMndmasbXwY4lYCxb35sV/ozBUzos1rzZVGF0UMQz7ZMGY7Uq16p9fOgqt62zk2PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICxC6A8Zy6zyVN2jsUbirHd68OrP/u7NMTU26t+7DKVhAiA4FwYy4HZt+JDoPcPL4DzMFbU40r1/kfEZrNLyib8zdg=="}]}, "directories": {}, "contributors": []}, "0.19.0": {"name": "archiver", "version": "0.19.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"async": "~1.5.0", "buffer-crc32": "~0.2.1", "glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0", "tar-stream": "~1.3.1", "zip-stream": "~0.7.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "18e7f310888782e8c6f7497c0dbc21bac9c6ac2b", "_id": "archiver@0.19.0", "_shasum": "9c5af21e1e6e2751b99015fc48b8059a6cd776e6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9c5af21e1e6e2751b99015fc48b8059a6cd776e6", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.19.0.tgz", "integrity": "sha512-HhNZB8OdISbzpyJ2jh+0jdaU4tHADwPsuDaX7FWB1Kt7nunQN6V/v9ljH7lZywkZoB9fBuPWn09YGL6eTOwNDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjCogVpXfvytFyI7HXK34/f8nXeDLZXN4gqnBiKVgqvAIgJOcOlwU8GY/dWiCHy39Zo3ReFbuQ3OFx3vSl3rBKid0="}]}, "directories": {}, "contributors": []}, "0.20.0": {"name": "archiver", "version": "0.20.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "~0.3.0", "async": "~1.5.0", "buffer-crc32": "~0.2.1", "glob": "~6.0.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0", "tar-stream": "~1.3.1", "zip-stream": "~0.8.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "468600bf9b41c2d031c6f057db8894726382d4ca", "_id": "archiver@0.20.0", "_shasum": "c488ec5fc5b905da358bcd1835106e105648e302", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c488ec5fc5b905da358bcd1835106e105648e302", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.20.0.tgz", "integrity": "sha512-bkgePbrcysFfrnNP30/Kdyoby/VEEx2AM7JoQCztg6AgoqqrHuwBPrAyZDvQ1DVdXveBN/NZst6KQS/Hp09GrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAs1l6nNvNPxbuiNL8GCVQ2q3XewH2Iit+bq0pNPEXj+AiB7Pauk1ha64GE+7uHTn4o/OOp/u8ZYhb+rGJ6Qflu2fQ=="}]}, "directories": {}, "contributors": []}, "0.21.0": {"name": "archiver", "version": "0.21.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "gendocs": "jsdoc -c jsdoc.json readme.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "~0.3.0", "async": "~1.5.0", "buffer-crc32": "~0.2.1", "glob": "~6.0.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0", "tar-stream": "~1.3.1", "zip-stream": "~0.8.0"}, "devDependencies": {"jsdoc": "~3.4.0", "chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0", "minami": "~1.1.0", "stream-bench": "~0.1.2", "tar": "~2.2.1", "yauzl": "~2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "0eecde8525112bfadd96e7141639b07d4878231b", "_id": "archiver@0.21.0", "_shasum": "129da0c8eae1a9bf08a012d8a4e2043a1e2909ce", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "129da0c8eae1a9bf08a012d8a4e2043a1e2909ce", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-0.21.0.tgz", "integrity": "sha512-Y9qsG7ZosW5K9epNUdJHVU0ecvqKqMIcckmCEIfNOGTlA+yU/6/NkJiOjimjcEFdlXcCw7nR5y0nagduO56NhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID0pNc4gc9GfeN/XapeO8Mc+eJ9dvJMidalNZD8UtyU7AiEArVB/dJEDH+sayNNUmKTarj8/1KBKC/cYBMVYOb/ql4s="}]}, "directories": {}, "contributors": []}, "1.0.0": {"name": "archiver", "version": "1.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.0.0", "async": "^1.5.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^2.2.1", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "6e9076775fe97ea9202b9fe53908d7abdbac3dd9", "_id": "archiver@1.0.0", "_shasum": "de1d61082e947755b599bb3bc27130a4c859fc83", "_from": ".", "_npmVersion": "3.8.5", "_nodeVersion": "4.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "de1d61082e947755b599bb3bc27130a4c859fc83", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-1.0.0.tgz", "integrity": "sha512-azvnpZFaorQ7D8YBF193urzFv0E8R+3nuFdXuG+9X/TlOpcsuTGOtDXOOtBNpWuxn8Ue/2ksW1O84LoZ5RKghw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGWZvG429eQmitO5vwpYjMvfLZT3fRDTBKfv4LPHJMDAAiEAx9o/zzTOSKjLsA5iTntKXP55mmRQK6tDqXW1EChKeYA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/archiver-1.0.0.tgz_1459916011064_0.06444857316091657"}, "directories": {}, "contributors": []}, "1.0.1": {"name": "archiver", "version": "1.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.0.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^2.2.1", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "b6e7725ab646ef83d1ac53f045487de43d87811a", "_id": "archiver@1.0.1", "_shasum": "aaffd0fda3e03c77ee45169a781b4d9be5f3f25f", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "aaffd0fda3e03c77ee45169a781b4d9be5f3f25f", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-1.0.1.tgz", "integrity": "sha512-TyUs59sBvAstN/7ydqU0bNytEZqho/GyoOsV1mNGXBcLnkC4bCuvmRSWrn21VrNStbyYSAqFPZD5cvfWN8Jk6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPcLp/C96WONl6UxBGA7T7G6abUJOSt8KqMqHcjojhZQIhAN3cP+ysIs0dxxkDfpidOFDQE1UG3FDxRbg9efFixRWO"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/archiver-1.0.1.tgz_1469670960329_0.05152583378367126"}, "directories": {}, "contributors": []}, "1.1.0": {"name": "archiver", "version": "1.1.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^2.2.1", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "38ff225bdcf0ad4ff1e6395212922498d1f2e270", "_id": "archiver@1.1.0", "_shasum": "e1e8c4d356cf155308f351d60cc18cb6fb2344ee", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e1e8c4d356cf155308f351d60cc18cb6fb2344ee", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-1.1.0.tgz", "integrity": "sha512-d+FdRgDU6LkPVX+atAxXlk4GDvShF45JrNYkQSLwUsiBEaVOKaLufwIO4IhQXJEScDLuzftx+Y0cIy6dY6QRmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6f3kPW69Z+xSHudM0Z+PdEye887LCGErXVAB8nI70hAiBAiweIUYxLbZdQZ8MwBOfiRae+ibzi52R5cNMYmQZKEQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/archiver-1.1.0.tgz_1472514513200_0.19586061546579003"}, "directories": {}, "contributors": []}, "1.2.0": {"name": "archiver", "version": "1.2.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^2.2.1", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "4d9458840c96669714ed47c0857df9b482e7a8ec", "_id": "archiver@1.2.0", "_shasum": "fb5c6af5443b3fa6a426344753bad2a7b444aadd", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fb5c6af5443b3fa6a426344753bad2a7b444aadd", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-1.2.0.tgz", "integrity": "sha512-5GQRAgpHGPwWIiMzL9lthd+t75fLi8BpRBYtflomSYv2i6+EO9trtwWAm2+zGjIuwKmVmBRknAZFFBSqxYxiJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+YqOWSZOh4+b8z78zyHcfP26NhX7SslScacSWLUvOgQIgJnIeYejnaIiDLZWTBg/GyzDulP7c0BmnSc1NI89yRs8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/archiver-1.2.0.tgz_1478128727230_0.5829374848399311"}, "directories": {}, "contributors": []}, "1.3.0": {"name": "archiver", "version": "1.3.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.1.0", "walkdir": "^0.0.11"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^3.4.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^2.2.1", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "14e63cdc327e7630420f15acccbfbd598e3a3c40", "_id": "archiver@1.3.0", "_shasum": "4f2194d6d8f99df3f531e6881f14f15d55faaf22", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4f2194d6d8f99df3f531e6881f14f15d55faaf22", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-1.3.0.tgz", "integrity": "sha512-4q/CtGPNVyC5aT9eYHhFP7SAEjKYzQIDIJWXfexUIPNxitNs1y6hORdX+sYxERSZ6qPeNNBJ5UolFsJdWTU02g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaoFA2eMqD1K+HzzFTKp3Lt/QTkj4jqbBLKPidSRXPTQIgA7N55h179RjgRloc4/dtrcWnW1V0th0+xhm65rFN/oE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/archiver-1.3.0.tgz_1481644082536_0.9562644546385854"}, "directories": {}, "contributors": []}, "2.0.0": {"name": "archiver", "version": "2.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0", "walkdir": "^0.0.11"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "4ea661ec8a9d4cfc43f6c84bbeb63147156648e8", "_id": "archiver@2.0.0", "_shasum": "ffb73ecccd8dd65b0019e1180f78092a053d43c4", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ffb73ecccd8dd65b0019e1180f78092a053d43c4", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.0.0.tgz", "integrity": "sha512-KfHkVxvp13r1AwEoFLLx0msqcAydgFFMp5Z7n9eqRvN6fy+Y+HUff9+NNea15Fyr+bN77oIh60PAhdP6u150rQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDQRhMQNVkFgYq/nUDSJNrJNdNAGnGBR0vhRPgJcnYyFAiBG32cTUpprZ6nVtSjLSCtnhvpa5sJfRpU04IuWpJ2Mkw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.0.0.tgz_1499297350026_0.9806722025386989"}, "directories": {}, "contributors": []}, "2.0.1": {"name": "archiver", "version": "2.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0", "walkdir": "^0.0.11"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "86a62ceb8e8ffb56350e005eb972549094ede2cd", "_id": "archiver@2.0.1", "_shasum": "dad2201c36cf457c152243b932e6c3380ff7bb3b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dad2201c36cf457c152243b932e6c3380ff7bb3b", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.0.1.tgz", "integrity": "sha512-laVk+HI1cZV3oiw1D5MuKrzQ9PUYXBazS+KLbrNpl9ee6Fd6bJ1UfpRPE0jyVzNZQdUIvb2NpSsJ0/QHphoh+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBr1/I6PEsfV2pG3UfeXDzyhBmdKCRLxbJT0fbsSpUmPAiEAkkW8QqkexGiW0VQZ3Femv6nYqEGaBCzwwcoRB2D424M="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.0.1.tgz_1503707126389_0.14689469314180315"}, "directories": {}, "contributors": []}, "2.0.2": {"name": "archiver", "version": "2.0.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0", "walkdir": "^0.0.11"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f6ba2ce47778c21b271e8b0eb15f17f1c31a33ea", "_id": "archiver@2.0.2", "_shasum": "5de5634076f23ad8f3b3d507a74a11f3c66b9982", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5de5634076f23ad8f3b3d507a74a11f3c66b9982", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.0.2.tgz", "integrity": "sha512-T8hH470F6l5/2GauscfOS/BSyCoG71ucu7DJFiYOdmAbJgY+Z2Hanx2TqQp0zVbqhXkuzu20t4vVhIa7GJhoaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPOxURi7zMbbh9E1SZhnewM8eDCFJvWiO7FlUrhd92igIhAKxy2Tp2933J9Gl9TB5u8x/0D1wBldK6uoSkcoLFg9BO"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.0.2.tgz_1503707545247_0.21387374866753817"}, "directories": {}, "contributors": []}, "2.0.3": {"name": "archiver", "version": "2.0.3", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0", "walkdir": "^0.0.11"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "b36a8322fd6f92fe729bec105eb10575e201bdf0", "_id": "archiver@2.0.3", "_shasum": "b4360bb584af1437991942716f21d7c523d1dbbd", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b4360bb584af1437991942716f21d7c523d1dbbd", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.0.3.tgz", "integrity": "sha512-TqHyk3if+6c3elGaLZu+wPMnaY2hm8PYmgmt1J34NZlFyHKwTiSugJFLMfb0K1xbYAgRtDJjjyW3T2p2B1f//g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGjXAS+QihjsiGlo3fAoG4/LGK9M/RZq0V6EwPItIM4gIhANMJ9k4NGCZTO3S3AU5AN/BAtLsFImrNqa+KDRxXq5Of"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.0.3.tgz_1503708703976_0.2944835510570556"}, "directories": {}, "contributors": []}, "2.1.0": {"name": "archiver", "version": "2.1.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "3d0f9740c1aa515630c57f13ab025388e0f2cb77", "_id": "archiver@2.1.0", "_shasum": "d2df2e8d5773a82c1dcce925ccc41450ea999afd", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d2df2e8d5773a82c1dcce925ccc41450ea999afd", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.1.0.tgz", "integrity": "sha512-jTj7icZ0qicfXcuxguuwzXgj10wSKk7jmIVFGEj7ggZyMnGqh6nNWydqQdiQ+aHs2NNO+JaM+4iEvL9ppuONbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB9hkbalXl3GHWhGRf26YKdtrDyCDUo1OoRwbAenuqPsAiBJBuKzxStWQic8NuQtrq9RixoBBcUZvlqV4TTulQYRlg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.1.0.tgz_1507824846077_0.04930658685043454"}, "directories": {}, "contributors": []}, "2.1.1": {"name": "archiver", "version": "2.1.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 4"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^1.2.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^3.1.1", "rimraf": "^2.4.2", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^3.1.0", "yauzl": "^2.3.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "d809684e9673a1627e638f57579725aed909d6fa", "_id": "archiver@2.1.1", "_shasum": "ff662b4a78201494a3ee544d3a33fe7496509ebc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ff662b4a78201494a3ee544d3a33fe7496509ebc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-2.1.1.tgz", "integrity": "sha512-01psM0DMD3YItvhnAXZODfsViaeDidrJwfne3lsoVrbyYa/xFQwTbVjY+2WlEBm7qH1fCsyxAA1SgNr/XenTlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE7EGqI61r4RMzm+LdGpvFPlSRPFA2Zp/RC7/685O+jCAiEA5SKXBdS1Bv9vuTRhWV2gP7WJ+XD/80pGpWcAU5hwXJ8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-2.1.1.tgz_1515603748698_0.6485320313367993"}, "directories": {}, "contributors": []}, "3.0.0": {"name": "archiver", "version": "3.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.0.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "zip-stream": "^2.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "^1.0.0", "jsdoc": "~3.4.0", "chai": "^4.0.0", "mocha": "^5.0.0", "rimraf": "^2.6.0", "mkdirp": "^0.5.0", "stream-bench": "^0.1.2", "tar": "^4.3.0", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "78c7c398d98ed0a97bd1d36ea8da2fb2b60a32ea", "_id": "archiver@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5QeR6Xc5hSA9X1rbQfcuQ6VZuUXOaEdB65Dhmk9duuRJHYif/ZyJfuyJqsQrj34PFjU5emv5/MmfgA8un06onw==", "shasum": "50b2628cf032adcbf35d35d111b5324db95bfb69", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-3.0.0.tgz", "fileCount": 10, "unpackedSize": 48146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfeViCRA9TVsSAnZWagAAzgYP/iU/K+8Ucse+jf+F0X7e\nik/FJaQ6l/T2MPaMgZYEz1jwvy97HQHSDUlpb33Z6A99iC8AbGWXas5obmR4\n1a4Z0tsuQQyvRXtGE01pPMc/SsfgdWQRwG9X2kq9Fj7fm0DV6tnEbVFjEUWd\nZSvWEbOgqonugKXgxGJTlDWtRGYmnQOF+LKCQ7wI1JZnOkpmQPQWH/FSBN7M\nRBACFaI7dVL7D0k5m2OTD6iHA0l1nrP/CcTnaVupoVuVXuZQbOytOxzFBYTP\neiAL6izvVG02LJ/k54H7JNK8UWMKVsWBzQ4j6Luor+ti6nXKzbgZlF+6YCkJ\nR1qLM0VRwjv+v0qWajAe1uIKEj9r3izm7oYeEp/De3MzDi8x6MgbHR/1p2jY\nVM10iWqI3l76koAJHwqmoKTB3Wx06ysg66dG5X8lBGyV9UoePEHv5RrvBPNR\n/KcFJxS7Ihglr1r9/Tzig1NcJ4gLcCaoqw9zZZyYC1mk9vhm5fK8dlHDGbVr\nizZNM7+ctGq984uCP/T2MHVy7fz2QbrHwGrrdGOttgzx79NKx4yVXD7jPxeh\nPdbnm4GeuH8UZGMWpHvoQJblHni7400kC9hIRkYRoEEf3y5lC+tFIjGfwCEb\nUrWMeqMPkoh2vUULE9nDDM0C9DA94ndLpf2RzAa50QOnh+gRxSn4om91Fkeo\nyN7s\r\n=SsMQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7JXmkLvQKTTmgpWHd6A8aJbCvW6nZ1Dev1BqIQgxfRAiBQSG00FbEks/1JQqGk2Is/xNo3gwO5tym5zg5d//zwcA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_3.0.0_1534977378106_0.9304089401875357"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.1": {"name": "archiver", "version": "3.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.0.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.0", "chai": "^4.2.0", "jsdoc": "^3.6.3", "mkdirp": "^0.5.0", "mocha": "^6.2.0", "rimraf": "^2.6.3", "stream-bench": "^0.1.2", "tar": "^4.4.10", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "042e7a2ff10697cf3b7835538930d8d24deacc1f", "_id": "archiver@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yYXO0MxbTbPuAd/4+Gb6+1QCvVXq3wiQ1pT5V6sakVPGJ6C30dL287lCcBHXoIJmCCxakQeIZDBmGlgPHbEaEA==", "shasum": "794c4dbb1901b97b424b889f93ce5a1994c78956", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-3.0.1.tgz", "fileCount": 10, "unpackedSize": 48346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMkP/CRA9TVsSAnZWagAAqFQP/i3CMOAwkhq+8e9e0uIm\naE4IbvjkSp9Nq2A4g2WboY512K2n7QAokbWjDX74NG4VQHwG6qS3lsW5+DQm\n2t27RUxyrHwhdGOJD0ZYQUjI+VzC7CX2LaeQh7yLjzPjGMySTtrviA092+xV\n92E2lrsXUbo+l0N5z53UnoGYgas+qpPJVjT7QH7YEpjGXOfosCZXXFNIStQN\nf/eN3JpNASgTyAp8WSuIgz1Y9QTxHIOkUy3iBuqsGE5yp0ipUcQdNnF7+KFc\ncN5+MG1jpN7gZoENfgLV4UPyou1x0UFtKnWVMIH7tIHFDEEnwqKjBZg2Xlkr\nANO0+OVN3vFHi29UUKqQMx7ysIM7yjW5+MW3a8zK2G2UTTVXhre+7DE4InWj\nDuA2yYK2QeeSe2IHi9wiw8CnlbzeqjcOaAhzz2zTTwMOaR9JlMoTlW2PiSGz\nH0Q9HGSC2ZEKMZALFKhCViKiuZpgfpH/6A+Zu9wEKmGUJfWc/rKz+/C3DWzA\n/amgYjPn3hMN2WigMtS2aWLhRBSMRgTLAqLGLrmopWBKLvuDzNOSxfLFHJHR\nsSqxEdoeZEIgDWgvOYq3y88oy0NGQNgeZ7naFZZRDqfMyk7eE1lmPHuBsvIg\nbAI00xEJeeU+42Qhb2V053BUf5bPb6kyC/vCNEev+F+FF96GN6/6+39sLUMs\nlsta\r\n=aSKb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsqqPJ6GzmdEovNCNELZVDU1Nv1kCDTVbHsjghDYwfFAiBzgXT98G4Awi0ztvRbfu5BuAVn3kTmq9piPRmU7nMjbA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_3.0.1_1563575295060_0.17862745711176387"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.3": {"name": "archiver", "version": "3.0.3", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.3", "mkdirp": "^0.5.0", "mocha": "^6.2.0", "rimraf": "^2.6.3", "stream-bench": "^0.1.2", "tar": "^4.4.10", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "6fa6e7c7a6184ccee992aa49ad4d0852c11814c4", "_id": "archiver@3.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-d0W7NUyXoLklozHHfvWnHoHS3dvQk8eB22pv5tBwcu1jEO5eZY8W+gHytkAaJ0R8fU2TnNThrWYxjvFlKvRxpw==", "shasum": "7487be5172650619eb5e3a473032a348a3412cdc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-3.0.3.tgz", "fileCount": 10, "unpackedSize": 48692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMl36CRA9TVsSAnZWagAAdLMP/jlMAhrCnbgefk+e57dC\nJKvvE3tL9zUx32RR1WJwUZ48Oc8YrsaiGaLcNVmjGXwSzIzELqe43G5X8mB1\n9ex4BKux5uPbq1CHtST5DsPmccbzhU6wIStQ+s/tjs8P5GpumF7JD5rwDqBG\nxOWKwVlkKzx4H01AC81UFA/9zW/Ley3u/mx/88dHT1gqw+XEnX4EvfZCxuKZ\nwjWW9vzR9EH9Y6Xi98XWIkD7WjKvqr9NNhRrs/oDCbp3PT9quCtjLYq74tiC\nq2nrUx8+qaosF36BB194NFs3MuGF69DXFM5xoE10ekUNnUuD62BpflYcOu7w\noavxkJYgFYz7jAlljpGDrNJNxxcwu5oSDuXHRsr1v/Ht501tANmrTldkWBtt\nY8/CQXrABx58B2zjKp5Sq1oUoB/T4ejmU8QkVKK9JFqLdQovK97pidvcKaer\npSPjnubv8RKOpRahUr+PUEntrRPogWfQBYjVrhMyP2QZf5+gvJZp+9+OAf8Y\nlTxPIUQsMxp/TnK7PKOs/A+/+pqjG1jLVit8mLWPpyK7ri/TOmMe8GJSfMOy\nq0vIpO7PalH1y1sbM1r2Kn64MEfvyHtgHlxLqd7jk7aI+Imnk6NFImJxj7jf\nJvlC2MkieOPKZKMyZ6zpRo8TNpEKIVmrbIsmUVzcLShgL+rlj3E//CDR1GaP\nkqF7\r\n=QjOu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEwNipnO46FJmGxpAApegE/0xCrwVrxcV7w6hWYtm7+nAiEA410Jfs2U8Y6fIhG1rNi5bTOpNJnKgAbBqH13NnoEmzc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_3.0.3_1563581945902_0.2485779341869072"}, "_hasShrinkwrap": false, "contributors": []}, "3.1.0": {"name": "archiver", "version": "3.1.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.1"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.3", "mkdirp": "^0.5.0", "mocha": "^6.2.0", "rimraf": "^2.6.3", "stream-bench": "^0.1.2", "tar": "^4.4.10", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "6fa6e7c7a6184ccee992aa49ad4d0852c11814c4", "_id": "archiver@3.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FXYAX8Eso99q0c/Kaby/P4HxGdzwLzTxjTq7WL7dKk3FQkqAdZROmji67Xdn9C2SAXJBH5kqFQeL3UEXVDvWqw==", "shasum": "6ee398fb892bf0840b798c5ebfaf5ded9dbd7820", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-3.1.0.tgz", "fileCount": 10, "unpackedSize": 48853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRGEnCRA9TVsSAnZWagAAZc8P/2rKlk5MyY1NLzn4YZZx\njlENqDrBWqvdTnhspPls6Aulnv6KqM4tL7VyMQYJlAyY5KNaDTiEq44yldqx\noIsDhclwSDOhvPzddmfoth229rZCCTHw7jrpMnrBroo50Jj0OxvA6qyOXCac\nuBPYPCy+fwdyHrRKSgjuoOIQ6f0jWKTDHVieukhNzw6CJ1/0nO9Bmh5XfQLu\nPEJQH3y7AYW8rcuWee1EWrQRrozgtOzwGcOh6h31NcJF921XaBh53O2/Tbfo\nlY1A+BbjcmHj09wqpe6nmnl295uGJ+/fcPfzW06laf+A2jwr0SBJGSnDxGeT\n/pOEmOybIryGMesgBjareFHfbjpiNK/bDAu73g2KSvRj++HIfs/QpwQxd8Wo\naitH3Iq1NlaUqN96F3m6imPb4T7cQBj2PGovIFzb7lbhpnJ8m07KaNKRTa78\nKR3EV72j/tz+gno1hUURat9B4vrmCjS+QyOnFc0sB5kAAlLzIEk8KpLCVTTg\nbBIKwrypFkNHdLylMBewQe8jgZGdUDrRsIDZhABqIScaeTV2jY2fEvN/R2pN\nzh5mDCYazquwIphRMWxu+Fc06+VlsF+pIpsn+TvOoN3+RpfDepdSXACiR0m/\n5S1x+dV1SEh8wI485Qg364XZKPfqmXdZQeUvGchge+8aKppwFtQSkIK4L60c\ntaf3\r\n=doz/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDeN9vjsCpBpKnbMQ3EnBT/Ub8UuqqqTmt15shdGJaH5AiEAsdUxbhtWnCwLUazWtqk7UEK2ygaSkmkG8+kuQqayLn0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_3.1.0_1564762407225_0.6267741205013"}, "_hasShrinkwrap": false, "contributors": []}, "3.1.1": {"name": "archiver", "version": "3.1.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.2"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.3", "mkdirp": "^0.5.0", "mocha": "^6.2.0", "rimraf": "^2.6.3", "stream-bench": "^0.1.2", "tar": "^4.4.10", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "60191864f4fbebfeffc2a06615d995da214658a7", "_id": "archiver@3.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5Hxxcig7gw5Jod/8Gq0OneVgLYET+oNHcxgWItq4TbhOzRLKNAFUb9edAftiMKXvXfCB0vbGrJdZDNq0dWMsxg==", "shasum": "9db7819d4daf60aec10fe86b16cb9258ced66ea0", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-3.1.1.tgz", "fileCount": 10, "unpackedSize": 49008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRMKVCRA9TVsSAnZWagAAmcwP+wUju/WfDgPsn7KbVXcO\niXN75CS/FwkV3zTxsre7/5P+6NoFE1T0kY2sc+Q7bNgJew8XzO/cXRU0+7AJ\nc0Cde6t7r5NfzEqjZOZd16n5yhc4TQnbafqx3itbZt9SQ3cvRBfxgZV4wmoZ\n9OvHTYX9IBvGksKz5DloAgfCmDuNv5b/pv9kELT9AAZQndcERJbba9g5fAc1\nbbFzXjpXAk6UQXkpUtQj+plEHJExHXx5Nnz9O2U9sqTmARVclRDioiQHhUVO\ngonSkKjo7C7sY7As5YNyJGKL2SByY7GBU5Dyn77m26RIYK6flIEheqsn7kpH\nznpst5qcjGXfWFR12KCVg5o+aPfQEpNBS//WZe8wD2ho87+DNBWmYAm7Oufw\nYj8GymHwGwZH8a0IkByWExHl4hHa3wlJ/+XE9iFqHSbCR5MWCsp6EPiEfIXe\nxf9Q2M90H/m/QKFwgxlkDuXdUq9gOAKOcP7xWvxdin/faXF4H3WQD6vioz1q\nYnQqV9hZk14l/zaD6KQbTEue2bG1OjqXxehqEj4nvEOeZtY9oYzNsVltFCnQ\n7PbPRkHo/ABVOpEiffTang2WhTg3ODMk3oSQK3L+KZuM/RSnThZ8Bwl0Fcz+\nXS7/VqrEb1ZndDNT532dN0TBFg2/NyhOWfQXWhPBawL6bdCxMBAdwq/0a66G\nsHwb\r\n=w37P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG94WPZceC8sRS9Yls/+NUhpgMihfyR5KwfbmXeSwkO9AiEAlUdyJnfuRRdEOf7ypcVgdjApwkT8303iXF/42vr+HFs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_3.1.1_1564787348535_0.508525605334794"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.0": {"name": "archiver", "version": "4.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 8"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.6", "readable-stream": "^3.6.0", "tar-stream": "^2.1.2", "zip-stream": "^2.1.3"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^6.2.3", "rimraf": "^2.7.1", "stream-bench": "^0.1.2", "tar": "^4.4.13", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "63f574a7b218c76e518e0db5b9cfbea7a3c9fe4f", "_id": "archiver@4.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-2ay76fdYM1OtC3rgPQVX1fXfgn8Yac5IEvMJU5rUuTPot5jrl3LmFhKuTDOGNUIlTZ6cJEaAidli4favNvvKYg==", "shasum": "f905272e3a5999c2d44fd6ee9ee7bb4d291df865", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-4.0.0.tgz", "fileCount": 10, "unpackedSize": 47333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeliKaCRA9TVsSAnZWagAALdwP/396olLYyMgrhGXNrOLN\noELMlHiWY7rniDpiwvajLJ6MDhjjL+Dlb6y8lqS9QfxYkwWJMoKxH5D4MISm\nkCy4V+0pJC7ZvCNSYtxNOQS/AMTWr4du9n65AYp6Q4qoFDOGdDRqILprAjZQ\nlx8rrlR7LQz41FLCAW7aaAPxRYoqjVlgmo/6f7fp8oXu/qbzovQwJASG/9a6\nyUKRYcsJ1RCxVrAlVsZmq3DrWl4Rm1Y7jkv4KEX5nNfZrasiGeBwnYMJnUNK\nwck4hOY3Stjwm2xf9ciD0cwzJQojYqjFg2Kipk6CMoLfBt453T0Z0ZdrGXkD\nGQ/W8EWj1I4iOWuvouxw5SiFuJY+X4p89xnQSJODfSLEQGAEf/qEYtF9+AyB\n7ZZXbExXLqRXEbh/lie4pnu9JApeOTV5RVVPoa8Y/M7G3C4B9CSJo0F8JTJx\nFpYyPbXBz0pnFq6AznnvkmSdsTgH19zvV5RS+1fpNCezSacTxZACAuwS24Pi\nITLdwiaJs7LtKbh6yfDxfgjIeCY21bQtvNgmEHsiAbvp5D/AC6CIvNBEKmQR\nS9GdbERRNISTC+EDbCAFDTy7gUhD7MYTwwc6Te4BjEbfbPWqSRhrIV0xdE3v\nSpUtEBkztMiRRYITacF2eCcdPu/Dp1QYaGu9LnLsC+czmIHtt9Wr4oYOpxXx\nHl5s\r\n=QGwb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCqRO0CoDJRPk75oj0iCqa5y6E46qtNXZVQAYl+mcr0AIhAONIYw3TQozfO9x2YU35hp/AcF4norJwNapvC8BQkM5I"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_4.0.0_1586897561829_0.8858661111307358"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.1": {"name": "archiver", "version": "4.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 8"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.6", "readable-stream": "^3.6.0", "tar-stream": "^2.1.2", "zip-stream": "^3.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^6.2.3", "rimraf": "^2.7.1", "stream-bench": "^0.1.2", "tar": "^4.4.13", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "a9ca639d1f218961eac52cffa5c9b588c277a94b", "_id": "archiver@4.0.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-/YV1pU4Nhpf/rJArM23W6GTUjT0l++VbjykrCRua1TSXrn+yM8Qs7XvtwSiRse0iCe49EPNf7ktXnPsWuSb91Q==", "shasum": "3f722b121777e361ca9fad374ecda38e77e63c7f", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-4.0.1.tgz", "fileCount": 10, "unpackedSize": 47487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelinCCRA9TVsSAnZWagAAswsQAKMaBvAwQykSYOJd1v0K\nSCP2Kg1k2z30PNxNDtduixRtnCoZfxkenAPC83cxU2p49ijHk0WCJXT1Vg5J\nTlRR8PHcGehL2lKs17b1w9MXBViGIfvTjYJmwjdGAwMR4PvYfFg27kPj7cJm\nEfiAan4lAUSFA+m/JyqffJKUkrHym/Aq39+S11Jk4pq1KihIFupZYqItl3/y\n58SFS5vPwh2l8e8WC8gPAirHoWEM+Ezfz1ZhjBnRFu3FYpvRSt3ZJyeXFQ9Z\nSelzWSRg6ZnbOIoPC2sPcSkvTK4N7+aeJliDxkHlJrDksYAGIwnNHidDshFA\nACuvGa6ky4EMto1xRcdlHP7Qec4zk1UPMThadLelienYtXIIOGf7ZcZ80G5k\nPLFBifbKrot/lqBx/tKNpplVPBQJcYkYmhEvJnGpoQMboSSy2p6h2jCWekk1\nGTLs2YHQ6+2WtUvEpVfJAt7ZD3KhU6ulFnYSJAJ8bGwSSrKJFXYbj8pHsJ2z\nuzWiOY7zyrbvcRCToZVbb50rFO9m05nTE2HZqv92nX3bBZQpFfod6mnfauEF\nqXYksLQbrMVVepHInufc3CHPRDCrdsXalRwmDWUHZRavaXLtjzSGD32rR6/8\n8IVDey1DG75A87fUtylnY1pD/1mZRi4r3lZd31hSTRBHyvGr9smvavjXnCH5\neEcA\r\n=LafC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDY6mHZW7fbzVBTNOdkpnTH20nHWIiKm5hYb+kQnCkoMQIgbVsiq6q/gDhNMmieKYkh2R84HpAxZBxM1uS6V7TI8TI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_4.0.1_1586899393638_0.4756781825050309"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.2": {"name": "archiver", "version": "4.0.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 8"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "glob": "^7.1.6", "readable-stream": "^3.6.0", "tar-stream": "^2.1.2", "zip-stream": "^3.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^6.2.3", "rimraf": "^2.7.1", "stream-bench": "^0.1.2", "tar": "^4.4.13", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f646f86e166c7609f1321ad2d3cbd34ed24201fb", "_id": "archiver@4.0.2", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-B9IZjlGwaxF33UN4oPbfBkyA4V1SxNLeIhR1qY8sRXSsbdUkEHrrOvwlYFPx+8uQeCe9M+FG6KgO+imDmQ79CQ==", "shasum": "43c72865eadb4ddaaa2fb74852527b6a450d927c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-4.0.2.tgz", "fileCount": 10, "unpackedSize": 47640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCd0dCRA9TVsSAnZWagAAATwP/RJ28W06LV/NpAybG1h4\nPpgkfE/wEhMenCsdJTDedkrICLpuzFWYKSlbP3FdCkyYBiJLKrumy3tg8W4/\nTqfodaOD81Y+92gJyZbJ3EJX2aplYxSTdZXc+r8KKCB9lVZRLnA6AqbmfmXv\nalNIjo59vgBKTF9f6HBD6LknPyaGwk07NazU6BGRFzFqUYg/Oesvgijx2CWQ\nxD6tGJF8MfzI5cCVbjgvxsLvRnYB1r2n4UyyWvakT+5wOBFSrPAO8FMukD5L\ns4hTyK9eJVdgMygLBNInWuM9gl9FnxJli3TEXG3VZgAu4WA9jejScU4Qa0EE\nSvEv0ermHYgkeVOFhTdi+yLMOAUhVQD0nSQSQ9kW8Tc2Kq3tih3U9JvYyEbC\nWWfXlxd0uStIycskgBFe/vTKu20UXj6+JzjDD/90H0hcxO67TnLaNSGbgXTR\nnAIkCNV/wT7cN+OfY/Ados4edwZbdppYlAATybRcgVDNhRF5XtQYt177BrCp\npsCVou1GhZBIa8niW1ySulcJGsgQ6onPxuJjAxPFaheeWEcWI8p04INV3aFb\nuRAZEkkBlDDvt+NFYFhUx/KKzaOEAZbM7+g2nzvz0OvRylyuMZZr0lJs6ob9\nLvnmsm1zXR33fvQYp9I/j394cZprc64bYLPYE/aKQP5cIFKA8Ddt9ftjI6YC\n9LG0\r\n=Im6v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGjgLxVnzUJ2xx01/74ifXJxcD0qYsbK6b6jFzxvCyHjAiEAgt0I+q7WVBFVft77E5igNROw9iJzNucbR1Vc/qOI9iI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_4.0.2_1594481948817_0.6003455940908178"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.0": {"name": "archiver", "version": "5.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.1.2", "zip-stream": "^4.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.0.2", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "5d3f168ebcf25643cdff4749739fa6a947fda41a", "_id": "archiver@5.0.0", "_nodeVersion": "12.18.2", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-AEWhJz6Yi6hWtN1Sqy/H4sZo/lLMJ/NftXxGaDy/TnOMmmjsRaZc/Ts+U4BsPoBQkuunTN6t8hk7iU9A+HBxLw==", "shasum": "b1e7dc075a4e18e0aa59afdd7c3e5f3d3321cbeb", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.0.0.tgz", "fileCount": 10, "unpackedSize": 48750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGO2cCRA9TVsSAnZWagAAn+UQAJbd2C5lTDUepaMpRtRN\nda0uCGge/D+/Lw3NsTCNyhqVOqOCottfXvT2pFgWUBsnCwSpcsVL0gh/GOB5\n7qkF48Tw3gp0z/A9eR++RaMiKJ5biBrqzfgFefnUvX8sS9Ln7/DOEgpC3Wfi\nP9jKDtnC4pQJi1DSHK/GrUJQ6S06MVyZ5VaaoqD5kmdwEyW69oJCm7gp8L65\n21vqwF29NbZEk/oKGmUfJmbovO0yfJ4cq9dNt/1TMlaxbXxRC285pK7hX4Ck\n/2StV4XZCEMvlPPZx13J7Z88JN58m6ZYcq3kVRJ+EBHSiwgYEsfWdHXQlx4j\nCev8c5UcRmzcVZIjPzCjwukuGTRN5TU0VaxvmsBn17tqxKZXNXwX6NXkR8Sr\nK9HTjvkbppqt2Io4qw0TexCZ4QVtjMtqfwbBFTQ96ZluWnxMCjQYVgVKFgGP\nZutOxqsdW0tYQghnOAijEbQbBMXCw8tc+7Njc5uO7yEZV0hkGiRp2jFDriJ/\nYEvW1XfGTfZoVsWZ2Q7mMQpSQd9zrEH74bJKWrpdmeznCEfM0/BkE625MrXw\ngjzayJN4ih86LguKSQUkRYqrCJ2ckmaclVVIqHgHqvFjPVempNYB/kFAUt1S\nGJ819oiPpRnrtBWqHh3DRZp89h+tgqJvHvvENzxC0fTPxTleIaj6HSIqUTLU\nyis9\r\n=t6Uv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCchu0E9wSUxo0IbXQxZvzkbB91Vpp2E8r6IjAJmalYtAIganoIdFUz4zzf6GueaJtpCqT3/hYxFEQHN8O1EQ3Obh4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.0.0_1595469211568_0.09872312803763306"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.1": {"name": "archiver", "version": "5.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.1.2", "zip-stream": "^4.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.0.2", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "cef1ac966e231834d6c1f083ee50698b133d17cb", "_id": "archiver@5.0.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-DnYH6anESMM9w1gzi0dp2iNXNTsFUYGs0x90Qlm8OhmlxMESZXQLY3800HwKaFASgyqaw4Yri9I8K4DApgnfRg==", "shasum": "d7e6dfebd2200a0c96e3f405f07f1ae28fb2fbaa", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.0.1.tgz", "fileCount": 10, "unpackedSize": 48762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWsCVCRA9TVsSAnZWagAApsUP/jxu1A5YlTcfgk4cbXa9\nO0BCZchwUWZOw4NFJ2JPNEYnFB/AMXp4xxWORfKQPDJ88xFZCf2vt+qtkJiE\nbdusWi+sx/Z8U8K0fVPCSEwzpL9Wpr05Jc3/GxXLNGGXWm3L54aaw60r/2ok\n2+7Et0rr8NcSXEnFOVO2bowjt1Cp1Hg7f+5I/iRZb8KggAx2rPL2EnBfHwRs\nyHrcq0c+9REenorS51I+1dHoHCiTO7qlL2y5PA0wVOTu3nc2+BWmZI4ftamI\no/TbiVo2g5b3oYzo9yj+e1ayS7QkeUFmbkd+4JzdMEyRshM775/WtRlMw7QW\nHDKKXM0Ptq2ml+KqpADBmK5ip07nmSiIy5qlxUjlinIdYDcgaInb4EiRbmwc\nlO7J22v6CaUNmHKKXTtiFqlzJ+im4R+ZDpSgrL6sAuuGEbL7rrhoqExOGPsY\nT2jf7n2XuWgj4Bqds46NC9YjteVeecZCXDomT0JGEBB5xRfu5Ww0jbZ2aoAZ\nFlqy/wGTo6v3YqeQmqDiCcw+mn7iVh5pgtbeelLrwFrbLa7cdOscGXNKL6wR\ni9tH4uIRcsAu/d3gVjrbYKaujf2T8KS2ssxdXruGLpSTTN+dFzEWxj3Yz2SR\npyp9M8Z1/H6V3MqmRTp05ss5V/tN3fQ67L0jS8Xg7mw8OKh2UhQ815oDw8vk\n8bwH\r\n=c2Db\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDB3JaSw3R8skGWK3meCnCoiPiKlHdijcRRYPIWp6iXEAiEAx0nhY63uc/601ZEH9FNGES6+3CHTo0M6fjtyTTu2ne4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.0.1_1599783061067_0.9720985203518209"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.2": {"name": "archiver", "version": "5.0.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.1.4", "zip-stream": "^4.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.0.2", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "e34d0a39dd636509687277a2ba8dae3be49dd954", "_id": "archiver@5.0.2", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-Tq3yV/T4wxBsD2Wign8W9VQKhaUxzzRmjEiSoOK0SLqPgDP/N1TKdYyBeIEu56T4I9iO4fKTTR0mN9NWkBA0sg==", "shasum": "b2c435823499b1f46eb07aa18e7bcb332f6ca3fc", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.0.2.tgz", "fileCount": 10, "unpackedSize": 48762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfW7A2CRA9TVsSAnZWagAAGmoP/R5NJg1+gXoInB+Ie4UH\nt4/DdIiwSGGsOGtJC8WVW+AcJqAwXeTk/9pzUrjbGckikJo/x4ySXTXxONuA\nHvIdlvgRs/eifqyLd8J2MlhsgVNkFq2yz/Xhn4rPdJJSvA+XAhJEuk2v7lJw\nXtjRoLoDNbhEbL2JzLHCI8tPHvqMw6L+nASGBg0WKoEzSBCOV7mJvXxLaRCW\nRm7gEdRACp/+CITAV3N+XIEf9+AYutlH8xB4Dl+d53omkI2D+pI/FDpwgDMI\ner1sLexEXucs+cf/4g6NaCUccKRmwQu5Zc7YqXIKFcb9EnIxmc1+vjiBtXyl\nKyepigaksAwvfkQox0EWTVaUt+/26MHBRK/IF4xAzSMYQslQKGUElH5AVz6C\nFKyFRUn2fYCXVh7S573Ya32nZPDFyESOeQVQZv2+1AxTvPep4zYTspCqHVEl\n5jBIV2W/808a0x+7ZolQVjw5rFSNjCKQ84OG6RICekfrbnwVe1RRRbB6o9Pl\n/EctKjAa/tmQaQxb/py635ShgwabUQ4Od4zHeGDPDFwcUlhjkV9LcTclVuJ8\nR6yzkfR/Ju8RoDYKG3tlo8CgU1kTWvTSUngDW53dSzpClNavHSxakAr+jMdS\nhBM8au7+Tixc3R14nd1ZjW62PqVdogzJfUQSnLgLxixUfuJurahlf6L2sJwS\n/1lV\r\n=MXZf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDizz5nEc8bwSYUhrza+40Tryr9pTcASGW9cLGURbeEjgIhAL4OjJNuYgWLD4uxUReN5vq928w9cxBgI6qMuBCuajR2"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.0.2_1599844406220_0.5415021686325672"}, "_hasShrinkwrap": false, "contributors": []}, "5.1.0": {"name": "archiver", "version": "5.1.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.1.4", "zip-stream": "^4.0.4"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.0.2", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "2e0edfa5d17239019b59573fd61e735ff48320a6", "_id": "archiver@5.1.0", "_nodeVersion": "12.19.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-iKuQUP1nuKzBC2PFlGet5twENzCfyODmvkxwDV0cEFXavwcLrIW5ssTuHi9dyTPvpWr6Faweo2eQaQiLIwyXTA==", "shasum": "05b0f6f7836f3e6356a0532763d2bb91017a7e37", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.1.0.tgz", "fileCount": 10, "unpackedSize": 50559, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJft1I4CRA9TVsSAnZWagAA7rwP/0TCbUeOxPnkYDXDmbye\nsd1M+/SPVEUc4hwIzcMRGbFbKVJ7ZvKvNGGCmNFiClkvJ7LWPwrOEJPsTa62\nydE6WiMfGSvv8FM+kEDG9eJzWsnR9B2zA9l6NSUIjz7w6sTYt5ifuXBQtD1l\nwgT/WNrxg57WeRWxqLuWU5xXJb/vYNT/DdjWr+PyfqOFWw5xCSW5uPeqRC6l\nNzEKwOrMnVaHCK7+JU8Ae0FksG4Cw+UkjX4kgb9kOT/g9Hx3KRsCsl0D23Qg\n1Xr1B55hFBkMSgD+OxMkzaOMetl5YB0Hg3/W9T+aFqkDuxkX46FClFyYNR/3\n8yOUXyUnrzZdg4LxEGJATs4lGdmjnQx6m1jnhCOUG8R64YtclJLv8kIwbpAW\ngl8W2BArCm+QGES8UooR46gv2roIjWK+TRhs8Nj1SMsi4GUPGJxgMfb/+MDo\nq7me6mwcy3yEFmMoWYtq8qj/HgRT3u6TSVdFZK19Xf4ZMZx3t3jpdSN67LP6\n4dliTYc/VSKnH80CmljJgZbiIwiBpbjMv9m8pIKmwluC90L7/k+AJbl42pLa\ngzgRp5bwe66tt4Ad8aR3csROzkTY3hAd6IfnO0VKGDDzXiMwHF3zymqBI0JL\nxph4JXqgMwEtVLxAtouLEGkRlXLBQNd+t2BfMZmJn10cQRyheCHl2mtM/lxE\nNsLt\r\n=+COA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHkWD4pqml9xoCOh5Te446PaQedlPM605hVhWeKfhCWgIhANva+GEcnVUdhLC42obiejzgjehkFW/uoXAeE7TGxa4U"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.1.0_1605849655518_0.5239379481636914"}, "_hasShrinkwrap": false, "contributors": []}, "5.2.0": {"name": "archiver", "version": "5.2.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.1.4", "zip-stream": "^4.0.4"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.0.2", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "a56eeb64069d17f2a6a32886672f093f8753e75f", "_id": "archiver@5.2.0", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-QEAKlgQuAtUxKeZB9w5/ggKXh21bZS+dzzuQ0RPBC20qtDCbTyzqmisoeJP46MP39fg4B4IcyvR+yeyEBdblsQ==", "shasum": "25aa1b3d9febf7aec5b0f296e77e69960c26db94", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.2.0.tgz", "fileCount": 10, "unpackedSize": 51039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9ohZCRA9TVsSAnZWagAAHiUP/iSBh9tX1N9rZcDC6GSP\nu2VwJX9bvCNsGJ7+Nsd6iUTQ7+ZYNT5zSwAmkzfSaVe9d3Fm4UsLyuGt1ayl\ntHB2Y6DbdZ1auE6kIbqJSYOmV+vPtPVKlvTIitJWRbZ+0t86VUWtHeHYcg2C\nmAsTKXI9cZE8JuWsjj8zhQdmAbG3qf9fmnV62VEXv3zdv2tGjNjVqWoyEyv3\nO7xEVFrK/eam0DqgHcb2mMPVdaYvySANa+BXHuBXHh5MZ/Dcc7+OfhXicpeU\nCOzyyvLaDrG/llANmPsA6ZTqOZ+FG7zA/Ibzkrzgs3Z6LewhgdrXhUnHZ62U\n05mLeeTVF3VlHLBRZFxKwcfx6TLVs0Xs5t+0EzyB81gQ4aPjARZSA9zIjM5t\nh2u2aTtNvf/hDkPT0AWt4HfDez5dZpDJCDLATt/xyyE3O9gAqlpebwZuVcuL\npjYUv69yJEGoo3rX6/ythTOZY14tm6VVj5h3es/NnfA0XhAmcQCVPug0uccb\nefK6YMzfcjdX4+hPXG40npF9xEx8itOtcly7dP4Vtno/l4kA1EyNB6Wbxp3m\nRxIXjuZ8jRFeQb22H1JxlsX5zegsU6nT0XBW09cdHK/ty9zRgdjo+ycb6+gF\nWMjB6y7L/0fDDR/uYAa0i6cdCHKGXuhqCm8Urm9/C6RmWC1MvLO2szv7rNNg\nFcyc\r\n=4uVq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDdctlfZ++2woYRX/aMiBinOqxFN50CuQhT9Lim9EAi+AiBA417kQVGURW37oSUDsTfNt7SIdQeQf9avWWc3goLbWw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.2.0_1609992281171_0.46046487556465476"}, "_hasShrinkwrap": false, "contributors": []}, "5.3.0": {"name": "archiver", "version": "5.3.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.0", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^8.0.1", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.1.0", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f6c9cc2fa6a167594c82428faf22dc59dbba7404", "_id": "archiver@5.3.0", "_nodeVersion": "12.21.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-iUw+oDwK0fgNpvveEsdQ0Ase6IIKztBJU2U0E9MzszMfmVVUyv1QJhS2ITW9ZCqx8dktAxVAjWWkKehuZE8OPg==", "shasum": "dd3e097624481741df626267564f7dd8640a45ba", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.3.0.tgz", "fileCount": 10, "unpackedSize": 51865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRUbzCRA9TVsSAnZWagAASXUP/3n00a37RVI43GEP6axO\nED0v0y1lige/6z5Fkp1mJK1GSdchyVnRh1E4hhRMHR5oWmz7K/5ODdQhw2Ho\nE0CNRp+03P883ILjIWqdm6X6qs3c7Kcs8TpOazG2ILHHLpqIIxTxYoeE4J+3\nQsch8Wgfs1hfksYqmln/AD3csYWJRRs222jbWqzifIxnCfi3f0rT7V8iUi04\nR2iSLLyKILnRth190DasmMw/uNSv3NDGroL1Q+pCnCn8ijzufnRTKHRpefcS\n+6Ob83ZW7bibdsZV7lSVPdW3pA2RpSjlQSzjFihgzEnHPIiUDpFSiW+CRZNW\nN4Xfvnz2ilHF5/SgwnlRIAUIg+cjx0EuwV8G95aiGUy7l8FNzH8bfqxRGQW8\n1xSdBRE5O7sX00sXDY2GGtCCZW/YHMzZQHBY/FfZ/iSPyqGONkKXuciV20o2\nuAqZdvmcGdgh3MN/7Tc97Adjr73Ypie2TIAJ9qhK0jjvoUuK28ewaKkf83k5\nL92AlrDE6eanhazPsRSUitXg7/8r+EC8pXIQhXgo2bpfqA7M1lwrZBxZISTe\n+hMgnhbFNVVSTadWrz3yjIqzzPXpUfPSXvakPunpyQrVS+vmNtxu/SsKDk0Q\nARasl/pJZpPz2UMnYxeutS68hKdX5sTHr5ajZalWagWnSYfCjEXfPEXRzOz4\nCKc1\r\n=zE2C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHOaatAjQpXtLCdHpRM30BEgIZJ3zkylP2zjwjQXLMqrAiEA6PPghdDfgrDauEE7hSCeNeeTbKsW4Nk4oBfeEPFWRaQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.3.0_1615152883407_0.9999354668685307"}, "_hasShrinkwrap": false, "contributors": []}, "5.3.1": {"name": "archiver", "version": "5.3.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.3", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.2.0", "jsdoc": "^3.6.4", "mkdirp": "^1.0.4", "mocha": "^9.0.2", "rimraf": "^3.0.2", "stream-bench": "^0.1.2", "tar": "^6.1.11", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "b5cc14cc97cc64bdca32c0cbe9d660b5b979be7c", "_id": "archiver@5.3.1", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-8KyabkmbYrH+9ibcTScQ1xCJC/CGcugdVIwB+53f5sZziXgwUh3iXlAlANMxcZyDEfTHMe6+Z5FofV8nopXP7w==", "shasum": "21e92811d6f09ecfce649fbefefe8c79e57cbbb6", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.3.1.tgz", "fileCount": 10, "unpackedSize": 52893, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAT3ZuReUCfSPtAmkRtPZGRYSEWYa98XNEAe+MkfCqOaAiEAwBMNXzj3SxKJEBszm469uMr6/eFLWRgL9FvuvRmfIH0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWdeBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0vQ/+I4AQi8pnK8j3z2PyXDvQTnb3U28KWoGjqEL8o7teSwiBmU/x\r\nOssJJ+YEo8keSwTaHvZQ3xmgfRNZlXZTbhODb6k6g0Ogw8EJIIKWOU9R/12g\r\n4EsRzx5vDmpHTVOm0lSw6bTRycv6Vd7EdAuDsqCwQox5vjSxMTZAtQOIhiq3\r\n5qJzxd/b37Sk//Epzrc5q/P/fSVW+554/Dk75M+jdWAg7G9pr7FfWYnuucB/\r\ntMcjc07PO2Zs8U5HlKrNzeHysk8x7aGv+/DW4CMBEemAo40kitGItxTWTdYJ\r\nwhKOl4ns5juHvCpeE0XaUluqehSitdyYD5R7jnfa+qJUzRHrInJnVmqtVUWN\r\nVpTjENJJi4/3z5PJj2pCk0uF0mKGeF2fUQmmTY6DicPG3ADE0SksphhWBpj5\r\nNnFDL6lOmaYf1WX8qwt5lBJNLyNV2siAl5H/FG1XP6QbPgkfn7qgzZAJp40W\r\nwHZ4wHTNTEjy7Oxsag52WUtlwvo6EgWcjnF2hzRs8c9j66iHzJ+cephTOrrE\r\nNquTC/ELJOV6dOKE4a1oZKGif/ixDXJVRM4SMh9NRVPfoQGGxLhm1qCOSNX1\r\naE6m8P/k9eRhKWo5CH4iZu3mhFJm4ZHpJ5pnF4+fabIzPK4vt7nAX0a4QvvQ\r\nFZ5MBEX1Ex1kZtaUjbgiZOn4K2N7HrKWl4E=\r\n=3Tn7\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.3.1_1650055040969_0.32314778402127464"}, "_hasShrinkwrap": false, "contributors": []}, "5.3.2": {"name": "archiver", "version": "5.3.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.3.7", "jsdoc": "^3.6.4", "mkdirp": "^2.1.5", "mocha": "^9.0.2", "rimraf": "^4.3.1", "stream-bench": "^0.1.2", "tar": "^6.1.13", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "692be046cf17fd185676984fa51c27d4957b6ea3", "_id": "archiver@5.3.2", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-+25nxyyznAXF7Nef3y0EbBeqmGZgeN/BxHX29Rs39djAfaFalmQ89SE6CWyDCHzGL0yt/ycBtNOmGTW0FyGWNw==", "shasum": "99991d5957e53bd0303a392979276ac4ddccf3b0", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-5.3.2.tgz", "fileCount": 10, "unpackedSize": 53627, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCY4aLKmt6w4YT8AbCgw8lIL3EM+zPmAMhpFhzDIgYVyAIgEXCUfq/jjoc8m4otScMrIbSxvsyw+4zl6iRUmd0U5Ig="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_5.3.2_1692243033313_0.9994526414896947"}, "_hasShrinkwrap": false, "contributors": []}, "6.0.0": {"name": "archiver", "version": "6.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^3.0.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.3.7", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "4.4.1", "stream-bench": "0.1.2", "tar": "6.1.15", "yauzl": "2.10.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "ebe5957e29dd85058795499978f31f05844d441a", "_id": "archiver@6.0.0", "_nodeVersion": "16.20.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-EPGa+bYaxaMiCT8DCbEDqFz8IjeBSExrJzyUOJx2FBkFJ/OZzJuso3lMSk901M50gMqXxTQcumlGajOFlXhVhw==", "shasum": "b366c8a21bdb84932b3913d83d0cc576dd4ec112", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-6.0.0.tgz", "fileCount": 9, "unpackedSize": 43159, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG73+uItjF7bq2Q3b1yg/LsyeZsV1IPP3quWNhSWUUJ9AiA4PQPW1iTYJYak+29DUyyx3gf2svm9ercMAA8DUAqT4Q=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_6.0.0_1692325019009_0.15118629780570036"}, "_hasShrinkwrap": false, "contributors": []}, "6.0.1": {"name": "archiver", "version": "6.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^4.0.1", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^5.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.3.8", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "4.4.1", "stream-bench": "0.1.2", "tar": "6.1.15", "yauzl": "2.10.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "5eeef621b133ea64d35feb1417c0645573a747c4", "_id": "archiver@6.0.1", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-CXGy4poOLBKptiZH//VlWdFuUC1RESbdZjGjILwBuZ73P7WkAUN0htfSfBq/7k6FRFlpu7bg4JOkj1vU9G6jcQ==", "shasum": "d56968d4c09df309435adb5a1bbfc370dae48133", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-6.0.1.tgz", "fileCount": 9, "unpackedSize": 43081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbFlqPGMIXgn09icOmNjtsd+JUyc5K4B4YcfT4tF5I0gIhAOd+Ra1x9AnYjrCuVEY+xaZkZGahUmkO6iyfM88Q68vI"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_6.0.1_1693798868825_0.08083500461894655"}, "_hasShrinkwrap": false, "contributors": []}, "6.0.2": {"name": "archiver", "version": "6.0.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^4.0.1", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^5.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "4.4.1", "stream-bench": "0.1.2", "tar": "6.2.0", "yauzl": "2.10.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "4dba2cfd863ce5048bac41b30364bb6651956d28", "_id": "archiver@6.0.2", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-UQ/2nW7NMl1G+1UnrLypQw1VdT9XZg/ECcKPq7l+STzStrSivFIXIp34D8M5zeNGW5NoOupdYCHv6VySCPNNlw==", "shasum": "f45e7598dfe48e834ac8c7a0c37033f826f5a639", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-6.0.2.tgz", "fileCount": 9, "unpackedSize": 43080, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqRmvmTg2j3K1Yd7Of9DgFAU/dewTMp/olli7M1ZpGrAiEA38Z5/3kQN0oCPmHGsB+6vB/yEx5kVVL6DtXM6eHgz9A="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_6.0.2_1709050605756_0.15713310000323055"}, "_hasShrinkwrap": false, "contributors": []}, "7.0.0": {"name": "archiver", "version": "7.0.0", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 14"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^5.0.0", "async": "^3.2.4", "buffer-crc32": "^1.0.0", "readable-stream": "^4.0.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^6.0.0"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5", "stream-bench": "0.1.2", "tar": "6.2.0", "yauzl": "3.1.1"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "6ff0d12e85d012c41c34705f8a8f317d28f5cd8a", "_id": "archiver@7.0.0", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-R9HM9egs8FfktSqUqyjlKmvF4U+CWNqm/2tlROV+lOFg79MLdT67ae1l3hU47pGy8twSXxHoiefMCh43w0BriQ==", "shasum": "06802fffa0c00afe4628119d2752cc7b70e1e6e6", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-7.0.0.tgz", "fileCount": 9, "unpackedSize": 43076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDptyJ1yq+5dJP+0slxmjKqyNPkU9h+7JcZeCU20Z/3aQIgV7mhUaMI8/bD/poLjyyPGU350SV4jLE31aVoxswVe/k="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_7.0.0_1709130808221_0.7507669304743723"}, "_hasShrinkwrap": false, "contributors": []}, "7.0.1": {"name": "archiver", "version": "7.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">= 14"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^5.0.2", "async": "^3.2.4", "buffer-crc32": "^1.0.0", "readable-stream": "^4.0.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^6.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5", "stream-bench": "0.1.2", "tar": "6.2.0", "yauzl": "3.1.2"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "0e0e9ceab65c35e8131701fd2b29ba3c28397385", "_id": "archiver@7.0.1", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-ZcbTaIqJOfCc03QwD468Unz/5Ir8ATtvAHsK+FdXbDIbGfihqh9mrvdcYunQzqn4HrvWWaFyaxJhGZagaJJpPQ==", "shasum": "c9d91c350362040b8927379c7aa69c0655122f61", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver/-/archiver-7.0.1.tgz", "fileCount": 9, "unpackedSize": 43076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBOIKdK4YWrQRKGVU84wMY/VFwBiHqI5hM9aySGP7pqAAiACsGkqLlYADkpVb+L/f9YROO6/ae6jYanx1X49qVdRug=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver_7.0.1_1710041033246_0.12937983759359173"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2024-03-10T03:23:53.825Z", "created": "2012-10-09T06:02:40.305Z", "0.1.0": "2012-10-09T06:02:41.913Z", "0.1.1-alpha": "2012-11-03T20:53:36.407Z", "0.1.1": "2012-11-15T03:36:50.565Z", "0.2.0": "2013-01-02T13:30:53.250Z", "0.2.1": "2013-01-04T19:47:38.317Z", "0.2.2": "2013-01-07T11:13:39.549Z", "0.3.0": "2013-01-27T20:58:21.387Z", "0.4.0": "2013-02-17T21:03:16.207Z", "0.4.1": "2013-03-13T14:48:28.485Z", "0.4.2": "2013-04-09T16:31:03.253Z", "0.4.3": "2013-04-18T00:14:07.406Z", "0.4.4": "2013-06-08T09:34:20.976Z", "0.4.5": "2013-06-25T19:25:09.978Z", "0.4.6": "2013-07-07T22:09:45.955Z", "0.4.7": "2013-08-18T11:33:16.577Z", "0.4.8": "2013-08-18T19:47:17.764Z", "0.4.9": "2013-09-03T02:23:28.526Z", "0.4.10": "2013-09-29T03:09:32.000Z", "0.5.0-alpha": "2014-01-09T23:37:16.930Z", "0.5.0": "2014-01-11T04:56:29.352Z", "0.5.1": "2014-01-15T17:19:45.548Z", "0.5.2": "2014-01-25T01:03:49.086Z", "0.6.0": "2014-02-15T04:33:08.604Z", "0.6.1": "2014-02-16T07:27:26.556Z", "0.7.0": "2014-03-23T03:59:58.960Z", "0.7.1": "2014-03-27T05:56:35.997Z", "0.8.0": "2014-04-01T17:46:05.897Z", "0.8.1": "2014-04-01T19:01:36.766Z", "0.9.0": "2014-04-19T04:38:35.325Z", "0.9.1": "2014-05-04T07:54:24.489Z", "0.10.0-alpha": "2014-05-12T03:22:02.729Z", "0.10.0": "2014-05-29T17:01:01.057Z", "0.10.1": "2014-06-17T22:04:48.124Z", "0.11.0-alpha": "2014-07-13T21:15:46.953Z", "0.11.0": "2014-08-24T11:54:20.533Z", "0.12.0": "2014-10-24T00:07:46.653Z", "0.13.0": "2014-11-28T21:57:05.932Z", "0.13.1": "2015-01-03T22:25:20.984Z", "0.14.0": "2015-01-23T03:54:19.180Z", "0.14.1": "2015-01-23T09:06:26.856Z", "0.14.2": "2015-01-23T09:42:21.327Z", "0.14.3": "2015-02-15T01:39:34.529Z", "0.14.4": "2015-05-20T14:54:45.210Z", "0.15.0-1": "2015-06-05T19:54:15.771Z", "0.15.0": "2015-08-22T21:36:50.972Z", "0.15.1": "2015-09-10T03:31:55.274Z", "0.16.0": "2015-10-17T20:50:50.810Z", "0.17.0": "2015-11-24T22:34:56.226Z", "0.18.0": "2015-11-28T21:05:11.982Z", "0.19.0": "2015-11-28T21:12:27.143Z", "0.20.0": "2015-11-30T19:50:40.555Z", "0.21.0": "2015-12-22T05:11:34.289Z", "1.0.0": "2016-04-06T04:13:33.272Z", "1.0.1": "2016-07-28T01:56:02.231Z", "1.1.0": "2016-08-29T23:48:34.594Z", "1.2.0": "2016-11-02T23:18:47.751Z", "1.3.0": "2016-12-13T15:48:04.575Z", "2.0.0": "2017-07-05T23:29:11.072Z", "2.0.1": "2017-08-26T00:25:27.329Z", "2.0.2": "2017-08-26T00:32:26.270Z", "2.0.3": "2017-08-26T00:51:44.846Z", "2.1.0": "2017-10-12T16:14:07.026Z", "2.1.1": "2018-01-10T17:02:28.791Z", "3.0.0": "2018-08-22T22:36:18.223Z", "3.0.1": "2019-07-19T22:28:15.207Z", "3.0.3": "2019-07-20T00:19:06.045Z", "3.1.0": "2019-08-02T16:13:27.327Z", "3.1.1": "2019-08-02T23:09:08.675Z", "4.0.0": "2020-04-14T20:52:42.027Z", "4.0.1": "2020-04-14T21:23:13.881Z", "4.0.2": "2020-07-11T15:39:08.913Z", "5.0.0": "2020-07-23T01:53:31.759Z", "5.0.1": "2020-09-11T00:11:01.191Z", "5.0.2": "2020-09-11T17:13:26.344Z", "5.1.0": "2020-11-20T05:20:55.728Z", "5.2.0": "2021-01-07T04:04:41.365Z", "5.3.0": "2021-03-07T21:34:43.521Z", "5.3.1": "2022-04-15T20:37:21.151Z", "5.3.2": "2023-08-17T03:30:33.559Z", "6.0.0": "2023-08-18T02:16:59.177Z", "6.0.1": "2023-09-04T03:41:09.012Z", "6.0.2": "2024-02-27T16:16:45.994Z", "7.0.0": "2024-02-28T14:33:28.446Z", "7.0.1": "2024-03-10T03:23:53.397Z"}, "users": {}, "dist-tags": {"latest": "7.0.1", "canary": "0.15.0-1"}, "_rev": "3584-a1b5043a9fd3c30f", "_id": "archiver", "readme": "# Archiver\n\nA streaming interface for archive generation\n\nVisit the [API documentation](https://www.archiverjs.com/) for a list of all methods available.\n\n## Install\n\n```bash\nnpm install archiver --save\n```\n\n## Quick Start\n\n```js\n// require modules\nconst fs = require('fs');\nconst archiver = require('archiver');\n\n// create a file to stream archive data to.\nconst output = fs.createWriteStream(__dirname + '/example.zip');\nconst archive = archiver('zip', {\n  zlib: { level: 9 } // Sets the compression level.\n});\n\n// listen for all archive data to be written\n// 'close' event is fired only when a file descriptor is involved\noutput.on('close', function() {\n  console.log(archive.pointer() + ' total bytes');\n  console.log('archiver has been finalized and the output file descriptor has closed.');\n});\n\n// This event is fired when the data source is drained no matter what was the data source.\n// It is not part of this library but rather from the NodeJS Stream API.\n// @see: https://nodejs.org/api/stream.html#stream_event_end\noutput.on('end', function() {\n  console.log('Data has been drained');\n});\n\n// good practice to catch warnings (ie stat failures and other non-blocking errors)\narchive.on('warning', function(err) {\n  if (err.code === 'ENOENT') {\n    // log warning\n  } else {\n    // throw error\n    throw err;\n  }\n});\n\n// good practice to catch this error explicitly\narchive.on('error', function(err) {\n  throw err;\n});\n\n// pipe archive data to the file\narchive.pipe(output);\n\n// append a file from stream\nconst file1 = __dirname + '/file1.txt';\narchive.append(fs.createReadStream(file1), { name: 'file1.txt' });\n\n// append a file from string\narchive.append('string cheese!', { name: 'file2.txt' });\n\n// append a file from buffer\nconst buffer3 = Buffer.from('buff it!');\narchive.append(buffer3, { name: 'file3.txt' });\n\n// append a file\narchive.file('file1.txt', { name: 'file4.txt' });\n\n// append files from a sub-directory and naming it `new-subdir` within the archive\narchive.directory('subdir/', 'new-subdir');\n\n// append files from a sub-directory, putting its contents at the root of archive\narchive.directory('subdir/', false);\n\n// append files from a glob pattern\narchive.glob('file*.txt', {cwd:__dirname});\n\n// finalize the archive (ie we are done appending files but streams have to finish yet)\n// 'close', 'end' or 'finish' may be fired right after calling this method so register to them beforehand\narchive.finalize();\n```\n\n## Formats\n\nArchiver ships with out of the box support for TAR and ZIP archives.\n\nYou can register additional formats with `registerFormat`.\n\nYou can check if format already exists before to register a new one with `isRegisteredFormat`.", "_attachments": {}}