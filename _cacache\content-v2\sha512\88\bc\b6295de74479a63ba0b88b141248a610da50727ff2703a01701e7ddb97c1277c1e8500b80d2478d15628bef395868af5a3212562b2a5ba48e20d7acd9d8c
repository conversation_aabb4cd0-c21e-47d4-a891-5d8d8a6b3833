{"name": "log-symbols", "versions": {"1.0.0": {"name": "log-symbols", "version": "1.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "150dad5afd4800a74de564200ad7a734e0e8660f", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-1.0.0.tgz", "integrity": "sha512-jlvvs+hYwUqlo2iyqWq/i5VPcH1HIFLFRdIhsDfBgprK0cvdsJblIK7wjJ70F4E7slSsJw9EgSBmK6HYVjnP9Q==", "signatures": [{"sig": "MEQCICTEB7ZMkS9+rpjUbYxidPfym9/nnxWVmg/FV2SsvcO7AiApR1Lp+tcIuFPoOkrlFo7196DyXf8iSCehOx+wgGHVjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "150dad5afd4800a74de564200ad7a734e0e8660f", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/log-symbols", "type": "git"}, "_npmVersion": "1.4.9", "description": "Colored symbols for various log levels. Example: ✔︎ success", "directories": {}, "dependencies": {"chalk": "^0.5.1"}, "devDependencies": {"ava": "0.0.3"}, "contributors": []}, "1.0.1": {"name": "log-symbols", "version": "1.0.1", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "38e09b991a389585ee77186dc94a0333ab5ed727", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-1.0.1.tgz", "integrity": "sha512-21YznDMtl/STuH+mKdtbL4Di3RRAyK+W7qffBsTOpRYfPADN/Dv8cxhtNATQiZmdT6b/+TCmNt/NBzXQSo7TZw==", "signatures": [{"sig": "MEUCIET+hruTs9UR4Sy9iyZkNHGnKpwrQVWaMQ86MimoGPi1AiEA+ovx42mCC0JWazqy9wjVulXSmJ7Vta/mlk6j+B6AykU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "38e09b991a389585ee77186dc94a0333ab5ed727", "engines": {"node": ">=0.10.0"}, "gitHead": "4df95a6ac075d1e847a387527e726ea88c2e53be", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/log-symbols", "type": "git"}, "_npmVersion": "2.0.0", "description": "Colored symbols for various log levels. Example: ✔︎ success", "directories": {}, "dependencies": {"chalk": "^0.5.1"}, "devDependencies": {"ava": "0.0.3"}, "contributors": []}, "1.0.2": {"name": "log-symbols", "version": "1.0.2", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "376ff7b58ea3086a0f09facc74617eca501e1a18", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-1.0.2.tgz", "integrity": "sha512-mmPrW0Fh2fxOzdBbFv4g1m6pR72haFLPJ2G5SJEELf1y+iaQrDG6cWCPjy54RHYbZAt7X+ls690Kw62AdWXBzQ==", "signatures": [{"sig": "MEUCIEljuMJi4NUpR0gZcU67fgUVq1QTOhuZh4GbwDE+kebAAiEAt+QpV5HV/C9srAIyXzLRjBqX26iIt8uGWbwCDpwSqls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "376ff7b58ea3086a0f09facc74617eca501e1a18", "engines": {"node": ">=0.10.0"}, "gitHead": "ffcc995a1d4efc91ceed3e536fecccc8dc517c76", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/log-symbols", "type": "git"}, "_npmVersion": "2.5.1", "description": "Colored symbols for various log levels. Example: ✔︎ success", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"chalk": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "contributors": []}, "2.0.0": {"name": "log-symbols", "version": "2.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "595e63be4d5c8cbf294a9e09e0d5629f5913fc0c", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-2.0.0.tgz", "integrity": "sha512-ValCSal2pRxRbet7O69a/1g5fZ2MLxf1YXIslNrdJF42ofY9zVf6MTqTwfhG+2x168xrbZATCgFQfXAwdNHv+w==", "signatures": [{"sig": "MEYCIQCOO5wHtSLTr/+0M1TMpuDlM/3p9iYnBLPtPCgBtFmy6gIhAJAr4QiqQvi3EqL6aYI4aIUKtO/wxeUgBB+HtfD6kLr+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "ff541baee06e7438b566ce562e2d70243488a561", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Colored symbols for various log levels. Example: ✔︎ Success", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"chalk": "^2.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols-2.0.0.tgz_1500484153469_0.3959882273338735", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "log-symbols", "version": "2.1.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "f35fa60e278832b538dc4dddcbb478a45d3e3be6", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-2.1.0.tgz", "integrity": "sha512-zLeLrzMA1A2vRF1e/0Mo+LNINzi6jzBylHj5WqvQ/WK/5WCZt8si9SyN4p9llr/HRYvVR1AoXHRHl4WTHyQAzQ==", "signatures": [{"sig": "MEUCIQDKlondo+WObjgXTnWHOTxKXXgaoDnVonbmZivW9quMxwIgZqEJTbHJx9wlHGMXa8G6MzoL1xpJlHgkV/+krU6J33E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "721e006266ea7c55565322c99e2bcb35b04cdc4c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Colored symbols for various log levels. Example: ✔︎ Success", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"chalk": "^2.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols-2.1.0.tgz_1506158437520_0.19431678322143853", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.2.0": {"name": "log-symbols", "version": "2.2.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@2.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-2.2.0.tgz", "integrity": "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==", "signatures": [{"sig": "MEUCIQCiZaulHYron5jED0or2wndt+E+lIhZOzyd5zEarAqM+wIgeMC8A+6GaPIUVzb8wSVsL2yrrJWCsoUsgegiQ2ss7Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "029dbb35f81c0e8471a4fa1fcf014af416d3230d", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Colored symbols for various log levels. Example: ✔︎ Success", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"chalk": "^2.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols-2.2.0.tgz_1516578275647_0.2500845284666866", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "log-symbols", "version": "3.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "f3a08516a5dea893336a7dee14d18a1cfdab77c4", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-dSkNGuI7iG3mfvDzUuYZyvk5dD9ocYCYzNU6CYDE6+Xqd+gwme6Z00NS3dUh8mq/73HaEtT7m6W+yUPtU6BZnQ==", "signatures": [{"sig": "MEYCIQDJZdhe4ZMYweDl2ZdJUzMII1niiPzOQGcWVT0uFVxHHwIhAK/y4uVGCA32SbT+sJvjZmrAZVc7LCz/WF7cti7FUB2j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct0pxCRA9TVsSAnZWagAAX/wQAI8fsLd3r9Yn6/mSwYFW\n7xAyAf+B/MuBFvv8OwRyi8ofDET0VdFtd1XktCVRnwv3O1FJERCAe2cJyewt\nOerlUnwz6ggK7rXfFeyA8bbpQB4PANYwuV2YYpusd9AIhH0FAiN4lXrOj2pq\n5mTAKSYVuIuJMpQtUj+FwtOnPY3YzWvMq/wP5ad2zVvdAmNFfSTljXdXRR7H\nSbloC7xDVz7qukQnMvyopRSaAHYe9DmQW8McIzEbKSFkclxtHpEtRqfGYTIP\n2KBu7aGippT/I9c51FGErRp8V3nVABz+3wVCpwNkSmM5X7UjvtlMGdVFs1VD\nIhAIKf6yzcinFGUPOSfllZNqnVEImSx7/EUeY8DZsBMZKTuEdtyVZUbOhOHE\nuwwYcnSXKOGrwkfR+wv4J7rewg0e3M8JdVTtWVs+H21SVTdJXNw2DPmY4i4i\nwUAPAPyHa05Z4GdY+6PQYIfLlVZ9GyeBuwG1oFknrz3ZYR0t7b1V7xjwJSmq\nse443HTZn+8O2xFcWnheNww9wFdkecZxRNWcaVHpfhWOXDLl/lXUzdswU0LZ\nRC+zlJNm25wo3bBC3NSq7TaHcMQ5PGTJBEPW3RhLUkaxeeeonCp/e5EZiFEg\n1zvJqtVwmFtmjyqEujW9QTzEghfoTFXy4x9V/1Dn7u3NxBlQLdoofvyCpHw9\n9EU0\r\n=Bfli\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "1f4ac6ef31cb4c7c4c4c67b7d6f9db712d4c0186", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"chalk": "^2.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "strip-ansi": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_3.0.0_1555516016694_0.9514962763710724", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "log-symbols", "version": "4.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "69b3cc46d20f448eccdb75ea1fa733d9e821c920", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-FN8JBzLx6CzeMrB0tg6pqlGU1wCrXW+ZXGH481kfsBqer0hToTIiHdjH4Mq8xJUbvATujKCvaREGWpGUionraA==", "signatures": [{"sig": "MEUCIBsZHbiEAjA0m92+jgRmF+xN1y0xHmYqFWVJBG+rX12EAiEAl6foHlQA8pIT7MZ/qdVo2SymAwnsLawCT65JUIF9PSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepWbICRA9TVsSAnZWagAA/8AQAJ+dPzdYSeDLvW+Jeics\niJVXX7+k+a+IAyoNdpjsB4VztfL46DPQmg5gn44O502KzRkq61B7ojOuDnUf\n6IVkpMYdi5hiXtWVV/L69XyzvtqkCheP15agNF3KFpp8vw2vbI8gANKXADpg\naKjlS58tLIcGAmngQr121xvctTG5EhViHUhf6tVsn+NPBS7RLwKIaTH5tm1u\nJR/R/Q8U8IpN5jZuEgsB4lU4WwHpB1IVmAP0oXH5Ei2TbMOs7Lqc8vstsdpU\n9vkCFdrLpuQA0HqySwCBU0RM/5HgCM7yTDxsdEivAcw/7Zbscl9vfxdRE0ab\n5BpxiHYCvuvUG7vL4jfutcC3VlLm+im9x5oNP2KY6NZYo+3HQ1wpa/g2fsT6\nVF/o7TnnlXEYVlY/CwXfNKhQmfyKgGGg1OyR8PY4I+tYlfVu/AAiJLL5pXtW\nrnOKfS1WAF7kJ+kJVystEs28QE5i6KssF3/YjaQtKTNkJIWD9ay1ZezSygm2\n5HL387o8b5F3XJRzRlspD2PzqnFw7g+ExPoCzXa1/LZ98odrJzzKxTCnzPPb\nAHU0gqnj44D302FD2JO0MYwm9r1PVWYIoaSxIO04Iomcyw+hIYq3Rgwi21mo\nUB12NRAXXQ36cpqJ11Kl+zejxWzGAM3vozKNufBotDHDeZ/mB2cXcM7Bb6CT\n5Oja\r\n=+uXu\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=10"}, "gitHead": "76fd8f2e7b4a4e90d58b0c807388cd034d607515", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"chalk": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.30.0", "ava": "^3.7.1", "tsd": "^0.11.0", "strip-ansi": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_4.0.0_1587898056188_0.12109328307453571", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.0": {"name": "log-symbols", "version": "4.1.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "3fbdbb95b4683ac9fc785111e792e558d4abd503", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "signatures": [{"sig": "MEYCIQDoLbHmRSIHjvVXblsL4kgjkTP9YGgqpF7PUoW4sHgkmgIhALasMoRpk6x0AtaJWutKQmvlEdBiMTMKWP0a+EufOEJj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUdoBCRA9TVsSAnZWagAApYgQAJ9S903l8AGNSRZRQpD6\n9FN4RrrTIElQHFf7pIIbIpUntp9C3k2YQE5nn2ay5Mxvol2conoxAKri3N1f\nFJupvoJ8RLxiGzIUVVdFYhkdrNhEjP2c2zNxLiDOt1CaAG9CCy7BnHDNPWbN\n5pMdNWXb0NTn0CgyQFzDq7TU9N++NkPxp0yfTpVPZwaC2oc/QLeon4TnhFYF\nTwo2DppN/I+2VysUQbY9jqwrb1cutuIlVU5VOnatWMysSlXouXTWElxrOe0g\n6gsmyKDwaUZJyNcXhPAUytsfBVKH44V9uwZchXGHhGzCb325LctVvRtEFJb8\nkKVV6jpmlCcGDvyHJNTZDnuZYZNBXInnXCgHKu41oH2z+q6P4oXlr8S9xdTD\n8kQJUAlUrvrMmTvUzOS/83pQ7qm7k399cMZqMaQVLqIJDfNEeSH7X8fBVUhk\nMCx0U7UPmdzAscAZeJYJI76bWUwZY+fo5WmCSMK1QEDOIOHEOkZ0oj3FrjdO\nLySx7RHUkDzzo0L00hPSeVOlT/Uz7gekiI1dYCYdQAzTG6WXz8LpJlUQvBDH\nMwwivJ8sesuHH4LlDd/r6d5AINET3dC9vMB5NAP6J0H23fDXvXdFX96liPDR\n0Z/gJWH6fWfeE+smnMzyP8XXMrKntH+M4O8CSWYk7AgxPGYXKHYA7eIrJ4HN\nwVEP\r\n=q12b\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b507bab98430d31b5830941088ef0bb85c2dc420", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^2.4.0", "tsd": "^0.14.0", "strip-ansi": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_4.1.0_1615976960813_0.440412928367961", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "log-symbols", "version": "5.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "7720d3c6a56c365e1f658916069ba18d941092ca", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-zBsSKauX7sM0kcqrf8VpMRPqcWzU6a/Wi7iEl0QlVSCiIZ4CctaLdfVdiZUn6q2/nenyt392qJqpw9FhNAwqxQ==", "signatures": [{"sig": "MEYCIQCTTyfTTnBLdIA7GxkXtVLGkEm9LYN4LkSDrkqtPQaQcwIhAPz0S3Hn73M3RJKZYNkI0Vkh5j8iEk2JkRS/BOYAiHgU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge7/gCRA9TVsSAnZWagAAhSMP/innDyFsGcJibXE7COlM\ni5qeId4ojuRKKsUq5/m0vH9HHmYaJV0WWSpl2F/emuKaZPFcycvOUjq/lBrp\n23Db7vom1xUCdDrGEEOMlKwOx0AxWPy+cfomEs0c0AZDaZ0aUUHknxbD9GBq\npA3z6bPuECrZQlktT+/BcLGIsDrHCHOA64px1k/OS+Js3d4AdKu5Frqi6TIE\nG4BFi0ycwnEBaKk5Hjeae0PyvVXLP0JWZxz5nqvFaxbCJSekWo9Xad2Y5yAp\nzCd8GBa08hF6Xcc/kLb73RbUj4Teuo3gdjezZhqYwL93oZ49x/EMPTq/ebyL\nsmgsO7o9wqyQiS7fdB7CH/54+Q4K6kcL3DLaw4D1MOpqV4XImHHWDwEu42VW\nnlJMwEhF5ZluBjs03eOyHQvnQPmajUjMu6KiSNiwLjJPSzzXQEAHtCYxxoby\neUi9LAB4+nqphl3HluONaAko8xj6tSwli0nHGgrNw8QV8GzZfusSk9VGjcwI\nf6vG09G1gg4vzHQQSnnDEi4BNZOhJ6KvbC6MpBS6SegBCVihnYI+SJlsz/lO\nvl712U/avnatytDAbPU1yXhIBoU6UTdRavLyIcmbuLhkd11bdB7igkyN13vf\ncoJFnFj2bQbsPDuBbZTVDEkeva6kurwW1DV2nt1tL/d3A7Uq0ku3jt1wPfJz\nlfLy\r\n=xdQr\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6aea9853e1fb85a437f868d0ef400ce122c08c1d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0", "strip-ansi": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_5.0.0_1618722784063_0.38919868847090133", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.1.0": {"name": "log-symbols", "version": "5.1.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "a20e3b9a5f53fac6aeb8e2bb22c07cf2c8f16d93", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-5.1.0.tgz", "fileCount": 6, "integrity": "sha512-l0x2DvrW294C9uDCoQe1VSU4gf529FkSZ6leBl4TiqZH/e+0R7hSfHQBNut2mNygDgHwvYHfFLn6Oxb3VWj2rA==", "signatures": [{"sig": "MEYCIQDDydyaUs7GOf+k6ZvKrBQ7c8EN8v0+cApRbAU5mdHkCgIhAPFmyiQzj+V6+oN47zpw/b9kaycLcqRVE9djlmrwAa46", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhq5qjCRA9TVsSAnZWagAAaQ8P/1ALpjqUoIRO8ENWy+BX\nK2K+RFbWJL+dgaH2vC/Y057tCSYSwqY60j7jZpCkTfokPec0YgHaMZFtNayv\nLoRqRer4Yujai4u/2ScQUsgNMQGCLbJtfraUkoNGoDW4G4048SWZ2iYylNoY\n1WUZKniPyOIGKktFTi3bwfOEjnVzpAByEZvvQEBcne8zuhkZl1JH3dlLz3ck\n+kovZdSzagXP2u571lPud6JHUAv6n6ZT3VYYMM5jBGFE3z9moPH8V6iWUeb/\nWAlHQj51fk4Q1MYMOsbaCFVTY/0g9mDBaAV2phtaWZ4EdszN9dfhzCrYi1Vx\nmwhlafAdVOQQ2kAmjto4V/kM1UGaeyXhVmEhv3n4yHk5mCZkcA8QA+2f6QRe\n7MFnQYVR2am8PgFbqlsvVLOz/IlDv1s2a0tGFHaXK/Oy48Ads+HmDaX7Qet6\no+r1jEfaOB0WUgQlWjAhCCZKBLvG4q3oM1K/gZ3slaxZF/2PVxQujpL/2rtF\nOwMPHYv22LYq6IFQkioBjWdZ51z59HocfpRiWZoaAcmWBxzquoJwyQIaM/na\n/ffwyb/tx/+N0yl37/M8wvriaIhUttJ+R6dLGRKHxC7Zf3+6u9M6SsrvWK8s\nhsQDWJYMwJCqxTjEQDerrML2h91yalEkuyDudLD3qRbtciBaQBjxeIIbeNod\no8KW\r\n=lp1E\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "9fef1176a7fcd7796bc2804c5b0694233a57995b", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"chalk": "^5.0.0", "is-unicode-supported": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "ava": "^3.15.0", "tsd": "^0.19.0", "strip-ansi": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_5.1.0_1638636195563_0.9832834993025006", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "log-symbols", "version": "6.0.0", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "log-symbols@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "dist": {"shasum": "bb95e5f05322651cac30c0feb6404f9f2a8a9439", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-i24m8rpwhmPIS4zscNzK6MSEhk0DUWa/8iYQWxhffV8jkI4Phvs3F+quL5xvS0gdQR0FyTCMMH33Y78dDTzzIw==", "signatures": [{"sig": "MEUCIQCtKl4VI6tD3i81o0ptvZs5xKNxlEj+FvaTvyJ4Wc8N8gIgLlGsl5uat9ORXJBoH6uvkD3JpIvm7exvb27UCx3WOGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4246}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"node": "./index.js", "types": "./index.d.ts", "default": "./browser.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "249ffb48c3df3a7b6b9fbfa75916b0bf6848586f", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/log-symbols.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^5.3.0", "is-unicode-supported": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/log-symbols_6.0.0_1698003490201_0.11648689705361259", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "log-symbols", "version": "7.0.0", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/log-symbols.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "dependencies": {"is-unicode-supported": "^2.0.0", "yoctocolors": "^2.1.1"}, "devDependencies": {"ava": "^6.1.3", "strip-ansi": "^7.1.0", "tsd": "^0.31.1", "xo": "^0.59.3"}, "_id": "log-symbols@7.0.0", "gitHead": "a2389bbc9c4fd3867711a9bc10d92f9246e3e3bc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "homepage": "https://github.com/sindresorhus/log-symbols#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-zrc91EDk2M+2AXo/9BTvK91pqb7qrPg2nX/Hy+u8a5qQlbaOflCKO+6SqgZ+M+xUFxGdKTgwnGiL96b1W3ikRA==", "shasum": "953999bb9cec27a09049c8f45e1154ec81163061", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-7.0.0.tgz", "fileCount": 8, "unpackedSize": 4399, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA4Hu+GGuFOyC5QU4UODtzPk+XUTc9sHWz3H7zzcpBhCAiEAj8VkCKPDUP01XaXd2RZy8fCr+1INd2LqYgAG3v7hRJQ="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/log-symbols_7.0.0_1723894414790_0.36877424351913635"}, "_hasShrinkwrap": false, "contributors": []}, "7.0.1": {"name": "log-symbols", "version": "7.0.1", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/log-symbols.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "dependencies": {"is-unicode-supported": "^2.0.0", "yoctocolors": "^2.1.1"}, "devDependencies": {"ava": "^6.1.3", "strip-ansi": "^7.1.0", "tsd": "^0.31.1", "xo": "^0.59.3"}, "_id": "log-symbols@7.0.1", "gitHead": "aab590f8cb4b895b39f3d6d97d458daa6a8fc9ae", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "homepage": "https://github.com/sindresorhus/log-symbols#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ja1E3yCr9i/0hmBVaM0bfwDjnGy8I/s6PP4DFp+yP+a+mrHO4Rm7DtmnqROTUkHIkqffC84YY7AeqX6oFk0WFg==", "shasum": "f52e68037d96f589fc572ff2193dc424d48c195b", "tarball": "https://mirrors.cloud.tencent.com/npm/log-symbols/-/log-symbols-7.0.1.tgz", "fileCount": 8, "unpackedSize": 4396, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD0hIVhmWMnrTm67rJIj9BTkoVQJKsWp7chV2ytxHHW1QIhAPlqvPOCmmRwWE8HN9kAKhliKfNunNT1NmqbUMJuvIle"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/log-symbols_7.0.1_1747835258890_0.21469152491919563"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-07-10T00:55:11.397Z", "modified": "2025-05-21T13:47:39.267Z", "1.0.0": "2014-07-10T00:55:11.397Z", "1.0.1": "2014-09-24T09:33:36.184Z", "1.0.2": "2015-02-24T08:57:54.293Z", "2.0.0": "2017-07-19T17:09:14.271Z", "2.1.0": "2017-09-23T09:20:37.914Z", "2.2.0": "2018-01-21T23:44:35.752Z", "3.0.0": "2019-04-17T15:46:56.791Z", "4.0.0": "2020-04-26T10:47:36.370Z", "4.1.0": "2021-03-17T10:29:21.279Z", "5.0.0": "2021-04-18T05:13:04.243Z", "5.1.0": "2021-12-04T16:43:15.702Z", "6.0.0": "2023-10-22T19:38:10.487Z", "7.0.0": "2024-08-17T11:33:34.950Z", "7.0.1": "2025-05-21T13:47:39.089Z"}, "users": {}, "dist-tags": {"latest": "7.0.1"}, "_rev": "19793-2be254715a548d2a", "_id": "log-symbols", "readme": "# log-symbols\n\n<img src=\"screenshot.png\" width=\"226\" height=\"192\" align=\"right\">\n\n> Colored symbols for various log levels\n\nIncludes fallbacks for Windows CMD which only supports a [limited character set](https://en.wikipedia.org/wiki/Code_page_437).\n\n## Install\n\n```sh\nnpm install log-symbols\n```\n\n## Usage\n\n```js\nimport logSymbols from 'log-symbols';\n\nconsole.log(logSymbols.success, 'Finished successfully!');\n// Terminals with Unicode support:     ✔ Finished successfully!\n// Terminals without Unicode support:  √ Finished successfully!\n```\n\n## API\n\n### logSymbols\n\n#### info\n#### success\n#### warning\n#### error\n\n## Related\n\n- [figures](https://github.com/sindresorhus/figures) - Unicode symbols with Windows CMD fallbacks\n- [py-log-symbols](https://github.com/ManrajGrover/py-log-symbols) - Python port\n- [log-symbols](https://github.com/palash25/log-symbols) - Ruby port\n- [guumaster/logsymbols](https://github.com/guumaster/logsymbols) - Golang port", "_attachments": {}}