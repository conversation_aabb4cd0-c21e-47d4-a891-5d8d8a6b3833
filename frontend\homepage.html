<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .home-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 90%;
        }
        
        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .greeting {
            margin-bottom: 40px;
        }
        
        .greeting h1 {
            font-size: 28px;
            color: #1e293b;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .greeting p {
            font-size: 16px;
            color: #64748b;
            line-height: 1.5;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        .input-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .task-input {
            width: 100%;
            min-height: 80px;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .task-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .task-input::placeholder {
            color: #94a3b8;
        }
        
        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .send-btn:active {
            transform: translateY(0);
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .examples {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #f1f5f9;
        }
        
        .examples h3 {
            font-size: 18px;
            color: #374151;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .example-list {
            list-style: none;
            text-align: left;
        }
        
        .example-list li {
            padding: 8px 0;
            color: #64748b;
            font-size: 14px;
            position: relative;
            padding-left: 20px;
        }
        
        .example-list li::before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        @media (max-width: 480px) {
            .home-container {
                padding: 40px 20px;
            }
            
            .avatar {
                width: 100px;
                height: 100px;
                font-size: 50px;
            }
            
            .greeting h1 {
                font-size: 24px;
            }
            
            .greeting p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="home-container">
        <div class="avatar">🤖</div>
        
        <div class="greeting">
            <h1>你好！</h1>
            <p>我是AI助手，我能为你做什么？</p>
        </div>
        
        <div class="input-section">
            <div class="input-container">
                <textarea 
                    id="task-input" 
                    class="task-input" 
                    placeholder="请描述您的需求，例如：帮我写一份小红书文案、生成工作周报、制定日程安排..."
                ></textarea>
            </div>
            <button id="send-btn" class="send-btn">发送</button>
        </div>
        
        <div class="examples">
            <h3>我可以帮您：</h3>
            <ul class="example-list">
                <li>写小红书文案和内容创作</li>
                <li>生成工作日报、周报、月报</li>
                <li>制定日程安排和时间管理</li>
                <li>创作作文和公文写作</li>
                <li>新媒体营销文案策划</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const taskInput = document.getElementById('task-input');
            const sendBtn = document.getElementById('send-btn');
            
            // 自动聚焦输入框
            taskInput.focus();
            
            // 发送按钮点击事件
            sendBtn.addEventListener('click', function() {
                const task = taskInput.value.trim();
                if (task && !sendBtn.disabled) {
                    sendTask(task);
                }
            });
            
            // Enter键发送
            taskInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!sendBtn.disabled) {
                        sendBtn.click();
                    }
                }
            });
            
            // 输入框变化时启用/禁用按钮
            taskInput.addEventListener('input', function() {
                sendBtn.disabled = !this.value.trim();
            });
        });
        
        async function sendTask(task) {
            const sendBtn = document.getElementById('send-btn');
            
            try {
                // 直接跳转到对话页面并传递任务
                window.location.href = `chat.html?task=${encodeURIComponent(task)}`;
                
            } catch (error) {
                console.error('跳转失败:', error);
                alert('跳转失败，请重试');
            }
        }
    </script>
</body>
</html> 