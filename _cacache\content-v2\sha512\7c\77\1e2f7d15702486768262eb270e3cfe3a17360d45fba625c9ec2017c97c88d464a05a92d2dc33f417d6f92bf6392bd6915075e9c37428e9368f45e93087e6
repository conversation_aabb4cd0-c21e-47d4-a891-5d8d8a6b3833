{"name": "babel-template", "dist-tags": {"latest": "6.26.0", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-template", "version": "6.0.2", "description": "", "dist": {"shasum": "d5a9bef2b33616dd2f08452287d1cab64b1373d0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.0.2.tgz", "integrity": "sha512-ePt81ZUUCQehHNCGJBZSSHkcJFsI+46M94jOvRYURP/JPkB/eGe2Vo2N8G8+hG3btqcK73tEawSKbBrxoqzTxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH1O8f9bRrvT+/3/T691bAEguDumomkzTos36Rcr2rLgAiEAm1IAHdSqGSA6ivTRju7kIk3oDEmrfL0UQL3NbT7QKbk="}]}, "directories": {}, "dependencies": {"babylon": "^6.0.2", "babel-traverse": "^6.0.2", "babel-types": "^6.0.2", "babel-runtime": "^6.0.2", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.0.12": {"name": "babel-template", "version": "6.0.12", "description": "Generate an AST from a string template.", "dist": {"shasum": "e8358ddd10ac4552a488c86758aa9a7f7d0fa5f3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.0.12.tgz", "integrity": "sha512-h3dah5KHtoQjQnmB9yxhWI3/U6S/HaF9hZtfnyb2qFOfIgPuv5lnvsUW+/BUpLe1QNe4+TTxvHxCmScvL3FW9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6BkTPL7PlCE+2+gXfM8zB6/0jmg1QcRK6T0naNmWEHAiEAx211IbYXxf6CnnTfXDCkvdc78Ukgb5mfJWP3kddb6Og="}]}, "directories": {}, "dependencies": {"babylon": "^6.0.2", "babel-traverse": "^6.0.2", "babel-types": "^6.0.12", "babel-runtime": "^6.0.12", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-template", "version": "6.0.14", "description": "Generate an AST from a string template.", "dist": {"shasum": "2f28e72fa19ee37ffefd2489c6603060445f1969", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.0.14.tgz", "integrity": "sha512-ydO9voVMyEK/hibh8AjmNHCnTPMQHJCIqv4Wkg3KlQYGOR0Q2IDdixHGx8ktb7/aV6MnEtXHWE+IR9RY77dyBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDPTta85vyCqfINzOLaEK2WDaDKyAr3jFM6XgxdAGmEmAiEA36E692chL0Jd1Z/gkVZOqsPTWpFUxFpYSJJ43RejuJE="}]}, "directories": {}, "dependencies": {"babylon": "^6.0.14", "babel-traverse": "^6.0.14", "babel-types": "^6.0.14", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-template", "version": "6.0.15", "description": "Generate an AST from a string template.", "dist": {"shasum": "d3794fe3d894a7ea46815f474e8ca518ac3eca61", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.0.15.tgz", "integrity": "sha512-titJxRJYlOott+Ny2fg31cMxRa53XoLSttDfFJgndBpo92tesd4a+yUmvLpDCh4ok+1CI/87KX/Za2v4+TkWwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6g1Us6PtHSS2/tnK2dYAbCoTwYJQWIMw3ChF7CpItQgIgQbdjxyVukqTteQyJREStlRaHar3viAZ5zNHNhmKbft0="}]}, "directories": {}, "dependencies": {"babylon": "^6.0.14", "babel-traverse": "^6.0.14", "babel-types": "^6.0.15", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.0.16": {"name": "babel-template", "version": "6.0.16", "description": "Generate an AST from a string template.", "dist": {"shasum": "f0456f7bba9dd8316a6a227cc305e002a5341bb2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.0.16.tgz", "integrity": "sha512-2Ywh6uYr+GLGYs5HZNndVrfctX2SJQLkRExnxEsOfFpgQMkrr568fEDZqC6U3xFo9wp4oW1m/kBvYTEHxpmmEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICg0ioZnNMhVWV1anFkYNoHU4eI+N+h7IymO4T3xbgG+AiAuVPurFcLkwpzlWgo6eoJFtTKI/2QtMy8sfwuuA/cU4g=="}]}, "directories": {}, "dependencies": {"babylon": "^6.0.14", "babel-traverse": "^6.0.16", "babel-types": "^6.0.15", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-template", "version": "6.1.17", "description": "Generate an AST from a string template.", "dist": {"shasum": "e43d9757cc70c73954c4596459b49c2d585dd90d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.1.17.tgz", "integrity": "sha512-bTXPd/8UzuGvXwzgpwLnL1jzOUYQIdR+QKNhvXWPgPRCCU4E87oOE6rbGV1M+RqkKjI8HB4nxPquxV2aP+XJ1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9mM/SOHc0+Ae+Evl9bHgtWf0gw5uL3zRPzD27JnSBDQIgUEILutvP9jGn0c361WbHgutJ8WmQa+fDg2D9N8qdfv0="}]}, "directories": {}, "dependencies": {"babylon": "^6.1.17", "babel-traverse": "^6.1.17", "babel-types": "^6.1.17", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-template", "version": "6.1.18", "description": "Generate an AST from a string template.", "dist": {"shasum": "dc1d675b7524ef1d95042a6bd406393984f648a4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.1.18.tgz", "integrity": "sha512-Lt3Xg4KcUmKbzzkOJNqET4fkL0NweTp2Qun/Ps4ZywpDBq4lscHl8veUg/ypwoRTBa63IJTp7MmWmMY85ePqHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICS2LQG/o9U2FdqYjhFmTaLYTUARHikUosSpqh0EgInBAiAxt1juQU+9a3eSfMbHsyqgFEk8G1GMFi3funltoVMhLg=="}]}, "directories": {}, "dependencies": {"babylon": "^6.1.18", "babel-traverse": "^6.1.18", "babel-types": "^6.1.18", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-template", "version": "6.2.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "4008c1b0a41f48b8f6e8383c58f579fb91f911d2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.2.0.tgz", "integrity": "sha512-hcf3DsvVmlKo8HOJR6USeH2/UMH/ER7izNGv1eQGsvSS29/fNIENM5OzwOzonjUr7XukmIRr25vyQgYsOf8qXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMOxQTgq/thuVXSmMkGZrWBCm4vdLD2T1r/amIVY9bnAiEApp37chbrDMzRs96KNvCtERQAaoW3kIlXHFY5XPirxFc="}]}, "directories": {}, "dependencies": {"babylon": "^6.2.0", "babel-traverse": "^6.2.0", "babel-types": "^6.2.0", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-template", "version": "6.2.4", "description": "Generate an AST from a string template.", "dist": {"shasum": "1e1842a787579a16e13d54c76a56a5d4af91addf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.2.4.tgz", "integrity": "sha512-kV3DSiBZY/JM7vTUDruebbl/mYwEEAMjqtaHEc3VaMakCyM83xS0b0fgSdDXeKPqeGnXHZSoO7uuKqOQU3Dibg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo8opynMzND7unm8QPPIyOAB4F4K0wZ1T0iSd5LggbIwIgP28lRtOew52clIzgn7a0A6V3gYQZEyTmC7BlwTOFF9k="}]}, "directories": {}, "dependencies": {"babylon": "^6.2.4", "babel-traverse": "^6.2.4", "babel-types": "^6.2.4", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.3.0": {"name": "babel-template", "version": "6.3.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "6ff5b3273f9e82c93e5e81cd608d38fcf926816e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.3.0.tgz", "integrity": "sha512-VjD1vw3AjV7Uvh8E1IW05mI9ORDnhUlQmECdEx1KX4tA8ksCPKA6GuNicBXLXC7AckvTMAg+yh/ANPnSGFw3Gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICn/G82H8//UqNeXt9tyYemTwUfNolWehVnGFOWxQjQ0AiACJ2nToZl2L6VPjmR4GixKF/yUqfWKtrusUvzY9t8DOg=="}]}, "directories": {}, "dependencies": {"babylon": "^6.3.0", "babel-traverse": "^6.2.0", "babel-types": "^6.3.0", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-template", "version": "6.3.13", "description": "Generate an AST from a string template.", "dist": {"shasum": "8575f16867a928a9dae84f3bbcdca7e7806fc22c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.3.13.tgz", "integrity": "sha512-jK9y4+FybZ72xM71il4v9qKQrgKWMDvtUVmKkt1LK6QmhXNxXdkQ3IkxTEzKCEMlFG1ZtwIigbfejLzUMHQP0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDohsH/gYhYGu1di+D0Oo2XbMt6Dz7HzJhjPPZPSRrukgIgfotLt6sMSDr5sDXN0S+BobThI3eBuEhL4leBYMtMjrg="}]}, "directories": {}, "dependencies": {"babylon": "^6.3.13", "babel-traverse": "^6.3.13", "babel-types": "^6.3.13", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-template", "version": "6.5.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "bc29886672be55d7b2a3a38d8e3ceb01c96738a6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.5.0.tgz", "integrity": "sha512-ruuWyr9y+DVCJtuETsyutMAe4fsKYCyQPTu33S1o0ZREz3tsdEg+jDMOCZM4H74T1SHkayerknCQkGv1TQqI2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBHaSmzZ//A0me6yJABObjIDnlZZFpv6qcGK8E/kUlzzAiEA5an3xpllSa1PP+5p/2Ot1CBKrnOZk4hSnpSxphMNwZQ="}]}, "directories": {}, "dependencies": {"babylon": "^6.3.13", "babel-traverse": "^6.3.13", "babel-types": "^6.3.13", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-template", "version": "6.5.0-1", "description": "Generate an AST from a string template.", "dist": {"shasum": "0fe16e8f245e797b49d8f4939370768ff783f4e9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.5.0-1.tgz", "integrity": "sha512-yZpqgOm2vMSV88Ghy3DyCJAV6azQnC1eiZbaublodAMPrHCRv8Rrrpx0DtIB+xMG8cT16jz4DNj4q3yK60XqEg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEnbPicMBQo3433OhVLcYjbXLh6TQmSoKPgeWF4L1EBAIgNtC2O7skmgerC4bQytoZUNxQyBPXnKijX+2/bBfHemY="}]}, "directories": {}, "dependencies": {"babylon": "^6.5.0-1", "babel-traverse": "^6.5.0-1", "babel-types": "^6.5.0-1", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-template", "version": "6.6.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "df4f302e512337567ecccba87ebcff92f80e24cb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.6.0.tgz", "integrity": "sha512-cZ1sZ3LmtDFYj0M+5KWVdoddZqt/rwgMNeE9e9WUxyrJwsTomeOuP60k9SJROQnQFPxuKikltYqmGYmRdhM6Kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+U55v5udHN9bBYnrJ9gFYLuCVhw3tqTuGcuzIWrFITgIgDntBVHUta9+ur6NowIxUk8J3vMvdg1EH0h1yVZ3vSdw="}]}, "directories": {}, "dependencies": {"babylon": "^6.6.0", "babel-traverse": "^6.6.0", "babel-types": "^6.6.0", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-template", "version": "6.6.4", "description": "Generate an AST from a string template.", "dist": {"shasum": "cf08a350085279fd0d88cafd01e631a8a4041cda", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.6.4.tgz", "integrity": "sha512-F72Bf9Obf8+uDE/+h/ixHwdyjfAlVOCQTThrnuQaGgIXv2MB4cCTukZyNhNeqTC1jVo3g7lxAaAVEh3Uo/WtaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZQhzuABow7x48cJZrf6xTrwz7hAwO/MQnTnTCfaYYtQIgLyUBs+Awwhnti/lMJrLANOOsd+tbebMXW+qP71O4H18="}]}, "directories": {}, "dependencies": {"babylon": "^6.6.4", "babel-traverse": "^6.6.4", "babel-types": "^6.6.4", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-template", "version": "6.6.5", "description": "Generate an AST from a string template.", "dist": {"shasum": "91567523948778562a515426fa40a25be11a85db", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.6.5.tgz", "integrity": "sha512-U/Gd1lyrxEgIo4FWL1we3gincN2RWAAHU+bDypY4zSoi90+1cMlKV851+Q+quGRIWB/p4bTV7LwOBmMIqOFN0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCy9MSZCQzoeDmflS72cz4/0AGjvLWy+a1axQzMbgZGpAIhAPOWyeOcjHtO4aupCOnFPMjJwIw+XThdHU396RWk1Py+"}]}, "directories": {}, "dependencies": {"babylon": "^6.6.5", "babel-traverse": "^6.6.5", "babel-types": "^6.6.5", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.7.0": {"name": "babel-template", "version": "6.7.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "e30f32639aa2bcdaa6a77bc9b92bde5c98144902", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.7.0.tgz", "integrity": "sha512-3KJ4wVXqh0VGmfJhfpC6eSC/QWXrQZwaH8jByFFc5QlIdL/BtwiWEvnh71MvoLsvjSq0CtibWSSWc/1GaP43ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtIGv/W8IM//f2xRywTaX8ySbFdnXfDvJ+dlHDFR4O3AiB530RLTX3DOghtSXOp7YOed9FXQLoJSGMuzneKIuI6vw=="}]}, "directories": {}, "dependencies": {"babylon": "^6.7.0", "babel-traverse": "^6.7.0", "babel-types": "^6.7.0", "babel-runtime": "^5.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-template", "version": "6.8.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "20eb33dadd5ada9ab6388b3ee74239d02df20193", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.8.0.tgz", "integrity": "sha512-5hN3duSrJP8IDQPx/co/i0NzrRck00D3Z09gm7vRoEtWJocT0/P33wSvjInRKnbTcSURKxym+W8PA27ZhpBSFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5RaXzW8ntvIq+xO2OkAJpWKTiFNIUFCjxfcSEFoSfRAiBcMGfcRTyE+noBkwrg93xqwbha9J0jRJp0OIzyv7jKlA=="}]}, "directories": {}, "dependencies": {"babylon": "^6.7.0", "babel-traverse": "^6.8.0", "babel-types": "^6.8.0", "babel-runtime": "^6.0.0", "lodash": "^3.10.1"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-template", "version": "6.9.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "97090fcf6bc15685b4f05be65c0a9438aa7e23e3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.9.0.tgz", "integrity": "sha512-Qm92fRm/b/meu6EVot+bKaMu/ay6pxpvtwLPX5lUwobznzIOkX6/y5KY1mmcPmTJJZJ84P/m13uE5lbKwn7ELQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNQZ/ME/tzW92yH9W24WDmGIINBTGoHKTQGFYGz1pUjAIhAMiJvAspL13Gw/fei9D55wDAYC3AMG/bsuhFIC1BAsRc"}]}, "directories": {}, "dependencies": {"babylon": "^6.7.0", "babel-traverse": "^6.9.0", "babel-types": "^6.9.0", "babel-runtime": "^6.9.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-template", "version": "6.14.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "9f30497d9bda51f1e439c2ee547d4de89b34b01e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.14.0.tgz", "integrity": "sha512-ZsUUwtZWGglZEdq9I1bXKveA8AwDXp3lMrZvniRYpcVeqhtp0ciD6o4qnkVT5GlpjsS54xGHZu+IgOkkYkNqZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ2d59LmjlTSOnX8n/PhL9XFfJHM3TvqZOLPqFK2XTPQIgeNLRFtMS7EXMFxSzLtQnUZgArhXJ27fnvq1z+yqfm/Q="}]}, "directories": {}, "dependencies": {"babylon": "^6.9.0", "babel-traverse": "^6.14.0", "babel-types": "^6.14.0", "babel-runtime": "^6.9.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.15.0": {"name": "babel-template", "version": "6.15.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "a0f249c89d5d57e806fc50d0ec522fbddeade1f2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.15.0.tgz", "integrity": "sha512-NMciJSDB4p/4/XBewi0Fd0SZWvQJJMenvHzwb47Lvc84Ntl41QxyPze3SgJaWX/Oae7caD75rbcN/3sKxsz2Cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGUah3Nu3MF71NtIvmYpmVWAhPlxyXDmKyyBrDdv0h0fAiEAt9PUy/XueIAFYA39fkovVa2GsYigkAP7Znr8Xlk0ZSY="}]}, "directories": {}, "dependencies": {"babylon": "^6.9.0", "babel-traverse": "^6.15.0", "babel-types": "^6.15.0", "babel-runtime": "^6.9.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-template", "version": "6.16.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "e149dd1a9f03a35f817ddbc4d0481988e7ebc8ca", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.16.0.tgz", "integrity": "sha512-ZeJdsT/Eu/2O6WQaKlPJKBHnC/g3CL2WZbfI7Y5vjw14VFSoAoBMCUOko6AwTHOrxS1hJ/FyIH0XAofc941qVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDWe3uLt3Ne+pe2if8JROQ+jBNFI8vKvSoJzAnRhHZBgIgRikaeOUzTzetsu8gP6FWkk70uGpODqkRwd7VZgSFEOk="}]}, "directories": {}, "dependencies": {"babylon": "^6.11.0", "babel-traverse": "^6.16.0", "babel-types": "^6.16.0", "babel-runtime": "^6.9.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-template", "version": "6.22.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "403d110905a4626b317a2a1fcb8f3b73204b2edb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.22.0.tgz", "integrity": "sha512-+lkwSis8pjRz86SUYUQqDK+VZv85ogVkcHJIN9cr66WBiF3bNqzoMzcp6muyAlMPYTVLQMFkI1yRuWyXmiFWew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7sC8k5k+RVrkU8JyOaVrXQxYnuq9KqaTThRlxVEfT+gIhAPHnI39xrZhnEuopsrtv87gKfZixS/bmsEZPWkcL8cYq"}]}, "directories": {}, "dependencies": {"babylon": "^6.11.0", "babel-traverse": "^6.22.0", "babel-types": "^6.22.0", "babel-runtime": "^6.22.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-template", "version": "6.23.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "04d4f270adbb3aa704a8143ae26faa529238e638", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.23.0.tgz", "integrity": "sha512-YeXJYUh5wMEdJpsmKmz20I/IxtDNarxnurMKaZjT2HIiQtCoJx1YYRcy5mlvZ1cwdNX4RwqzWEsSfmhCkjpvgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZp0GbWUPtxqGDoifCaWQ/ZozFF1zHH2/baAGYYMZcJAiB69sgZK88VlC2/ZNtAUwSruwVR4Y9QWFWUf8/LJssogw=="}]}, "directories": {}, "dependencies": {"babylon": "^6.11.0", "babel-traverse": "^6.23.0", "babel-types": "^6.23.0", "babel-runtime": "^6.22.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-template", "version": "7.0.0-alpha.1", "description": "Generate an AST from a string template.", "dist": {"shasum": "df6394b3ac62ae7721691d1d4e1e7218f3063bba", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.1.tgz", "integrity": "sha512-e1jCWmURFitN87jf5jbkdpH9DiZIu32EAZpNsLV9q/oHmdSzjncHY0zJfwKrcYfEm/J62w+O9bdbCpIO+IS86w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHdgkCBDGACR9qWeZOeSU+Zg3hrZiylXbnCpV9ucs8pjAiEA5TuDCaiWAdP9CUCiCaZvkQLUJhUOklv6yjjWTQT5d7Y="}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.4", "babel-traverse": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-template", "version": "7.0.0-alpha.3", "description": "Generate an AST from a string template.", "dist": {"shasum": "eff537d49216ae6e70722f3556a17cc3de62b117", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.3.tgz", "integrity": "sha512-ccqzrECyidqIF7vStJLejyfwEIjUhNQZ4FnqdsL/R2fvC00tsgotUPCSx5jgnHohybzZ+jd97s49G6Gvj8Z2tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDww5YrYDsgb4WpTHliGO9BmDoPbL/qrnN0WTPjjsTLyAIhANGiqcjbTnOTzYKNhbQSV1CRgF/OEagpfBv5wMdUlqkW"}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.7", "babel-traverse": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-template", "version": "7.0.0-alpha.7", "description": "Generate an AST from a string template.", "dist": {"shasum": "82e26500980d1b3f14d9ebe8ae8b9325dc158392", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.7.tgz", "integrity": "sha512-5j21DGQ2T2P2bj7L3JUb6WtXqQ5yjPNVqmEtYF1nTA/nLc7YALtJQxxZ9bxwhy+V45d3DM9B+oTgqPkpqQKF5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3POiMgZbtka+xy8sgMQBMLKyEMak6R9K5K8jWtzildwIhAP1gh8WoZuBdYmUeIgkceSYQCzA6II65v67YP9acW8np"}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.8", "babel-traverse": "7.0.0-alpha.7", "babel-types": "7.0.0-alpha.7", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-template", "version": "6.24.1", "description": "Generate an AST from a string template.", "dist": {"shasum": "04ae514f1f93b3a2537f2a0f60a5a45fb8308333", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.24.1.tgz", "integrity": "sha512-eLYCmQldaoRCcb1C1DjNiKTAzkZjPot+hkCGW4T6LHV/wxIg5WXLpnQ6V0+6ClIz0tSJgVa+QaH6gNtZJVwRsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBSvEk4fUYw8M/mWdpE68uxMm1Zx6LteOlKVFTGSJRDgIgbTV4LZI27Zi1x1RhdKZ1RrPByLiCffk64x2E5cc/OkE="}]}, "directories": {}, "dependencies": {"babylon": "^6.11.0", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1", "babel-runtime": "^6.22.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-template", "version": "7.0.0-alpha.8", "description": "Generate an AST from a string template.", "dist": {"shasum": "d3784ccbca118fa61f91be56d0fb09a75b78e8e3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.8.tgz", "integrity": "sha512-wrSo/uukLKjHCZKXFEcccD4aYhV5+Oj2Gp9PLGxTYk5B8nPUGhuszGWfLlNqj3zJHl5mDwJtKBffzu2mAmVH7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICImpSHeEXjBmv8KMRxV/txuGy87hiW+qZo1++006f9UAiB/dCUG/Dp4cA3uJzR1mPT+h/F+ydNYMHwiQ+NMhkdXPw=="}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.8", "babel-traverse": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-template", "version": "7.0.0-alpha.9", "description": "Generate an AST from a string template.", "dist": {"shasum": "e32c51ea13f8ed391ed4a1c9adcfd3ed9ca928dc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.9.tgz", "integrity": "sha512-Q5sliSS4MPml2egCJBsU15J9CZi4D9C5/KyOsSScMvLF4r6pTR7Uua+4xoPb4jOYcyrUkyA5fAT+g5YJvbp74g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDORnGpX6sKLvKoUTuwavTdazJvRcRdqndDlEIiby8ZiQIhANEvb4n3LwnJmdZraUzCgZb/0uWvoBeXfM+l6CIQxj1J"}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.8", "babel-traverse": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-template", "version": "7.0.0-alpha.10", "description": "Generate an AST from a string template.", "dist": {"shasum": "e382d0ffb6356a1d21f51a89cb573aa8c74512db", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.10.tgz", "integrity": "sha512-A<PERSON>rjBKL1ZDHZ8NchRPfkpihnW6kzp0Bw1xwIPoAfoN0WACE5nFMi9B4eXsae6l3DbAomMHvzRH6G5ih+EO0xQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGuqCYgASadk+4xNXJEgm6c6aLZagvU3bh3MpVC5zhqvAiAXNq0qJ9I0q0MMs/8+JVR+QhYorRCRVAtulTyn+U9qPA=="}]}, "directories": {}, "dependencies": {"babylon": "7.0.0-beta.10", "babel-traverse": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-template", "version": "7.0.0-alpha.11", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-GemFRTRdpVQTRAG1l9rflriH2vkGHfSN8q9m1NuIHy/5ZtydLq/Ve5Sp/yO7L9tKjfetjUd17gGn3ETP3XoQAQ==", "shasum": "5448375020510a175c3d2f7fb7e1dcd72b18d77c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHty1QmFyDaDPJS8sQC34uBiV1bgMVsEZzRjYU7orawAAiBL7qePLQM0JHh2qVvhb2UMw2gNMEssnewFmbExdrV+hQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11", "babylon": "7.0.0-beta.12", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-template", "version": "7.0.0-alpha.12", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-OwEhzLJTUK2PMcqnx/knycDbwiOfyHZ+9RE62ieYH38Hn3q3WKqkD22vqCQcUNevO9LBOOuKkJLXKglo8X3NgA==", "shasum": "f41e395ab0bb9ea26d04e968cdcf703adbaec78a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGkQx3OveJw6nQld0Dr2Me8UfqMzC7wFk2Bu1IGj4wHAiAcgie3xEav7sngqnuas0So7Xkk2NrMKBsK7yf5HF5AMg=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12", "babylon": "7.0.0-beta.12", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.25.0": {"name": "babel-template", "version": "6.25.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "665241166b7c2aa4c619d71e192969552b10c071", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.25.0.tgz", "integrity": "sha512-Ak4qz4gxFYXuj3O5m+Um2RvhZw2CUVTDM3sMK5XhrJLRfIFi7nxCwBLCG0RBwqNzo7DVM996bHlo6kSkJ0X/jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBhQXYQMRXhKLq5M/FZb5f5JQobXrOoqKtQTAJr6HWsNAiBm9WOm5dHn2sl00K48Ftxsthg0vADuJ0WfMra7z1qRog=="}]}, "directories": {}, "dependencies": {"babylon": "^6.17.2", "babel-traverse": "^6.25.0", "babel-types": "^6.25.0", "babel-runtime": "^6.22.0", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-template", "version": "7.0.0-alpha.14", "description": "Generate an AST from a string template.", "dist": {"shasum": "99f28179aac12eb7771a4bb8b5c08cecf14f3439", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.14.tgz", "integrity": "sha512-wRg3yyB4Dq0hbH+oEiN4XjIuA9Nqy21wkIXKSul46MSsx33JEJ02hgdCTVD8JTfrIKNKh1LnT732wGQXsvnU0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+I13ID45KlfD7H0osFM5xaH9Tx3GL1r/DlkodCYofVAiAZlsWiTneZxPgvMI9aUolrZvCZ2de74uyRE7qebdsdew=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14", "babylon": "7.0.0-beta.13", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-template", "version": "7.0.0-alpha.15", "description": "Generate an AST from a string template.", "dist": {"shasum": "08b56562987c9893bbd6646bce4819074ba1cf90", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.15.tgz", "integrity": "sha512-MK28CgwFJroYBX/gSzZPk+9byorNjOXZ69BITk0VKYjDdtF7UjvoMM5CK5dTfs9sTdUkwVEnzJqmS6uPs/PNsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHmzk2F1dSkO9/rIvk79cLBnA7uNNLqU5+2hySV7yvV3AiEAzd9ogWnDbperVelg5osf49BQ3V4NkPGcwCQ7Nl/uscU="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15", "babylon": "7.0.0-beta.13", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-template", "version": "7.0.0-alpha.16", "description": "Generate an AST from a string template.", "dist": {"shasum": "f8bec2b5074e150c47b415d13db231b15e45598d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.16.tgz", "integrity": "sha512-cSBuxggfdkgPPhNhP4hbmO1YSQLvonP32stNARqul23as3oLEldYktWlh5YjdIL7ns1y6mwtXgSvJrDZ8nLAPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGLNjGsqFfzMhiyqBaqRUlPe7INqF3afUmrPTyRpaFDzAiBOfS9P62qRq6saO4TqUWD8qtMAoYU/Oxi6q3KNiaqWhA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16", "babylon": "7.0.0-beta.17", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-template", "version": "7.0.0-alpha.17", "description": "Generate an AST from a string template.", "dist": {"shasum": "085597c0021644c72fa74460a8f76a85e76da943", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.17.tgz", "integrity": "sha512-9L+YvJHoTK1NonbBGjxFAeJHpnNxIYTxQ92UR0g26VkXHmPIfr+iyPptOYlZkH9SCL6vvUqkIn1NR/Gifw9P+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCfymHxMMjP3A5TiubqrAA0I/HGO4RIQXYP8yYVEUJMAiEA4KDH0Tt5ehaFWGzSetq4wqico2T3cY9B0RSEVwUFLrk="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17", "babylon": "7.0.0-beta.18", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-template", "version": "7.0.0-alpha.18", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-ZeWz1IIKknajWGcrkPH5jOZnYZO6QSQxMWomb2xlwmOoo7HcvQag/tfi2rLBFOjZN79oa6zccLM4fUsMJvLXQw==", "shasum": "333cf25e642f1411484558aa5a78aed78ac58786", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFg+fvFtZ1kFma9CAPz+XWUxWyoOlnxN/V0wzGlVnikuAiEAgHw34hsZuv5ziQrdzmQCPN3S8Evqy9Zmvi1Urg0sxqU="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18", "babylon": "7.0.0-beta.18", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-template", "version": "7.0.0-alpha.19", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-96IEIj99ubXPjZyF2V/ZZrjSUAO3baNtOP16Qlibd9q5Hh4WVm+Xv2mOqdjABJG96N991EAtNXk3+JibJGxnWQ==", "shasum": "6ef867c43dbf8c204a22f9cfd62101c64c8ade9c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErKbeUPI2AAg1rlEPzmWySe6yRoLtpG9yM17KvxPv+5AiAjsXZRMt0ASJO7jF1hIpHiN7v3HsAQvvAGInIYCbJvyw=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19", "babylon": "7.0.0-beta.18", "lodash": "^4.2.0"}, "hasInstallScript": false}, "6.26.0": {"name": "babel-template", "version": "6.26.0", "description": "Generate an AST from a string template.", "dist": {"shasum": "de03e2d16396b069f46dd9fff8521fb1a0e35e02", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-6.26.0.tgz", "integrity": "sha512-PCOcLFW7/eazGUKIoqH97sO9A2UYMahsn/yRQ7uOk37iutwjq7ODtcTNF+iFDSHNfkctqsLRjLP7URnOx0T1fg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqVVfDkQLNGoG5XDtumCzMqrpfaHdnyvgtnpHSHHEkhAiEAydjN6tDKcxY8njHDhPoIKYy9e0gI60odkfxZ6QMumLc="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-template", "version": "7.0.0-alpha.20", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-/663UVaKGdancvZtdJOdNoWxRs44nvR6490B/M+QzqjYECJPdlIJB+ZAfrEjOjzcQWrIn6FKOhVKJdOr2qi+AQ==", "shasum": "2ddab1b03a9eb8a98e7f9920cb4dc1816430db80", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChqvkwtekktbd7YhS+dHoLmHj+eaVjGAoc8W/h0rXaFQIgJeRyOQLCvo6Ko6XlWEdIZg2Qciaz6QDAwnKfnHvbwL8="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20", "babylon": "7.0.0-beta.22", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-template", "version": "7.0.0-beta.0", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-tmdH+MmmU0F6Ur8humpevSmFzYKbrN3Oru0g5Qyg4R6+sxjnzZmnvzUbsP0aKMr7tB0Ua6xhEb9arKTOsEMkyA==", "shasum": "85083cf9e4395d5e48bf5154d7a8d6991cafecfb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFyT4h+qnapr+QJCgTChsAXnOIkmVoG/Q+ea7/hGzIvTAiB9yyyAGALNNXWVg2yoB2lEgnP9KWrilgb+u89lHAjq3Q=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0", "babylon": "7.0.0-beta.22", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-template", "version": "7.0.0-beta.1", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-EyRSOfj4Z7QBCeWqx0Q3ojEXDnouwdq098RgaahCDhGoLmR6FsJ791UMjrihTKr2Yu8r9ukhmLV5E5HbqEFyBw==", "shasum": "f8711b186f5087589b6c2bbf1fdcd014cc05c26e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBtCg+bCB6YoPsazh+Z8o01gRR/tVG0nzX6HpUxInwwPAiAOWNl4UQR9FfGdCZ+WkDZ/veT/eAOdUK8s6MI2xZQBrw=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1", "babylon": "7.0.0-beta.22", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-template", "version": "7.0.0-beta.2", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-2Ez5h/VrTqR0cAx+7EyPOTb19vvh+KaEHoolWxT7T4mWLtkxGAt+rJlQl8LFJVkNXSgpNeiMV8t6L5QuRPo94g==", "shasum": "e5140a36854c113e12680110f0975daf09d4b4c0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtxrqegjuNn/cKwkhc6rawVaA5Jhg8z2uMYuGA9eJyLAiEAoLM+YR6LMdkkVkfLzCbd7YgiYbeECsUatLFnN0WVoA8="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2", "babylon": "7.0.0-beta.25", "lodash": "^4.2.0"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-template", "version": "7.0.0-beta.3", "description": "Generate an AST from a string template.", "dist": {"integrity": "sha512-urJduLja89kSDGqY8ryw8iIwQnMl30IvhMtMNmDD7vBX0l0oylaLgK+7df/9ODX9vR/PhXuif6HYl5HlzAKXMg==", "shasum": "ebb877b6070ce9912b0d0c22fcad3372165913a8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-template/-/babel-template-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1M3quD1svSpwD8ZqTiGoOf5rRceAQ70W4/iPWghJHvwIhAK3oLv4I3xgQgF0kZFrrOIkwc1LvvzA6zeJcGflxHxJx"}]}, "directories": {}, "dependencies": {"babel-code-frame": "7.0.0-beta.3", "babel-traverse": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3", "babylon": "7.0.0-beta.27", "lodash": "^4.2.0"}, "hasInstallScript": false}}, "modified": "2023-07-21T15:42:49.334Z", "time": {"modified": "2023-07-21T15:42:49.334Z", "created": "2015-10-29T18:22:21.286Z", "6.0.2": "2015-10-29T18:22:21.286Z", "6.0.12": "2015-10-30T04:56:12.971Z", "6.0.14": "2015-10-30T23:44:17.020Z", "6.0.15": "2015-11-01T22:11:10.695Z", "6.0.16": "2015-11-02T07:14:40.991Z", "6.1.17": "2015-11-12T21:44:02.333Z", "6.1.18": "2015-11-12T21:53:11.533Z", "6.2.0": "2015-11-19T04:34:41.349Z", "6.2.4": "2015-11-25T03:16:04.341Z", "6.3.0": "2015-11-30T22:59:02.829Z", "6.3.13": "2015-12-04T12:01:29.573Z", "6.5.0": "2016-02-07T00:08:29.284Z", "6.5.0-1": "2016-02-07T02:41:51.990Z", "6.6.0": "2016-02-29T21:13:12.518Z", "6.6.4": "2016-03-02T21:30:02.182Z", "6.6.5": "2016-03-04T23:17:16.430Z", "6.7.0": "2016-03-09T00:53:06.903Z", "6.8.0": "2016-05-02T23:45:24.857Z", "6.9.0": "2016-05-17T18:49:50.018Z", "6.14.0": "2016-08-24T23:41:01.429Z", "6.15.0": "2016-09-01T15:03:09.559Z", "6.16.0": "2016-09-28T19:39:05.985Z", "6.22.0": "2017-01-20T00:34:28.829Z", "6.23.0": "2017-02-14T01:14:20.967Z", "7.0.0-alpha.1": "2017-03-02T21:05:40.925Z", "7.0.0-alpha.3": "2017-03-23T19:49:42.821Z", "7.0.0-alpha.7": "2017-04-05T21:14:12.763Z", "6.24.1": "2017-04-07T15:19:14.163Z", "7.0.0-alpha.8": "2017-04-17T19:13:07.218Z", "7.0.0-alpha.9": "2017-04-18T14:42:14.069Z", "7.0.0-alpha.10": "2017-05-25T19:17:39.840Z", "7.0.0-alpha.11": "2017-05-31T20:43:51.157Z", "7.0.0-alpha.12": "2017-05-31T21:12:07.292Z", "6.25.0": "2017-06-08T21:29:08.541Z", "7.0.0-alpha.14": "2017-07-12T02:54:02.590Z", "7.0.0-alpha.15": "2017-07-12T03:36:17.493Z", "7.0.0-alpha.16": "2017-07-25T21:18:12.617Z", "7.0.0-alpha.17": "2017-07-26T12:39:44.564Z", "7.0.0-alpha.18": "2017-08-03T22:21:21.111Z", "7.0.0-alpha.19": "2017-08-07T22:22:02.417Z", "6.26.0": "2017-08-16T15:54:17.927Z", "7.0.0-alpha.20": "2017-08-30T19:04:14.162Z", "7.0.0-beta.0": "2017-09-12T03:02:45.158Z", "7.0.0-beta.1": "2017-09-19T20:10:07.587Z", "7.0.0-beta.2": "2017-09-26T15:15:49.889Z", "7.0.0-beta.3": "2017-10-15T13:12:14.494Z"}}