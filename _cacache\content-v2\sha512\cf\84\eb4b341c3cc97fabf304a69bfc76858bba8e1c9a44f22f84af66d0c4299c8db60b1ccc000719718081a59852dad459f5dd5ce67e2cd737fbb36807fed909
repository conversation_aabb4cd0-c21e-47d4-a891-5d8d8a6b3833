{"name": "parse-json", "versions": {"1.0.0": {"name": "parse-json", "version": "1.0.0", "keywords": ["JSON", "parse", "async", "promise"], "author": {"url": "https://github.com/Kikobeats", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@1.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/Kikobeats/parse-json", "bugs": {"url": "https://github.com/Kikobeats/parse-json/issues"}, "dist": {"shasum": "d2b2abb5b86bb36fb50f3835a040109c5a3c9c37", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-1.0.0.tgz", "integrity": "sha512-kJ+nx+a39l/d921JYb0MMH8vfaui+iFGv27vtSDajGrO+BXY/pZ0QvrRgRREPq9jC+MK0ylWdT6ZmgpFBUviOw==", "signatures": [{"sig": "MEUCIQCGZ2KujLN/DAQXE7vuO/F6gKuzP7YCsjdyL4OwXRqPkAIgD9yhlWw0AdwftttLa9VpO4VAJgtHpDuoIvfH3EOY9Z8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "d2b2abb5b86bb36fb50f3835a040109c5a3c9c37", "engines": {"npm": ">= 1.4.0", "node": ">= 0.10.0"}, "gitHead": "2e224300571311c6b9dc8cb387c4b7c7069b31dc", "scripts": {"test": "sh test/test.sh"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "deprecated": "renamed into 'json-parse-async', use it better!", "repository": {"url": "https://github.com/Kikobeats/parse-json", "type": "git"}, "_npmVersion": "2.11.3", "description": "The missing JSON.parse async interface.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"cb2promise": "~1.0.0", "errorifier": "~0.1.3", "ensure-async": "~1.0.0"}, "devDependencies": {"gulp": "*", "mocha": "*", "should": "*", "coffeeify": "*", "gulp-util": "*", "browserify": "*", "gulp-header": "*", "gulp-uglify": "*", "vinyl-buffer": "*", "coffee-script": "*", "vinyl-source-stream": "*"}, "contributors": []}, "1.0.1": {"name": "parse-json", "version": "1.0.1", "keywords": ["JSON", "parse", "async", "promise"], "author": {"url": "https://github.com/Kikobeats", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@1.0.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/Kikobeats/parse-json", "bugs": {"url": "https://github.com/Kikobeats/parse-json/issues"}, "dist": {"shasum": "6fc95f7ba8d60a58ac4cd9ea8ec88374e85987e9", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-1.0.1.tgz", "integrity": "sha512-AMcKJvrdyGeLpr/+3b2tCsEIiYwqBj3sffwiUPsF0z6SWcu+OrPkyYJAQzTUm9FMQHqnuurVHd+Kn4IZio+qvA==", "signatures": [{"sig": "MEYCIQD9F1aL2tLdUCtRJ62m3JCbwM90I4jH+eRI7Rp1LjHdHQIhAMfCFJLQKPjgVC1GVk6+aNod07wOhWlNAhO9oG4Sl5ma", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6fc95f7ba8d60a58ac4cd9ea8ec88374e85987e9", "engines": {"npm": ">= 1.4.0", "node": ">= 0.10.0"}, "gitHead": "25928f3a558d379cf6a52b9ae416177f2463237c", "scripts": {"test": "sh test/test.sh"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "deprecated": "renamed into 'json-parse-async', use it better!", "repository": {"url": "https://github.com/Kikobeats/parse-json", "type": "git"}, "_npmVersion": "2.11.3", "description": "The missing JSON.parse async interface.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"cb2promise": "~1.0.0", "errorifier": "~0.1.3", "ensure-async": "~1.0.0"}, "devDependencies": {"gulp": "*", "mocha": "*", "should": "*", "coffeeify": "*", "gulp-util": "*", "browserify": "*", "gulp-header": "*", "gulp-uglify": "*", "vinyl-buffer": "*", "coffee-script": "*", "vinyl-source-stream": "*"}, "contributors": []}, "2.0.0": {"name": "parse-json", "version": "2.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@2.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "xo": {"ignores": ["vendor/**"]}, "dist": {"shasum": "e59e30d2642041e9a359c99613c476b1133ca428", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-2.0.0.tgz", "integrity": "sha512-OHC9AAb3O1/NuY9JEOW+BSKa39XeqHrhidLNrryoo9VRf7bX+O99Qu67ly72QsOaXR7Bszy8EQeciNAwTReE+g==", "signatures": [{"sig": "MEUCIQCUk2aEcwt8ExVIZ9PZs0cwIsJr6NymI5z3yTPor7X/2QIgUFf7Cb067AGEXZASsFMG5VobjGTAWGhdnBxAzRMtIxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "vendor"], "_shasum": "e59e30d2642041e9a359c99613c476b1133ca428", "engines": {"node": ">=0.10.0"}, "gitHead": "a1c87cd506ccb52eb4aadb5eacb5d0cf54a3d84f", "scripts": {"test": "xo && node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"xo": "*", "ava": "0.0.4"}, "contributors": []}, "2.1.0": {"name": "parse-json", "version": "2.1.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "xo": {"ignores": ["vendor/**"]}, "dist": {"shasum": "7125b5f3f679b3d1d5c7fc4cd561cc1f4576aacc", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-2.1.0.tgz", "integrity": "sha512-ov5dmwlB+Kd4rCN+65vFFi49Uny3Kslwnpa5tyrn3k4+RN7ZEFZyUuH1r/k+4W1vO6avpFDxTDhdQslYyuIRDA==", "signatures": [{"sig": "MEYCIQCNp65rAjTQMwd37s8alR3QlxVErzj6ZuptpCtEV4X74AIhAK844X6Tw/mUMe3wuthU515N3ho05QmvPjDheRkDLsMH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "vendor"], "_shasum": "7125b5f3f679b3d1d5c7fc4cd561cc1f4576aacc", "engines": {"node": ">=0.10.0"}, "gitHead": "f91dcce91727e00a22dfe22af57575c4cbc34c77", "scripts": {"test": "xo && node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"error-ex": "^1.1.0"}, "devDependencies": {"xo": "*", "ava": "0.0.4"}, "contributors": []}, "2.2.0": {"name": "parse-json", "version": "2.2.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@2.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "xo": {"ignores": ["vendor/**"]}, "dist": {"shasum": "f480f40434ef80741f8469099f8dea18f55a4dc9", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-2.2.0.tgz", "integrity": "sha512-QR/GGaKCkhwk1ePQNYDRKYZ3mwU9ypsKhB0XyFnLQdomyEqk3e8wpW3V5Jp88zbxK4n5ST1nqo+g9juTpownhQ==", "signatures": [{"sig": "MEUCIQDFryAM3HaZyIDPPZsKbZwvh8eUDHFUq6GrjyatJDApCAIgMqq8hv1uPmS0LvSpF6OjsDy6g8NRTuqu32r/rdej6FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "vendor"], "_shasum": "f480f40434ef80741f8469099f8dea18f55a4dc9", "engines": {"node": ">=0.10.0"}, "gitHead": "419b0cbb83e67af53f9fd3f7ff98605ea2020eb6", "scripts": {"test": "xo && node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/parse-json", "type": "git"}, "_npmVersion": "2.11.3", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"error-ex": "^1.2.0"}, "devDependencies": {"xo": "*", "ava": "0.0.4"}, "contributors": []}, "3.0.0": {"name": "parse-json", "version": "3.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "fa6f47b18e23826ead32f263e744d0e1e847fb13", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-3.0.0.tgz", "integrity": "sha512-bO3CWnT9rDPNFvYVH9+t7ZqrML3DUPYzyN3X87T1snwJHbgrb8+oscL116q39/ViL+qYoMCwLH70N0bTORMa8w==", "signatures": [{"sig": "MEUCIGja8BwhgHUhKdum9rjj7DVDR5EEEVIawOU00viI6inKAiEA0PJF0l+wJptDHxmiHP1FgNOP5LJ8jZh+aK5UwvNH6PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "vendor"], "_shasum": "fa6f47b18e23826ead32f263e744d0e1e847fb13", "engines": {"node": ">=4"}, "gitHead": "aacda96ce310718e1fff98cefdb6f114f031d23b", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "4.8.3", "dependencies": {"error-ex": "^1.3.1"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json-3.0.0.tgz_1502964675950_0.48904961277730763", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "parse-json", "version": "4.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "be35f5425be1f7f6c747184f98a788cb99477ee0", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==", "signatures": [{"sig": "MEUCIQCyCt67wE7l7vKmhyBlssm4KWEOQlfz8r8Z4iQccEEOrQIgbVNCH0kcyavdR3O9eNVr9nks2jfEYmGscOlUoDzUOYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "vendor"], "_shasum": "be35f5425be1f7f6c747184f98a788cb99477ee0", "engines": {"node": ">=4"}, "gitHead": "d1f4edbbef6e76ddc084b2f88e4d64a2b08081c5", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json-4.0.0.tgz_1509782029937_0.44315575854852796", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "parse-json", "version": "5.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "73e5114c986d143efa3712d4ea24db9a4266f60f", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-OOY5b7PAEFV0E2Fir1KOkxchnZNCdowAJgQ5NuxjpBKTRP3pQhwkrkxqQjeoKJ+fO7bCpmIZaogI4eZGDMEGOw==", "signatures": [{"sig": "MEQCIHEYLeQ/pIfNqTcR1Vk6L0YfqqEk1ZNBZHK57jQuPGX2AiAnBcvJvUG/g93PIk+xd1azs2KcuS4AqcNAFnINwAQKcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdG5wuCRA9TVsSAnZWagAALdEP/05PCibYsIS3td6u3H2f\nQ/e68ihzQKgTcFRazUSWfXEYhNxEvj7ZjiZWlpdY/rvtRMMrTjyQHYuteiGk\n1UmE88sssHa27TWZKgukr2SoKaz/7S1PlHlmEXkjIkwq+7DJL0u4PD9xgP+X\nbo3VG9HnPaMIkgKWUQRKdvw95h9Sb8wQji6j0w0XOw9oftYHX61M7y+X3igw\na14swEeK2DhkA8rwTlnXITFSBayQkenFpeFx+ymTgTdTGZIAuWtharXUHAgO\nU0mBtNm23WicUbBEeObYC0G3+3j7TlODCzewr75ZmdD4q1Fv4+GBIKQNi6jG\ntO4nWrGjogzETDIB3Un2NSDUuGXXewZA+MEkkJvIOvHBWpri+sOpZkAmKJVm\ntA6hNTbgtfQ0To1fyJ9y2jtz6q+u2kgtKhZtiiPZp/oQq2keyo0zpVU7zTRe\nGqEB1FGwmc3MXzSWXlEviuNAtwMxmwL72LtmJAqzPZNgN34JfXxV/SIcd4Kv\nlIdSgEgU73QsYEjaxAyqhYFF5wingF1plkbtsj7dyShYhahc0eoRGfYp6RkL\n6g4ZC9y6ZZ55hiQCflZWqaZNhvwHy4z11YTXzx57SopRIv1aVsXVWJjQCOX7\nB4JgvvGDaQnIfziSmKpf+hju3c8Zng77dq3iyKmTCM/+hBqfbyMftQZ+K3mA\nQKLc\r\n=0mFk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "b5dca23e12a0a3dc9d2f706a24a6f569b23b2c40", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"error-ex": "^1.3.1", "@babel/code-frame": "^7.0.0", "lines-and-columns": "^1.1.6", "json-parse-better-errors": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "nyc": "^14.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_5.0.0_1562090541124_0.5214942991603031", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.1": {"name": "parse-json", "version": "5.0.1", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "7cfe35c1ccd641bce3981467e6c2ece61b3b3878", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-5.0.1.tgz", "fileCount": 4, "integrity": "sha512-ztoZ4/DYeXQq4E21v169sC8qWINGpcosGv9XhTDvg9/hWvx/zrFkc9BiWxR58OJLHGk28j5BL0SDLeV2WmFZlQ==", "signatures": [{"sig": "MEQCICU5ebBu4080GFoYU3gPAfXwWx0pIxkqT+MbPU2sokF3AiBubfB71ZhxT5klD3zCcVKfw3N2+2dA5KLzK4hFiqecLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGm8tCRA9TVsSAnZWagAAdBAP/i6CUu20W68dHolLFMt+\n4tmfiLldBS98qJEWXwvQ2moLRwE8FF1vofPKXcQxmguKEXomVdWImfp8kS73\nITHAgw+F6ANpFDl9EonU/0rlL8+m9MLXm7dAhriO2K3Yh0BLx6m0idp5G/k3\nhCBa5VJE5UWnAYFWTsnWpIm9h0C51damuwOvQwLA6cJSXBb5UUqOJFoUM1Zh\nUMqZzBGo8MR5oKHaW5jh1vz3317crpztyXroBxOUOmKklRha320/pujzdteh\n6MT9Hq9C7FYaebmlBt+QcAG9hdPtfBhbDWHsFxaY4k+mjLHloRZXfdWdutLs\nVN3yrmdKA8s5x59MhKhqLNPDfa5XxGGOMEqzIeHNdR8N3+D4Y7pUxwmF875P\nV9MGMlp4yCLAdbOErVSzneUOCfCb1Z2wNTmth2IwR7XZgUOVUS5hMnm1FTTi\nUfIBK9UsknNgn08Ic03DNEMt8ph5/iM2KlzsnAeHD3o9iOnPVzUnswYymFe8\n27DIfTaH4PQ82Dk0/1G3Rhdsu7aY3Xqiqrhbz1T/J8KN71XAgneNQ+GPbnUl\n3xLvFgZQm/vT7QU8XyjDWBNfmfAEQu2araPTEPx07RKJroORvBOYtCidq8OS\nSv2QmOoLFnjTAHM58ISS3LIF6TkzehvpfsdRjL+Gu1YgSUmhQc4K6CTywScb\njqWN\r\n=r0qd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2c8dd7e96ede24bde310bc0238678b776d3b0b90", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"error-ex": "^1.3.1", "@babel/code-frame": "^7.0.0", "lines-and-columns": "^1.1.6", "json-parse-better-errors": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "nyc": "^14.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_5.0.1_1595567916652_0.7594929598672941", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.1.0": {"name": "parse-json", "version": "5.1.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "f96088cdf24a8faa9aea9a009f2d9d942c999646", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-5.1.0.tgz", "fileCount": 4, "integrity": "sha512-+mi/lmVVNKFNVyLXV31ERiy2CY5E1/F6QtJFEzoChPRwwngMNXRDQ9GJ5WdE2Z2P4AujsOi0/+2qHID68KwfIQ==", "signatures": [{"sig": "MEUCIQDZ+enGgpgMAasUXAFH8jwFjiQ3atvw00Gq0fqZuE4xIQIgVLnFATrpMWTs3hixEOA5ihNYYuoLz17mIok6p0IrEtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQYNcCRA9TVsSAnZWagAAW8cQAI+SAcS2dX9tXnF7a2wn\n4tptE4MDmFrq1yxeHUYRmTy+v4DWs5Re/a6IfRYPP9KLZjAL+j2iC9vVcbvl\nAbLSL6KnQ+mlzAxv8NP/C25My0bDaMkNXjAfEPgVAqaS0ha7r3KFAi0z3YZF\n+F7S0q35gm6bJRVDVAsWLcOgeMUx6dOX/HipHspYSGv55X1xq9XMK9ajTdBR\ntDaWgHdVRFKbusFZ+cNWCj/tTu+hnDhG/rMIR6BRTRtAvFlzA05yE92AFIw1\nA3hsD07359h3HnVNeiIz3wgdaP1jcXmnETTE3UPMz/0LcmJdsWFgLMnb+ScZ\ndrDfZV/Sv+0mh1EyYYmm8oSjUiXBoajAlTXX4oeIR9TKcVTTBRf2k/kTVjl1\nVEd0XMdjWOLX88IaYcuWMGURYqyfQiCq0QKhZFr3wSvW2zSQ71EyqU7N0pC4\ni/m01lo9NspavgSBsEOkZmlWzbyA/NhEzjvtIcCidtBW39b/fXb8dgvDJuzt\nLIX18QfRAgtZYBpexVS6M4jAmY+CmKMF2fmMDs7lYpqFdfC+dYdVXNDk8NP+\ndDx9dC6oq+g5XZr2wynltH2NH4PrfCxVKzszaMDNguHYyfCNbnfascl3CUPQ\n9F+h+G4J+Bh57zLhsxcsA4lYgx6uGgAomYbv4mmGjkfudyuxzfVx+6zokGdL\nVJxG\r\n=byfQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0661a9c246bcec65407322126cf473dda91c0727", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"error-ex": "^1.3.1", "@babel/code-frame": "^7.0.0", "lines-and-columns": "^1.1.6", "json-parse-even-better-errors": "^2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "nyc": "^14.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_5.1.0_1598128987764_0.28417646929083196", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.2.0": {"name": "parse-json", "version": "5.2.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@5.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "c76fc66dee54231c962b22bcc8a72cf2f99753cd", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-5.2.0.tgz", "fileCount": 4, "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "signatures": [{"sig": "MEUCIG9iRz+gMBm4cmYIecAppGMWu2WQxmMggNlJFNZOaBWsAiEAtN0seGQXcIrZ0xlKuW+JAWmY65O9smSF96KC/EqCgYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBW3BCRA9TVsSAnZWagAAD10P/2QA+zzHXTVVFzUj/Snf\nnxRgubnkZXBq2ozvL0giFWH02VVWSDsyjqJ3/q1h0uNQxuUjWoul1+vBP+qf\nDr8/MeVPjvbk/ZEn0BqbuIDpFd2+l2fmRkBSQsNZ9aaxsY15kM/p/fD4APLv\nEoFZ0jx4j60KNko5YjQeqPh3BTdZdOCi0V1J6TESp9aDr5wjok5OjCFurhDP\n5IiEgncwHFLYt3tf/nl8omhxkixh3xZI9P+ovv+OcyybTte8OMJKs6DPe9xP\nBu6BsrYUnfGOqS87218iZgZX30ZL+utsYVhxNxnv9Rclwf7xWbDFvEZoANuW\nTXL3vM2S3fIqWAGY0e7DmRlsi25lqNY9N1J7fpkff1KqaigKEgoZ3holc6R/\nNBxdbu4UFRAMGbaQ/bFy0fkdtVDMbngNPNymqvsHI4sEPi53LsaJsScW/mGh\ngGbPjN0ImwsIcbwHmNK12DW28I5/JSkMD+YVAFbARN4YlIIthX0RUdnb+hgW\nCY74coPQi4XQmuedKcxsILuTIHryk+s8isYXIWnBIF/uE2gqdm55TuBMx6Us\nsrwYz52dUSB5go+fz8xYz035QQ6vQfO6haXTG5hdCprGs0PadJ8+fk5xeZ37\n0ha1i4+3FgeHUew/OGAYDSLHz241CLjc5lOo2os0RnuuF3VuyzBP87oQYrzg\nf1al\r\n=/qgU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b778e2eb368a0b935e0a999f6366e9edd95e4dc7", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"error-ex": "^1.3.1", "@babel/code-frame": "^7.0.0", "lines-and-columns": "^1.1.6", "json-parse-even-better-errors": "^2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "nyc": "^14.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_5.2.0_1610968513349_0.05302152846825847", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "parse-json", "version": "6.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "c8ee78ba5c5751047801b86808f45748a95a7bb8", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-ynQMB8AR6LdSrULxBydOHi3tTol76eE9m+04pFcCyJB3jrwhj9kR3uBHv5KhJhQuK3yS7HlcWXetAMJLoya/9w==", "signatures": [{"sig": "MEYCIQDkY9WfLPSNLLFACxk6zqTGBVTVwKA0uENlH61QZHUFjwIhAKKyQ5SUCUFsb2kYPF8M+/VWLVvzLnO3W/5jp0wcJffQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5504}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f3689b3ab3a0fdd96e5fea2da1c401fd209e370c", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"error-ex": "^1.3.2", "@babel/code-frame": "^7.16.0", "lines-and-columns": "^1.1.6", "json-parse-even-better-errors": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.4", "ava": "^3.15.0", "nyc": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_6.0.0_1636011093752_0.8174168094929426", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.1": {"name": "parse-json", "version": "6.0.1", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@6.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "30160ddf08d672801fecbc678b1e2222614da06c", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-28P5JlUVtxDdCrr/cmGcQWrJOD6+UUyvQ8OL16ceYdE2sS5V7HRbNwsGOfGPey34fpgcv9IVVhjNWp8P1kfUEQ==", "signatures": [{"sig": "MEQCIHE1ClfUzjBR0GvriVyQFJ9eOIj3244GqsowckazD72pAiBEBXxpGEmPW/wxYSUC69QQTtbqWHxRD1FdnDnjyXtR9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5443}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0b526f282e8ce915ed38803c5076c6739a113a87", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"error-ex": "^1.3.2", "@babel/code-frame": "^7.16.0", "lines-and-columns": "^1.1.6", "json-parse-even-better-errors": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.4", "ava": "^3.15.0", "nyc": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_6.0.1_1636944489097_0.6280738220215905", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.2": {"name": "parse-json", "version": "6.0.2", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@6.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "6bf79c201351cc12d5d66eba48d5a097c13dc200", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-6.0.2.tgz", "fileCount": 4, "integrity": "sha512-SA5aMiaIjXkAiBrW/yPgLgQAQg42f7K3ACO+2l/zOvtQBwX58DMUsFJXelW2fx3yMBmWOVkR6j1MGsdSbCA4UA==", "signatures": [{"sig": "MEYCIQDZ6E63xJy7iDEZQitsCGrBXqVipOzjbTam+fYZjore0wIhAPDnooBhvciKlTU3BSSDxqG6YT+eTFF+//5DNE1KTJaQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmeUjCRA9TVsSAnZWagAAJQsQAIg5IpxGuQXWze9l7v3e\nEf0sgcdSJ8jtv3csmId/T83bwMCrhEEufcdXy2Z335N/nQXdxanmXr0fZxia\nrIxfLe/qc0Q0eB5cW5eGILTnjPTItBbWZ+XczcY51ge9j9J7rwE4yRRzkOg1\ncJ9DgkCFDQdrN/vjgI+D1WFuThcPAnSeDax2OdCzzYheRijZdWoZstRcbG7y\nNJ2x2v3PsaVokbtVxH9OROg7jbSzGOLGIXC7NS6UwRsgNXpnT4iCTqYVcN8n\n/W/2afmeMMCKYuOYXu+w0NBTKLfxXeb8oSEBfSorBpjNRHvQ9arQ7qKXRkgW\nSgsV+ygyUnuZ1WbdH8YYBl6WWtTLKQvwjGdFTwrAh+y0yjEvKwaljNF5EhgH\nNoBoIAOQpG4t8Jqy1UqNAd5EFBQGBYWNVb5xztiTBotjUA+oDMJK10IVDCB9\nu0rtZ6nlSxcVGHbtvwHgC+B2J7DATCnCkt/gLmyFHK1saYq/4EcpXWqD16oU\nWMPLQulYuySTrIYsrsqbYJ4TGO2rW5B3T5qygw20DUe9jpwFC0ORyoQsaTGl\nphFpc+nj4bp8zLASITilNDADheyUJl3xJY/LwS15Si30f/aBI/QBBpuVNsHl\n95rY9ow1bz4QAq2Lqk+epXWYmRIRJwzPswDaGr014vda+47GxrAqll4f0sJv\n9w48\r\n=otS3\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c49a9c5e7835525cbbf2cf7ba973776bd37dbfe1", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "17.0.1", "dependencies": {"error-ex": "^1.3.2", "@babel/code-frame": "^7.16.0", "lines-and-columns": "^2.0.2", "json-parse-even-better-errors": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.4", "ava": "^3.15.0", "nyc": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_6.0.2_1637475619018_0.6693775715479393", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "parse-json", "version": "7.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "51c0713f233b804eb5adee3ef1e75d3243e0ff06", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-kP+TQYAzAiVnzOlWOe0diD6L35s9bJh0SCn95PIbZFKrOYuIRQsQkeWEYxzVDuHTt9V9YqvYCJ2Qo4z9wdfZPw==", "signatures": [{"sig": "MEUCIQC0SmfaZUpAiwPrNnqv23yS5FyqpPJIhK0weBrztfnprQIgTI26OrDqqBa8zV+9nB9EfX5NgT+vFCWqJ1FpubooblM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4dGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGdg//fHeXXGtK0nzG4WwwF07AV8EZx4HTLSt+zxtj0QZO1HyKFkDi\r\nYqOS1XeaansZi+gJ1BkFMcVevqJftu+0yurCTD4Zn74xJDP1Hn02QamuPiQO\r\nQiMZB18PpigO5LrDSr6OGLdbBRs8PL2aQDXgESukkpGPC8odZ6oCRFisAfPN\r\nQSoRbEWZZnjqV63RUGCTx3HwoGn1aMMUE3litr7wC3W1jVhYP8TW/r3jC5VY\r\n5fKRWXNzxr6/1EmbtRsSq1j4mGNnLxOVxSvGDUFDsmbMJk6T1ZjxoGRFXhJV\r\n7vvxU3mlUWoA/BWGISlIsSjrleFI3nYD2f1lkBHY+Wk4B8UjcopnQ3hLFFi0\r\n6p6RWebxyfSqSqBIKFI+fLh/4tqKIzUZdoShJZ758gvLe+LoMM3+ycrcS1jW\r\nTsf4V3BrLS9ga8QKK8r49Whgka13XJIrXS+TVgH/aiiDuB/5QZ0alkD5SthD\r\n9glvUivkMnLB0VfdZfH1imcjs3Aw7iNCnNrqfoT/jJFrWbwLrfaMmjB/7ETb\r\nq1oBhLfbCDuiwCJCf5DWTjGBq8R6AjYoXSuJ258BrvsE1CWYt5DaM0MdpPdK\r\nrrPc4dqH1gakfMZYqio9pfNJ/XuylCH2uWsWIyQHiJ5lfM1rjV+lg1w9OPZ4\r\n/QPgF3x2K8A6bQUWWUe5D29xmEn4c//7I/M=\r\n=c90Q\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "258f8312b24363ed1389e8db3f580e9048c70588", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"error-ex": "^1.3.2", "type-fest": "^3.8.0", "@babel/code-frame": "^7.21.4", "lines-and-columns": "^2.0.3", "json-parse-even-better-errors": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.0", "ava": "^5.2.0", "nyc": "^15.1.0", "tsd": "^0.28.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_7.0.0_1680836421770_0.7574724347968855", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.1.0": {"name": "parse-json", "version": "7.1.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@7.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "4cffd0ee00ffa597b995fd70a9811993c4f95023", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-7.1.0.tgz", "fileCount": 5, "integrity": "sha512-ihtdrgbqdONYD156Ap6qTcaGcGdkdAxodO1wLqQ/j7HP1u2sFYppINiq4jyC8F+Nm+4fVufylCV00QmkTHkSUg==", "signatures": [{"sig": "MEYCIQC12mpBJpet09sdj0M4n1/6YZkdqOI11MFMD7M7BC7VcwIhANEErM+nXlRyOcZY7gPMYEVYiUW0GEx/xnACc51rwOFd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8346}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "da94669530f225526461018517563c12fd7633e0", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"error-ex": "^1.3.2", "type-fest": "^3.8.0", "@babel/code-frame": "^7.21.4", "lines-and-columns": "^2.0.3", "json-parse-even-better-errors": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.0", "ava": "^5.2.0", "nyc": "^15.1.0", "tsd": "^0.28.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_7.1.0_1693550405228_0.7108466533759863", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.1.1": {"name": "parse-json", "version": "7.1.1", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@7.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "68f7e6f0edf88c54ab14c00eb700b753b14e2120", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-7.1.1.tgz", "fileCount": 5, "integrity": "sha512-SgOTCX/EZXtZxBE5eJ97P4yGM5n37BwRU+YMsH4vNzFqJV/oWFXXCmwFlgWUM4PrakybVOueJJ6pwHqSVhTFDw==", "signatures": [{"sig": "MEQCIHUdrh/PEYMfViYHFoP9mftyI3/FKo5efeZYjvV9bvBlAiBmIoZSA3Wufu5z72I8lU09Is6E0Vimr1lUPza7wOjt7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8671}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c26d16e382872ce567c2b8c52bfd82e392b64689", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"error-ex": "^1.3.2", "type-fest": "^3.8.0", "@babel/code-frame": "^7.21.4", "lines-and-columns": "^2.0.3", "json-parse-even-better-errors": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.0", "ava": "^5.2.0", "nyc": "^15.1.0", "tsd": "^0.28.1", "outdent": "^0.8.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_7.1.1_1698431533794_0.27783702697893164", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.0": {"name": "parse-json", "version": "8.0.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "ad386a5990556791736cab443042ca234cdfbba9", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-QtWnjHuun44MCLbq9f2rlcX9Bp9FSsPgQS9nuGcIm3J557b3/CvmYUhwChgJJDlMpuNN0sFRAogzQ8xMitD1oQ==", "signatures": [{"sig": "MEQCIF216qYYzQ4fNZZeEZkntJrEWFAE9mNZI0sK6DN1OzM9AiBbhmAL/DZSoAM58VEmTwyahukiR+jTwLi2oAuQDE8xvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9015}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7230975ba5ad27ad9fa40a2fa93f95786acafc8e", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"type-fest": "^4.6.0", "@babel/code-frame": "^7.22.13", "index-to-position": "^0.1.0", "json-parse-even-better-errors": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "nyc": "^15.1.0", "tsd": "^0.29.0", "outdent": "^0.8.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_8.0.0_1698952333962_0.2514557675845246", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.1": {"name": "parse-json", "version": "8.0.1", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@8.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "13920ceb04b9dc126268fc3abea89e4c7c6b0f86", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-8.0.1.tgz", "fileCount": 5, "integrity": "sha512-soKUg/q/8bcfuF3+plsbYldE74cVEVEPSC1BUPIGTaX1byXdz6Fo+CVYBdH0jj/5xWsFrNRksl11QkBgHqPQeQ==", "signatures": [{"sig": "MEQCIGAqNKlMdtkeEGDd3Y5soOft1zRh+kqRJp/m1kFUHzQMAiApgr/2k2BIwf/cd/4GSg1KcwhnVX8lvLZ+cYFh19uAcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9311}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "1927b6d08ead7bd5ff3912842d30bb2ca96aaf72", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "sideEffects": false, "_nodeVersion": "20.9.0", "dependencies": {"type-fest": "^4.7.1", "@babel/code-frame": "^7.22.13", "index-to-position": "^0.1.1", "json-parse-even-better-errors": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "nyc": "^15.1.0", "tsd": "^0.29.0", "outdent": "^0.8.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_8.0.1_1699527534615_0.31001972047475546", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.1.0": {"name": "parse-json", "version": "8.1.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@8.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "91cdc7728004e955af9cb734de5684733b24a717", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-8.1.0.tgz", "fileCount": 5, "integrity": "sha512-rum1bPifK5SSar35Z6EKZuYPJx85pkNaFrxBK3mwdfSJ1/WKbYrjoW/zTPSjRRamfmVX1ACBIdFAO0VRErW/EA==", "signatures": [{"sig": "MEUCIBfJQMMiDz0AVpeDLRQC/FcqAJ7vr1dp0bvnQKB6BgMfAiEAwoMOqZp45O2OAsmKgQwu56s/GisdZqRoylJXSr22/GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9522}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "5ad65169677db8f164a32dcd08a19bc5ae8426e3", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "sideEffects": false, "_nodeVersion": "18.18.2", "dependencies": {"type-fest": "^4.7.1", "@babel/code-frame": "^7.22.13", "index-to-position": "^0.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "nyc": "^15.1.0", "tsd": "^0.29.0", "outdent": "^0.8.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_8.1.0_1700666199422_0.8829135113944024", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.2.0": {"name": "parse-json", "version": "8.2.0", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse-json@8.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/parse-json#readme", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dist": {"shasum": "794a590dcf54588ec2282ce6065f15121fa348a0", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-8.2.0.tgz", "fileCount": 5, "integrity": "sha512-eONBZy4hm2AgxjNFd8a4nyDJnzUAH0g34xSQAwWEVGCjdZ4ZL7dKZBfq267GWP/JaS9zW62Xs2FeAdDvpHHJGQ==", "signatures": [{"sig": "MEQCIDazbj+P3sIZxgXp4MuxHD2aOHKSvCg6ti/qfSoetn8yAiAVAE8jUmiddf+080GuYsGSXe9+HngV5lgevjSib2aJSg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11195}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6a7bae72889ff172cb44d58223097abb67e9f8c2", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/parse-json.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Parse JSO<PERSON> with more helpful errors", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.1", "dependencies": {"type-fest": "^4.37.0", "@babel/code-frame": "^7.26.2", "index-to-position": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "xo": "^0.60.0", "ava": "^6.2.0", "tsd": "^0.31.2", "outdent": "^0.8.0", "strip-ansi": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/parse-json_8.2.0_1742543868476_0.09362295905957718", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "8.3.0": {"name": "parse-json", "version": "8.3.0", "description": "Parse JSO<PERSON> with more helpful errors", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parse-json.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && c8 ava && tsd"}, "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "dependencies": {"@babel/code-frame": "^7.26.2", "index-to-position": "^1.1.0", "type-fest": "^4.39.1"}, "devDependencies": {"ava": "^6.2.0", "c8": "^10.1.3", "outdent": "^0.8.0", "strip-ansi": "^7.1.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "_id": "parse-json@8.3.0", "gitHead": "6fee59751db59a539fdf53537101a1d7c6378a65", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "homepage": "https://github.com/sindresorhus/parse-json#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ybiGyvspI+fAoRQbIPRddCcSTV9/LsJbf0e/S85VLowVGzRmokfneg2kwVW/KU5rOXrPSbF1qAKPMgNTqqROQQ==", "shasum": "88a195a2157025139a2317a4f2f9252b61304ed5", "tarball": "https://mirrors.cloud.tencent.com/npm/parse-json/-/parse-json-8.3.0.tgz", "fileCount": 5, "unpackedSize": 10975, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC+Ca34Dlc/v6P2mtwoiQmrNw1TpX/lovEleCDHKxr92QIgK2lF7ou/TW3kT9r4bGxtBX3ywNYRcrZL8uh/aQKreWc="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/parse-json_8.3.0_1744189323129_0.7474471720361489"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2015-07-28T19:05:09.989Z", "modified": "2025-04-09T09:02:03.484Z", "1.0.0": "2015-07-28T19:05:09.989Z", "1.0.1": "2015-08-03T10:29:22.749Z", "2.0.0": "2015-08-25T14:14:38.170Z", "2.1.0": "2015-08-26T15:15:54.581Z", "2.2.0": "2015-08-31T16:36:30.005Z", "3.0.0": "2017-08-17T10:11:16.080Z", "4.0.0": "2017-11-04T07:53:50.125Z", "5.0.0": "2019-07-02T18:02:21.255Z", "5.0.1": "2020-07-24T05:18:36.819Z", "5.1.0": "2020-08-22T20:43:07.895Z", "5.2.0": "2021-01-18T11:15:13.459Z", "6.0.0": "2021-11-04T07:31:33.913Z", "6.0.1": "2021-11-15T02:48:09.224Z", "6.0.2": "2021-11-21T06:20:19.136Z", "7.0.0": "2023-04-07T03:00:21.988Z", "7.1.0": "2023-09-01T06:40:05.405Z", "7.1.1": "2023-10-27T18:32:13.983Z", "8.0.0": "2023-11-02T19:12:14.219Z", "8.0.1": "2023-11-09T10:58:54.795Z", "8.1.0": "2023-11-22T15:16:39.592Z", "8.2.0": "2025-03-21T07:57:48.662Z", "8.3.0": "2025-04-09T09:02:03.317Z"}, "users": {}, "dist-tags": {"latest": "8.3.0"}, "_rev": "6775-4ca7d113879929d2", "_id": "parse-json", "readme": "# parse-json\n\n> Parse JSON with more helpful errors\n\n## Install\n\n```sh\nnpm install parse-json\n```\n\n## Usage\n\n```js\nimport parseJson, {JSONError} from 'parse-json';\n\nconst json = '{\\n\\t\"foo\": true,\\n}';\n\n\nJSON.parse(json);\n/*\nSyntaxError: Expected double-quoted property name in JSON at position 16 (line 3 column 1)\n*/\n\n\nparseJson(json);\n/*\nJSONError: Expected double-quoted property name in JSON at position 16 (line 3 column 1)\n\n  1 | {\n  2 |   \"foo\": true,\n> 3 | }\n    | ^\n*/\n\n\nparseJson(json, 'foo.json');\n/*\nJSONError: Expected double-quoted property name in JSON at position 16 (line 3 column 1) in foo.json\n\n  1 | {\n  2 |   \"foo\": true,\n> 3 | }\n    | ^\n  fileName: 'foo.json',\n  [cause]: SyntaxError: Expected double-quoted property name in JSON at position 16 (line 3 column 1)\n      at JSON.parse (<anonymous>)\n      at ...\n*/\n\n\n// You can also add the filename at a later point\ntry {\n\tparseJson(json);\n} catch (error) {\n\tif (error instanceof JSONError) {\n\t\terror.fileName = 'foo.json';\n\t}\n\n\tthrow error;\n}\n/*\nJSONError: Expected double-quoted property name in JSON at position 16 (line 3 column 1) in foo.json\n\n  1 | {\n  2 |   \"foo\": true,\n> 3 | }\n    | ^\n\n  fileName: 'foo.json',\n  [cause]: SyntaxError: Expected double-quoted property name in JSON at position 16 (line 3 column 1)\n      at JSON.parse (<anonymous>)\n      at ...\n*/\n```\n\n## API\n\n### parseJson(string, reviver?, filename?)\n\nThrows a `JSONError` when there is a parsing error.\n\n#### string\n\nType: `string`\n\n#### reviver\n\nType: `Function`\n\nPrescribes how the value originally produced by parsing is transformed, before being returned. See [`JSON.parse` docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter\n) for more.\n\n#### filename\n\nType: `string`\n\nThe filename displayed in the error message.\n\n### JSONError\n\nExposed for `instanceof` checking.\n\n#### fileName\n\nType: `string`\n\nThe filename displayed in the error message.\n\n#### codeFrame\n\nType: `string`\n\nThe printable section of the JSON which produces the error.\n\n#### rawCodeFrame\n\nType: `string`\n\nThe raw version of `codeFrame` without colors.", "_attachments": {}}