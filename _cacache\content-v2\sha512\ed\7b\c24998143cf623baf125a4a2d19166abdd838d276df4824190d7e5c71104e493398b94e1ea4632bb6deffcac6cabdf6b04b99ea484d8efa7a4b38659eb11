{"name": "mustache", "versions": {"0.3.1-dev": {"name": "mustache", "version": "0.3.1-dev", "keywords": ["template"], "author": {"name": "http://mustache.github.com/"}, "_id": "mustache@0.3.1-dev", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}], "dist": {"shasum": "2576329dc46c16f3b02291c9f5a072ccc6e70324", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.3.1-dev.tgz", "integrity": "sha512-VrKOn6pi5TMfB+nqLeZdQ9X+p5bmDEccN8DK5HUQsxjlXskIgScjsaidO17MV2l2hK8Hw1rgoyzkefbsT93jzw==", "signatures": [{"sig": "MEQCIGuwnQP5GtxC1duptCGq4/cyqjVnrBl4UNc/ZuHmYZEuAiAhng8VMk7CB9JjFAuLaX+pLCRsGGwQJ9kHc6Y3nGkh0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache", "engines": {"node": "*"}, "_npmUser": {"name": "nathan", "email": "<EMAIL>"}, "_npmVersion": "0.2.7-2", "description": "{{ mustache }} in JavaScript — Logic-less templates.", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.4.0": {"name": "mustache", "version": "0.4.0", "keywords": ["template", "templates", "mustache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "mustache@0.4.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}], "dist": {"shasum": "49eb3bc60fd41119c50c87aa7067ef63d1592bdd", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.4.0.tgz", "integrity": "sha512-lOIx6XdcDjwR2JApJUO4LuQ7rN2oT41NSphYEBqFKZOa/M+mRKnHZgpa6wbCEFQ1GE0iNZLh7UV3xXc1+8Z+YQ==", "signatures": [{"sig": "MEYCIQDm7cKGmXtBL2NgIjKG6QtO3PMgtYI1wIMv36nZAh5YngIhAPVaGNYylYGxXpVpm6MFep/9bvRFXz6+mUGDDxDpS2BS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache", "engines": {"node": "*"}, "_npmUser": {"name": "nathan", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-beta-10", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "v0.6.7", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.5.1-dev": {"name": "mustache", "version": "0.5.1-dev", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.5.1-dev", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "d709fa12baaae22025e6a440ac9af5b3cb5a1f1a", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.5.1-dev.tgz", "integrity": "sha512-SftREtI1y1KxCysb6Lb26W1nVNZwu5HjBze7l63gmH5MG1LMViZoNUwGAc6hhjqN+go0zVO4CZt7l3OrT3l9GQ==", "signatures": [{"sig": "MEUCIQCB0maCxY/2Z9oDI0BxsrvKhH1pGnujIQChqzOOCmOn2gIgFcxzwNGRJHjCTfDU8jT3j/fX1lj4DbayN5jlJmKuPx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "engines": {"node": "*"}, "scripts": {"test": "vows --spec"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x", "uglify-js": "1.3.0"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.5.2": {"name": "mustache", "version": "0.5.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.5.2", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "f1f95a2eacc69318a1dc515da755c45e79a182a3", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.5.2.tgz", "integrity": "sha512-RqiqJMaKPmBXFaFNiccA80kBU+bAcEc3Vkb5knHr+mZIuYy7g3CCWIxrVVNG8/jpcavYeRrIj5g1eQMogvTuCw==", "signatures": [{"sig": "MEYCIQC4l4JB8gbKvdx6F5ZDKnHnpU+prfEVTLc1rCXON5DyhwIhALebTGnoon42p+sPavk2Z450wr3gJQzYK8Vxbru2gBsG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "engines": {"node": "*"}, "scripts": {"test": "vows --spec"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x", "uglify-js": "1.3.0"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.6.0": {"name": "mustache", "version": "0.6.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.6.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "08f1c9713e8fe277b02d878caa721da80dc569ab", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.6.0.tgz", "integrity": "sha512-1lv1NQ2aIM+1CRM2SXNz2YSUPswSRWnvv0P5r2CQAzxegtI9UQxyCmm6j8mVN+TTkKbNViO8No3VLv+zmChD8w==", "signatures": [{"sig": "MEUCIQDQ+kxr1OQJOvPjQOb6+yNripcHfnbCywwSH9GcQHfcdAIgWrF59KMybU8aQLpTNAvo/MJg3UORh9FEVPp9v9+dKgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.6.0/mustache.js"}, "engines": {"node": "*"}, "scripts": {"test": "vows --spec"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.7.0": {"name": "mustache", "version": "0.7.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.7.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "fca750fb3ebde6377f77498c71cf9fd23a502ee9", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.7.0.tgz", "integrity": "sha512-WjFqdjg49B80tJtZxjUPVqsz6B/vljh085KVkn+nYG9ma4KYwtCwf+toa1NvF7o9APDhRDdPJ0KWLYBcdNfuOw==", "signatures": [{"sig": "MEQCIGkLJHevlBsVNF9IpxNLeMvaF8CkX+TCJ+saVVzKcGRJAiA6f+fZNrz6tjPxXgp9FXo29Ea6HV01mdOUHNFNwuD8+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.0/mustache.js"}, "engines": {"node": "*"}, "scripts": {"test": "vows --spec"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.7.1": {"name": "mustache", "version": "0.7.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.7.1", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "2576a00c885f8fc6fcc34f46c953ffb39f65a793", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.7.1.tgz", "integrity": "sha512-FgUuxQib0vT5GaCukdUqkjNNfxoA/nSRAbAVDeVRxNP2ZJH3YII7efSAb3D8dm35vJ1mf/QNywyC2QSj2z5rPQ==", "signatures": [{"sig": "MEQCIHbbIHrfeqvxtfoGANx+eOD9Lsh3WlkVMwuqKHDGYtHtAiBsyUcJJ9of/gh2Dic6Nq3sVlLQhTgVCjbEON8p9co3YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.1/mustache.js"}, "scripts": {"test": "vows --spec"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"vows": "0.6.x"}, "contributors": []}, "0.7.2": {"name": "mustache", "version": "0.7.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.7.2", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "dist": {"shasum": "bae32cb81816488ab1ffd04777b1bb070e6d63eb", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.7.2.tgz", "integrity": "sha512-dW2GYLY1cRG/l/HrXiJ7ARItPcm2eKC1qhTmdZE2dTjhJ91RpyrDaWp98jU5QTqL4iORRK4r2K3u7ENKJ9ieXA==", "signatures": [{"sig": "MEUCIQDVEgEG/Wov/ZhThfN/DfxTipg9HdRtfxLLLV4iFG4//AIgaDWIPimC46Q/+sOs+ylRS/WRfOV8z5fBxnn1RWfi0kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.2/mustache.js"}, "scripts": {"test": "mocha test"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "1.5.0"}, "contributors": []}, "0.7.3": {"name": "mustache", "version": "0.7.3", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.7.3", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "dist": {"shasum": "248d68d0b1050397848fa634c2446c18f30490e9", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.7.3.tgz", "integrity": "sha512-Og/1AGzDp6b/9eYdgjn60HTQeIEKU8luLeYmLdsQDMTdhn385XH+IzcFy+zp9pmTHJt399KsIJ4MEtlNRLpOfw==", "signatures": [{"sig": "MEUCIQDzlQKbYM7eaG80x2RnSnGDUSAKVuC1kzaxEoeMMDxVkAIgOtttG/ED9ybnGFXZy5+xr6U78HcNYQ5G/o8H9uPgD3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.3/mustache.js"}, "_from": ".", "scripts": {"test": "mocha test"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "1.5.0"}, "contributors": []}, "0.8.0": {"name": "mustache", "version": "0.8.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.8.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "dist": {"shasum": "429aeab0eacb29170e0d3146f33d8bb8c188ac76", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.8.0.tgz", "integrity": "sha512-mONTOMBzZCrHGP/NgtohZLcYRzTbDRy4+6pH+gbtMuU+9O8RbDQQlrO9g1u4GH7Aj6EOtqf3ixTj2OrE3OBy5Q==", "signatures": [{"sig": "MEQCIDi/w0lEkYbw4//8KgkRyUTZtbXHMNdZkOt5m2Mev9PMAiAHVLBVKyEqvkCDgEtbLw4/UbZwqhi+PAA2pS3y9yqQKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.3/mustache.js"}, "_from": ".", "scripts": {"test": "mocha test"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "1.5.0"}, "contributors": []}, "0.8.1": {"name": "mustache", "version": "0.8.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.8.1", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "dist": {"shasum": "deba29cc9274cf98289877e0c625663882398ee7", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.8.1.tgz", "integrity": "sha512-sIpXQJnbT7IjXfLcV9fZoptCoJj2OsZZWj3mxM+wUB9EJ5n10cdnubPyturDnH/7Bns5QW2zZdOa2lQCIIS+4w==", "signatures": [{"sig": "MEUCIGUXbS1vT1n99Ku76T2YcaCq5wBZXflxEvp5rbZvXtbzAiEAuiLhfJI0DtaCb7v3l1KXjjrjmDIVh7iSQfr4CCgkXMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.3/mustache.js"}, "_from": ".", "scripts": {"test": "mocha test"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "1.5.0"}, "contributors": []}, "0.8.2": {"name": "mustache", "version": "0.8.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@0.8.2", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "dist": {"shasum": "bf5b922b8f40cdcfb91c035dcd916110d1621f9b", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-0.8.2.tgz", "integrity": "sha512-aP5cS3jJRtVwpVX2oBysncQDpR9S9S/fhc4dAGlSFVS0DCg1SQl2zzgQd+xz+rkQfV3GP64ma49woMOJe4bteA==", "signatures": [{"sig": "MEUCIQClorqWkMzh0okgsF47rYKmlmyi/0QBSZG1HsSLib2gXQIgdZHhlaZaVASFWrMJIZKqCtc3vu2TlqTJaiSdOV8J0Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/0.7.3/mustache.js"}, "_from": ".", "scripts": {"test": "mocha test"}, "_npmUser": {"name": "mjackson", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "1.5.0"}, "contributors": []}, "1.0.0": {"name": "mustache", "version": "1.0.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@1.0.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "8f5b8f68041dbead10997e0ba1d024771a03e15a", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-1.0.0.tgz", "integrity": "sha512-o6P2F8qnkAn/I9af11MkHIxxn22iV+XqFQSCuezc6590Pkf6sketkymLqzHaYDn5NSbYENFNdnDRbJsgKgdx6A==", "signatures": [{"sig": "MEQCIAGW73VjR72XDuSoW5565V7g2i7iVDm3OMHlvmqSf2PEAiB4jzplztUEgcN97BEVJQJPP4hldhXC2iOJTYOo4DEE0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "_shasum": "8f5b8f68041dbead10997e0ba1d024771a03e15a", "gitHead": "2ba7481d41e356869dc2db695ccc4a0cf3ce38cd", "scripts": {"test": "mocha --reporter spec test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "2.0.1"}, "contributors": []}, "1.1.0": {"name": "mustache", "version": "1.1.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@1.1.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "0d9a554b4b3f9e248e6c2742c2579d618d2ff00f", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-1.1.0.tgz", "integrity": "sha512-G0DVdrHs22u4t44VjPcweHkpR5fgr9ATJ3mi5R7F5Kxr/LK4RBpqJwPuVWaSy85+9twp1KBw+7u43PQoITBSXg==", "signatures": [{"sig": "MEUCIFdUnMWogtOfwmrZskcPYWQBGYt34bimNLwKLfa0qRsYAiEA+bUZry7VAnRA2NbDMgCbfXPvFXZjpzk8m8Fi55dIwt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "_shasum": "0d9a554b4b3f9e248e6c2742c2579d618d2ff00f", "engines": {"npm": ">=1.4.0"}, "gitHead": "d4ba5a19d4d04b139bbf7840fe342bb43930aee3", "scripts": {"test": "mocha --reporter spec", "pretest": "jshint mustache.js", "test-render": "mocha  --reporter spec test/render-test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "devDependencies": {"mocha": "~2.1.0", "jshint": "~2.4.4"}, "contributors": []}, "1.2.0": {"name": "mustache", "version": "1.2.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@1.2.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "6cdec1cb03798792a948f8cc53ad69da0d0dd5c8", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-1.2.0.tgz", "integrity": "sha512-ikOM6Z34Ww+A/uSReogEu4/c+jyJo/imVKe0P7+mApvlcSSRLJSH+W28W9OmKoivOs8F+WGnk2anlm4QzP8maA==", "signatures": [{"sig": "MEQCIGoYRwOj6gDgcGHgmfifD3LGcdxS/ZltI1YtaT/8KN6pAiB9YG1TybiE8Xq5DgDpmllkIbE/23Gm6cN0yjG6lF3D9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "6cdec1cb03798792a948f8cc53ad69da0d0dd5c8", "engines": {"npm": ">=1.4.0"}, "gitHead": "85b940801c3e511a2520b058849001f7a8584948", "scripts": {"test": "mocha --reporter spec", "pretest": "jshint mustache.js", "test-render": "mocha  --reporter spec test/render-test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"mocha": "~2.1.0", "jshint": "~2.4.4"}, "contributors": []}, "2.0.0": {"name": "mustache", "version": "2.0.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "_id": "mustache@2.0.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "b86a7157cc85c6e18c03afef8a671f03069890f3", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.0.0.tgz", "integrity": "sha512-njycTVgYdpmS8gRiSVJX3QI2/YWthhM1V3FYBuXjqKDCg4ivDSNTvTA+y1TNWMWhG/3DQnCRJq4VCe+dGqGcww==", "signatures": [{"sig": "MEYCIQClkyXcDuIzQpkwsXSLRny0N8Hi41hQrTBiw4q8dK76FwIhAJjPLLd45L9vGYibRfOaCC7rIvX7gzkvVgRZwsnJkNzn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "b86a7157cc85c6e18c03afef8a671f03069890f3", "engines": {"npm": ">=1.4.0"}, "gitHead": "c52df34ee05ed9b0ffac945eb86e814c84b29b07", "scripts": {"test": "mocha --reporter spec", "pretest": "jshint mustache.js", "test-render": "mocha  --reporter spec test/render-test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"mocha": "~2.1.0", "jshint": "~2.4.4"}, "contributors": []}, "2.1.0": {"name": "mustache", "version": "2.1.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.1.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js#readme", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "00ae3d7757c68e4c997485c6728947e77f354b26", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.1.0.tgz", "integrity": "sha512-UmUbzXmM9eqOV4Vig6tWOd+6YgOOT6pSr4UPG9VMzJ4P99qe4IskV1jU0zTCB0pZUHyTw7HxDr8kq35P8P6iNQ==", "signatures": [{"sig": "MEYCIQCVKaXdNzovkBRK4LqayT5q3/RPZMfuXNpXTBwTv+ndbAIhALktZvoCcpLam1Gnd5KEaZbbEVOPEvJAb+pOUH3cV3+u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "00ae3d7757c68e4c997485c6728947e77f354b26", "engines": {"npm": ">=1.4.0"}, "gitHead": "b479bb8fb8c2b5b26fe9479f826f82fa7d768c4e", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"chai": "^2.3.0", "zuul": "^2.1.1", "mocha": "^2.1.0", "eslint": "^0.20.0"}, "contributors": []}, "2.1.1": {"name": "mustache", "version": "2.1.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.1.1", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js#readme", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "cc2bfbf698654b8c7130c72608f013d17804614c", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.1.1.tgz", "integrity": "sha512-yYNE6qHoN4iPn7XPe6y1QqYLrTuj+YDd4jgb8fhPsZfEQt58ERpFi/FWgTc9jX4QD+pWPa99yYBBeOdwy/R8UQ==", "signatures": [{"sig": "MEQCIBZpg+2SNpQmNiaW4boeqKhT2OFEYI3yBtnSWrvCIkY2AiAh/3HtemlWFT0Pqu+UslJlRkNwz45zzeZlwZAZ2LCplg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "cc2bfbf698654b8c7130c72608f013d17804614c", "engines": {"npm": ">=1.4.0"}, "gitHead": "aef96817ae53c44db248ddae530a0c7a18786e9b", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"chai": "^2.3.0", "zuul": "^2.1.1", "mocha": "^2.1.0", "eslint": "^0.20.0"}, "contributors": []}, "2.1.2": {"name": "mustache", "version": "2.1.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.1.2", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "aff2960bc59720e4db7f6a7a758cae39994aab77", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.1.2.tgz", "integrity": "sha512-NrpkDvEukkdTi1jOcDTWEKS0fmPqqnnC0nW0bDp4VDoYDptdtYBdohg8+tOwDrmK/YBJARAkxSGdwsTs/9cMVQ==", "signatures": [{"sig": "MEQCIA35zXc9ZuCUs5OhMC32fNhtyK5Ew23WXsbl13M+wmMIAiA7DuARCPlaGGV2UvE+g55eDUkG8ALBZhBVHawfbk58gA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "aff2960bc59720e4db7f6a7a758cae39994aab77", "engines": {"npm": ">=1.4.0"}, "gitHead": "55c1370a11a34403e804903f713ff76a0c549a40", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"chai": "^2.3.0", "zuul": "^2.1.1", "mocha": "^2.1.0", "eslint": "^0.20.0"}, "contributors": []}, "2.1.3": {"name": "mustache", "version": "2.1.3", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.1.3", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "25b90b4204a454c898e8bb2e38d26de223abbd56", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.1.3.tgz", "integrity": "sha512-rJ7+0QdnoKy464CizHFlwjDULImCh82jWVWQOtPX1c0iTHh7flde4FOIPl1M+M7skvNQIcuUMBkDPIz2G1A7aQ==", "signatures": [{"sig": "MEQCIG9YGoLjBV5Hsup/zMNrONyTEHWp5CUsEnduMCxADV5vAiBb53FcVeWUzsh9nh/Th7KTTjWUloUuI/xWB1ta6TdV+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "25b90b4204a454c898e8bb2e38d26de223abbd56", "engines": {"npm": ">=1.4.0"}, "gitHead": "20c089392ef1c23b47a25b62f3ff99843d140f22", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"chai": "^2.3.0", "zuul": "^2.1.1", "mocha": "^2.1.0", "eslint": "^0.20.0"}, "contributors": []}, "2.2.0": {"name": "mustache", "version": "2.2.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.2.0", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "d29813b499b8067712b503347eac342c616727c7", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.2.0.tgz", "integrity": "sha512-cImOaHqbrTcHBQTIQtCHezFqn2hoPs0th1Nx3Sp9+YFnXCGI9lD6xtyjk7+1jmZ/bla3GCzVTz8Sf7FHslCIuA==", "signatures": [{"sig": "MEUCIFhih9i48XMoMMiMwh8DMUYCzRWfc6/Fusn7MfX3WTYuAiEAmQuBOp3AkJzOsrYnxbpIw00cdrD1J1QS3/3sxvA4kk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "d29813b499b8067712b503347eac342c616727c7", "engines": {"npm": ">=1.4.0"}, "gitHead": "aac23a542c6cb7cd17a8b995fdc0406486cd3a23", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"chai": "3.3.0", "zuul": "^2.1.1", "mocha": "^2.1.0", "eslint": "1.6.0"}, "contributors": []}, "2.2.1": {"name": "mustache", "version": "2.2.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.2.1", "maintainers": [{"name": "nathan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "2c40ca21c278f53150682bcf9090e41a3339b876", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.2.1.tgz", "integrity": "sha512-azYRexmi9y6h2lk2JqfBLh1htlDMjKYyEYOkxoGKa0FRdr5aY4f5q8bH4JIecM181DtUEYLSz8PcRO46mgzMNQ==", "signatures": [{"sig": "MEUCIQD1Q0gHTi9J7ar6QAx7LStH1p5ULP/cVuif71Uq2ix44wIgVMgYt7DcfuBf9ZvviFzisLyO5ZkyEXhvZh4nBg8Pumg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "2c40ca21c278f53150682bcf9090e41a3339b876", "engines": {"npm": ">=1.4.0"}, "gitHead": "cd06b22dabdaeffe3e4c74ee02bd492a11bbb740", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "2.14.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"chai": "^3.4.0", "zuul": "^3.7.0", "mocha": "^2.1.0", "eslint": "^1.7.3"}, "contributors": []}, "2.3.0": {"name": "mustache", "version": "2.3.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "4028f7778b17708a489930a6e52ac3bca0da41d0", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.3.0.tgz", "integrity": "sha512-IgZ/cCHtDG1ft0vdDV9wrlNz20SvbUu2ECoDF6dhk2ZtedLNy1Kehy4oFlzmHPxcUQmVZuXYS2j+d0NkaEjTXQ==", "signatures": [{"sig": "MEUCIQClr1tM3JcRY8hyk47s9ehnUSg1F3xbB7zKb2ZoM7l1EAIgKiCfHVoMAfgi86Xm1RM8ZbJ70n+SwCDvmWe/vYThGkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "_from": ".", "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "_shasum": "4028f7778b17708a489930a6e52ac3bca0da41d0", "engines": {"npm": ">=1.4.0"}, "gitHead": "23beb3a8805c9a857e3ea777431481599fab503e", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "7.0.0", "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache-2.3.0.tgz_1478622318106_0.5297125231008977", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "2.3.1": {"name": "mustache", "version": "2.3.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "ef5db3c0d11f1640e9baa47f4e65ba0c3fcd82b9", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.3.1.tgz", "fileCount": 17, "integrity": "sha512-20dW38oeiTzauvbxs1YxQbr3gbu/Lfo15J4V0EqbspYnn/GwSeTSDNtESy2nak28BW0k8qp7dnrFhrsejLPUtw==", "signatures": [{"sig": "MEUCIQD2QLAHPwdW7jyJsSmFDI3HthLhknvMurSJfor5Dcf57AIgLM0K9lc3ixvOsQHjk4XjuADPYNSbuR11aPVtSQeRAhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaeBECRA9TVsSAnZWagAAwQkP/jKC2B2W24CnNsSePh3L\nQuKJiG6esN/z8On7XA0Kdr3jeYhVq5//8PCxgezQL1uIWVLbgUZE3S7fnAqm\n+HJq3Bo0K0ndKFuuWJBOIX+IsP45R6zo4Q8E3F+Crqu1ELz5qOGV+jhq1fEa\nedanU2+ZKN9Eb3E6eHwd0e5hgt+/0u7txjvx84lTIbT4lPkPLEFOUh+WM9p0\nYoxZC4p/G2+biYPZVUH4eJ5lAxdSLhvZoY67bGA/8EIbGO59rceaIeSv7l8A\nwitrqIxiEQEEx17ZdhtwMJJblB8P4y0OrN+SsFepelqDSksECAR9+x0+Oj/9\nSzlAwcI6dNAsH+ijguH2+eIGNoCFi6zIcUhTH5fzJpeQaBeOL26/hTjpS9Sq\nUrjisu0ui+AHcscxicfZjspEkNkkk4Oem8khpS8CXc+6yt9nSe6DPzOEtyID\nYwqrsW8wiNsc3GjjOmoYe8DkbpLc18Tu3rYb4OCsAdaRZZvAYDHwTZ0E29/P\nymEd23PbX1cufSCIKWrKUvu5mUC4NNIT145fEKB8amUa9gP5oeNoY5wG/P02\nQgELNttj+pi+5hKQ5751OsNiFtPcBckcGehFS5X1N4NfexbjmVSjh5X7Jrr5\nD2WJs+ubZydshUnpCncsFAsr1iyBCgP1/OPjwCkoUoQGVhR+tn7IzNbgGX6q\nBEHC\r\n=6TRQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "engines": {"npm": ">=1.4.0"}, "gitHead": "9e8035ddd2e4ea9b177614660bde86018f027be8", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_2.3.1_1533665348768_0.4074625022246561", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.3.2": {"name": "mustache", "version": "2.3.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@2.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "a6d4d9c3f91d13359ab889a812954f9230a3d0c5", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-2.3.2.tgz", "fileCount": 17, "integrity": "sha512-KpMNwdQsYz3O/SBS1qJ/o3sqUJ5wSb8gb0pul8CO0S56b9Y2ALm8zCfsjPXsqGFfoNBkDwZuZIAjhsZI03gYVQ==", "signatures": [{"sig": "MEYCIQDrwJ3UeHtCHlnRnFBjwWljUEJcmMFyLOAzAfyyrs0c5QIhAJp2oL7VNiMpomiiIAJeut3pZ/cBrPIJsvoaEQS2ZrN3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdr1KCRA9TVsSAnZWagAAVdcP/j1zBH5EG0i2cWriTb11\nSiOGdbB3wsln3CgMWisn2d5LbV7PzGTwDkbUdUeKwJGTNqJJiiV+KGoNwwqA\ngmbwvt88LsWLg27VwaSH0kO8/wX5QIa5WwDcrFOcWXB2a0AGhvqoMVdsAeaT\njX44TYDc4ROCVE4aIIkkrU4K6c++ll/WVlL+yb6xsB5OY56waXtw2s3uCQNZ\nUwzvJ8mw+A0U/tCYS8dIfSuqsOceY/makJSk/YWs5FIlcWdIdh+62elQ3uSO\nHDuFzikqnsQB3610ws2MEy4xeFzvilaaXdtVivvw0Ars/z5/bx9rAeu4Wmqh\n6JgYVg412WWCug9Qs3Yj7AHDP4WMOvgG/ilZ/gOSFDaiyAMwvwK1+Xra+vUY\noCZ8v9cwhmay561EW5rH2Abh+dAZtsqD4xMXGmnclF36fB/aZY8zgJg4jzl5\nrkW8DK7hAVQ9PKYuOT0jPbRRMBTEZ7NVysvt+jk/4x0EfbghDQvKKRCsVC4O\n1XpZUJ2VD3GsKQZKZJTVVj2BIbChbS8RplCmNRpX1bCeaQSvjph+w95UPUp+\n7Mbq1Dm3z2Ls7OW1xKNdz4Cyz0BPmOyfyl6YyRbctgnfiUhaXaYAHi1qgO8G\nmGhvwu5r5S2pHJKwJy7F6ocw4wfj4F5ZvziiCwWFVBS9GmH2XIXyhrSIHQO0\nHSQ+\r\n=G/8e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "files": ["mustache.js", "mustache.min.js", "bin", "wrappers", "LICENSE"], "engines": {"npm": ">=1.4.0"}, "gitHead": "49714ba8ae0035ee5461eac42ba5f53ff5c0f86a", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_2.3.2_1534508361710_0.6629104457769521", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "mustache", "version": "3.0.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "3de22dd9ba38152f7355399a953dd4528c403338", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.0.0.tgz", "fileCount": 17, "integrity": "sha512-bhBDkK/PioIbtQzRIbGUGypvc3MC4c389QnJt8KDIEJ666OidRPoXAQAHPivikfS3JkMEaWoPvcDL7YrQxtSwg==", "signatures": [{"sig": "MEUCIQCv4wprAizUB333wBBkssu5p+j+17bTH0qx4Um3Zo7/mAIgbGyTixosGfPCHJ3ztQAsumA1dqCpre/hDq+WA7mqFnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbnsADCRA9TVsSAnZWagAASD4P/1J11huU95SWcC3Z3a0S\nK8DfTHC2wwr0AyPQgGU3rp48e0YZf15ovjVhg9kz7LthgGTtC8Y/+NXXvqOK\n6SmzuZDit2m0trf1dd8SdcI2c2bhE5+EUK/NxcZ0sJloDajNXsCbwUxsh6qG\nedjBEQu5MC8kalHNqlEMZIwOYM+qs60aQq+q3B3QOOQuIm8FsRo+9GvVo9YC\nltYQjqkc72x2+W5IhQgkZyRnJEhbV0NymjFyymsLPH/NRc7bbdtubIPUP67I\nkvXddKGwiQxHLfx/enwx15IHPmtqbu4Mps+kuSO/pRgMxmeMzgCUYurBnh3L\nzWUZdvPj6lT1acYBZZQng1/oxHZ6Vo3OJOqKn7KMKTs/9mA5g0JnHR8G1RN/\n7F3500u5vOIjmIsknBdLOJ7Fx8sPyFifb0w5DOVyoQiZlE4nSKDH17DvUTgq\nlL8BMlzmSlV0xPheCRJnd1FAZw4ozX8c0qvk5IMw4/EqPYGEduRBMFf3rY1F\nVATxN4y1kd1Jb9WU4Fp8+tTb4ag0STsJKNGw+BQ2aAZ27UEZjNK+xaa8+VQp\nwcsIGtPlvjOT6bFg4BLd7pP6Jgx0FxK408HvXbUonxaNVPE7StFkFyvJPnMC\nTy3y8yTiEkWm1ZKWOU4LOLn5mqAqR1IHPc+ilhvabuBPKletGLakgR5KM5iG\ngiO4\r\n=89wt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "17510f07d12ed3fb09866fec8226cf34cff90339", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "8.12.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.0.0_1537130498756_0.8487409639182437", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.1": {"name": "mustache", "version": "3.0.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "873855f23aa8a95b150fb96d9836edbc5a1d248a", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.0.1.tgz", "fileCount": 17, "integrity": "sha512-jFI/4UVRsRYdUbuDTKT7KzfOp7FiD5WzYmmwNwXyUVypC0xjoTL78Fqc0jHUPIvvGD+6DQSPHIt1NE7D1ArsqA==", "signatures": [{"sig": "MEYCIQDQ42iTLv4dlo6Bw0h23yLbl+E8ZwRnKQsN1QegrbE5rQIhAMRE6dGXS8R1Yk+97VnaT0ENmmfhrKJ+bz/sK8Mxi0+k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6J6ACRA9TVsSAnZWagAAEO8P/3a57ni3sBUWa316HZT4\nOajxiFqLLWaN11OWCHQolaOyNflh7aGdvUQWYI61vlgFeQ2avfN14fbG84xr\nggwsyMaRvnmrRKB4F0eOhp7t8CTxEqEMy+n30G/6ZPxgLC0imautJHdkshyl\nvOcQNq6xyi7EhurKXgEKSI4pEg2humg2JRcONb/z7eohP1MheJSbD6Ck59ft\nJy6se60A8qGORtTq/E2wMOt3Uqn5uEWuHUrKwduoDebvZsONzO1KhWgXPlU1\nt13PCIU2LO+0FrbZcHZGzoMG2btto5ElzaVhipoPaUNU1Oup68CH2Rp+mpCv\nHt+t5GhOc/UwPWDBryLt92y12twMY6PFi2IMdwDTZTY77/W6810ptUqPu4oC\nQeVg+xKCpc+fZYvpIpzPKJ/Rqx5OrXhGbDlc270ZGkk1rxtvp53JZjy2AdUo\nibh/1HnKpBBgk5SymSlx36KZrPOOJh7EbIxd2WAUx1vRZCD74S9XR7Cb698l\n64fibO3diB3LSMKelvH+zHzsqzwkkq3zO42AhxjTjv7maV5M590zmdnZ4u6d\nuZIuFMNKQsQFRkPNcT0j2rusrPfZCRy8xXrJaqfjELUV8FVNPUZG0ZspKPAh\nrQRgsNYFUzf2DxuGb0RSFrCimwKDtzId2zlG7rr0xI4yQM2SRc4ruu8rX/g/\n425t\r\n=OuJG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "38b1448e65f1d4716c3e3ad792ae6ab3aaf487ab", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.9.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.0.1_1541971583996_0.16125326264588447", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.2": {"name": "mustache", "version": "3.0.2", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "35bb886a1e5baad0a8f192258fe9a904f711868c", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.0.2.tgz", "fileCount": 17, "integrity": "sha512-64neoEgmozb8e/ecGBOSE+RfnevLSFzCI0UKPcrWmjv953/8fXhYO9+EQFtfbi6hwoFxcTA+Fp5mRiOiI9eTuA==", "signatures": [{"sig": "MEYCIQD66yBffLGHYQGXc2BUFz9Ov9B2+OLFfpcHmIp4jBtFYAIhAM9++rYzlUKuxSvZx2XCqzj3rq0SYcEyphSS2W99cFNw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXaZ+CRA9TVsSAnZWagAAXYsQAKOzxC3AmC30VwbE2A3P\nHZSyD7DEJY8pysGTqjCxEMSaEG/skfrBS2OhFyGOaqnq8bh694hi3fTwYG0M\nZz/MA/dMcOsmsb4/YsLyH/Pr91ZUk5Z1P2BHO7VEzQwbYIx/6Zr4xv84erf9\nwaUk7GHEReTobobrYUke8/1O468J0wXO8H+fWe11gSo/xh97/5RkrApxbnul\nI1eafjkM4uzaaaoYRqjDRLR/rwfLYEn+PO8muuIXvu+T4IZldZ6wGkHfNp/r\n+uGXY+WE1017sWMXLXNqZNYCsEibu8UdUD9+kwOSqpTjS86Fyfc3nBmRAt1J\nM9HdF81SIcuFFcjWZUd6cNkDanVUm/igwFPdJLWcqprA1zduIJxtxH9flIMF\n0Wl17+6chONylPyjLHk17n4iZKnlauNaNGKB9ItfrYiaL2nPsNhEw1JosDYT\niD6Gwk/3KttZehQrPB2UzKDWia9EoLgNxqtO3mz9i6ao/9YJi1AC8+Y7G8ey\nfoPYwUptTEkIB99PgXET3ZGOQSiByKa7CG9uoqHsUDTBWzMMJqmPymhuPIf9\n4kN6PzyF0ygCP7fPlbX2gChNDhosyPA1f1mw7W2LXpQPrYlJ3G0b8yksyfoJ\nFpups48nLe5AVG4ydkYRajY0FR6iNGRHZi1XCpPTSYTveFLfB+IilinJ2SAd\nJWuw\r\n=axzt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "6c3608bfb9fa74684cd9e22f5bb4c097f87484ef", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache test/**/*.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.0.2_1566418557391_0.6264722547909329", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.3": {"name": "mustache", "version": "3.0.3", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "ee4fb971887fa6cc1b6b6d219a74b5e3c7535f32", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.0.3.tgz", "fileCount": 17, "integrity": "sha512-vM5FkMHamTYmVYeAujypihuPrJQDtaUIlKeeVb1AMJ73OZLtWiF7GprqrjxD0gJWT53W9JfqXxf97nXQjMQkqA==", "signatures": [{"sig": "MEUCIF5UPySP9hwMfFcMOPaer2UMU04CeDAhRICob97BU0p6AiEAjU1w/PQq9LOwvUrRc/b1nbkCV0lHlVlTp0dRvmMLhp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZO/SCRA9TVsSAnZWagAAbvoP/j+DNTIxOkXPV877jOqh\nj6hbcjku+QVk40FxUKc9RbKS23zJ2Wdv6n4wWHhSbidbWZqtPrF0XPwLKdUQ\nmqe9TmSy6Zk7NyImpRyRLZ7g+YuK32liZWiMUs4jVBwHCOYS5DVngsYOM9Sc\nZJks1Pj/2iemY8KePmeoPwGcEGhXjuIu9gncSDMUVF1Srisfv2M5BvCyaVmC\nv/81ggFv+/cHRt3s34ca4yW4Qo0jqCbriWKG8S2aGbmFlLr2m2HFwar5FhJk\nDSb507NMNnPjPau/aoEgiW0FVHuQ/VmZkOcG6M0GUWXYo38bG9KoVGPexnzj\n//D0rNJZa2oy0HIAFS+0Z1xw7GkZsi1u5WJmBHVyCSVaxpZvlTj8Ll5sqdIr\n3FEPlfiKp1lOUIvNqSpBdEsR3HCGbdlIwmDmFuoT7gQYPL0p9JVbKzOsuYwr\nc/+tSAKJTXWLJPfBxLN3awO1MRYgKPczy6PHKUFSO6lGtzgLkCWoRujntVTZ\nFrfmQSTIg35Np1eTzA/Hjm5lYbl2rPtE6nx2uHFoBubBMUuSw3GadVj2+ooU\n/Cfn+uR8feBg7AP4HhZpm0J5+exhu41vdJzk03iJsgUXxoNZw0CjE3GZMmV4\nMGPFE/pnIpOnyYt/aYonmsnEoLGC6WhWlv/aS7w+Ayhz7voPDVzkh4pT3nue\ncaZO\r\n=va27\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "96cb5ef372becfbf6beea1998bbe41f8eac75000", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache test/**/*.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.0.3_1566896081872_0.9651318791665673", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "mustache", "version": "3.1.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "spm": {"main": "mustache.js", "ignore": ["test", "wrappers"]}, "dist": {"shasum": "9fba26e7aefc5709f07ff585abb7e0abced6c372", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.1.0.tgz", "fileCount": 17, "integrity": "sha512-3Bxq1R5LBZp7fbFPZzFe5WN4s0q3+gxZaZuZVY+QctYJiCiVgXHOTIC0/HgZuOPFt/6BQcx5u0H2CUOxT/RoGQ==", "signatures": [{"sig": "MEQCIFqeeiOKiP/I89WCFDYKoYSg3mxAyBCblEFQhTbyiBnrAiAWyFqEFk4iEv1BDRY8pHpPW1Q86t7Slb1igQ/0y7+v4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde3syCRA9TVsSAnZWagAABuMP/j6xapMOKcp0I3Lq+Jtm\n2Z8gWAFbkt/EY6hFrn6HAWpPb5puTOHjUc3/4hPSbBfNX5gkxEMjrmboG/h0\nd73kmQjy9EBhylq49eJXiK5crKZ1kG64G4ZBexeFPa4PMsr8itmfosAr9O5v\n4IYGyaIJaUMVyw9K8uiO8UmxQrc645vfEFZfwUDYz+D2Jz/0B2Vt48fSt08Y\nJer7GozNWQ43WV3X8PNNM4mDWLyusxSlF3QWpZFFfsV+V11+6Y2OGnJT77tV\nExg/nPl0Ay+9f4Y86UDzPAa+hx4oRxB/OD3uHM8Zkc9kX2RRXtq2iudhYhSa\nTBkNVGbxZrkm86U0a1QBRx1/gYU4IKvR+3yWdoP+Qm42owzj1uAR4fOaAfoC\nNGzzSD2R6vGksjggaBCV6kmioCL+Y7neOS3BsTEICVq3oJZKcP84N4DV+Fe9\n9GbJKkK5i1QxHh/Go9WEEUf7KLRQTo1si/xxYeDR5lzTKLA0yinxX1KzZmKE\nUU2vNG7rayzLEMsMsMuw1yEQMqydFy5ZJvtfmhBA/Nj0BMzi8tA9mshk5xiy\nl7h85iP3XQzSrD5Sf1t35JmeiEm1PvPMaEUueo2GS7p4enLDQR2jJJQz+sJp\nbsYfGAFVnEPiE0x+TCuqZer0KMGCGdhuWGXBeFPvKbXCCSn83ZQz1AWJZ+PE\nsiMf\r\n=Ykox\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "b28bf2c46ba3bbf7eff44742fc421a9974b27cc5", "scripts": {"test": "mocha --reporter spec test/*-test.js", "pretest": "eslint mustache.js bin/mustache test/**/*.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "2.5.1", "jshint": "^2.9.5", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.1.0_1568373553930_0.17435981503909703", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.0-beta.0": {"name": "mustache", "version": "3.2.0-beta.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.2.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "dist": {"shasum": "9276e369a57c407c38c254b15c055e0ef7e440fe", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.2.0-beta.0.tgz", "fileCount": 18, "integrity": "sha512-2Ffxq/YYLuubsEJN+SnkjOZ7iVxI78Uwv2YDFUpD/6L2rEAuz+b+ZPicUZ2ILZ4Jprk4cq8ExY//Nf0gX2BcAw==", "signatures": [{"sig": "MEQCIGX4ZPPHSGZtrdOt93hxIMpfDRDEZpICho7uBOKQsvEhAiB8Tm17c4JE0mLGRSe2oaKOOnG9saduIZrOefdsD3apkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd64izCRA9TVsSAnZWagAARkgQAJ4m9OswQUCCIocoIxuK\nKyi8t8dmeNPltHY6YSiE6YAexTl6woa3YyPI7cTNdPonNe6GgEhigsK6ujyy\ngFXcgI/1nHavVqZlBYNZWmuJ/0uHuqOi/1vPDHA+8fOcmprYPYM4WMwldpwc\nQrWjV62q5m0Y3y8jYiMvTZMuV+s43yqnHJPsGVVP3RyOX1b4mXm6GVaSAHSU\nOH1P3sDBnhrCisKD72XcDMqRgwCGlB4WnDkW5KulJBLr59rFKxmpjtcoYt1X\nGuMxPSYD8+7Jq5UPwEy3vCZlkEUl+KF1Ki5P6CJu8S+edzvE7r2xlwA8kWgX\n9XvcthoUJPMTOpyus5kvQlqHTYBvfQv2qT7uAU/7j//So+xSdQ0z6i+lX1Hf\nu6WiusitgO/OZB+vHyRTOE1gFrerUGCAfjBaF314e3bRAxfH1c4CKUnCsf8p\no99kmUp8pcxRFD+lxNcKEwgDr7xGgyqnV4b/jR837eXyExSOjQSntcG5aciI\nS2OWZF6TsxN+sMBzdoZLtgqNhO5SQ8c7kfWgvMcMQ2Y7CwA6+fkRBI9VejAS\n5pjHzE7hKakofe1ElaEiUpJnya/lYAzqpOM/zpyqTCC0pRCYI1U9NGpMBfny\nlU/869ltZq6yIF8mOSfZ59TewLd2aPmnjV7CaIICHMd4xRY5xd0b5IXhPavb\nw9vd\r\n=v/nH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "e0097b1a7dcf14c0836a76d5fbff0ac758419f02", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner '// This file has been generated from mustache.mjs' && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "13.0.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.2.0-beta.0_1575717042343_0.6053049653621227", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.0": {"name": "mustache", "version": "3.2.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "dist": {"shasum": "1c68e0bf77817a92e8a9216e35c53bbb342345f6", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.2.0.tgz", "fileCount": 18, "integrity": "sha512-n5de2nQ1g2iz3PO9cmq/ZZx3W7glqjf0kavThtqfuNlZRllgU2a2Q0jWoQy3BloT5A6no7sjCTHBVn1rEKjx1Q==", "signatures": [{"sig": "MEUCIHg4rDkfauQkrzxcrmoj1tP2XBn+8bJLIhqTJxzvh0SRAiEAqh+zsvNnvFgkBlDVlTPZSu+DMIaudMsptunmOKwNVLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+pMGCRA9TVsSAnZWagAA2M0P/iTICn6jxnR8Kbqy0U7L\ntpA0YhStYm4v0O71fb1O9RF7v2vH5h10EzkI6ShJ/qGqjCCjAsIumMlWdUof\nHWQ+rUohWdh+0NhFe7r4Jzmrn8kYKDwdvb6z74JTpvfiIzF0yNF7qjHYiqtI\nroEDS0xGHj/H7Zn/5C1mQkIwifXhy7pVzKmrVTQfELHauXzST+qbXFVMOvkm\nbKJaId7THVXDi4lkNvybQEA2fwD9L+nl62XSBFCyC8ATewxuBX+1RjfHXmvX\nqwG7tmi+c1+cEko1ywMLru5u075q7W6uL147tT1Fl12zDHjI3fnIhNtQUyQr\nIpRWh8lheOGM7i2DPam2jc+6kYMopNgAqtprkXEyIHvSCc/ZZp77XJCM7NAQ\nFUZJquHHLlZgClJyr5TW2Q0avGNvN55O2iTbR3MEtfFiZwnosf0VS1ISPPcF\nk/BEGsMaHAnGw5zVQkdZENm0KpIzrlUgzaEfaLUDwHK0psW43KHnZU09r0mP\nncPEhGzKLZ4Al16wsGHvyMbSQ5gYvFMr6tg8RWDAYmbTrCigjUahkQ0kSyZP\nMVEpgL8mKe4BiIYw9pyNt4bJbO+cTJ7wfEi9DDaQvEkRd9h2+Vfs56t1up2K\napQ0z2KJPKOIyUOhQk+MLDlvHAXD53YvvfpRpz36CvOpDD16peER53cfhwjj\noVZq\r\n=C+Lk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "70d3e7ec4e54e315d8640fd8fc94d5c0f65ca208", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner '// This file has been generated from mustache.mjs' && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.2.0_1576702726168_0.13302998656480947", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.1": {"name": "mustache", "version": "3.2.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "bin/mustache"}, "dist": {"shasum": "89e78a9d207d78f2799b1e95764a25bf71a28322", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-3.2.1.tgz", "fileCount": 18, "integrity": "sha512-RERvMFdLpaFfSRIEe632yDm5nsd0SDKn8hGmcUwswnyiE5mtdZLDybtHAz6hjJhawokF0hXvGLtx9mrQfm6FkA==", "signatures": [{"sig": "MEQCIFPAl5UZVKv7omdW8H9SR2NBJwVBzPkhVLuldtax5OaUAiAg8JPCTU+PFVjv1zpoky0XznXphZGgpNUabWiAQIWEbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeCbMfCRA9TVsSAnZWagAANIMP/2mgdYf2Oh/ta8/BdHJW\n3mKH4x73uOANebZSwOWkB53V5GGGVd5QZriiEFfPiQvXhNHd3g6qjqEe1qTi\ngVREf/vucWehac1qqoCs8WxgEdg/A8jXoLkztDW5wZAbThagh6vJZJadDSMB\nLM4X5fU7FfyvKs/a0zyNYt3rlG82rgyMvH/9E1T482t/7FrKvywbMk1x1aBX\nA5Y5CrmsAIFJ0OPG+0c8fpwaCHndXvvPrLaIHHFzRi55m0KXsQyfkAxZ1LJs\n2/esM4WshWLsFDqnprGsnrhMRTeUTbga26tG9Z8mU6IMtaBDyb1OVmoqS7X7\nP4CeYsv5Fm4CMJw+K3hbb1PYgb75jsunnaSukWncOGdsRupugdM+UXEvE8vM\nMlVDv6/VP7or6ydidGZa+6pFT2HHrCsSNdGwJID/iPvDSeBDu3E3ORhaoJgT\nd2xS44A8z8t/IOetHcp8ISJMTrF3lXYup2yx6Sf2MOiLJmgSeTrwNDS4kWkx\nK/zGPOSIIMmaj8PaHZuoiIxy5EKOXq2bb6tgrKhz7Y+CatoX8vkeUYnv7gco\n2UpaLKBy1HugL7uw7w1Ycbpq0fRvY/HBoFY2cEpIGxapV2h5z20OIFcIIm7A\nV+c7tiVy4pSwulEk6fBmpB6calGLWaoCkzChYQ/xoAhX2SaAtGOSB4aWIytA\nnlFk\r\n=Fh1G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "8e52a4ac6cf4ed86d0fedb3c4dae643fd7b56998", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner '// This file has been generated from mustache.mjs' && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "12.14.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_3.2.1_1577693983266_0.3103848521064303", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "mustache", "version": "4.0.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "dist": {"shasum": "7f02465dbb5b435859d154831c032acdfbbefb31", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.0.0.tgz", "fileCount": 18, "integrity": "sha512-FJgjyX/IVkbXBXYUwH+OYwQKqWpFPLaLVESd70yHjSDunwzV2hZOoTBvPf4KLoxesUzzyfTH6F784Uqd7Wm5yA==", "signatures": [{"sig": "MEQCIFWKoNvVzzt7mapNyrdugPI8YsKzNw2gscwH4njfA/f2AiA58UPo/Q6rgUENZzrq8I/XFGBDKfzrPDCImLfTQANYbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIGgLCRA9TVsSAnZWagAASe4P+wRl9C+J0qizVrM1PmHn\nFyuzscfFGRsUz/GxKqrAOvE4usXXNE8qfQ/F7hwaL8No+po+mJJJ4nEW89eM\nu+yohxHH1bQaZj46o2Og5zuc2SpY+t1DQaJZGfZ6782CXGzMcqdyaQ4/uvOi\nOqNqSGXcihN23gWQl77m/c9xdP1lM18WVJYXhfpKB0mCcsgqtvlzsdMPT28o\nfGryEiAGRItTW5tat/xr5+v/AQgXCQ6Ef83/hb0mqs3jHXHWDVs+2oV2TWxG\nxhg0yFA4YzvXKMiNYwZTfAlhna4t/HgVWMjHQLlzstXDGN5D4Y+RqTC1Ne+C\n0zuOBhWSxdUTjJaZZvCZmz2bmlT3YMEcxwXm1rXI0wW9qnLX0uVVs9ZvYLfM\n+YM652qjMjfd7fenFORqE8Vz31Ooas1M5tkn50iKLfPHhS2S7QD2gfNhA4ZC\nv4exNqEOTf5KjUKGIeDSvYvtSt7z0xsGCIVh8LhoZgBYNpkIq9QnG7Ee75H5\niPikEmJMxcpF+N1qvFCFqQ1/dOnPjF9qXkUz/bL3fGppYJcTY8rUvQGkqud3\nxneUTej7my1dPH/pfXb0rSsU3VzU6HX9bVD0AttDRY3r7OvnMJHcroNutcYR\nmy2BJ2h+FeaNvTt67BP1d2959StiN/DuhfJPh7LwuaxiGg+fqnxprbfDnb3e\n61nK\r\n=ie/z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "aca97b82c80e8fd1d36162e05e4b289380965d96", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner '// This file has been generated from mustache.mjs' && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.0.0_1579182090821_0.9894351170259381", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "mustache", "version": "4.0.1", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}, {"name": "jan", "email": "<EMAIL>"}, {"name": "mjackson", "email": "<EMAIL>"}, {"name": "nathan", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "./bin/mustache"}, "dist": {"shasum": "d99beb031701ad433338e7ea65e0489416c854a2", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.0.1.tgz", "fileCount": 18, "integrity": "sha512-yL5VE97+OXn4+Er3THSmTdCFCtx5hHWzrolvH+JObZnUYwuaG7XV+Ch4fR2cIrcYI0tFHxS7iyFYl14bW8y2sA==", "signatures": [{"sig": "MEYCIQDon8dF8Y6/SRC1F+wtctslbq/J6T3tJS4qblfj8IKj1gIhAIL7tMbBkB4JR8FDHrXm45zBwm8FtFSEnADU24iclb87", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebo2/CRA9TVsSAnZWagAANTAP/RNyiF2ysAoFwjeR0R4C\nb8R5VlbennlEDhbKOxjmVRZ+RDDuh9VQ2BGld2Gsg0GCb/aaXkHyXb1dvcPA\nLJt64bvA6AHm53OXUaOA0MsOrwV4WxQmFv25N+zlvjLtoGubVThmxi0ZUkAS\nJibcI2JO7L3egrcmIPi+N+WGfuY2+fUrEAavj9718XFHApDBJ09AkNCIth0W\ng34/8PZc8j4PQGlROXaJz+x1jWwCnrH3GIb26YKEOhQpDyQrB8tld2CflEK1\n5noB8pdVH5QV1WTnYHabxbEx/Fm/dXmgtF6R6JDb8ZczcDZROlwVssN9kYUZ\nBO6/X3WTYmd/CGfF/D3X2vMtvBCmQjEu9nY4q7coKoiAHI1C3LBE3PhVhrUk\nrqjR6sJjwwPZB4LXT/Q8AIP0oxCtwCRZd3Y2KYNKONPtbvAYcKCSlSU7/+ig\nuz4VrfwWUC7EtrciJCbdZcm8KITCjNz/hgO2BWQSUCfpSUvwnxp6CVqMvOg4\nFZXP1LztjlO3hIynnyZDn/lJEf6iXnWC7+OJB66s8CnT6hHNsX05ivgOf1ld\nPMCbfpoMlFtFxyDbN4t3icMasTTwoK1Dat8CHOf4LU/9NQKWwFogZQV+J7z4\nbAkLVzIc0fXehQxzVh2dfkDERQf8YJQCCTlnEFZeeHqc68E4NYFRp2aSpKly\nFYyb\r\n=kArg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "engines": {"npm": ">=1.4.0"}, "gitHead": "1de94bbdd3fe4b903cfbc084ebaaccfd1299dd3f", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner '// This file has been generated from mustache.mjs' && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.0.1_1584303551152_0.3830549134279049", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.0": {"name": "mustache", "version": "4.1.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.1.0", "maintainers": [{"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "bin/mustache"}, "dist": {"shasum": "8c1b042238a982d2eb2d30efc6c14296ae3f699d", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.1.0.tgz", "fileCount": 18, "integrity": "sha512-0FsgP/WVq4mKyjolIyX+Z9Bd+3WS8GOwoUTyKXT5cTYMGeauNTi2HPCwERqseC1IHAy0Z7MDZnJBfjabd4O8GQ==", "signatures": [{"sig": "MEUCIFqpcXbpLAL6RQEZ0nx4aumW6aXlRIiVxbIUYGNcSHO6AiEAyBCD1g0rToSr4JcBc7NG/IyK+P5ihqz+fYlLcyNkZfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzB01CRA9TVsSAnZWagAAA28P/AlzD+46pdCd5rVr+dDb\nY9jJ9AmRq2tgvu3Cxeidey145H9WdTenI9PNpgGC52M2MItRdVPfq7/fXH8b\nU7CrxZvq4yJut+sAZ3BjJVici7XhYOdDNOI/2QU45na+cQ/7PepHyA+ri1h7\n3VKCSthd04maHDT+bPta7A8vSfIB4oivKwxN0md1bVFnB5i8sNdIIB26pvCg\nogAzc6iWepMDqmNMixA21a3bY/qZy0d6vKV40qD1T2P4LVvAQSPR09LYpoGq\nKXkhmKG3OdJmZL1J59NwXJ0CxbCxEhdMDXd/YM7m626BTX3264rgSpdnniBJ\nNnwWNbP+uvJyGxaF0nDVXRN77r5kcqDUQ+N/bncItXe5mz0AZbuQ/bmpmO7k\nyB6FNLrRa1nHxkUe8BagrDeq+61b/skFLmIO2+qzInQLXvlsCa+gdoQhe1e5\nTeGuWENNKF+E+U2ccBqoMRwQmRjZ58pUGxYoDsSQ5fvDSSqHm3SlhfZeSrh+\nRDlFjwHYOyAd81J5OmtA9YZAP2j7ZK+k8gKCVZSD1RHcvOsD6WgCXXFdGT2e\n9UYhHVhTR8xsXRIpm0oPuu5aeFplXbSk1QfMHPtZetjeN2Ozj42XtvOLiIwX\niNcU96Q/ugUhMGtvEx7HblOyt3MhcsV3kiZS9qbEb8p7rD+t3u5czzLQOZmz\nKA75\r\n=u+sz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "gitHead": "67c39b89af494141fc2ec2b279aacf986b86a8c3", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "rollup mustache.mjs --file mustache.js --format umd --name Mustache --banner \"// This file has been generated from mustache.mjs\" && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.mjs bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.1.0_1607212340912_0.8223962159105924", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.1-beta.0": {"name": "mustache", "version": "4.1.1-beta.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.1.1-beta.0", "maintainers": [{"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "bin/mustache"}, "dist": {"shasum": "a7283e99774347854a15dc524d6569c7d54ce107", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.1.1-beta.0.tgz", "fileCount": 18, "integrity": "sha512-AJaxHFtSqXVJgHNxl7dNJCwnPqVJ4aeUlabG3+cbZ8IshxO0f6GOS7HZQlzQTQYpv+s1MwD/YGYgQmglJzcORw==", "signatures": [{"sig": "MEYCIQDZIblxI7fNO0h2OXlgwsF8b9uJ85MFm6fVPN1MiZooVgIhANQZlGw1fvbdK0A5FW6gWh+s0jFLT9yZJd9kBB2AZGld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgM6aeCRA9TVsSAnZWagAAXucP/1OChJIh064kKgJsgxVg\nVeS/nT2MfdV3X0Sfhq+KStkYyQn9eNmE8zW0d8VbYNaXNTWmpPvXIKMgOKiJ\nQWHcI0eYG+2Sx0bYZOy2KAc/TAnCZoVHbY9OfeoviHf+4vlfPPkNhBhT0/df\n2p6NHav2ONRlEQexv5DSg1qWID/i+3eKHu1DFD3mk+sCN08rNCY8hWefn37q\nlez2nQ4JVAdLJikRo7/aIwuOBlJgMUuN8YedPLh6tMa+Orh8dVZ+xRSJNKkH\nnaIIwiG0rlx7ijZVgCged5EoELJaVRt6WgRqU4dxZiyGtY3M0po3h9hZAUor\ncM5BAMapEDgSwIM4EUqSOgSy90Wg6SfxwRq4BCYO4+fRlVweDYIhpL6yfuD0\nHI2L1UQfBTZBO3N183PbvHdENyAKzFQKPj12oS0Gklq6mv+0FeaSCnv+ENXQ\nXkdI2yAH/83JfdNi9af9vYBKJ0FGJgKm9UjdkTtY8PA/WGKk3LePd9qKJts/\ntHPZZzftwig5L/bjoVNMfEZDec3GbWal7gEWrbSYnNh1q46Q1XDSWWiI5ETO\nTCT2hqqNDRoajGf1evI3L0nPj4Cd9aUr+3MYCApq41g/afHpn5pQWaP8QIGV\nRmB4qiDKCKKaYBsLRIRTv7zt9WVu6JRseAidBcgohCZ9dy6eBa2rzOk7Jqrl\nCLc4\r\n=HE/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "gitHead": "f643e7fa01e5b92075557715bc554048b556ca8d", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "cp mustache.js mustache.mjs && rollup mustache.mjs --file mustache.js --format umd --name Mustache && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.js bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "postversion": "scripts/bump-version-in-source", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "prepublishOnly": "npm run build", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.1.1-beta.0_1613997725833_0.39102180887179583", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.2.0-beta.0": {"name": "mustache", "version": "4.2.0-beta.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.2.0-beta.0", "maintainers": [{"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "bin/mustache"}, "dist": {"shasum": "d1048ae3c1ed65fa39e923168abbd116345d65d1", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.2.0-beta.0.tgz", "fileCount": 18, "integrity": "sha512-Oicbb3Arg3X9IT2kvCQhPjjccGWHWGWm2xwwFD3U1XyAr4Q+/0Y19yUH7WM7+PWdhxyP9lFBsmNafc7A1QX0qA==", "signatures": [{"sig": "MEUCIDU31QXveHgnl2I1foxWwh8YDboOmjc27pjvlD+T/GWmAiEA5wXIvzri0ed/zliZ5ZfVWiocorWn0NpH1cm1AEfr76o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTeDWCRA9TVsSAnZWagAAQxkP/1eSRdomIv98jWRq4Fxo\n77RXzr3nPmR0DjIX/o0GxxgW8UJWNf26fFme0uKICsi4xVviQlJR91gingev\nAFOK6O/R6HotlbsGTJelCcMkEt4924YfrC+i4KG63aHPShBiIiNpmyuVpVdQ\nziEu6b52Zbz8OTvaiDICizZcrjo1zjixciziRwLpyQTFb/6cS0K6VRXeDJ+D\nJkjiLhoG//wW7u3ZajQLgR6KVMQ+SbPEYOWQ7MuP1/OEj7vOR6oJiPy9oyJy\nEcmGIZZfa1VBg3j8UF6p+3SyTBdkaj9JjNBAs/bnba7vonG+NlMBDV3SUSoh\nemiYSA2sZcXhmi4ngoUvqdqzI5ajBMkgXO/AlUXnobTrSktOo1JXbPrl05dn\nIKvwE5dJT19w4pZ3U9yoz/mfIshkWhScufGBlTpGUtAQTz9RhfrwjsuiDdfp\nl9bjTFGhpYHILBeXaurKhNY1BWqttPgTsVqrU9Ba70wENwLtL3poZElEHFlk\nJWIwWhVKNhhf+a1BZzthH5Isr1/AdoIrX4gejuhZ7vlFnbSa8aS7IA/YDmP+\nR8hglUnyI1fxf9tRR3bgCguwXt6coQ5WUs6PEharToBITLUaje3G/rAufVaC\n7QFGHAFbfyI9f8su3c27XxJwjqTkj666pVFDuhSiGS78NAY4p+LmFkKAsEDX\ncHEy\r\n=EqPP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "exports": {".": {"import": "./mustache.mjs", "require": "./mustache.js"}, "./*": "./*"}, "gitHead": "5d3277dad55a6f4f012b245080049d16ea188d0e", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "cp mustache.js mustache.mjs && rollup mustache.mjs --file mustache.js --format umd --name Mustache && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.js bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "postversion": "scripts/bump-version-in-source", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "prepublishOnly": "npm run build", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.2.0-beta.0_1615716565871_0.5111161303700964", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.2.0": {"name": "mustache", "version": "4.2.0", "keywords": ["mustache", "template", "templates", "ejs"], "author": {"name": "mustache.js Authors", "email": "http://github.com/janl/mustache.js"}, "license": "MIT", "_id": "mustache@4.2.0", "maintainers": [{"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "flipp", "email": "<EMAIL>"}], "homepage": "https://github.com/janl/mustache.js", "bugs": {"url": "https://github.com/janl/mustache.js/issues"}, "bin": {"mustache": "bin/mustache"}, "dist": {"shasum": "e5892324d60a12ec9c2a73359edca52972bf6f64", "tarball": "https://mirrors.cloud.tencent.com/npm/mustache/-/mustache-4.2.0.tgz", "fileCount": 18, "integrity": "sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==", "signatures": [{"sig": "MEUCIFtdJyqzhDbl5KM7sITUuuxbVg7RTJoVkvyxf5jJksQEAiEAyRNFNxCajqWr05tvUxrMsuaCNLvWprbPlW0HQPmMYr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYNi/CRA9TVsSAnZWagAAQjgQAJqoHy7GmtCfyJZwGFeR\nuP6KA+BmaTJGfWtEvxkqnZ5zg2odnlaku2ZZHc0ZLb/b44yEITyd6TbYowD3\nV3B/nF0AlLZiqPcY4IKBLZcHfK+xp30wBXwektpb/686sIyssjehPenK6e95\nFxKfHpSUpHMBn5tuUw6YmezKL0NwbAFULKRU0S7bdWOwne2dPB7PSs3myLAJ\nq8+RZDy6vlUANcc27D5MG77lA4BtKlH1LhYdXjySipXlUNI3YKR9oU67MgxU\nNJhxAA2oPiKI0UqZDO/4jHUwENvHqiFJW3CzlnEWFxG5DAsEe99m4mGRESb+\nW4xegU2Wm8noqwBtuW4W8Hsf6fKGnlbnTXWadLYJxJs13iwl4cBcwetGHPFh\nTSVLktIsZ9nNAQlAnYiGTMLwyv5CHdCQe+htE1Q0Rtn4vt2hUrkdjvyl6oqh\nDkzXXa6H8R6dinurQ2zQXFv4J0X+/yqQ72MMFDDWj7IG+W59XLNJgMJeNjp5\ngjx7Ps9mESvGJ2nRUkOICccynEvCiiaIY7b3axuSmG3BelK9SdY2io48s6Pv\nzhMzkBUO3hmClKrXFmaPnqaNxx49WlsxdkRgqeGk93R2xI8cJdz+CoR1N/ii\nLaqMo0W1anxS72L336R7xc6DknTdDnqkoCT8ooiNqor8MTTW+eZhr6HgUB4j\n/CBB\r\n=rdxo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "mustache.js", "volo": {"url": "https://raw.github.com/janl/mustache.js/{version}/mustache.js"}, "exports": {".": {"import": "./mustache.mjs", "require": "./mustache.js"}, "./*": "./*"}, "gitHead": "813e273a658677852ab37e6f47c98a9d9352ccde", "scripts": {"test": "npm run test-lint && npm run test-unit", "build": "cp mustache.js mustache.mjs && rollup mustache.mjs --file mustache.js --format umd --name Mustache && uglifyjs mustache.js > mustache.min.js", "test-lint": "eslint mustache.js bin/mustache test/**/*.js", "test-unit": "mocha --reporter spec test/*-test.js", "postversion": "scripts/bump-version-in-source", "test-render": "mocha  --reporter spec test/render-test", "test-browser": "npm run pre-test-browser && zuul -- test/context-test.js test/parse-test.js test/scanner-test.js test/render-test-browser.js", "prepublishOnly": "npm run build", "pre-test-browser": "node test/create-browser-suite.js", "test-browser-local": "npm run pre-test-browser && zuul --local 8080 -- test/context-test.js test/scanner-test.js test/parse-test.js test/render-test-browser.js"}, "_npmUser": {"name": "flipp", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/janl/mustache.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Logic-less {{mustache}} templates with JavaScript", "directories": {}, "greenkeeper": {"ignore": ["eslint"]}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "chai": "^3.4.0", "zuul": "^3.11.0", "mocha": "^3.0.2", "eslint": "^6.5.1", "jshint": "^2.9.5", "rollup": "^1.26.3", "puppeteer": "^2.0.0", "uglify-js": "^3.4.6", "zuul-ngrok": "github:no<PERSON><PERSON><PERSON>/zuul-ngrok#patch-1"}, "_npmOperationalInternal": {"tmp": "tmp/mustache_4.2.0_1616959678984_0.980152493854989", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2012-01-09T18:34:02.818Z", "modified": "2024-10-22T17:26:42.327Z", "0.3.1-dev": "2012-01-09T18:34:02.818Z", "0.4.0": "2012-01-09T18:34:02.818Z", "0.5.1-dev": "2012-08-02T05:56:32.864Z", "0.5.2": "2012-08-02T05:58:11.369Z", "0.6.0": "2012-08-31T22:43:14.499Z", "0.7.0": "2012-09-10T22:48:28.533Z", "0.7.1": "2012-12-06T16:20:19.022Z", "0.7.2": "2012-12-27T20:09:59.953Z", "0.7.3": "2013-11-05T15:45:08.995Z", "0.8.0": "2013-12-02T23:23:48.971Z", "0.8.1": "2014-01-03T15:00:27.972Z", "0.8.2": "2014-06-13T14:49:39.219Z", "1.0.0": "2014-12-20T16:01:49.128Z", "1.1.0": "2015-02-18T11:10:06.532Z", "1.2.0": "2015-03-24T09:37:28.497Z", "2.0.0": "2015-03-27T09:33:01.850Z", "2.1.0": "2015-06-04T22:22:32.711Z", "2.1.1": "2015-06-11T08:57:09.228Z", "2.1.2": "2015-06-17T20:17:28.930Z", "2.1.3": "2015-07-23T16:35:18.400Z", "2.2.0": "2015-10-15T10:16:46.996Z", "2.2.1": "2015-12-13T11:06:56.462Z", "2.3.0": "2016-11-08T16:25:18.753Z", "2.3.1": "2018-08-07T18:09:08.987Z", "2.3.2": "2018-08-17T12:19:21.803Z", "3.0.0": "2018-09-16T20:41:38.967Z", "3.0.1": "2018-11-11T21:26:24.169Z", "3.0.2": "2019-08-21T20:15:57.623Z", "3.0.3": "2019-08-27T08:54:42.020Z", "3.1.0": "2019-09-13T11:19:14.062Z", "3.2.0-beta.0": "2019-12-07T11:10:42.879Z", "3.2.0": "2019-12-18T20:58:46.330Z", "3.2.1": "2019-12-30T08:19:43.397Z", "4.0.0": "2020-01-16T13:41:30.951Z", "4.0.1": "2020-03-15T20:19:11.276Z", "4.1.0": "2020-12-05T23:52:21.066Z", "4.1.1-beta.0": "2021-02-22T12:42:06.017Z", "4.2.0-beta.0": "2021-03-14T10:09:26.096Z", "4.2.0": "2021-03-28T19:27:59.176Z"}, "users": {}, "dist-tags": {"beta": "4.2.0-beta.0", "latest": "4.2.0"}, "_rev": "182-323936da42ed65f7", "_id": "mustache", "readme": "# mustache.js - Logic-less {{mustache}} templates with JavaScript\n\n> What could be more logical awesome than no logic at all?\n\n[![Build Status](https://travis-ci.org/janl/mustache.js.svg?branch=master)](https://travis-ci.org/janl/mustache.js)\n\n[mustache.js](http://github.com/janl/mustache.js) is a zero-dependency implementation of the [mustache](http://mustache.github.com/) template system in JavaScript.\n\n[Mustache](http://mustache.github.com/) is a logic-less template syntax. It can be used for HTML, config files, source code - anything. It works by expanding tags in a template using values provided in a hash or object.\n\nWe call it \"logic-less\" because there are no if statements, else clauses, or for loops. Instead there are only tags. Some tags are replaced with a value, some nothing, and others a series of values.\n\nFor a language-agnostic overview of mustache's template syntax, see the `mustache(5)` [manpage](http://mustache.github.com/mustache.5.html).\n\n## Where to use mustache.js?\n\nYou can use mustache.js to render mustache templates anywhere you can use JavaScript. This includes web browsers, server-side environments such as [Node.js](http://nodejs.org/), and [CouchDB](http://couchdb.apache.org/) views.\n\nmustache.js ships with support for the [CommonJS](http://www.commonjs.org/) module API, the [Asynchronous Module Definition](https://github.com/amdjs/amdjs-api/wiki/AMD) API (AMD) and [ECMAScript modules](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules).\n\nIn addition to being a package to be used programmatically, you can use it as a [command line tool](#command-line-tool).\n\nAnd this will be your templates after you use Mustache:\n\n!['stache](https://cloud.githubusercontent.com/assets/288977/8779228/a3cf700e-2f02-11e5-869a-300312fb7a00.gif)\n\n## Install\n\nYou can get Mustache via [npm](http://npmjs.com).\n\n```bash\n$ npm install mustache --save\n```\n\n## Usage\n\nBelow is a quick example how to use mustache.js:\n\n```js\nvar view = {\n  title: \"Joe\",\n  calc: function () {\n    return 2 + 4;\n  }\n};\n\nvar output = Mustache.render(\"{{title}} spends {{calc}}\", view);\n```\n\nIn this example, the `Mustache.render` function takes two parameters: 1) the [mustache](http://mustache.github.com/) template and 2) a `view` object that contains the data and code needed to render the template.\n\n## Templates\n\nA [mustache](http://mustache.github.com/) template is a string that contains any number of mustache tags. Tags are indicated by the double mustaches that surround them. `{{person}}` is a tag, as is `{{#person}}`. In both examples we refer to `person` as the tag's key. There are several types of tags available in mustache.js, described below.\n\nThere are several techniques that can be used to load templates and hand them to mustache.js, here are two of them:\n\n#### Include Templates\n\nIf you need a template for a dynamic part in a static website, you can consider including the template in the static HTML file to avoid loading templates separately. Here's a small example:\n\n```js\n// file: render.js\n\nfunction renderHello() {\n  var template = document.getElementById('template').innerHTML;\n  var rendered = Mustache.render(template, { name: 'Luke' });\n  document.getElementById('target').innerHTML = rendered;\n}\n```\n\n```html\n<html>\n  <body onload=\"renderHello()\">\n    <div id=\"target\">Loading...</div>\n    <script id=\"template\" type=\"x-tmpl-mustache\">\n      Hello {{ name }}!\n    </script>\n\n    <script src=\"https://unpkg.com/mustache@latest\"></script>\n    <script src=\"render.js\"></script>\n  </body>\n</html>\n```\n\n#### Load External Templates\n\nIf your templates reside in individual files, you can load them asynchronously and render them when they arrive. Another example using [fetch](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch):\n\n```js\nfunction renderHello() {\n  fetch('template.mustache')\n    .then((response) => response.text())\n    .then((template) => {\n      var rendered = Mustache.render(template, { name: 'Luke' });\n      document.getElementById('target').innerHTML = rendered;    \n    });\n}\n```\n\n### Variables\n\nThe most basic tag type is a simple variable. A `{{name}}` tag renders the value of the `name` key in the current context. If there is no such key, nothing is rendered.\n\nAll variables are HTML-escaped by default. If you want to render unescaped HTML, use the triple mustache: `{{{name}}}`. You can also use `&` to unescape a variable.\n\nIf you'd like to change HTML-escaping behavior globally (for example, to template non-HTML formats), you can override Mustache's escape function. For example, to disable all escaping: `Mustache.escape = function(text) {return text;};`.\n\nIf you want `{{name}}` _not_ to be interpreted as a mustache tag, but rather to appear exactly as `{{name}}` in the output, you must change and then restore the default delimiter. See the [Custom Delimiters](#custom-delimiters) section for more information.\n\nView:\n\n```json\n{\n  \"name\": \"Chris\",\n  \"company\": \"<b>GitHub</b>\"\n}\n```\n\nTemplate:\n\n```\n* {{name}}\n* {{age}}\n* {{company}}\n* {{{company}}}\n* {{&company}}\n{{=<% %>=}}\n* {{company}}\n<%={{ }}=%>\n```\n\nOutput:\n\n```html\n* Chris\n*\n* &lt;b&gt;GitHub&lt;/b&gt;\n* <b>GitHub</b>\n* <b>GitHub</b>\n* {{company}}\n```\n\nJavaScript's dot notation may be used to access keys that are properties of objects in a view.\n\nView:\n\n```json\n{\n  \"name\": {\n    \"first\": \"Michael\",\n    \"last\": \"Jackson\"\n  },\n  \"age\": \"RIP\"\n}\n```\n\nTemplate:\n\n```html\n* {{name.first}} {{name.last}}\n* {{age}}\n```\n\nOutput:\n\n```html\n* Michael Jackson\n* RIP\n```\n\n### Sections\n\nSections render blocks of text zero or more times, depending on the value of the key in the current context.\n\nA section begins with a pound and ends with a slash. That is, `{{#person}}` begins a `person` section, while `{{/person}}` ends it. The text between the two tags is referred to as that section's \"block\".\n\nThe behavior of the section is determined by the value of the key.\n\n#### False Values or Empty Lists\n\nIf the `person` key does not exist, or exists and has a value of `null`, `undefined`, `false`, `0`, or `NaN`, or is an empty string or an empty list, the block will not be rendered.\n\nView:\n\n```json\n{\n  \"person\": false\n}\n```\n\nTemplate:\n\n```html\nShown.\n{{#person}}\nNever shown!\n{{/person}}\n```\n\nOutput:\n\n```html\nShown.\n```\n\n#### Non-Empty Lists\n\nIf the `person` key exists and is not `null`, `undefined`, or `false`, and is not an empty list the block will be rendered one or more times.\n\nWhen the value is a list, the block is rendered once for each item in the list. The context of the block is set to the current item in the list for each iteration. In this way we can loop over collections.\n\nView:\n\n```json\n{\n  \"stooges\": [\n    { \"name\": \"Moe\" },\n    { \"name\": \"Larry\" },\n    { \"name\": \"Curly\" }\n  ]\n}\n```\n\nTemplate:\n\n```html\n{{#stooges}}\n<b>{{name}}</b>\n{{/stooges}}\n```\n\nOutput:\n\n```html\n<b>Moe</b>\n<b>Larry</b>\n<b>Curly</b>\n```\n\nWhen looping over an array of strings, a `.` can be used to refer to the current item in the list.\n\nView:\n\n```json\n{\n  \"musketeers\": [\"Athos\", \"Aramis\", \"Porthos\", \"D'Artagnan\"]\n}\n```\n\nTemplate:\n\n```html\n{{#musketeers}}\n* {{.}}\n{{/musketeers}}\n```\n\nOutput:\n\n```html\n* Athos\n* Aramis\n* Porthos\n* D'Artagnan\n```\n\nIf the value of a section variable is a function, it will be called in the context of the current item in the list on each iteration.\n\nView:\n\n```js\n{\n  \"beatles\": [\n    { \"firstName\": \"John\", \"lastName\": \"Lennon\" },\n    { \"firstName\": \"Paul\", \"lastName\": \"McCartney\" },\n    { \"firstName\": \"George\", \"lastName\": \"Harrison\" },\n    { \"firstName\": \"Ringo\", \"lastName\": \"Starr\" }\n  ],\n  \"name\": function () {\n    return this.firstName + \" \" + this.lastName;\n  }\n}\n```\n\nTemplate:\n\n```html\n{{#beatles}}\n* {{name}}\n{{/beatles}}\n```\n\nOutput:\n\n```html\n* John Lennon\n* Paul McCartney\n* George Harrison\n* Ringo Starr\n```\n\n#### Functions\n\nIf the value of a section key is a function, it is called with the section's literal block of text, un-rendered, as its first argument. The second argument is a special rendering function that uses the current view as its view argument. It is called in the context of the current view object.\n\nView:\n\n```js\n{\n  \"name\": \"Tater\",\n  \"bold\": function () {\n    return function (text, render) {\n      return \"<b>\" + render(text) + \"</b>\";\n    }\n  }\n}\n```\n\nTemplate:\n\n```html\n{{#bold}}Hi {{name}}.{{/bold}}\n```\n\nOutput:\n\n```html\n<b>Hi Tater.</b>\n```\n\n### Inverted Sections\n\nAn inverted section opens with `{{^section}}` instead of `{{#section}}`. The block of an inverted section is rendered only if the value of that section's tag is `null`, `undefined`, `false`, *falsy* or an empty list.\n\nView:\n\n```json\n{\n  \"repos\": []\n}\n```\n\nTemplate:\n\n```html\n{{#repos}}<b>{{name}}</b>{{/repos}}\n{{^repos}}No repos :({{/repos}}\n```\n\nOutput:\n\n```html\nNo repos :(\n```\n\n### Comments\n\nComments begin with a bang and are ignored. The following template:\n\n```html\n<h1>Today{{! ignore me }}.</h1>\n```\n\nWill render as follows:\n\n```html\n<h1>Today.</h1>\n```\n\nComments may contain newlines.\n\n### Partials\n\nPartials begin with a greater than sign, like {{> box}}.\n\nPartials are rendered at runtime (as opposed to compile time), so recursive partials are possible. Just avoid infinite loops.\n\nThey also inherit the calling context. Whereas in ERB you may have this:\n\n```html+erb\n<%= partial :next_more, :start => start, :size => size %>\n```\n\nMustache requires only this:\n\n```html\n{{> next_more}}\n```\n\nWhy? Because the `next_more.mustache` file will inherit the `size` and `start` variables from the calling context. In this way you may want to think of partials as includes, imports, template expansion, nested templates, or subtemplates, even though those aren't literally the case here.\n\n\nFor example, this template and partial:\n\n    base.mustache:\n    <h2>Names</h2>\n    {{#names}}\n      {{> user}}\n    {{/names}}\n\n    user.mustache:\n    <strong>{{name}}</strong>\n\nCan be thought of as a single, expanded template:\n\n```html\n<h2>Names</h2>\n{{#names}}\n  <strong>{{name}}</strong>\n{{/names}}\n```\n\nIn mustache.js an object of partials may be passed as the third argument to `Mustache.render`. The object should be keyed by the name of the partial, and its value should be the partial text.\n\n```js\nMustache.render(template, view, {\n  user: userTemplate\n});\n```\n\n### Custom Delimiters\n\nCustom delimiters can be used in place of `{{` and `}}` by setting the new values in JavaScript or in templates.\n\n#### Setting in JavaScript\n\nThe `Mustache.tags` property holds an array consisting of the opening and closing tag values. Set custom values by passing a new array of tags to `render()`, which gets honored over the default values, or by overriding the `Mustache.tags` property itself:\n\n```js\nvar customTags = [ '<%', '%>' ];\n```\n\n##### Pass Value into Render Method\n```js\nMustache.render(template, view, {}, customTags);\n```\n\n##### Override Tags Property\n```js\nMustache.tags = customTags;\n// Subsequent parse() and render() calls will use customTags\n```\n\n#### Setting in Templates\n\nSet Delimiter tags start with an equals sign and change the tag delimiters from `{{` and `}}` to custom strings.\n\nConsider the following contrived example:\n\n```html+erb\n* {{ default_tags }}\n{{=<% %>=}}\n* <% erb_style_tags %>\n<%={{ }}=%>\n* {{ default_tags_again }}\n```\n\nHere we have a list with three items. The first item uses the default tag style, the second uses ERB style as defined by the Set Delimiter tag, and the third returns to the default style after yet another Set Delimiter declaration.\n\nAccording to [ctemplates](https://htmlpreview.github.io/?https://raw.githubusercontent.com/OlafvdSpek/ctemplate/master/doc/howto.html), this \"is useful for languages like TeX, where double-braces may occur in the text and are awkward to use for markup.\"\n\nCustom delimiters may not contain whitespace or the equals sign.\n\n## Pre-parsing and Caching Templates\n\nBy default, when mustache.js first parses a template it keeps the full parsed token tree in a cache. The next time it sees that same template it skips the parsing step and renders the template much more quickly. If you'd like, you can do this ahead of time using `mustache.parse`.\n\n```js\nMustache.parse(template);\n\n// Then, sometime later.\nMustache.render(template, view);\n```\n\n## Command line tool\n\nmustache.js is shipped with a Node.js based command line tool. It might be installed as a global tool on your computer to render a mustache template of some kind\n\n```bash\n$ npm install -g mustache\n\n$ mustache dataView.json myTemplate.mustache > output.html\n```\n\nalso supports stdin.\n\n```bash\n$ cat dataView.json | mustache - myTemplate.mustache > output.html\n```\n\nor as a package.json `devDependency` in a build process maybe?\n\n```bash\n$ npm install mustache --save-dev\n```\n\n```json\n{\n  \"scripts\": {\n    \"build\": \"mustache dataView.json myTemplate.mustache > public/output.html\"\n  }\n}\n```\n```bash\n$ npm run build\n```\n\nThe command line tool is basically a wrapper around `Mustache.render` so you get all the features.\n\nIf your templates use partials you should pass paths to partials using `-p` flag:\n\n```bash\n$ mustache -p path/to/partial1.mustache -p path/to/partial2.mustache dataView.json myTemplate.mustache\n```\n\n## Plugins for JavaScript Libraries\n\nmustache.js may be built specifically for several different client libraries, including the following:\n\n  - [jQuery](http://jquery.com/)\n  - [MooTools](http://mootools.net/)\n  - [Dojo](http://www.dojotoolkit.org/)\n  - [YUI](http://developer.yahoo.com/yui/)\n  - [qooxdoo](http://qooxdoo.org/)\n\nThese may be built using [Rake](http://rake.rubyforge.org/) and one of the following commands:\n```bash\n$ rake jquery\n$ rake mootools\n$ rake dojo\n$ rake yui3\n$ rake qooxdoo\n```\n\n## TypeScript\n\nSince the source code of this package is written in JavaScript, we follow the [TypeScript publishing docs](https://www.typescriptlang.org/docs/handbook/declaration-files/publishing.html) preferred approach\nby having type definitions available via [@types/mustache](https://www.npmjs.com/package/@types/mustache).\n\n## Testing\n\nIn order to run the tests you'll need to install [Node.js](http://nodejs.org/).\n\nYou also need to install the sub module containing [Mustache specifications](http://github.com/mustache/spec) in the project root.\n```bash\n$ git submodule init\n$ git submodule update\n```\nInstall dependencies.\n```bash\n$ npm install\n```\nThen run the tests.\n```bash\n$ npm test\n```\nThe test suite consists of both unit and integration tests. If a template isn't rendering correctly for you, you can make a test for it by doing the following:\n\n  1. Create a template file named `mytest.mustache` in the `test/_files`\n     directory. Replace `mytest` with the name of your test.\n  2. Create a corresponding view file named `mytest.js` in the same directory.\n     This file should contain a JavaScript object literal enclosed in\n     parentheses. See any of the other view files for an example.\n  3. Create a file with the expected output in `mytest.txt` in the same\n     directory.\n\nThen, you can run the test with:\n```bash\n$ TEST=mytest npm run test-render\n```\n\n### Browser tests\n\nBrowser tests are not included in `npm test` as they run for too long, although they are ran automatically on Travis when merged into master. Run browser tests locally in any browser:\n```bash\n$ npm run test-browser-local\n```\nthen point your browser to `http://localhost:8080/__zuul`\n\n## Who uses mustache.js?\n\nAn updated list of mustache.js users is kept [on the Github wiki](https://github.com/janl/mustache.js/wiki/Beard-Competition). Add yourself or your company if you use mustache.js!\n\n## Contributing\n\nmustache.js is a mature project, but it continues to actively invite maintainers. You can help out a high-profile project that is used in a lot of places on the web. No big commitment required, if all you do is review a single [Pull Request](https://github.com/janl/mustache.js/pulls), you are a maintainer. And a hero.\n\n### Your First Contribution\n\n- review a [Pull Request](https://github.com/janl/mustache.js/pulls)\n- fix an [Issue](https://github.com/janl/mustache.js/issues)\n- update the [documentation](https://github.com/janl/mustache.js#usage)\n- make a website\n- write a tutorial\n\n## Thanks\n\nmustache.js wouldn't kick ass if it weren't for these fine souls:\n\n  * Chris Wanstrath / defunkt\n  * Alexander Lang / langalex\n  * Sebastian Cohnen / tisba\n  * J Chris Anderson / jchris\n  * Tom Robinson / tlrobinson\n  * Aaron Quint / quirkey\n  * Douglas Crockford\n  * Nikita Vasilyev / NV\n  * Elise Wood / glytch\n  * Damien Mathieu / dmathieu\n  * Jakub Kuźma / qoobaa\n  * Will Leinweber / will\n  * dpree\n  * Jason Smith / jhs\n  * Aaron Gibralter / agibralter\n  * Ross Boucher / boucher\n  * Matt Sanford / mzsanford\n  * Ben Cherry / bcherry\n  * Michael Jackson / mjackson\n  * Phillip Johnsen / phillipj\n  * David da Silva Contín / dasilvacontin", "_attachments": {}}