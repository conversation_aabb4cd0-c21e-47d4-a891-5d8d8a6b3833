{"name": "is-unicode-supported", "versions": {"0.1.0": {"name": "is-unicode-supported", "version": "0.1.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "3f26c76a809593b52bfa2ecb5710ed2779b522a7", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "fileCount": 5, "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "signatures": [{"sig": "MEQCIFuRlamkCwFU1ELXvLnnqP7A66bhdSOwWlMNnw7d2O0XAiAnVnw4WwAD9MzT979hLaIkvtuS4N/wfs5pBd5BI9bTZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUdXCCRA9TVsSAnZWagAAjswP/2RSnMaY9hYr2sLk2wCt\nx0ONlloczHi6yj5G/cGyY5iwjrxdORpHkQ9EV3e9uVB1DR63ktGjNsmQnV0n\nT+ENyl3MrN2TnEjmZv7Jmd6pfPioWGue21D4qBG+f3CZR+37arSYEfx1Zukm\n0M3KINeSM6M6UnEhS4XRZzO5RFljBjX4dkx6vf7aImTJZYlhxbEKuOKNeNKy\nSu0gTs6uBOp4dkXkP8nV89l9bhJlcACCuZSTAZUtkXENyfY8Qkp9Boc5P5Qd\n3PNYA4rLa0XfUQ+E+EogdWQc1fyU3EHxfSrAuBqNRVxqv+knFslgUS30Cr9g\nHGvatFKbGdbHPTaq3erCQsKDCzVql0+RdBVfMZ6z60NOPGWQCdwHULQpFJrW\nJ/4RtLAJLzePsj+voRz19f5DXqP0UbxH8Q66WuQxyUb5sTFXHh7/crQQLACY\nxS7AWY5ib9bE/InEQ7V/rPtpViNrFHc2WNpTMVECvSZB/l5ta1SbC8Waobt9\n0GNzeGpphvE+u6zsUIEycVBlGWblulr+YTSMy9JTQdzt+VAWnSnQTJFXtezp\ny6UdleCB+lizQAp/Chc8MbWRe3gTpI3l5rFrFlfdo9BvZaIABxMZ6Rf5T+NH\nVK8+66oR2hO745DvUZsfz0lN3Z0AL6a+FkBos2qxgBj8xzPNNbdrqlJEri9t\ngh6T\r\n=0xWP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "73d12afd9a8bb7efd747b18d8b8278d7e9364cfb", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^2.4.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_0.1.0_1615975873665_0.8645116150518524", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.0": {"name": "is-unicode-supported", "version": "1.0.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "57284e13e7f5caefc7a45168285227e5c47d6413", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-TfeaLetCvWAJ/QjG8zBbr0H2LXE0m/Oatp25w0kgU2T/N+Xd2VMV3ENm/h3H4/ajBG9euaK2gsqipL4aCz8lTQ==", "signatures": [{"sig": "MEUCIQCw1puNbnsfjyMeCaghTm4Ada5AGHAvrg1m/WynB9TltgIgeuGZ6RomRBdP993OTVRbJGuy0ocVn4R/cUbTwgpHQss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge78XCRA9TVsSAnZWagAAU64P/3vKVaR1xbFi2d+hzYlQ\nUBg1i1JiNuW007xSOiC5CtSnY+RfEpOFX6hu9W1Dx79VUekUeHETpiKj0jrk\ncZDsGszHQ2VMHD3R5CoOV6+0/NWecrtskiywfb6GXZqvukhyynT4d+f0n3BH\nC3qVVi6T4XfNELgC06EWHsuTfaQrJyEvyfsgy0CLwYFWF9r+T3klGzoNrSoW\niF9+LySjfxYhp2z0tD7CHhVTUQolD9jaaoApV06oo8lxizGmvXA00t3Bv5e7\nBnMbAmxpSrrnapGjxXmdHUfV3F/n+KjW2srZvMfoAnjwG/mgRqDC4CwetHeZ\ncleI0whOVqonEj1Uv6tkVzc8JeDxFXRRz+TtDmREBKZzvZQ2Y8BeutkoJfM6\nYRCm5WwnA9BEQ1i6G1SayNPi9Lfe8F4an9dJvKuD249ckfg+ZR70n7qD81pd\nR3DsgWdq+0mAPbSxXMEnfw5Fem417CtUesNsJwgYFVc+FqZ9YjN3jfFek/fO\newEq2mcl+Fi6JJTnC9e0OnqL+hxIw6w3HTfXveBpX23zUbAkpcgnJIRWnGSB\nMCIVhgMVLZ7ps+6yMOhjQZCjzIZvGo7skmbtBWTyiQzIeuwJK1TQcYxBRoyH\noCpif+A9QOIn4qDotqK1DKHzou9hDPTW8cyd9aT1z+/TRwYyi2jIeeEPsSel\n1MA9\r\n=Zyha\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a30a1f243cef54d25a078134f096acd02070eb2e", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_1.0.0_1618722582803_0.33430717601574766", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.1.0": {"name": "is-unicode-supported", "version": "1.1.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "9127b71f9fa82f52ca5c20e982e7bec0ee31ee1e", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-lDcxivp8TJpLG75/DpatAqNzOpDPSpED8XNtrpBHTdQ2InQ1PbW78jhwSxyxhhu+xbVSast2X38bwj8atwoUQA==", "signatures": [{"sig": "MEYCIQDyMerr9TITLb2qtBuaWJQ8VlHLAkN7yeKH37nafJwW3AIhAPDf9phqj2WH0Wy8FXprWz8hSUV6L+QtRTSX6VQlyXXw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4ARHCRA9TVsSAnZWagAA/oEP/2l3IO2YOTZqt5XYtmHt\n+kU4cggsZgxmbZSkTjBGZ6lJqrv9t4XjD2j/BMh3Lm/1dsrQ9d8G1EWic8XK\nrL0uPDBY7Q82DSVOzLWR9kOukMH2ixSijSUhrW/oJkOaWEUUUdZgVPgKUVni\nZtfdGV15ZRVjH/2cBXqxt2EvDW7hI58lQeckpfOA6t4/YY5bFracGGcfZ3IV\n0Frp5qwsHAj3qsqo4+TLIexVExyqz+4egP5AxADPut9MOfV9Vj8RWPfIbUX3\n+wRqdG4yiQTVj+jZYNVLoQ8TVN+TDxKvlLA1BscoMR6WpY0oWf19JxLfJzQ3\nEHgAHpFDFqcw8x3OlEbSGv5c906a74wXR6FXUweUSTqmu0ZSZut9wUSVa1If\nsKA2BdWeMq58JDXobP2uVOVHhHQrgMEIlV1m0QetkqXaWr74YB7plQIXMpud\nxQCrF/NDYpAp2Z3TOchfRb264oVwdPjepdRsizv1d0EO8Wdros6rFDGHtrMT\nvCyOpG/U7OOH8W3djil+QSlMf9/OfWONoNI+HIS+ZkwrX/+CmW6g9n06TbaZ\nZ5MT41xhfIW+HYoX4WwvPp2gBdmwfVjK5g4sb0YgHn41RAMqb1csFgiFA1JF\noDGuv7D6NlT5Ltj7Ggwf/5RVMt7UV2Md7/A93Yj/h+W1y64/K4VXxUj077v/\n84K4\r\n=W1/o\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f0c2c97ee97160b56d9143c24ac8ac18aef10b7d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_1.1.0_1625293895229_0.06268368729171092", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.2.0": {"name": "is-unicode-supported", "version": "1.2.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "f4f54f34d8ebc84a46b93559a036763b6d3e1014", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-wH+U77omcRzevfIG8dDhTS0V9zZyweakfD01FULl97+0EHiJTTZtJqxPSkIIo/SDPv/i07k/C9jAPY+jwLLeUQ==", "signatures": [{"sig": "MEYCIQDW8lddN6OnrOmL9uHOQ1wpCheCKyDRo6HJzlIKXKqvMQIhALHKmpEkHDKvTIwA1VW68UwbbuEdEL9bP3ZWz8fPnOhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOsQUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosJhAAo//WozRKd6+0l93Unk6s3ir8XDtOU71ypeCT0jlfrCq0GSrj\r\nan1DYVYJwwhi2XLyMU9QNGaheTt7ZqeC9YwQrb8BOrcR1+eH8GkMFqv7G1NV\r\nkky3wG2rt+UXUX25xHvE8j7PnRSepm/gMvio4/iT2cVLRVdUXbiHV0wJs9o1\r\nmNagncOpJmDSb1WhDnZWgWb9v/cUcNpv4cXJd4cHBLXiCgyq//R88Icu4ztk\r\ngNZhJW9CyorMNI8MrncG5SP/1hYADFQDJoBhRhJG6sJoBd4LWx6urf6vWAvy\r\npMP1+WGp93+JtNW/v7SU7Dc2vQfrfcexFYmU7diNNpEx5u+Js9Er+99AhbE2\r\nlob8tnGA1HTAQ0Q/0xuMIWZcjKCQhkk1IX9+A/HUzzJaUUAz0TBjcnbqXyeB\r\nvg+BO5E8XCuJXf4YX5za4CyrBXxA0MFzRO18Zu8seY+HbntUj+Dc9idnCzow\r\nF2ABKcQliu/4/Uq0637YBrUlh/cCmYbH1Zuq7KtdlR69ycWWaibbWogizofY\r\nMj7HJmDHrNtlxrvCEYwGpu9R9vO6kWTc/CU4nq5OTLDxuY69lElX75kZw/9r\r\nD4JX2PdZ1/kCYCIGKokj5Ym+N+Oixabca+zrNfsAToaF0ryYo6iQFzJKmtCv\r\nSa5fEGWGh0iZ4QPlFJbn4eCkDIuOeJe7QLY=\r\n=0Muc\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ee13cffb6ebfe5cd2d442d06ddfecd4db567e576", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "ava": "^4.0.1", "tsd": "^0.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_1.2.0_1648018452544_0.5836375545625783", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.3.0": {"name": "is-unicode-supported", "version": "1.3.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "d824984b616c292a2e198207d4a609983842f714", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz", "fileCount": 5, "integrity": "sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==", "signatures": [{"sig": "MEUCIEHE6YK5TITgTxpONPJuNE7EAoVp31q0Bgw5VU0LBxTaAiEA0i0jRbfCs3zsPZ8nYoGJ02KLRoD0FfBnYeZiIk8KV8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGWaZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprKQ/9FZjARtrnJqWnZf1alx02Ep6r88QcPH88/KmO0CRSSls7wGI3\r\nh+/IClWQn5y0Vr8D7QuE32j3h2pgIBS4YxDQ0vxvImFQKtxiCK2+hMjAHyTb\r\nPioimDttS0iOIkI9S+D30bzzdjNoVkSXB98UzMtIs/RT+L+Bi34JhQjS1oBw\r\neXQXZyYClaTJHhCMGXZpaaZdEe6Afvw8Km0sj+T9gRluOGmMcB0qw0BIzEft\r\nwMDL/piw2c4lkmNpM9DpAsWsb4U/EyXFmtfFk9vUI75XLo7SIgB7xlLMsJhf\r\nqipI5pyH9HxJpB3ugRS4omjDZwWCwIfsihG+bJI3pzIWKgOO+oJaugRH3gYS\r\nIYwoGAh4XYcbYOx3+KjNJ/96AJTRIBPH4nQHOsaiu2FT4FkTQIAaxr17jH83\r\naWstvLQ1GvQQwOvakvbmWbw6C80LjJbidft1X/W45SEFS8zNZ1viuvQpM4l+\r\nT9QmeoUsn9Kej/XTARYSpVagVIBaycy3lf9tj/XhphLUmITak8Cyfyqg32gV\r\nioTUTbELG2KnHkJjO7MmkWpQDJlmG63dljPpF78C3lQEEsH3USmmbMLGt0UY\r\nIgfWgqoSC+CT3HZSDYfvE9I3dshj3vaiEhUGxgi/08WpjJJdwHc41zm7rPWE\r\nkOKCr96ylvtlAScusyg3bbxnvPp2s2uUPjQ=\r\n=55CZ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c80c691dde9e2fcfe3996810858c6672c8f35ad9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "ava": "^4.0.1", "tsd": "^0.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_1.3.0_1662609049768_0.513218337738387", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "is-unicode-supported", "version": "2.0.0", "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-unicode-supported@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "dist": {"shasum": "fdf32df9ae98ff6ab2cedc155a5a6e895701c451", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-FRdAyx5lusK1iHG0TWpVtk9+1i+GjrzRffhDg4ovQ7mcidMQ6mj+MhKPmvh7Xwyv5gIS06ns49CA7Sqg7lC22Q==", "signatures": [{"sig": "MEUCIGoGI3A+EAcERXxVKPJrpdJdlFsUS7fhiC9gvI1e9YJ5AiEAiq1W6fFUdzZR6AuMCNdML9i2om9I3e6afAug7tty7ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3900}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "506f27260df3636555714bf10ed40ab9e6a6c96e", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is-unicode-supported.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Detect whether the terminal supports Unicode", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-unicode-supported_2.0.0_1698588174725_0.032026229254733085", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "is-unicode-supported", "version": "2.1.0", "description": "Detect whether the terminal supports Unicode", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-unicode-supported.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "devDependencies": {"ava": "^6.1.3", "tsd": "^0.31.2", "xo": "^0.59.3"}, "_id": "is-unicode-supported@2.1.0", "gitHead": "e0373335038856c63034c8eef6ac43ee3827a601", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/is-unicode-supported/issues"}, "homepage": "https://github.com/sindresorhus/is-unicode-supported#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==", "shasum": "09f0ab0de6d3744d48d265ebb98f65d11f2a9b3a", "tarball": "https://mirrors.cloud.tencent.com/npm/is-unicode-supported/-/is-unicode-supported-2.1.0.tgz", "fileCount": 5, "unpackedSize": 3958, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEMqnisv1hemFGpwKq0qi4mWKZqslUN1TgHI4z5B43i1AiEAwNwBJFmjmg/NVNZAFOBO7cMVqhqosE9hgzycDnVmCNI="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-unicode-supported_2.1.0_1725863162943_0.488025161975274"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2021-03-17T10:11:13.665Z", "modified": "2024-09-09T06:26:03.265Z", "0.1.0": "2021-03-17T10:11:13.836Z", "1.0.0": "2021-04-18T05:09:42.952Z", "1.1.0": "2021-07-03T06:31:35.341Z", "1.2.0": "2022-03-23T06:54:12.696Z", "1.3.0": "2022-09-08T03:50:49.956Z", "2.0.0": "2023-10-29T14:02:54.928Z", "2.1.0": "2024-09-09T06:26:03.073Z"}, "users": {}, "dist-tags": {"latest": "2.1.0"}, "_rev": "18489-c23727bb2e482fd4", "_id": "is-unicode-supported", "readme": "# is-unicode-supported\n\n> Detect whether the terminal supports Unicode\n\nThis can be useful to decide whether to use Unicode characters or fallback ASCII characters in command-line output.\n\nNote that the check is quite naive. It just assumes all non-Windows terminals support Unicode and hard-codes which Windows terminals that do support Unicode. However, I have been using this logic in some popular packages for years without problems.\n\n## Install\n\n```sh\nnpm install is-unicode-supported\n```\n\n## Usage\n\n```js\nimport isUnicodeSupported from 'is-unicode-supported';\n\nisUnicodeSupported();\n//=> true\n```\n\n## API\n\n### isUnicodeSupported()\n\nReturns a `boolean` for whether the terminal supports Unicode.\n\n## Related\n\n- [is-interactive](https://github.com/sindresorhus/is-interactive) - Check if stdout or stderr is interactive\n- [supports-color](https://github.com/chalk/supports-color) - Detect whether a terminal supports color\n- [figures](https://github.com/sindresorhus/figures) - Unicode symbols with Windows fallbacks\n- [log-symbols](https://github.com/sindresorhus/log-symbols) - Colored symbols for various log levels", "_attachments": {}}