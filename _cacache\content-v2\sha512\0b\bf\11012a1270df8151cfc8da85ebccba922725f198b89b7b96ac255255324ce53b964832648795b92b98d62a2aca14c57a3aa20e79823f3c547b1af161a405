{"name": "babel-helper-bindify-decorators", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.0": {"name": "babel-helper-bindify-decorators", "version": "6.0.0", "description": "## Usage", "dist": {"shasum": "0719480f96a426202e7c7905ab1f0dd076275390", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.0.0.tgz", "integrity": "sha512-IpGN1rO4Simz67oweWu8l4wUjB0nw9eUKWlTbr0Qg7G1eGpF05vyctoScbVa/FywjmA75tgzc6IM3T+QcTDATw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAENFOrWLshdGeI5Tndj1QCmYtednc4XrHEr7EDVcwNwAiATsEUI8gb/YshWyLVWU+zV3uUSmfyCknC1dM1LD5M6OQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-traverse": "^6.0.0", "babel-types": "^6.0.0"}, "hasInstallScript": false}, "6.0.2": {"name": "babel-helper-bindify-decorators", "version": "6.0.2", "description": "## Usage", "dist": {"shasum": "e2edb6e83c8aad0990d85ef837e1a3c6e12dee8b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.0.2.tgz", "integrity": "sha512-tyLnwK41tlyQ8VHiZvYLcoV+Fm1nqdYZHneRfOmjv7Rx/ZCNZGH3byzMsBjKHBTnBsS3TrOFdBj3b40XOM7dJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdOVVfGgHrSX3h/WhffucRGg6Ls66/RgEEcXKs7gO/+AiB5rP9QkCPdbgLBQ63D5LFDYdgtgcuwpkIqo6xOJqRenQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.2", "babel-traverse": "^6.0.2", "babel-types": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-helper-bindify-decorators", "version": "6.0.14", "description": "## Usage", "dist": {"shasum": "974778063f241e1de4325cf8ac9c140af1a4e199", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.0.14.tgz", "integrity": "sha512-YRK3kC/pjOaOXHFthaRZukX2Yx5OTPllukoCCPoSLFPGvnp15S28312q9gFlRYHeiQwZed8/Kb2EP/y/D/csGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCy7kRmG/8aPeQBFCQ6QtsYDa8L1KzEx27U7pEOGIFy6wIhANmhHirtCeGfhn/XKeolYNcvuiJhkGl5nnMmtQjI5CSx"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.0.14", "babel-types": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-helper-bindify-decorators", "version": "6.0.15", "description": "## Usage", "dist": {"shasum": "7762e6feafcf758638de324ac343852f78e4b5b4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.0.15.tgz", "integrity": "sha512-dfGHM1lyG18qCgHu5ol7Ex/g8g/wskB+ZymUy0ZbeVddfWq6mgRgLkAvHX/guZC0VySwtw/BVhf1ABR5Yk+J7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF4mnbIVh+MUyDTElnodLn4rv0+/AkhZX9WWRQ9NQFB1AiEA8kcCsZoLS5f6KDxHFRt51a4sVgrrM/9hRJyBKSNY8AA="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.0.14", "babel-types": "^6.0.15"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-helper-bindify-decorators", "version": "6.1.5", "description": "## Usage", "dist": {"shasum": "e26c0fc2e000db4c309ee59e2e2bbaf8c0b99fc8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.5.tgz", "integrity": "sha512-QEclZiopimXKcQhSXTRruYGkQUsVmc6mLul6d0Mo6eiZY6F7Vsu8Lkafyt9SLjJOoF5ImRI4AMorBzrzkz/3XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCczy0BVt4iKqXAfBJ/dW9jCcs1e6kjrDZHKKpNmJ1lSwIgGORK3PZ7Dl2GYwtcN0oYg4hIaQkQitpOMqqsbwFq8iA="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.5", "babel-types": "^6.1.5"}, "hasInstallScript": false}, "6.1.6": {"name": "babel-helper-bindify-decorators", "version": "6.1.6", "description": "## Usage", "dist": {"shasum": "fef34d2f15ca564d4304b94081e06b03599697e7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.6.tgz", "integrity": "sha512-QJIeEX7q0tZ3aYfZY1JwU8JIqMqwIYDh6PK8Jymtase0dKlO1D/WcH74DB69I9y52IcNyeUHM15VlkX59ppi7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEccsVYRhtrs7bP5WT3G9Wzcvz2RrtDGAqGt7O5RV46/AiAsrnLAt6jn/nW+JZ5V2D3m+25wqUu+gicsv3Ydrm8luQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.6", "babel-types": "^6.1.6"}, "hasInstallScript": false}, "6.1.7": {"name": "babel-helper-bindify-decorators", "version": "6.1.7", "description": "## Usage", "dist": {"shasum": "583ffd5dd2b2a14c4ac3f0cc86562e6372d1397f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.7.tgz", "integrity": "sha512-rkNrYDAtHrOKDW151Z2OBhKBZydNHb/2GY+MSoPc44e3mN3FOWnmHd8fQYx3ltGnivJ3A42Wp2HWkVJ3lkxrOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFyenLK85Scb3vZIDRq+LCoKU9d5pckjy9xu0n5C+AlRAiEAnAS5JOGh8aSU10NhwqwBoFsfmtBQz/CHRgTaqTjbQPI="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.7", "babel-types": "^6.1.7"}, "hasInstallScript": false}, "6.1.8": {"name": "babel-helper-bindify-decorators", "version": "6.1.8", "description": "## Usage", "dist": {"shasum": "75c0a128fa5a69f6cd844f32a097449c0a695772", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.8.tgz", "integrity": "sha512-fjWV5+tkbLiUS9pOesugaoFFk2PsI0rmu83JshSGvtMYvFG83Vcp0YmdEM+azzXcaQ3ydQX26sRaHP3muIR5Ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEP/WIHL9MigzxjESRSlNQ8YQ2mDpG9uDudnFM4BQZEiAiEAmjFj8dlw4P3ffUwur9U7h/lZvnwzf7Bujnhiby/mRTE="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.8", "babel-types": "^6.1.8"}, "hasInstallScript": false}, "6.1.9": {"name": "babel-helper-bindify-decorators", "version": "6.1.9", "description": "## Usage", "dist": {"shasum": "404b455cdf3247e7a79f6e73f483d3bd343a0c3a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.9.tgz", "integrity": "sha512-BV6W1Xn1Okb0/muA8T/ZQh8dnf5n2C7e3wKFrJF0M1C8AV7obGOiLOM6G2Nj9ujsQnvtvBybwh3xeufy2wPBJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuKRZ5dLlWxu/gaYnfXC9E7v885gDstIA+4R5qUD+M+wIgfEYxb872D0NC3lkY54PCDwRARXWRCzvqJv1yM7onEL4="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.9", "babel-types": "^6.1.9"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-helper-bindify-decorators", "version": "6.1.10", "description": "## Usage", "dist": {"shasum": "bfa97a0de6444d388c71648a9fed0ba76c4e172d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.10.tgz", "integrity": "sha512-aR8fHhvoRcItdk72xUGXEADstMTV8Y9HfJNodHoEsM4hR2Hsy03hG1iY96Pg5e76k7uJrbLm0qoZ/SbV6dDaag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCz9iF9zkZJfTSlcMz9ya/IKAAOolZHbAl8ghTONx0uVAIhAOq33fsPMXsxzh/5R1dqeB/l5dzYF0XGyoL86medUaDq"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.10", "babel-types": "^6.1.10"}, "hasInstallScript": false}, "6.1.11": {"name": "babel-helper-bindify-decorators", "version": "6.1.11", "description": "## Usage", "dist": {"shasum": "c221ff5d5a8d7ec163c975893948532d43edcce0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.11.tgz", "integrity": "sha512-GWFcQ9drnGr6/z/sBn2lkgr5zEYmf2Xuxt2o1zQuLp2oHp6Ei6kFJqO8qr0tzW4u5EPqKCKRSqnDI04ERBGmIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+vfbbFUkSZKOnHmMw47WsdZzQGxg+P2W7c7e7ONq1DAiEAhOGt+RYEyNUmmwVn5lCkgm0qSCHkNWhfTarmGkkDW24="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.11", "babel-types": "^6.1.11"}, "hasInstallScript": false}, "6.1.12": {"name": "babel-helper-bindify-decorators", "version": "6.1.12", "description": "## Usage", "dist": {"shasum": "43e3a17e1670d350c150cc687e467113691029f2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.12.tgz", "integrity": "sha512-Wqjr6+OcD9+tqwmfrlHGDOr43j//A9oZHVtps6UKOa1Z//2wJfpCBDyegCh9IpAcTuK+CelA9DLswijc4svxOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTP6+4uZWhtDyivkVyWqNkdMpyEFOXZx/busG2+hdZlgIhAJI8R311C+iqwdFECyRwM2xymvuNiDBnmyPM3PgIbFBE"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.12", "babel-types": "^6.1.12"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-helper-bindify-decorators", "version": "6.1.13", "description": "## Usage", "dist": {"shasum": "3338ddecb393338bc2ae9c85d2e2c7c29615e47a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.13.tgz", "integrity": "sha512-W0OcmSPHOSvCKy6MjSrJ+BCvO2O7tuUMrt10wxNFVN1Q+vLRk7ayqV69UmRVNC2T7SltuowMvzEIDyrsmVALkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDhBr7GZ//XafxDZLAojDP0I0vLfE4Sqb903t06xjNXtAiBr+/k/edLHdR8GvBhjh9t6dt4yMjPYFwQK8sQTEdcHFQ=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.13", "babel-types": "^6.1.13"}, "hasInstallScript": false}, "6.1.15": {"name": "babel-helper-bindify-decorators", "version": "6.1.15", "description": "## Usage", "dist": {"shasum": "d6eeaa9407f6440981156a8d780c2ab4abd6e9ed", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.15.tgz", "integrity": "sha512-47/t2blWh2v28KMsP/1YNLlIee1p4F6Er0LK8B/CjENYCmBumsjo/+bPpeXeqAewPe1PVi1+9VnA2t8ksXXK/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqX0/f43DwlvwDrc4/coJ0qDcDqjo9MOOr5pS8uYwQOQIhAL+QUSO5QbIUGoLyNRLeS8MqecKDfD2Q7Evyxhyj6g4p"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.15", "babel-types": "^6.1.15"}, "hasInstallScript": false}, "6.1.16": {"name": "babel-helper-bindify-decorators", "version": "6.1.16", "description": "## Usage", "dist": {"shasum": "8a4cd37e1b52a91b4061befb2fa838c70ba59ac9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.16.tgz", "integrity": "sha512-mvoOkrbTwSNe/UWcLBHN7lRn+8lN57NK2qZ9NgAF8EqtYrGpQU8r682n6yEdcy4p2eMMEkPxRVYOTDo/5sq2RA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoLlDYoklXOOrYsIZwSJltOGzFqbfjB9DhfhpLAw0wGAIhANlLneOE298Q5pJ5tA9Un/DKknmEm9ITpe6FF5G15Oi/"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.16", "babel-types": "^6.1.16"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-helper-bindify-decorators", "version": "6.1.17", "description": "## Usage", "dist": {"shasum": "5df169469a76f574e19de4005c0d8c6dd1cca31f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.17.tgz", "integrity": "sha512-WHxUPJTixL93bogj1g1tFtFl6mPP0W5EAcAUgLcW41qldDNCIDaNfQe/4Fz2GdOQoveDJseV2oxm8K0l6D2rQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNO1RYkyz4sQcOjav5D8K65Hk/pHubCgZkgVLgtmQpRAiEA7lA7y7wi+Fq6NutgJhLM0VLasQxbMhcTm3GeeS/z+EQ="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.17", "babel-types": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-helper-bindify-decorators", "version": "6.1.18", "description": "## Usage", "dist": {"shasum": "7bf2ad2b65c2897e057879bb133a043ab85fdf74", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.1.18.tgz", "integrity": "sha512-9t6/yBcq1opP7agKoXe3Cf/XWAMK22YE1UlhF9auTejnPIlRm7eZPsRpC3fEmmrxha+Q9AKBDiAbAwqcWcrQjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIA7Ah/voLdCEVl9vmhr2l+TZk6a2qT8WvUXaZt1bg3r9Ah9hi3Dpj3qJGkYlTPCR5i1O/q94wBMgAsTHCg87vcmT"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.1.18", "babel-types": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-helper-bindify-decorators", "version": "6.2.4", "description": "## Usage", "dist": {"shasum": "404e381d951584e9a546f6eeb65c9e9d5ed739bc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.2.4.tgz", "integrity": "sha512-YA90M8K3TQAEU+N/9Aq84RXMp2NzGWKOo2jHepspEdPcxWmqpY/8o1isqoJAes3fTZynkdF8zkZalGrIYssoSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2ElNtKM9rA9MgC7+zKwDjm4GYLk/4PQlNdaa9J33arAiEA7Z/J1jGdSx7MI4TrwpVoTF2bv85NUjixvSM3X6tcEY4="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.2.4", "babel-types": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-helper-bindify-decorators", "version": "6.3.13", "description": "## Usage", "dist": {"shasum": "2fbe1094eaf79f18fcba29d4f4d26b91d8d2e003", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.3.13.tgz", "integrity": "sha512-xwFaQQ11c4y9D+4DZ5AbF8oFM/2CeXAFq8vWKAWg+KnmWwQ0S7SDv6rjzcaAaGNOn0N91rFj96XyCj0wocy3mw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFbfT38rv591d21R7DRnxx9XjMZZlydiJ3i827udCqFAiBD/zS2wgEjVrM7/HO4mDxWlqBkLmL4yDSOiywSSDBhaA=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.3.13", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-helper-bindify-decorators", "version": "6.5.0", "description": "## Usage", "dist": {"shasum": "6222d38811fd9904786113a0b7530a250c4f2206", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.5.0.tgz", "integrity": "sha512-yTg5Py/eqHd3CKvfXdlj/mS50Zw6u5itxYhXhNQSrK6AARUjxw2bQOvIWHn3yB1bezUTwsEEZZCoGu0qoOkAdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAbYTFUxoiL0inVCnuQcw2PqmThrOxF9wXNUUbUBXcGhAiBoYKuTyN87+JxoDarU+mpaumpcJR4YBA2S6yHSD2+QXw=="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.3.13", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-helper-bindify-decorators", "version": "6.5.0-1", "description": "## Usage", "dist": {"shasum": "e5e41800c52e1342f8a640ca9be85e4e0df755e5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.5.0-1.tgz", "integrity": "sha512-dPzL6+uolBf/4GEwlHLzcBLVjs5RR1ppzbn6sjciFIobL3SJQK2mqjlLRVV7W0MOkpZZgLeCTyF1ynfBLN5ORg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDy5uVudTPWBLRnxG0VHpSg2BrgWBJV/cpITAqsTtc4rQIhAPd2mRHwp24EjJxDR2AgQ9ww7xIcFVDzvcGorm1QKNYH"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.5.0-1", "babel-types": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-helper-bindify-decorators", "version": "6.6.0", "description": "## Usage", "dist": {"shasum": "978cb1e1c989296982ec16dfd18b339f2ce4eddb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.6.0.tgz", "integrity": "sha512-P6mbrEK10TJgsAWnEzxgvHT6Cih1UY3SrOXmefiH934rZasx/kw5MPs8qYisxiJRdPUy+Zwptt+DkuiKmga3Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8NgwdXE0N4xlbgYyzfsPesCh+8xUB+Fr5mNsP/Vg6bAiEA3ktITHL3uTOEW5/gI+5KQwBqZPVpdp03cxbkXBw+h6c="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.6.0", "babel-types": "^6.6.0"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-helper-bindify-decorators", "version": "6.6.4", "description": "## Usage", "dist": {"shasum": "df08b9cf56e2718b2d970a515644328e6b2cd529", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.6.4.tgz", "integrity": "sha512-8LwO2MxkUloO5TxJlKiifffRjKegD6CVZUz3hnZEuSTzUbKr/2OcX37be8S9HrL1OmNaVu705Dg6zPb2FAtQ+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRFDQ6uBaPQ6uZq8cB1inPnNZL3uw0Mkku1t3xkPOfcwIhAI54xgHzGN8GQXg/5B34oEBybFkYRWRPUKjGwsQtnRNL"}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.6.4", "babel-types": "^6.6.4"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-helper-bindify-decorators", "version": "6.6.5", "description": "## Usage", "dist": {"shasum": "afb6e0e170bbc5a3f3a1b58de2c81d43e81ce365", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.6.5.tgz", "integrity": "sha512-WTible3VHk04SyRo4ofrWR7Ohx9d32GE31khD/6hBEo/F6HkdZuLeFcpf9IXRdw5H9TxSd+kfDvhk4THVkQyaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6h/3OJ31OmbQEUuPV3XP26oxgUQ5lVV8q2FBZv7BMFwIgDC/JQlHAkfeQasS2gHulF4Sfp3+bViVwoxMonhXhwCk="}]}, "directories": {}, "dependencies": {"babel-runtime": "^5.0.0", "babel-traverse": "^6.6.5", "babel-types": "^6.6.5"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-helper-bindify-decorators", "version": "6.8.0", "description": "## Usage", "dist": {"shasum": "b34805a30b1433cc0042f7054f88a7133c144909", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.8.0.tgz", "integrity": "sha512-iP1e05659XCUaURh14lbNLEdhsnpGxnrkVGKAIZ5UKVUb5v5xh5FtYzr0HTb2QCSPte3Va3E2ufeL8UxKwvAKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGFXbDnB2OkbebjT5C9cEK68+n+4zbVh54NtrekaefrQIgC2OPQbQWZoWMrmjXFmFCSsroKl8jedrUcBsm8QfkVG0="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-traverse": "^6.8.0", "babel-types": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-helper-bindify-decorators", "version": "6.18.0", "description": "Helper function to bindify decorators", "dist": {"shasum": "fc00c573676a6e702fffa00019580892ec8780a5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.18.0.tgz", "integrity": "sha512-E0bQ6xSStUCJjfpu7Ebpq7lLwdWF1WRcChBqMidq7lJqxGJJdEvK5rfSr2GxB7ZxRdc1phYgoc4UDz/hbPNyEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvy7nIOP2yOq5S4KM47dWb2vCdhyewbUar/ogMEhprLAiEA0S8rC7xHCXdaruDm9abmmC4/kbihMl9Ai68GrVFZem0="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.0.0", "babel-traverse": "^6.18.0", "babel-types": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-helper-bindify-decorators", "version": "6.22.0", "description": "Helper function to bindify decorators", "dist": {"shasum": "d7f5bc261275941ac62acfc4e20dacfb8a3fe952", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.22.0.tgz", "integrity": "sha512-75leXU2k0meWlAavyp6y/6GLLLM60zBCRtICbOgKZVNIYBbFCyk728JSePFXIOAr5LIZhDdvxJJcZPN1x6S+WQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4s5ePpfhiLwSA2NPUJ5vnALHe+E95qPD+ZElKmwzPggIgYUuh/lVrp80ITxl2t4+aA4Jf1skGUciPby8EHoWJfTI="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.22.0", "babel-traverse": "^6.22.0", "babel-types": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.1", "description": "Helper function to bindify decorators", "dist": {"shasum": "6eed43fb92e029bbeb3451328b137632f773fcd6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.1.tgz", "integrity": "sha512-GNsljzYxI2i0BzxNKlcIX3UJX8/noG42YPd379XZPz8hPKMWiUQxCmBSo+y6De6a9dF/iktBD4kqP9MEZfDbqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICxd/ESNMHM3IkZkMtSEd1wzmCoHbETu1LVupiZzigV6AiBG3WKPQ0drgG5EFmKTx7ufwa6SI28molEWmRMgFPqgPQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.3", "description": "Helper function to bindify decorators", "dist": {"shasum": "2b8ed121e4ec39920a9d835ba05d2632b02986ba", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.3.tgz", "integrity": "sha512-6n1LJ6vE7zzV2BeP3g/7lAJuHasrHZI42ep1NxnkkYLtdGfde4oD+GQEvgW6/44daaaHQ1pL7W294hG+JenhoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAOqOdVfnJapxXOPIqK8y8v2uFi08AgY0EkAzbHwcuGUAiBduXGWTu7DJuPZHIAbwxvfGNtZr0g66p35V6+Kcaabbg=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.7", "description": "Helper function to bindify decorators", "dist": {"shasum": "646d86e725bd862e9200c9bd371cefc9430d050d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.7.tgz", "integrity": "sha512-UHx2SxmwH6T0U31QyIZoQiYa4V54xDPOml/lZLH2d8qkuYVujPNQmBYFuMx58bagqdPi4cDa47Hy+6U+n2fghA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuxe1jWJ6f3VcGDT53O52Kp07975sOlHBdBBdUhuR/KwIhAPyWartrUaJOwrsOIaaQ2bB5F6b3HuMDaKLbaFZiYaiL"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.7", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-helper-bindify-decorators", "version": "6.24.1", "description": "Helper function to bindify decorators", "dist": {"shasum": "14c19e5f142d7b47f19a52431e52b1ccbc40a330", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.24.1.tgz", "integrity": "sha512-TYX2QQATKA6Wssp6j7jqlw4QLmABDN1olRdEHndYvBXdaXM5dcx6j5rN0+nd+aVL+Th40fAEYvvw/Xxd/LETuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDD9GnAzhoEQmKKdEqYf/VEJu8Pz2m6RusaI2VonPMSYAiEA2eMCmDG6Chnv4NeTCSW6V9FSBwgaYpBxSMsuGzcy6oM="}]}, "directories": {}, "dependencies": {"babel-runtime": "^6.22.0", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.8", "description": "Helper function to bindify decorators", "dist": {"shasum": "1600a4b5280d172e0cd751d7f00cf646328ee7d6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.8.tgz", "integrity": "sha512-T68CZ92dTCmtrsFcjahyIX3uD3juuMjH22t3D3pcYJB2KjGuN2g/H1wfuJSkQHmAaN7m4LgSEcu6o2b4SKw94Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUS0Gt+AsqDeWdU+OaR0elRGuAgrOtUfaIgFRoTxvUSQIgGc1kaUd6IfMC4nAYX3UYqhnbUXorLPcMp0b4KmQI7Cc="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.9", "description": "Helper function to bindify decorators", "dist": {"shasum": "46b2e90d1c90abdc8b5b1658af31e534fe468284", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.9.tgz", "integrity": "sha512-1/siQ7AiucOdWw0XXFP2SqZlsuyPCnZn9gC7roa/kd4wqnTi6IIF8H9ii7pnUZiiJIG6j7nC7X+gwjw236kZmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKPNtrTvITAZA8olJeJE0n2lVyasH1DL4pJz3EBDm5jAiEA3WyW3RI1kk2FICOGt3FIdwNbkcGWXsyzRv1u0eW4OT4="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.10", "description": "Helper function to bindify decorators", "dist": {"shasum": "bc633f4412fcc7402cf0bad64bc5224ec487c790", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.10.tgz", "integrity": "sha512-sTjBuQLQk+HsAwXmir+PKXEJ6vd6oEb/XUeEyHJM/WaXd39OP8B7/Ag/EEMDveGSrlZK5R8nJ0gpnpWU8z9NWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKpaGRbS95+Z+CxHDnx+XL3UFsn7sWVyAAp0vF6DpnUQIhAMIxvFH+Ip6GWXqCJRLtCrBVaaCu2S2F/Deu363b5NUv"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.11", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-Bw6lizr1OzZL0ljs+b2v4MGf7NhC9J18IB4TE4ZqHjBMihOypOfNpD/8uBGv1A6G3DuOgurHgVeZO6ag4F5IKQ==", "shasum": "a2caf7de4004b576beca5a2358aee0a1a758cee5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHGn+po1Jh4TmHFIW8108GB4Gw1LMnTcdaBLhbXuCwhuAiAchkkCz3+Ayqwls/2auZ5XgK5FGUFZFNAWA60vtWdFnA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.12", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-KBkYw/RrKOsFbfcmr4Deg1DI6btXOALRP7OcquqT2gmIzdfaM/zMjtq1et1/wtYPh/afk/4osMxSMDGRyLe/aQ==", "shasum": "39455b405345af86ec2398ab69c0a56add843e21", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGbNP9V1wIjFor9VWUCA29N7CM/gJFEpLKgKsxduR/phAiEAo+j0KMaKyvR1BEQvunSRItbx24k4vqPkS09bi1UkFM8="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.14", "description": "Helper function to bindify decorators", "dist": {"shasum": "7980c4dd2e0eab1c923f9a42a7dc22bf26dc9fed", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.14.tgz", "integrity": "sha512-iXSwH6TY3YCCsUAFPFyVc8rUc+D01fGYPuXVV93uq+2xqgY/EaP/F5G2oucdKCLs9CW9aAMmi2UmTaBactKCKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAZ2Q0LQ58JPjrYHAwMwCuZbtLdVNarP/OXmZBVki+bQIgSBkn5CeDzwSrV2v8aMYEZGnufkKRMBhA7Cqi3yhlsuY="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.15", "description": "Helper function to bindify decorators", "dist": {"shasum": "3499c99a90988ec0041954e57064f6b04f5fbe72", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.15.tgz", "integrity": "sha512-4V7K2U1UDKdEoW8ZX/ecru9v1lYCokLa5lO5rNJ2+ePTim42S8+w0FkLyrgRyVnQxfon2vmyZf//dK7ERFGMMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2iq6zprcLIQi2dojqQoTS4jyQDEkfBhPrfqvWfdmGAgIhAJ+qaq+WWsISEOgOsP3x2Hi6N23EjIcl2BCpm7OcdAIs"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.16", "description": "Helper function to bindify decorators", "dist": {"shasum": "e59e274c31c403479b3435cef27ef688b35417da", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.16.tgz", "integrity": "sha512-TgkcvxpF8E4WioOn1xt+my8X3+IUuY1PF8LCxRVHrZOxZ5OUrbKFUcWY1YfA8rTLpUupoDuW+RN0zl6mLbg11A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDInvt3xjphiJ8YJC9gX4dXp20AEFO1ij0kWpWArYfLWgIgAKfQQ1vPcXqOkk0ayC1dFXPw9a7lHY5amYvzcSqNHwc="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.17", "description": "Helper function to bindify decorators", "dist": {"shasum": "5dc63d8e678b89b13d490ee462efc3f83d54938b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.17.tgz", "integrity": "sha512-fuWleJIhz6WRMguIS7GDmG1xEolR3csuC+W8BjPnQqbU1U9Knd10elH8fSPq1wXfnVJ10YzKT2xPmWsxnKX1GQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHs4IdfYMly6qVS2jv7JoQXYcD0kHyDf1szKxrF2SY2lAiBkbdvWB4h0y+5LQABGCXbIM+OSVk9/0uwJJswKU76lOw=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.18", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-z05eQmg8ngQJOXCykzLd3omfpcrIs7gIr7U/7DWdZ357fIYn0myTCBoc2HOHEHZRtd7OsIitQo/SW5s2ifcrLQ==", "shasum": "8b9cbea9e7f8b4b5ce87ae0ef99f711d79ff084d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDezUf3fDDQG37Aprsh5wIVcanLbZLqH0PD9OqfTc8ibQIgW8J6A2GaHtrfhI6pqUAapT30ni8W9J0lBn0qcyX45Eg="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.19", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-Z9ceKMas/dDV19pHy8ZEqkX9isju/aCGI1mqxtiANez+ISM3Ysqj6hsbk8QOxqw5gyYDfKHwkZtQx47g33GZXw==", "shasum": "442cb30a984519860952de11e6e71b49ba7e62cf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGclj+5BqiNuHF/4utK6qA9bWGq8wocOU6TLsRKTS0MMAiEAjstcEGX9HnmY2mz2tdhyf5S1scHXHefAuLPmBpMbqyU="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-alpha.20", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-rYrnPIySZ5mRPrOPYEdyfbx2DrRRmDedMtItRLykyz3KIcdRT6EpVHzalZWydKloJsYsPaCuLnUTTLMrGSsf3A==", "shasum": "336ec7417b2f8ff4ff1f5b14fa0ca834630677e3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID31uxWHkaRrCz4B4nqUfIWHs4xp0ZFvcvH8DX8XBunjAiEA1tI78Lm6lqFa7vtmurHMzvai0K8AnyiOCmZnYdSgoZ4="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-beta.0", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-hh807EJc0HXusB0+21qBCloNBh+OJ7oiQ1Ze2YEtIIuKGcuVK9PEQM3/5sBs3nFFxgrKpSTsHwuK2TMgLHGrqA==", "shasum": "e1cddb5f7ebb89b198e150bea1b0d13f7224a8ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjUBHIat878KZlM3e3c6u8FmvpZhaIDGf7hV28SqhwKAiACcE4TF7fmMHKrIu07lA/wNNQTmkcVfKcXaZ0J+2IyKg=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-beta.1", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-i7FfRdjjnJGwPrueOpOSeYeuZH8gBII8oKpBqmOOFq2dgJdx+UKKxo/SI/MUqblvJu6PAv/cbErgbK2xLkHq+A==", "shasum": "6c607e9f26e6d454006fdf019f3ab0945043fae9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDfEGoaNvUyoFsHLTMDaUKZCo31SaHadGx66narexV6RAiBp+t7MgSKhKQvKKxE2rdN7NG3I/SX/0SEEBuh7kidLtw=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-beta.2", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-uHp3PyznD4db3IttBZcdxQZTlXH3TGXS/Oc7Z9n+wS/TaMZUrXz+EkGxUnSYKWW2r7fLBUygKQz5TZExdTtScQ==", "shasum": "22ba3d1163aace1b257ac5682fda3d084f3d42de", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC8EPf46wzRcV48+vhVoNBm6M9150lSRbQQD5biHM1xiAiEAhzO3Dhaj9WdJ8hulURhGDGkEQPD4OR5txN1Pq5nQWqI="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-helper-bindify-decorators", "version": "7.0.0-beta.3", "description": "Helper function to bindify decorators", "dist": {"integrity": "sha512-a+ue2Dzfv7rTrHs6WlvQUbqXlY1dlFqRFqz8Wv1ux1Tdv+cFrvnx3x0dAsNb1KSYk3T6eCPEpmubxlx0IYOPww==", "shasum": "1f6c06845b66f606658687ce433f8c14d032c196", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGnVhoOuCHguj09jylqYRa/B7fio0BItR+cog6pqYfnqAiBaluLZ6OHpmJmQXWZXeQ2CgwP0vd3dXcpeIpBHHvAMYA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T03:59:57.090Z", "time": {"modified": "2022-06-13T03:59:57.090Z", "created": "2015-10-29T17:53:37.648Z", "6.0.0": "2015-10-29T17:53:37.648Z", "6.0.2": "2015-10-29T18:08:05.917Z", "6.0.14": "2015-10-30T23:31:58.021Z", "6.0.15": "2015-11-01T22:08:08.539Z", "6.1.5": "2015-11-12T06:49:47.764Z", "6.1.6": "2015-11-12T07:33:30.141Z", "6.1.7": "2015-11-12T07:37:59.373Z", "6.1.8": "2015-11-12T07:40:59.880Z", "6.1.9": "2015-11-12T07:46:39.073Z", "6.1.10": "2015-11-12T07:53:26.205Z", "6.1.11": "2015-11-12T07:59:09.077Z", "6.1.12": "2015-11-12T08:48:28.277Z", "6.1.13": "2015-11-12T19:58:11.557Z", "6.1.15": "2015-11-12T20:19:03.318Z", "6.1.16": "2015-11-12T21:34:44.129Z", "6.1.17": "2015-11-12T21:40:50.046Z", "6.1.18": "2015-11-12T21:46:55.602Z", "6.2.4": "2015-11-25T03:12:36.766Z", "6.3.13": "2015-12-04T11:57:03.986Z", "6.5.0": "2016-02-07T00:06:50.254Z", "6.5.0-1": "2016-02-07T02:39:37.347Z", "6.6.0": "2016-02-29T21:12:23.436Z", "6.6.4": "2016-03-02T21:29:23.871Z", "6.6.5": "2016-03-04T23:16:28.362Z", "6.8.0": "2016-05-02T23:43:56.824Z", "6.18.0": "2016-10-24T21:18:39.579Z", "6.22.0": "2017-01-20T00:33:27.552Z", "7.0.0-alpha.1": "2017-03-02T21:05:35.829Z", "7.0.0-alpha.3": "2017-03-23T19:49:41.295Z", "7.0.0-alpha.7": "2017-04-05T21:14:09.630Z", "6.24.1": "2017-04-07T15:19:12.775Z", "7.0.0-alpha.8": "2017-04-17T19:13:04.238Z", "7.0.0-alpha.9": "2017-04-18T14:42:09.985Z", "7.0.0-alpha.10": "2017-05-25T19:17:37.790Z", "7.0.0-alpha.11": "2017-05-31T20:43:49.775Z", "7.0.0-alpha.12": "2017-05-31T21:12:04.406Z", "7.0.0-alpha.14": "2017-07-12T02:54:17.993Z", "7.0.0-alpha.15": "2017-07-12T03:36:35.200Z", "7.0.0-alpha.16": "2017-07-25T21:18:27.625Z", "7.0.0-alpha.17": "2017-07-26T12:40:00.150Z", "7.0.0-alpha.18": "2017-08-03T22:21:30.994Z", "7.0.0-alpha.19": "2017-08-07T22:22:14.620Z", "7.0.0-alpha.20": "2017-08-30T19:04:34.616Z", "7.0.0-beta.0": "2017-09-12T03:03:00.894Z", "7.0.0-beta.1": "2017-09-19T20:24:49.181Z", "7.0.0-beta.2": "2017-09-26T15:16:02.780Z", "7.0.0-beta.3": "2017-10-15T13:12:28.965Z"}}