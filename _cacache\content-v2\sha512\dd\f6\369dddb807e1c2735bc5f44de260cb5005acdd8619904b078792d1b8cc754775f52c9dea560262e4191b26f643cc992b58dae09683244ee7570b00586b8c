{"name": "@babel/traverse", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/traverse", "version": "7.0.0-beta.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9808b6916dc701bc94ea6b32b44b16684766394e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.4.tgz", "integrity": "sha512-b8wIugf9k+nFBhVvDdGDxMvFXblYnBH2zahAcpe0u3/fT0/tt3usAGJmT3ewIZuLvfFlemHPYMsD7ktT9wOUug==", "signatures": [{"sig": "MEUCIFHqvT+mo6ssvwPRQD0663cqwo86NGQUGJFZoTesFOzIAiEAjZs+X+hVy8isfwVrPGmBG/wgLqzeHEI8u6NQMHPyZYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.30", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.4", "@babel/code-frame": "7.0.0-beta.4", "@babel/helper-function-name": "7.0.0-beta.4"}, "devDependencies": {"@babel/generator": "7.0.0-beta.4", "@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "hasInstallScript": false}, "7.0.0-beta.5": {"name": "@babel/traverse", "version": "7.0.0-beta.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ba051a096396f0e5a20a3e66497243c2b52a17b7", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.5.tgz", "integrity": "sha512-UUm6AP+3F7k6Q6B8tAd9VxwKvI5Dm1jngC6C38gtMcKxcyn0zVVtM/Dpj9ef12MT2pO6a/asMI+F8140AaCZHQ==", "signatures": [{"sig": "MEQCIHaMKXPvtfOk7A7hDjZ16byQmot3ZS2PFeW9R3QO+j67AiAhrP17NJNlu0Bu1oCDgi3uNhrsXQZih+0hVmiEzR5IgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.30", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.5", "@babel/code-frame": "7.0.0-beta.5", "@babel/helper-function-name": "7.0.0-beta.5"}, "devDependencies": {"@babel/generator": "7.0.0-beta.5", "@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "hasInstallScript": false}, "7.0.0-beta.31": {"name": "@babel/traverse", "version": "7.0.0-beta.31", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "db399499ad74aefda014f0c10321ab255134b1df", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.31.tgz", "integrity": "sha512-3N+VJW+KlezEjFBG7WSYeMyC5kIqVLPb/PGSzCDPFcJrnArluD1GIl7Y3xC7cjKiTq2/JohaLWHVPjJWHlo9Gg==", "signatures": [{"sig": "MEUCIQD5Qv8KMxozL/WKvmBduQdCJJdKFPPhmLtfKwH3WN1sNAIgR5PjrqG3uMlnfaGyyhnzzT5lUR+EZCZDAmhUjfbLpI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.31", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.31", "@babel/code-frame": "7.0.0-beta.31", "@babel/helper-function-name": "7.0.0-beta.31"}, "devDependencies": {"@babel/generator": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "hasInstallScript": false}, "7.0.0-beta.32": {"name": "@babel/traverse", "version": "7.0.0-beta.32", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b78b754c6e1af3360626183738e4c7a05951bc99", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.32.tgz", "integrity": "sha512-dGe2CLduCIZ/iDkbmnqspQguRy5ARvI+zC8TiwFnsJ2YYO2TWK7x2aEwrbkSmi0iPlBP+Syiag7Idc1qNQq74g==", "signatures": [{"sig": "MEUCIQDILFlwWoAvJYbnD5bWWUtMMM3w01uyWLPEVp1C34uj5gIgAq0U1yLAwhb4HZTY29tKKBFD8gkmmDAodD6QhMeyFGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.32", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.32", "@babel/code-frame": "7.0.0-beta.32", "@babel/helper-function-name": "7.0.0-beta.32"}, "devDependencies": {"@babel/generator": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "hasInstallScript": false}, "7.0.0-beta.33": {"name": "@babel/traverse", "version": "7.0.0-beta.33", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "680be9185ca9f4a7b264adddecb3807deca33ea9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.33.tgz", "integrity": "sha512-UpgHmbzMqwXPn0Mt60BhhxU0tO1kOekcpo0hTXAYscu6XJSevvVoKTF7Lg1KqOjw3g9z8qCq7HgMd3SrrixXlQ==", "signatures": [{"sig": "MEYCIQDrIyfICyK938rf5TuAlbRAdlDZjGEyAlLK6SA9r06YvgIhAMsPH+B0jkEFipEb1bC/6aXGT4rbbhZ6+YFJlp+n4c+/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.33", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.33", "@babel/code-frame": "7.0.0-beta.33", "@babel/helper-function-name": "7.0.0-beta.33"}, "devDependencies": {"@babel/generator": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "hasInstallScript": false}, "7.0.0-beta.34": {"name": "@babel/traverse", "version": "7.0.0-beta.34", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c352fc560e1c78198ee52252c780fe848235fdfe", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.34.tgz", "integrity": "sha512-FmqSqGu+x3kjzWOww7Ro9Od7BPNCwgqZOrzj9hmvACCrxXcHIhwqFG+mFZSDfew35QlAh54YIfWGq0xGW5ztJw==", "signatures": [{"sig": "MEQCIHsdRm8Nz+88y6DSHdRmDJIfS8NROp14NyH34K8G5QHBAiBjGeavh/CNxnA20JCthzxNsygB2CUXLs+LIKqLaB44tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.34", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.34", "@babel/code-frame": "7.0.0-beta.34", "@babel/helper-function-name": "7.0.0-beta.34"}, "devDependencies": {"@babel/generator": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "hasInstallScript": false}, "7.0.0-beta.35": {"name": "@babel/traverse", "version": "7.0.0-beta.35", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c91819807b7ac256d2f6dd5aaa94d4c66e06bbc5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.35.tgz", "integrity": "sha512-oj2mjz/20iiDt+X0mlzE2IEkzLyM0nmT1zSUy/6i6vyzitVeoyRaHoM7O81gmAHSfBSqyjWRU0OuD9VIUgj8Vg==", "signatures": [{"sig": "MEYCIQCi0+wkE/+SsZs/I26DY43BlGRmREth9jz0/0+BepITQQIhAM3ZDl5g9tNU8HaiAuEMhHlzJ4Vo3Yms83F00SL6CU7N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.35", "globals": "^10.0.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.35", "@babel/code-frame": "7.0.0-beta.35", "@babel/helper-function-name": "7.0.0-beta.35"}, "devDependencies": {"@babel/generator": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "hasInstallScript": false}, "7.0.0-beta.36": {"name": "@babel/traverse", "version": "7.0.0-beta.36", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1dc6f8750e89b6b979de5fe44aa993b1a2192261", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.36.tgz", "integrity": "sha512-OTUb6iSKVR/98dGThRJ1BiyfwbuX10BVnkz89IpaerjTPRhDfMBfLsqmzxz5MiywUOW4M0Clta0o7rSxkfcuzw==", "signatures": [{"sig": "MEUCIQD2/nIe9eXfKbHMKSQvMQ61LM1gj/K63jS5Ozuq4ri7/AIgXMirBIFDlYY/Pa3cyYnB7wzK2W6/9HeVdAlLy2QqYeY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.36", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.36", "@babel/code-frame": "7.0.0-beta.36", "@babel/helper-function-name": "7.0.0-beta.36"}, "devDependencies": {"@babel/generator": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "hasInstallScript": false}, "7.0.0-beta.37": {"name": "@babel/traverse", "version": "7.0.0-beta.37", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "50fe6d841521cebb1486cf5e398f34b007a86812", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.37.tgz", "integrity": "sha512-t5ApLRWKOlpShf91YWIqjtq97dfhewOzg5sheLnLwftrV1KcCQEl+nelyv9cVCJIkbjlpiImpjMQ6PNR5/o1vw==", "signatures": [{"sig": "MEUCIQCqibmGPtOqEkF0WaQj0vyY2IQwzNeTs6g7K5SYBdCIGAIgDCRRj2jB8F0147flCXR8Hy0s9eIGDecT8pCQ4qOzoBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.37", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.37", "@babel/code-frame": "7.0.0-beta.37", "@babel/helper-function-name": "7.0.0-beta.37"}, "devDependencies": {"@babel/generator": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "hasInstallScript": false}, "7.0.0-beta.38": {"name": "@babel/traverse", "version": "7.0.0-beta.38", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "56462b91753dd5c6faf36e56a77c64077ddb85b8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.38.tgz", "integrity": "sha512-qbrc59aPCnMCj3uroG7QXdFMmrFLFvEfcwfVzmF2MXh2a7OhRzs73g/PNN8Xb0W/4Z1H4ZTY95CczmfDmXapnw==", "signatures": [{"sig": "MEQCIHYZIYFjlAbdDtj/P6PyV2txJdZLyu2SSM7NtFsBLSsrAiAhc2Rvzwi6jaB25oddkkNwPXRnlIFtDb0J66UU+bwJig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.38", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.38", "@babel/generator": "7.0.0-beta.38", "@babel/code-frame": "7.0.0-beta.38", "@babel/helper-function-name": "7.0.0-beta.38"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "hasInstallScript": false}, "7.0.0-beta.39": {"name": "@babel/traverse", "version": "7.0.0-beta.39", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ccb5abfb878403a39af249997dd6f36136de7694", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.39.tgz", "integrity": "sha512-L/MFJCUTiiK4wAN2nCEEc67yYm5fMtsVtWOizCHgPi45iYeqI3Zp7mL5RMrLqd7cCn6WpsLWVHuCGhDyDIONjQ==", "signatures": [{"sig": "MEUCIQDBuvEQXHqhSKR8E6MZc9FJVhufSZcTIQSDflCrDSNyzAIgW5S/+kqiEl8T5lL4vKz6wXr+HQKOc6W12I3Q2MqyMG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.39", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.39", "@babel/generator": "7.0.0-beta.39", "@babel/code-frame": "7.0.0-beta.39", "@babel/helper-function-name": "7.0.0-beta.39"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "hasInstallScript": false}, "7.0.0-beta.40": {"name": "@babel/traverse", "version": "7.0.0-beta.40", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d140e449b2e093ef9fe1a2eecc28421ffb4e521e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.40.tgz", "fileCount": 27, "integrity": "sha512-h96SQorjvdSuxQ6hHFIuAa3oxnad1TA5bU1Zz88+XqzwmM5QM0/k2D+heXGGy/76gT5ajl7xYLKGiPA/KTyVhQ==", "signatures": [{"sig": "MEYCIQD2DgjvJsp+ZhHWXqdySJrqyJDtc0MWYeWvlG//fBq71AIhAISEOLHrPyHq0voPCoF4lpODL3VI+7PWJWohZlWIAnVM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155337}, "directories": {}, "dependencies": {"debug": "^3.0.1", "lodash": "^4.2.0", "babylon": "7.0.0-beta.40", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.40", "@babel/generator": "7.0.0-beta.40", "@babel/code-frame": "7.0.0-beta.40", "@babel/helper-function-name": "7.0.0-beta.40"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.41": {"name": "@babel/traverse", "version": "7.0.0-beta.41", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1615f6fa87382c34511be8be1cd083eba9b1ae88", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.41.tgz", "fileCount": 27, "integrity": "sha512-EkWyDxYvaGaFjV+7xsGBeTsryqj5+oMo6noN/fhT5j5C4bpPuNwWnl47Ivvg10bk6v1NpfCoI6NHOWj3x+v9Ag==", "signatures": [{"sig": "MEQCICrQ7S7q1by4nXx6gJG1hz+5jVtJ56M1qWHCbutzfRNqAiBO/YSvA2MSlN0dPmXUr9V5AaMZrauMEgz9P3lWabyd/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154890}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.41", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.41", "@babel/generator": "7.0.0-beta.41", "@babel/code-frame": "7.0.0-beta.41", "@babel/helper-function-name": "7.0.0-beta.41", "@babel/helper-split-export-declaration": "7.0.0-beta.41"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.42": {"name": "@babel/traverse", "version": "7.0.0-beta.42", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f4bf4d1e33d41baf45205e2d0463591d57326285", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.42.tgz", "fileCount": 27, "integrity": "sha512-DZwMuZBfYVIn/cxpXZzHDgKmarW/MWqplLv1k7QJYhK5r5l6GAac/DkKl75A0CjPYrD3VGco6H6ZQp12QaYKSw==", "signatures": [{"sig": "MEYCIQCNkk9KFwfU0pdhyYfRW5Ojh8jusZnVuikezrPp2mYubQIhAJUWyyQWiZiIXOvp1/0lqyufi3026FhXcI1eVvJjUgm5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154890}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.42", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.42", "@babel/generator": "7.0.0-beta.42", "@babel/code-frame": "7.0.0-beta.42", "@babel/helper-function-name": "7.0.0-beta.42", "@babel/helper-split-export-declaration": "7.0.0-beta.42"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.43": {"name": "@babel/traverse", "version": "7.0.0-beta.43", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "dad8700f1e51ed960de22c5060bf1c3e8c8fc494", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.43.tgz", "fileCount": 27, "integrity": "sha512-8uKS8xoSjT0v3lKfkTyvw8IQ03Hfo6i0uJDX8HAqgYJ33ZlIapWLA98vlfb//XyXcSIfkbM+g4HV8FNqEYgbAw==", "signatures": [{"sig": "MEUCIG3Geh/EO8ZSKBTn6BmHckgd1MlkgF82G8G2sXfMsdAVAiEA7sU1/Zca6RmUTWxnfL3eL4TL2Yiqb+fam5XFtNP6PXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142258}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.43", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.43", "@babel/generator": "7.0.0-beta.43", "@babel/code-frame": "7.0.0-beta.43", "@babel/helper-function-name": "7.0.0-beta.43", "@babel/helper-split-export-declaration": "7.0.0-beta.43"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.44": {"name": "@babel/traverse", "version": "7.0.0-beta.44", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a970a2c45477ad18017e2e465a0606feee0d2966", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.44.tgz", "fileCount": 27, "integrity": "sha512-UHuDz8ukQkJCDASKHf+oDt3FVUzFd+QYfuBIsiNu/4+/ix6pP/C+uQZJ6K1oEfbCMv/IKWbgDEh7fcsnIE5AtA==", "signatures": [{"sig": "MEYCIQDhIdnsX+DvjDQ9LxE+2j0peu1EbbewUTBbzyt4Px6iwwIhAKpQm0TMeqHvX+uzS1k//eTs5r+OE75vyZJWJKrNR9BV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159540}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.44", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.44", "@babel/generator": "7.0.0-beta.44", "@babel/code-frame": "7.0.0-beta.44", "@babel/helper-function-name": "7.0.0-beta.44", "@babel/helper-split-export-declaration": "7.0.0-beta.44"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.45": {"name": "@babel/traverse", "version": "7.0.0-beta.45", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "fb919e49040490c6132c6e7dda4b634f431155d2", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.45.tgz", "fileCount": 27, "integrity": "sha512-Ulgrq51aG6mqO/4xF5endMUf8g8C41+dXJd6JUedHnY91OFgP/ivsonvwl2feLkyRR07zREosdWvbgJ87AOinA==", "signatures": [{"sig": "MEUCIQDJKZ79MAhaKKdSSzKoB6eISScVp1brL9j8C6uWgEM0MAIgZzQCrI7kNGI2I6tofXyPI8dVUAG6LKSSyo1lE0S56j0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T23CRA9TVsSAnZWagAAjsUP/2mXjtwtnqwBKVBWMMxV\nQzGaoULIBtKiUOJnVX07xO0RSy98998BVqvl0zAGJe161NhwWhQhCT4qXdg1\n5Z2rtJ/ehEnxv2LzjY117v0fPmKU5eHptB0Bwlj3jQT7wONCzwMWLRTf4zPO\nnGA3I7j/8oWJuJsXCffAaMv5pACik5KuS+dF9d82dPaN7q4SIuTfj1JKT5rJ\ncOwMz2NX9tlF7z18G0GyRkk5Kd5DB6G+a/P2M4AxoAyv+fz7ROI0jmp5jYJ3\n6bBKJe+EJPnAkPcmsjSG45hICib0Z/U+9+SJpY+d6K3gW/ZCL+GQqmyR0rXu\niwGqbrkYAbslw1w9a6/xYySHP9byHf55WvT4+G8Fu8Vuyv4XQsP0VP2HsGEs\ngkZ8cH7tDSKQsLmEu9bJYJdvI6ANJpZzjV6p87JbR5rxcSPw4YifYY/541yY\nEP0dXu/chIIYGbxF+3pmn4WkKKzk0+9YS2Ri1GDdQtpSGOVufbavRbnTLLKU\nJBV1oqMfzHPv4MXOWq2zMzeIjFksTFfvgOmeaoR/zKBAx1md35ZhaYKWgV75\n8zqnpg2E0MhEkBhreAbkhVHIH0w3TDBHcNSZqLvGxjSip+piTNqLXiH+b7gh\nA/kVaPa43iJONFOiGsuIlnoSc/fanL1QMdSZ71CmJVYCCDj0iXSJnCSVjjT2\nHit0\r\n=kYG/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.45", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.45", "@babel/generator": "7.0.0-beta.45", "@babel/code-frame": "7.0.0-beta.45", "@babel/helper-function-name": "7.0.0-beta.45", "@babel/helper-split-export-declaration": "7.0.0-beta.45"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.46": {"name": "@babel/traverse", "version": "7.0.0-beta.46", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "29a0c0395b3642f0297e6f8e475bde89f9343755", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.46.tgz", "fileCount": 27, "integrity": "sha512-IU7MTGbcjpfhf5tyCu3sDB7sWYainZQcT+CqOBdVZXZfq5MMr130R7aiZBI2g5dJYUaW1PS81DVNpd0/Sq/Gzg==", "signatures": [{"sig": "MEQCIGl7sjOwD03LXlUKYgoU6zT1RzcHHLqC3Cveei/dic1bAiBSMhO26pSdJ0BfTnmAgN9rb7Z24JA6hD/dhilu62LtPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHhCRA9TVsSAnZWagAAqE4P/3efUqekaA04ArPLUxjF\nFT4d79EbzQR8YQGGGjTq/nSZWoJ+ncasx3D7+V15aeV9CDYbeCPMUH+obIs9\nFeBw+u7SKS5syAG2C1evcgRMwDSJ1VcyW1d7aYFAfsSBju/cAdmt+drtdb3M\nEi8A67zU6hc7UtogV8ApGuvIG41FiI1v0B3Wrsfbb8m9RecijNL4N8b9skd/\nSSgCBbkEWxfWwKl7uxzcjusqizbLf5bIi0IoWrUKDbGmYYBpNnxsxp3VCkZ/\nw3lnK9xcfXSkv7aaQnq+/Bkvc+WEMpS1R/ucqbLSYJxYmWYmWmz9eLFYS5dW\nzAEwitsybBeManATzO9FnNMcHAhBAET5IO2OK1eEYGug2CgjPGEg5HhUeYZS\nsZWzHYz9LzBqvwfkHxv8dgxIFgfkRxs7JxrVaAcJ4trjPSgMMNFGCplg0BnL\nZpnIgjPAtnJ3yTH54sRTlkx2r9lWF6VLsySNjBaYsv2wIUswQ6wZTFJy9GXK\nv73GH1zW/J1DLEMlciKnZMHOmbl4i9C+kWkfOcbuq5sKYYKPlaLabAF8++o0\ndK1qpqav6+U22CaQZhoI/l20Ev+8Q0q3cB4J1NxSonRWtDq+97csdS1SHP/7\nIuP6Q9jSyP3zRyEFq+eMFT7SX1CjLRoKYoo6btpeRaMG/Nu4Bbf3R8tSr/+F\nioPH\r\n=iETD\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.2.0", "babylon": "7.0.0-beta.46", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.46", "@babel/generator": "7.0.0-beta.46", "@babel/code-frame": "7.0.0-beta.46", "@babel/helper-function-name": "7.0.0-beta.46", "@babel/helper-split-export-declaration": "7.0.0-beta.46"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.47": {"name": "@babel/traverse", "version": "7.0.0-beta.47", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0e57fdbb9ff3a909188b6ebf1e529c641e6c82a4", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.47.tgz", "fileCount": 27, "integrity": "sha512-kYGGs//OnUnei+9TTldxlgf7llprj7VUeDKtG50+g+0k1g0yZyrkEgbyFheYFdnudR8IDEHOEXVsUuY82r5Aiw==", "signatures": [{"sig": "MEUCIDpFG1PIqoGequL9lLK+4W9zmgMzFaVZTReyoVOnwWPRAiEAy7aa9jKVrmzsVKWMUbi4gjIB+FnQotUXp/7atKbWzCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icfCRA9TVsSAnZWagAAb1gQAI6KghQ+6nqFE/Iah15a\ncx+No6r45WccIEiA0+xUJvm9fR55qna26ShJYsze/mz/tr0H7k7XIIl2ebjJ\nqxS/VNqgxnJ4J+66924p4lnC+k7CS5rJC4sGHOziwR95rn3di3v9pZcMPArQ\nGsHeWRIh8IW5oHI0+reKUsd/b0QWp4Y/JyfdH0vY+cPg4Cgj7QgidsEGlQcB\neYZYs+vKeNCNwb6Ldf0tnLMeRKrHRQ7VoPmE07fkjph943q2dlzP5czjuUkf\nbLgxAs3WEF++DdoU76KkK0p4z/M3YgI9OVOpFJU7XJ/B9ejgp6ch0J7LGGv2\nPhPwZDe1jqpP8hMh3TY0G7FrF1WO1WXJcgFoeDqi+dYTOXL+Q13DKnfKY/iH\nL7enwbKHopm+LWLeO5hhLWOyBjOR1T4zytuVERdBwzkjGZ6eH/Om8BZY5XNR\n/cIuQw6pDy3CGcfe2LTek8pz3asstVojlkqS3rLU4bvA17m0poP02yoZkmzT\nwhv309e8Tgwt9Yl+eLtzzriUabOhh+rv87oXzYtl87so5x08bYYgBKffrLQh\nOhulYqlS72HjNnppksc8APaK9jflbJEnbMKao4qiUeNiiC6Oz1iARRne396e\n7t/2jqkrwmHzbPcfBqr/KrJgBzWHxwIL4AY1OLduZX+Pgl2fW7B9JVFGTfgP\n/Gel\r\n=dXFE\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "babylon": "7.0.0-beta.47", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.47", "@babel/generator": "7.0.0-beta.47", "@babel/code-frame": "7.0.0-beta.47", "@babel/helper-function-name": "7.0.0-beta.47", "@babel/helper-split-export-declaration": "7.0.0-beta.47"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.48": {"name": "@babel/traverse", "version": "7.0.0-beta.48", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "e2f4dad48435ae500f8067d470216a355d947e74", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.48.tgz", "fileCount": 27, "integrity": "sha512-lXNTgDIFj6xyzapkpvSvqFC8x7g+sw4SzxGVLKjSbX5rK1GK7YH2XXMHii1e6yhfscfC+ski36yH23z8m9r2fQ==", "signatures": [{"sig": "MEYCIQC6xJEBsTGizbzPq3eCf6OyLJ15dHLI4t1z8/wyCyXMIgIhAOc99lTIwWlldEPB3FVx0+5LtCRT2bHP1dqRzapa74Hw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFuCRA9TVsSAnZWagAAmzgQAKOmjWPiDH+Sb/Mj7XX7\nTVYqXlR1Jn6OKTEY99OkfPwv4t8wNi0Z1O233U4VLJbTzgfPT6GiVT2/qmdb\n3mGZjuekXYW8ZF5bqEIqwQcstKxKB7XWJCllwR1WsTHyzSLDW/oTdsfRZnoy\ny00Md8uJiNbhgh60Cr17cvtEjBNT3+AyuX/1eqxCXkmdMdcjgbIYTzBW5vvb\n6Wrm+ZdaKPdpj0znLk4KQoHIdyzmYIPT9WyPzQ3Jjtiic7rxRe4UzMU928os\nxh3pHbK3A3gruKAh2GSddTTtIQa3MbOeQzlvyIVG64GlJcD8O4VRj52/z4hw\ncTyW4+LqnJ2Q0cO0Eg7ldWYD0hnb1QmE9GdTTgE8BQiueYp344uz/RwC88Mv\nlRvXzOdJADVKN9ZZRlQzguiH5/Q4HaCW9I3cf8oqCc1VzVyZxVW0syTK9Uku\nMeHJllE1h7H41GSPXd/vnoiekNbWBd7YQ4Lyz/+QPjOmZ8eLeBUBtpjwt5qZ\nzDjSz7OSMXO8n9abOhWGD98dRlCGqflc2YJgLCdVbsy3/Nc29w3Up2EQrjyq\nl0bGjCPC7z9yg3BjPcXKK1jlj4BW8jiCux7iAwX6ENMBvBkO64ZBqz4TVwKJ\nm/IU8BPc7+k3eAnWiX89NKW9RdXyT0uJqK0oqGT8ld608tU03rbfgldTmEjp\nre4B\r\n=/S+J\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.48", "@babel/parser": "7.0.0-beta.48", "@babel/generator": "7.0.0-beta.48", "@babel/code-frame": "7.0.0-beta.48", "@babel/helper-function-name": "7.0.0-beta.48", "@babel/helper-split-export-declaration": "7.0.0-beta.48"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.49": {"name": "@babel/traverse", "version": "7.0.0-beta.49", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4f2a73682a18334ed6625d100a8d27319f7c2d68", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.49.tgz", "fileCount": 28, "integrity": "sha512-JsEvqpRjKdbuE+wRjlwqnOvYMNNF42c5TMKuv4I0lddhMaWXOd+UTuovEfXmMw01JKBw/BMBKP3ZSESXgW8rfA==", "signatures": [{"sig": "MEQCIExnIuGVi5mCRlnJDGdNAJ1isFt7Tqah6/ORpVdwRBJ6AiBLEdRpzDbJbPq3UgW8tkzNurFzulq9+uP/5xobCQz2Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDP5CRA9TVsSAnZWagAASpoQAIFK0WRTDieROWEPO9kV\nYClR8ZdZ4991wyzZ4fryLnvcrWIK4SFLe/bxRPKqFUETBp38J1/9ms58nmfv\nwpJnThqRH70hCC2A76E6pZVYciZSKhUOfOMS8n3ZY0EwiNjJZQtrV8Y4dS9R\nnUqXzWIP+qQ6uK+IXs6pLyB/usSBBIrXke8qLPXK4uaRyfRQf9+eSbAiSGBh\n3PrIsXHgGFQaOmjjRGte2eKxXsNGGgaS/hEKni8pqIQ2oJI3Fzp9zAKhHqC4\nr+/Xa2Mi8onRcTPQCYjbPc6J1nAB36T6rfn1P2QV/Mn3kky2lW9px8zvDK1u\nmiw9SppZ4aBwIimB4mGEDv77H+qTc3j9osZC3zy7f6ekyITnHKPPCm2vpmQT\noCvJFLrgXA6O8ZZ+7C4132E9LzRxgdnX81F2dIt4SAdWHrKzhz6tmMJe7Zqz\nRisycGTTAE6/4zXdS+dzPWRkAU5oh6zEkOp+DhEy98hEKyErHxSvR42VZykD\ny6V2ujUGD+cL7IygFFMUb5nWLkrzeyU7I8RC56CgWQYF16g6cozxwMUarkxP\npbrpDQSF1nUrpZ8DIkrxpcpoVAntcvM3jJ3q+9b9iDI+8KCceybK5vCvzqfF\nIhJ7VIfD9UxCTj/Iugt8uZVom7Tvc+QCAIoMe1opuWQlyxY6F7IJkT9FF/9d\n5K73\r\n=/EiN\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "@babel/code-frame": "7.0.0-beta.49", "@babel/helper-function-name": "7.0.0-beta.49", "@babel/helper-split-export-declaration": "7.0.0-beta.49"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.50": {"name": "@babel/traverse", "version": "7.0.0-beta.50", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c5df3e15186ad19431d72ba7334b947a8afd5c4b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.50.tgz", "fileCount": 34, "integrity": "sha512-MzQJxcHyRcVkzZKr+o6TTU1nMh1s7MDSqOKF0+SyUZwTeZ2LguCvPQw6jBGyK+LerOGujnzOOfZ/E7nOvwei7g==", "signatures": [{"sig": "MEUCIQDm1wgI1xIt5v/kPR0tgheTRicJ2EsQAhGAdgZqkpndPAIgGiWmQ+mDH7solQUKyx+z8KbdolFpmxJOfeUR3jG4rPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143232}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.50", "@babel/parser": "7.0.0-beta.50", "@babel/generator": "7.0.0-beta.50", "@babel/code-frame": "7.0.0-beta.50", "@babel/helper-function-name": "7.0.0-beta.50", "@babel/helper-split-export-declaration": "7.0.0-beta.50"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.51": {"name": "@babel/traverse", "version": "7.0.0-beta.51", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "981daf2cec347a6231d3aa1d9e1803b03aaaa4a8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.51.tgz", "fileCount": 34, "integrity": "sha512-zX0HGB8jveQm/U5gVkteR39XLnTapcq0fHrGfTjRISLlscgbu400M+a4aJcIHestRQPJIoiLZDzGuk99nJXi7Q==", "signatures": [{"sig": "MEQCIF3Mp6n1G084U07nFedg9Y0HYp+y2myHehy6rbROamu2AiAuGYwvuZyrml1C1L3kU2/8Lku4FfswHRUEEw/rtnM2pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143232}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51", "@babel/code-frame": "7.0.0-beta.51", "@babel/helper-function-name": "7.0.0-beta.51", "@babel/helper-split-export-declaration": "7.0.0-beta.51"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.52": {"name": "@babel/traverse", "version": "7.0.0-beta.52", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9b8ba994f7264d9847858ad2feecc2738c5e2ef3", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.52.tgz", "fileCount": 34, "integrity": "sha512-Qwmi3OQQY2IPqyASGdQXgct7CKrqCjJSYgEcwwqkkYemWHjHZ213BgjTuK3g3H3EgQLPSoIFsWWJTxxVUlsy2g==", "signatures": [{"sig": "MEQCIBc8S1hUtV9sSCPHYUj+mpSF/YvxvKk5wJj8VR9FMcXBAiBja9aRBBWGTV309hDp30mViwtWhZVuxpvekc8Iic4lXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143231}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.52", "@babel/parser": "7.0.0-beta.52", "@babel/generator": "7.0.0-beta.52", "@babel/code-frame": "7.0.0-beta.52", "@babel/helper-function-name": "7.0.0-beta.52", "@babel/helper-split-export-declaration": "7.0.0-beta.52"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.53": {"name": "@babel/traverse", "version": "7.0.0-beta.53", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "00d32cd8d0b58f4c01d31157be622c662826d344", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.53.tgz", "fileCount": 34, "integrity": "sha512-JZh3vX/9ox9aoub2gLlpPRm8LM0yJuqzmp5MrbwD57SPh1dHMDWjGen9exbaITAe03t9MJV5PAacv0K2UJBffg==", "signatures": [{"sig": "MEYCIQD5hSy3On/Wb1GhxShjtDXcTV26GKgMUbrQlsz+lCpHnQIhAMULw0m7DzuK+m8Qn3Kj6m1jSIhur18IjQTTcSI9vW+J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143231}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "invariant": "^2.2.0", "@babel/types": "7.0.0-beta.53", "@babel/parser": "7.0.0-beta.53", "@babel/generator": "7.0.0-beta.53", "@babel/code-frame": "7.0.0-beta.53", "@babel/helper-function-name": "7.0.0-beta.53", "@babel/helper-split-export-declaration": "7.0.0-beta.53"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.54": {"name": "@babel/traverse", "version": "7.0.0-beta.54", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2c17f98dcdbf19aa918fde128f0e1a0bc089e05a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.54.tgz", "fileCount": 34, "integrity": "sha512-fgEG5zieEle+YY+w0sXX2xYTexBCua4P7TCfw3k/lsrRmv7XDcA2tzfOUhauniZNtr4cgdBgFOKjkmb/WqG27A==", "signatures": [{"sig": "MEQCIEWKD4sFPzAikAy+f0TkWYRFxOhmuMLm8llAmNJG6fdUAiBWepu3b2bKWhDMzGOngGywHzMNhClWvExhaYo+VqNVKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143058}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.5", "globals": "^11.1.0", "@babel/types": "7.0.0-beta.54", "@babel/parser": "7.0.0-beta.54", "@babel/generator": "7.0.0-beta.54", "@babel/code-frame": "7.0.0-beta.54", "@babel/helper-function-name": "7.0.0-beta.54", "@babel/helper-split-export-declaration": "7.0.0-beta.54"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.55": {"name": "@babel/traverse", "version": "7.0.0-beta.55", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "50be5d0fcc5cc4ac020a7b0c519be8dae345d4be", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.55.tgz", "fileCount": 34, "integrity": "sha512-+AuIbZCJg2vxdLw/ckbc+mRP+s2UTS32ghJ6oveuRFfO/g1JYYvDMgTqClWIfMJahV+K+vA14Lq2YaqMo9ZVZA==", "signatures": [{"sig": "MEYCIQD94LEPDuuKiJAiWq0x76g23p+dYt3tcgWXEe/D6dUN4AIhAP3X1ouqSY8RBM0IqpgwhJ6ahDlArFtsMRiDwTeqiX+S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143059}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-beta.55", "@babel/parser": "7.0.0-beta.55", "@babel/generator": "7.0.0-beta.55", "@babel/code-frame": "7.0.0-beta.55", "@babel/helper-function-name": "7.0.0-beta.55", "@babel/helper-split-export-declaration": "7.0.0-beta.55"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-beta.56": {"name": "@babel/traverse", "version": "7.0.0-beta.56", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "62fdfe69328cfaad414ef01844f5ab40e32f4ad0", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-beta.56.tgz", "fileCount": 34, "integrity": "sha512-9WTqtKP2Ll+jG68r+JEecXAbdH/kk5inN1VDSDaTgdYtZ82BYUS9XRWMVpc5HB9LJsu2ZEyUA1cGybID7eeOXA==", "signatures": [{"sig": "MEUCIHEaEtcCIS/UplWMJ9l1diUAbIFHv2sFAnANKBUE1dn6AiEA3Qf7U57EG8hDCVi1fx1Y3JknTTz+6rrfvyDcHqgjHdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPyHCRA9TVsSAnZWagAABPsP/090RVCc5zIvlI4iRWCN\nbceO1m7HEmqA8qoVzOknPE4GGOVdd6qfp7ZmBZG2N8GY48nVax3/1RzfYmXb\nJxrYxrPB9FDb+8L8+tPtXkzGj7O+vQEadNEQK5+FtBA5fnp8YenYBmvXuVHo\ns5Gp6xXeoKUptvUSWc3TRFElliRYfX8g/BM8c5BvT815HS3x/Eya/buWMnDq\n+MtVxE/gFYnwaN5VvyYeHDecfxp8xadM8oU85gYjaP5favd/6LoRKT4uB0vf\nhRp+toZLk4x8aDLEnZZ30+eDijVJFd3S/RQN2U3SOrnf4KB44HiYgo8S2TIU\n38V39keXp8qLlSRtrGfzepEw15+SvCQLgVKW6RWOQR/xG0M4Vkz9NlUJoTb5\nwEEDfW6CDWY+A9VtgOxdFFJjllzKHAteH8FJBFWR2r0xNCjSe/yWGzMgigc8\nanx9HGF8dtWyL390kaeB2Kx8jNTJBcIpKg5IlWTJDNZm2VNoZCEecjG73Ux3\ntWvUnD0hDTPFRTlk/LuRaui4bDL5J98plz+cJFAl4HFcn05E+kjMfuzumvRs\nnNj3vqRhH9wYqnQhrDrKq3+OQs6R3msjwfKPZ7e6D2qxaKHZ5gGloulME3cH\nJk8d+F46ipI+t+25zzmIyB91L0Ejb88yEpaCmBiQIFF0nYQluxOlh4KtiImk\nqyvW\r\n=/3n9\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56", "@babel/generator": "7.0.0-beta.56", "@babel/code-frame": "7.0.0-beta.56", "@babel/helper-function-name": "7.0.0-beta.56", "@babel/helper-split-export-declaration": "7.0.0-beta.56"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-rc.0": {"name": "@babel/traverse", "version": "7.0.0-rc.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0cfb037208ee00a853b2471bf944b3f010b77b90", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-rc.0.tgz", "fileCount": 34, "integrity": "sha512-95dT978H79fUEJ7R2sw7oVIPXXlSFBpr8VYUuSrQMwYdnIgscCeT0WMZTo6fTUsNKEeS71JudRAPM+TCiGgQsA==", "signatures": [{"sig": "MEYCIQCeoMHli2u25cnDZ1QSC5ECQStDthv6yCmTVUWRa/F8oQIhANqDAK55R+KidvFvSoIf/0Rsqwu+Ns+w4tp3EbgEbdp8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTzCRA9TVsSAnZWagAA3A4P/0YTEHq0zOAsuGX4RJe3\nGdwoUPg+iCmqncvpVo997o5v4Msp6g2PwsHL0wHozzS3hUlYMHlyBX9XmGfk\nzEAzUB4/9VHc1ssgqorzyfRH/qMkO2zT6pCZm8RfgagKsbzQVKGHuLS3js+r\nmxBAbmi02rJgwuLEDIwmcxKD7l7HI9wdXY+TfG+wwcQOaR45kk8FP1KFmju9\nDOeb/oWuvANmi6uTasVVCKFY8gKIWXpXtnE0NUkCzmfAPorWj+b0W4ZWDHFw\nrKXVVWyZ6RXnJBZ7YI12eFK88N7d6L07KgtLkFAA4OvcOj2wd9rP4u/w2vN6\nuGr4zyz3B6bQZI5VQMClu+puDQVlJ+GW5Pgcz3k8XQg/xHu+4NmBResPyp2V\noDeaoHizPM9hdJdDZUCdOV3TOb9L+XBdFEfQjwOKB6N86lwn22qTDtkPPatR\ngPQcIQJ6ZqA+RDNA2ynyGWP95o7NyBUK0oSq6E5Rhy9lEaDYVKSyjV7ai/wA\nkkmEVXXOqjCFpZFzNqIX611ZKa+O/bZIm6eoS4teQoxaY2qhfXBuSsnsZojV\ndu4nEgeI5EWwnpMGu7lbVzwQ+piqmjzfjMA6pM++o/tisjQ1eYZ9BH/flkne\nLLkxtA6/pmAVAe7HZlLtLfsFyvhIcEPPnP0MDCQErrqqCKJKxffcZ2Cm16Gg\n5QQ7\r\n=4jKk\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-rc.0", "@babel/parser": "7.0.0-rc.0", "@babel/generator": "7.0.0-rc.0", "@babel/code-frame": "7.0.0-rc.0", "@babel/helper-function-name": "7.0.0-rc.0", "@babel/helper-split-export-declaration": "7.0.0-rc.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-rc.1": {"name": "@babel/traverse", "version": "7.0.0-rc.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "867b4b45ada2d51ae2d0076f1c1d5880f8557158", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-rc.1.tgz", "fileCount": 34, "integrity": "sha512-lNOpJ5xzakg+fCobQQHdeDRYeN54b+bAZpeTYMeeYPAvN+hTldg9/FSNKYEMRs5EWoQ0Yt74gwq98InSORdSDQ==", "signatures": [{"sig": "MEQCIHvqVJHlzL/K4XTSHubzsH+WpRfofzXkQNz3GtdcSTfTAiBS9pyhdj7hnH8teuoHmHEXxUUam6Xasm3m74um+profQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+ECRA9TVsSAnZWagAAl0AP/3q0J2jezeTB40R1F8sI\ntT4WfJRWSaLfKYOR2GWKUvqLZwHlgW3wp3rNNf2auwGfFhAGI0rW2anuWOrW\nNi7VQttCWhC1PAY+zfmTOx68f6z92p2HcyBrIbmzIBBCYYOqFFRl7aetnIOr\ncHHydMBe/+P/h24CWxszJyXY9RfRtih0Y0xD3TGSCIZ1IgDUA1XbkG+CzoUt\ndunAyjftPEYNvSgWljUauH8CdHOWd7Wnevx71o9mdL2pS+EtbSzyZbLSMwVa\nxwTWL3SgB1tIsbuOgDIa1UFf30bLyqw+2rVUzziOwgndSG29Ya20jFKsuBqb\nou/1fg45Ds915rUzE/foEwO3qFi4jFPj148/PM5CJD4/+4Mv/gJ9kpRtOb1V\nblFuRKMUBhJ/bmPeI3pE2nHQn2/pwpmLuwQSQnmuKO3Yqn8taYrN/GHIOsjs\nMBYT4khm5E38h4D6qRxB0OxeAvIjEpv3yy7hwCvg1zWpLXsc30y4WXz7iE+5\nmX70uVkKqBPoUmHx+B/iW2kpqFMuO7nvHjDc95073PIUIzhlZwrF1UNH/c0g\n4mVNPqnKLt/Py+ivfafkpv1U9n3alAC+RyNSkod584RLMmKKDC2e3a+hIu1c\nfneBDHQW68dZqd433psDlrCfKcTyJXghFrwd83Y6ALnW1g2b/87vu4CSWQjg\ncAUb\r\n=mkpH\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-rc.1", "@babel/parser": "7.0.0-rc.1", "@babel/generator": "7.0.0-rc.1", "@babel/code-frame": "7.0.0-rc.1", "@babel/helper-function-name": "7.0.0-rc.1", "@babel/helper-split-export-declaration": "7.0.0-rc.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-rc.2": {"name": "@babel/traverse", "version": "7.0.0-rc.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "6e54ebe82aa1b3b3cf5ec05594bc14d7c59c9766", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-rc.2.tgz", "fileCount": 34, "integrity": "sha512-x8y9E+KZHs3Xmy5uiYmr1TtDhOBAZnL9vUtLIt95Pw3jovkY9q2NIwgLzfSlzOU83sQvzAooZWuJ65JERwxx+Q==", "signatures": [{"sig": "MEUCIQC3aQwNsgwkLL5unlqFGdgPu9EKDyENLr07Ep8ijDOx1QIgbhcyKTzETBrZM13cj3ohbbQQZNTDqrDPvdX9JuEvOdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGc5CRA9TVsSAnZWagAAAusQAIJz0lwQuzxel2ga/cqO\n1bxOaJonyZ0X1cS+IlrUu/ogXkdQp8V/Fj2vRinsRDrToy7AhzmgH/IvpS4e\neeOoprZBDpUcxV2Kc/Tie1kvZUfBAj7e+35kdKJk16S5Sx2UAlHxeRmLFJHX\nOW44Qtp90cACotmx3MjNK6DeGhewu+GejF21CRdfG+bPm8xgTgEei+KT11uL\nMDKhhG18DVCZhxX5VwqfX7kSH0zuERD0IXwl7nrzgi2mOYHC0JarQvmmL9iX\niCWpuQKjxzxd4VMsmFb/iPILks3f+QaLGOM16UYFPLdtiF5Lz6WYyuRn6ApC\n1m1zCyQkSv/bU0T1QyDXV5RA8sdJHt0zTzyIFgDdiP4uo9i3De1Allbk8r0y\n7/a96HnHLmmiWxAZo6j+1C01V9iTQ0cCLDE3FZkVeiZJXVqDG91xkwfi1jU2\n5isj5VicKtXAvNlizWmlG4svq/sYzHyncBXpomUG225FaeniNNRnPBW6nm/w\nv1nHpqr6xkMfqDLAkqikwF/S/GBaf6VpgBu1GUJ5BfHAsMxFT2EmawhfyZLV\ntlmRA+Y4SRsQLiR8q04OXeIBedjrW1TEOTbwvYH15X2HYs3Eu0F/bBG3GMvt\nwNMgi/TPk9tCfz/gKf4mivqCwfGYajCDchs0xyzEdz0jdJXm0nf/IABz2Rte\nFfhU\r\n=UQL6\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-rc.2", "@babel/parser": "7.0.0-rc.2", "@babel/generator": "7.0.0-rc.2", "@babel/code-frame": "7.0.0-rc.2", "@babel/helper-function-name": "7.0.0-rc.2", "@babel/helper-split-export-declaration": "7.0.0-rc.2"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-rc.3": {"name": "@babel/traverse", "version": "7.0.0-rc.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "bcf659e46d24244ab51379c849093f8c4e54d239", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-rc.3.tgz", "fileCount": 35, "integrity": "sha512-6f7ge8Yz1WjL5wbFaFYZR0fN1BOFOLAHe7PxkKqr9sFcPP/dLE1DqVi540CONk7yhwwpoPspDcnqNLDqTZDQRg==", "signatures": [{"sig": "MEYCIQDd7OYv6iGoOmS+TbtIeqyhZVhG90/wfm6KKTWIscMFZAIhALcEk470pAH3q/Rupt95NGqC8NSMuA/zmXu+EsN4TqF3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnZCRA9TVsSAnZWagAARroP/Aial2ZJmJE0llAQwt11\nS+ok8z4z4BWioCSBU9trNBCSvYeDS0NPA63eVKegmJVslMMNVFUdPtIY6u9c\nQ4ge7gP1Pns/Q5nnrM7sQv7RjKvZSdf6AQuCWZGpuMRm3gtliU0SGHZY5L4N\nWYQfMN6IXglYwztCAA7MBF/JGAjFuDy03IiC4YPRyZYQy0fa2paL5vUXWFk/\nwGglL35t780i/NWxWiaEw3KjNl++Wn3xqjRfcm4iERztGmo4BzDa1h8z8Z2G\nmPgBX0aRhE4F2UoOdmVsko7b5RJpBAMvEPO0l6CbEk2Sno2F+SAUhFPBnk5r\nH6aJJtNF3EgqKXpwP+wygmG8wgfSE2fjcuDOj0JMtUUsvDmOuRQeo42UnsWY\nVSfTvzwP9sdEQi5Hbn+3deSPzcGcyqr7mKaXKD1jbI9T03hsehQQS4Ce3br5\nTzZvwPZRaO2qP2deChrEvhBZzR8h5MnZwqjScpBbaYf8vp0k/rMYWDCKlXtC\nQkFHlIEFO2DrDhLqfBH/umLz667CBaub1AWNFQ1aoSv4jhKoY9fTgzpdyNLX\nQWQppJWOJic0Vm6vItgq6KZ7yGi7fCEdQTOX0OLvTPSRJU31SFFl7eU7PnW0\nsevyfXjZKPuasnXjogh2moRzSImPBfA9lGgA4LtYJfOKwiGCUFI+oLlpjMLJ\nwhfq\r\n=NgHc\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "7.0.0-rc.3", "@babel/parser": "7.0.0-rc.3", "@babel/generator": "7.0.0-rc.3", "@babel/code-frame": "7.0.0-rc.3", "@babel/helper-function-name": "7.0.0-rc.3", "@babel/helper-split-export-declaration": "7.0.0-rc.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0-rc.4": {"name": "@babel/traverse", "version": "7.0.0-rc.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "42d91ea7f785515496e1a1e1f5f79e39f8b1fe14", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0-rc.4.tgz", "fileCount": 35, "integrity": "sha512-H+DAcCI5enDlidjGZfLx8sjwTwxz4zjU0YU3jcnmbA2RyKAhKsc6Iuhr7n1nvUZhmO+cE2/uBSkFYW2hbBntKA==", "signatures": [{"sig": "MEQCICzRQ0wJBdbO62zGxPx9oNRyIA5D96O9sGa6OWuglJ5GAiBYL3T8P4MM0OWXHivvhh+UUc2K0rRZupcfBqRa2PaNIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrTCRA9TVsSAnZWagAA6NcP/A34MVS5NXdj4aA0funC\nRrDI7+AhJN/8AQPIcI++IXqU7U5TQyrMqHKDONxcusBQkN/wkZB6J8c/1fdO\njE5DZFPXnhqV8qEuWScGv6TFyvOdRHcIB9xgM4OWRyIbNE4YaUIp54vamFbh\nmbtYRc3+HqBNUBeAM5fcUFapGWZdyMp0M5MscVyXzB6ivA7zzR4ElnofHzYG\nAJ8muQ0WSsTvVT5BFkPr+CUfZ9cWi4hq4UXi9GRnO22F0zicYj7XcLynbwYe\n2DA1lqL5HleuJKdwd0tuGUKAApMZlo2MUugNDpFsHXRlGGLz3WdGxK5HijpV\nKhb9DSp/FTzPw0HnALHKkU1Yhf0mKSdTqK/opEiHfr0C2UvecNiYsb34RVY1\n/X8t1TbikoQC/Gym62QX3rigl7BFfE95H2aEpniAGG/MJgg3uikuiqixX7y6\nTHmaPwnpbgAcUb+mEkxw1J+jAtSDG5CPy78Be1yRPHzCUF9bnIlfYkC1i1jp\neTKaHvgpngJ3FLsZSTAhAA/Aw5C1TFBRBfJ+2Uvd2R6KSrF/MTxN+c4gTXW5\nD6fWQV55uB5a4L1UHY4OsGacIWdwGVJ6H6AKpZWtSQMpJIvdy5cRFShfCLzj\nN/80/vLdsSr/i5Yoy9B+lQVLf0LrPtJYC4tq7uH7gHiefain3L4djAFb9JS7\nZRvb\r\n=8SPB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.0.0-rc.4", "@babel/parser": "^7.0.0-rc.4", "@babel/generator": "^7.0.0-rc.4", "@babel/code-frame": "^7.0.0-rc.4", "@babel/helper-function-name": "^7.0.0-rc.4", "@babel/helper-split-export-declaration": "^7.0.0-rc.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.0.0": {"name": "@babel/traverse", "version": "7.0.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b1fe9b6567fdf3ab542cfad6f3b31f854d799a61", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.0.0.tgz", "fileCount": 35, "integrity": "sha512-ka/lwaonJZTlJyn97C4g5FYjPOx+Oxd3ab05hbDr1Mx9aP1FclJ+SUHyLx3Tx40sGmOVJApDxE6puJhd3ld2kw==", "signatures": [{"sig": "MEUCIGr4MMsD8CajAakrgW+rWXqMLcL/PnD0SJ4xyCdXBv8TAiEAgB9suXJ5vICWTd7q7YoJrbS6id6RCrYXLiiaThB5d4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDJCRA9TVsSAnZWagAAfhIP/R9lIR8+RcNHnbwtpzfN\nFSb3fqAUSce1ycM/F8kbcRd7XRS+PjfM2qV2caxlV7MyUMtBS0o5LnZcwNDc\nGAU1AWbc9v36ceyMSjxXILqJ9WYjRKTAQe+roADCqA6RnSo6hMeRCkJwgWqZ\nHC3l/4M1tIACjJfHKX9eKbnN63zC6UbJcaKJGF950h8RBoTMvilE6zJaEZLT\nJ54PWI83UgbHtngSE0dH0OYkgQfan6WSHopezF7ZWKnvh/BzYrPedcILqjSb\nMUw4FR3IB/q5/JWvawRrlaDVZ7eAwWOZAM3j8Slun+86q1SWdAY2z9MYXE5Z\nEVHe7BOXnQh4uWw+v414N8CTgwWveRiQn24DexAWOpCAONYuBwzF0hs/oTF6\ngKto9P0CIGBOe+6fri8m9t9ZxInbdOc81gUh9vlxksNKveMcHeGnHkS1XsJP\nU/SVUmkAqzVD551f27V+qvu1LUuWWvf8TtK+T1KE+5R4w0Yi9mevd9ywnXLP\n2RJRx2zqu9Atc1j3sb2dtZ9bWCYGga7ornxqTmAWGhThfuJCuJewTg2WDctV\nwlLkaZwyWdQj56xRdS4he44aw1ekJKP8jOf7psvFK5XvnkShm2siNBQ7tgrt\nXWoro+B/SXGBPB+0WolwmqwauYN/5L+bWTkVnBhruOULhWJqzn76RBRmxKJ/\nZIOH\r\n=vyM2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/generator": "^7.0.0", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.1.0": {"name": "@babel/traverse", "version": "7.1.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "503ec6669387efd182c3888c4eec07bcc45d91b2", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.1.0.tgz", "fileCount": 35, "integrity": "sha512-bwgln0FsMoxm3pLOgrrnGaXk18sSM9JNf1/nHC/FksmNGFbYnPWY4GYCfLxyP1KRmfsxqkRpfoa6xr6VuuSxdw==", "signatures": [{"sig": "MEUCIFm4gBKWkjCK1OjCSlSEhXSuao9UnGhsNgutk60SLhEQAiEAoBfhCGODxDcqwnCy0nwI5pwAVu9LSwr2Da3ctBhk7cs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADnCRA9TVsSAnZWagAA6vMQAJKhqQYFY6IZg/sAWBld\njKRtZn1f64J7FBnjXDmMQK+HmnchmGtlAIQDUcge3E8P8qztHgmyEpS/rWxB\nsBGytf9TXl3b77+1X9fhAjRwz1z1lOW+M52KtIDiR4U8Xbya0cF2a77EegNz\nPPHGoYUJ2PMhjOQS3VnlfBExTl8bxxl3DkBDO05Ro9fiPPEzjURBAY90w7c0\nc5DoIp4/NDqas428h1yvx77RKX2KdibCZ0nlVfFpFQ3MVWoI00w89EP9m8K4\nPV0213JsPkgDyL0KYJABGYZlG+WdHuXc85X/5CEHKQLUJRPsNMd1sbrDPRpg\nfi1f4AJK6wtykJtsz7REv+kBDbfGr/DHrWV4M8EHchRKL6vWTg0flYzGHPxG\neqV17BIuSyeQSJ4kl99XNHMw7ab/607O6alZZa7XfYryrL5yX82DfJyVnfxN\nSmeiw1H1IIj1P0fnPpXJPsNoUUOY0BDCfO9X/OoWRaAT9E4+2fLGPJR8Wxjr\nqjniMtpcuTPgrrWgCuJlAln4seOih8PFlv2xs/fjbJMr7f5WrLxX7AOwX6T5\nKcsno4/kOzF5yVbizkLG8Bpuh0s6uec2G4HWsdIVJZRpszPyxONFOGlr7j8B\n+CGbCP0VORKGTt9czhBceaaFQTLbgRfvkPEjonZIG1Dnd55Zxy0Z9ksDMbiO\njOcz\r\n=oMzK\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@babel/generator": "^7.0.0", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.1.3": {"name": "@babel/traverse", "version": "7.1.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "7a3731b5f049d1190780de7a5e7fc68357fa5e6c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.1.3.tgz", "fileCount": 35, "integrity": "sha512-Hj<PERSON>EwtHRWmIQYrZW5UGFMOB9PYfGrCjP0TAJEv70D+SVe7PG0uwWbQchsCeg+a1Mv3bPjnDIrh5wa1OgWg6Z1A==", "signatures": [{"sig": "MEUCIEP8j4ItqOEnyjb5/5AtmpFZrLSosUOE51IToNdLxrXRAiEA/LQcTVm7D26q5WKyGTXF4NM57CAPIatwk2qZN9hg6GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv3HLCRA9TVsSAnZWagAAk3UP/R+hML8mxCN+LG1kox4f\nU0iyH1VRB7JZn58mmGOXKUJwHvmthCUC/wXJ5l/t+zTuH77xt0BovSdVvbE4\nMx5BDBBRJ4BfBozoessqXICOGpuUO/RWE/WpSvq6Od47FymYEJuZ3RR/kvql\nHyzQffqlqDDQwYCCUwGePTywAnAhN7mmZtpmUnnEm7hxuizdrTMYHpK97t5F\n776NO6lzIWffbk4I1EaBg7z86lBOm12Kg1IeXDSLIrXeNdueTwIvt9qbB1F7\nQzw+ZXXa5xyxx2Ke/E3LDxy6sd0LDlsuaP3Dtb9va40CG9eVyhRJu9m1kBBr\nuBGfIthgXErhAsMXW/GTK6Vk3x6SEVAjxh6j+TWfOiuOA9rta2BbhT8buZzC\ni3nEYsWMs8pvGHbikwcqcSDjdBERFdZEy5efmMWf1tAh2bN9GuklPyG6I/1Y\n2UVXmYum6pa5mXcT8wYHBu+jRiXMHzvZEtlHHDCfRwe8CNizvOKbcZBWwD6o\ndiUsK8fOdSAPg2sH4nxDRYG3OthIHKVXl++dfpWKodL6gU6DMcP+vDuAPD8h\neO7j2q15smVgBVJ4mi9gRJpjijPlp/hwLfkyiaa3kJA/n0fCLzC1KFAwFZ0P\nGBRdaC3EJxy/qpzSWn7C3GtRPJtPXD0II+fSBh+CHZx0wgWIPyw+BtEOw7Ms\nwrx4\r\n=vqkS\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.1.3", "@babel/parser": "^7.1.3", "@babel/generator": "^7.1.3", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.1.4": {"name": "@babel/traverse", "version": "7.1.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f4f83b93d649b4b2c91121a9087fa2fa949ec2b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.1.4.tgz", "fileCount": 35, "integrity": "sha512-my9mdrAIGdDiSVBuMjpn/oXYpva0/EZwWL3sm3Wcy/AVWO2eXnsoZruOT9jOGNRXU8KbCIu5zsKnXcAJ6PcV6Q==", "signatures": [{"sig": "MEUCIAI+VyklHFt8pXghWrzElTilJJAHL1iOjN30r+opUDPzAiEAztpGGQIL+DDtBt/9TpispoK8UgMkh+0wHwC7eQFxLMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv4klCRA9TVsSAnZWagAAaHsP/AqKQW8oUQQqE/mRr3vh\nmDyTWDd4AZGpwppxcEghRHlK8Q5GCH3iOFx0bT1GKI01Q1ji506eKpKX9efB\n4fSIVJ8V9n/3UHTDm+nSkHtsDfgdt0v4z8jwkOWU0H9jTLAEgjNppdyDwYTy\nnKQJI++A3XFP5NmMUeTWyo9nF/JCgXEGv4lxmIRzSXb286hVAoSyVyT01tnk\nl8pdvkndhHIzrlhmeO7jmIksZbd/Yo7gJhSL5FU0ISb7IdwO+9BSA0KNZrv2\n66w87f1aSeKsYJDnvUFR+TvFcZ8k6M0R0LP68wb6CjEJdO+Sq/KuAIXktSIZ\n2fctm2oqykqURpk5bOMlLyb8M9GxNYOaFQzhQTYSqgwjz58+24hfLZYf4XdF\nnEawN6fR+75rOkTxnXHc4lBcZ3ArOUbXBVj4bp1OGYCF+gRRyYk/I8dSTvVj\nkP830+a66W+qnuEeXuw9E50xpHOQ4EYtOWH5GpH2gbSr2f+bIpKsV1UtMle0\nHnwm4dv8VA6lb9nl3lQW4Mj7WtQEELl7WFzAgpIcCDLiw3+VwF+vPIGcxjpy\ns4CzBWqM+4c8svYfb3RIZaJXD24UJV1KQsviQ9Yf1ROUq92Mv0mllcZvFXJP\n9j9ix3jzox33Ij7OgGMeY00f+rGE0pEWP58rUZ5epep7chU03/yekGoMz2FJ\n1Fv9\r\n=nkpd\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.1.3", "@babel/parser": "^7.1.3", "@babel/generator": "^7.1.3", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.1.5": {"name": "@babel/traverse", "version": "7.1.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "5aafca2039aa058c104cf2bfeb9fc4a857ccbca9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.1.5.tgz", "fileCount": 35, "integrity": "sha512-eU6XokWypl0MVJo+MTSPUtlfPePkrqsF26O+l1qFGlCKWwmiYAYy2Sy44Qw8m2u/LbPCsxYt90rghmqhYMGpPA==", "signatures": [{"sig": "MEUCIQDf19kLTn3KGnQT/gRkrvFOvikYLrTImxt7b45v1JtenAIgQHK3IBoy15K+JhD7KKES6PRzKXw68RWOSsuIiO+BNgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hP5CRA9TVsSAnZWagAAgeAQAJJ0kK9dYa1doFUA8Yig\nK0vealR+j0JFd1y9a11+LmbCTe1sueimqX9R0n+lk6Tv8sropwWuLHwxkuSR\nIs2J5AgMWY6YvVG/1dETx+Lx7BbhJPdc2seCE5ireTFmX7r6UqnaDdLV8JEK\nuu23f8apqonClRZdLDVtm+n3Y8mtQpqeGSfC6D+LsX7AIuW1KvwSq6hLQfcW\nAzV7NWFhP1EMvHEyH/n23oU+nHWozuEJXrz4kDQjPTGp9im1r9bsyJelRI3O\n/ocJLbgw6/4U6S12zrFnPTXkuo1f/BWXbJzKfaJvtI6oklS9kiRhKZLKuEay\nwbgx59VDR/ITE4zNDT86SjejP9tUJI41YBcge/OjYJEGOcqKDwwbNx/0Lbcc\nUsjjfKBo9V7ecllLivxEkhCLR1cNcLeTxhvJJVZy1tokhW+ttmsU0l7r/bXo\n6/TNat08WcFk6ENL5dW7Kymr6uuA70YBvZRFPqa/AhMPOYZjw2IdKQNy4rUE\niXMTuYCAlsupkDxAkZvbcwdaOct/Gtd9YSNiHU+YGQuJ8rcauvJ3sNy7X8Mm\nzFX2vT7rQfqMqXrpC3GDadC6YWh6fGhOj8XUiDE+qEfxwMIX8LklD9YNeCPC\nBD+OGy4xCJ1twjRDSdJK2SIE6rOj+e6HtDUmAhQLAi/J6CBHEWkaOB0V6RYx\n0jLL\r\n=jxxU\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^3.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.1.5", "@babel/parser": "^7.1.5", "@babel/generator": "^7.1.5", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.1.6": {"name": "@babel/traverse", "version": "7.1.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c8db9963ab4ce5b894222435482bd8ea854b7b5c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.1.6.tgz", "fileCount": 35, "integrity": "sha512-CXedit6GpISz3sC2k2FsGCUpOhUqKdyL0lqNrImQojagnUMXf8hex4AxYFRuMkNGcvJX5QAFGzB5WJQmSv8SiQ==", "signatures": [{"sig": "MEYCIQDjF3cCqPLKAmR1z6F6xw8X+IKkCWuWt2eVhfXhIRtB6gIhAJTnd7y+r17EjkXfQ+W5lScumNB5QLHeFTr7/UXU4rfF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3bCRA9TVsSAnZWagAA13wP/1hX2nb/22JPOS038HY6\ncptLIk7iSiRYEaJY5YXBpOkdMMh8jA3JR/lS/HbKy/W/XwmwJrj5xcygHDxi\njzZFGiQmvnDEaMJ3k2EGXcZBBziPHHyrmBuLyrgkXxVEK/d7b7uKeevVg1Xv\nJKetA/OenM3CJO/K1jTcoOqBJrkQjNGJ0iePEFdGkvvo2majP5X6f81Ax7cp\n1ya1a23/tD6mlUfaPgR8q9qzbzDRx3It80bNg+QVPctdgw718vEobtdGugRR\n1ocy3gLEOeoBuRoTm0bb2nMwqklP935oYHYqqBG2bqrETRwmcd2fV571PtxM\nHPl2mXieiBv3VE/4KJg6T70+6s8H4uXPzo1trp2r6zIyAxMUjGa5DfO1gGbW\njAHB3SROGBrDbBy7NgzL808MXg0yYrrdb4aFUrqurmgIYatCWqBI/pI/sK9J\nwvMBK9QUxzb8wwtsLRhREZ9zGjjr6rSZj/yxpbbVR5+TC6bsjM47Y4eTFSo9\nWgSnA8pepintQ4gk6NfxCA4gdvoAtUD7lDoFw8xxMkwEXrAAo7ykeAITSlpe\nhko2K7E+RMh/iY5uED7if35yXIYVwuyvs3FTvFyE7klbAOODMMJBglhNBy3H\n7Sn/bfP231MmYjh0H5hUvfDcIk9tqarDBVEj+iA4reBRg0mtmq3VG4lgwUdO\nnTjt\r\n=QYCd\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.1.6", "@babel/parser": "^7.1.6", "@babel/generator": "^7.1.6", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.2.2": {"name": "@babel/traverse", "version": "7.2.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "961039de1f9bcb946d807efe2dba9c92e859d188", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.2.2.tgz", "fileCount": 35, "integrity": "sha512-E5Bn9FSwHpSkUhthw/XEuvFZxIgrqb9M8cX8j5EUQtrUG5DQUy6bFyl7G7iQ1D1Czudor+xkmp81JbLVVM0Sjg==", "signatures": [{"sig": "MEUCIQDq8mmKMkbZQkT1nzuq6mZgEt0BOKHy1cmfPrGGCbKn4gIgdZQGXyf8in6RzjJEnnHWwNzG9LBXafgmMHoiRE948Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNH3CRA9TVsSAnZWagAA9z0P/iQ8D2ERz8OIdz3zv11a\nN7RaJpRTrs3zpJEog9kph146eEHnqUFI0bhIutM5qiPleNU6SokVjmmCm9lN\n3rYRP4He6Yd1vmKjuxRcNQsmyq8bLXBJQY1DVyaq013nN1bgim/0vKlAhc9o\neun42GIIU8K5HXVSLYXliXuGcDGvz7JVTEFpHc/TmGMbCJDBVmpo9foDgKdy\nN8pJaQHS5o20P+B+pCPQz0PlsjL6mdqVoLMk8UZMLfvy+Otn1aPMJPl7bvz2\n8gqzw+ujJkq14AXUzjk9sgiBOlDqgEmOFWNXa8Uu1+uCucBfQh0MipZ1Xocx\nVshZgwUbfMbjlGTUKZVINFoi6zcIbfhJhO/utP/ocIZRytDo+jWmoSdPeCIs\nnYqiNVugRWwk8JQb/oenkqA9zXO2hnGcRH0j++ww/+njB0DvbN+MSxyxW0JR\nRP7+Z75rN4oTVDuiEIHQQaFh5Ka6OhFwea1p3oPaVzygbSpvABC97GGI3Mv2\nNyEksjltJf6eNn2NAt7u4La3r1xhH5rxp3xe3DGDE+Q3FRpjFfqHofw33Q5B\nHbhYLip/fkImXmzzfTpWF33poED5A+jIk5iDxsBUAherfi2+zgbI5akEnWHm\nmo2WoNKR585rGm7RxuxUUBrjnf7Gh8NkT1sYUgS9ZkzCEDwBxfZz7+UDpWX1\nLVyH\r\n=nKzt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.2.2", "@babel/parser": "^7.2.2", "@babel/generator": "^7.2.2", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.2.3": {"name": "@babel/traverse", "version": "7.2.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "7ff50cefa9c7c0bd2d81231fdac122f3957748d8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.2.3.tgz", "fileCount": 28, "integrity": "sha512-Z31oUD/fJvEWVR0lNZtfgvVt512ForCTNKYcJBGbPb1QZfve4WGH8Wsy7+Mev33/45fhP/hwQtvgusNdcCMgSw==", "signatures": [{"sig": "MEQCIGHIPh8WB30WIhxOGgPyqeuISwv1FyQF2fl3Yx1KgwzRAiAIcYj6zdLZg+NEQIdb3euOtSbDd7vE5yohOfyU2LN1uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3lLCRA9TVsSAnZWagAAoGIP/35dWXE9Qs3XmIizhz0f\nB5k0yReeIfgsDuXDpg7PZTpPlvG25PcL0lBoo7/8BT+PIpRMtY0qLqlg/b4j\nsD8axn3vfgCU3in8ae0gYF5XwdT3q6tyxQ9SPYW5+Tqa+9w9E1mA4Z9TEJj3\nbYRn26nDPDdyq45aZjk4AcdMxGAwB7esuF+Lz4/PxgdEE/Kv1YU5ib5Zh0z7\n/9XsVSuPh9LEglgFVLZxxI00y+KZiWlAdDNtVEHqGSg4vuHQf7Z6CcrjzGqO\n8WcCeqIrjGgb8tdpCMll9UWIzcCN1D8uxwj6krMh3wbO8nktbzzy5Y17HaIj\n9v2xRYVJXM93oG+Pd66Z3+RVjq9fiOtHLeSUHHuodnjpLowZ+OEv2zFcfM0E\nYhlX77uUJhzBijvkR3C8+wnzQ6SWECFwWg7navADniLXeO9BO6wlIpOyVbFy\nAMBHg6vGpSvYjhJufejBP9UikvHNIR8/GlotMxJNz3Wgcuh8MMn+N9ZragfB\nHPOv2u6FcIPB0wqsM45nMSyepIiJGfGkaL3GFHAhsSi4ktKbIXaMgmwJnY2x\nzg7EbwT9Jeg+bYC9/GPwM4yOpcUvV1mIjTI8OnzrOtKnXvhkEKoSse+3qb3k\n1dMfbtx9WCwS6wFQbTWsl4L+CQDrX0aABVyChuTi9Z/Z6MXn5ZsqrF3T19yj\n/UsO\r\n=+wPa\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.10", "globals": "^11.1.0", "@babel/types": "^7.2.2", "@babel/parser": "^7.2.3", "@babel/generator": "^7.2.2", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.3.4": {"name": "@babel/traverse", "version": "7.3.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1330aab72234f8dea091b08c4f8b9d05c7119e06", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.3.4.tgz", "fileCount": 28, "integrity": "sha512-TvTHKp6471OYEcE/91uWmhR6PrrYywQntCHSaZ8CM8Vmp+pjAusal4nGB2WCCQd0rvI7nOMKn9GnbcvTUz3/ZQ==", "signatures": [{"sig": "MEYCIQCsNeE7i8pukzVZOeaHflNxFOQguV83rmbgLY6URCfWCAIhAIIVLLbot776repugbymIC8aDSrPR65NEp39SEGxmEIi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDWBCRA9TVsSAnZWagAALRoP/2cMFvkJE+d9MO45S93V\nvBvgT4uYiyb77Mj2TSj/X5taf7MDR05D8PHx8elCeLVFiuJFasAU4JgCY2Tg\nG+ifOwuM/8+tU5mTxu6xPfRXnkop6/kdGOxsr5P/j384xDzmPETCeEF7VzFo\n7Vxs4v/U/61j4CH2vGcJ+/uUhumCSARyaQkHglhdo5yYb3GxxfC1+9rGL9p5\n/RDRBZtlaJJVikSBS8FvGBKYi8Iw1l36LNURS1eHwPKszm2pTHgDw698ZyKY\n0zQgMUHXt3Kyqk1aE/vrCy0wV5eYukypjMZp8fNdphcUAlOoEVRzzrs8j8Hb\nmpVX7CHp5MFAET1MFNQI8QC+LGJggpSjzt7KVE9DxaUJEaT0FSW849vZ/64a\nQmLdIlTnBQzmVZCN6xYBx/G2I+xB7VjRwzEZPsHZulZvQE4G149fCo35p2bL\n2qzWpbFaD3OO9CLNRYbUUKVrH9snVD1pDAnmTed0hS93QrfiLFTqjmZNNBee\n4IPtQHkZ42oiEjKkYRPcqsy14gU/jrquGKX25kdlB8LTVGlqsFgESJFRNwsO\nF9jmGPxAIqvyVBENwgsnU+s+sH6W3xLDqKkOmCG7pY4roAp5GY9ekBeYweXQ\ncYHgiQAwfvvaDp7e6cCpN/9liP9Y3oDL9AdbQl2WH3fHeuW8qzPqRlrY19qQ\notuN\r\n=JWqZ\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.3.4", "@babel/parser": "^7.3.4", "@babel/generator": "^7.3.4", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.4.0": {"name": "@babel/traverse", "version": "7.4.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "14006967dd1d2b3494cdd650c686db9daf0ddada", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.4.0.tgz", "fileCount": 28, "integrity": "sha512-/DtIHKfyg2bBKnIN+BItaIlEg5pjAnzHOIQe5w+rHAw/rg9g0V7T4rqPX8BJPfW11kt3koyjAnTNwCzb28Y1PA==", "signatures": [{"sig": "MEUCIBEe96pgHB2JODNQzWL/39p1rmkYYBv5xRuW7ejp1nOIAiEAjUmzJELGRpYLxrb+ClSJ3prQGNhzuddmfwZQ46FNl/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTWCRA9TVsSAnZWagAAzrYP/A0HRvc6Z5iuRKoQgEQw\n7m15Y5wgL4FxADAeWzxaS34wjttzXHJzQj12aShaHvsVqTFVuydb1fRnYIXF\nnOAocFDVwnVSbiH3TIwkdelRoLYm2JVv2izqghpB8pynKH4XMrGA3kK5nny1\nGfSOEVpomXfvFSrfTtTANNRsCvV52RSC1asvdgR1wmGbzhspsLQ12y8OjbnT\nxOJn/v8/3Ka8R6crG+J5BA26Z5w/msvpYU8ziCpYPKapPtvFVHI6DbpcsiRc\nKfHB6ZtBqEgne2ey0eLf9pP58wrGMvIHq03wh23ArJgY0a6tgjLSIUMwQAkJ\ncTf2v7xRvLJzALc1/qaEqHM+MbopV38tHIRYjVDindxJPDr6o7dmKFCJH3iA\n8vJWWOvgHOuQs4W/NBIDsL6Cyj96cHITpxkOMoLVAQG116/9jBJYhfBFp9Qj\nBr6TBUHZpH9KJcSkwhXVVI/PLscylRzhgoklCJLHsxiLQXLOtjmVBv8uRBXD\nYf+XNgrlUizH/l0t4d1qbyKvNhfZyrF991S+2oWqeBQ/7uZuhz3MVyGWeVw8\nfm0sKsQrG9n2gABsb6+DjEFYFewnE+395BPRrDLK9XZ+cd/qpa42B1Hq0Av/\naNYlInq7gN50S1NSSkmQ/d+HvDode5VhAHe/vjcokfLmTleVaQdYq105H57d\nYbs4\r\n=YH59\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.4.0", "@babel/parser": "^7.4.0", "@babel/generator": "^7.4.0", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.4.3": {"name": "@babel/traverse", "version": "7.4.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1a01f078fc575d589ff30c0f71bf3c3d9ccbad84", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.4.3.tgz", "fileCount": 28, "integrity": "sha512-HmA01qrtaCwwJWpSKpA948cBvU5BrmviAief/b3AVw936DtcdsTexlbyzNuDnthwhOQ37xshn7hvQaEQk7ISYQ==", "signatures": [{"sig": "MEYCIQDljrytCEanMtIB6eP+m7w9z+qAPYW8fecgGpQVpml0LAIhAK9JMDzJq+CgEl7SEtVOWJl6ZocIHVt/09S+yQC1zEcz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75lCRA9TVsSAnZWagAA6KsP/0MMlLHicB4pH6qbXB2x\n5uYbfIrjvjuMpLIr2/vMosENeVfsjet61C4Biiy8jUVCPLo5dnAjpi+BoeGE\n1xMmXh+HV6EU92nd39zv3WhkAi111ML8C8lCkO1RmtDAiFtT5nBPYnTkAPJc\nyNrQK5VBtjAK8JpN9wZ4ik9QOKjxYBdGvecNDOZfhL7i6e0aa7g7BRbdmK7o\nMoXY6oXpkui63duGmdyn3dA3ZWYVBXWAS1iMqyA5Q/ljKo7jGEbyY9ySTrU1\nmFmMsdgpBnfRSZ1n+EpShPR5G4ss4GR+ocUbEt6pnU0g3IUEcK5XR3Vt1VQO\n+8S4ouIhOrUIWzqC04mO8jHJzxRBQDN/qpnqbxN95wS1lofs3+mZbKqh6E5/\nzy9stjzLFXPvmVUqolwi0ME5GBn2PPWpguCw5UnaSlkjjPNh8SRMYLg6mIsn\n2I6XOAUEfiWzNH1njleFUKpyhCQ3dwd51nq/ssiZ5XyZUq0py43GQviZHmAv\n6Tjg2XnZamBU3jlKw7yv92ZecbxM5b+6ti0PwqIceg69bXmU50rJTml2cviF\n4it45syu98jXu5AXyDNSP9JfHnCAyUafPan1uNsi1/scqpUFC9vhwXd9gZeI\nk1Qjjk7MSDoDD63InLZcjwpH75ksMBcjOymf7fpAeXNy7xC8T6Qe8YuaaGQQ\n5d3O\r\n=jPTH\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.4.0", "@babel/parser": "^7.4.3", "@babel/generator": "^7.4.0", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.4.4": {"name": "@babel/traverse", "version": "7.4.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0776f038f6d78361860b6823887d4f3937133fe8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.4.4.tgz", "fileCount": 28, "integrity": "sha512-Gw6qqkw/e6AGzlyj9KnkabJX7VcubqPtkUQVAwkc0wUMldr3A/hezNB3Rc5eIvId95iSGkGIOe5hh1kMKf951A==", "signatures": [{"sig": "MEYCIQCJ7pJXUt5+Huf4NZMrNIlF0WuO7SioJ6nBcwdwBU6YcAIhAI3s/msP11gLvZKgz5g+SHpa2l9Kylbjjyem3V3YwpYs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JrCRA9TVsSAnZWagAAAKgQAJmbL3ZiGx/Bn7F28UZv\nFg8xRx9iedTcI4cyBzslzKlvjDBTkzZzYvRcOTOkTsGNJUUt9GArlCQ8amQQ\n5tm0x4RX2wvNOlObQFLjhbWbcEzKzncAj0cIoNxIKOLVFwr7nVeWBnnyTZnY\nafl9fFCh7Mfc1WhGMMcAxww0ppBV4tXAqtQ3zs9H3WAOrnkPuQWGDF1i13I/\ne5qRTJb668hN5/V5k6buyNMuOYStc3bImzGr49+8a786WSDkTU92ZN2szKux\nN0z1xshV3Swfuxk6r04sk15NY/C4VUCwcG5kMN7KJCEc0FITnseInUETiLgm\n/9j8rfkStyV1ODSfzU3jPMMH5DEz+FDvcKNZA6fWCPU2bmhpkUi76TVQCqDY\nlpD5VVig8efhGsa2Zc/8u6/Yr/2a7w0x1lReXq4bFFUMr1eRTvRDVBPH6YVx\nM+fhhVFcKGuD0EvT4UkFLQcQ+cIFC7swEYA9lsw2OBiYVYiedA+KHtP7VZ1t\nII0TksWRapiyZ3/X8acPW3rApODE3PG2J2hg2PM/ejIsc8Aoh+/bTWZoxM3d\nR9ShNhKimN4Q3KP7MftHY19KijT9wZ/EAhVyr9FuNh3jihM+GMCY9R7gL1l8\nKUm0dT7kuCwHvTjzTxNmkzPb65nE50aAf2/lKgsfi533zDvuG3GXQRZTJm8N\nvzPw\r\n=JBS6\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.4.4", "@babel/parser": "^7.4.4", "@babel/generator": "^7.4.4", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.4.5": {"name": "@babel/traverse", "version": "7.4.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4e92d1728fd2f1897dafdd321efbff92156c3216", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.4.5.tgz", "fileCount": 28, "integrity": "sha512-Vc+qjynwkjRmIFGxy0KYoPj4FdVDxLej89kMHFsWScq999uX+pwcX4v9mWRjW0KcAYTPAuVQl2LKP1wEVLsp+A==", "signatures": [{"sig": "MEYCIQDZbIFwXjYpgv7Amlwg8PD1gWEtR62jTpn2qxpVFr3NqAIhAMUGw9p6zJr4BWSUy0CChiPntsQGRBbQvBHzgIYR35EA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5DlXCRA9TVsSAnZWagAA4r4QAKMRwLZKRG59Gv7u6OOU\n2j99tWV6SS1BLGxooyzx5wSsNZ2ncFN+Hstn5YN8UekdejfKLy57Gg2xuMDV\nQ0il/moTYVo8Wtpkw0NJC0ICS3pyxbiXJNyOh6ITTUCwN1P6VqDKTbo8JjXM\nxUiN8MIr1nyS1lgY2dVNlstXHV/FhoOmQe3YNluo+zZUkNlUtGNXXw6m3WWC\nJ8FSDrfbVr7upcKjI477VVf7x/6gAt2TWw7Zd+ZBsBC0xVgqqpYvQG1zyWIM\nOXl0EpbUNHlLduhOd3ex+nVIqqXc1p3hY7haSN9LWOLsdQ1vMfM22pQBXQ4L\nFhf4IOHjphm11Xl2l/KGBJGcqv3rjIgaz4y94kOMYefZpHMHHUYp4/fV/YS3\nNxNQGNLamiNhJsvvBULQQsmjeytFXCJB3m2//7f9kU3H1/DGg31K3LOaQVYY\n5Z8G1CBdzuaGpvrxZ4kV2GFqQEBKdPq0z7g1ejk2ourBqs13ctp3KUS1hJay\nKjXfDKOPd4sa6AVW2jbhR5RNm47TUHSU4QGYH5urRt8thzBUaGg2ZeeOUEG2\n/PPLDlF9wPRQOytg3zH70EPyWfCZvpWRj5bVLCtq4vUV5oiPBXFfXnxhbwhV\ng/CrBzMhII09RgIymH9Lm64xF9A4Gi5yrV4eWV45HIA94yMz0SslBV4bL+rx\nss+E\r\n=MItI\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.4.4", "@babel/parser": "^7.4.5", "@babel/generator": "^7.4.4", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.5.0": {"name": "@babel/traverse", "version": "7.5.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4216d6586854ef5c3c4592dab56ec7eb78485485", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.5.0.tgz", "fileCount": 28, "integrity": "sha512-SnA9aLbyOCcnnbQEGwdfBggnc142h/rbqqsXcaATj2hZcegCl903pUD/lfpsNBlBSuWow/YDfRyJuWi2EPR5cg==", "signatures": [{"sig": "MEUCIQD3w/4FKRtoKH64FSBZ8TZ54jLl+nKTix8cxYLyXyqiBgIgSX6BDyMBhXqnkM2q7/WBf6tpEhOK+0mY6j79irxSiBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffrCRA9TVsSAnZWagAAQBgP/i/2j+eltUkvZJGjLpMt\np5VG/VbwVicmTDj5Amkv8oVu8yTMS9YUYHjYxFnhHom57Sa+ZbbRndVrh74a\nni7RyE43Kwq/uWyE5xmNZM+lHgcigG0POTl0Avu0qvIspX55+Ix5a5L5+Ouq\nRmxGcbopQlou968jDIggjMsE80LXUTp+d01c/7TJpTgD6qG5sQtykF2dDSys\nbaa0QwIJc2Vp9FAKbEqQu4cUpik7/TidUh4RQqVJKlj9ylac7nltkYXA7EjO\nqcuWAShUtZYIL/hWezKH6XZjFoBMKfbd1FipRpsuBtEt2mFswaJWvN/QvDmn\nnuHbhDqpgBccrT/t5ogdOaeo+MEyvlDK8S7MEPlw7ge4bJx4OOHa+H6XgISr\nWkMSRbWaTh9/aIurjMPVzpnpg+1tp20tX09T035MXz83CGwzbYxqvv1WPO4c\nVCF8+L++KsyOuHtr/9x7UZHQW5zschOX9sKHq1gpvy41ZygJzoxFfsCkS7FH\nO+hLkWQyLvbhADJL5z1J154RdwZutHgl17oAOX5VVmmvDmcyFbx2ioAcPYE0\nm5C6q5g9VdgoCnBEj6xlmaXAdL1oit4Y96GwOikvp0bFgIwV9MMqpiHAFbHq\nVpSXbNq+5vyX8tOVxvDVQbv71Di2d7kUmJHDb5e6OrUBUtwvfsg1rk9DaK1m\n8p+R\r\n=LbTk\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.11", "globals": "^11.1.0", "@babel/types": "^7.5.0", "@babel/parser": "^7.5.0", "@babel/generator": "^7.5.0", "@babel/code-frame": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.5.5": {"name": "@babel/traverse", "version": "7.5.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f664f8f368ed32988cd648da9f72d5ca70f165bb", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.5.5.tgz", "fileCount": 28, "integrity": "sha512-MqB0782whsfffYfSjH4TM+LMjrJnhCNEDMDIjeTpl+ASaUvxcjoiVCo/sM1GhS1pHOXYfWVCYneLjMckuUxDaQ==", "signatures": [{"sig": "MEQCID8uPQ0ItFsarF4PwgRli4bbYacEy03q65PDguLcbek8AiAZqHmgQZECTQNiUFl9mYCiEPlY53KCvIQfQC8mHr2b0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FqCRA9TVsSAnZWagAAhuoP/jSeV0q3+gwSlqe5zodu\nrb5vkptsQZFvVSAylOS4ISRbGji6Bso9KWq+Z+9S0bfnKQtydqSJ82YeW5Eq\nC4QOkVjf6EWG0gBOf9k9Md9UrAhinMyPd9RfWWprngbvH1jLIDEvNpiAKYh6\n/R0xGAvrUcLbdanHDETm7Q2LLiW1psfKO3uFtu3edP1SQymn/WfXRG1Y1kUx\nCg6X1osB7/orARhHaavq5e79x7jqQ6jOXyjvcazn66ESoT3XD/pKKf3EA7Yt\n6nRRpRZFHau9uq59MH83jsA2ns5VsWezEbNlIHUAxkhwyFc3QB/quQwNX1YY\nbTARH23FRZ22luyfxEQEx0yV63NdW/xfmM29SAVC+e0iFu0NLnKlxkrb1ZOK\nQjCBBJtrat02GBAWDwMAbDmJo0JiiuK8PoGKZ/+N+rB34AqSfvnmKhBFOIuG\nss26/AmTNMMzUtcDCavKZM9TvvRXTSw9qy/pEUjBlOkaEy/gF6DZS7CjAGgp\nTMy93qZInqwWzV7GKe8A3BTsPQMcaF5gVAoFzZpFLQG9UcnjM6dNnQjOrVfC\n7TmaFuZSLq0BcvRekc4trpBD/iLAgjcp3YLN5884JMKK+QiHL6drir+Aukm7\nBUlZMnPt+Awfa+mtl1TbWktnqaUJsmmaB+o1stMMmWx+WAPf1SZxNPnr84iz\nTH/9\r\n=3Or6\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.5.5", "@babel/parser": "^7.5.5", "@babel/generator": "^7.5.5", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.6.0": {"name": "@babel/traverse", "version": "7.6.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "389391d510f79be7ce2ddd6717be66d3fed4b516", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.6.0.tgz", "fileCount": 28, "integrity": "sha512-93t52SaOBgml/xY74lsmt7xOR4ufYvhb5c5qiM6lu4J/dWGMAfAh6eKw4PjLes6DI6nQgearoxnFJk60YchpvQ==", "signatures": [{"sig": "MEQCIHDO+l541Gy5DnLLNFv2tDQVJw6iM+QeLem/WEYuFMWmAiBK7D0mwYkIvWi0/RuXYR3RxTxK2muNZnhLPiScK4Dw+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcpiJCRA9TVsSAnZWagAARpcQAJTYLAcxMXi8x0VOlLzZ\nUOc9hZuGX4PNs309rCjDQLeiNjpy39AazJPzH+hRhAzZ5pRoo2Eb5IrTJxEY\nWoA4czvzIZWMyPqGp+cYuRSZnQm81x/FXGK8FiZXaxoIy6S9s1zJxlPAsFGN\nOhJEOFMaRqEwvi7SSSYEUTykhT69CZ50HHOqPn6RVe9HIR8wqxc8f2hpIj5u\nAukVwqJoGSJ+vT5w8sqGqkPHzRLi+zNoEQQWrzzrmG7gk6tj+63fJ9s+Oo/d\nweK9i4obX8MBWmdn4Y2mm3PvRgNUGr1EgXKaqiqYO7LuSElHuwT+J5qNbXhN\nNAYLYoTdCpB0IXb9UJX+599fbOP9PHeWmxpIjbmEQAJKoxfmWpdWH6KRIePl\n1bxoklyHNh636/DRU4Hx+MsAijXzX2P96YPdzJOWfzqpwI+yMohubqqKdKQr\n229CwQgHKYu6k/OrvMB1fSrgSf8ZYWgc9/8I9d/YG37BGwNz0ruD8wRL8Db3\nzHDdgOIOFbq0sKM19IR5PWQh4ju6PUk2uHWT3ksoHk1dzs33tSVAM5D0AGcM\nuXTglFRwAwIPYSE9Xb6lzL2RYNE+8De9zN9GjO8yiaKYFxZ45BLZBd4l/Gwd\nT8CpF/1nPHkMlVSsknp+dguwMkOOfb1jD7pTbgdYF7Xzu4jLt2nZWwXEgnUM\nFuHH\r\n=pOoB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.6.0", "@babel/parser": "^7.6.0", "@babel/generator": "^7.6.0", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.6.2": {"name": "@babel/traverse", "version": "7.6.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b0e2bfd401d339ce0e6c05690206d1e11502ce2c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.6.2.tgz", "fileCount": 28, "integrity": "sha512-8fRE76xNwNttVEF2TwxJDGBLWthUkHWSldmfuBzVRmEDWOtu4XdINTgN7TDWzuLg4bbeIMLvfMFD9we5YcWkRQ==", "signatures": [{"sig": "MEUCIEuDrcCAGLh2Edxr9kO+2CLYyLu3Qqe0rRU30vH2yRRdAiEAsJqjNtLmDlcnHwJ0a5OI10LxbLdR2vAO9JD9RKdQzEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdsCRA9TVsSAnZWagAAlmQP/05SKlW5NBN01JnUYZMZ\n1wt92hVWPOp6EkESNq4H0gU1Cya1QY1Q/om38RIxdudJOYAN481ZfbBzELd/\nr119UfXI0Jncm1S7O3f54fads6CheIYDh0Cpgl2YBVNZLmGirrqvy67ePVn3\nQryQtSBx1UBh6lZkMwfGBf/YeBqzUXO63Z5DQVrmgW0muZRYCt7EQGP88dGE\ngsR0HxWPNvJYYSluDEnpbYkbumlmIX93DOgKQpsrtoAdBEg40LbwmfpOu0dI\nqpf58ovjqjNLVcC1agEbA5DRw46F4R715ekkx+uW3siDjh4IawO7JFZ7WvNI\nxOQRElX2QeOta70XI8bCkwga2lQVZ9mltQ3NJIw2qzsLEojOBmho9R9Ddj5I\ncx51ZlUL8MCP2bqudA1nOy83+/11BQjUkTFixmgNgIDdDZRgPxFYcXN48GKk\nd1f7rDnpBTY9VSCKTNQkyPXH3gy5yETuJc3+XtyJNVwDEVApbsHYWC0HXjIZ\nB2qsV1FasbOsGV/iBB+Ia8GoymK7ZW91YIEubd9u38K0cRDG6uAC14JOo1zU\nskO11j0g5ERYes+X+fp0qZjcuX8Oo+ptwlsA9bjSs8IuLEVRpwsw1pl7jVhj\n1gMANlTA9DBm7ydJ78b1nDct7PgQJhm2hA7nrBxbjaJ9HnmdlcAFC73tLg8+\nINdh\r\n=rHx8\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.6.0", "@babel/parser": "^7.6.2", "@babel/generator": "^7.6.2", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.6.3": {"name": "@babel/traverse", "version": "7.6.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "66d7dba146b086703c0fb10dd588b7364cec47f9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.6.3.tgz", "fileCount": 28, "integrity": "sha512-unn7P4LGsijIxaAJo/wpoU11zN+2IaClkQAxcJWBNCMS6cmVh802IyLHNkAjQ0iYnRS3nnxk5O3fuXW28IMxTw==", "signatures": [{"sig": "MEYCIQCtoWpOhAgnW/UwqgMEWMN48bvutvK7bvWrC5B7r28ZhAIhAJr02ISAnC4vbugVH7uBzUbtP1LJrMiOEUntI71nNhlm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhfCRA9TVsSAnZWagAATZ4P/2EtATcccIHpct9E8em9\nSCHiVWAzk6LsFxqCrv9Vio26rzDENGGPdCjKqKpP/WGZlyB9QL0iSKgwtxFS\nRQABbtjHMI3UGUdyi6xRfcZnVJf0EWusUi6ham6HWaXeXhGe54uigSrswnvW\nm8IZuFh93gXwlIkp7ALtydnOcaTcqs5LJwqnrGgy9/cVC/qOIaOj22wqzo1d\n6vszlT2Wxf6g0MpxgIdShaW/VSqBB9XWa5PG+c+qDWu2Vx36vLWKL7RzXtRP\nccsFlLBtxtL6dh3KDFq0ZRC4dnyRhkVzG/hCWISmx7EXaetsr4gUFJpqJBEI\nx/kxAHFuYr00oXVHNNNJ6O7BKOcqvV2Qqyj0a5GhOU/tzkCIpOAFnYoT4vSj\nrDj+1vtklPzJwblhJWOqqtjIKbStwmT/VZjCnpYTox7GY6QJ94SqKAqPAT3X\n3Lsu2J48LrR/763yqlH/Wp89pt3htzEfn08qVS6H0tv8gM5t0SHgBHrZurxf\nNHD+1wss3+G5J+k5kUDPJp9sxZajtax7vUGpcAt8FBkRG7VEnv4zFQ6DI23F\nOcoBafv8aa5q27n0fNuNebkQIXKKupr96Qaj20ihe/5uQn9URfpu6VQ0RY0Y\n+Fb4QKWF/0y3kxFc8kDJbR8gU6fHDC2bq1vq7F8X1iVoCOUl5mkdLZvmez79\n7VXp\r\n=P8tX\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.6.3", "@babel/parser": "^7.6.3", "@babel/generator": "^7.6.3", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.7.0": {"name": "@babel/traverse", "version": "7.7.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9f5744346b8d10097fd2ec2eeffcaf19813cbfaf", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.7.0.tgz", "fileCount": 28, "integrity": "sha512-ea/3wRZc//e/uwCpuBX2itrhI0U9l7+FsrKWyKGNyvWbuMcCG7ATKY2VI4wlg2b2TA39HHwIxnvmXvtiKsyn7w==", "signatures": [{"sig": "MEQCIDM0djzh7YqPKsGHza9uVXJ+m6M+Y63BHierlyzhhOJFAiBE2HzZ0IAZlXUTzYEy3a9zdB+2RVVppfRTekKR2VHBfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVS8CRA9TVsSAnZWagAAQygP/2STkynqzcEu+VzVZkQM\nNaKV9pcCLkeaCAEDcxnry8NJadRrnZl3sIrexY3S2/LXE6WFF3WVeDkHFKMk\ncsaAWLPTGLkOAQz+rgHLUmkO6p+FLJd1jbRKiV8Q0AF61F5FiXm20acW3+Wq\npmNzV+fiWc3UymBTrxyFS4nIQO/ReonKViejIeClh1O7+2jKJCL8XXiOQ77z\n3g/iKqoIBpF7FiWlZzUT/Vjvcr1pmAl629MPW4z/A+YTLmjI8Qkv6q6AvVA+\ncW3DrnFzwTSVzWtt3cfZCRvdCCGu9zr3bMQa6dyUCX8bFSNDXJDvWKpFYIfP\nmz7t+GpA9QoKtKaO4fwj5YZZHwp6QTLd1dUrZpzdllPhB+2Kz60NJuTOYinn\nPF5sm8Yv4CpPsevE1sAXaU8oMlTba/wtmYMBpC+BP3t0JLDgMlnfbvcH76fW\nb57YuoNLxDKclmb9qMCgQEk0pl1yIl0Bg+04OjHC157B/8jxOQhcPo3g/LhN\nNBRs0/mrkK4zW8+dKiZGrdr1yb3uEEmnENV8svn7c0Z9urV3XJ64rnsUSQeD\nemFJ4o/ZgJunLBiGc7z44tqYLzPQSxkQmu5gF3PUF/uQdkVZdJrzYc6ktzZx\nVpsx6jcTWMOhWewUfsqbgoxxFAANtJVJhWpTc2FSK+Xn7bBBSRXNd4m6zoD4\njbrs\r\n=/U9M\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.7.0", "@babel/parser": "^7.7.0", "@babel/generator": "^7.7.0", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.7.0", "@babel/helper-split-export-declaration": "^7.7.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.7.2": {"name": "@babel/traverse", "version": "7.7.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ef0a65e07a2f3c550967366b3d9b62a2dcbeae09", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.7.2.tgz", "fileCount": 28, "integrity": "sha512-TM01cXib2+rgIZrGJOLaHV/iZUAxf4A0dt5auY6KNZ+cm6aschuJGqKJM3ROTt3raPUdIDk9siAufIFEleRwtw==", "signatures": [{"sig": "MEUCIQCdWu3x3Hw+RKIdBJ40tXheePmPvixncHqm84eDagejowIgdJYnz/pxirdYhL7JRp4bHrcZoj+0AYm680ah2ggN15I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1btCRA9TVsSAnZWagAAZtYQAJVXx0lU3QNm8kwLfPDO\nLgN7em29QJaAiFdS82XsjF2nzuEhRPjtCNvnwfloIQ8YXOKcnE8fdyPMQRtN\noL43kbLmmeazoayrFo7Gh5zupotjVfYmaaze2HyHykzlgyrmBMPGflK+ws28\nFZXFSNYnNzKThr/E0cYYGGcywBokbeRoTl0qIpOjLwmg4Q2rpGh5sUrWQvYS\nXFx6BSZ+ZlPdxR4BfLiKQfcnFMze+kFFULdjmrnnrehsW3n6esZrKfPCFbZV\ncUqBzp3m/ZtG3vwi4LyWiehJX7DikPMMIL88/vrDvYxIyHJQraKykuzoShei\npk0VkUReDzxubGnpCFOvHmKbVNnUafb7cJl/PlfpHaAjXHjPJzRIYA6ZQELg\nnXhLbNGCM5uiL1mxf4XTGUtyAn40zhhDF25GLqF3NCeIULwId2RXawKIZG4O\nHGbx+Q3Y4q8YIrY/halmYMZodICnUhZy4z0+v60qzwYEFuiSIpEHVZC+ZBve\naJbvwivy82BCp/xWuRRuLfmZK9hm9jlJKqI5WwRL3p1YN6KWY4ltp0ilM98C\n0SNwYAFQCsxWxVsRwW/AEUJFFdPePSQEuYsvkp9Vs4Pqs8lp7bSj5FBtqdH9\nk9SK8uwAwsydOronJbD7W1buanjusZDdQD7V9PN8K4WqYV8F5i0VPowvhPQM\nQaCW\r\n=R1sm\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.7.2", "@babel/parser": "^7.7.2", "@babel/generator": "^7.7.2", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.7.0", "@babel/helper-split-export-declaration": "^7.7.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.7.4": {"name": "@babel/traverse", "version": "7.7.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9c1e7c60fb679fe4fcfaa42500833333c2058558", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.7.4.tgz", "fileCount": 28, "integrity": "sha512-P1L58hQyupn8+ezVA2z5KBm4/Zr4lCC8dwKCMYzsa5jFMDMQAzaBNy9W5VjB+KAmBjb40U7a/H6ao+Xo+9saIw==", "signatures": [{"sig": "MEQCIEGkHdnF0sEbGa0c52wooTTahZqFmOpcf1l7VS8uzBZXAiBjb4j6YOlJB+o2CdyobrieDNPMKvVHlzLe72CatkrVKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBVCRA9TVsSAnZWagAAFeYP/iF1Yt1dynwfsm9I11Hg\n+euIBPDmiOK0CyPOCAZ+ZUoXlOf0Vk91S5/U4rBOUDvtmF5mZGjZ45oq9jjn\nVAm9onDUCvsgMhmDBWk+3UJ1uIEN3tfxbUa4bJy0f3UiHgdI1NlR1ItvTsGn\nR7NjCfAkx3iMGUexV9EHEQw0/I5+SiEvVQuA9DtqHdsGgAOcIuL7kDiTafeU\n8BdtsOhOoTdcAMLgXBcv0e7CnunE1TNIH8qVqH2Ji1AbbcdQUaG7c5SZtvJ7\naw7bt/wFMwVy3zM0QJgHv7ywLeaaJraVb5v6sArjt66dvxpH8P0vabo8lrAH\n1TDwoaNbLXgDTDb5TLT9B2nMEotn9XGqfKpU6S5eDqN5yEP50OQ31lNrw6TH\n3NSHmBHHPY1MS4c5PWLDICwv/qQkfuVR9r6Vu6t92zpc4LDS2oAwHJotu9ln\ntxihtpOTS6dOxcQSdelZc3FS8o0x035vIiu5sU7Y3YYuDQ6+BA5wMw0MEZHp\n1pHXG4udq9DWQKEN3ywc9BBRjNta8i5jDGrBI51S4eXNd5XD7xHjM+ZHaEWr\nyg50a8dIAilVgweY8Wu2QUxZdZTQjO3VlEDcYpk1bGwbtHlJkfWGk6IZymoJ\nz5PcvSw6hrWb8axvI+XDP6O0bA+2FtwIDMLYKGBYGfHZqX2L/77PinolKEwB\nMNoH\r\n=4S+J\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.7.4", "@babel/parser": "^7.7.4", "@babel/generator": "^7.7.4", "@babel/code-frame": "^7.5.5", "@babel/helper-function-name": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.7.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.8.0": {"name": "@babel/traverse", "version": "7.8.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d85266fdcff553c10e57b672604b36383a127c1f", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.8.0.tgz", "fileCount": 28, "integrity": "sha512-d/6sPXFLGlJHZO/zWDtgFaKyalCOHLedzxpVJn6el1cw+f2TZa7xZEszeXdOw6EUemqRFBAn106BWBvtSck9Qw==", "signatures": [{"sig": "MEUCIFaKBTpu6jFxumf+avb1kYP8IWv49lJb0BxbCh3J9SioAiEA28ziqWWJM3/4edJSnw7Qa+utwKo9RWfT4Kde4DKqUIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWNCRA9TVsSAnZWagAAwLUP/RnEftdP5VjDhrAMhVBt\nbuMa8YZ3ah9NVvtQsQUULRvsDySnYDyWN7rf9k1CdemSuyMzsWVVSfUt82FR\ntcRrS2lAkh9y+JLT7Gn3ztJL4q0FQNHmSCVaPQW9mouOBQ4UnnUyLXvuuAO6\nKLc9xq5kbPN1os3UG8dDQtcL2K+8EVCMXjHNamglrcWjnS3zEpeids9AN251\nY3DtFiRqSLAOoFgGf8PTJwtLAnEmqQWJXdzyRp42Nz3kuTG5fTn7Lzfo4kBa\nVzkw25WqDH1OtSV5U3E5vY/GsszoRwQLyrwklNmfJvFp/TGXNBz2jpw3hXRI\n8oQZeVziVwLq4Z7Stc20/0QYkhGwo1RWZrZsluu++FJS/9QvkU4wLi1qxEsp\n8lszfLRtSGENVhVovEjpE+3X059+Lray9+gXnz+Y78x2FO7fawTvZvYb5qzF\nCSNqQ+OyO+3iUGX/iMzlY56SELoBjv+1gWl6i96ZG3uxcuU0mSV7QXIYL/10\nzl+nNHaSoen9tj31esE8MLV9AFbF9Z7yXcW3xlihj00XWQYdabubjsQevdKN\nPTb2NsOasfsKE1u1eu9s+ADJY42rM0+ALCSlaNOGuN4XPxDZwazopM50yVLI\n2GiRJ0ipbwt76dKq/Mdh5Gem4C7nbQVuNd76yrUaPKvKJT6dDJWgByMX25F+\npjiQ\r\n=QKgS\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.8.0", "@babel/parser": "^7.8.0", "@babel/generator": "^7.8.0", "@babel/code-frame": "^7.8.0", "@babel/helper-function-name": "^7.8.0", "@babel/helper-split-export-declaration": "^7.8.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.8.3": {"name": "@babel/traverse", "version": "7.8.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a826215b011c9b4f73f3a893afbc05151358bf9a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.8.3.tgz", "fileCount": 28, "integrity": "sha512-we+a2lti+eEImHmEXp7bM9cTxGzxPmBiVJlLVD+FuuQMeeO7RaDbutbgeheDkw+Xe3mCfJHnGOWLswT74m2IPg==", "signatures": [{"sig": "MEUCIGa0S9tZk5gJgHLQeyaQGTKtAO/BFdY1FERkICOQNs7gAiEAl/e1Twae23awMvMJ7Yjo/AIbHn3PgMY3pD5N3pdDGQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ5CRA9TVsSAnZWagAAzLIP+wZsURTOo20c8k/ilgUv\nhdyuVIUaef+Oh5MWO2w67XQ5TUXMHg8kWePgCPI4xSIaUx63AQqO2ktyoS+7\nPk5z12itYvsWSTqL2YbZuUat+Q0/pZHVN7tATJvLMLXB/odnPwnDc2NmEXzh\nrnakMCcwqdjfx3kw/5jrVSi2WxsoNXZOGJGsQIFnQZ+S69xDZQ/j0sW+uOL5\nZpUHXIiqpizfDFHgGJsahxmtbWpcqDjfIR4wdS6smT0a7nA0xIrAvO7q4vSB\nUEqe3JeYQ+NlWz5u/t4005XsR1m36D8mlfU/d9UJqQ+eWR4+LVj/x7ZRSY3X\nysVbN3x1GNUoG3rSEcemgW3mDaNq7GgEZ4xHmy0zCuBGj8Y7nLdaHdm8gqAI\n/eJyMb20L20oRjNTwdvfRydCOYDV38mWGBG3STn/i7dSfrfm22M+wf+JIuvp\nnb8RHgoL67qVoolGCVcIZASFYWQBycnxo6pVpNfiIKmGDxWH9Uf1xb0lATQk\nTUg/TkRT9Fb4T5jE2XT7f9wSzQTuk/ETkndV3yXOHkrHlryQrEAeVADP225K\nobHUmXlz9rYMV00t3+HVOiIBjixsosuBOVRI4OcKbc7fsGqTfmcogOQL5U0X\nLpiKDHDHQ1iYiRFN+bl7/1EWE1dpKgk18tE/gDAK/vFD05NDSQQ9PVb/V9WQ\nZ+tJ\r\n=s6X8\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.8.3", "@babel/parser": "^7.8.3", "@babel/generator": "^7.8.3", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.8.4": {"name": "@babel/traverse", "version": "7.8.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f0845822365f9d5b0e312ed3959d3f827f869e3c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.8.4.tgz", "fileCount": 28, "integrity": "sha512-NGLJPZwnVEyBPLI+bl9y9aSnxMhsKz42so7ApAv9D+b4vAFPpY013FTS9LdKxcABoIYFU52HcYga1pPlx454mg==", "signatures": [{"sig": "MEQCIHDfz+Zy94z/pVomonv4K/8LS1VbTZGByIdI6tslKcc2AiAeIXaTjMvipd63hJ43G6o14eeQUekajzm4RDSsj23R+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs4MCRA9TVsSAnZWagAAKJ8P/jfNTNI51goChlq2aSy5\n84yrluo2TAsTJqEWtU8mWyf6YP/IAlLDppnJ8foy8umVky/Prktdz2kXbeZO\nsMOyPYYxDVhUpXLXN/fuVoOvDIkmpvoxqhfGzyQFxpJuCDkXZSWl9HdmKpAD\nv4PsRGYe+C3MqzxkkHZWfmze9SjopPEVgQNIsm8kdZ2q2Ld6scmhbqBYDsbF\nyCbhDwmuKMWbCk9FZJRMaylh5Be+oEehyabg06v6mK1JB1QH35Ts9/Q/c+RT\no7n2bX6Hfe4lqXIpyHZr/WzFHCTvXA+qIYCEviLrcyrcUxJdjQVkRS9huofN\nYbcfIMb5KYR8Y8vGHVzT8LpGKfPwDsx1fWUZB0eYX57L9ABJ7F43g1/ulG6h\nv+vJ/ZqTv1at3xhfsjUI9kz/8uefQ+ijTn4yAOFKKETtUtjorR5YEa0ULIoU\nUYbLUQmDw5qObfpJd94WbFYOmnwgcbr474d+nTqjJnmLK6XFfxvoCFqsulJ5\nt2eQa+qc761ulwyPSAHvl1+hIjL7dl7SLQ54VE5HdAImmAX7Chgio8vvfdVZ\neHbv9oy6djC+QJMoePlgJNoF0gqhk92/IvUKwF/7uYCJEbKwUjLbZ8eAcIMK\n7Q4WEow/F53j3iqR3ZBCcKbmvZuG3yuoK2ItXs1jOwte0vZ/uGGV3VYnk+BV\nePbZ\r\n=CYUm\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.8.3", "@babel/parser": "^7.8.4", "@babel/generator": "^7.8.4", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.8.6": {"name": "@babel/traverse", "version": "7.8.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "acfe0c64e1cd991b3e32eae813a6eb564954b5ff", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.8.6.tgz", "fileCount": 28, "integrity": "sha512-2B8l0db/DPi8iinITKuo7cbPznLCEk0kCxDoB9/N6gGNg/gxOXiR/IcymAFPiBwk5w6TtQ27w4wpElgp9btR9A==", "signatures": [{"sig": "MEUCIDid9uIOvByS5RvwkAdjSXWLYO2cid7h5RcIb/V4uKCMAiEAx0i1cxcX1xfPrrqguwl5VU2lwk6Y5W9W1aW2xEIhvFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RSCRA9TVsSAnZWagAAq1wP+wcylGe/7tGQgd+Kdxoa\nBy2fcjLxCpCKy3fm/VW1MZBdBAvIF6RxhH2ZblaHdyHOXnYdxyTYl6bV25eU\nSZfBgQDc2bFW2jI5IR6yywevchEGWn2YoPjtjg+YGGEx3tPn93zl+2fodeYZ\nDQDa3NwQMFYFCk9MrWDjjM/OiZD3aBjNGoeeBkCibnSvf6GuEiKVfvAxU7da\nx68yzpLXE0C9NxOlQ71Ky9jkDX3+ei6xt5CObnMtgvdwUNhN8Jd1dy6Zrh9h\nxdsmPTRvVRVqjp/0ST76TYnWOR0ch9y6q32F24mU31gN8fCD1GkvmHUnlT2j\n5H68mrVwdkM0bZ1FmMxwWJMIk0s7wrRwOcN9E0SFFE5si+SUkEnEagFyFl5e\nQWm7QzCPM2aypUybrj0Ij3QRt9yC/MuWPMFWgqRz/v3q2i+esfzilWfTlrJu\nPZZJVYNPH0nGWSUqXnfcTksh5n2fMZEi6HSKbFDvo1CfiOHPW60gWLXa8Taq\nt25qhpWa203SMOxUyDWqujROnKLk+GShqaqSHqrOT8fn15mPc2Kd5FI1MNoA\nGVPV46ObZDGs0AE9LZcRrh5RDilodBsLfgHx8bxRsXbT9VahOcmkCRNyHuMB\nxKaSiGD8sGx/f7l/JBerItLkIV6PVct97Nyu5Ud9UVBmnw/ruYKR2zNVF5X1\nHpsc\r\n=/l+B\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.8.6", "@babel/parser": "^7.8.6", "@babel/generator": "^7.8.6", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.9.0": {"name": "@babel/traverse", "version": "7.9.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d3882c2830e513f4fe4cec9fe76ea1cc78747892", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.9.0.tgz", "fileCount": 28, "integrity": "sha512-jAZQj0+kn4WTHO5dUZkZKhbFrqZE7K5LAQ5JysMnmvGij+wOdr+8lWqPeW0BcF4wFwrEXXtdGO7wcV6YPJcf3w==", "signatures": [{"sig": "MEYCIQDUWcZnlOX1RXsDPxrF9Y+9J07zUCLDRgM4Uqvr/f4bXgIhAKm4+QTVQAYEDDaUVeZP5R1i/Rqy/GVHl3wTjd0s8B3v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPRCRA9TVsSAnZWagAA7nsP/RtK7iX/dV/I3qbGlrI0\nnvshtrbQ7VJzcTLLcvxiZLGydv6KjoCQCaY0vPdxR4LjemMLZeNprcyllQZy\nAS1QFcuLR7xqlfKJk8W+p2DtbRKvYZqysiNiUhz10egU4Y+AfNymMglHipA+\nsZDNLAeHsRby24hLN2xKau498nNWqa13sdFzP3+3zfxv4DTt3lG3kV+dstFZ\ncFSyI7ftMH/XcveMTYNBNNi+GHehs2KZvabn/IwAFexR8G+f84KKGocYZHeA\n/dtZpYyYcM8rLqpxZ8EpBtaXDHJT70Y6eGi8PgvDfMyNpqcSqIUy4sHPsHrq\nHA2f4mc8+g7t0lSL4bob3N9EvWZJlc0MnhpluwC0g3Sa9FdqyMgWl4E5qPp8\nJWe53Vq4/IZZMHM7Maw7NrBCrndtyXRabkvwNcdnbJcAPeN+k6U0z4mRylY9\n5s6+9OPr3SXDmJLJ1VYeeSbknUjai9eth1r5FJSm3KbmYQ3poDH3KPg00MXQ\n73Y22GOan2cEI4LwVsXxebJAHIKJi3N7kXn9cJPE4prwlkRlvet4wD5mS2GC\nSO8tz6zfyvf2r1DKnmJFGdS+DF937wLwkc4x82rd+2YEIYnIvmUWXNLyzt9g\nmPf8mogNIm+9U+xa/7XttYPH/6DNjRvy/GzyntLMQzBhHls8euOFlL0soMgl\nkZ7J\r\n=bjhu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.9.0", "@babel/parser": "^7.9.0", "@babel/generator": "^7.9.0", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.9.5": {"name": "@babel/traverse", "version": "7.9.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "6e7c56b44e2ac7011a948c21e283ddd9d9db97a2", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.9.5.tgz", "fileCount": 28, "integrity": "sha512-c4gH3jsvSuGUezlP6rzSJ6jf8fYjLj3hsMZRx/nX0h+fmHN0w+ekubRrHPqnMec0meycA2nwCsJ7dC8IPem2FQ==", "signatures": [{"sig": "MEUCIQDgpXVsnXeKeMN6miPWqUS/naPZmsvM+8T6hoSsixSsBQIgVyJN5GGMHWdXaSe1YUTJI/c9Sc9epLkLmVPrpfrZ+Jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOvCRA9TVsSAnZWagAAyCUP/jEtg/9QV5ra/M1DmJgm\nzu6iQC6chX3px7YQ2I2/XKAoWfiySZsxwLU82MK8NbRQ/GIbg7m2mIHI26iU\nwEPjAAj3cXlibvvxTTKYli1jilVB5ncfMkdCgzkOUXXzjOMD3MLSweMXP7Fw\neWF4BI3PoTMFw25+Kuq2ZQ/4Hwkbq/eEL3v1W5uDkZPpnvNiqkFuqDVWLoXg\nTlVC7DA07mV2aULax5T6giZdrPKzURnU4NFG7h9ZZ8oX8QXeZ/k6C1H6l5+Y\ntz8s2nilYNiUkUri6v93KuJCmJ6V47Dc4AQ/9TwBtavH/VuBpUi8gxtXCS3H\np3MSl8OGKTfuN1PzT6WayHUpdU4fdb3N8F/uosWIE0+kD3xHa4k7aCtzS/kW\n9+PyuntaBxP8v4iHdk6kPw9zEgWrTRjV0xpmMgP7IigzQkOJQolQYkULQEDS\nnvFnDxKqnTuhRUbvxllZBOAd//Ifk7rquQGnsH2a8g5dkmusW41wEW9ZP5e+\nFqzn3apO4wqEkRses5u884xHJ8HSA6EczY7qQbD/YKoot1HVKhhuyvqyOWL6\nEur0DKkGznII8UsxpcUZQNOegFGS/yQjUCi3xg7c/lCDVg5oXA3COXnPG7c3\nsiUAITw8Apx9+eBzxyppi3rYblpNvA+vdEDO6Cm5jKduqouP8TLnZKa4eDFE\n+vm7\r\n=GWWt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.9.5", "@babel/parser": "^7.9.0", "@babel/generator": "^7.9.5", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.9.6": {"name": "@babel/traverse", "version": "7.9.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "5540d7577697bf619cc57b92aa0f1c231a94f442", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.9.6.tgz", "fileCount": 28, "integrity": "sha512-b3rAHSjbxy6VEAvlxM8OV/0X4XrG72zoxme6q1MOoe2vd0bEc+TwayhuC1+Dfgqh1QEG+pj7atQqvUprHIccsg==", "signatures": [{"sig": "MEQCIQDZUbhZILS0b56ldTpmuHy47wBJf5nAO6M3AkbpwyeQ7QIfAQeMevQyQYe0tsCStcX1qH/IHiOdYAsyOCGXKCv21Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmYCRA9TVsSAnZWagAAtyoP/A86ARDfqli/tSY4ovQ+\nHlNi8QsxJUfQuLHYeLYJnaqpK4wiYNc3KRALpFxDDD2nKrFGC0QV0dKhAwzv\nhpR2H4ctbynnufxIv3hNwtwx2kpg/5Plum/Lot1qDvp2O3QZezfXIqJeYoYN\nOWhbhtH6UqEoRErGIIuiPdaoK53FwM1Gm0WAWsDc1/kMz6Y8XexWeQCCcmtb\ngpKtyPq19u/gXsPWNGHHCsdMOSGL4OHxmzE+7zh4o+tP4uh1B+gyuFyT4G77\n8Gazz+UL00YuO2Zu4QDNnurrl5wgn38ppo0Y7fThQ/6l2jYziY21/++opKXG\njG7uQTnATOfdWz8X1Frg+dWoOY/hCDMtGpOwxSeeaYnV1e0MstAJh40YfxqD\nYkwxpN4jc6CY04ozGoFG/7dlTDvJGOUbpI2Hl+J1ubewIUQTOCEZNQR42MWG\nAa0SbCiqGJsRQdAUprzLaB41JEsw8BD06fWRB+a8RnnPd/eJ73/RZTMjfkuw\n0rnb9NDB5H/fTByDhb4i2vkJCRLYtjEd1skwukra8sy+gl+kh1qAdeenuEqb\nAMUnpbgdRUw56qHodvwyA6MDvz5uDq3fVM081Fh5xkFIoYX7Mivs6m+zDJOD\nnFP+6uNy8sL18aaNj2/2b8rvuvNgv7OF0CB8S9c4sZxvikixrtVusBz5GGvL\nfHgZ\r\n=ArUi\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.9.6", "@babel/parser": "^7.9.6", "@babel/generator": "^7.9.6", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.10.0": {"name": "@babel/traverse", "version": "7.10.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "290935529881baf619398d94fd453838bef36740", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.10.0.tgz", "fileCount": 28, "integrity": "sha512-NZsFleMaLF1zX3NxbtXI/JCs2RPOdpGru6UBdGsfhdsDsP+kFF+h2QQJnMJglxk0kc69YmMFs4A44OJY0tKo5g==", "signatures": [{"sig": "MEUCIQDBxqW9aJRdQA5Z6G987Pvq+FayIi3maaNwDkVoCo1zoAIgTGEFwmnrnnfaS5j32fCc1ACrj3ShjjCBKAQGds/GPw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2OCRA9TVsSAnZWagAA1/EQAIgUfjX0LBycRGSs5pKA\n8SAX60tX+Z2+0cVcikYRhQNUm3xAABquf4HBAlL0yLO330Gk2T+525daRKDW\nQtqaWzMEXb6TTTRgSnFD0bj4tontV/ftJIjU2WjOjpSB/QsqGiXtPU5Y4+0/\nKTuh2hwK50gWobCstPqiV8vnFrdULckDnl0Mo3aHMkby3T3U/Q/AwXYnM+WC\nCYVB1UOhgMTM+KybL1FxOVI3wXrEKzTqtCeGfZCdDokF+JFomEVw5ODJLL94\n/XTM1j4NIjexB2Z6U042tgyj/IuWgLVd/tT/Kgkdehq07uRmD+r1LX6k4vzF\nkFcBsC0YBmyKj186BUiOO6yYKNQCA6o9+vNdZTQXjhcd/W0LYSt4J+QF5VyZ\n+BRGHrIXtKzihpRWXiRqFJwvCvK97ReAcDd+MZpnvc/L1PVM2hB77VswbEHM\n36ET6QHZWP4mD5eJ3yuj3qPGT7eFtwGw53zUrF1SBnlhKQLhH5IzqFAfI6TI\nZroN7ivRRiPyPyf2MgVDcQQ8rN2OrYP51rLbzEkcspYbRxb7SjEaacOuEfyA\nwB4p1WtYcgx3Wgb5jXSwqMNlEdROku03Bcrq3C9Aqx7sSx0zCwZAIYzX9CW+\ndYaS8f+LqL49EtKiz/11UGqc89ZA/yf7LoqvwThdPA7kOego77DAk5GToSG2\n6Djb\r\n=lguu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.10.0", "@babel/parser": "^7.10.0", "@babel/generator": "^7.10.0", "@babel/code-frame": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-split-export-declaration": "^7.8.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.10.1": {"name": "@babel/traverse", "version": "7.10.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "bbcef3031e4152a6c0b50147f4958df54ca0dd27", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.10.1.tgz", "fileCount": 28, "integrity": "sha512-C/cTuXeKt85K+p08jN6vMDz8vSV0vZcI0wmQ36o6mjbuo++kPMdpOYw23W2XH04dbRt9/nMEfA4W3eR21CD+TQ==", "signatures": [{"sig": "MEQCIDqskZ+OzAF+JuKE0IpAEVw8XlkHEaaN4v1dOKVt0uBMAiBhV3Y0Erol7j4Q+I+QHUoDiydKxX8Kq9rIpva5JJszew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTDCRA9TVsSAnZWagAAl3wP/1L1GgzribzqeiukP+pn\nfAnUO5+LhtqbcZ7QEoLCUPDmMC8gKWVtuui0w+MRXFmalbLTmTl57Mu89ZuQ\n0SLCD5FXG/B153vFEyqliCBWQ7Iz7SeVZPzNQR7kHPETP11oWn/GdTtiBlH1\n0alIFRpST3279yjqfyoeqB9MpH+Q+yxPp/JNM7qROELbcK2WCT9CtulJP6hb\n5dOInzPhRi+r9rzN2kBRFujr46vqb7mPv18K9+62AeXyRarbnLLxcGouprcG\nV9pmuOycuM4vGkSU5TlFlXU9OR2XFhpdMJNhcEsCuM3H/F9y1MGlmeDALEYd\nwrwVnbqIbm7SATAd2cgZ+iAVgIPd/OeCf9X6hqSOfPmNVE9pXo6KTG6a2DgO\ns2ZPH1UHUIyuR0Lx9nVsns5ctFdPYIvIetF40S/3OuaB8WVxW9s4hOWVcV2M\nCVKky6ScaAOuFQltHVVnqSBVPaR0lo24E9KpslMWCh9hfqEt55HDMMVQgYCr\nkI+iZxgDwg+bDgfEyLPRvU3g1O6koMJs0xtZSc2sBHXpISEXSmzCwbrL9Hbj\nF3FLrQpbGZ0m7JVfoCtDUqzc5zrxe+l55FYQogJYa+8HoFF1iHgozHIAIyfn\ncVa1fUuDqWusdoB2JdWrxRKhsgPSeIfGS8bhznxkcHEkgZyftzVVjP9BKRgU\nh/+s\r\n=yJLA\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.10.1", "@babel/parser": "^7.10.1", "@babel/generator": "^7.10.1", "@babel/code-frame": "^7.10.1", "@babel/helper-function-name": "^7.10.1", "@babel/helper-split-export-declaration": "^7.10.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.10.3": {"name": "@babel/traverse", "version": "7.10.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0b01731794aa7b77b214bcd96661f18281155d7e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.10.3.tgz", "fileCount": 28, "integrity": "sha512-qO6623eBFhuPm0TmmrUFMT1FulCmsSeJuVGhiLodk2raUDFhhTECLd9E9jC4LBIWziqt4wgF6KuXE4d+Jz9yug==", "signatures": [{"sig": "MEUCIQDpOTwYfPCCzF33CHFgjYtIWGEhMntU2oH+W6ldXONcoAIgGEgzOE7rpVNS0+HleNBGJxViWVZmB3y32vaBuya/qZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYSCRA9TVsSAnZWagAAkMgQAJEdMcDFgBpp1FqK2Ats\nDQhpJjLsINIUmmsLZOE+xbd0D8jdrNUDhq4Fa+8Z7X5rKtAv+Vt2J1w3Mmu5\nDPvGrU3huopZ9nlWyYX6JUJvc1zRU9H6gqy49bpIhtqv1CREH7TDgNB1LA+S\nh0CIYOyJJJgsrnPsiiptHzFFmtU0KyWlLy/eW4deAMU3GgJ9rqIzlIXG+VAD\nEta2hu5e8FmjZmbvcbKauup9/JmC+vACpltVcr58H8wStVmaIsR6VA4CqJJs\nDjvJF1jFPbOwjvS8hGI94df5hLk262iQc3oYUc8sGJd0srt4+XSr53CFco9S\nVQ02MtFvNU9rNY+Tg0XPjygkoGkXkdfCtqrnKynIwnrhgq+mpo+2zkxliQDg\nwl8o4jMYAUV+0LDe6yxEKyso8Y4Xapm92PaSrpGoXc8+v2X3lcl5uiFTELMQ\nVxvqo0bWyf6NLfqV2nT5T3sVjb1xIhMRT5bwawEiOgk6DeDVZwTwHVLc7npD\nNDjrURqqsV+Wr5pUhVbjUpPF0T/ivHN7LrT2gmsIeMybVQHhR5/xkyaWJy51\nzFqzOF96C4UxQiUKpeQgxIyVURfsNdvyrcjlptUJFlhmpkjxs8SkZ/bSM1TP\nnGccC2VO6MOQBSALOV9UB+Hn7BSg3lTBsxSawJHhl9P70w9zckhbxxWEwv8u\nzctg\r\n=BPj+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.10.3", "@babel/parser": "^7.10.3", "@babel/generator": "^7.10.3", "@babel/code-frame": "^7.10.3", "@babel/helper-function-name": "^7.10.3", "@babel/helper-split-export-declaration": "^7.10.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.10.4": {"name": "@babel/traverse", "version": "7.10.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "e642e5395a3b09cc95c8e74a27432b484b697818", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.10.4.tgz", "fileCount": 28, "integrity": "sha512-aSy7p5THgSYm4YyxNGz6jZpXf+Ok40QF3aA2LyIONkDHpAcJzDUqlCKXv6peqYUs2gmic849C/t2HKw2a2K20Q==", "signatures": [{"sig": "MEUCIQCY0402eXGolh+84C4lCPN8XPYwReS9CF1BDEp8qQ01rgIgSzKsAZwdP6lKOi2dhQoFNb8ApoqwIyTtmVkeXWyDF+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpmCRA9TVsSAnZWagAAXo4P/iCR1OJGwFY10ncQwKM1\nft3arFip9vGDQFGUNfBx3TI3AmbY0qocuwuVD08QKScEWCC7/raGRbtC9fOe\nga1Br6CQA2WEk6BTorGtJ728HHqw0wuYQDzHEAV4qDyahKWNXwVUbaWb5FZo\nvgNijq0roMgYXIA+BWKNAVq7A2lX0+Mlgg5iMjAd/whlbfMbLgdKaB+nvM1d\nU36lAnrjHKViQHqnpxyNPeUvrQQvjtgI3ZjrdheBEBYP3dspXOdahe5I976S\n6Le3iTlsg57uUjnN0cJcNHfsNmfrAFoEEt22zV8PdrGj5m2RaPhnFTMxygJF\nP9bmdU53OhhuWcUYAJCndcstKs5BKozu++NZQMKP2ngp/aAvOLBE6uLs+dq4\nCwy4Bqe2r/1F5aIQgBKdF0odIbeYZTY5Y9hbsIV6lp4E+i7OsjvW96yBCEif\n6vuCnWi1FVG797NgxhhXMhgaHW1HzHJcOL25mbPB7aFOGQ6Aul15j7cqzf77\nXz7c5JxoUbDjDhGCzcfXSaQEs+6xyrX1XHOjXsoTJuMT+oFonuizKKZx4+9f\nvqXEMnAwnwEfD31NZ18dPiqonITsj4kmEa496dNGSQKD59IQaHD8QDU+kKhS\nel//4eQznsA/eeHhQPmdi8iNQzFPyKqTMPahKv58LwV1x1YjGXKWtiSCP6FC\nC4VG\r\n=j+R5\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.13", "globals": "^11.1.0", "@babel/types": "^7.10.4", "@babel/parser": "^7.10.4", "@babel/generator": "^7.10.4", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.10.5": {"name": "@babel/traverse", "version": "7.10.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "77ce464f5b258be265af618d8fddf0536f20b564", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.10.5.tgz", "fileCount": 28, "integrity": "sha512-yc/fyv2gUjPqzTz0WHeRJH2pv7jA9kA7mBX2tXl/x5iOE81uaVPuGPtaYk7wmkx4b67mQ7NqI8rmT2pF47KYKQ==", "signatures": [{"sig": "MEYCIQDS+tX3u1ogITeDUR3w7ecp2/dc+DtGrT+b2AZCpoBvqwIhAN6mfa98sPzpMORHlAv6SdTdeKxDxPUbL4EcQjty0sCc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbkCRA9TVsSAnZWagAAIP8QAKJoPNvXHU1lc89K/RQ1\nL49T3ZoiEtJYM7d7hh8zGlx7LiOXJ9iPDDn79Uh+ZjEOidDv7SRCE/pay/nU\n4qDDj7RscvxVuOyYJlG+vW53ECnvR23hKeUHo4aqm21ard1zUpODHOZmiTOo\nrXXZWHtdm82+Y1tqduTXCfdBdFqSaGKH432Nkr5srC84Vg1L/aRnavmbB7zA\nK5uVeQls70kVtymKrp9mbUhs0nbyu6xVTuSvd5yzjQpjjMxGSKIYHl0ePgel\nGub9yxGGgvtmHXNVaoZ0Va9B8z9gHc64A7ABKqZh3TOxHK51QplLXGfhq0qx\nqY5s3U0vtpYkkDsYcmD6G6wP/4SSTNpKKnFoVY2LpeNh9quFNSr+qo1RQl2z\nArAb03IIyPL55BdgIzTAL72OGxgM7xZMtAKCu4KxSyANY4EsA7teDAJa72BI\n4ZjvBGSVYW+VBgafwevu51IewhIpB5/Wg+ohCyn7YaI1Qk/cW/8z8M+w/fFg\nMPJa+CnYSeKeUm8+YkiCcbRkWMM/ocSST4U4REkoqR3L8TKcE7jUv8n9wb0k\nPqaYlTihVknb6TvZIVEo4av9NU20LGPqcS/AKtiZA5YHEPEErH5bsth2FREE\n5B2scq/NUJlpEFFdT3zvVFvZdb09XF6QXfRzVB5ulWGtHlLpeRypfu/KC7QS\n/7kf\r\n=Zkit\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.10.5", "@babel/parser": "^7.10.5", "@babel/generator": "^7.10.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.11.0": {"name": "@babel/traverse", "version": "7.11.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9b996ce1b98f53f7c3e4175115605d56ed07dd24", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.11.0.tgz", "fileCount": 28, "integrity": "sha512-ZB2V+LskoWKNpMq6E5UUCrjtDUh5IOTAyIl0dTjIEoXum/iKWkoIEKIRDnUucO6f+2FzNkE0oD4RLKoPIufDtg==", "signatures": [{"sig": "MEYCIQCVJ26PegEQUq2y5Ab53ogDT9YMCNcqEFmZPTsQQIInOQIhANFT5bMAIqnn8UeNP/eCXFqvy5TDbnvpoaFA7zfy5QeO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzuNCRA9TVsSAnZWagAAY0YP/jXgIS64AGfHpbROZN6x\nHEUrkrK4dXUW2h81U8DK5Ca9EB+TKkRDpMMQOzOHMyipcyB//zd3ar4RZjlR\nfYTEItrT3u//QaNxhItCrEkY/lC8yTwRpvzA9qqS01UnYNWR5+CwvWnDu/MG\nlDi+MYBdoJ+R6ZUk/P/8C8+JVjaFPueklH3jbQJdNSOx012c5SzE9bNoGYaf\n89I98Zix/a34zX8/AaWNEF8CPrK3stickNa7FBMIma4EVfKdzDG4icjKOc0P\nMCPdHfprRJzyxVFM2hGJKjxeWd3OjcO3p2Eg6UYJ7arPsdD3ZBkjQx91FLX4\neJfR0xHw0w8ZWcCfLKAcGlJ7wdWzJDa1MkfbagoPgm4rXmC1vzSTyqMoY7uu\nUb7tk/rVONSUMKOy/ucIRqSy8+ztLjKybzolLWzC32ZiVwvRwYkn2k/Kwf8z\nqbu2cPAfZFtfXX5J7E/M1gzPBX+B5k2uKoeOwcnsNY5YwksQOeL4S+PJVVbl\nJVY9IAT7QsZ8uk/6Y6u/l35DooTH9+fjrZTkulArtkoMVBACLg5ttxfwqIaf\nCgJQnTpEz2G/SlVJGA4e3vOgQwcExMxr8973IbgAGgoG1ITT8xHgU/Bjt41t\n1MuUr2kNZ46/TyVuXgqddGHtZC/GPRUbAotH8mFfvqrz52C4T0xTHHV+6zcn\nGNLr\r\n=q3Ox\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.11.0", "@babel/parser": "^7.11.0", "@babel/generator": "^7.11.0", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.11.5": {"name": "@babel/traverse", "version": "7.11.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "be777b93b518eb6d76ee2e1ea1d143daa11e61c3", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.11.5.tgz", "fileCount": 28, "integrity": "sha512-EjiPXt+r7LiCZXEfRpSJd+jUMnBd4/9OUv7Nx3+0u9+eimMwJmG0Q98lw4/289JCoxSE8OolDMNZaaF/JZ69WQ==", "signatures": [{"sig": "MEUCICHZ7UkkuJ4/uKEGQa0pZ6/v81aC8kOOagyU5w9YN5XNAiEA0vDCQvWIWH0YvO8wHvTe61VXNFQWv4WgEVexzT57jWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTVdTCRA9TVsSAnZWagAAb9wQAI/gKZ5fm7MJBaj8qEx/\nvHo7mLLylt2C6FsVvdC1Vy9t6LkIDq59hZ7Wj9jpxI7dPmE2J40iLEiDyvpI\n3yadHqouhX/17Kk5UW0SDCXzFuZbCb3Q7W318sCWTuctP/dyZg2kLQMLs6On\n6eVYP2+sBoThsTSFOa7/9wURpOa6ydc9+meTfNjY4dkd5pe6cxK269FtOpGd\n7JLpeRudy6Kb2hEMl5VUd3xUgNA9fygUmd4edCKPjHSMqBOwlP6xVP8XDM2O\nS/4te3rzTI/2tP+/6CcFIC0/XHdoRxPLvZ1EzsOejOnq1zJL/lGoTSvn8fLR\nmSRbUH31BiFYv2+HZNhMEaQNvT7l41/YwFTxRKjS6rySrzbXt94hYh5y99aW\nEYFUDI6Rzz4aOwvKnA9DEnUsFllymujC5dTKxW5FQ5E+GMElhv2mFvgDSOxL\nrqKSjr3QXd9ozZpKx29t8CtfqKdWAyDWD38CpH/pRcAvPoqXotM0dstvo/oe\n0TdyHpltZPqEuQSipmY7dyJ4/0uq7vn3hK47Efzn1uZTXJTk7Tg7ZtHaPn+W\ncOCTxiJsumMVAFt+jDDa1sYsVqip+WuYNJid5KnOx7Uebddpne2IL3zq2iul\nwJ1tkkX5FhsIskPGIWd69jf9fGbzbQh66rcYucoNxBjLqOXZ7MHrGkOZXbQW\nbnMM\r\n=7ss5\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.11.5", "@babel/parser": "^7.11.5", "@babel/generator": "^7.11.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.0": {"name": "@babel/traverse", "version": "7.12.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ed31953d6e708cdd34443de2fcdb55f72cdfb266", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.0.tgz", "fileCount": 28, "integrity": "sha512-ZU9e79xpOukCNPkQ1UzR4gJKCruGckr6edd8v8lmKpSk8iakgUIvb+5ZtaKKV9f7O+x5r+xbMDDIbzVpUoiIuw==", "signatures": [{"sig": "MEYCIQDkIP7tm08Xsh8ThZcIur4koT9QHUNoRfydCVJvBJoLkwIhAKY5A17vELL+z57hd0U7kmA9DK2WIpF2mM7kqr4HltnT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mJCRA9TVsSAnZWagAAkukQAIU+qMy+tELjPb4ciR/d\n0gAYsrNM7HuDRBcGdCsPQWUiqP5sYMzg8FKJga8bdqIyzzA0EEssivUEw8IP\ndt2jUyGD1WA2BBdg0uBkXyy5DridfLseAhyrEoFlARO1muayrm3fJc5kDpVu\ntASEEuVCi2a5xjwI0mO7TLCw6X0tE2LDk7QmrHrs7CKwTINGZW4UIyVc2ZOA\n7iea5UhDQPvHEJgmTNSNmnp0RVLpt4uHpH3OAbNanufKunvKOriIxSIYLTkq\nDl1ckmVotPIfgEKfLMuU6cHLaj57BAvZ+glivbeaPG7fTauJPEwLNWRsA06K\nF1LHKobRpLwX8AL+b3tLqpiFYcCVQnTj7Iz4sfVK/KQGpcLYTIbWNAl901Rg\n7ehH9Ctc377FZNoUgVnEDd7/u3F5p6R1UkgNWHIQevbb1J31GCduL/qhcdik\nNhS3Gu5OhwLq0D0WIR6o4GboUTq+BXsG8nSzSwUAkggnZCIv7rbKbLi+3JLF\nL+cY0D46FneADMQMkVeNZohFebj0HmnyJN6sxZ2dgWLIVlsUQPXsI6ktZ/Rs\n0/UfrnLjWjqXb9SyKMYqLivTqLYHgEQAeyQ0BCkxxYLwnDCJd0rW7QxGwZXS\njHIkZ6l9PZX1HhuRHBNggsVYDCJOXZc60Ocwera+0D2b2nVDRkSq7jfuNIjl\nFb1f\r\n=YdD3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.0", "@babel/parser": "^7.12.0", "@babel/generator": "^7.12.0", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.1": {"name": "@babel/traverse", "version": "7.12.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "941395e0c5cc86d5d3e75caa095d3924526f0c1e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.1.tgz", "fileCount": 28, "integrity": "sha512-MA3WPoRt1ZHo2ZmoGKNqi20YnPt0B1S0GTZEPhhd+hw2KGUzBlHuVunj6K4sNuK+reEvyiPwtp0cpaqLzJDmAw==", "signatures": [{"sig": "MEUCIQC5Btx+c7XVv4jMYzk26bfiL5GdfvjOcU9zJwoh1wvnrQIgEcnQsBW1KyOsINKjQj6HTnx9C7gaKzFdMDnDULFmEVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAbCRA9TVsSAnZWagAAkXMQAIW8gWuu2sUkjj/Y0YRt\npCcNRKNTASPMcgjxadPZdA0Xn50RRf/c1KqZF5FO7eCF32Kr7PYFWBXoOvK9\n8SdHrSgblTvji/PFSmofzkQNV2NGQ0BEUwhGafs/y+fZjpX4b+stx4B7eBJm\n9HwUlFsZHD2poDyeL4lZ8tpmm+ODI0IEBjpoxAW3JELQHNha/ENSo45GmwER\na9QC3tV48Ac4KRmcY2T+ykwglcuQABjfHEHd/FotIWBC6H64kJW7XRAzJAud\nuFGW8H0y8zahbqyHXINEOEF/4+HYE3pUN7KPbjJeftA8BVoYTt17AL9+CLZd\nprZYS/WXDeNad/5LkHfZKSx5tMj5kFhjJBrr4B02pYYd0OGgj8bBGRn4rk1l\nrp81f/9Je1y5yBWHW2wspRHOPRDkzmjr+hv/lbJGfeiklHRZvXrJYqJdgI2H\nHTB1WVVIBs5YnZ7MWMYMHc8nVqVQgEfOaHTa/hvUrJJY/Ya7NSB4ofd3NWyO\n5AOpT2aF+I17a08cehsS/mLxy4+NEvXviGgknan0rYQUPjM+soE1rdhoYoIL\niIsBjajblJb82CQosYYvsdhmur0Wre+52QI5UcdcdPhxfOVDY6B7ezYPxGsn\nqWuwvldj1180ZZyv0+K6VuudAqoMKCxFV3Jm9LK4jyoSL8CrgBKwupZDw1E9\nQgDQ\r\n=Abrc\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.1", "@babel/parser": "^7.12.1", "@babel/generator": "^7.12.1", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.5": {"name": "@babel/traverse", "version": "7.12.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "78a0c68c8e8a35e4cacfd31db8bb303d5606f095", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.5.tgz", "fileCount": 28, "integrity": "sha512-xa15FbQnias7z9a62LwYAA5SZZPkHIXpd42C6uW68o8uTuua96FHZy1y61Va5P/i83FAAcMpW8+A/QayntzuqA==", "signatures": [{"sig": "MEQCIAXJnFGFnMTiGE6jCmZMpBdr2tGNH9M6G3E43CuZjPmmAiA0JJJSRE5vdAGvTz3LSgecWLLYtxXSiSfF2CC7z0fSzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodr7CRA9TVsSAnZWagAAbLUP+wWj7WugOxfICbMstL92\n8T6MGdGs91kPrDFBaJ8D+V8erRKlRG5i37TPZlcMDzs62XLPhbd5b6JfH3rW\no7pN1yZXSWq4QOs3iI44ffJVDQAjzbS4eRknVqFqf4XyLDb4b8BjzrGXHxT8\noEbAvN+XJ1h1SOjlu97E6tE8Z/ZOmc37V1kjR6aykCO3E4694tkLSuheHv6w\nk1AAOwxi88jrkqhDxUrvd5Cf/eMlF8tIB2QAbxqaJeS0Kg5Bcd/y+1yQOMju\n8TVakSqcKd9ALGECZVedaDLWZi0T5ag4D0GRDOZAIo+YV9obZpD9hyVH370E\nmbXddsgdeiV0SH1/qT+98dyuqg5jb/mIubfe7F+RVi3SUCfsnaJztOl9paTk\nCwpcRkmc4/Scukqsa5IXZJzY5UwgdAGVHCDQb3UvFgrcX7hXv5viw/NBwPMI\n+4xVIgVLtznyDNnxaLBiU9QQd5iTReX7155gYy80eUzKS/Zc9trOSq6USmGL\ndx2rrpjiLCfVT6RtSaYFvrghp6Vd3LqQcHSVkIcOSwFj8QOaiWMEoXK7EYQp\nFHA83BsNH7ekfSER7KqyDmAPcRN1hsDrZfeaZI6ZiGs+U+Ie+s3B0xT2+iOS\n2pD+uJ+9gO17dav2PGA7wrw22pxl1+EGrODeKGzHOUhglscKMRJXE2Sp6bYA\nBDlI\r\n=e67R\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.5", "@babel/parser": "^7.12.5", "@babel/generator": "^7.12.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.7": {"name": "@babel/traverse", "version": "7.12.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "572a722408681cef17d6b0bef69ef2e728ca69f1", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.7.tgz", "fileCount": 28, "integrity": "sha512-nMWaqsQEeSvMNypswUDzjqQ+0rR6pqCtoQpsqGJC4/Khm9cISwPTSpai57F6/jDaOoEGz8yE/WxcO3PV6tKSmQ==", "signatures": [{"sig": "MEYCIQCMEMQT7x5cFYbpsQwUPcUjBEWL4109wtVMj0agZfyhQwIhALbz2qX0oZR5gyrN+CPUfzJqvQFRYUmLOaM55Ox+LSKi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+zCRA9TVsSAnZWagAAUQMP/isY5CwqH6X4hUlMaa7A\nBDPKx5Q6pZfzPYc7IhfP07nspBz8GXbr4+hxzd3Pr0RZGRubBRwNaNUsdqUr\nW/a8S6v1VoTMA0P+Fz94HGoVqoH4rFT864cuCBkgMnVtEUNDB6eltILUgX8C\nmLF8NtB7D5DC3raWbS36yxd6v4jqyeQQaTjosyqSsd5fdrwW+5/ftVdcpTGg\nMZMEATVagOqRtYapUU1LaHTF+2gOKrx4T/THUlzQEs5azqVs+Ccu+mRrb8hq\n4/1oYyWyBVRDt3BjsIPH2lqCjEOFRFZMTZidekVnKcPk1dV+MDxRenKca0zl\n35Lqj9lRH5zBxhpHVswouUpFWQmcYwSGi3MBToHuF42W6uAmtcu7BDZuBtyX\n+UdnkZuiLamlcyx0F4Fysjx1t6ouqIyWenVN4JIfzVOjuY1DvlWp0HMeKrVY\n3H/5Njq92Z3+RQdvXurXc+B0z7IOvRQ1QthVfN9p/eKHo8WLX6+D1NjEmtaR\nZZ9KSWJXdcT9ImSYYrLtxWS8OVBZKwGE2WajltDljNHUNvLBaYKyvyRBqLo1\nU6112QbSJhbPLdpAe5aD7UqYdF1/gY7aKBGYpvF/31XAJ1BcQYcAWRf5o89+\nW4lZf92h8AU/ACYl4GLSPAxOT0Ok8P7WkeFQTAkDvE2z9H1XnEzgIsPWpNJt\nD+CZ\r\n=fe9R\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.7", "@babel/parser": "^7.12.7", "@babel/generator": "^7.12.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.8": {"name": "@babel/traverse", "version": "7.12.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c1c2983bf9ba0f4f0eaa11dff7e77fa63307b2a4", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.8.tgz", "fileCount": 28, "integrity": "sha512-EIRQXPTwFEGRZyu6gXbjfpNORN1oZvwuzJbxcXjAgWV0iqXYDszN1Hx3FVm6YgZfu1ZQbCVAk3l+nIw95Xll9Q==", "signatures": [{"sig": "MEQCIHeNtfROPhmL9IieIcI4BVpASf60ZB19wAnnSMwOG03cAiAVHxPV37HOTHBeqGxN9v77ZrEdGUf+UXu3xdK1nmvBxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvDfBCRA9TVsSAnZWagAAFDsP/3hY8r4DYrZGllvYAWgT\nt2Ujo9dOJeCtvR9Fpjry1FctPKcVtInIPBHz4l5INa49tUZF7wNXArlddgVm\nd3gugzybkffnHt1XpZHRN3seg55QNQavD7zCL5qXIJN+bw4P5mHpbaKE+BN8\nXxLkEaAVeWrmqO9NaTOd72OIKoEgEOpMQwauY88NHQJgVCkGSkovgP60dL3v\n8IZuXcbm16Rvxcxe6rLmdeNEK6oO6aadlxE68avAJfk016GBvB9K6vCL4HvM\npg87nMLUSOSWJiU08p5EqbI/itJg+hYK51PuXolY8nLlDzQcSSEqpTexs3ld\nlKgf3N5rbO2S4aW+L9RHLyNyn8OaaxkfSStQhtJAdgaUVDUvffmpouafzTz+\n/+ealPutqgwMSqAbnhap5eoS7UIY4ooZN8OtW0tqkau82gaQ0F8iK/+1i2vN\n/f/5R0L05wCyf7PG9qyHgS0sCPVMO8Qsdv/BB+9Dyh8ORSnl8x/ACMyRVyxB\nKiJ76cgek2PTSLjWCHbhkjSvsrZBtfBvS9Q0LDq1O9hMit3JmwOsY0kkx/Zm\nTiknDc5qkTXDDWEYWMCBHns6Y0HvhfRJV2GgKBOVYQTdhme+IvN8/B9F7hoy\neCbh2YRG71vHLoYqd9/ozOausjo4n7/NlDpvpH+v8Rb140wcZ/e4gpHO8zKK\ndGst\r\n=Xm+R\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.7", "@babel/parser": "^7.12.7", "@babel/generator": "^7.12.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.9": {"name": "@babel/traverse", "version": "7.12.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "fad26c972eabbc11350e0b695978de6cc8e8596f", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.9.tgz", "fileCount": 28, "integrity": "sha512-iX9ajqnLdoU1s1nHt36JDI9KG4k+vmI8WgjK5d+aDTwQbL2fUnzedNedssA645Ede3PM2ma1n8Q4h2ohwXgMXw==", "signatures": [{"sig": "MEYCIQD7sa+tB0nUq0wX/MYhzJtQ96zEHksXELapZkApN0bAfAIhAPiKO0C3SCcIuRS64mbYEEw9BcBJQLhiZD8D5YvzYVs2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvXTiCRA9TVsSAnZWagAA3RUP/1WLaa69Xz4Yh57QBRSw\nA8YEIvHixpnRcxBU3yYYizlN3ESFTLO5VdDK6bimpM6Hl4K8bvCm+k79f0ZI\n6FRxadbJAwNYJ3ZD61O8ZH05PueJqGclnou5v/cWbo9OJ+0meBIiEZrJzbzj\nYC8dc6vK/YrKkv+DNuZf7LKnAtg9lcr7wKx6xOLvEkhcPtdNdQEyvSqgstrL\nlwuM3aGH00tljyPT0kzTZ0zIwL3UypCz0L2Q/EeddKew9Exsqcry6X0jqCyD\neBjOjvNrOdaziE4SYxhwLO4nWIcqP+avOiZ61we0yKZZZlQEYzsasXlRshg8\nmNTIT+Px2K11JuTEPfjtkOFnw+IfgT/p+QwR8BMUw5Bdn+cQ3n9wiUNoOVKS\n8ipLAuqDHDMDKiF7BGnhkXnJE6UOnVtLOLolwXEwBVleVDrQqsTEj/YyrFm2\nIsUJDcLzWp4jMJyT+Hdhsks+hVZt8UOiIk4NovD/D48lhaSLHVv5j4/ijg66\nF5ZSSmI+/PgloCNJP14mzaZ/bmHV+UY2ek22nFruKixi79+/Yx+JtT2amsQA\nw37AdwVb4K714sgKbPMogyU25zFPBcK2Tgtct+av2sTZsH6YhdF2V4McDPnC\nnP6FWzf1bZHQExjgmf0qnp0/L7cMzFgUb/zVKujI58tce7xo87bcGO0dBtG+\n6K7l\r\n=jjpV\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.7", "@babel/parser": "^7.12.7", "@babel/generator": "^7.12.5", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.10": {"name": "@babel/traverse", "version": "7.12.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2d1f4041e8bf42ea099e5b2dc48d6a594c00017a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.10.tgz", "fileCount": 28, "integrity": "sha512-6aEtf0IeRgbYWzta29lePeYSk+YAFIC3kyqESeft8o5CkFlYIMX+EQDDWEiAQ9LHOA3d0oHdgrSsID/CKqXJlg==", "signatures": [{"sig": "MEUCIQCQgJcoFzAI+QPxrjoHHedrhysWlaYQzUST8iRu1iQrLAIgJpK6lYd+ny3FAJmi/dSsfTHF4jcpRilh1l3YwTP6rr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQtCRA9TVsSAnZWagAAFskQAJnNEP+FKm+erOdxOVHW\nf3SR6/9TYiLR0BKvQE/ncnA5XL3pOnVS8DftyvnpTwE9lNuRNNMMPdoRfWWl\n2YfllGFt/635Nj9SFxEBM8u53qGtTExpCN6iXV8HGQyX9TrFrhl7hvmg+llJ\n5VeNWKErXd4HGvH1XD4sADdtzvVcqtKt21ukfjp6v7TRaNtWtHkRPWBsumJH\nAdUqr/XdtZBFENm0JZXrWvHhSUjcxkVjWSyKOV8qIJCnte//03D0lkekhxoH\ndlplrb8w9fqVyNQOKkK/vO7w2SO5fUMIfw0SnYfMjaaUfqqbkeWGV4tjS+1X\n8zy+iPwKCnvJLsKBJWWYo4B5lAVZ6m7wZxb2siM5U+ouTJ1nRos/qwA/1PyQ\noLQCIPqa8DWT0ohFOkQOW/fRWyg/LKhoExE8FlyP2KY+s8wWVQETDDhqRwNg\n5AkN5zTXkN23QXaTnF2CcZQhVh1IPcCwKSE2cuyv2NONvLXCTIbB2732+9bj\ny1xa28s+Is984UNZS/hMjjGFUoHpgWkigtGoMd6n5/Kw2Bek/lyEeRXctYkn\nsxV9hFjQsJsN3DH5rzdZ0hsONcZpvI9SUttMkf4/udGSjX3MXO1pghe0zBej\nBQehHA++o1c6mYE3iOUq5i9THwaQreiE7M0gHsJ7gFHUWj4WwLivVKzZbf+h\nDpRs\r\n=f0I+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.10", "@babel/parser": "^7.12.10", "@babel/generator": "^7.12.10", "@babel/code-frame": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.12": {"name": "@babel/traverse", "version": "7.12.12", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d0cd87892704edd8da002d674bc811ce64743376", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.12.tgz", "fileCount": 28, "integrity": "sha512-s88i0X0lPy45RrLM8b9mz8RPH5FqO9G9p7ti59cToE44xFm1Q+Pjh5Gq4SXBbtb88X7Uy7pexeqRIQDDMNkL0w==", "signatures": [{"sig": "MEUCIQDCDCeCpAQChWnszBm4xo08x7EYxva0GFsZLprBtAAGLAIgCjJrC9NMWVjBbLTij1Vr2RjJ+TPLdqtYmoP1w1px3aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf406oCRA9TVsSAnZWagAABt4P/3HRa5SKjdJqfy7zFshy\nLFS/j+9Z5HAuZKpNvUyg7hlQm8l9RaCrypgUGjp0FuqI0wAg1kpyYAKTiPGf\nT7jr9DkoZ4BLNZlWzNNDfGkRm7RSF72DBCOXlDKMbQhh0GUFgJ1fd8pKTwF2\nHz/wCX/M9uV433XxbWaEyTDs8WwAGCe8oytyshHkvimwa0oF9YMYn82n4tCs\nVwz9vwtaSE9xVD36ONr8rWhIZ4jbKoCyBcADmLb7bLRdc5q6YbTrqMT8nC6p\nqDpnQqGGcZBP2Lfonoii4oTnR9awwYyh8yTPh9pf19ijBMqmxi+YbvB99gLc\nL3B1HxnseFHaABPGHMzizqR5Zz3Slb5JBMIQYeFowhFi7Yy+9KRw1D3esX2S\ngPCycn6u+746Od1EHYJPExSSVSS3cH3W37zRukhy4lRfpX4eduE75ehi1GS8\n+UEkw5ktMdUjMNBWcWuGuDvCIwAgqiduu+74axddcAU1Zq8LAbjsmZ/q+CKW\nejkmjoxZG9e8R39GtdiR6rSkayH0wXOJrhKsjpgk44clL0c5WHFrzo+ecdPc\nHBju1xirk5+rOLpkuD/08qxnzlsdxsMyWMGKoFYHWqIX99duPTd3oeUC2nx+\n24HYTvf8smZu8vxAPEEFgdThO6U4x+7b6UdpHtTc/Px8+oY+k0NWVZkD6+kF\nKEW6\r\n=DRLY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.12", "@babel/parser": "^7.12.11", "@babel/generator": "^7.12.11", "@babel/code-frame": "^7.12.11", "@babel/helper-function-name": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.11"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.10.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.13": {"name": "@babel/traverse", "version": "7.12.13", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "689f0e4b4c08587ad26622832632735fb8c4e0c0", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.13.tgz", "fileCount": 36, "integrity": "sha512-3Zb4w7eE/OslI0fTp8c7b286/cQps3+vdLW3UcwC8VSJC6GbKn55aeVVu2QJNuCDoeKyptLOFrPq8WqZZBodyA==", "signatures": [{"sig": "MEYCIQCKs2idUSxr57vveZoZ/FlbV432bcbMwbdjz4TvzVIimwIhAJGGGga6Wl6eF4gfcpn1X+ex/gJrSR8isBQvwHpWNFjS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167371, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhRCRA9TVsSAnZWagAAH/MP/jN+0MSCed6wTEnCVn0e\nJLUQ2Rk8am22jEulvhzme+h4Ha2OntpNaCmXJ0/78mqsvHzMHptQxsu8tUN2\nM1Gz70ctYedMR2ChdZFryFuVfWONQejZjIYBfL6mRbO8MOVgWgHGc6wo/1Af\nO1pN9rcgcwGq40ImK3i0zp18Og92a3Ur/0RIv/R0Uq+v9morUFBbKXJWuXCH\nrXJR9R+DXISAUGo2ycpVoUpUYCe6uma4oAtj7+7udleGywAt7ztK3pUEU17C\nWI5ED2Ijr1VmOFm5/rWLVhiG32614takaMHBLfuVbwQ0tOAAKeA1xKQrPwBI\nZU5cFthaWtW6q9VvLczac+MXEUBxBuKta4vsLFg0NNXOTuAbVCFCt4B6q7rj\niyZGLJm+GpTtP0Ihdct05alAVSZ8auRu6+RwGwlkPZ1dtl6v0b1e/Orgf+S9\nwSG3xmJcpE2/Zib4ELrK6gVe5zTZYFuFbZoRf9f706wCuMUu1AjxL0DeoM3B\nfaBHDQQswRBoO70BiCPDsbkD3g2ol6wsuZ56XBljLylHtooBsuMLDVIPcti3\nS+ICRSe5Axb45Gjcn7DDd0tPuzmH5ScCswM04nqhOwBKI97gTXNwMWWcPbi2\nyu4qkJ1snuH5B/mBq3Dkqkclwujh224dYffiWQ6ZxqSOtljFgFMx5b3HvoZp\ncYiE\r\n=FaH4\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.13", "@babel/parser": "^7.12.13", "@babel/generator": "^7.12.13", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.12.13"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.12.17": {"name": "@babel/traverse", "version": "7.12.17", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "40ec8c7ffb502c4e54c7f95492dc11b88d718619", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.12.17.tgz", "fileCount": 36, "integrity": "sha512-LGkTqDqdiwC6Q7fWSwQoas/oyiEYw6Hqjve5KOSykXkmFJFqzvGMb9niaUEag3Rlve492Mkye3gLw9FTv94fdQ==", "signatures": [{"sig": "MEYCIQD7UkP+ZJp7J9bsanW4bxBY71EsY2ImLBJoQM1pwGQg/wIhANStEg6i9vwmZF5/ZzVjqhLoXsFQNFS56Kyf3Nyc+9aU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoQQCRA9TVsSAnZWagAAxc0P+gP9lx143tZc2RPYuCuA\nee/h/MmvVDsjIGgGUolO54s3lm4PGd2XnMiKY9VixeDxU0SXEo/CsvIhg5dV\n/SVNMRtchchIMbtVot0OlNsAQpy+tK65eGIy/DIRawWdW3zlfFUcZYcqQASL\nfJY1g8Jz256PsZfZlHODgPjZYmsNO/rNt0+KJavOr13b7vx3iVXiDEKciJJo\nGBIEDvM+ZjP2E8RzMUQ/oh8Hz4IUk9mNdUxSHssybLEdEy4mwxruk5R/SP3a\nftkIgIdIEeDwBe0byiyximAVYCIyFeZw5s5qhN0rWWfg8dQ+SlY5/h7Wsx4m\n6creBz5rgGmkD5yKIcIGHff4WZo6sGfKbsHpYZjZQyF0s/4cF4SAT3HkimeB\nD94GThDctA2HLR5sMaStVVqzpdWW2uS9tbRDYd8OzqyKMHdP5xvOqmTFDTak\n3IAILTcXhkvDJmdJeAy4zs9ECW1Gd6hSALtVFuPY5Ky519ZPe0QERpRb9dlA\n7jVS3OVMJg1m7ALXofogPdEW/ukI+U+wXp/aRhMj/HQqb5dvM1iJk0kuvRsh\nwxRBDOftzNrpevrSFY0D0qH3hFp+sGb8yMiDMogGfH7V1yp1p12WfAJXhDTD\nogtIshwylBWpwgcLrKjZFLM2YQrqeug6jQ/CPGyxtsGIiN83AZfBCDowv5Md\nfsqQ\r\n=AaDP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.12.17", "@babel/parser": "^7.12.17", "@babel/generator": "^7.12.17", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.12.13"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.13.0": {"name": "@babel/traverse", "version": "7.13.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "6d95752475f86ee7ded06536de309a65fc8966cc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.13.0.tgz", "fileCount": 36, "integrity": "sha512-xys5xi5JEhzC3RzEmSGrs/b3pJW/o87SypZ+G/PhaE7uqVQNv/jlmVIBXuoh5atqQ434LfXV+sf23Oxj0bchJQ==", "signatures": [{"sig": "MEYCIQDK/fsN1vTjU9+DIqjdx7PR1QaaOGpQ0PKIh/iZLDiTyAIhAMWhGyveWwRPeKu3IREOtRHoQgo4eY6QtrzM7ArjT78+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUhCRA9TVsSAnZWagAAItYP/jdq11HmL2rkof3+01QQ\n54uaMLwefjZ2YhAIJyN11gKkM6Re9SHBy0Rf40maJIOBYEh7SGIc4OTHJt7M\nqsyoJ+bMLLTgBETFipTvvYbCNJZhBH3GfrR1SlF0TVo5xfqyuQ/NdWy/8UTj\nwt/dW9y6RXqCxw9LvCSHu+MOAobB7zPvWnEfIVo/JZq/SAQye3Zt/MmigufX\nMUlqeD2fxrO7BAoK/5hwkZ5n0m5OdW8S2HHQoMt9EJUDf8+polZKa/pnz7aJ\nEZFnvIAR4UA5QPwLu6TbVNItA4qMdL1Z73+bKy9PL9yxJdEutzEiah8Ohn1J\nwJmeIpydtuszmbKF4Gkd6pcfXmjydZrk0Jc89dLeW9WadhGC3PjH6M7dg/S9\nVhhmK/p5xMlv2e+zUFDghkAET+kWz8ddIv6dELkE1UOz0wtghU9FVSYz1QOs\nsn0VwSYWWQfOUoLjEhbIRbMkankPJHt3qrn+ER0lQ5sVGlYTyU0VnZleerFj\npO0GwMslB6PjikpWvwotV1+PJHVefbvfeBR9RwXdL2yMgX11QfwFwWq7Ozqn\niZgOEVKSH7rC84uZeYOXswFhPl7HLVzmzGyVeCa0L6iW7xNKqc3y8dXJUAbB\npHXTSKOrgW33pi1DS4XHU9fO6LMRvXLkfcnf9hp5RcJ7rgr/SmCA/0qgw/5F\n9GGc\r\n=jwFu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "lodash": "^4.17.19", "globals": "^11.1.0", "@babel/types": "^7.13.0", "@babel/parser": "^7.13.0", "@babel/generator": "^7.13.0", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.12.13"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.13.13": {"name": "@babel/traverse", "version": "7.13.13", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "39aa9c21aab69f74d948a486dd28a2dbdbf5114d", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.13.13.tgz", "fileCount": 36, "integrity": "sha512-CblEcwmXKR6eP43oQGG++0QMTtCjAsa3frUuzHoiIJWpaIIi8dwMyEFUJoXRLxagGqCK+jALRwIO+o3R9p/uUg==", "signatures": [{"sig": "MEUCIQCokY/RaIqAFPTyZtb9KNoJpc0RF3Cr48+++XnkJnl/7QIge5tkj5zyK92f7AHNnqptty9w2TM4BCfrJBGT3GjyB7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlAfCRA9TVsSAnZWagAAw84P/A0po3kpI6gEzZfqN6q/\nkDWJaxR43T50Ap4UgcJBHvWW5ddtUGdoKjV/S31+DSmi1nw0G9BUHlvf/6li\nHIBKVEISaugEOuHkGROVILBj7SY4Wcej9h/qvmDCxzRXqT+x04onWUHE1XYV\n0V6mr69kBa6HwdMCAF0m676Op+AvS60+bwsoFrcKhEVxI7GrNCNT6fz/qS9Y\nPZzrytuo8nXx9MggfOiBYP3NmlHQXB5bWZ+KXD5JOyHcmsu45nVxsN7yG+VY\nl0qNt3+MF5+PoWJA4V3VNQI0f94iAesRKWZKbBR2ts4/iUYa8JgaZ5CNBBFU\nAsUTuJHMul30SqKes0zaWpXHaUeSHo1CxR9OwFjy7vCbWfkiE+0JwzsrVpks\nBErNTETclOJz59iT0EOgM6oTkEJw3i+wieE5OTRNNsMAIEIKZ4gXfX2IMF7Q\nyEJ7YihB12qX1IYVyH6ZIJG/tF8s0W5e6SjW7xrPsyWgty2cCimO6xLP+m9b\nZp9tI7VOPEUs/W4TTQruPckDeXf+Li5ynBCLfEaKc2ubHbycYjEneepcr8DV\nJzZTn1nFM75lP9fHf0eg2o0xghXu4UUazVrN6ne1h3GI89HXB/WcRZ58wucf\nS0wmLvNSTPqrN9OY+reIL05JSn/Wfr/AIU21KsGEo7b+q18ns3T6EyfeIs1T\n3lk1\r\n=lrzh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.13.13", "@babel/parser": "^7.13.13", "@babel/generator": "^7.13.9", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.13.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.13.15": {"name": "@babel/traverse", "version": "7.13.15", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c38bf7679334ddd4028e8e1f7b3aa5019f0dada7", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.13.15.tgz", "fileCount": 36, "integrity": "sha512-/mpZMNvj6bce59Qzl09fHEs8Bt8NnpEDQYleHUPZQ3wXUMvXi+HJPLars68oAbmp839fGoOkv2pSL2z9ajCIaQ==", "signatures": [{"sig": "MEUCIGezQXMqOf2zAE1KbUmaJs3u+5Sahw2uMwOmakWtf1w9AiEA6VwV4TKauXyEzBSlQbCb1nYriLBcwyoLyqKBvbCH8dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbyZFCRA9TVsSAnZWagAAEeUP/jjbxy5zgOmM2MMbKn8E\nlMi5aQ/GU3E3bDw6GzGP64D+k1PnEO/Z9du0NVVKDlcEA1iF1t1i2nnWFzVP\nkJm4g184CF5bYpWf/4tMckf7NX+Mmdm79gjX9/9kMT/Fn7yPnhNaIawa1Zj6\nwAjC38z+UaH+wgF2m+uQHOr9dIb6fW3F4JJ+RTcHgdzFgzyXkS2uCGRV6Xx1\neufXBlc3mXcVwIjdr2jr3Wip+w2/ePjef3EpFS4TRGrwWtj8HXFA10zEUgTS\nCPguaD7h1mFG38Ab4y0m0b56EGDOZF8C3bTVAqA9aWtWxVtxKOlJetMywad0\nYLfQCBraKSjt8FzRFQqE6Nf9N5Xj5i7hJYz309G3vfn1/3Xh2y7tFf389gB2\nAgFHlkcmD37tq3gMuZS6Yg2sYrDoBPZOy3MBhrJbqfXrERo+YyRH5wefI/Mp\ngPUAecfPm1qvjxpdc1R5Oz0KS6bpBMM64MZ6Bz3rYDkOxExbd/ZL6WbQAxEv\nwC1CwqnjlOTKXvUIyLRKtlCDEpE537DvHXQ1sw5HedQ/ND9nstG9XW/gnIjQ\nHkTUNMM31TzELskqxsdUoxvZjHWdrzIn+rpD5iDB7T61rPnPkTHUXUMfRF72\n9A6MZgemOFKvLNqQ0B8jeDCsN/yarLysA48UDakhaUkAPEztDRYoI5ZvMV5+\nm+EH\r\n=g7D0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.13.14", "@babel/parser": "^7.13.15", "@babel/generator": "^7.13.9", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.13.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.13.17": {"name": "@babel/traverse", "version": "7.13.17", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c85415e0c7d50ac053d758baec98b28b2ecfeea3", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.13.17.tgz", "fileCount": 36, "integrity": "sha512-BMnZn0R+X6ayqm3C3To7o1j7Q020gWdqdyP50KEoVqaCO2c/Im7sYZSmVgvefp8TTMQ+9CtwuBp0Z1CZ8V3Pvg==", "signatures": [{"sig": "MEUCIQCrT09ydga81KIUgYEm/SmIGj0nPWJjp9VRRAH9s+ui5AIgaHmlB+e3kX3YH+vp2CXg+UhqbvSpjea//lvMP+PpnTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf2F0CRA9TVsSAnZWagAAHrgP/0cbzSttpQDwbzzmMxtR\nYmR0tFndS/4thqMNm8+VR3POcOHPmTb8Im1A9UcnDqRhuCWgDUxf4r8mevHK\n5NENbeBk562mjRpJUTdZBb9aAEpkfvqr5uWMm81aZMdw1NPmvQdhR+UfmD2g\ntvOadD9IK5wb7Ihpx3tYy8ShTW1yniQyccx5n8fz0att5Ygn94OBguJw2dSH\n/9zfCVIeKHuvkZ5mzGJyAdDUtD14i4/OrHwsDJqsFgFWl744nqJMzn4VC0hn\nrjgBqHw1bkn64KTJ8Z5xI75s2JpV8eEPan0SwqtASq+GZ3rByqi/XPspGJa6\nftg+ypZle/wg5NZlwlNULJkztB2SSoaI3osNCI7HEONDD5HxLkLLuoCiCSV9\nTejI89VLtjfy7eOV4MfK4aPJPSdawzoPRrfufsk9Gc+DpwLGGwWRPciSnbtT\nfpazsoVHcoIsdHZAscsV/s7Wegnv7W8OxQH5z4nwP4TEgDJEho/J6zeujfxx\nM1ZCUsy+QXj/gHCXLRlJnPocnOCXio7etbF15IqgtS87LSVROmcHcrFbi9UW\nlkut/CWHBeT16M3GxzbrbqnQNfJzBbm3Lj/oo1Q0EYUxY5BF4LFDrU5qdGfi\nhTZdYmQ7EJz+j32pbSbj0xfWK7yj8o+USvPs+jSMXSswzJY+pAncRNAWbFNx\nNVyo\r\n=vdiA\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.13.17", "@babel/parser": "^7.13.16", "@babel/generator": "^7.13.16", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.13.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.0": {"name": "@babel/traverse", "version": "7.14.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "cea0dc8ae7e2b1dec65f512f39f3483e8cc95aef", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.0.tgz", "fileCount": 36, "integrity": "sha512-dZ/a371EE5XNhTHomvtuLTUyx6UEoJmYX+DT5zBCQN3McHemsuIaKKYqsc/fs26BEkHs/lBZy0J571LP5z9kQA==", "signatures": [{"sig": "MEYCIQDuP+Lf8dehfiD8EOcdAWIaxwrVAe/VuIbrvLnp71kMGwIhAJ2TsogJ2phEo3XKwBLswq95H1w64ZRtjqkmpkOAQebI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKkCRA9TVsSAnZWagAAQrEP/RCnTxR86Swb8nkGgrF5\nyC3+gQjhjSboprGYZwqCDGl70xd+yt2Jt1A7pMg8wWiKDGHOFqmuzVGa/0gB\nJMEd+kHs0zHIzLLLwLW/t1/Tgjz0FYviGCvMYMMEW08VbfGpQnQ2TNkNm6NF\njqUD9yK1kIySawHxu2rjaINo7A99nQBenMymvAfYcQAz46/5x3euCtozo/7Z\nOU53M7jgfiX1UHBxbjSJZJKCQjsmgUvRf1IDxOaxjEdmqZr/emhCkwARztp6\nQApgeIrovXqrNLQ4xdGE7/snLEcWynLXoATZWc8XKxUrMnoICsgX2DkIisIF\nTn7cSnK6Vp9x14nST2yV52xB2FXrQXKlKspJm81Bc+kBbrjFmORw8iB8IIma\n5xjEfK8LrTu29FqOE3/ersBgdlA8k66coq5EX0dRygZ0CXfupOagNUkG8IQo\ngAiDr6pjWoSJYEKHhA6O/P18Jj0eqJwPi+W62V0N8TYPie/t1Q1YjQu0i0vp\no/JObGnDD+FiBDeM7K5biER3wLxbtbo5necQ8EmD6QGMKgXqccrUIUq+7wX5\nUdair/5vWPv9/QNP4Wicissi4NQXIK30AXh0HKQwK3+GZNR1yPhAgluhIqNO\nIsc0KJ7mI72L1t29K4yz4+bma/IIcvdVtId+jPjFX3T4IO26JFYhLmbFYkRl\nkFM5\r\n=o7cY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.0", "@babel/parser": "^7.14.0", "@babel/generator": "^7.14.0", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.13.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.2": {"name": "@babel/traverse", "version": "7.14.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9201a8d912723a831c2679c7ebbf2fe1416d765b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.2.tgz", "fileCount": 36, "integrity": "sha512-TsdRgvBFHMyHOOzcP9S6QU0QQtjxlRpEYOy3mcCO5RgmC305ki42aSAmfZEMSSYBla2oZ9BMqYlncBaKmD/7iA==", "signatures": [{"sig": "MEUCIE648Y076MLn7f8SqOh4DOII6vIUHRW4CZk7qeEc55v7AiEApJ9lSl9noC+tpygD2dySrwGZ9b3Cj9TmlYT5wVZPwMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvQCRA9TVsSAnZWagAAltEP/j9Iu0oDlWSr3HEpvrD2\nuDMQv83+QQfhjnNUqjpkTOnYHeeUbwLHsWOV3rMJaXFnyzY3yXQwVowqeh/3\nkPOh+juaYI2ESmQOqdvny8NcAktyiZnC93C4LOBG3PQX9jkYrARrs5BxDck1\nAuTFrCu7uq5sD4wdcpQFMIkXc/DCjkq8x2hbT4jfAZY4aDa1BeB+kZ/DBf5C\n9uWic9KYxfgvcfsr94ajdf0+LqWiura41T3P5hFVEEVzg0IIlZl8WIzsPMXZ\nT/zp2+2aOMvlB/SRU5uioQHkUIwB0GmvD35hB+stFpPkUFRriE/BPfiVd/3H\nrsoocAAdZf0Gf7K4HKcQcLLWuZ/LsURDNXgdu3Sxq9kqcH8g7yfTL58kDOqP\nBf4iseXr+80qPNsbQp8TYskanMd956O1Itt9At2Hg1JudUUnHb/Qhv8JjdnY\nzmZoxK4uAlh7C5qlbbzPE/iI69nraF+718LulIyzBbOiLRX9LXWHXgw54T8R\naL2Rk8NxVFwVGNbJ7DpF9rB2yT11f3I0+R9MShlUvJDyzi9X9kwnkNGAlo+y\no7OazftZkn9fE2iWB6K2d7L74xpR7eLGZnc8Vd/s9WhAS6Jjf4rqMBGIG0dn\nhh20yngs79YEVKwptHcBZlsK5ycjrk+XwbJDajlS7Sfa61mmUWucssvn5frb\nhRXV\r\n=/RgR\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.2", "@babel/parser": "^7.14.2", "@babel/generator": "^7.14.2", "@babel/code-frame": "^7.12.13", "@babel/helper-function-name": "^7.14.2", "@babel/helper-split-export-declaration": "^7.12.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.13.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.5": {"name": "@babel/traverse", "version": "7.14.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c111b0f58afab4fea3d3385a406f692748c59870", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.5.tgz", "fileCount": 36, "integrity": "sha512-G3BiS15vevepdmFqmUc9X+64y0viZYygubAMO8SvBmKARuF6CPSZtH4Ng9vi/lrWlZFGe3FWdXNy835akH8Glg==", "signatures": [{"sig": "MEUCIQDfzCQxbACE4tlRlvNoD6tfrYsvs0bEGIdPUdbskbrsRwIgHLPGQdVJ47xdC0jTNZB9lVLdBlr5o2eMXxKwmT9/ris=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr9CRA9TVsSAnZWagAA05IP/1QDgPlz5L8gXGnP7SEJ\n+4aS+iUmN4oNL+Ca2mxY5Zk2IlNEcQI/SMf50WeuEhmgGGPgtxzL/w054vPJ\nMEYU6tU3T+D66CTsIy5cimkjfPMpQJ/9pOSvMjR/Srl+ZJzGIJlvNclLMgsE\nU25iShrbpuaZqv5DfLZ3fAhbgNK0iolmdN69QgM3ar9gt0JSVR/RYHusqqig\nv7++DQiMZ6mv5EvYKvqEhE0xukM8WfcfJbbLlmVCfpN9+xPmRJIDkRO3Teaq\nM/R7uO9EVeLQlfILrDCJlnKzVBCVfpW5TalQTCUehGuS+vO390kdgIewGasS\ncEExsTHpyXGQakpW3Qx/9DEeNCpsMFYD1UrcgdgFcxmq/+tzhJltz3/44AtU\n+Mrkn2tkG5goACjOPdGhHtCRWiZFkckxUzCuU+pnKIaTBzA7flGGmp0unUuT\n+Pk47U07ePaPu43xU1nWsiVbHqGZmhPnTxF0/2Pfn7Un5f0u9TFqiacH5DUF\neuyMw26JWh0RZcJ0mMIt8JCupMmU4zzjZ+0FfpSJA9UNbTV2YLL9HyoyD5hw\nR5EtIPbVxJ3VhTkXAYQIcPO+GxnmqJL/VXgV3pOdALqXdyEDZ+n6WKKQqEk8\nF6hDd5vfX4e977H4O/kRKA2x+43bYVWjUmGXoznsRPKhrQyVMOaqDj8CcA+N\nRuHj\r\n=OsaX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.5", "@babel/parser": "^7.14.5", "@babel/generator": "^7.14.5", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.7": {"name": "@babel/traverse", "version": "7.14.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "64007c9774cfdc3abd23b0780bc18a3ce3631753", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.7.tgz", "fileCount": 36, "integrity": "sha512-9vDr5NzHu27wgwejuKL7kIOm4bwEtaPQ4Z6cpCmjSuaRqpH/7xc4qcGEscwMqlkwgcXl6MvqoAjZkQ24uSdIZQ==", "signatures": [{"sig": "MEYCIQDpSuj843JWu2PsN2VAX485gSMt7Lhqm4KaaG62tzTCeQIhAISyjAwoVFRdXm+N/2L4Rp8mOtwI7mzLNxUwJxFaf0fy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0QqDCRA9TVsSAnZWagAAU5kP/1bfEv9kbTVynn6s4W/7\ntpeVsbqkgwbVb0AsN24vAyCRsY5bP6T5bhw0ZpSa3GAEw/rpJ/thVzZHc3ni\npXR1uWyI8N668wOsvtYgFGn9rgvJhHnyYXBe6v/05RcxqLBZHj0p1eYG6o5h\nnsPsUF/rUYv6+CN5r3Y22qMCCjNjtVnaplN6Nh0E+isDtIRR+fOE4CTh3xz4\n/2XD4/tB/0PXmubzJaIOd5uZwJKApGSl549lpBjJN+9c9M18/UGlbAdITRow\npntF21BrxauY+IZkQWfN32YzcdFNhZjMzyd/qDbNfKirVLu5JcUvE4J8xLof\nSUVw2SVJ1fxdj7l3eWvl5ceVQXRZ/+bY3Etsd5aYdFBfAQ8VsfyiFCnVMdj7\nTVkdcmFSLsYtf8Nt0OS1gj8sJ/decy6DnQVWtMW/mayyTkJrxeBIEvXOy+IT\nZJKSqds9HA8gn11yvsLFHU+s8qHRskd2xTAjnhWMmsDtARY0VldQMBbf1h3j\ntO2FnIMfsbToJ4Ei2/YnvPLSdczXJO3bveGYD2U2cnugpq8eIdGKIiG76f9P\nRlZvMqEv6bfm/bzSsQk2mQqu4r/Sd5jjEngzArIxwqqHeCUs3TQ7JnFTPlOp\nlfe3BDjCPRY9elifPnmi4FjVHdQNJHdV8/bs/rIh/rRopS9Bvj9qzyQwikgm\nIZm1\r\n=In1y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.5", "@babel/parser": "^7.14.7", "@babel/generator": "^7.14.5", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.8": {"name": "@babel/traverse", "version": "7.14.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c0253f02677c5de1a8ff9df6b0aacbec7da1a8ce", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.8.tgz", "fileCount": 36, "integrity": "sha512-kexHhzCljJcFNn1KYAQ6A5wxMRzq9ebYpEDV4+WdNyr3i7O44tanbDOR/xjiG2F3sllan+LgwK+7OMk0EmydHg==", "signatures": [{"sig": "MEUCIDHZIPBD0uVhWgsa92pBOyYMJOzdQOvXThvw/LfVS13xAiEAwOz2pEt7pVoTXh6HNJvLlEJrezp15/T8RzQjBSLwrfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w/ICRA9TVsSAnZWagAAyuUP/iLyNVv9RguL11zKkaN+\nEsruX8weoFT7Xo3I6+jI3HxVvEX6eToSJxGevpd0GdUosLa8J9+qQ1TPoxbq\nqqUGg4dmmjA2MbOfOgF6vP6H7iO+fbA/Poc9BzMiH4dBmom1qEhEnjmb3Z5i\ny9Efnp8TGw+Ep2MzqlRwOjIzv9JGbi4nYQoYzi6PZUclktPEIVWbVv1Kdcij\n9xcQVczHuik+SKtZIlB/5pbMfK4g009+OsjKsVuLIB55/yMwUU0fpZFmh5HS\n0iGWhA2MQ1iKIY/9Tr0t0v3Y4ENRRyo84lY++gXFsO+QuyyJ169i10/egOBA\nxKMNv0huV2mqD5QA1J/pDofFL1htmV04FK5ZTEHssVi+dhnA4Cc/ot9FvwXl\nG046PGycF2fWY7So34imbepLz6hmz7luwcCJY1/hm7AptPaqimAN1gt9snlY\nVIFscYUwzAvPUfIKLt3EpP593JYJre1qOtSFLBLuK46RDsZXe6VHqPvSKT88\n0a+KuFPkHhMMXdRhsWJr/oT5CAxfBlfXtli/IbsD0m18eIWYJxFtKs+nzkRv\nw6dcB4Dgplsw4AqTdejuzPk8nFn9giAJgpmq+31wSVgh7RUXwlL6nQ+esrQ1\nEqwzwETTRtCh1K3qTOuYrJoMt2Nf4CYCsBDuswewcZBwQxiUrkYktnROWlmF\nFbbi\r\n=E55u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.8", "@babel/parser": "^7.14.8", "@babel/generator": "^7.14.8", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.14.9": {"name": "@babel/traverse", "version": "7.14.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "016126b331210bf06fff29d52971eef8383e556f", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.14.9.tgz", "fileCount": 36, "integrity": "sha512-bldh6dtB49L8q9bUyB7bC20UKgU+EFDwKJylwl234Kv+ySZeMD31Xeht6URyueQ6LrRRpF2tmkfcZooZR9/e8g==", "signatures": [{"sig": "MEYCIQCHWbAq9DWpBXRB53IPlypnzo7PpOWQYl0LHOwhdhlR/AIhAPb3w+TBBXtvLAx7w2CCcDFvC5MBK0r1qW6omUagS7Q8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlL2CRA9TVsSAnZWagAAamwQAIu1x/WoSX197fEvlaQ1\ngUncVL6srsVhRUAnq6/vFd4qIZ6k8QibKo5rA3vxT6/JmzpSHFzniyKuHo89\n44Z3jyPzzbEpqhVd2IGgHgfBmCjW3HxU6/89CmlCUTlulrM/e/TilJeP1ybz\nDhRqsydt8gxxoNL0bFF6qK+HKfBbX3g/7fVRrZcunLkaOMSgbKiA2ICpIdfl\nC+BEm3qDFJzPvmXLUqaubI/JLlIGR4SS/4eJwVWvqvN58sDoxCTui/BHWqcy\n1ozWt0dwQSx+gPhY8PhMnGNPpO06miGym+xyDsVMkHyzjK96ohs0wR4L+J4s\nQVyW3hQ7fVeMdaNvjAKcV7UmT9/V5mM8kFEiT06ejpmas5vEgpwC/xe3iLNj\nmnIWrmRJqzPAXUHTMJMGXgnVYnthDyK+i8+Yb1yDx5f8HF39vND40OwjIpIS\nAMohXAjGpOWf7Fv8DpbNjcXAgnMS4EHYO56VyhN8rkesvzfBE0INe5sDKK4d\n2MaH1OS/nh3RXiJsbXlV1HmREpR/VYGwTOINODJmO67/7KuSHl03eoDsRzGJ\nTYJMKcOYrYXQrjBFRlNZwbzUYWaCUuvEQH0Hq5UvkELod1K9TcTYvXxFWpGT\n20joK+pMVTivXnkjoCZspbpjh1sKPqJGbm3qzafaf+hVaQtkF9edvCoGg2AL\nYy35\r\n=C6BZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.14.9", "@babel/parser": "^7.14.9", "@babel/generator": "^7.14.9", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.15.0": {"name": "@babel/traverse", "version": "7.15.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4cca838fd1b2a03283c1f38e141f639d60b3fc98", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.15.0.tgz", "fileCount": 36, "integrity": "sha512-392d8BN0C9eVxVWd8H6x9WfipgVH5IaIoLp23334Sc1vbKKWINnvwRpb4us0xtPaCumlwbTtIYNA0Dv/32sVFw==", "signatures": [{"sig": "MEUCIQD47QVp30TdSXVBoPFYO3kpvzbfDxQnqacW2WRaRoD3GgIgIWGMDbObLNmWbFTkaef7FTuWGuASFpP97q1kOzYceNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLiCRA9TVsSAnZWagAA150P/iBAl2VEqfdtjxLSAvTI\nE7CBR6jRCaghNoLU7/JKH89JZ9DoJF61ZA3wULDVg2rBUW4nH1KoFY96hTTC\nJH8B19VAshkfvdgULq5mMZo8s9jjQiLPcgyuvQ8GSjq7VW9roAzlfUSKYwd+\nYHdQ/l1YBKC+9tPCsOnuZ3WObckMg+4ANV47Wj8u2lzQftQosyeQ55GV8Q55\njkFi69heXq/HYuRupc3VWzsn1U4J2qgdBtDuB8W4sX93i3uIceHgH0/AyTNx\n3Wvm766uUkPBmZXu33NlMPiJUgbwJcdHcIomH8+C0FPCN+KMCF7p/rnhymBK\nEydn1s6oDGJkk3d/GXCINrhFjLPw5NG1Ry7+Yt7krIKF9DOha4f12IT52DQC\noQrLoUBq9piYc9RFJeckvfhwUFivy9n96o0xta7oWCdImNMWXO31isrmgp0G\nw//mlwgXg/sS3NKwP8DyNpnwPRuzuJ/QgCzJ+kTOUQNySrvhXDp2V5JsYnO5\njoqYVCrPt5YC7832W6lYMkjfQAPq0izkkCE7wfplM12cO34L+Pw/vx0bkMsG\nznyk10zLY9RDgiI7ri9iXmMpj6CAtcLYijpw5YqP3K5+z0K/bgUVDsJyges4\ngxYCJ4rPcmjetEK+syyK0v+NTlAvnodQ0wCWwAZByD1cjbrFpytP8ux4tccE\nrfxh\r\n=CKMZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.15.0", "@babel/parser": "^7.15.0", "@babel/generator": "^7.15.0", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.15.4": {"name": "@babel/traverse", "version": "7.15.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ff8510367a144bfbff552d9e18e28f3e2889c22d", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.15.4.tgz", "fileCount": 36, "integrity": "sha512-W6lQD8l4rUbQR/vYgSuCAE75ADyyQvOpFVsvPPdkhf6lATXAsQIG9YdtOcu8BB1dZ0LKu+Zo3c1wEcbKeuhdlA==", "signatures": [{"sig": "MEUCIBQdZZNmYZTpWRJvoLOCjZrpSw9XOy2/9TUAZ0OwEaXBAiEA4yAOUv+1dCKLN5W83hn2BYDz8hLkt/I8+h1CAs40uv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSgCRA9TVsSAnZWagAA0HcP/jl+zYPaoMr8r9OJKksy\nWVQm3QSIzWDj+qlVrqVAyritz4CptoTvE7aW0c+7/2SWa1ZryB9oER8suEDU\nKc1o5BXCI6wMojj9ufHdNzTmaL/Z4nSsGurhhb0CZoYUORFv6zQa6MpswgVr\n5vMIY/OL3/Ju+2NZOjcFXeKQizUl/gecoQIEHTBT09pZKBrRfsgy27AQ9Tia\nsVA/nBKC4esWwwz4UBiIq7x28tvDlHo+ZHYQ4WCfRHundeIm8oTkBC5u1T1c\n7r4Fml0P+QV5NNnlcjSknkhhCRuwmzUuFfCNovrdDDOgeH8yqOZjgzfEbA/P\nF7i3zkKkhiU7og9ytA+G5Tho+J8ZKsOtztIN/In0VoMmujn3pj4gy7z16QB8\n+qQltJo2vJFdtYJvbtA0OPcYb+VNvVA9YQszfNA0tYag3s/lzYQV7xi49w3n\nGQLyQQTecEop7pAC3t/G0ApInnIB1wZpuv4dKTduJ0ApidCTadd0OMxgoA0i\n7jbc+Qo62b3lk14anX5dk6kxHWQL6CM8ISA3pyPdL4hDf171qV7oTaPEKbZf\nMG4E8gCgWaOW+efmWCcXC5dsKZ5jELhjeN6ONrsbHLGHQrIY+OJxzsYlmURw\nmognJpfkITn6Y9fpnxkQaAIrjcm1UBBiGKLV22joC7gZeVbH3trf1btVWG0o\nzwjx\r\n=STT9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/generator": "^7.15.4", "@babel/code-frame": "^7.14.5", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.14.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.0": {"name": "@babel/traverse", "version": "7.16.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "965df6c6bfc0a958c1e739284d3c9fa4a6e3c45b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.0.tgz", "fileCount": 36, "integrity": "sha512-qQ84jIs1aRQxaGaxSysII9TuDaguZ5yVrEuC0BN2vcPlalwfLovVmCjbFDPECPXcYM/wLvNFfp8uDOliLxIoUQ==", "signatures": [{"sig": "MEUCIFJSwXns7En6V0CCcDLBAw67xroxCYH+gyk/ZdOSqijtAiEA470nCsTpsmKFO0/OtkVkUfJt3GFSZHTDOISc/JCOPEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150984}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.0", "@babel/parser": "^7.16.0", "@babel/generator": "^7.16.0", "@babel/code-frame": "^7.16.0", "@babel/helper-function-name": "^7.16.0", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.3": {"name": "@babel/traverse", "version": "7.16.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f63e8a938cc1b780f66d9ed3c54f532ca2d14787", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.3.tgz", "fileCount": 36, "integrity": "sha512-eolumr1vVMjqevCpwVO99yN/LoGL0EyHiLO5I043aYQvwOJ9eR5UsZSClHVCzfhBduMAsSzgA/6AyqPjNayJag==", "signatures": [{"sig": "MEYCIQCMx0GNu5Oi5C211QXVWm+edR3H2o6w3dz6Fq1yQ7eyFgIhAOTb8LRvIg8/nKL7aVgurJBLh6hAWDB6AvLhX3QXwy1v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151547}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.0", "@babel/parser": "^7.16.3", "@babel/generator": "^7.16.0", "@babel/code-frame": "^7.16.0", "@babel/helper-function-name": "^7.16.0", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.5": {"name": "@babel/traverse", "version": "7.16.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d7d400a8229c714a59b87624fc67b0f1fbd4b2b3", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.5.tgz", "fileCount": 36, "integrity": "sha512-FOCODAzqUMROikDYLYxl4nmwiLlu85rNqBML/A5hKRVXG2LV8d0iMqgPzdYTcIpjZEBB7D6UDU9vxRZiriASdQ==", "signatures": [{"sig": "MEQCIEPce+b11Mir0CquaaiQ2yY2SQx+6fQpFlUSvqW57TpxAiBtMtXLAWyQi4XhD/pU471loVln+wAov1KZm+I96oHBzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kUCRA9TVsSAnZWagAAmCcP/jFeKPTVhCjcwgCXRGkX\nHfW3hfgNAJIim3nKXCg92PwzMa9JqzYXjS0dseVXT219uFvcPUoB1uzjB6iz\nlTqRrEykccz5NU5wgc7JOR17YUXs1SGJQu1q+NvLjuIMMWFtnPlz1zcwf1Hr\ndFXelmrMNuSrp8FSWByS6iflkHOFMu0R++59PMp+JED4na9X2G7wAFEMkT/T\nj8zmUatjPzsFibh9FPBFiNDzxNVfwedUMhESa1pRpYeymM57HW7ppb9inuPv\n8OZUqgRXO7TPr2/8u+YzG1bCY8Ly9455iMjX4jPylZ9YHhEFk4yOR0NvUrk7\n8hKrxOwShObvCkYe6IYiYhoZSTLBapmMLAV/ZOnD2bdF2RwD0PaDne3LpMz5\newlDSU3oiPZHIL350w9JnLAUti8qFqWK8x3BjkkbzvE5qK3boazxr3A1X/Jl\nC2oDCFO0PKwdNdETEugQLcZZZC33vxZmtEn0eh85Mr6fEH+qQNu/Qy19W7sf\nY8MV17fHlG2yVFhl6XQMvfffnb6cQ8FLqTUTQm+15CbYYilYC30/zB4C93jN\niyKMYMjlD/4kaF7fOpvNx6dJLgEbqdLUtHoaRD0deq5e/bisn4wHZSAl1KwR\n7GxQ9aOTElJwX0kRr4J5BzEhG+2D0fkMiWYW6QuEpzGTFkfevxQa0/vrgslZ\nwbDL\r\n=L9g3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.0", "@babel/parser": "^7.16.5", "@babel/generator": "^7.16.5", "@babel/code-frame": "^7.16.0", "@babel/helper-function-name": "^7.16.0", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-split-export-declaration": "^7.16.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.7": {"name": "@babel/traverse", "version": "7.16.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "dac01236a72c2560073658dd1a285fe4e0865d76", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.7.tgz", "fileCount": 36, "integrity": "sha512-8KWJPIb8c2VvY8AJrydh6+fVRo2ODx1wYBU2398xJVq0JomuLBZmVQzLPBblJgHIGYG4znCpUZUZ0Pt2vdmVYQ==", "signatures": [{"sig": "MEUCIDOivHjTNqjSCo6cEMahj/I72T0k57s0YHhIpm0zc7hGAiEA+BKTQovZU3Ib8ShgrS3afh9IvsHdcQ9CXZN4g1iwVTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1cCRA9TVsSAnZWagAActAP/3GVRa9DkNFH7998iomC\nijls8xcrfnav5iEiRPARjwvWSMFLyeGSHQyYXVRuDAqbjNQ9dNXL7VuspabI\njB9PlIdHuWM42VMnVLC3vvh9snMwj/2V9Bb1MEsbM7N6KAvY+L+i6/WCQa2l\nvcwRSB3nC6VVdSQk47DC8FsrYWdch2jrYm/QdvA7fkdj17UALPsjkEwRlm+q\nkDX07p6UNLiNuJpFCirFJ3bZzxp2WFbTEL/ueNVa+hb8sxKv7vCtyqGpUI21\nNd9h7ntom8BiFqbzaUJImVcY0aCI07bbXRxKxDqO03ZStc/499mokhzaGfcW\nzz9vVLRV+zcJ4Aib2bfXH3WZ9Ac1Zk99AZ9QvTj3PBqR4oBlqev9pIKMnO8G\n0O2erlAZR6xgBIEWfgUk2s78Tv6P432UoPsdbGP1FmSnQ3QNsEcy9nGIiZRF\nq0o8Iqu4KAZp6uThra19vSKl9RJ7AwmY6ixAdr/9bWnre54qvNPXZyRfWebp\nmOyQozfj4UrqKhDkzukfred+1OFzUFQmqW4jdPgCUlkLzleTJf8eq81kdHzG\nSidM7PBwOdArtg2LE/mqUCsZMyZdIGzygoEsMjTv8HssE3meA6exfTU/g9Da\nzbPim9cHovKFzKk1/nCtiy/ZNkDr9sngDqu2OK1PfPnVkc7ZZHUG1115ySAg\ns8oI\r\n=iV55\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.7", "@babel/parser": "^7.16.7", "@babel/generator": "^7.16.7", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.8": {"name": "@babel/traverse", "version": "7.16.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "bab2f2b09a5fe8a8d9cad22cbfe3ba1d126fef9c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.8.tgz", "fileCount": 37, "integrity": "sha512-xe+H7JlvKsDQwXRsBhSnq1/+9c+LlQcCK3Tn/l5sbx02HYns/cn7ibp9+RV1sIUqu7hKg91NWsgHurO9dowITQ==", "signatures": [{"sig": "MEUCIQClqOU+zm1RqzhABpeBukM68HCdmpJM9X+TJ6QssOZGhAIgerpI+6tuyK9ofZhEyU4ED0SHQoVvC0pTBErP/HXYPx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKsCRA9TVsSAnZWagAAgRMP/RDaGzeowRS5K3BuwX5P\nhomDkd/cp6IE7F2KD+v/vhLsA5Seadr9TSw+xG1/B+w0tvFRpytBFFdZNbxf\nsBbjq5ae+wYV9WJ/GCEziyOYOhfrhlAblbc4eHsc9QlOlk3yK0u6uWkQbJqX\nTNgMjyPVoacLXMqZewa0sKlr7G9UgZmyovpcYrCVs1CJ3Zwb9d1xU3VJjdoh\ncXixGNk+Tf1Eqm2KQc49G00uMHaNOAR7TvqsN8HY46AQrPGPTFS0WsRgxayI\nsvcQzhsEK4PL92WPY/1kSfw6Et7YWO9NqY23TYbdnUcM3ge+6yx1o6yJnCwD\nxGkrDlE9w1IJUEd9+KRXhYZxWP9wi4lQrYiQsnnBH4Dfv8F8z3gCjHzd0IrM\nZOMx7zwN1iV4KmXI1HNpVNsRLo8psDeU3ZZEDEcg0fkLeAw+TeSlTcJiFptr\npdueikcVhuM1e6sUp6yNXNcHip0wqJPEVR3t5nG8bP3e/RSK5+ZBzEfHiXu8\nipMGpQSQcA+yOo0iiWNB2bw2Nuxuv3z8/WZi8IKOJUzu+uFGkuw2h/8tTXoQ\nXe60ufILeBVoGn0T/SdtWrZt18d+o2u4F/uf2sPOHkDsLh5lqlz36oSw3bXR\n9s2b3rjz0MSD0Z6INBgoj0gIgG/LIlJ6aKhZRe3VpW6yEFglRACW/ic0xkl/\ngkg6\r\n=ivBZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.8", "@babel/parser": "^7.16.8", "@babel/generator": "^7.16.8", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.16.10": {"name": "@babel/traverse", "version": "7.16.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "448f940defbe95b5a8029975b051f75993e8239f", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.16.10.tgz", "fileCount": 37, "integrity": "sha512-yzuaYXoRJBGMlBhsMJoUW7G1UmSb/eXr/JHYM/MsOJgavJibLwASijW7oXBdw3NQ6T0bW7Ty5P/VarOs9cHmqw==", "signatures": [{"sig": "MEQCIFwRtr36SF5Bs7kDYHN0srWj0YWZIk5jEgbkvkuyXWPFAiB+1inRl1wYicdrVyG+NTYN3IrQTrNa9ApHOJ16dH3xGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6FrNCRA9TVsSAnZWagAAWr4QAJ22aK6eMlHcqrtjgo60\nBjsRQQioE3yINMnLRXnTlNbDW9numw/tLWIgg9EnOuxzabgQPp0htQD/9cab\n+jymmDZacFOqYPvnSiHG7SyV2VehtlQrBQc1mcUsU84jNVckdBBTZS27MiYA\nBR+Su5LkBR2kf1dDV+Re5kogWVPrIxL9k7F7HpXSL/yk6OXads0CuA0MPDFz\nFI66WfjR8m+Kojg5qoEiDWeHt6UZl1y2BzZSAIe1ukI/0d7gK/ehpYQ8ZDF3\ntEZwya03Vru2rje6krudPzVQ4NazLjeRcYWCE+Bi7HsXgO1TWIQHjbtL66s6\nXlZBu7lGe+5es+fPq2Sob72HatCUYblmzhdoj6CyAJOqknN+Mqi9B6wGgV7d\n66tUWHE66qBj0GDllhmuI6i2pvFeMcDHw5YpA7TvNfN6ZiVYhyyhr3XELE5c\nghhzO8BIXXXJZ8rEV1W3OvSsVVetvoAgVbIlDAOtPsFmG74IdRQTMJktKIcD\n5+FaJ4GRBsdigt9oZQK5YLUi327GwA28xMyEgtTisSTZrwXf35URy+BfU2Ph\nH5Ou/vwApUTMDE2f6M2eMnYtj6Jot+qoceF3E5ess0IeWO1vFHJs9sMK84AU\nqQJ7y9r732LiusQ1y9b/ZxUjaeWuFxx5io4qu+jLuacdm05WqyX4/ODFt0Tx\nfAmb\r\n=wF2B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.16.8", "@babel/parser": "^7.16.10", "@babel/generator": "^7.16.8", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.17.0": {"name": "@babel/traverse", "version": "7.17.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "3143e5066796408ccc880a33ecd3184f3e75cd30", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.17.0.tgz", "fileCount": 37, "integrity": "sha512-fpFIXvqD6kC7c7PUNnZ0Z8cQXlarCLtCUpt2S1Dx7PjoRtCFffvOkHHSom+m5HIxMZn5bIBVb71lhabcmjEsqg==", "signatures": [{"sig": "MEQCIHr282UtiBGAwUE1jWatvyu7MOkTghN0//i8Bunc1xioAiAOoUtj1/0/bARH9FILwIeBSyrfwVzw6AD/G4puaTMsxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4cCRA9TVsSAnZWagAACo4P+waBoo1GGSX/xHBYEBoz\nffbHvq8mSSa4xevEABHS808xcwd7UvVyAQCw6QwNcsktM9okYQXqHtYbjwAO\n6iTk22fvXEgUfRyT3WJbn/s//i0K3bDG7hJwQSnVi+WszPYYfNLuMVIWFFcy\nAN9GIzTPC+f5IrcgYEP8TlQB3sXd6wgbK1mLGS1PW/XlcpbOx8owtO9SVuX5\nHxgo7pVJCfdKMmqLyn+1pq5prU0t8+Uk7oKcOYun7tuACg3NNdajiys4Mrw9\n22JZnI+B21GY8y7CKMba98ufN6S5wdJ8/ZyQOfvxzzH8KGp8aheeF+SjDrbr\nwgqYZSVNv/NNM3Jbwpf6KnkMcGgmx9VtiJ5irvGFqMm+cjRF1SLpS7uaAuZt\nOSiQSH9zvV7ANLi1GhD1t640Gb2aoQL8c/kCUd7iXo/zgZgYu9oOTImCaVaY\nmvIhfuYXFUBK8L75g0zKISeGvdvsc2zgwE97fbn3PVReabOEtXeNnh2ALi5i\novZ+S0IzsOJkbvls8WlOxv0B6QwAeWYHOajga2a7gqo/dQwDSBKw1FxXJWco\naR6XDZgPh4brzxw8nNFhnNzFo4gaPqpHG2YUysnBHDKDFDnI0gEl6MIhplhR\nNxpeGEkDc7sTXbsB1Vs3v0ARqSyX850vu2v/fJsptt85GMv56RNllaW8Q7Wh\nR7Dv\r\n=juQa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.17.0", "@babel/parser": "^7.17.0", "@babel/generator": "^7.17.0", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.17.3": {"name": "@babel/traverse", "version": "7.17.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0ae0f15b27d9a92ba1f2263358ea7c4e7db47b57", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.17.3.tgz", "fileCount": 37, "integrity": "sha512-5irClVky7TxRWIRtxlh2WPUUOLhcPN06AGgaQSB8AEwuyEBgJVuJ5imdHm5zxk8w0QS5T+tDfnDxAlhWjpb7cw==", "signatures": [{"sig": "MEUCIQCDUVfjuHWcnFOPO4gFj7/+/GVTYVjcGJ1vhwF7IFPYuAIgf7Kgas6TvM73CDcJB06V0rf0z6Qa9g42cAMDFNkSAfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC8pbCRA9TVsSAnZWagAAcIQP/RjZu+4RXePl0daiPc//\nNuI0uSgv1s0D3JIVQALQD2+ekpdcLcQ90tqGRibZ1xDsgqKgXrZdr/Rd398V\nlcU9PAUrw41QglejUcRUMF+Hji+6x9vyN3tirSKVpPezHgSD/adyakP05Ul5\nspw2T0mln0ary1Q4VA997z0x2YVI9wbFK/EM+HuPdIkaMCPvHM+92wHP83tu\nVzsd8z6JikzA9nTJVBV5N/78XIY6lDrd5eEz2QpqfmnUyEn/s9wpsaJevqud\nZldlqf0BgUK1nLDzx9re+rO+Vm6ko7domdpBixpiexmZK7j6yKS3eTDzhgCX\na3ySF9dlbNR1zxoV7CP6P0A1bJw4BREawafwfKpFaVdg/b/X0OoRULIujaH6\n4HN27vr+DCRImcNK1VasMfUrYaNpuz8O2Is6/hlH83dwE5ZZsXtrI3RTbW4x\nkoHhwYJH1j/w8Hjnebm93t6lNzeq7c2nCDBLkqF9rMNvuRfyhSpzK/oAoGdu\n02m8OMfG6t0h6g1Iin/eiv+pS73kllljgx6vahO6uJJCNhizUuMq1f10v/ir\norpbdlOGMz1Os5FaIVdSFjkZhiZzDq40xJiBeQSklwKHN6DjfzuMS1bnP/EC\nLH+6uxVO9TE1gSZWApIStefbkb/hD9Xl7n2/2ciEO9OsXE7NYibWyNsVytPY\nlZjq\r\n=jNGv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.17.0", "@babel/parser": "^7.17.3", "@babel/generator": "^7.17.3", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.17.9": {"name": "@babel/traverse", "version": "7.17.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1f9b207435d9ae4a8ed6998b2b82300d83c37a0d", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.17.9.tgz", "fileCount": 37, "integrity": "sha512-PQO<PERSON>sDIJ8SIwipTPiR71kJQCKQYB5NGImbOviK8K+kg5xkNSYXLBupuX9QhatFowrsvo9Hj8WgArg3W7ijNAQw==", "signatures": [{"sig": "MEQCIFpsnZbZrlmsJSwAiZ93m3bayG5SxmVSRXwtmHNM2fxLAiAE5uacL1fWOEbbUrl15cpPrqBByCGpQdHiR86fnxYcxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbfsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqviRAAgtj6LnmK2hD6QRL0reDcBKzwxq6IdeAYkO/O+kR8ndpH6MVo\r\n2gCWBf0/h/mKYuYeAP/Fhvn43HN6QzkVmnoDL0WipFa+BiST5Cn0CIvgXHnE\r\n+eImI1FXjUxcOd9Avrlp5EVJwlgsW0aLk5HOKUDslDPTfGzJtFXFpwpyka/K\r\nOnSz5dQDIyPHPO2cSr5jLEvMBSawhKMNM5NlpqQAkzA4g1S4RLlIxNiRfyvS\r\nU1U1uv/MWAmLwKpwiCF8kVmPldIJU7O7p7JXA3ZjqhZoaoRZ9cfJ+J1AShqD\r\n2GiQRTWcrdu8K7TYUmcvXB3JHCNrFz1TUyEOZ1kTISlLFjcdvmAyjvKQezQD\r\nsk3Cxax6dmvwI6E2OM1ySuafy36/RESEdXBu9VHofK/vPwvWv7kH+V4M9+/R\r\nC+IivSWQj6/tomVb9ST5Re2yv8NvKvqTrwFjQHM5WEqFv7x8ZQlgQJ2e9FU4\r\neIBnETVAu2VlcLIYPS7gAdCvnm/2QoGa9hDQqwITjMsQvYpm1whmI/bMPelx\r\nzu8LOGjjIeswBU1Pep5y9T0eQtXIroI8gNxDjUl4N1OCHAJ2c4xjfGiGHvyQ\r\nz5Vb8pxeVCngyTX3Bvj2UKi0N2rmN1IbF9U8h2awk/9jEPdDcu2X6tBBoDNr\r\nl9ZsSnmUi2IR0WV3cKjhfCmXWq7S6Tb3cI8=\r\n=eHyF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.17.0", "@babel/parser": "^7.17.9", "@babel/generator": "^7.17.9", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.17.10": {"name": "@babel/traverse", "version": "7.17.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1ee1a5ac39f4eac844e6cf855b35520e5eb6f8b5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.17.10.tgz", "fileCount": 37, "integrity": "sha512-VmbrTHQteIdUUQNTb+zE12SHS/xQVIShmBPhlNP12hD5poF2pbITW1Z4172d03HegaQWhLffdkRJYtAzp0AGcw==", "signatures": [{"sig": "MEYCIQCRRTS7gkvm+iwPjR8J59ZagCyWkl3AmxqLqWC9nL553gIhAIjotcDzHxK4OtVMiKkmJJMZ/izsxBP0VNK7eCVSUZTL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzTxAAn1TipzLi7X5au26QK1/QMQAJ+P9Qz3w429NZOCx2wSPkhAyH\r\n8lA/ohKlBuYN0DT5VHc2Ig8+Gd3pMcXyXlhKocDBm3nMe8cjf6FbX9GA+3hO\r\np+e5cJYAqYt65i673nus1dFyODaqXfRoKnHlC5/h5gVll080PMyPfG4Vtp7G\r\nvKoNgc9LfrvLwCNLDZipcdjBcsw4EdbM10Gn08TVUxWu++pQhoXT8LlHcSTl\r\nnq9lh0m82DvSZyl1Ns7kXyTAI32yXdBNgffwjuxooScQhoKLiuNQWsnTq9Es\r\n+VjH+pOH9Q/PRwZC98Blwt2fNpp2tgpkvvxT53y0L052BVpHnI7zGuDBoziF\r\n1EoNVznvbpub4xaiiZwSH2GvMGwy7lmcv/G9GD3dSygJkWwg+z9bccgvef+j\r\netKM9jvlbD9XAnQomHjH/e3p2sBdwt2xZewC+9OaPox5L1ugfyd/3Jz6N+ON\r\nWQTS1dTx2qDfdZ0ybp5/lX6wWDX52lRUPSLbbZWrTC3gjGns0XONVnlogBOA\r\n9uiGYtxetVQoGx0vEEr2q9IeM/HY8TiQkPEQ/4aO2iyH9+E7t7GYJxIlfVE+\r\nlWlC9YqAA0N7hMTfcGupGNM170LvV6aDQZQ31De2S608Daf1PrDEzOWxJDd3\r\noJ5QpSuZIM31rFAxhTOxK0POVxbc85fZzgo=\r\n=DeV4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.17.10", "@babel/parser": "^7.17.10", "@babel/generator": "^7.17.10", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.17.12": {"name": "@babel/traverse", "version": "7.17.12", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "011874d2abbca0ccf1adbe38f6f7a4ff1747599c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.17.12.tgz", "fileCount": 37, "integrity": "sha512-zULPs+TbCvOkIFd4FrG53xrpxvCBwLIgo6tO0tJorY7YV2IWFxUfS/lXDJbGgfyYt9ery/Gxj2niwttNnB0gIw==", "signatures": [{"sig": "MEQCIEJ0i3Euymjwq0LMp91JBXL7U1FbvytDJ3wM8nALrnJ7AiAnKtRTR6mXoGgr+n/AhzCvhMDxdY63noYEJNAgbXcZfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkWg/9FXayftixdZA4IO6Yqro4w8Xo+YJWBaMhN3tPcciie4+ntP4F\r\nY29gZPv1rq6RE+FYNbUw+f0qGuN61ZQIcX2VbMdlt+YHForvaIO0eTnVAadr\r\nPgo8yY48p5Cqs9wO30cb91MItNQaZrSOeqrW4DJ3OGpsJ1N1viOiE/9KI9Lr\r\npdyYMfNQP/IaumaN0s2R+OVI/EB89w2xkbqj7cFP/RWMMGexwHyJpkSrrub7\r\n+bi6+QRRTk/3fsBbacmZ0TCF5aKomULuMJeNP0WecGgJenJOahHFKAPv7Koa\r\nupENEP/he9SkmLo1CUf2+lu6Ff5eRNiXv1obrXHngLf6oPFp+1gN+vohN8PQ\r\nKmRC8zAfLM6sjA4+blBNC8E27brHwLU+6ooecZ379BLR1ZYD3o8UzxndV4P/\r\nJ66MW2eggOH2Pdfc6CI+Qa/lI//OztPdSyeXMFblp37Ixdb/luaJa5+aMs7M\r\nFOOJ07NSiVWxbOIfk8tccsTr37ClddJkR9wFX8j69PM4CzW0B/cKI1uGk6IA\r\nWoJG1fitq+0rrFBpxiH80vYeomotRdN08LQK/Z+Q/Bs+h1/cwvqRCnDfEx5l\r\nJJPFXtC4F8UrwPM9ZbUR05GdaJ0djdAjxR2d3+ha2JcIyH0iEz7xtf/ToXLo\r\nLWg1j9cDUJDZeCmEV/OWTPSgvxbv8KYVhvw=\r\n=q3uX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.17.12", "@babel/parser": "^7.17.12", "@babel/generator": "^7.17.12", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.0": {"name": "@babel/traverse", "version": "7.18.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0e5ec6db098660b2372dd63d096bf484e32d27ba", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.0.tgz", "fileCount": 37, "integrity": "sha512-oNOO4vaoIQoGjDQ84LgtF/IAlxlyqL4TUuoQ7xLkQETFaHkY1F7yazhB4Kt3VcZGL0ZF/jhrEpnXqUb0M7V3sw==", "signatures": [{"sig": "MEYCIQCAFSmtjXXTFNbQcdrKiOMclS0cBLjK9AG/Z1cq28rcLQIhAJEJqA7qATrZNhUzC6p2TU/AfWVK9tAu9e9GlP3XHnWF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpW+g//c6DlOIW0GY+k9OBenpXomlFoL+nwekpQwnVthdmy32KJTR05\r\nQcyh5nZXq+fS62DwcAu0tb1U655ys7SPJykxjFW2Hp/vIecS9gXqEgf9PqK5\r\n1D5y7vo3hmQteqgCKuDLl0JBb4+SJT7BdCEdP/WJ+Y9TD6cwhF5dfc+sAS0A\r\ny7Eo1Dg9yLMeVsHtTiTuwPZxjZeay1xsCpWvckH3rh5EG08lpiRjxvjoNRck\r\n18TqIg2mSFd6R2wAx3VYVl3FKRgP+GCM+p8Mz8tieonIxuZVSR1k2OUHRDPm\r\nY0zaw9X940CJ9+RliDFvPeoShHqsHN5T0OSxvlGi+mL23gqp9mV13HOYJaci\r\n9Ixy6TzGjxMT7s3kEqQrBOJAOlw/AqU2DCHJz345cWACuTmFSQ9VHhDzGEjI\r\nPwCSss+Cki6mRWIamhAo2SgBF2eFErShbpWBEFb4lFvvXOD+IkjJzACgP9Uf\r\n7lgcsWNZmZ6PxIS3LlGCOL37wboXqJaEi1NNBdWPUbA+AsrIUwYNmmP3ZXHB\r\nQNrWfypcms/a+aq1TDRxS5j8rGLbiTmrBwyYErHUuva8zRdiTknWMpUXj2Q7\r\n2Jy2TVLalLqkevnyvyDE+efuX99gw1HUcUXYz5llgb9AG9ivaQQzjq7uRWfX\r\nDuXvX2LIHwY2Yhqc625pjoQu7HJ+T7vu+f4=\r\n=5STf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.0", "@babel/parser": "^7.18.0", "@babel/generator": "^7.18.0", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.2": {"name": "@babel/traverse", "version": "7.18.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b77a52604b5cc836a9e1e08dca01cba67a12d2e8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.2.tgz", "fileCount": 37, "integrity": "sha512-9eNwoeovJ6KH9zcCNnENY7DMFwTU9JdGCFtqNLfUAqtUHRCOsTOqWoffosP8vKmNYeSBUv3yVJXjfd8ucwOjUA==", "signatures": [{"sig": "MEYCIQCgotdquFK07pJuqGAHc8KuiijB7Gv3R8CXnLvG6T0VMAIhAIRuxNHQAshclm+YzuyQZnaevhvOB/ScPn5VfK3LTc0C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfPyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIARAAga6nlMeswuKxfW8LEBy3bndzrH/YwBCiflQY+n1XHeCe985H\r\nY4j0Rb+FdI/4QlmVqOsKnGInjpcO1QLlwqubcIRL88bJqqltn9ApEm1YFhHd\r\nFApAci5jpcGDFRF3S8wmRWkYCCpq5/7s4754FCU+ErD3aaNbMUWMRLeMrvw6\r\nJ/vg/7J84WTExBa6je2atXmHT4Ev6lIus43gXG7WaeMKOgZuYdrWNZOf9lNy\r\nTL5sSVDvO8vxCV2HCWPNWhFb9Z5/zuenmloYjs8pkfkIawbji0fW2ryCI2LZ\r\nVr1M6zAq9xLlXxYhlU13JzjmP1hFPwXZ4ALPbCtJsYGxzFQmaWOgNlslaP5+\r\nBQs4scOWbPsOxm3YnuAm7p1XpfSIsLuBpf4PkZqiLPpHQT0rgb9fhK6jHNF/\r\nrdHz2XNDzG9uMAdkyuyIA94ekmncsFxOFPq9rpLD+1Vx7DsT5iKSqqGUqZlS\r\nokn8jkY3Zsx/MhGahURkygLbNc+NgFWX8LuI6K7AqqoP8Hg3uxNqBisUrfGY\r\n6Rol62C13+OGmfZvQrTQ6BYGAYE1HrxbfWbKMYNEI0v0ZhEsThiErG6ywxML\r\nkxoTeeISM8PK8ZSupP8RqRWP8VStNk+axoLZk4pedzto74aILTT0foefTRrF\r\n4Ti16pp7xXvOl07cGQ0C8s11DrfDjh50n1Q=\r\n=cJGJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.2", "@babel/parser": "^7.18.0", "@babel/generator": "^7.18.2", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.18.2", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.5": {"name": "@babel/traverse", "version": "7.18.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "94a8195ad9642801837988ab77f36e992d9a20cd", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.5.tgz", "fileCount": 37, "integrity": "sha512-aKXj1KT66sBj0vVzk6rEeAO6Z9aiiQ68wfDgge3nHhA/my6xMM/7HGQUNumKZaoa2qUPQ5whJG9aAifsxUKfLA==", "signatures": [{"sig": "MEYCIQDV1GtUn530AMpfVBcHQPmHW2ypuK3U++wH2wbO9uAiDgIhANZI59p+YsCoWkqUhpVtIabLuYJTE3o1+Nlf2Obu4H1c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiptvYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrv1g//TX5nHU9eb3NRqgoeSXZu1VKSTA694+FLQNxTaLjx/euezhzD\r\nFbEzerrjV7SkoEWQRyU8tWIoUp15LyXP0jlf47btniEMr7ERBhu+nAzIPGTf\r\nUSbBdoWHuD1SLV8CjMdMNKAKdaETuhtf9wH/ekZPHaqCQhk4DJ3dafQavh0C\r\n2FXo6/+VSW9j+cRVY+2vjTbJSr9/nEEZ0OsLQoIkayh9uTFC3jswS5dV8OHq\r\nHiS0r5RHYvHn+XZHoWyGE0AIPzCF5VohEj0ZQ9DL7H4P7DaolrwzPBLogPEC\r\nusyJFj3DmbM44rKF4+J2Tsm2drKvOqZ3lDhLqDHr0+I6aCCD+WrjJ7CnL3IT\r\nBgtcEi5UCLsXs0GFrOdgzP7fv1V3AyhEhU+hTwLsa/60MWn28KnG8QCZeDLs\r\nrHZgJJV1wCcuHXAk/GibDTZeOaJ2kQWXY1QHhlDbeOm9P7E5e1GU4QXyjleS\r\nBD6FicwlzilWTEsMG47WSrJT0j6wWo3yPBVlhs/DahAG77zC754UmbcAkGZl\r\nAGn1By9jGS2x6fJNWdxSyo+wNboUhMi76VD36YEcQV5DNVSoChLVMjnuSbaL\r\nuR6mbrWV162iO2B9/HIveC7Mg5XCf7fxp+F1EGqjhRhhm+QKLzgE0a6A8Mvh\r\nad8A0A/X0zHPD9mqqaP22+GoA0PbF58Hpho=\r\n=IuO4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.4", "@babel/parser": "^7.18.5", "@babel/generator": "^7.18.2", "@babel/code-frame": "^7.16.7", "@babel/helper-function-name": "^7.17.9", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-environment-visitor": "^7.18.2", "@babel/helper-split-export-declaration": "^7.16.7"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.16.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.6": {"name": "@babel/traverse", "version": "7.18.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a228562d2f46e89258efa4ddd0416942e2fd671d", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.6.tgz", "fileCount": 37, "integrity": "sha512-zS/OKyqmD7lslOtFqbscH6gMLFYOfG1YPqCKfAW5KrTeolKqvB8UelR49Fpr6y93kYkW2Ik00mT1LOGiAGvizw==", "signatures": [{"sig": "MEUCIQCfPn4YiWpiZS0DKjNGHH5wxaidFvP30ckJssnRbK1mRwIgVCxMiJ69v2RsKkCi/0RFIlNgdAu5fndDpRh7f3uZX7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBrg/+OXSNrc/KDITShNB5XlX/aqLq8J/LCdw/BdlygzfDsD+69GEo\r\nYZk7He9i/+C1nm3+Mo4/9yXo12fTiILNCtoAWsaTNSJcgXKJgUK0U+ulce6V\r\n43WRfvmiDsOjf5ZGaBqspWTdxeecT8ud/2TKbfpLHVwatECTFFBzC5+f0dMb\r\nLuaQ8bi7rqQXEhLiGiV6ZStA34ct/ZDM39z8eiiM6QIjKGaSbEBhowAFkWmg\r\nsIgJbk4iT9FfsmZ6prSliZMW399bYiEvYKKrpyvilKmVMRdKTtydx0YCn6+H\r\nCCiHHS9/Q/5FbScAHqc0K7NypfJS2XByhzLUoaPLH+M/r2Uy42KdpWal8VfN\r\ng9d4nYqvJI7Y1a73BpXHaxQHXKQBXQoCB3Rhn+OutszM/lRXo5w0tHJMFpU5\r\neZRuEwLxyoabuDAPxgQPXieUF+Cy1k0E2fVURJ6IBl4KI5/F0cR3ZU9sDUk8\r\npmHxyYcKZfN4mQw1xlvPL6CvjQHlDwrLwXs80Y0eAn64qW00HkP8ruHbhPLX\r\nzgFLLXHmkp2w6TgRr3yEFVkBTURO8K/buz0cc1exf6+8K0/tKU0NP9jAWF6c\r\ngWgGVQoLwVzoXL5odp2oBeVbHhPT1x6rC2/SLiWV6XkqoFcKJYxwoLXmGuIG\r\nCR+WxHFKtzt+CxyalETDBcJQRhFVWWoU1Uc=\r\n=klY2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.6", "@babel/parser": "^7.18.6", "@babel/generator": "^7.18.6", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.8": {"name": "@babel/traverse", "version": "7.18.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f095e62ab46abf1da35e5a2011f43aee72d8d5b0", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.8.tgz", "fileCount": 38, "integrity": "sha512-UNg/AcSySJYR/+mIcJQDCv00T+AqRO7j/ZEJLzpaYtgM48rMg5MnkJgyNqkzo88+p4tfRvZJCEiwwfG6h4jkRg==", "signatures": [{"sig": "MEQCIFIo6tpP6Hqqxr7N0mJ+P4sqQL5uSzncujcdAOcVfQLAAiBSdkZSocEe1Pa5zJkDRgsxGOgFFg1PwB8380H6T15cVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/m1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwpQ/7BaHMptPPq2o7HYhgl5Vmctl+1QOTvbr9gH365miq4M4fzHzq\r\n4gWL85+V/kzlh11dwk2lR3GvluLpvorAgLw0D675g6M3DtMZ1iAv0xPITGHs\r\nkAT2tltIPaRXnqTiEURTONacK8FyQzV0hCCCH48Odwq4x05d4xtCZ2wk6PQd\r\neUiDuFteNtUtub10D89Qy2LMmOW/MRfA+cTYui03ykwcmrOX5n4B89zLQ9Dv\r\nwYEHlAue18/L6Pejouta60gwU4ny7dZeXllfoa2Kvh7d916OVI7WCrBcTEE1\r\ngf6473k015cGe3sqzB5t52qj6sytmzDVpqTIzI1hVwoDiP9A7tKp1nsfcTad\r\nmc+n6PBY2AVOHdLvFKUhYg7ifHCZTNln607w4Hg35PHM+eAXvZw/nCInecQ5\r\nkpCnQxieT2Vl3QW2T/bgzZvSx6WqMH4Vd8nq9QxdA8SfHKajbXwWgi/Z2yQM\r\n4HBll1WdHrjTfJTZ10RWssCs8OV3O+/ZgS+tnS1ZnF0gfxNDX7ZcVj8U5tbU\r\nRIVGwJTaDLfv0IrU3kVBrenxDeC/VItyxhAylo/PXrhwrpSfnOflfJYegDyN\r\ngl/wuyW3vHy43IoD4P4MbyjW81IlLlLTGkV4rdOSiiV46xhZyaHcMOim73GI\r\ntrlm87PUuiTgRs/y+L99V0Lce6XXHjeFD+w=\r\n=ji/s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.8", "@babel/parser": "^7.18.8", "@babel/generator": "^7.18.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.9": {"name": "@babel/traverse", "version": "7.18.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "deeff3e8f1bad9786874cb2feda7a2d77a904f98", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.9.tgz", "fileCount": 38, "integrity": "sha512-LcPAnujXGwBgv3/WHv01pHtb2tihcyW1XuL9wd7jqh1Z8AQkTd+QVjMrMijrln0T7ED3UXLIy36P9Ao7W75rYg==", "signatures": [{"sig": "MEUCIEqWHfy+URdlM0R534mb3AkgVHrifAQJZM5vGCBwMKOOAiEArJOwnEpv+i737CSdWzTX5SoejcF7ukNEyKxzC/Uo2wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/xBAAn/gMnex58VkQFbjPDhwbst9RJzqKdzTL1y+bEJz9kImRDboK\r\nHo6AhBlegfI/V2tOBl791oyfqP1FLX+2gBLq4yb88YofWjG2hiyu6RiXRQb0\r\nnhMmJguSn+nhWzLucMwvc4UVTkop9cLi76xBoJW0yHMHSVkQnDBpapnzeArW\r\nwfArOaVTD3tk0225wsheWtGOtKWnKQ3lebLR/2eEobJVoBkv9NIFUsiDBERx\r\n0yIOkKzcDD9TYJqx5ZGiy0fpR7cFkyj06p2GAS46OdHnzUCqBXwwsaVSdoPm\r\nxYuUwaF2lYXYEslesuYXM+LvldzM53KWlkXm+O4XBYhQuqQwkxxZqgaDJyqt\r\ndw1v4mNXmfG9dxDPkRRlEDSp4Rb6iBYQFHDY45HaaYAJXGzhxcQgjiJTJlmo\r\nIWGikazPbdOAYqGMXzSOJ7BuIjmZDf3ZuuZRx+XlsQhUnSLiUEicX4j+ValV\r\ndaDXsH2IpPgrRv2HNxIZlLDGqAMapsOWungeXLejWCwUw2TfDUQJyavdfTlU\r\nIJoKWYCeZhfucU5z/n9MJS+yIbSMrXKgq9a6N58j8P3irfsjBTn0DCNuWnsf\r\nYcw2c5MYYr5XIFoiHVTK4SHglzPCmmzsmcn5fJ37CymQaayP7fRsD6vZBHKP\r\nEv8DlDeIf6WbF4U3xv7VKtWall0/zNaWeXE=\r\n=/W3p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.9", "@babel/parser": "^7.18.9", "@babel/generator": "^7.18.9", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.9", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.10": {"name": "@babel/traverse", "version": "7.18.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "37ad97d1cb00efa869b91dd5d1950f8a6cf0cb08", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.10.tgz", "fileCount": 37, "integrity": "sha512-J7ycxg0/K9XCtLyHf0cz2DqDihonJeIo+z+HEdRe9YuT8TY4A66i+Ab2/xZCEW7Ro60bPCBBfqqboHSamoV3+g==", "signatures": [{"sig": "MEUCIQCpRNltSJ1I3RsiHxjtMO2dgoNLMSN0IyRpc0+jRFUaNwIgcU4juPMIYJkDc0VPb4xfz6jQ+lw1bDbpqOzoaRC3jb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSOQ/9HTbbdLXOTUZiZSmrCefreDJ5eQ9Gv7YtiD9vDzixj6iruig7\r\nJoKJ/JZsdRcTcOlKXP06PemThN11UypgW2V+cexRQuXKquB52VQmaNzQvrX5\r\n2y9uqg7thZ8xezIPOJd82bAxuoJmQI9iHZAX1SqDDlXksEGxW1qqvZWA+7mG\r\nMvEe0tMq1whxoeOJQfDlGa52aKcFQCsxvbExO/uJU0S7oew9QMnGaRbS1508\r\n6HvJ233BVILh3QvmF6EAbcsiaL6dhlVPjXeArxNxH9lTFNx6HWlNhJAZtOmU\r\nTT5Pa/8guh/EybBSFgZNSM4WIieaN8aHZ1QIjkfKSilTi16JMfGEH+f3Mz79\r\nE3BbGlDSsyF7oLjdGUGfgfAwrbMtmdHOU3YezwInC8YYaEeYcX+Xr+MUzT8Z\r\nb2VpOnwkMv3ROL86IfFNKon7a8I9Sz+zx37e24Mzal1wsVY/dTOaY2m6aJuF\r\n4W9LqWAWkkPBeUhRsJjjsQj3yt1Bf8kxj/2QHVcNSoCe+NGl7ZJQlnGdirxH\r\npR8WFJ3dAE77aTBiBIpXbgK3H3JOKETVDdA3ro1N8wXGQm1TlAXNjpcMwAO8\r\nupZ/meK+eToHQyzSvXwcFPcVVVKjU4XCK61STV19DBNuawdiIcPxQdQx/Fti\r\neUyMOgEzkmh4KYEMJ+pRDHByXyYDJgubQ2c=\r\n=bCzV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.10", "@babel/parser": "^7.18.10", "@babel/generator": "^7.18.10", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.9", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.11": {"name": "@babel/traverse", "version": "7.18.11", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "3d51f2afbd83ecf9912bcbb5c4d94e3d2ddaa16f", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.11.tgz", "fileCount": 37, "integrity": "sha512-TG9PiM2R/cWCAy6BPJKeHzNbu4lPzOSZpeMfeNErskGpTJx6trEvFaVCbDvpcxwy49BKWmEPwiW8mrysNiDvIQ==", "signatures": [{"sig": "MEUCIQCql/gXOOoxDN4pZIz482Ta3Td01hr0HA5dDuWAoGX40QIgLYl2oXuLbhxm+/SfUbO1t01SV4jttq96+tHB3nMcB18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6790ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2bA/8CScAW07pa5ePCURcE0IPMmp7HDakJoLATfJc04fCA7HMQbU1\r\nGNikq2NbukFRIHMjIXzLXS8je8/J5f8XvYuBoYoKgFuY4YsFrPaId74BW0Ol\r\n/mCUxXmtIjuKUliAk9VF8pXUkzwPpCEZgZbVyhvRdFuNJP1MT5XrUmNHDzDb\r\n85JyHbZeFsJyTYG9nyi66ZOVfL60vmQeuoZDRZUUAvbG/7fGEl3h0OHmeANt\r\n7VGKHK0wOjEWjv7rmEEQWf8WMoCQ7vBt4sY3pbDMF6uVl1l1qkJYAjgUZR43\r\nd4FxWv2g4aL/sKnI/ZrcKH3Zz5pzDIcVlCe52V5BbFVsv7WHHL6GRJh8nrI9\r\nFKhuzjZ4qWVLjNDfushy3vSvHTL6eDmrvR2Zzcc9OTdFiZleSCjhtdVTA8vL\r\nDtkomgj2NUOaiWl3NEary00m8MJG7zc8HbH2fPHBxXF7d5LMfh94lchcRgw4\r\n37U/VxDAlwS8EEpxZLsQYMq7mpDEQdsNwsONG0DHToVxmol/yalNWTXx5ccE\r\nUBADFQpw7M3D+v7E+0NbAIIi4G0F/JxX9sP6epVJZCQ25w40Tr2lVBqhH99h\r\nfDM+MpeRHhePj53z+vnuZYGkHL4vSNMHKpIxx5V17g+91gRHOdtmNIaNDc/w\r\ntYOFzEUTAojRXGOHT315CYQMDG9Dmb00ijQ=\r\n=geNm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.10", "@babel/parser": "^7.18.11", "@babel/generator": "^7.18.10", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.9", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.18.13": {"name": "@babel/traverse", "version": "7.18.13", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "5ab59ef51a997b3f10c4587d648b9696b6cb1a68", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.18.13.tgz", "fileCount": 37, "integrity": "sha512-N6kt9X1jRMLPxxxPYWi7tgvJRH/rtoU+dbKAPDM44RFHiMH8igdsaSBgFeskhSl/kLWLDUvIh1RXCrTmg0/zvA==", "signatures": [{"sig": "MEYCIQC0dqgvoMFFfsP84985/iJkGkNQQxijrYcL5gXNRoSCMAIhAJ5+mJwsQI00b1nXMEr6Um9eQrKfQ4V5nAyYATjIT4lI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouLg//Q+qjiI6Gr8yqet2akWNnLUC++H+7pDDmJUejy94+aYH499BB\r\nk3nKfRQX+XUx4kBnl1Ng+S9w5/n0DmVUZ509hVhPB+LKNJAlZpf8w8GgeQ2b\r\n2dwGAdtCEcgb2d0vUhTIU/AOOeX6J+LOBTxQi6zXl1DDRX84TsHAaEskz9U5\r\nd3GtJoNtcwwU1rc0fNcVLY6w5CKyunM//mP55KY+0utbA1u0GYxSQULEQK8n\r\nQZgElWA9ITrZz6JD6LI3sP5FLVu/6Ml8zfxZLJazLQvz/mVV740uZxGWPMPP\r\nY5viVZm5qJ7ohfVwpOnw+FoVAiS8R+CjEmhZhC9fUeeH5NejkVVzZe6bKViD\r\nYpDQLKGE4D1DCpGMr6/x/MWFqTIoH6MyLbxoTxDuEBYwkZP8HwTQu+meYbcm\r\ngNI9KD2oM6GC6dyKeQjr5Ulo1NU2O5MieJCmNnatROREqTqNdZQZwhQ2DYI3\r\nx0GDF4JFGmRnp7ucLyJt5ZOT20QMwZO/O966iz6gjk42gv44m3J9t0qrUhev\r\nGsokKw9YdDxiP8kLWca/r0mOGeAFj3MDQL43qQvr+8B5SYAUyksGfTKT/tbQ\r\n1PXaUEY2aKZ3rZDuAogtjzcaHRNVGAzrzB1/4qbJyEMcA2ED8fUz8pzunQnD\r\njf6UJIocxfEbNc79836XWVDA3d+XWo6O3Tg=\r\n=YdVv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.18.13", "@babel/parser": "^7.18.13", "@babel/generator": "^7.18.13", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.18.9", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.19.0": {"name": "@babel/traverse", "version": "7.19.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "eb9c561c7360005c592cc645abafe0c3c4548eed", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.19.0.tgz", "fileCount": 68, "integrity": "sha512-4pKpFRDh+utd2mbRC8JLnlsMUii3PMHjpL6a0SZ4NMZy7YFP9aXORxEhdMVOc9CpWtDF09IkciQLEhK7Ml7gRA==", "signatures": [{"sig": "MEUCIA6NocxuRnyXM4LrJQdP/JjIg62mX4zTtN2f7nn3AqWiAiEA4KvnHNG3Y5nuReTUTmWwDWKd2q7Y8Kw0WWD22z3xvg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphdw/+JxQ9bbKawjq4v8lOMe64OYOmiIBbPIYWMsoBkXMBej5qVnVr\r\npyAxzbv6F4hSI5DxUraVBENxzjiBNXMCvr17quOJ8ap1Q9/VvaG9oWMMUscM\r\n0ZoHdnV5iGd+G/YnvLJ9Ygnc+9ov9JkJIlSQB334IUbavYN1fi3AkAIbkIvt\r\n2J1OrLM3taPkUuZ5sPdrHkpkjCTrFkFsIyFL42iNo4EHTKG9Q+yzmn6oNkvO\r\ni+2Vv66rnZe4ITXEMinb7F/UJ6m2ZW/WTcyqcu71sMhwFTMXLPIegrO8ddkS\r\nTswXxFJs9w3qsd9sXHONMguTNjnqazGSOJlE5KnJ9wENSrb5aBrOdoYtm7xs\r\nYj36tkUIyUxDCBmB5daRxoO1MiFwTbR396RO4lgXsT6lP7Tz44Qykxd/f2q6\r\nw3sw1HUnY0qL46kVFDaI64O+RiKdgbAd8WtPZYzSKCqPisr8f+vi5SmFBbmA\r\nL5sFZsRvDb6nMn1Rs6mUlhF2tfikEX4vH8fiT9mzxhtw8duqQSK3zp1zpJQL\r\nXBmwtvIrIkmHMxlwjRiFyl8MeXyvWvSt6KhjzZuwFjd9qbhrNDeTx8/4VAO4\r\ny9bRtyfxbUcttBRVpD9f+q0QXs3ADcNr0Afj9S5S+0Mis2bOdsnaZwVCuEKA\r\nPfP/iM9eqPVWXMopO4n95tcPCczzb7buZ/M=\r\n=1uDt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.19.0", "@babel/parser": "^7.19.0", "@babel/generator": "^7.19.0", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.19.1": {"name": "@babel/traverse", "version": "7.19.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0fafe100a8c2a603b4718b1d9bf2568d1d193347", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.19.1.tgz", "fileCount": 68, "integrity": "sha512-0j/ZfZMxKukDaag2PtOPDbwuELqIar6lLskVPPJDjXMXjfLb1Obo/1yjxIGqqAJrmfaTIY3z2wFLAQ7qSkLsuA==", "signatures": [{"sig": "MEUCIQCJ0v2xGzoUHu4iHZ2ZfXc2ot+SIBrTIMYI39/LQShkbAIgXShW4mg+vLPuKvONqSB+lPu5LE04NTdRYagpvcC338s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGPA//a+tRfQUEMESq5Rex5EbmFBTzMUo0VJhxFK5Qz7tW2ZtkwBnB\r\nwy5nx0UjwC99TeT4VHVQd+vlfWzk1521tbWLzri4Phiiycx3o33Au5Hm9gfg\r\ntC5bNZuKcxVTmpgzb9gI11wFtVk0XYgnKjlxbVsthokeWfyrE3fQU2PNnBOI\r\nOjzLJ5A5NYYUsHWehigk3jZQTMQixeJCzoiq9mRzgM0SuG1K8H07yiSwEDpQ\r\n8CX1cqiSOGHohD0RFy/OIgjhgmfk5CsvRRzBW9/VWbuOhAvgI9yc9adq1pdd\r\nFWc/bivv86ez/ZIBHcpuB+f1MP+44zEn3lyQoGAnKzJkxiZD8+ooBsDXTpzY\r\nwUMeKNYAzOr8mr6aSPVBMBe4N3HydP6Dr+/EM2oZNwt84BTcjCIcCtpMLKPq\r\nbhWys2X4x6KIA7z00oTaVl4ohnb4/tXsEsrC1EzosOP539kHGA0U2NGLCGPG\r\n8k0V44rmKhD7mltf1b8DB95P0i+ZlCRMJE1nTYfFbnrMFwQBPAuqrdi6NtU8\r\n2IFsa+lp50b5icAEYFjf+dZcPYF742U22w+aO6RfOi4s4QSaY76JTNvrA8IU\r\n2Crx7frpyzwlMa+O0ZAmKG4V6IR85C8p1yVVIIsMXTUu5dDlHaQKpoQtdvnb\r\nhgIQRxepXXPkyZi/2bauGx94xYlWlG2OhwU=\r\n=ME3z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.19.0", "@babel/parser": "^7.19.1", "@babel/generator": "^7.19.0", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.19.3": {"name": "@babel/traverse", "version": "7.19.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "3a3c5348d4988ba60884e8494b0592b2f15a04b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.19.3.tgz", "fileCount": 64, "integrity": "sha512-qh5yf6149zhq2sgIXmwjnsvmnNQC2iw70UFjp4olxucKrWd/dvlUsBI88VSLUsnMNF7/vnOiA+nk1+yLoCqROQ==", "signatures": [{"sig": "MEYCIQCRj4OCdstZuunwfcdfQjJ2YsNYnX4kbCVLtdznEsPeTAIhAKINOIAZBXQJ/Ne9E+CT6cawiQuvbbL26GUvKBcRLyoa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0LCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2nxAAgJyGhW01Xq/s6v13ZJUMUbNrue5D1yLhdTrC0/xp6ASaobup\r\n5CS4CtxnIpvsEVTZxMQ3csQCa7NChr/8zwdz368aBY/IgCPQjLTmd23GzSol\r\nZGTsmAisJCTz7NXa6XbfK/4+lhkO81pkNQB7jbYnu9YqAalHIzQKoyNBw2XW\r\nFguF9nFfevt7vAYkl0F8UktCHpSE6Amurvg8vHj5qTLqFIW/hIHp7Uojb9fw\r\nptyxoV+ZfDpd3mMTOD8GSBk4VtGzPjsBzBlStCpVF1pY2bKHlIWbJ5EoQgVb\r\nc0CztWy+d54OSMwPRPVaiz8yY9Ncof1y5HBo2Y6qO78q+B/JEs1smkMo+//T\r\nApoVDKRESIGTCum+k1RrdghzG7ZH82CiRuZbHQJOVyrOhpcdbNNFRaWAEHWR\r\nsYRoxjxaRHhnxPALSyge8du2HKQQuJZV7Ay+EBHjsb/6mumNmxIDdEYRPB9L\r\nrpmgcpW3aYFzF3S5RFtBFRacvUk8THcQ69++b2IJfAymLHbudwO0DVQ+GDrb\r\nQ1sQ3zcWZebWSkPa0LRJGEQ4dwqkmjkPERB9j38BBujhs2pZEOpKZHfWuT/R\r\ngSL5RV1Z7oISHv4ov0Ipy4q1Pdq4qu+XCzfjuqEyGjozvbABl1OLxNA+e+xu\r\nyFmgDsowmTMUkKoK4hc/TXpti9BNrfIRj7w=\r\n=W77z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.19.3", "@babel/parser": "^7.19.3", "@babel/generator": "^7.19.3", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.19.4": {"name": "@babel/traverse", "version": "7.19.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f117820e18b1e59448a6c1fa9d0ff08f7ac459a8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.19.4.tgz", "fileCount": 64, "integrity": "sha512-w3K1i+V5u2aJUOXBFFC5pveFLmtq1s3qcdDNC2qRI6WPBQIDaKFqXxDEqDO/h1dQ3HjsZoZMyIy6jGLq0xtw+g==", "signatures": [{"sig": "MEUCIFTGfB6EVlriL2JgVHCUHGoZ6MJQsJt07IOumawzGXUFAiEAxUFHid5MaxoUV/+PjRspjgSrb+l4UoTEnzeH4d7RlA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh7Q//cXgJBqSN7tz3P4+5Ey5iQ2Odnhz7AwKYZRaXunyP8xff7x6l\r\nYvyG3E4m8fzFuIA1/arck646cQZm0eol7L63xQYLiU8kxrKxC9ZGePmBh5gn\r\n2+YBLmxkmulKfdBTi611iW3xw4cpGb9NSOnjVX8fVMcZDpKRzsMjeb45Yzcx\r\n7BTq4GKQux5vsmfpZP181oLzSagogpiRJvfgXgB0u0TJbWWwLM05tVRjnAJR\r\nKw2UaFHRKFxk3BoMIMHG4HfP09uY+mrYacEcLLWvy5zEGIBw93olNdUlu4Y+\r\nl5EYIF+D7bHmENUga4Q2kyFyDRMNqddKMrtxnAgMr5+E8coOb0l/LYeeEerF\r\nzT0C/2XVFDJs8HqBDj2krRAxuKBoYWuCC5zBRgBYPfnnTvAMd82tjU+o8TT+\r\nOoyRnK2PJTjWNTTbjo3FRPVVZKEShwzN1rebCpi/1+09tt9LuctrxcaFDGUD\r\nuIjo88F+BSUgLzttyBKQX0HjWZOv4KH8tyG5bLOn30GBAQsQwyV4/Y0u0Eh2\r\nVWeKsWFBnT8Jk4Mt4zFhqYJnQUN6AsDVP4Hz6d5JnTm75Ig/NV72H1McO1ZW\r\nC6kuEe3ZWBHxXCSf60dNELHnm8ccCRbmp0BN6muI+uZPcEaE1Sb4HHd7UH4y\r\nGAxeZzwd5vKb/RdXv3hdSu3TAyWVEtagD+A=\r\n=SHrE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.19.4", "@babel/parser": "^7.19.4", "@babel/generator": "^7.19.4", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.19.6": {"name": "@babel/traverse", "version": "7.19.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "7b4c865611df6d99cb131eec2e8ac71656a490dc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.19.6.tgz", "fileCount": 64, "integrity": "sha512-6l5HrUCzFM04mfbG09AagtYyR2P0B71B1wN7PfSPiksDPz2k5H9CBC1tcZpz2M8OxbKTPccByoOJ22rUKbpmQQ==", "signatures": [{"sig": "MEYCIQCqJUdvfeOghj3iyQa7IA/dA6DyYvGGH9vEF+3GnJGQBwIhAPVYg95z2SY+Y/+y12lGZVZMHwfEgR4N+8ejAfvVpgTa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 568417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfXw//SALOKNmLoh9aPCBnO/qC6uwwRn/gg9b0bYV3tbNKHKPg7WSw\r\nnrQ04X/okwMDSNHVh30kUe8HDFXoXw3tYLE1MQcVWgj3U68stu3b+85fIGns\r\nWVuwu5TobrKQ3Du1lh+i6vT10TM9ic3kn5MdH9T9a9UZ/7F8V3LNcwvwkQ2I\r\n9kyzI9a/YoD0e64UnP3m4mkdeQdRrTI1MHdm/UgzVEtbahIejkeOFdTn6yZR\r\nsKt8QtneA0++ZGS7hqNoISfxBQgAIXh0Qm6xUBDiHUHEN0O4pLOhGaIJYUbW\r\nyPd3BrE4b+cwgvI9/RECzIT8ZlDDzzunmyLpdnC03adBpqRoc8Rw6zNeyzr4\r\nw/kojHHWksfqjdK7G2yMQrqGlFtDoftkIZq00GDNITpK21VsVlUgCJW63sHl\r\nkYuKo7sQ+lUxNreXOJBdr+8wYwEU2beQKFJSCQ10LvbpcnzBoSTxAxkDpli/\r\n0JnfY1+bDbKY4EiDKIJuggTl6MwrIyGnO2gXyVVgvA2ikFTJMnGAZpeGmleH\r\n/LPG5Dnv1qUuTjy2D/a2x+B4LgxJ6At43bGpI/DUQbSBzoCLRbsO3JJ0YH3o\r\nKtA3vySZ8pguF3zagnAfpsvdnuaeO/LdNEgHXhjbKl4EjgL2a7BgfJtw9oXk\r\nAfdIZS3JrA69ckEYgtgHV86Kl+854jZWeAw=\r\n=th2U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.19.4", "@babel/parser": "^7.19.6", "@babel/generator": "^7.19.6", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.0": {"name": "@babel/traverse", "version": "7.20.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "538c4c6ce6255f5666eba02252a7b59fc2d5ed98", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.0.tgz", "fileCount": 61, "integrity": "sha512-5+cAXQNARgjRUK0JWu2UBwja4JLSO/rBMPJzpsKb+oBF5xlUuCfljQepS4XypBQoiigL0VQjTZy6WiONtUdScQ==", "signatures": [{"sig": "MEYCIQCHOvmd2IuC8e61UjedfnTnaUkPuFdixePTnzM/03qHCwIhAMLUA1ldzCNgVF9Yi8pZvGmHob6N3tOTq99BAn81t5k7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5Kw/9FaOBvgpbjUr0SZbI/mnMhGsHYo8p5dpTwYPqVtAcwFe8QyVR\r\n7kFLAwtM87fMedQ6ltGc0Pf57FhvxeEGh1TYp533SoD/QoBmbvDJLDbUHLCa\r\nePXZJssjKbt0YibjolV4JNUsydvTW2bEuv4RF/o8IWJa0atc3C+5e69yp8XS\r\n3IP6wFhu5lHjsH0jFsS/Fbf2cNNQakLAbOKPIcK73e6RY2MuXkFI3rZUpOBR\r\nhdN4ZU2e9GvUuFJ2aTJFgfLEZD1dc69Ocrar55INdQMFiBNhk5N6ZM0J6mE4\r\nlqjI4OgDOlneuz9PA6mvKIEoZ5ldJ3fRS/0zNv9MGtJcGQNsFa46AnEUXIAO\r\n+pIpx+r/QYjpZElCJoihLrDnStbdsHeZmWbgJgXbLDWfRBWLghG+S97AtFjM\r\nxjdHE3ZI6m2V2bRRgnlxoCdEsf+r//rAy1NrrOyvoXh9qYWHf4yts2BIhFbb\r\n2d2EJG9xgVD475bnQWIkoJIyVNrPNPDsfT1lRWtCGzXbhdbNgoeVOJO8KxuF\r\nHJLYdausma5KFNJf7vh3KuoZCsA7T5DbKkkjAd0dIEYr1bGWaJOZZgpMWTpq\r\nkDsLITH2/e0vACGlrCU0JcV6JVEQeM96vIAYMHDVcRAQpxYXcc4D73iFIUoT\r\nHXQ6rgmIIG4UP19aZ1Y7DsLu2ep6zppKhoE=\r\n=31On\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.0", "@babel/parser": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.1": {"name": "@babel/traverse", "version": "7.20.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9b15ccbf882f6d107eeeecf263fbcdd208777ec8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.1.tgz", "fileCount": 61, "integrity": "sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA==", "signatures": [{"sig": "MEQCIDUp54IQVE3qMfvRP16YJkqCVLF7DOZzC7FSet2cONY3AiBTjiXzc/P0qDJkJ469/mxWNr5ae3AUk0iyyV0vbe5nvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 571169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYQI4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOAw/6A1xQOEF/73QCPowHvLYaOriIFNufheyooxA80ZNdCXBU16y1\r\nMHk6evjDKeoBo+0GH/tKEBJy49q+0yagMHLLVztxwaWyDhnOW+bIT7KN4W+D\r\nnqRG+mXpZbwI2U+rBOB/8q2Pr8hQmik7sX826IswvuKUj0vW7rlc1o7vtOoM\r\nBFODJhWZMb8WzpgqNhIA9B5RPo+V8/ai6rlSfG9W57CflKmKh7qeTln7NDzL\r\ng7K9jzdHLcACoiUF8a3+JO+roFo73tF6hPSYFen9laBlMvA0t9lkz20kPgxe\r\noeqfgf9PsU4Vo9/9UukDT91lwJ1+h4McR+AxQuJptz4kLtwNTsKYa4pmiu52\r\n60AcUsURtiHt1rPyYWwRMdM9Mo+M5OuLpyHkKunqZ63mZY7hJgyHI+iU7De2\r\n8NZE7km4/1/+JdxJtxMKK2FwNSzzGnGepDaa2NgKhjz8Fd0yGmAxSIHlYIxp\r\nLd7mDKA1TmFd78r8aEijuV5jm4fuIjQxOZE/IuvWcgEH8MpPxzrcYhALLgO+\r\nyUp/DeaYqXjhF1/Ip6yBlgdaJY+fADAvNuAAnReTdqb7CViwyL/TYaeX2Kmm\r\n5gH1iEXtqqR/ndVRhzWTC3EAdQKU6BRp+4mvLnjPIasWvvinrRDIRHZsv1I8\r\nFNR5VzIW2zdfTqpAZAMqr6EfNGpYXss00Ho=\r\n=uOOK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.0", "@babel/parser": "^7.20.1", "@babel/generator": "^7.20.1", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.5": {"name": "@babel/traverse", "version": "7.20.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "78eb244bea8270fdda1ef9af22a5d5e5b7e57133", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.5.tgz", "fileCount": 61, "integrity": "sha512-WM5ZNN3JITQIq9tFZaw1ojLU3WgWdtkxnhM1AegMS+PvHjkM5IXjmYEGY7yukz5XS4sJyEf2VzWjI8uAavhxBQ==", "signatures": [{"sig": "MEQCIBHqadpUyz9FdnRkOqqfRxYSeAz1G53fI59N+ohY/QBKAiBgTmd6QgM12pNn21IEWaXaCNdS/vH/9uvAydv3JYpzzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 573737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIcQ/+NBDWs+FGlk9tfeea9hfTRXyiL43OEsu+rgkdb9NLtYnaifZ/\r\nY9xKVILL1Fv9DN+KoX3/nRMHaAvbG91P4LeQBHLAStqwZ0vrETlGjt1YcwPi\r\njmkwN17W2y9tyKN4zE4xoBv+W9ivUXnke0XU40q9eUwZDcQJqdENlS4XgpGN\r\ndndfwkXq3cIJn7adtI+8lplGE1FnNxCroJGKKqB5Ti+5y2gNjMo0ht/y2iRQ\r\nRZgV2apRUSl9ulw3E2LW2viOKuRiQSDzqWvMwUodyjDxGQUCeZYPNGXKB0qX\r\nHynUA3idhXTNbU2qv4sY3VG888MjaTLAQ7vayvhbWO8ohbN2MG/0f/v3Q92L\r\nLm1mvlXfRzmP7Na4Zw2PVgwq36POWohaQ8WZ9aw7HG47Z2ZaZfmdnk/qDC/6\r\nnFWuyonb8gp/pFtsWnfoMcO8fLNmPTLgIOEEK9lFnXM2J4GzhJkVS0SlCTFU\r\nvYnnouYcB/Dlx/irLZ1xUJpKaLLevFpQ85PzMnjnB6U7v92WEP81uSz0JLpW\r\nsOhLhwb1/Kn5HW0xDXIVnoAQ0aLdZt+jDGUQsA554PUazWH+gGdNHrHgsUTr\r\nypoxy05y7wkoR0lsnRxl8UAtUSwOVIhk/LZT0koW/Yq8dDjD6nsyxOANePho\r\nncIk9gWBbNAvUgjEozNyGYwolptB/Muq4JY=\r\n=Sy15\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.5", "@babel/parser": "^7.20.5", "@babel/generator": "^7.20.5", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.7": {"name": "@babel/traverse", "version": "7.20.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "114f992fa989a390896ea72db5220780edab509c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.7.tgz", "fileCount": 61, "integrity": "sha512-xueOL5+ZKX2dJbg8z8o4f4uTRTqGDRjilva9D1hiRlayJbTY8jBRL+Ph67IeRTIE439/VifHk+Z4g0SwRtQE0A==", "signatures": [{"sig": "MEQCIHTPku1beMzx4ujODOph+ud+hlqMp9rbOc/lc0Zvp9hxAiBssVHbmrvxHU2vKo0HueyaDMjZiJYaQTZuK5dj8N4Iog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 576609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Og//bbWBZJZ46IaGOI4o5izzHuyCTG3EmbVJeuSM9Oo4eg9fLGUt\r\nn9OCniDnCY4/E8j0Xe29Rv6gdPTwEvF3M9apSyw2YfSn1cpcvF/XoEhfI2uU\r\n88I8qWgzVszh9wmk7YumQyptSg30L7OxI4m3arVdiEzygaWlMo3g0OPvAQNb\r\nG9BOqZkyQ3iVPU6G+SMk2gll9dbCYv4nDM2D8EJY80ehkSZQOLP8217yexha\r\n0BvbAP59cURvxhEK4rQ4zZ+ls8c2HO1WozJ/Z7bEc/6NPW+F7ms79S9wu+x7\r\nS0UcR8Mv4EhQDN9eff864t8DXYe1Twa3+8qBr8bmrFG4wMXRrgU89gTT7tJB\r\ntXuylMD6z5nV9sJk2b+UfFntrqf4STzoVDvxhDRgM0ghAXOXJx25gZ2e4Y4n\r\nuqWOu3vDltxs+TSWXc4JPeIym3iscOm2hL+HHvEcljJ1WSun6PfgjoyEyNZd\r\nRa6OgIunxeEQN+ke0P7fdiMUyCZiioT7Sk11jEww+dKMtejDdc9aA1zYuMrO\r\nBQ6sPVPGpmaHqnjly4PRW6ro9sQXr1gvE9IcerHyiN/jm7PaUKMksKqZ5t4z\r\nRoJfaEKhnJ4zLW33ovbg5HZPtqifv/47w8l6UnH1oniz5hoUCF3hn1R+G2vv\r\nQ2PkjCkZPqMvU12hetQg2A3m8+njkSFPSo8=\r\n=ZHrq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.8": {"name": "@babel/traverse", "version": "7.20.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "e3a23eb04af24f8bbe8a8ba3eef6155b77df0b08", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.8.tgz", "fileCount": 61, "integrity": "sha512-/RNkaYDeCy4MjyV70+QkSHhxbvj2JO/5Ft2Pa880qJOG8tWrqcT/wXUuCCv43yogfqPzHL77Xu101KQPf4clnQ==", "signatures": [{"sig": "MEUCIQCJ4VfH7aIrWM/nw6SGDyBzSK8iibtUjtM6vRZ3mvh4WAIgdmcfvTVJPK87vx1Tzk4ZOwA6mffMYoeQMucYKKhMFVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 577468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpIa+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvYw//RxFSbC99w20b2Y79ZQy/MuWog1eSOkvfT/xjRFzUfyHwdL2P\r\nMOynNfpLF7U6CqDlWu2ugoZ29C4ITsj0vW+o/8PLd9XoZBiJGGOj7xk1XVAH\r\nRaS6w3KwQceLcpPPHu7Z2xC+dLI9Q71pevQ9AMUshfYVlVSuDkhuHiW1KPlx\r\nn8eKQk19RgfANKSZAXvz7Q41bTfTDBWO4+fzD6ZAjNWwMZ/aDo6FByAh6oho\r\nsL5YUwSMvQlBwDTJfDZXcHKT/jNGfcV39lIHL0i93e44l6pxfxkRyg0aglnL\r\nKKup5SjXtsosrFU0fRmHexHegV4RURjUooHpWWCI31BcUWhwTnmStilZtt/4\r\nNy57a3QTn7lazJolb/cGRijTybSwdyainW6OZGY13ZD3cGi2LyENa+rzC1YF\r\niOWRZoz2Sd1BFd1JwYXm000I8bztzs+8ZJMrwV5ayTi9Q7D5rjdVYdrvUPVG\r\nWI6g/um7el9KMiPNlTbPCzyEv2OQ88/PClG6OyMqqsNf/ZAGTTokAsEhfrQt\r\n0nKsNk46YmmdX9ykrtJtvVHg1QTQWQS7ek6y4ImfH4JTGwXnBC/1QWuEpQv4\r\n7tm8366c1xT6wxORngji2PEo4tuqc/tj1o09VHfe0HssdRQzPrDvUlV7ETrl\r\nQI39YDVeAtWwcVtpe6FvEOTnMZnX7bZQtWo=\r\n=/De9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.10": {"name": "@babel/traverse", "version": "7.20.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2bf98239597fcec12f842756f186a9dde6d09230", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.10.tgz", "fileCount": 61, "integrity": "sha512-oSf1juCgymrSez8NI4A2sr4+uB/mFd9MXplYGPEBnfAuWmmyeVcHa6xLPiaRBcXkcb/28bgxmQLTVwFKE1yfsg==", "signatures": [{"sig": "MEYCIQDysNxt8g3zEGneFf5jpnJy+RbnKDsOnbWzgShVmmc7kwIhAMExRPZz6mS3w6R+SMPidXXdRLJ2zN1ZBt7W1FvnAu7/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 577311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpXKOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxgA/9HQAEhPvSuLTx3cQhRWDSZadwEh1EjUlS/u4kUYj9wel7PPVr\r\nCdOLAx2+C/JH1nFryhezFCen11pRU1DIs6J5o/WVchFPLbIdpJJULMmSQcIc\r\nPT0Fq/VkngEv6hQ1J7XIQYcvRxQ6X5yFTDBcitNR5yA4VOGz6y6sxD4Iafcq\r\ngGdyXEYY3EInka7VBjvA0uZa6rNjJ6/IIBF1jDPk+P7+4TftBdHnZGY4DjHj\r\nWU7lwMtkEjBfaQNvVa10bScOzcnI2dtX0WUjI6HZkoJURphUv3mVDh6pCuuU\r\npyeW7ohj8W7NaBBsUbJPI1Ta/ZpIOqMmxUpNm7L6yXVo81VOFrGSTA92J6Eb\r\nuhc7Jm4vbQRSJwwsY6W11fW9zJSG77fBhh8yoT/eaPOa9O1yWpFfkcFG0jeb\r\nwGIyOw28C/B1i0bCM1dFF8JbnD5RyXpzQFp4/Q7SslUbCajxlkWNdlF2qw+f\r\njNPfxey4FIYBHVujth4u0+QmCk8pDzl4AQil4Oz0zKvYFdgenCJaGIkMVEq7\r\nmt9ZKowgtdRCAPq826MUyBUOvaqUkfMnbwHVCdoRK7I3xD8Q1hT/aHBe61AM\r\nCINVTmerAK82xfo1dvJmce9c5DV+B7/m8tlsFF7VqpL4uGVDWrdEfJokOVZk\r\njlSPQXKoELzf9E7QodGlXnm1G4yRsc17Nhk=\r\n=i+9b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.12": {"name": "@babel/traverse", "version": "7.20.12", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "7f0f787b3a67ca4475adef1f56cb94f6abd4a4b5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.12.tgz", "fileCount": 61, "integrity": "sha512-MsIbFN0u+raeja38qboyF8TIT7K0BFzz/Yd/77ta4MsUsmP2RAnidIlwq7d5HFQrH/OZJecGV6B71C4zAgpoSQ==", "signatures": [{"sig": "MEUCIHh8f7EYWz0ciuNto7d9Ctb6TwYxjIampmsu0QLVI808AiEA/1KeCh3TeLleyuHrTLIcOAn5PqEdjuh22F+2wEKc8po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 577714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtaMIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkJxAAjb0/YC5jAHep4SftdhnbWHQiK4Qq/rMBtRTz5qHbQ3FHzwmM\r\nELCxp3hcN8E7jn5TfYMl5hSz9DhujFNawuwG7al3VFwrLtnWlucPHnp6d+UI\r\nN7Lmr77GlQXXCCjJ/5uznUydXFea6GXIP+PF/UTKolq66Bfg48Tvbwm5DSJN\r\nuG+HEFbAqxZt7NqQ0c8GZ/pp+WIM0LMQRNjYVVrjHmgkbed4OG0EIVVm476K\r\nLQfY0m87HRSgh1q82lN+idIvWzvhwHcfzgyoPUV61JXlr4rgOQNy/pqphBAj\r\nU0UVegRRdco2/9MB/PelKzXI2xd/pWswO3992e3WDSjDyBJRUJF21dKLl2dJ\r\nbUtUYmj6hiDYzwQLdpVdmAthPzZ5qReogtlKECK0JPpU4WiXBm16BknAexuF\r\nFuY72MWBHT5pbNcYahzMHV6N/kNOqJrkmuqKVtowhf8q4XaYHbH9LSqFd8IZ\r\nD6VshzNReVixGMCSo/7fgtzxE81leDUdOzLvycZEKUSzlDUfsx28g/hlbJar\r\n6OqWTYPKpc1Peb0+bUJKjgW5y0UlNd3f8KGdnX6wXbosejRfKdVMtLSeGaXa\r\n/hor4KeTYgfU1yqSWO5P5xBwJ8G0Bp2cs0L5N8vTnUQ1SN+QHBl6VLLBdKPR\r\na7NZNjasWJauDH0i7CcZm+BhJPN8dcBwDbU=\r\n=4RVY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.20.13": {"name": "@babel/traverse", "version": "7.20.13", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "817c1ba13d11accca89478bd5481b2d168d07473", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.20.13.tgz", "fileCount": 61, "integrity": "sha512-kMJXfF0T6DIS9E8cgdLCSAL+cuCK+YEZHWiLK0SXpTo8YRj5lpJu3CDNKiIBCne4m9hhTIqUg6SYTAI39tAiVQ==", "signatures": [{"sig": "MEUCIElmRtCnWP2Q/u3jJsDiy1u5CKlleiI2fK+lFIszS5UIAiEApK8+LCN+Khs3L+qqCmhbYEu7pKKWh/uW9BQ7yXnOUQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwdA/+MzC1wbUSF6X5YhqnsPzXhYgUs8EPKIKyTSuPHXMBCXTH8UDu\r\nBHs733RI3DJRjMv2SnUNfH/vxZwLAVZxqR3mw8VyB2ekpL3AdjdRtQBv1ESO\r\noJQZ1PL3YdWIO2YHTxD3Ea1w9xCq7yLkGo4rJmcBWq3bJuWzb8pvso95ZXd1\r\navvijWox8oFkXU2/kQIyUa9TfZ8lfwOOFHtIDSjNn7oWyZtaJ1u+7wsYeyzt\r\nUlljDId6LRn2qbNsLNzm2waET8rA0LA734q2qWAz/tCy5KPx4fKRNDC43kIc\r\n7PnB+mbfW+l+PBM2LiC1V52/1AThC6/nLJM8N6TgV89NFTBDuqw00nQdy7/c\r\nF4HfZO+rIVVNUn5QplZTiTgmEtUqHHHfk5xvarA2ekDLKStAyoVrWWvF1Az3\r\n9K2WCcgFLLQYmgcdTRieAAwpSmHL7zZ8+mDwAmsl9YClcjXXSIJePM1ibN30\r\nMhv909aYgS72NUabJcfz99HFK4KyrCxRW5ACDX4FQwaKXcEf3ZrDVk93H9I+\r\nhyc0ecsbuwwZQKP3Q+biraytygxnArmvu1SQEweMgwAtE7KRW2nYf91r+8+Q\r\n9DWKeAKysin5ALUKm3Y3pHtU0NgESkHFur2fsP6anLLDllNNoh9KPJpzlPVo\r\n2UrBWerXcz4GQ5zzNpS8y8X9eR7oloyagyk=\r\n=glej\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.20.7", "@babel/parser": "^7.20.13", "@babel/generator": "^7.20.7", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.0": {"name": "@babel/traverse", "version": "7.21.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0e1807abd5db98e6a19c204b80ed1e3f5bca0edc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.0.tgz", "fileCount": 61, "integrity": "sha512-Xdt2P1H4LKTO8ApPfnO1KmzYMFpp7D/EinoXzLYN/cHcBNrVCAkAtGUcXnHXrl/VGktureU6fkQrHSBE2URfoA==", "signatures": [{"sig": "MEUCIQD//ocwvWz90SumgskjcPx3BwWfoJeB7oApkpand/FqOAIgGQRHaT2eY2fujuu2E5Y0uM7o/9Br6jyyvl3SRIY1sA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 579344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPGQ/+JrygBIbiNXXzKj7MUo5EvLuG7GL0jo7jdqcSC1vEw2NoVufx\r\nLrFD/qB+VDirwVQFVqm/1pndgVl0ZpaNLyj4CIx/ZgTviXL3ukRs4HG7pHPw\r\nL72CPnvfCtgdaVwgvLBlvK+KzUUT6k3o5lDaDlD/T0UZlh9Xs6TszH0Yaz8W\r\nzfzq2VQuwfzx8kvjc+BK+WxEHHiUNkwJubG9ovP0gw9N++B9t+8R0ogW6X+m\r\npy3/MoJLv9gIHUlB0mfyE0toq8B3K7QaaFrH2IMyU7xM/wr/+ZEhzicGpN//\r\nETVp01Ji+MeE+UPuu0d/lZRcn5Cc9wKCAWOzxaM7Cw0nzTX+ZexfwceD6562\r\n71MZ4tNjhJQbldqQ/nRV5HzScgj6ahF+v6T7/44gvCIubKC4fOMcEUM1MUo8\r\nq78fH2tL3xbyakms67LOZEOYOnIHp95HC8vcon24/qpaGSqfacewH9ypyMZb\r\n1/XebVvvkXLROSrVxTLfV6mQdSmcY6buYng7qZsKXHlVOl5fpvgrDFUtINtQ\r\nqUhsZQ5F5jfMzziS+WTyQ3C34gcEFf7ZbccDiDoKMYRmaSR0uc5Dexs/pTUk\r\n/1IqRK3bZPvIhr70adV8bYjTeQk6tlduzIOSUbuw9n3c2z4shm4EapsyPx3n\r\n/rkjRsEGgL2uJDz0tQKPOdOVeBbNfv65f5s=\r\n=URYu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.0", "@babel/parser": "^7.21.0", "@babel/generator": "^7.21.0", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.2": {"name": "@babel/traverse", "version": "7.21.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ac7e1f27658750892e815e60ae90f382a46d8e75", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.2.tgz", "fileCount": 61, "integrity": "sha512-ts5FFU/dSUPS13tv8XiEObDu9K+iagEKME9kAbaP7r0Y9KtZJZ+NGndDvWoRAYNpeWafbpFeki3q9QoMD6gxyw==", "signatures": [{"sig": "MEYCIQDdBNc4P0mTYRXDDJKu+0983ljoebfh5B2IlYfeA1zbiAIhALCF88uNmvKi6JJRcQFuwzWyAuUP4ZNzUvOHU8jaV51M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 579304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zJ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqryQ//eVosguTtqY/7O7JGwdlDHQWpar2gC1rH2w0iwalQT3S2PWVQ\r\nZeRnsK6JOzilM3ujZdchtG/sf4VOPLovK9hKhA/3DS02vC3TSaLKFtObw5SV\r\nzmnERh4OSD6FIhwC+3HXAAZTARvG0hLDUX5BVl8WnIrenk4KHJkrx2qxBL/9\r\nXgssKdHew5/kjuJlXmXglvqIoXdgYLYio9Rco99IJsZFWP76jeagQfuEqpfO\r\n0Yoq10TPlneqtAtR3nclr6/mfBUmuUbz0D60bS6fTljM5F/3TMPAWWKzdVph\r\n42/RJHunq9Ka7JTqK2Bp8wrF3TF19ZLHWsldlEtYSC5guGgZPu+bftuZgNJP\r\nSPb8katDFOzAsSwOpbxUQ5JixNw5c6ZKH2P254NDsKfNQzhfLO0ts+8sgFoc\r\ncDu5VmsyvJVxRZBLRWGn2APhU5XAvBZyemNy49QDnw5/L3pUXImF5+3YMDGK\r\nPgOY7GPzU8h4CDCY9fBvRRRvE1/HzwQ8P/ziwQbz4rXnPxDy919Aj0njIF96\r\nL7mO8WKBDbOguRc2w2aVCsg4aDqHK9Bmcg4unlHhYPCY/xY3V3Co4T0QWk8R\r\nYBcSEl/Au1yD+7EdhMarlXFCqRmkHYG5FaYygQwUhuY15n9xxM1v2qum7xOD\r\n3/J716YfAFdWbI1MyiY1V4GDo3gfSg2eeWk=\r\n=vIG1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.2", "@babel/parser": "^7.21.2", "@babel/generator": "^7.21.1", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.3": {"name": "@babel/traverse", "version": "7.21.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4747c5e7903d224be71f90788b06798331896f67", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.3.tgz", "fileCount": 61, "integrity": "sha512-XLyopNeaTancVitYZe2MlUEvgKb6YVVPXzofHgqHijCImG33b/uTurMS488ht/Hbsb2XK3U2BnSTxKVNGV3nGQ==", "signatures": [{"sig": "MEYCIQDCLzruCtdCW5X7q4H3fUqDYZGHieNHkanyFDPqXov1iQIhAP1ylBhZy5sA6fJNvErbCrI2HJB3HlKPz763wgxxSHK0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 585176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIveACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUsA/+LPuTAxJUWInwLarQ6yy/Ru2Q52s504HSR0ZiWyrR7bgaPVsx\r\nzDPOotm4i9tJcsr5YoHH+WHsR3lS+weGzvuwP2neWlAvA0DRXLO0glusHI+Z\r\nTS0Wffs+WYYBSLT7V5EnYFCh9eSM6LZd9WMDNbk6Gdwu+tHcg4Gc/fUJpyuD\r\nMmDyYA6JJ2J94ccmfuFmt5GhUizxZsdUUdTG46Xm2tp7BpQOJODmm77AZuZD\r\noMc786OhnvMENqgwWUj6IPib0lNKH8DE7PzxGbAS83yYyk6ShViXW7P4lxdC\r\ntCPDT1aYRsOeVV56U+CIRaf8+1UgFH3VY/sshEhfvJQaoccDB3wxc4fOIGzS\r\n/dD2TnZoB2iAo300QNoR/asHZWZeVOva3RaLCIZ6NaL6A4gQBubKbZ21hl8m\r\nbXrE1EmXtVT2mAgFhMxIWUM9rKjLQmAi/reCB/kvOVKYBSM4yw0NOJwqa6Rp\r\n52OZrrbnMHGI49zYwKAv9TjoQLI+YA0wa89zmcPqeeOuJgy1VPtpRuEJzUid\r\n0gzIW8UGo/vjgr5kzAIDCSY1c45qsdTEbmsZrd48l4tuvme0ZBftFr35M1gv\r\nQuTgY24Hb1GmApXmM8gXw087SYxd9VAD03fF6cA4ug31ufnvD4U1F2Lm4vbR\r\nKSV0yzxgkFgEDWzYOcEsUqEHQQZr4j/D7NI=\r\n=w8PF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.3", "@babel/parser": "^7.21.3", "@babel/generator": "^7.21.3", "@babel/code-frame": "^7.18.6", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4": {"name": "@babel/traverse", "version": "7.21.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a836aca7b116634e97a6ed99976236b3282c9d36", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4.tgz", "fileCount": 61, "integrity": "sha512-eyKrRHKdyZxqDm+fV1iqL9UAHMoIg0nDaGqfIOd8rKH17m5snv7Gn4qgjBoFfLz9APvjFU/ICT00NVCv1Epp8Q==", "signatures": [{"sig": "MEQCIG4+1KkEmKMz6RJJd3JtD6h06QuZFyDIKE+ee9sHsjmIAiAvUBZb9GUSF+o4h+6UtukM8z4dcW8spwQ4HebDKJjisw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 586963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTUw//ZJWvw7e8iZfJAt2K5FZOUdfenb7TW3qHQgYBD20lJBG2Zt2m\r\n1dc4DohkK8zH4CuhV5Es2XMrOli5CoJQ224w9hWzAR25ebPMYcCpP7jMX/Qi\r\nh3CS3FYJu6rSXo0k5+OULHV4f786SHMu4tDKoO92PpUxReWwHnpCSK1y4s0d\r\nXVBpGJN8gpXwWytjsm5N/0cX4iN/xnaMNJszjPehewt0cxtEKoXS5oalQJ/o\r\nUOXBbhOX25j1/8Ol/SnLkj+NMAtGajAj+KklT4HmhPE7x+Vso45JMAC/QsfX\r\ntYaBefJs+/BnS6R1yg1E/Px4+QVetp56NaDGy8qOajADcg33LtLWEE5tXT5k\r\ngiH7B5hiW7FBxLXgW2mXy8ianluTm8LuG0+NxQVwPHdCANHM4i27SznTQA3d\r\nUzdQnbyHWSOaMU9H6rCaEArMwwceg9w21UcyTSdcMByOheYXRlfYDRfQXs3x\r\nHbU2Tvn0CmK0onougEuMxxQlKWDO0FqkdVFxYhV3Pno2Eu2nI3rZx/uO+b/o\r\nrexuLkmxaKr1D6RFCyzL7DF0bJI/JKImMZwl0YyXGqOnkOXf9v4QFQAeqphx\r\nEmEz+O6dbn3OKiKPldHjz6GdNeAmFPa9JPtRF17oBU+DFUAkndyfz4sXrL13\r\nndxhxTyu51bioTbi/EExo7HzNYmOm4O30RU=\r\n=gvyw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.4", "@babel/parser": "^7.21.4", "@babel/generator": "^7.21.4", "@babel/code-frame": "^7.21.4", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4-esm": {"name": "@babel/traverse", "version": "7.21.4-esm", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "077f14f9375502ca8ff2b585fbccdcfb8792b927", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4-esm.tgz", "fileCount": 62, "integrity": "sha512-0O+hmIeLgfHnH62C7vgg5WXxMi+u7QijtHMq4VCRQA39+GfKeUlDVqwD3A1u7R9QVjNTnfdgaZ2yrReiMBpxBw==", "signatures": [{"sig": "MEYCIQD/cOTYxosO/e1uZnrts+6iLeZa1IFqcY97HEfWT3TWkwIhAMqNlg0HHLHqxv6wWYOn3HEU8MVhxV2imp4TnAR+FJkK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrk8w/+Lg3NCi75nMnLWuY8ZImarb8g49ggoA8pqYv0K7reYJFIku9e\r\nqZeDkTT7nY6Ls7GOlM+SmW/ts/l0fXJVQLPNIU8j9GJyyO4Jucjv+nr2Db8A\r\nnSRLNM2LvFkc6lnZG0anEbZ8jUHT8PNYOj3UJghIUXEr3XVdWfQRXaHi1C1U\r\n/vetZH1QtMvPgX2upT+sPndVtMxIhGio1ux8S0ZspJotcNu95YE1LDEor1iA\r\nz+0YZenTBU1ED2Yxg7Y6vyg1/SJO1eXUhyRtnQR3+mTXqoNa4DNooSUN9X0N\r\n042iS5hK40BPBHJWXygLVn+py3pPNyxyvcoS0mTfBGnq/BoAnzO4r57VWXI8\r\n7CFVOJ0OPLBhMreLdOW+ekRkqal9KJzYw2z9edVAgJAASCCvKCL/arwbxtVo\r\nyg1su1E/mzyrVZnk1aq7d/uq7+PgtxAIpzys2RSHAVozxllX+gPgFvGGvJ3O\r\nxjJg6GC7n3rCaCT/WX78vsaVcdZc3Bxqz0fwoZWyJlRV6csfEpMmyuqnhqFL\r\n3ErVGkgtgANBcvQUIGqA82gAhIqnlSixDEOZGIlQ/LkAodH2WzB1ZJ/SvChE\r\nqtCqZdmNgr6bnXOufdIFky4UNdZhNBOM/SMObWuYqyckM2d2wfGWjVHRgzon\r\nfhLDMVp5Z9kP9/aOhIHP4ic3dJGve9obQSM=\r\n=nEMc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.4-esm", "@babel/parser": "^7.21.4-esm", "@babel/generator": "^7.21.4-esm", "@babel/code-frame": "^7.21.4-esm", "@babel/helper-function-name": "^7.21.4-esm", "@babel/helper-hoist-variables": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm", "@babel/helper-split-export-declaration": "^7.21.4-esm"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4-esm.1": {"name": "@babel/traverse", "version": "7.21.4-esm.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f03e81b96e2e750fe3d71dbfc2e508bdffdc859c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4-esm.1.tgz", "fileCount": 62, "integrity": "sha512-nmB5As6yAikcfLIJnjDmWBRWAXg+KVK+md/zODGoPNeT06FcPVafqOlvAdTDM66eR2NiN3VX7mLb/tvuVSICDw==", "signatures": [{"sig": "MEQCIBLmMx/T6J0LowdxZWmp0ibRmJ5dkDEV85aD9jEzhBAZAiBi7f5+IY9sqAL3vYK7ilIeTUvkdAvxCqkf91P7jkxqgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 576602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYCw/8DjEhG5kBKVAzk+Rv56svdk8DyDrXsRdKoYlGiWA6GgNd/zL5\r\nmNGCX+ok+UJxzTnY+Oo4QKXL4cRX9znZSIaFq5YOmpf4SyKyKlCrxBPDwEpN\r\nRS/pTNl2/jPDa5vYQ6gApCu+vDYmPgHBUGJVO//2AOTpJD2/6xS7IdqL23Ya\r\nX4713N0XALH3TsAnt1R2GT7Rl8JdeULj1OgdHsYuBCZTLQcDxMeBnv8/IjgW\r\nAdj3GtVoxQnLYkj9lw7fXPyvu5+iqK75p0brX3zBF0JTrcYo1PJU3OoVLPUg\r\nsdtmV4A3XQ5UEwPLn+8hNM1xc1IX2xVshV6L8w2h8Bl+xEAtVGLwYFiHfray\r\nH7NJCbCiMRZHEzo0HcPamCaoT9XOPhZTrADQLuYNDTRHZkbbNWwhpy6zkniQ\r\nChr6JuY73cT63a/V+oxxFdbib4Gx9qj6ggpvDxEKC0qtD2KkxZkU1zicSSDG\r\nItyYLYYw+j0nzgrZ/VG0g5zhngGWSw8oBZDo/mGy4+cywa0/5/gWaL9WGUNj\r\nUJb2xbHDUPsrYbxMiNGW2vPCluQ0oS+vDHT4Y/EPH7UsxjIFdCXbAONQYUsd\r\npVzTtPG+EMJxyaVEWwpVEolMhwFORXrlFnVRePHfY3knl0JCMx+73FTzzMvV\r\nilIJkBkOEGlSv+uiI4G9XyH8uS8H36dSSgo=\r\n=AuZw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.4-esm.1", "@babel/parser": "^7.21.4-esm.1", "@babel/generator": "^7.21.4-esm.1", "@babel/code-frame": "^7.21.4-esm.1", "@babel/helper-function-name": "^7.21.4-esm.1", "@babel/helper-hoist-variables": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1", "@babel/helper-split-export-declaration": "^7.21.4-esm.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4-esm.2": {"name": "@babel/traverse", "version": "7.21.4-esm.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a25d0cb08f91748b8283ab59af2236d40f6b05dc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4-esm.2.tgz", "fileCount": 61, "integrity": "sha512-OvxktE5eVSH5+2klkkVnGhkNk1qhoPPZiEHtSWDDDObcLqW91gZSN5Yw+9TnEND/6Sdk8rpFL44lGBniY35h5g==", "signatures": [{"sig": "MEQCIAyn2wLdOa+5D7x6/HltfrWuwYCwN3uHdmGr1o5aCNnXAiBb4d5MmWTMbK2yR+6y0w7SGK9cYl4KnJ5M/FVpLLBTrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 576573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou+Q//RPw7EUOL90VidETuauVFBFKYMx8DbWlKaLaBfPNpoGRPk7c5\r\nqszfU35xht1TvjB2op4YD117/l/pucs92/JR7g7nKcUBNfjdOjt/hT3LkzRu\r\ne+kvcJU9m5/kW0Iu2tqSvoUdjlXOzhZqjkKNo327vIgasb5GKUMWUyHOWMP1\r\nx4VmyfHVq8rPgvjmM9ru+YPixETfI4KUsyY/wZDPNz2yD1EI5p85Da7RasN<PERSON>\r\nflE9CUvfrl1mUNFmrzIpnz2xf8AfzOajxT8HbY1K5bphJ1R8wTAcvWSVSEXp\r\nLkdXM7Zt+TfuZ+9TS6aWAi183UZmVZqRZshbn4jYrKQ0vpLQCEA+oDZvgrx9\r\n1jwBnVpW+j5CM4FHz+c0VfqCKnswir5cXm8qZzfJFfFdsSlor3uG9EcwloH1\r\nnHaoH9tk2k5YstrQMZgt6MNlGg0cSCEbRh3TqFw3P0JeArho/Kb5BpVrqoEn\r\n/RBwNGAHRPB6YXeUCLwZQFxYTMgztTlIYQRWClk/M6pGkqfevSRrw/Rtr8jW\r\nlGpXv3vrrb1mPfxs4RZ6D5MuW2uIR7lDfcol6Egi2Eosi0UpwSx4fCnAqxji\r\n8YvCX18KOF3zZnhz6E39791KWFdvqR5foePJqSisKzCWLkHMMux3AjD/fZYZ\r\nXFgBOAbJN0vpeyncrohNvykivzOs608+TVg=\r\n=NNvG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "7.21.4-esm.2", "@babel/parser": "7.21.4-esm.2", "@babel/generator": "7.21.4-esm.2", "@babel/code-frame": "7.21.4-esm.2", "@babel/helper-function-name": "7.21.4-esm.2", "@babel/helper-hoist-variables": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2", "@babel/helper-split-export-declaration": "7.21.4-esm.2"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4-esm.3": {"name": "@babel/traverse", "version": "7.21.4-esm.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "534b907e0f9822dfd17411b873ad31bf5f386248", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4-esm.3.tgz", "fileCount": 61, "integrity": "sha512-C3whrntIkAcHyrKY3Ua/NbDJ49nyaCtgt+SXV1siyiIr/VpdrDRHcTeRsgCNW8XqCOXdKWfRPzVO6yfsws5YLA==", "signatures": [{"sig": "MEUCIDXVoCFhugTWxbtOU8gjgwnMsKaNReJt6Z5v6vZBydDZAiEAq3ateStzNWNEwYQ/FSdfJ4MBwJl3QAsnI3K+aTlsHT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEJw//X/71ktgIZI9wXgUVqDBqDhPX72uw1x/oMeRyDxAKvTSmbRfu\r\nY8+OO1riYqPT/HPNWXyNezymrFH9pk8IUuGJCALRwAY1TbykpP6bozob5uhn\r\n7WG01P6OtuJI4J0z1Mwz/e8oyPPB2gE1xg7EVp19t4RglAB755qXdSdH/wyV\r\n/ILuI0CbmQdpq4sKUuzF35xJBYVAQIHNgJhhuYs/tqPuNgW6nuAqZ3KD31x9\r\nUXbmQDkj6W80RayR0ep5IBaGcfowVqNqR0pmJhjWC3GszFhRjR0MNUIOVRvj\r\nu7mQJLl4t84Pr5dg8vnBsb8dBiFjyDB/rRJbLfuIGxyBiLbzHo3ngrQh1mIk\r\nacRe5axCuqLga8oro3LCtTjfya90wlJIxMSsGDlxzCq96T4qJabEoVfjZzrI\r\njapq24ft7T00GIAcdd+cC5uhD83ZqJvIyLLopkIDtx3tODhpCrqbLVKmXXfJ\r\nlhfbmjnMA05Eypy82MDV3cgyWizQzb3y5r0BDNAZt2YQawIIro1gsGeVyFHr\r\ndmY426AJG9lc+nGZJZAIXFuws5ybo66C2Wz8xYNsqggopKfWWpcFt6en/Z2o\r\nRFyyYIxZ9KxoRbYo/kILdnQl6TM05iMsUZZJDpQL5w4Wa4YAwItZzf2E7Gff\r\ne6BCHx5Zis3xcA+Gqp4p8UHYgDamFmfd1i0=\r\n=57tC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "7.21.4-esm.3", "@babel/parser": "7.21.4-esm.3", "@babel/generator": "7.21.4-esm.3", "@babel/code-frame": "7.21.4-esm.3", "@babel/helper-function-name": "7.21.4-esm.3", "@babel/helper-hoist-variables": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3", "@babel/helper-split-export-declaration": "7.21.4-esm.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.4-esm.4": {"name": "@babel/traverse", "version": "7.21.4-esm.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "694e5b8da685cd4cd54d3634be0aa529785636d6", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.4-esm.4.tgz", "fileCount": 62, "integrity": "sha512-iXxkfeU8PB2PO0782/mygZ+ExpzUsUT0fhSGGBwycpat1u62SxD7zqngxDmQesZDNFtlhUwJbdILMZfLMC3Gww==", "signatures": [{"sig": "MEUCIQDdWPqRj2DHEx2cEIztGkpsM6/J3fSL7xma0N5PaOmBBAIgHuoAMQewr6wxTiDeUKOph4TBnJas1lvXWIahk1eel/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 576593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyhA/5AI3IANBgtx6t8ggO0/EmbyIOsVkTetsnM/bvDVe8/jB4NlFN\r\nyM3O94CTHr+KMhAiYjRAga+ALDTAlTB/ExZ9s05l/4rAgRElLVYsbuAenEBB\r\nTrw0WPP5sAJnAkbMpeQKPIPnuqnIaB+IzGlpmEfwS2mk00n7Y7pSa/8X1/sV\r\npvouNtFFJtrsHl4R0DswZj6ob4/DPTJtC6BzJXRNrxyMi+OJzwMcDOxIRAq/\r\n63qJR5SFovNlSDZykdadjZmTlEV/7m4M+cALbNRD+KtAkuJeCpfKSV4DoMbY\r\nFa535drDRImgvzsHTg0Vh7k26CYOiAc25tTw1Z+L1m/fx6gQxCC6neXIeklZ\r\nF7HGZnneXXRkCjNXjt47LNzcDsyh2WHsy5l1yWI1utM24/Nd+y+1NuGHcuAL\r\nLILM7jX9gD4CEfA2xSoMW68HtwYGHfotaL0HuqUBa2K9lIWpUffz5re1UkaC\r\nTxEMMlvl+EkHjdJprvdAGPjTT09Slp+E2vMscB4NYxve3dMeppZ2/NQMOdLw\r\nyhaG22c0fCSYzO58/tXd5N0cFpLSgzlY9k/PL+W9uHRi0UyJuWsT/byumFaO\r\n46pwK6aFbyzF+7GD+A7o3ZikBTlOS4WZsWiLdcAVE6llp+KHv+qz6S6h78P8\r\nywMGkUDx4h5qcJR0X9HSHdiVAeR8ZrwsMYI=\r\n=N7ff\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "7.21.4-esm.4", "@babel/parser": "7.21.4-esm.4", "@babel/generator": "7.21.4-esm.4", "@babel/code-frame": "7.21.4-esm.4", "@babel/helper-function-name": "7.21.4-esm.4", "@babel/helper-hoist-variables": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4", "@babel/helper-split-export-declaration": "7.21.4-esm.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.21.5": {"name": "@babel/traverse", "version": "7.21.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ad22361d352a5154b498299d523cf72998a4b133", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.21.5.tgz", "fileCount": 61, "integrity": "sha512-AhQoI3YjWi6u/y/ntv7k48mcrCXmus0t79J9qPNlk/lAsFlCiJ047RmbfMOawySTHtywXhbXgpx/8nXMYd+oFw==", "signatures": [{"sig": "MEUCIG4o7s38n1fkr3q2Jl3VNfUBUt5ZxGfJsHPWLkQ/BGO4AiEAsxL/syctK7Bu+6OzlRgAuhTpiKUw3kQUSThI6n/CrNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZDA//XStKpuxC9Yk9Mu0rx/yGnxSWjTbbkpeidgPRSg07B/euS09P\r\ny68hN1b7rDsy3jDRJaf4i5QrwYBs7LFdKLGTsbleCTioptecCSBiXGovqrTQ\r\nKIxxQAxtXxXNClQPI49/vXG1VoU3dpZJFKaYNpXYZH4WZuZombgplInqQLaC\r\nv2gCumJdDYtSGzSG//eoLezflQYyCcsT4n6+S88nEwzEFd5gjyPhW4xKJqHz\r\nd0bEbS79YBO4wqIEgZIyU3SZZ4r+WAY04b3ebo9lxEBoidZxANv7Ld2cIG2t\r\njlS79T1JrW1VfHsuGx4+Wxyab5l2/wMZHwVwOSs70FdNtx3FM5DUXvzd9ex2\r\npwibV0R/US9NfD4CRlUx7r5lkemkBue3lYlcWk3wWHQtVEwQrx/REqc00rRz\r\no8NtOVlY96IMG96EI37cEGYBHx1RGneDsvxmD/ySX9PJ9mKErQtNzE7hc9id\r\n4s35rDSPV5i18z1iISYitDdMhU6MXFvaZL6UJx94vOZc9P2duwqcEo4DktkR\r\nz6XPMogU/BFLKxYjXu+p4b0AAZySegXfDRe3i9zkdr+oAOwDkV2VhVycz426\r\nFxI8BhJ64u2x0oZBcXT+prlQmZ4kfRuWF5kfYsa6voAiz1OtneNZqo40cmpw\r\n201E+ltSVvWQgx11cqxf+w8PocB+YnCyv4Q=\r\n=8cE3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.21.5", "@babel/parser": "^7.21.5", "@babel/generator": "^7.21.5", "@babel/code-frame": "^7.21.4", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.0": {"name": "@babel/traverse", "version": "7.22.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a8b95473bb6ef3900c66683eafa3ca229e806f09", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.0.tgz", "fileCount": 62, "integrity": "sha512-V5Zp3k0nFGWSIC7zYR8PnfdU6i6VYU4JnifdSSMlXM1GMojPAaelPsKmKPW4tWTmpX9GM+RzKl4Io0UVcHVlpw==", "signatures": [{"sig": "MEUCIQD5tCG1zX9ye5q4Pwkm/DnRvuSjp8y440gKFrgWqCMB/QIgFwbhEOid6/TyG2lsg9AnyUY+Pk4wx33x25IaipGLWss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587867}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.0", "@babel/parser": "^7.22.0", "@babel/generator": "^7.22.0", "@babel/code-frame": "^7.21.4", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.1": {"name": "@babel/traverse", "version": "7.22.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "bd22c50b1439cfcfc2fa137b7fdf6c06787456e9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.1.tgz", "fileCount": 61, "integrity": "sha512-lAWkdCoUFnmwLBhIRLciFntGYsIIoC6vIbN8zrLPqBnJmPu7Z6nzqnKd7FsxQUNAvZfVZ0x6KdNvNp8zWIOHSQ==", "signatures": [{"sig": "MEUCICxjNnap1MjE/+FBW2nIo31vGVmBxhcwe1HjU/FYl34nAiEA0HmzYFL0gFo4JkY3L6nz/zTuBfA/7exAvvQjLe0cYUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 589896}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.0", "@babel/parser": "^7.22.0", "@babel/generator": "^7.22.0", "@babel/code-frame": "^7.21.4", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.22.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.4": {"name": "@babel/traverse", "version": "7.22.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c3cf96c5c290bd13b55e29d025274057727664c0", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.4.tgz", "fileCount": 61, "integrity": "sha512-Tn1pDsjIcI+JcLKq1AVlZEr4226gpuAQTsLMorsYg9tuS/kG7nuwwJ4AB8jfQuEgb/COBwR/DqJxmoiYFu5/rQ==", "signatures": [{"sig": "MEUCIQCjEVD2mrxObK+6XhcWhV3HDwBMgQ0RmhHrwP+O7PTN+QIgPD136LEIYPHHK3QZYGEVhrJBDBRlUOn6OSyRmPIFCkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591557}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.4", "@babel/parser": "^7.22.4", "@babel/generator": "^7.22.3", "@babel/code-frame": "^7.21.4", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-environment-visitor": "^7.22.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.18.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.5": {"name": "@babel/traverse", "version": "7.22.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "44bd276690db6f4940fdb84e1cb4abd2f729ccd1", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.5.tgz", "fileCount": 61, "integrity": "sha512-7DuIjPgERaNo6r+PZwItpjCZEa5vyw4eJGufeLxrPdBXBoLcCJCIasvK6pK/9DVNrLZTLFhUGqaC6X/PA007TQ==", "signatures": [{"sig": "MEUCIQCSYf+IVC/fkcXfwti7M65GAGY5tgAUA45VJTSHAgq28QIgRLdeHwTV9ky23uRwpmotA/sb+elLtO46jSp/s+TvGmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 593567}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/generator": "^7.22.5", "@babel/code-frame": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.6": {"name": "@babel/traverse", "version": "7.22.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "8f2f83a5c588251584914<PERSON>beee38f35f661a300", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.6.tgz", "fileCount": 61, "integrity": "sha512-53CijMvKlLIDlOTrdWiHileRddlIiwUIyCKqYa7lYnnPldXCG5dUSN38uT0cA6i7rHWNKJLH0VU/Kxdr1GzB3w==", "signatures": [{"sig": "MEUCIC8GuPdJLVZ8NN/PTPx7bb8Lrf9h2yLZpRprjnEq3kBAAiEA5ArqDNaJPnk7oEs3p3gE198CrAOj6//3B9wPGDvE6iY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 595447}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.5", "@babel/parser": "^7.22.6", "@babel/generator": "^7.22.5", "@babel/code-frame": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.7": {"name": "@babel/traverse", "version": "7.22.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "68a5513f3c6b88c7b5f5825d0720fb43e8a31826", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.7.tgz", "fileCount": 61, "integrity": "sha512-vQn61YQzktf1wFNzCka2dynnnbmBpUDeUCds3Y+FBHZpcVxpBq0XscQGDDVN7sV2Vf1pZDY1HmPR3U/5t7VfMQ==", "signatures": [{"sig": "MEYCIQD0Pqpwf6CCCAYGCE3UtfxUF0rI07Er0sV6nnLitcHniwIhAIVyCqtgH3A3gXi/zEdo1m7kKnR8jcL0pWu5OomkNm0d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600253}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.5", "@babel/parser": "^7.22.7", "@babel/generator": "^7.22.7", "@babel/code-frame": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.7", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.8": {"name": "@babel/traverse", "version": "7.22.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4d4451d31bc34efeae01eac222b514a77aa4000e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.8.tgz", "fileCount": 61, "integrity": "sha512-y6LPR+wpM2I3qJrsheCTwhIinzkETbplIgPBbwvqPKc+uljeA5gP+3nP8irdYt1mjQaDnlIcG+dw8OjAco4GXw==", "signatures": [{"sig": "MEQCIDeP9JYSXfJL2c5nkUzLcXWlb35gGL+ebNzbysPnnoCxAiBQl/8cqUVi2NcGC6t50yfsmieXeTjHIGRbXjvj7ysqHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596907}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.5", "@babel/parser": "^7.22.7", "@babel/generator": "^7.22.7", "@babel/code-frame": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.0": {"name": "@babel/traverse", "version": "8.0.0-alpha.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "16094c541dce6f6d68dc28c731d4f3e9d9192d0c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.0.tgz", "fileCount": 61, "integrity": "sha512-6l7x2whZ4ROJ4CFqgqUL8JANf9ENevn6ozhrrDb/jgxjdgWwl5Jzj1rtA984BwyFSkCH4cgQIWzahc0sYgR5pg==", "signatures": [{"sig": "MEYCIQDzMTlljrXp3BAwwC8LL+4D9ZcXlJnsmDSs3LrJtTz78gIhAIXKw5MOVx0gY7kbas51s6IAaqTyktDKHUJaUnW561mC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1160816}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.0", "@babel/parser": "^8.0.0-alpha.0", "@babel/generator": "^8.0.0-alpha.0", "@babel/code-frame": "^8.0.0-alpha.0", "@babel/helper-function-name": "^8.0.0-alpha.0", "@babel/helper-hoist-variables": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/helper-split-export-declaration": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.1": {"name": "@babel/traverse", "version": "8.0.0-alpha.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b0db0e577ba316935eb82819f0b3b246801dab45", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.1.tgz", "fileCount": 61, "integrity": "sha512-dpB4kxhCG8P3V6Za5pepWjOK/8Whhh6MMWXMcKzFZjOofu8cvpFpC9sonibkXWV+yNzKB2u84cCy431OCxHu9Q==", "signatures": [{"sig": "MEQCIFr/hNzc5PqkKFRLUstxWig690M/IVLGZMJsvbTPY1GxAiAzZIQHrCwmQ/x9/IOJCWl/N3pQOK719JibbMrKdEd5Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1160816}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.1", "@babel/parser": "^8.0.0-alpha.1", "@babel/generator": "^8.0.0-alpha.1", "@babel/code-frame": "^8.0.0-alpha.1", "@babel/helper-function-name": "^8.0.0-alpha.1", "@babel/helper-hoist-variables": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/helper-split-export-declaration": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.10": {"name": "@babel/traverse", "version": "7.22.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "20252acb240e746d27c2e82b4484f199cf8141aa", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.10.tgz", "fileCount": 61, "integrity": "sha512-Q/urqV4pRByiNNpb/f5OSv28ZlGJiFiiTh+GAHktbIrkPhPbl90+uW6SmpoLyZqutrg9AEaEf3Q/ZBRHBXgxig==", "signatures": [{"sig": "MEQCIHtUVXW2JZDifR95kRDiCcD3lLjxYFEtlDxchuSQPwSJAiBAz9gHLJNyugQa0CjEBPspb8xLCEikJbPH+LCy2DA89w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602271}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.10", "@babel/parser": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/code-frame": "^7.22.10", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.2": {"name": "@babel/traverse", "version": "8.0.0-alpha.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "cf06d438c5ca6249d32796fa0683e9510c34076a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.2.tgz", "fileCount": 61, "integrity": "sha512-AtZpwWO8DPgYz922sHSlNxu94nmUU8kVNg/GCJFuL1zg1Ad7jXaUUzMW573Qn6c7JG4Gd2p3dDfuGc3z0/imoQ==", "signatures": [{"sig": "MEUCIQCZY7ZAKLIkMSM4KwFqCB89Gk2Q2hSTnsZsY4Ot8ABC8wIgIG1KwXC5uQ869L771fIPTce2nYCTo+jYqyiYp8cZrc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1168160}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.2", "@babel/parser": "^8.0.0-alpha.2", "@babel/generator": "^8.0.0-alpha.2", "@babel/code-frame": "^8.0.0-alpha.2", "@babel/helper-function-name": "^8.0.0-alpha.2", "@babel/helper-hoist-variables": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-split-export-declaration": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.11": {"name": "@babel/traverse", "version": "7.22.11", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "71ebb3af7a05ff97280b83f05f8865ac94b2027c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.11.tgz", "fileCount": 61, "integrity": "sha512-mzAenteTfomcB7mfPtyi+4oe5BZ6MXxWcn4CX+h4IRJ+OOGXBrWU6jDQavkQI9Vuc5P+donFabBfFCcmWka9lQ==", "signatures": [{"sig": "MEYCIQDZ+TjG6SjxrfeDeHIcRHYPDJ9NA3bHSYgxLU6bNMSWzQIhAI/AeFlhZCxWNjzewQfhzfJleJslqZtrK7n65Crx9HBd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 601597}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.11", "@babel/parser": "^7.22.11", "@babel/generator": "^7.22.10", "@babel/code-frame": "^7.22.10", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.15": {"name": "@babel/traverse", "version": "7.22.15", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "75be4d2d6e216e880e93017f4e2389aeb77ef2d9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.15.tgz", "fileCount": 61, "integrity": "sha512-DdHPwvJY0sEeN4xJU5uRLmZjgMMDIvMPniLuYzUVXj/GGzysPl0/fwt44JBkyUIzGJPV8QgHMcQdQ34XFuKTYQ==", "signatures": [{"sig": "MEQCIHCXeaR6RFEujPm7beG5t1+QRgB8Ea/6eMCe9yy3foY0AiBxuvAgPp1vp/32O9KLGDjJMEwwXL7dUSC6EZklrGoHbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602227}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.15", "@babel/parser": "^7.22.15", "@babel/generator": "^7.22.15", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.17": {"name": "@babel/traverse", "version": "7.22.17", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b23c203ab3707e3be816043081b4a994fcacec44", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.17.tgz", "fileCount": 61, "integrity": "sha512-xK4Uwm0JnAMvxYZxOVecss85WxTEIbTa7bnGyf/+EgCL5Zt3U7htUpEOWv9detPlamGKuRzCqw74xVglDWpPdg==", "signatures": [{"sig": "MEQCIG/5KlzS3dHse+ZAute5om5mtVx+/U0u6U2Mjzy0OG4LAiAyN3elYqeoeKrTcmfZ1Dia1aS8l+uvsLbiPYQLGI15uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602690}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.17", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.17", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.18": {"name": "@babel/traverse", "version": "7.22.18", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "83bcd5fac33db117ce1d93a8d2a1906281e4812e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.18.tgz", "fileCount": 61, "integrity": "sha512-gkIzxRqYbe5Qc4Dkm+oatEsS1AnuXk6uR3Rl6Y7m+T9ug+vbhQ7qCTv/DkLsNXp4sQdH/83AhxftEPI8+U9f/w==", "signatures": [{"sig": "MEUCIQDMrGTKNa9yYmEJBg0W7wwmnhFTJ+ynd3aFFARlbS7ZtAIgJw7ETL4j8bw06S5fauTb9HHQ7CAcU045zdluW2A3fyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602227}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.18", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.18", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.19": {"name": "@babel/traverse", "version": "7.22.19", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "bb2b12b7de9d7fec9e812ed89eea097b941954f8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.19.tgz", "fileCount": 61, "integrity": "sha512-ZCcpVPK64krfdScRbpxF6xA5fz7IOsfMwx1tcACvCzt6JY+0aHkBk7eIU8FRDSZRU5Zei6Z4JfgAxN1bqXGECg==", "signatures": [{"sig": "MEYCIQDR3ixYFCEK4eTIyO+wTCy1DzpApEVfqHmi5EgeOis+4QIhAKHgXxJwXm2I1jtQ6y0zLdq2ALbyRMlwa2Xos8mp6dQ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602227}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.19", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.19", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.22.20": {"name": "@babel/traverse", "version": "7.22.20", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "db572d9cb5c79e02d83e5618b82f6991c07584c9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.22.20.tgz", "fileCount": 61, "integrity": "sha512-eU260mPZbU7mZ0N+X10pxXhQFMGTeLb9eFS0mxehS8HZp9o1uSnFeWQuG1UPrlxgA7QoUzFhOnilHDp0AXCyHw==", "signatures": [{"sig": "MEUCIA8FRGceK3n3+z/7/vuBopkQvO0XY4ywqbKK4/BLTzogAiEAwin1bBoMx1sZr56gwIlICELhyekPzfUY6aLOU/Yost8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602102}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.22.19", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.22.20", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.0": {"name": "@babel/traverse", "version": "7.23.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "18196ddfbcf4ccea324b7f6d3ada00d8c5a99c53", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.0.tgz", "fileCount": 61, "integrity": "sha512-t/QaEvyIoIkwzpiZ7aoSKK8kObQYeF7T2v+dazAYCb8SXtp58zEVkWW7zAnju8FNKNdr4ScAOEDmMItbyOmEYw==", "signatures": [{"sig": "MEUCIACPYgrLrKK07lc4cSOyPCRjwEmfpdPF098MHbkdjCgrAiEAnbHcKSDnEIA2iDH50E1aD2bXQVVy4kejIeGzKiEEOjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 603312}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/generator": "^7.23.0", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.3": {"name": "@babel/traverse", "version": "8.0.0-alpha.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9fcd4940579a3e02c693c00efdd120279abd3dfc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-9YEqwAnH6AgeBzMvWCmMGVYpm/thlsWVyWuNEm4T0Xw3Y7toOHaeLHpDWSyhZvQAcfZZoOWqjhdvjiYjKkHgLA==", "signatures": [{"sig": "MEYCIQCfi3gwU+MExKI4QukW5YGPGo3gvHswltUf6yoAa22mbgIhALlqOjDE74swO1317ni281pkUPMcxzah+b4O3RGZjIJ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596344}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.3", "@babel/parser": "^8.0.0-alpha.3", "@babel/generator": "^8.0.0-alpha.3", "@babel/code-frame": "^8.0.0-alpha.3", "@babel/helper-function-name": "^8.0.0-alpha.3", "@babel/helper-hoist-variables": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-split-export-declaration": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.2": {"name": "@babel/traverse", "version": "7.23.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "329c7a06735e144a506bdb2cad0268b7f46f4ad8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.2.tgz", "fileCount": 61, "integrity": "sha512-azpe59SQ48qG6nu2CzcMLbxUudtN+dOM9kDbUqGq3HXUJRlo7i8fvPoxQUzYgLZ4cMVmuZgm8vvBpNeRhd6XSw==", "signatures": [{"sig": "MEUCIQD+1zNr7k5cTnZctrE1qGvumcKgnvUyriAgHEr7hwaE7gIgL2jEl/gspb5cwOSFpHEH6FZbmurNgqFH8d1cgg2BDT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 603640}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/generator": "^7.23.0", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.4": {"name": "@babel/traverse", "version": "8.0.0-alpha.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "53e338b0e44adef37c02581ecdaa70932be19e59", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Y73Sn9Mdeayk5Y0yH2a0hCdUYrclsItmlDbW/YEfFfBA8RJIGLTm8Lm6PnKiun/M8nmiDCpxRTA9MVqa3Mp5rQ==", "signatures": [{"sig": "MEYCIQCOp+tV/eQVDJpJ8vtOlEucFyKO/ZNl/+jlHIPb8ur2vwIhAM8tPoHA/KnqK9FacayyaMJDiDPK6aUC0RDDJZXvYJYw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596688}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.4", "@babel/parser": "^8.0.0-alpha.4", "@babel/generator": "^8.0.0-alpha.4", "@babel/code-frame": "^8.0.0-alpha.4", "@babel/helper-function-name": "^8.0.0-alpha.4", "@babel/helper-hoist-variables": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-split-export-declaration": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.3": {"name": "@babel/traverse", "version": "7.23.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "26ee5f252e725aa7aca3474aa5b324eaf7908b5b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.3.tgz", "fileCount": 61, "integrity": "sha512-+K0yF1/9yR0oHdE0StHuEj3uTPzwwbrLGfNOndVJVV2TqA5+j3oljJUb4nmB954FLGjNem976+B+eDuLIjesiQ==", "signatures": [{"sig": "MEQCIAILUixxMNf9G0wCq1zgKwoUeVj+fqtE6rZNvUrIDyJAAiAksRHoTjiijUQ97UP1og6lqXzyqArnsRHmbVj8cIYjgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610296}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.23.3", "@babel/parser": "^7.23.3", "@babel/generator": "^7.23.3", "@babel/code-frame": "^7.22.13", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.4": {"name": "@babel/traverse", "version": "7.23.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c2790f7edf106d059a0098770fe70801417f3f85", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.4.tgz", "fileCount": 61, "integrity": "sha512-IYM8wSUwunWTB6tFC2dkKZhxbIjHoWemdK+3f8/wq8aKhbUscxD5MX72ubd90fxvFknaLPeGw5ycU84V1obHJg==", "signatures": [{"sig": "MEYCIQDRxW9nC2ncyz4Ue2FEDHgQUOUZeuut0tDpG9IeKmAeOAIhAOlLaLwhkSmEi/3mjrz989q5sxr9ItgBU8DWyYiBqWg1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610307}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.23.4", "@babel/parser": "^7.23.4", "@babel/generator": "^7.23.4", "@babel/code-frame": "^7.23.4", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.5": {"name": "@babel/traverse", "version": "7.23.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f546bf9aba9ef2b042c0e00d245990c15508e7ec", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.5.tgz", "fileCount": 61, "integrity": "sha512-czx7Xy5a6sapWWRx61m1Ke1Ra4vczu1mCTtJam5zRTBOonfdJ+S/B6HYmGYu3fJtr8GGET3si6IhgWVBhJ/m8w==", "signatures": [{"sig": "MEUCIQDvc7cii7nvI2X4tyFzOGZ2HQuH/TYY3mbAR1VxKdq2zwIgNHj30kg78mHf3pmSCRwMSP8so41wG7SZHDPWWDkgnjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610416}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.1.0", "globals": "^11.1.0", "@babel/types": "^7.23.5", "@babel/parser": "^7.23.5", "@babel/generator": "^7.23.5", "@babel/code-frame": "^7.23.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.6": {"name": "@babel/traverse", "version": "7.23.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b53526a2367a0dd6edc423637f3d2d0f2521abc5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.6.tgz", "fileCount": 61, "integrity": "sha512-czastdK1e8YByZqezMPFiZ8ahwVMh/ESl9vPgvgdB9AmFMGP5jfpFax74AQgl5zj4XHzqeYAg2l8PuUeRS1MgQ==", "signatures": [{"sig": "MEUCIQCMrHf/71DRyysceynCeHhji6TC9sw7Cb0kiEbtmpFhNQIgceVk+LTdv7pwKL0mr+B8u7EzsznK1+mlHit0A9jLrnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610416}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.23.6", "@babel/parser": "^7.23.6", "@babel/generator": "^7.23.6", "@babel/code-frame": "^7.23.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.5": {"name": "@babel/traverse", "version": "8.0.0-alpha.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "eec7edf8f9dd174a275c39d02e7531b1722ecf5b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-tSI1UuBxlvTEsw6+pDpCdRnFJOcvPd5NoKhH1NUgJJcmBSEQq9KiOPRIN4/0cqdcobTgA5PBL9qk/i3hIPoV8A==", "signatures": [{"sig": "MEUCIBqvTSt2GkxPlEzLu+OXkwH6FIT9y4aHIG/sAdf2YE90AiEA9B7Vr15ZiY5ad/W1dnOhIWQKM97TvZJqLgicFbZdqYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 604237}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.5", "@babel/parser": "^8.0.0-alpha.5", "@babel/generator": "^8.0.0-alpha.5", "@babel/code-frame": "^8.0.0-alpha.5", "@babel/helper-function-name": "^8.0.0-alpha.5", "@babel/helper-hoist-variables": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-split-export-declaration": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.7": {"name": "@babel/traverse", "version": "7.23.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "9a7bf285c928cb99b5ead19c3b1ce5b310c9c305", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.7.tgz", "fileCount": 61, "integrity": "sha512-tY3mM8rH9jM0YHFGyfC0/xf+SB5eKUu7HPj7/k3fpi9dAlsMc5YbQvDi0Sh2QTPXqMhyaAtzAr807TIyfQrmyg==", "signatures": [{"sig": "MEUCIDxwVxBG2wLvbCcIUZ1mWh5DewmZBnx7rjOdaUDjOL0dAiEAplEVIgTBhRt2GWv/pMSmzrwBZLEOZxVlN/RdB2vQc5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610525}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.23.6", "@babel/parser": "^7.23.6", "@babel/generator": "^7.23.6", "@babel/code-frame": "^7.23.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.23.9": {"name": "@babel/traverse", "version": "7.23.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2f9d6aead6b564669394c5ce0f9302bb65b9d950", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.23.9.tgz", "fileCount": 61, "integrity": "sha512-I/4UJ9vs90OkBtY6iiiTORVMyIhJ4kAVmsKo9KFc8UOxMeUfi2hvtIBsET5u9GizXE6/GFSuKCTNfgCswuEjRg==", "signatures": [{"sig": "MEUCIQDAShdZckH8y6v/gnBgyI1cKyhkxXvPnyvbyUm6DLQBAAIgHCjoSxj6lgRA/p3znKwga4yuO+F44CEq/VSMD2GpRKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610748}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.23.9", "@babel/parser": "^7.23.9", "@babel/generator": "^7.23.6", "@babel/code-frame": "^7.23.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.6": {"name": "@babel/traverse", "version": "8.0.0-alpha.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0ab8594d77ae975b2c37e83fbdc28693f8e70c2b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-EjUMbnUrSRyi9+h5GRhJwvkkwEZRzIKhe/dk6NaT6kHt8aCPcGKUf7ZilESHPtCUvI8jgx5ONDQWqxVlRoaqgw==", "signatures": [{"sig": "MEUCICR4VBwo/X2QLVUws7Qwmp/blsGxiYtMEy+YG0pyx9/HAiEAzOUPM+TUPO8mtKNUIvXfmZTPdM/U64DjUjDNuB0lusQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 604494}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.6", "@babel/parser": "^8.0.0-alpha.6", "@babel/generator": "^8.0.0-alpha.6", "@babel/code-frame": "^8.0.0-alpha.6", "@babel/helper-function-name": "^8.0.0-alpha.6", "@babel/helper-hoist-variables": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-split-export-declaration": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.0": {"name": "@babel/traverse", "version": "7.24.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4a408fbf364ff73135c714a2ab46a5eab2831b1e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.0.tgz", "fileCount": 61, "integrity": "sha512-HfuJlI8qq3dEDmNU5ChzzpZRWq+oxCZQyMzIMEqLho+AQnhMnKQUzH6ydo3RBl/YjPCuk68Y6s0Gx0AeyULiWw==", "signatures": [{"sig": "MEQCIEHu2cHXDrqDM3NTey8NP3SsMy/EE3b6rWnGYTQ7QvgvAiBbi26U1JUmMM3pKGmzDqP23jRG7CKbDjOROALjZzMqVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 611044}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.0", "@babel/parser": "^7.24.0", "@babel/generator": "^7.23.6", "@babel/code-frame": "^7.23.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.7": {"name": "@babel/traverse", "version": "8.0.0-alpha.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "42d8b63f335568fbe8f225b84f485938bed0a60e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-ttcHW30R+e9kYwmmtBQecQBOSa6HaTxBm2VYkoOVn86md0ClyNydS/FqiIkTB3O7QOQHcLSlc0BSYTtHfq9nKg==", "signatures": [{"sig": "MEYCIQD8rR3zXmUsF6j/R3kpiinh0Cjl9FXKXHFTFOZ13uXsuQIhAJL4tkW3BcmwD+MCSdHnFfOlLP1szk4qUxhSCM9vXZRU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 604811}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.7", "@babel/parser": "^8.0.0-alpha.7", "@babel/generator": "^8.0.0-alpha.7", "@babel/code-frame": "^8.0.0-alpha.7", "@babel/helper-function-name": "^8.0.0-alpha.7", "@babel/helper-hoist-variables": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-split-export-declaration": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.1": {"name": "@babel/traverse", "version": "7.24.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d65c36ac9dd17282175d1e4a3c49d5b7988f530c", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.1.tgz", "fileCount": 61, "integrity": "sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==", "signatures": [{"sig": "MEQCIEqExoRwds93qF2QWED3Xj8wewVQCJT6BoQmEZGIvY19AiA0jOUSARZJ73TlnyWM7EBHyXiuSCU+mSunaP9ORdpYvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 615031}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.0", "@babel/parser": "^7.24.1", "@babel/generator": "^7.24.1", "@babel/code-frame": "^7.24.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.8": {"name": "@babel/traverse", "version": "8.0.0-alpha.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f7dba3437fc717adbbb55da2feccb50ddba6073a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Lwwh1mNMks5PXF2zqHLKYu01tA00ClZj/AV7W/cCGuO+UwXZ/KaiFWsboTkWEzRqk6RviAAEdxtnq3t/AGlr2g==", "signatures": [{"sig": "MEUCIBrkYowgM17HEUUxDzaaioeSXaUkG6SVKvFfpuoW6LGOAiEA7bfKhHQc27ChBYTWBnqTRP+kbXhtsBRhfDJtksqsl1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608422}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.8", "@babel/parser": "^8.0.0-alpha.8", "@babel/generator": "^8.0.0-alpha.8", "@babel/code-frame": "^8.0.0-alpha.8", "@babel/helper-function-name": "^8.0.0-alpha.8", "@babel/helper-hoist-variables": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-split-export-declaration": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.5": {"name": "@babel/traverse", "version": "7.24.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "972aa0bc45f16983bf64aa1f877b2dd0eea7e6f8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.5.tgz", "fileCount": 61, "integrity": "sha512-7aaBLeDQ4zYcUFDUD41lJc1fG8+5IU9DaNSJAgal866FGvmD5EbWQgnEC6kO1gGLsX0esNkfnJSndbTXA3r7UA==", "signatures": [{"sig": "MEUCIGkOMUVGgtrdnfGSBz0GIscdolXyOmCp7w3km6p7wMvOAiEA8DFhaAbA3H+sK9Vv0AY8HSNP+QnXcO5nHHpmNPUkM2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 618154}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.5", "@babel/parser": "^7.24.5", "@babel/generator": "^7.24.5", "@babel/code-frame": "^7.24.2", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-split-export-declaration": "^7.24.5"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.6": {"name": "@babel/traverse", "version": "7.24.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0941ec50cdeaeacad0911eb67ae227a4f8424edc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.6.tgz", "fileCount": 61, "integrity": "sha512-OsNjaJwT9Zn8ozxcfoBc+RaHdj3gFmCmYoQLUII1o6ZrUwku0BMg80FoOTPx+Gi6XhcQxAYE4xyjPTo4SxEQqw==", "signatures": [{"sig": "MEUCIQCAb7jJ7wbI7IE7dawpNrI838N00avYSUIjBCM2NHVTngIgW7HWywXBwqIUtZSdBVj2lw4QVpAf58QzBHKoMZGkPN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634330}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.6", "@babel/parser": "^7.24.6", "@babel/generator": "^7.24.6", "@babel/code-frame": "^7.24.6", "@babel/helper-function-name": "^7.24.6", "@babel/helper-hoist-variables": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6", "@babel/helper-split-export-declaration": "^7.24.6"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.9": {"name": "@babel/traverse", "version": "8.0.0-alpha.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "c8b5eec7e4f0a07ec2cdb00d17981e88d88825d4", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-4dKrBETJgtJN8aqZisedkdB5WmlA5MPLHuglzWNPZIOGH4HDIZmSZCwcIYp77cMRvXZ4F8f2QzE0yhdkzCaGUA==", "signatures": [{"sig": "MEYCIQC4gFGMdokmoDlRYet01Gzvspypn5/UKmgmwCkbZqF6CgIhAO7r9GgJ8J13bkpu3sdU07j8Ql5t0Nw7dWslKCNEXnr/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 788760}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.9", "@babel/parser": "^8.0.0-alpha.9", "@babel/generator": "^8.0.0-alpha.9", "@babel/code-frame": "^8.0.0-alpha.9", "@babel/helper-function-name": "^8.0.0-alpha.9", "@babel/helper-hoist-variables": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-split-export-declaration": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.10": {"name": "@babel/traverse", "version": "8.0.0-alpha.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ab6511383afaa8e3c3169d01ff6c17c879b4b129", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-tpV2EM8nR3YVuXh/ygPLwCa06W2B76Uy7gvd8Fi0JQdDKdzxLAN6CEZlXRBx/HiHo3X5eOX3T39B97YnCTeCBg==", "signatures": [{"sig": "MEQCIBp20BgEleGA8iq3QKiQtOaoEC6nEQbVJ6fjHGMKOijFAiAomtyi+OdOmKEcyBwfa3tXfj1McwkdT1TtirGKgvhlUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 788850}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.10", "@babel/parser": "^8.0.0-alpha.10", "@babel/generator": "^8.0.0-alpha.10", "@babel/code-frame": "^8.0.0-alpha.10", "@babel/helper-function-name": "^8.0.0-alpha.10", "@babel/helper-hoist-variables": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-split-export-declaration": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.7": {"name": "@babel/traverse", "version": "7.24.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "de2b900163fa741721ba382163fe46a936c40cf5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.7.tgz", "fileCount": 61, "integrity": "sha512-yb65Ed5S/QAcewNPh0nZczy9JdYXkkAbIsEo+P7BE7yO3txAY30Y/oPa3QkQ5It3xVG2kpKMg9MsdxZaO31uKA==", "signatures": [{"sig": "MEYCIQCPQ6TbCVyGT+HCmkL1N+tfw8Y8X93VMeFiN8p0uIrVawIhAIEeNMRIc86qTrFRPE9Tp6r0J7q6FdMF1uDu3QamCwAr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634452}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.7", "@babel/parser": "^7.24.7", "@babel/generator": "^7.24.7", "@babel/code-frame": "^7.24.7", "@babel/helper-function-name": "^7.24.7", "@babel/helper-hoist-variables": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.11": {"name": "@babel/traverse", "version": "8.0.0-alpha.11", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2bfcdc14ba2cbd4355f9d96c4c4b873a53495807", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-LUiN74zmys4gGogS5OAAB1w03By4zunqtqbUEDkHMtSKCm/ADcgLEktKqzjpiprrdnfJLG+ojZcfpQKUHqkmag==", "signatures": [{"sig": "MEUCIQCTeLEMwE8Xwnh/Dszdu64Eyq+fwNmGb55MuoQeVdA2GgIgWsTJ8BqwkO0lV/Bn2N+eDdAAf9cUeg6mjfQ9y+jMGbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 788819}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^13.5.0", "@babel/types": "^8.0.0-alpha.11", "@babel/parser": "^8.0.0-alpha.11", "@babel/generator": "^8.0.0-alpha.11", "@babel/code-frame": "^8.0.0-alpha.11", "@babel/helper-function-name": "^8.0.0-alpha.11", "@babel/helper-hoist-variables": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-split-export-declaration": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.24.8": {"name": "@babel/traverse", "version": "7.24.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "6c14ed5232b7549df3371d820fbd9abfcd7dfab7", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.24.8.tgz", "fileCount": 61, "integrity": "sha512-t0P1xxAPzEDcEPmjprAQq19NWum4K0EQPjMwZQZbHt+GiZqvjCHjj755Weq1YRPVzBI+3zSfvScfpnuIecVFJQ==", "signatures": [{"sig": "MEUCIBR3EXwYDCmoOvs5QW21C7j/3EFpkd5H4i2c++3Yyi/iAiEAmOyohS8wYfo8E3o+FQc+Ofj3Hls/Jiww3JmuXeEDGL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 636905}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.24.8", "@babel/parser": "^7.24.8", "@babel/generator": "^7.24.8", "@babel/code-frame": "^7.24.7", "@babel/helper-function-name": "^7.24.7", "@babel/helper-hoist-variables": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.0": {"name": "@babel/traverse", "version": "7.25.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "e8533c0a57ed97921d1e5b20dd3db63d3efaebf5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.0.tgz", "fileCount": 61, "integrity": "sha512-ubALThHQy4GCf6mbb+5ZRNmLLCI7bJ3f8Q6LHBSRlSKSWj5a7dSUzJBLv3VuIhFrFPgjF4IzPF567YG/HSCdZA==", "signatures": [{"sig": "MEUCIAyfYhEnEv2t11PJMFetYSxkYdUotCwthzeVlQ3Ms6EhAiEA47QFKhGiyzAMtp33Fk2rMmc5BZirMHtX3IsaLP9krQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 663940}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.0", "@babel/parser": "^7.25.0", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.0", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.12": {"name": "@babel/traverse", "version": "8.0.0-alpha.12", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ff90069ea160b665ba53846bbb3974387e890aec", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-G56OmBNFaBsBvDIK8UFCKJfxLYoEiZy+keSTex0QlZxIyxYGM1SfgUZ3LpHn5lXNvoK3Bdqkll8ekTfdEnLgAQ==", "signatures": [{"sig": "MEQCIBvuPeBAA+tK5e5pwD/PitRfGXuSFmJoGRPfIVpih2H/AiAPQeFcI69yFomgUZc/ram+cod7NP/PgIZNNaIa5mB08g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 809514}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.6.0", "@babel/types": "^8.0.0-alpha.12", "@babel/parser": "^8.0.0-alpha.12", "@babel/template": "^8.0.0-alpha.12", "@babel/generator": "^8.0.0-alpha.12", "@babel/code-frame": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.1": {"name": "@babel/traverse", "version": "7.25.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "64dbc31effc5f3fa3cf10d19df0e6310214743f5", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.1.tgz", "fileCount": 61, "integrity": "sha512-LrHHoWq08ZpmmFqBAzN+hUdWwy5zt7FGa/hVwMcOqW6OVtwqaoD5utfuGYU87JYxdZgLUvktAsn37j/sYR9siA==", "signatures": [{"sig": "MEYCIQDU7SKoflwX/NLl7IRUobnwI7XyB7az0D14WjJjBDs21QIhAMpfdXXiXr+U6vfrbzxxTkyYcQ2TRYYvkYeTMGgUS2cP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 663942}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.0", "@babel/parser": "^7.25.0", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.0", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.2": {"name": "@babel/traverse", "version": "7.25.2", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1a0a4aef53177bead359ccd0c89f4426c805b2ae", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.2.tgz", "fileCount": 61, "integrity": "sha512-s4/r+a7xTnny2O6FcZzqgT6nE4/GHEdcqj4qAeglbUOh0TeglEfmNJFAd/OLoVtGd6ZhAO8GCVvCNUO5t/VJVQ==", "signatures": [{"sig": "MEYCIQCQbIh0vHffVxFeVM8al/wM0qmcQzlaf8YvvVdEJMqs1QIhAOosmIkauXUX4vm+V8bicvN0JhjpHc6B97BmWfOrBRfG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 665860}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.2", "@babel/parser": "^7.25.0", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.0", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.3": {"name": "@babel/traverse", "version": "7.25.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "f1b901951c83eda2f3e29450ce92743783373490", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.3.tgz", "fileCount": 61, "integrity": "sha512-HefgyP1x754oGCsKmV5reSmtV7IXj/kpaE1XYY+D9G5PvKKoFfSbiS4M77MdjuwlZKDIKFCffq9rPU+H/s3ZdQ==", "signatures": [{"sig": "MEYCIQCT4Z2POOw7aAS3+dGqkBdpi7LT9Uw8Qtsl/s4SRyT46gIhALlLOy+v+DwwtJUbcNGVm2C3bt8WzdGxxV8GPLb4HMcb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 666629}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.2", "@babel/parser": "^7.25.3", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.0", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.4": {"name": "@babel/traverse", "version": "7.25.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "648678046990f2957407e3086e97044f13c3e18e", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.4.tgz", "fileCount": 63, "integrity": "sha512-VJ4XsrD+nOvlXyLzmLzUs/0qjFS4sK30te5yEFlvbbUNEgKaVb2BHZUpAL+ttLPQAHNrsI3zZisbfha5Cvr8vg==", "signatures": [{"sig": "MEQCIGFeIJjE1PZNpm6CS7p0aGnbUXjQo7F+OwcpMTTqr4jPAiBjfAtqZZ0aK1zuMa2s6OVgwqV+/oYqrOpe3NRApfjBxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727552}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.4", "@babel/parser": "^7.25.4", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.4", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.6": {"name": "@babel/traverse", "version": "7.25.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "04fad980e444f182ecf1520504941940a90fea41", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.6.tgz", "fileCount": 63, "integrity": "sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==", "signatures": [{"sig": "MEYCIQDbMwiBLOkm94VQVGo1fF/3wUB0oGBzUzKs5K4vIDJ8vwIhAI6OvEbDnrelYOvH/Pfauf6YlfNEme89UrQReog/EsPL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 730693}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.6", "@babel/parser": "^7.25.6", "@babel/template": "^7.25.0", "@babel/generator": "^7.25.6", "@babel/code-frame": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.7": {"name": "@babel/traverse", "version": "7.25.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "83e367619be1cab8e4f2892ef30ba04c26a40fa8", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.7.tgz", "fileCount": 63, "integrity": "sha512-jatJPT1Zjqvh/1FyJs6qAHL+Dzb7sTb+xr7Q+gM1b+1oBsMsQQ4FkVKb6dFlJvLlVssqkRzV05Jzervt9yhnzg==", "signatures": [{"sig": "MEUCIQDXWC8xt/5HvvxZBqwP8GVeiKOlUxg/lYSESDDHdnltOQIgbo3ig0w0QR0SUdPBaDk5KtQrJ2by0cMsaB+mlrMUsMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 740293}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.7", "@babel/parser": "^7.25.7", "@babel/template": "^7.25.7", "@babel/generator": "^7.25.7", "@babel/code-frame": "^7.25.7"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.25.9": {"name": "@babel/traverse", "version": "7.25.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "a50f8fe49e7f69f53de5bea7e413cd35c5e13c84", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.25.9.tgz", "fileCount": 61, "integrity": "sha512-ZCuvfwOwlz/bawvAuvcj8rrithP2/N55Tzz342AkTvq4qaWbGfmCk/tKhNaV2cthijKrPAA8SRJV5WWe7IBMJw==", "signatures": [{"sig": "MEQCIGC0wxO6RECJFN2L8VwIXRQKXlFT/Iyp91dNSrkYd340AiAqAfvN8qSKAHloxSUMN3kUo8/TTHh6qpnXFJChzi7oWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 672429}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/template": "^7.25.9", "@babel/generator": "^7.25.9", "@babel/code-frame": "^7.25.9"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.13": {"name": "@babel/traverse", "version": "8.0.0-alpha.13", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "701a4cdae440c9989806390e1f9c0cf0a4777d2a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-eq35clOTKgbC+Zb92YhjXNFa3QWjaNxKrxwSbJQ7CEYtO34SycgnHdqRLoA6mikzfu4feDt8SHclkGh39jXUTw==", "signatures": [{"sig": "MEYCIQCu5NpY4NAfo41UGofz9mLrR7n+RhAf4/lTNYJ67iCZZQIhAPHzFM5wKxKcOI4/adLaXUiE9djFwZfeMlqsIWBBbf1S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 783464}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-alpha.13", "@babel/parser": "^8.0.0-alpha.13", "@babel/template": "^8.0.0-alpha.13", "@babel/generator": "^8.0.0-alpha.13", "@babel/code-frame": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.3": {"name": "@babel/traverse", "version": "7.26.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "1ebfc75bd748d8f96b3cc63af5e82ebd4c37ba35", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.3.tgz", "fileCount": 63, "integrity": "sha512-yTmc8J+Sj8yLzwr4PD5Xb/WF3bOYu2C2OoSZPzbuqRm4n98XirsbzaX+GloeO376UnSYIYJ4NCanwV5/ugZkwA==", "signatures": [{"sig": "MEUCIQDAg47B88ajiFbyA6oKh4b9N5moLYyWzQ56v62Ys8wmSAIgD+WKphntv02B+IwG/px+pXt7tD/dfLF94T6s6fbdTcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 680848}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.3", "@babel/parser": "^7.26.3", "@babel/template": "^7.25.9", "@babel/generator": "^7.26.3", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.4": {"name": "@babel/traverse", "version": "7.26.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "ac3a2a84b908dde6d463c3bfa2c5fdc1653574bd", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.4.tgz", "fileCount": 61, "integrity": "sha512-fH+b7Y4p3yqvApJALCPJcwb0/XaOSgtK4pzV6WVjPR5GLFQBRI7pfoX2V2iM48NXvX07NUxxm1Vw98YjqTcU5w==", "signatures": [{"sig": "MEUCIAZhpimV7aPtrhYRBhGKiDgwWlNW4/+L2aB97xU50CtUAiEA1k8+e3JjuyHQVKLkNpYP7a4Grdw2zrG/9Ac9QHKsS6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 672774}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.3", "@babel/parser": "^7.26.3", "@babel/template": "^7.25.9", "@babel/generator": "^7.26.3", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.14": {"name": "@babel/traverse", "version": "8.0.0-alpha.14", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "843a26d44d624d650bc3d6fd9da3530fceb89444", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-nSkWQakWQwBCiM0IPYP8Ra4UnPmKfR4tIWhEI7A3rT6UVD1b34s/GgjUdvPoPM7GRyIx7QJyPRa05FhycjU8MQ==", "signatures": [{"sig": "MEUCIQDsxRnDB22acvySVMXpPkuEqYmymdJnHy5JRdQTu2WhEwIgKtvsEhMi9GVYHWIvJ8SoMna24Ijx/68B+SfK9j8XW+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 783836}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-alpha.14", "@babel/parser": "^8.0.0-alpha.14", "@babel/template": "^8.0.0-alpha.14", "@babel/generator": "^8.0.0-alpha.14", "@babel/code-frame": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.5": {"name": "@babel/traverse", "version": "7.26.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "6d0be3e772ff786456c1a37538208286f6e79021", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.5.tgz", "fileCount": 61, "integrity": "sha512-rkOSPOw+AXbgtwUga3U4u8RpoK9FEFWBNAlTpcnkLFjL5CT+oyHNuUUC/xx6XefEJ16r38r8Bc/lfp6rYuHeJQ==", "signatures": [{"sig": "MEUCIQCIJKE4M55rPApTQjndrySp5cJ8c6sLnPmIB61CLif05AIgGasUbdF6ZFXs7j2pyDFDdhNALagV9M8Hda4CoA4aGtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 672774}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.5", "@babel/parser": "^7.26.5", "@babel/template": "^7.25.9", "@babel/generator": "^7.26.5", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.15": {"name": "@babel/traverse", "version": "8.0.0-alpha.15", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "d5bb2897cd933408ca9d9d84bce3bfe7a69489dc", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-o7bVzAGDmLJHoNAHwPWS5KV0EVdCtVo9xmMwMCutvh2ZOF79RtFKQLDjBssH7kBU4zriOI11lkl1iGyUbLvcpw==", "signatures": [{"sig": "MEUCIQDzIrDf5lRDHVdba8yjNaAS2MChKK7G80KcIc1Q08s9mwIgPbSMYKYN4svGmkl1OtYnk5hyvKX2SMTgFBBTbN1u3qU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 784171}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-alpha.15", "@babel/parser": "^8.0.0-alpha.15", "@babel/template": "^8.0.0-alpha.15", "@babel/generator": "^8.0.0-alpha.15", "@babel/code-frame": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.7": {"name": "@babel/traverse", "version": "7.26.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "99a0a136f6a75e7fb8b0a1ace421e0b25994b8bb", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.7.tgz", "fileCount": 61, "integrity": "sha512-1x1sgeyRLC3r5fQOM0/xtQKsYjyxmFjaOrLJNtZ81inNjyJHGIolTULPiSc/2qe1/qfpFLisLQYFnnZl7QoedA==", "signatures": [{"sig": "MEYCIQDeYJ93eWg72IRF6g6Ii9LpwFwVjoRtzFOBw94+p2xXQQIhAOuAYUcJ1EWXnpOEnHccW5ejkM4mFQhhImQ0embgXRs/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 673781}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.7", "@babel/parser": "^7.26.7", "@babel/template": "^7.25.9", "@babel/generator": "^7.26.5", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.8": {"name": "@babel/traverse", "version": "7.26.8", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "0a8a9c2b7cc9519eed14275f4fd2278ad46e8cc9", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.8.tgz", "fileCount": 61, "integrity": "sha512-nic9tRkjYH0oB2dzr/JoGIm+4Q6SuYeLEiIiZDwBscRMYFJ+tMAz98fuel9ZnbXViA2I0HVSSRRK8DW5fjXStA==", "signatures": [{"sig": "MEQCIGrRHNAKNYcI0vGRMbW+Is/NUIsxrUwfHVolHEWhumTIAiAuiyff3sY68uZxMRRKgE6SEgbmea+SSceovP62DjdP1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 673781}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.8", "@babel/parser": "^7.26.8", "@babel/template": "^7.26.8", "@babel/generator": "^7.26.8", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.8", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.9": {"name": "@babel/traverse", "version": "7.26.9", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4398f2394ba66d05d988b2ad13c219a2c857461a", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.9.tgz", "fileCount": 61, "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==", "signatures": [{"sig": "MEUCIQDhDxWyQLrbNGvh5y2HeutaOb2BxdNO4TTb/BhKFHDImwIgNNOoQZDfHrg/yijIYvZv5JhlZMx5qZEpTq3bBWRZz6E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 673773}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/generator": "^7.26.9", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.16": {"name": "@babel/traverse", "version": "8.0.0-alpha.16", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "345e5efe78d7dfab0bb7947fb088268340af68d2", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-TQIt9Ozd8eiHcnqBQkrDvlRyW7W/wNcMaoBPDjPUbmSMBhSchhOrbacOi1hLsmGKYxnaSj23VQuGa1UGOVYCOA==", "signatures": [{"sig": "MEUCIQDIc8+xeaLIXbZw1ZRCy9sapH7QgoC8E920np1A2segJAIgNoKWMjta61z676ICbxUH2im6Alv+tgOyA4buoz2Nkcs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 785705}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-alpha.16", "@babel/parser": "^8.0.0-alpha.16", "@babel/template": "^8.0.0-alpha.16", "@babel/generator": "^8.0.0-alpha.16", "@babel/code-frame": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.26.10": {"name": "@babel/traverse", "version": "7.26.10", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "43cca33d76005dbaa93024fae536cc1946a4c380", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.26.10.tgz", "fileCount": 61, "integrity": "sha512-k8NuDrxr0WrPH5Aupqb2LCVURP/S0vBEn5mK6iH+GIYob66U5EtoZvcdudR2jQ4cmTwhEwW1DLB+Yyas9zjF6A==", "signatures": [{"sig": "MEUCIFUAZqyRVGfYIetHI259yWhkV2XrcDQ4wLZNSs48eT3VAiEA/HhYk4Sx9b4/MkNnNhNdDI3Sz1qN18AgCLp32189QvQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 675468}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/template": "^7.26.9", "@babel/generator": "^7.26.10", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-alpha.17": {"name": "@babel/traverse", "version": "8.0.0-alpha.17", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "95e23e0154c7784d0ffe4976f3f9da13eaaf3746", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-tlAK1RCohe5UoHBJWaMa5f6wq1eC3Ie4sm/jTYyDLmKXUVA0ieKw8NQXkl3RGyMQZ8333W8QX16pvJ/MXVaXIg==", "signatures": [{"sig": "MEQCIE1M/mgP5N+lJfAQWukTMiqIXG24sww6pOA+cuznsym8AiAphY08zLYlyx6TiPUWe3Qs8NdhPGVkqLHkIyVYYI2ltg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 787636}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-alpha.17", "@babel/parser": "^8.0.0-alpha.17", "@babel/template": "^8.0.0-alpha.17", "@babel/generator": "^8.0.0-alpha.17", "@babel/code-frame": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.27.0": {"name": "@babel/traverse", "version": "7.27.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "11d7e644779e166c0442f9a07274d02cd91d4a70", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.27.0.tgz", "fileCount": 61, "integrity": "sha512-19lYZFzYVQkkHkl4Cy4WrAVcqBkgvV2YM2TU3xG6DIwO7O3ecbDPfW3yM3bjAGcqcQHi+CCtjMR3dIEHxsd6bA==", "signatures": [{"sig": "MEQCIAvPG3QISEHzpPdKhhSxCmdoeTbPlJUmtlAoz71o1jxLAiAy0Ilk7kuOkme1u/j6UL47pYPijwDoCaiI4uRPIm8vlA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 676526}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.27.0", "@babel/parser": "^7.27.0", "@babel/template": "^7.27.0", "@babel/generator": "^7.27.0", "@babel/code-frame": "^7.26.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.27.1": {"name": "@babel/traverse", "version": "7.27.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "4db772902b133bbddd1c4f7a7ee47761c1b9f291", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.27.1.tgz", "fileCount": 61, "integrity": "sha512-ZCYtZciz1IWJB4U61UPu4KEaqyfj+r5T1Q5mqPo+IBpcG9kHv30Z0aD8LXPgC1trYa6rK0orRyAhqUgk4MjmEg==", "signatures": [{"sig": "MEQCIBYr8u9r1j5p37aMEvH5Z0R0STDhqQCFFzKHwrJEknKbAiBrh4xw7t+iquMDY2WOmT/wu93g4yeDDw4qnuXB0IEWQQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 689113}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/code-frame": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.27.3": {"name": "@babel/traverse", "version": "7.27.3", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "8b62a6c2d10f9d921ba7339c90074708509cffae", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.27.3.tgz", "fileCount": 61, "integrity": "sha512-lId/IfN/Ye1CIu8xG7oKBHXd2iNb2aW1ilPszzGcJug6M8RCKfVNcYhpI5+bMvFYjK7lXIM0R+a+6r8xhHp2FQ==", "signatures": [{"sig": "MEUCIQD6XgD+DFWrw7KlLDYewfFDLInklH3nxlA4qfR0CL30AQIgcB0Txq5t6etHyroYPDfiI5OAIk2aktwP5GNfPuu1s2o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 689125}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/template": "^7.27.2", "@babel/generator": "^7.27.3", "@babel/code-frame": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.27.4": {"name": "@babel/traverse", "version": "7.27.4", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "b0045ac7023c8472c3d35effd7cc9ebd638da6ea", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.27.4.tgz", "fileCount": 61, "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "signatures": [{"sig": "MEUCIDMocc7t9BGo702pZjWOGcI1AkEn/9luORbwNUrBACoHAiEA+FRT7yZld9ZR3HQFRuJa20FeOJYxEg9St+bUFsNH6kQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 692909}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/generator": "^7.27.3", "@babel/code-frame": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-beta.0": {"name": "@babel/traverse", "version": "8.0.0-beta.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "2b839685a3cb99ec804382c9cdac736f5e643576", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-BVIHvvWcy52CqLgXGbvzzUmKJ/SZibO2fPuHQl6JGWCEoi0ahxB74AQbDo9PsaP9gqtRonXMuWXgfyancd6UcA==", "signatures": [{"sig": "MEUCIQD1fzUDx4PX7dMPWbq0M+j8L6IKM9I9zfUBS/QI5pLZtwIgckDTbhLk+oYUswylVT/UKSwZqF6X3tb+3jGg7DXh/zo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 811472}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^15.9.0", "@babel/types": "^8.0.0-beta.0", "@babel/parser": "^8.0.0-beta.0", "@babel/template": "^8.0.0-beta.0", "@babel/generator": "^8.0.0-beta.0", "@babel/code-frame": "^8.0.0-beta.0"}, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.27.7": {"name": "@babel/traverse", "version": "7.27.7", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "8355c39be6818362eace058cf7f3e25ac2ec3b55", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.27.7.tgz", "fileCount": 61, "integrity": "sha512-X6ZlfR/O/s5EQ/SnUSLzr+6kGnkg8HXGMzpgsMsrJVcfDtH1vIp6ctCN4eZ1LS5c0+te5Cb6Y514fASjMRJ1nw==", "signatures": [{"sig": "MEQCIBHq7kSjqrPwp1env4p0tbmsWvnpWgK6WsErENeA/sh6AiB65Nir7hFwPxOYxbzap5bW8WZn6LEtgytAX16oH4Dg4A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 693001}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "globals": "^11.1.0", "@babel/types": "^7.27.7", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/generator": "^7.27.5", "@babel/code-frame": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "7.28.0": {"name": "@babel/traverse", "version": "7.28.0", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "518aa113359b062042379e333db18380b537e34b", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-7.28.0.tgz", "fileCount": 61, "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==", "signatures": [{"sig": "MEUCIQCn7hU+nqILW+T87Cvc6IYARpCqnOg3eAiasunAHQa0XwIgJ+D+yROT9hxWaMX2alFVM3lO0hi1Naw3/dz1cokAwtM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 693856}, "engines": {"node": ">=6.9.0"}, "directories": {}, "dependencies": {"debug": "^4.3.1", "@babel/types": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/generator": "^7.28.0", "@babel/code-frame": "^7.27.1", "@babel/helper-globals": "^7.28.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "8.0.0-beta.1": {"name": "@babel/traverse", "version": "8.0.0-beta.1", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "dist": {"shasum": "749843ad19c103a339154babcf49506b4e64f462", "integrity": "sha512-rD8T9WjDJmZiFjNDsFuX+joS9lgVrhgnjIa0Mc86Awgq1K8W6u03ayMjkz+R3QYckuvoCDkFNw2afW8Tn9iT3A==", "tarball": "https://mirrors.cloud.tencent.com/npm/@babel/traverse/-/traverse-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 813424, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC7koanGNgB2IkjLyCpGhwAuTb1qoS6WxGA3G2Hsjv8IAiEA643DRpEwp2AbZEmqrcdmEivY/8HUf1xVBw91dd+7SLw="}]}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "directories": {}, "dependencies": {"@babel/code-frame": "^8.0.0-beta.1", "@babel/generator": "^8.0.0-beta.1", "@babel/helper-globals": "^8.0.0-beta.1", "@babel/parser": "^8.0.0-beta.1", "@babel/template": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1", "debug": "^4.3.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-07-02T09:04:35.141Z", "time": {"created": "2017-10-30T18:36:00.929Z", "modified": "2025-07-02T09:04:35.141Z", "7.0.0-beta.4": "2017-10-30T18:36:00.929Z", "7.0.0-beta.5": "2017-10-30T20:57:37.675Z", "7.0.0-beta.31": "2017-11-03T20:04:19.070Z", "7.0.0-beta.32": "2017-11-12T13:33:55.812Z", "7.0.0-beta.33": "2017-12-01T14:29:14.550Z", "7.0.0-beta.34": "2017-12-02T14:40:11.652Z", "7.0.0-beta.35": "2017-12-14T21:48:23.357Z", "7.0.0-beta.36": "2017-12-25T19:05:34.332Z", "7.0.0-beta.37": "2018-01-08T16:03:38.217Z", "7.0.0-beta.38": "2018-01-17T16:32:36.501Z", "7.0.0-beta.39": "2018-01-30T20:28:42.167Z", "7.0.0-beta.40": "2018-02-12T16:42:39.163Z", "7.0.0-beta.41": "2018-03-14T16:26:46.575Z", "7.0.0-beta.42": "2018-03-15T20:52:03.400Z", "7.0.0-beta.43": "2018-04-02T16:48:54.070Z", "7.0.0-beta.44": "2018-04-02T22:20:33.171Z", "7.0.0-beta.45": "2018-04-23T01:58:15.315Z", "7.0.0-beta.46": "2018-04-23T04:32:33.239Z", "7.0.0-beta.47": "2018-05-15T00:17:34.894Z", "7.0.0-beta.48": "2018-05-24T19:24:30.112Z", "7.0.0-beta.49": "2018-05-25T16:04:08.554Z", "7.0.0-beta.50": "2018-06-12T19:47:57.222Z", "7.0.0-beta.51": "2018-06-12T21:20:35.902Z", "7.0.0-beta.52": "2018-07-06T00:59:44.312Z", "7.0.0-beta.53": "2018-07-11T13:40:43.608Z", "7.0.0-beta.54": "2018-07-16T18:00:25.296Z", "7.0.0-beta.55": "2018-07-28T22:07:50.458Z", "7.0.0-beta.56": "2018-08-04T01:08:22.717Z", "7.0.0-rc.0": "2018-08-09T15:59:47.215Z", "7.0.0-rc.1": "2018-08-09T20:09:39.679Z", "7.0.0-rc.2": "2018-08-21T19:25:44.741Z", "7.0.0-rc.3": "2018-08-24T18:09:28.618Z", "7.0.0-rc.4": "2018-08-27T16:46:11.140Z", "7.0.0": "2018-08-27T21:44:40.640Z", "7.1.0": "2018-09-17T19:30:47.356Z", "7.1.3": "2018-10-11T15:52:43.059Z", "7.1.4": "2018-10-11T17:32:20.775Z", "7.1.5": "2018-11-06T22:21:44.503Z", "7.1.6": "2018-11-13T21:10:50.892Z", "7.2.2": "2018-12-15T10:05:43.424Z", "7.2.3": "2018-12-20T11:13:15.060Z", "7.3.4": "2019-02-25T18:35:45.084Z", "7.4.0": "2019-03-19T20:45:10.194Z", "7.4.3": "2019-04-02T19:56:20.892Z", "7.4.4": "2019-04-26T21:04:43.167Z", "7.4.5": "2019-05-21T17:45:59.251Z", "7.5.0": "2019-07-04T12:58:19.495Z", "7.5.5": "2019-07-17T21:21:45.853Z", "7.6.0": "2019-09-06T17:34:00.738Z", "7.6.2": "2019-09-23T21:21:48.238Z", "7.6.3": "2019-10-08T19:49:50.921Z", "7.7.0": "2019-11-05T10:53:48.326Z", "7.7.2": "2019-11-06T23:27:41.112Z", "7.7.4": "2019-11-22T23:33:41.389Z", "7.8.0": "2020-01-12T00:17:17.532Z", "7.8.3": "2020-01-13T21:42:17.388Z", "7.8.4": "2020-01-30T12:37:31.792Z", "7.8.6": "2020-02-27T12:21:37.922Z", "7.9.0": "2020-03-20T15:40:00.988Z", "7.9.5": "2020-04-07T19:25:34.682Z", "7.9.6": "2020-04-29T18:38:16.129Z", "7.10.0": "2020-05-26T21:43:42.092Z", "7.10.1": "2020-05-27T22:08:03.274Z", "7.10.3": "2020-06-19T20:54:42.190Z", "7.10.4": "2020-06-30T13:13:10.122Z", "7.10.5": "2020-07-14T18:18:11.870Z", "7.11.0": "2020-07-30T21:28:45.211Z", "7.11.5": "2020-08-31T20:02:27.560Z", "7.12.0": "2020-10-14T20:03:21.174Z", "7.12.1": "2020-10-15T22:41:31.564Z", "7.12.5": "2020-11-03T22:34:35.451Z", "7.12.7": "2020-11-20T21:05:55.676Z", "7.12.8": "2020-11-23T22:29:21.606Z", "7.12.9": "2020-11-24T21:02:26.334Z", "7.12.10": "2020-12-09T22:48:13.042Z", "7.12.12": "2020-12-23T14:05:28.061Z", "7.12.13": "2021-02-03T01:11:44.816Z", "7.12.17": "2021-02-18T15:13:19.245Z", "7.13.0": "2021-02-22T22:50:08.878Z", "7.13.13": "2021-03-26T21:20:30.913Z", "7.13.15": "2021-04-08T15:50:29.361Z", "7.13.17": "2021-04-20T23:19:16.646Z", "7.14.0": "2021-04-29T20:10:12.561Z", "7.14.2": "2021-05-12T17:09:36.689Z", "7.14.5": "2021-06-09T23:13:01.555Z", "7.14.7": "2021-06-21T21:54:11.580Z", "7.14.8": "2021-07-20T18:02:48.818Z", "7.14.9": "2021-08-01T07:53:26.012Z", "7.15.0": "2021-08-04T21:13:06.706Z", "7.15.4": "2021-09-02T21:39:44.849Z", "7.16.0": "2021-10-29T23:47:52.687Z", "7.16.3": "2021-11-09T21:53:06.541Z", "7.16.5": "2021-12-13T22:28:36.648Z", "7.16.7": "2021-12-31T00:22:52.576Z", "7.16.8": "2022-01-10T21:18:36.847Z", "7.16.10": "2022-01-19T18:39:09.023Z", "7.17.0": "2022-02-02T23:05:00.100Z", "7.17.3": "2022-02-15T15:44:27.746Z", "7.17.9": "2022-04-06T15:55:24.941Z", "7.17.10": "2022-04-29T16:37:43.519Z", "7.17.12": "2022-05-16T19:33:13.029Z", "7.18.0": "2022-05-19T18:16:38.724Z", "7.18.2": "2022-05-25T09:16:34.349Z", "7.18.5": "2022-06-13T06:40:24.197Z", "7.18.6": "2022-06-27T19:50:29.821Z", "7.18.8": "2022-07-08T09:32:37.870Z", "7.18.9": "2022-07-18T09:17:38.439Z", "7.18.10": "2022-08-01T18:46:46.807Z", "7.18.11": "2022-08-04T12:45:40.339Z", "7.18.13": "2022-08-22T16:05:15.050Z", "7.19.0": "2022-09-05T19:02:20.193Z", "7.19.1": "2022-09-14T15:29:15.339Z", "7.19.3": "2022-09-27T18:36:50.233Z", "7.19.4": "2022-10-10T10:47:27.461Z", "7.19.6": "2022-10-20T09:03:33.752Z", "7.20.0": "2022-10-27T13:19:21.409Z", "7.20.1": "2022-11-01T11:25:44.718Z", "7.20.5": "2022-11-28T10:12:52.455Z", "7.20.7": "2022-12-22T09:45:35.201Z", "7.20.8": "2022-12-22T16:33:02.092Z", "7.20.10": "2022-12-23T09:19:10.716Z", "7.20.12": "2023-01-04T16:02:16.537Z", "7.20.13": "2023-01-21T14:30:44.518Z", "7.21.0": "2023-02-20T15:31:14.111Z", "7.21.2": "2023-02-23T09:31:37.184Z", "7.21.3": "2023-03-14T14:59:42.603Z", "7.21.4": "2023-03-31T09:01:58.592Z", "7.21.4-esm": "2023-04-04T14:09:52.800Z", "7.21.4-esm.1": "2023-04-04T14:21:50.407Z", "7.21.4-esm.2": "2023-04-04T14:39:51.583Z", "7.21.4-esm.3": "2023-04-04T14:56:37.466Z", "7.21.4-esm.4": "2023-04-04T15:13:46.223Z", "7.21.5": "2023-04-28T19:50:26.561Z", "7.22.0": "2023-05-26T13:45:46.203Z", "7.22.1": "2023-05-26T16:34:54.440Z", "7.22.4": "2023-05-29T14:26:58.447Z", "7.22.5": "2023-06-08T18:21:41.307Z", "7.22.6": "2023-07-04T07:48:59.048Z", "7.22.7": "2023-07-06T09:04:06.703Z", "7.22.8": "2023-07-06T12:34:07.052Z", "8.0.0-alpha.0": "2023-07-20T14:00:28.755Z", "8.0.0-alpha.1": "2023-07-24T17:53:02.244Z", "7.22.10": "2023-08-07T17:25:24.117Z", "8.0.0-alpha.2": "2023-08-09T15:15:23.616Z", "7.22.11": "2023-08-24T13:08:47.719Z", "7.22.15": "2023-09-04T12:25:22.208Z", "7.22.17": "2023-09-08T13:53:31.669Z", "7.22.18": "2023-09-14T15:59:21.236Z", "7.22.19": "2023-09-14T16:32:07.191Z", "7.22.20": "2023-09-16T16:28:47.757Z", "7.23.0": "2023-09-25T08:11:48.739Z", "8.0.0-alpha.3": "2023-09-26T14:57:36.970Z", "7.23.2": "2023-10-11T18:51:28.344Z", "8.0.0-alpha.4": "2023-10-12T02:06:50.574Z", "7.23.3": "2023-11-09T07:04:18.240Z", "7.23.4": "2023-11-20T14:22:19.060Z", "7.23.5": "2023-11-29T10:25:47.084Z", "7.23.6": "2023-12-11T13:10:04.802Z", "8.0.0-alpha.5": "2023-12-11T15:19:44.723Z", "7.23.7": "2023-12-29T20:01:33.278Z", "7.23.9": "2024-01-25T16:57:53.714Z", "8.0.0-alpha.6": "2024-01-26T16:14:48.279Z", "7.24.0": "2024-02-28T11:47:43.945Z", "8.0.0-alpha.7": "2024-02-28T14:05:45.024Z", "7.24.1": "2024-03-19T09:49:38.021Z", "8.0.0-alpha.8": "2024-04-04T13:20:20.333Z", "7.24.5": "2024-04-29T18:34:25.566Z", "7.24.6": "2024-05-24T12:25:10.738Z", "8.0.0-alpha.9": "2024-06-03T14:04:53.763Z", "8.0.0-alpha.10": "2024-06-04T11:20:30.332Z", "7.24.7": "2024-06-05T13:15:44.715Z", "8.0.0-alpha.11": "2024-06-07T09:15:55.081Z", "7.24.8": "2024-07-11T14:54:53.825Z", "7.25.0": "2024-07-26T16:59:27.639Z", "8.0.0-alpha.12": "2024-07-26T17:33:47.828Z", "7.25.1": "2024-07-28T19:36:41.375Z", "7.25.2": "2024-07-30T02:54:51.906Z", "7.25.3": "2024-07-31T12:25:44.630Z", "7.25.4": "2024-08-22T09:34:33.998Z", "7.25.6": "2024-08-29T10:14:27.860Z", "7.25.7": "2024-10-02T15:15:14.869Z", "7.25.9": "2024-10-22T15:21:30.282Z", "8.0.0-alpha.13": "2024-10-25T13:54:31.424Z", "7.26.3": "2024-12-04T12:35:44.421Z", "7.26.4": "2024-12-05T18:05:25.999Z", "8.0.0-alpha.14": "2024-12-06T16:54:21.333Z", "7.26.5": "2025-01-10T17:11:56.258Z", "8.0.0-alpha.15": "2025-01-10T17:24:49.372Z", "7.26.7": "2025-01-24T15:05:00.982Z", "7.26.8": "2025-02-08T09:59:30.443Z", "7.26.9": "2025-02-14T11:48:09.789Z", "8.0.0-alpha.16": "2025-02-14T11:59:25.843Z", "7.26.10": "2025-03-11T17:55:14.207Z", "8.0.0-alpha.17": "2025-03-11T18:25:18.353Z", "7.27.0": "2025-03-24T17:41:51.570Z", "7.27.1": "2025-04-30T15:09:14.494Z", "7.27.3": "2025-05-27T08:39:24.544Z", "7.27.4": "2025-05-30T15:01:48.115Z", "8.0.0-beta.0": "2025-05-30T15:51:31.070Z", "7.27.7": "2025-06-26T14:03:22.425Z", "7.28.0": "2025-07-02T08:38:26.301Z", "8.0.0-beta.1": "2025-07-02T09:04:34.916Z"}}