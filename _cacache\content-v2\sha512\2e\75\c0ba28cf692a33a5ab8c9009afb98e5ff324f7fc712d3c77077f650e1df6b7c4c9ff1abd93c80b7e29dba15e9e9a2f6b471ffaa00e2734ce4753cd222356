{"name": "babel-helper-replace-supers", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.0": {"name": "babel-helper-replace-supers", "version": "6.0.0", "description": "## Usage", "dist": {"shasum": "b52c06ed96c357dc6d2123cc67217efbc1363c04", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.0.0.tgz", "integrity": "sha512-qwey6Zmi5Lpcay9IArrm1KpZoE6ek8jOTYe03hcV8Hq1F1vRSDO8jzh0qxTjxGuXLJ2Fz0mPKllQQpI8EI3jog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAiDx4t0O4SuCjytBXJUXDniiBMLzvIufwTUPK0qvxHnAiBmmuwXbzyoPhiOQfbQDwHkyWftps+gttgtHdhGF1QX5g=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.0.0", "babel-runtime": "^6.0.0", "babel-traverse": "^6.0.0", "babel-messages": "^6.0.0", "babel-template": "^6.0.0", "babel-types": "^6.0.0"}, "hasInstallScript": false}, "6.0.2": {"name": "babel-helper-replace-supers", "version": "6.0.2", "description": "## Usage", "dist": {"shasum": "7881af617d088efde08c309faf4546445f43417d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.0.2.tgz", "integrity": "sha512-Gx3u8p92ZJDyMgmBphLm7kS5fmOiApltfEpHcNNi8aMy8Hp+hhZRFJIaX6pSkESDaUNfwYw721oZtYBqdBmgMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9zY/mR3ADZIvB/+Yl1ugKIuHYlQccahScvk5BMRa6fAiEA7PNIGe+hxUFN69aVutN4UDTB5yDuqu8wDhQCyesGQ9o="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.0.2", "babel-runtime": "^6.0.2", "babel-traverse": "^6.0.2", "babel-messages": "^6.0.2", "babel-template": "^6.0.2", "babel-types": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-helper-replace-supers", "version": "6.0.14", "description": "## Usage", "dist": {"shasum": "379edb2b66b3395800c45c4bcd24eff91f609383", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.0.14.tgz", "integrity": "sha512-IzzWucuFKJ+XiB7+gBxH7d5AucZxw7++nept9shApZ/2cHYcGpZbefovxcFlZPfJwliWIGB2YqAZdHL1EEmNqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZrtM1xAa2QM4fxb4wk+V3cpL4UT3zPQc9N9heKRahPAIgOodvJNw313232axiakMkg7Vc6GXCwJSEZ3vLM6MXGv8="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.0.14", "babel-runtime": "^5.0.0", "babel-traverse": "^6.0.14", "babel-messages": "^6.0.14", "babel-template": "^6.0.14", "babel-types": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-helper-replace-supers", "version": "6.0.15", "description": "## Usage", "dist": {"shasum": "587b53c0cda2d78ff96db9e712828a19de585592", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.0.15.tgz", "integrity": "sha512-Qf+yoJa2Coi4jflykqtea9ysibMx+k+30BAJPoYiJiYz+uxNjPLFbXukljAjxzj5gZizMKqu5rHO9oOQK35TgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwAoG3bcyVt6T7eKjkeQzJDhy5v3+6xV5fozO+KIeOGQIgIJYN83W2wWLlFL7wb9gmQEsA151HqbLuvnq7u0SkcLA="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.0.15", "babel-runtime": "^5.0.0", "babel-traverse": "^6.0.14", "babel-messages": "^6.0.15", "babel-template": "^6.0.15", "babel-types": "^6.0.15"}, "hasInstallScript": false}, "6.0.18": {"name": "babel-helper-replace-supers", "version": "6.0.18", "description": "## Usage", "dist": {"shasum": "b365885d1d0746accfe6b739f5f9fb269d833b43", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.0.18.tgz", "integrity": "sha512-ljZpVcDNqDIQ5DjtF1rMIXZ0mGHs06IdT4MvzyL896vciHS9e9F4bWyHWq+SmjrmBqnwh29+Mt7EqTwvDSb10Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLWDfSbK6MQrPJSeXBnr9IWlr1N1g+xVtzp/qm1PW0KAiBmKbwbT60ApnzNcFONUPExClbeyRtPp03YeRJMV2j70g=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.0.15", "babel-runtime": "^5.0.0", "babel-traverse": "^6.0.18", "babel-messages": "^6.0.15", "babel-template": "^6.0.15", "babel-types": "^6.0.18"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-helper-replace-supers", "version": "6.1.5", "description": "## Usage", "dist": {"shasum": "42bb100f37a9b225bed18703945998d4e03253c8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.5.tgz", "integrity": "sha512-X+qRNe+YUTiiNwW4EgHnYpfriUCKZ9qzPuvgsXEwEa880eeIqnL+jNZnHrXqTYu16jumISNqt9PVReun1Qj6CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfX9hEb6rov0ppDcnqyr/Z+SWSA2We0CPHrJpjJrKdcQIhAOpqUba9gfY8qDB8RYUEyAyqUZz8mDDUaPnkMM6GdXLC"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.5", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.5", "babel-messages": "^6.1.5", "babel-template": "^6.1.5", "babel-types": "^6.1.5"}, "hasInstallScript": false}, "6.1.6": {"name": "babel-helper-replace-supers", "version": "6.1.6", "description": "## Usage", "dist": {"shasum": "3be4125d213ac21fd7a18f53cdadf152b8bd8087", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.6.tgz", "integrity": "sha512-8oTRkZ6qkcKNrykuSHgeMhZmdFzhoUzRBoM1xJjqRejS8wdRBix+rmrZHsn2gfyZRszjo/LiUg530IS/CKICEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvXfh4aGK1qQz8HstJLKcYGpcmPbJB/uqX5POAi5U5yAIhAK5NCTCRRgaVRgeEVuR3YN8PWY4F1iG2qhOaUwzQI0D3"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.6", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.6", "babel-messages": "^6.1.6", "babel-template": "^6.1.6", "babel-types": "^6.1.6"}, "hasInstallScript": false}, "6.1.7": {"name": "babel-helper-replace-supers", "version": "6.1.7", "description": "## Usage", "dist": {"shasum": "5912535ba2c639cc85cae35471cb38b0139b65b1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.7.tgz", "integrity": "sha512-sNVL1nCTQt8lnatUTgTm59lbCnypoWJ6ySYjcRQUQxWAf2a9yjVEX8XQb5Bef7hYmQnvbENLpZBACiaDrLHMxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBNIPdkdAymVrecHDUFuPfUPeA+ZO98Sym5IfutjYv3/AiEAkjIsQ7SPaLzbgQCZirPhFn5CtPq6ANEQ5Kb4PsQu26Q="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.7", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.7", "babel-messages": "^6.1.7", "babel-template": "^6.1.7", "babel-types": "^6.1.7"}, "hasInstallScript": false}, "6.1.8": {"name": "babel-helper-replace-supers", "version": "6.1.8", "description": "## Usage", "dist": {"shasum": "88885d1c92056feb5e170cf24c481b87ecb37b1c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.8.tgz", "integrity": "sha512-tCHvHXhibnpvcGkqBGRuLX0RdtaB6Jjx9dG6ha2uTYGTjhtArZiwmVCeu1JGmMOjUQkpJVgqce0ecdY69Zqv+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLiuzfVpU2/k6nAR09EBQ27zkCfPn5Dw+XMWpU5f0mwAIgFfZPIpnvnRVKNEkF3QNZ865pDso6lBH/buHXT7mr36U="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.8", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.8", "babel-messages": "^6.1.8", "babel-template": "^6.1.8", "babel-types": "^6.1.8"}, "hasInstallScript": false}, "6.1.9": {"name": "babel-helper-replace-supers", "version": "6.1.9", "description": "## Usage", "dist": {"shasum": "42fd094fe623052945dab7e6aa9a6be71680e78a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.9.tgz", "integrity": "sha512-zkRJjk/9LkaKztUOvCEybKz+ElEPVtaYHGuYI9JYGokAPvaCoo7JojffSiFwygEMZiJ7r3h7U+Q/IFexD9inWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDiN3KorUA1gJ9Q9Sim7znWOzdQhzZ1OjIoRrHlQpRr3AiEA+6kEr69OKkeR7JUg6CJYrIPigtoOEl8diW+Iy9upkUQ="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.9", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.9", "babel-messages": "^6.1.9", "babel-template": "^6.1.9", "babel-types": "^6.1.9"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-helper-replace-supers", "version": "6.1.10", "description": "## Usage", "dist": {"shasum": "cb505c2865c0997ea1349d508dd97ff2f80403ea", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.10.tgz", "integrity": "sha512-gDeOuQK1fsTRQfMfdSzBY+oll6pGmIBtJeynxNXuWkCTPPcQS+DzolT0n9vWbPqhn3KjsYxfl3KScL6u2cEIQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1/24KMbrMpmbjSb46r4Yqt4DBxuPCRn4kiGqqAMHMsAiBQx8/yO2HIcfu9aB4PLj8yBUnXqYLqO7Wgg/XL9Q+EWA=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.10", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.10", "babel-messages": "^6.1.10", "babel-template": "^6.1.10", "babel-types": "^6.1.10"}, "hasInstallScript": false}, "6.1.12": {"name": "babel-helper-replace-supers", "version": "6.1.12", "description": "## Usage", "dist": {"shasum": "ec382121fd320a03a03e82d2c10e1a0b7504a30d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.12.tgz", "integrity": "sha512-AwcUduZkmX3obnWDMxruoEwWJpi67YIbc9A113Mms2yDJZ6lxY867hdTKRfuYcInlR8Jc3OxjCtA1BevR7QD5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCelvmruakekHLj3cy43MR4XJYWps2E/2R/g0krMIIkMwIgJPugeLk0PWQjZYAdZjR4U7P+BjNrx9b3SFDD3FWg6S8="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.12", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.12", "babel-messages": "^6.1.12", "babel-template": "^6.1.12", "babel-types": "^6.1.12"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-helper-replace-supers", "version": "6.1.13", "description": "## Usage", "dist": {"shasum": "fe30388cea1e0b2e59cb289a2947ec2d3f5a6da5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.13.tgz", "integrity": "sha512-Iikb82qIW34FnDne0/9CE33SIPgUK1R7Bb0RDY0ERskMbsDsX2Ns76i9+MLU/9lXEI7+cJCO9ZGS4T5/uY8rHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7Wx/9mLwX3zW+NDI+KWvXVsIepbcYeWlVW2H9pBtWvAIhAJsp58UinMTFqv+VnwPOrdQH8jHm+t1h1W8BnYE953S3"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.13", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.13", "babel-messages": "^6.1.13", "babel-template": "^6.1.13", "babel-types": "^6.1.13"}, "hasInstallScript": false}, "6.1.16": {"name": "babel-helper-replace-supers", "version": "6.1.16", "description": "## Usage", "dist": {"shasum": "677006b8d65996ea37ab10c6c55e009a68ea529e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.16.tgz", "integrity": "sha512-eJT/HXhcc4PpwNHVgn+rB8S03FGQNjqKyVTpVk8x/ovu4PXnnsbvh3hrAv7Xz4V5b8INOhHpy5N0OqFBtmwziQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdUrF9KzsYe41iZYWhH2fSc/hQ23GLs6Z5XLL4lyFW8wIhAOdgL4oWo6rGAL1oy96ywAarSR8nX4hfFxw98kpvz08p"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.16", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.16", "babel-messages": "^6.1.16", "babel-template": "^6.1.16", "babel-types": "^6.1.16"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-helper-replace-supers", "version": "6.1.17", "description": "## Usage", "dist": {"shasum": "ccd15ebe7c34993a45dbba6bb67d816229de1631", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.17.tgz", "integrity": "sha512-NmcvGTAL3W26d3KNn0B/8XVZ9JNMcEfNHJhGfyVIH3ruWvDf9YclXOwMTwhy7Wl1EPJXis5ptI+OZfsIku6eUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/vq+4KmHjrD+uHMts7QXuGGC6VPC7Pb6sy5OX7dBegQIhALVIdbQFNn84iqVnvROE9UD/oaJdjfzjKQzHsbCvxoMo"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.17", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.17", "babel-messages": "^6.1.17", "babel-template": "^6.1.17", "babel-types": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-helper-replace-supers", "version": "6.1.18", "description": "## Usage", "dist": {"shasum": "c3455e32ce2d9ff125bc62d4bd6d384b2730acd7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.1.18.tgz", "integrity": "sha512-do80jyWv2Qljf+dUYvnk3jqkhYl3XQjhr8JJAKTdOAeljwuWvGA6CwPipNb9hia18d2dA4RjLCYk1xlCojaSmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCR6I0o0+MFOb2Xr4pEx8GRl/tuV6ZD+HljPvy95mG51gIhAOmN6MFu+tlHzLyF8yOQnps1I9iBJzIRuAXv5TxUFTgv"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.18", "babel-runtime": "^5.0.0", "babel-traverse": "^6.1.18", "babel-messages": "^6.1.18", "babel-template": "^6.1.18", "babel-types": "^6.1.18"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-helper-replace-supers", "version": "6.2.0", "description": "## Usage", "dist": {"shasum": "dc27cf9f2441a296424cd584bd9947fe0bdae96e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.2.0.tgz", "integrity": "sha512-vbVvrO9iJSyWThVE1kH+UN9iF2n95tstruQPoywkwmbqlAe7vceQETS2aOOONPWr+jmsHgMBEo6YsNTK0J7NPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzeNrJnPynVnqBOpCiEE0ONrQ73K5oLJ8BEkPlQkJ+VQIhAKX8AgjJY+KV/FPu/w66dPC9XimDognmSejgX3u9UHE+"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.1.18", "babel-runtime": "^5.0.0", "babel-traverse": "^6.2.0", "babel-messages": "^6.2.0", "babel-template": "^6.2.0", "babel-types": "^6.2.0"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-helper-replace-supers", "version": "6.2.4", "description": "## Usage", "dist": {"shasum": "39cec72dd371e8d94460ec004e62b31dea160323", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.2.4.tgz", "integrity": "sha512-+J0F8b3YYK4u4KVUTzlfj14MU1HtE2p/GTtTgecitn5y1sfHDs0fDVjQEEEFHLG4qKWo+O9KS/9PFzuQXRWqoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtUKY4UbF/4cdZJYkvMcnIwFHswf8+c+iWOZGRAs3mSwIgWaDzcdvLuU/LuIyDJLwHm4GLGz5fd3Qew5GacwP0RL4="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.2.4", "babel-runtime": "^5.0.0", "babel-traverse": "^6.2.4", "babel-messages": "^6.2.4", "babel-template": "^6.2.4", "babel-types": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-helper-replace-supers", "version": "6.3.13", "description": "## Usage", "dist": {"shasum": "44ec93d7e9d7c06f339d4aba75924f88dee9120f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.3.13.tgz", "integrity": "sha512-SNamMkFvBo97BIqROrmERdCghjXg1UsYEcyYZzNamY3zUt4OCN+jTD+Wx975rhTlI1Wv1kBY1GYCSig/zTEgNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+q6eRCYpbH65gT4MNG3c25Fsmn7GATwNk8NwRXn/oxQIhAOwkoT3tOjiCI8nJRKpLUutiMrTSNfauvDFu4aLofJnB"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.3.13", "babel-runtime": "^5.0.0", "babel-traverse": "^6.3.13", "babel-messages": "^6.3.13", "babel-template": "^6.3.13", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-helper-replace-supers", "version": "6.5.0", "description": "## Usage", "dist": {"shasum": "4675f36a568f97b708803aa016269c505dccbf80", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.5.0.tgz", "integrity": "sha512-x4Y/9V0PwJIFpQJbgfUxlhwwrmJwAM478l8St/EAb2QhX7cZAn4xrfFMXIq0+aW0T6jsKGCINO+5Sbc2wH4VYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFEJMhKPoy/E9v5ItAJ+uwz5fjHLuRYKtZkKGhRkkiO+AiEAgUqaZ6OkCQgneTKj8lMVWHfBREGph8ikpW+AYXsK7hY="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.3.13", "babel-runtime": "^5.0.0", "babel-traverse": "^6.3.13", "babel-messages": "^6.3.13", "babel-template": "^6.3.13", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-helper-replace-supers", "version": "6.5.0-1", "description": "## Usage", "dist": {"shasum": "d07e00a8f02e305e250b31c388f24d6784a75e8c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.5.0-1.tgz", "integrity": "sha512-dgtIts+dDtAV58RtXceCcboKd9ouRvSIcqaLsP9pxBjvl4WMn+W2z0xpIDeQ0RL/pFnyVl1Vv8ZDq5Zqky2YTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCeXlluCpuSfOof9IyFIf2Q1LYrxoqPYU5k3roNMfbtQIgWkmmgmmARaNrDh4V5xy7HM+PPBpy2KrcgtyiGzzOqAg="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-traverse": "^6.5.0-1", "babel-messages": "^6.5.0-1", "babel-template": "^6.5.0-1", "babel-types": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-helper-replace-supers", "version": "6.6.0", "description": "## Usage", "dist": {"shasum": "fafa1b3f5adeb1e557495e457969f691dc2f4a4f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.6.0.tgz", "integrity": "sha512-oE+5DM2qE8sL/adyLzznZpdnZCS+P+uIc3RldGcCOH+IxIiVOJuPPu/dZ/8LcAJsVqktIfFt46rwP21AsTeFgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICBzW2rgSU6Q8bY/5vXigd8LApnEXrku2cl19ETemYzBAiEAp8eSuNjd5Agz0nnbRNSidXBbSp8sOtEHpaUBHyw7mS0="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.6.0", "babel-runtime": "^5.0.0", "babel-traverse": "^6.6.0", "babel-messages": "^6.6.0", "babel-template": "^6.6.0", "babel-types": "^6.6.0"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-helper-replace-supers", "version": "6.6.4", "description": "## Usage", "dist": {"shasum": "81745665289b75339c0b9b4aa6f7f29483373442", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.6.4.tgz", "integrity": "sha512-Nj3xfN4jBziEIlqd2T7T4YeJvGn9/cLjZhpWT6Hw7p9lmzc/4/mQfnR5ObLk/jT9Bh7313LmBJiBAGKFbJshnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmvS25JYzDOl3uXqwdhVJy30BJf4zRE3+2hJCjD/6lxQIhAPngupFe4UF0ZT6F27lhfY8RCYerfV2CTvYSWGoO/g14"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.6.0", "babel-runtime": "^5.0.0", "babel-traverse": "^6.6.4", "babel-messages": "^6.6.0", "babel-template": "^6.6.4", "babel-types": "^6.6.4"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-helper-replace-supers", "version": "6.6.5", "description": "## Usage", "dist": {"shasum": "80da543c22b8f142d1fe38cfc32936d3126298d2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.6.5.tgz", "integrity": "sha512-sWGKD9vns0aYJrfXw8YA1BtRe+nZnk4KjQrsCP4ORl4YX1NO6w7fcazftruBW4p5+g9zx68I8qo66y+gJWw/LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+vjIEXGx8u9cwFY0TFwYwM6uOZEm8Cc7XxlTBDTTKZAiEAhNWPj5VPUprbp2Az3bI6eQJo60LPWIkkGGQZYNGJs1w="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.6.0", "babel-runtime": "^5.0.0", "babel-traverse": "^6.6.5", "babel-messages": "^6.6.5", "babel-template": "^6.6.5", "babel-types": "^6.6.5"}, "hasInstallScript": false}, "6.7.0": {"name": "babel-helper-replace-supers", "version": "6.7.0", "description": "## Usage", "dist": {"shasum": "517426656a4199ddc87c8c09ebbf70c58fb1a07b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.7.0.tgz", "integrity": "sha512-eOmyPThCFmjKGTk1pHAI3chwtM4lGm9oAd+XRNcyVt4V4YMVAKIVAYODzzH8h4hGxa1y9gpJ3L9xULRs59Ucag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDCLwU2f+fqRsmiSedennkYj+SyBTIgtEwAXph89HtXAiBGbjf7RVOeP82Uxxgqyy0jC9peylXbLAjm/ySdE9+KEA=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.6.0", "babel-runtime": "^5.0.0", "babel-traverse": "^6.7.0", "babel-messages": "^6.6.5", "babel-template": "^6.7.0", "babel-types": "^6.7.0"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-helper-replace-supers", "version": "6.8.0", "description": "## Usage", "dist": {"shasum": "69cb6bc4ee90164325407af1a2536a42e8fb90d5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.8.0.tgz", "integrity": "sha512-dIecK/ZM1QmcCJn3plTFq9j3htd+VYc+lEGULlVOWuSpXf2A+gWr1beQjiKv5+mhFpoLJVp33qqxaSssH2Jucw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTN8HP7i1zHBKgVK3r1rOE69nBAW2m/u8KR00PZJRPvAIhAJ9l7NaGcTyG99WkgpnNELPhhVrODwCpcOrz0IR01+9p"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.8.0", "babel-runtime": "^6.0.0", "babel-traverse": "^6.8.0", "babel-messages": "^6.8.0", "babel-template": "^6.8.0", "babel-types": "^6.8.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-helper-replace-supers", "version": "6.14.0", "description": "## Usage", "dist": {"shasum": "038e13824d6de0e412fbd6708fd3611a7683d869", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.14.0.tgz", "integrity": "sha512-i76T4yZEMImwTdySJ041ZwlXIBZ7BdaBOxQ53lANRRwPi8HPi4Gwxpk7cN8Dd8APGGG8Wv55MMv0TDN4TCeYug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEXBkkiuHAPfKw9CgSZwTT0ztxd9tTZGBoX4R7hMuKvCAiAe+yPGEqRgThZVV67bdE0RkTS58Z9N2BWXSOeu8R2vMg=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.8.0", "babel-runtime": "^6.0.0", "babel-traverse": "^6.14.0", "babel-messages": "^6.8.0", "babel-template": "^6.14.0", "babel-types": "^6.14.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-helper-replace-supers", "version": "6.16.0", "description": "## Usage", "dist": {"shasum": "21c97623cc7e430855753f252740122626a39e6b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.16.0.tgz", "integrity": "sha512-CGxuBNl9TcNXdXWg/Kekf/hIsv2VYQM/OIyYkIyx9SV6UY/tcsc2AKvkPOyzbjJt3EBb0JiZSISLGmt49oyD1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4pr4dIy8O7Obzyn6xYpIqCkaU+qKHxlvSmIiypwzThAiAPbnl5V6QtaJBjW/uQGJ3qmEtlAloVYtexfB9URut/PA=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.8.0", "babel-runtime": "^6.0.0", "babel-traverse": "^6.16.0", "babel-messages": "^6.8.0", "babel-template": "^6.16.0", "babel-types": "^6.16.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-helper-replace-supers", "version": "6.18.0", "description": "Helper function to replace supers", "dist": {"shasum": "28ec69877be4144dbd64f4cc3a337e89f29a924e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.18.0.tgz", "integrity": "sha512-kFsVTwgpF4A6vOCLnNfLq8P+B1WA6hq0R2AR0KTg2LhUXaQjb6++syshrugtR2meru70J/n4VOxBlvDqxtfv1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvElGgIg7dEQo+dm3wMLa7oEwUKh6cldV0DZQDHW2OeAiBPlPMDgShMyiiNW5i4l0YkObKq8rG+Q5DiFZWJTX86BA=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.18.0", "babel-runtime": "^6.0.0", "babel-traverse": "^6.18.0", "babel-messages": "^6.8.0", "babel-template": "^6.16.0", "babel-types": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-helper-replace-supers", "version": "6.22.0", "description": "Helper function to replace supers", "dist": {"shasum": "1fcee2270657548908c34db16bcc345f9850cf42", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.22.0.tgz", "integrity": "sha512-VAupbbMp0Cm5qlZbKWFp/S4Mlr7AKB0so1VGAqeJ+U6bIAqSu79zwTlf5lI3QZx3yHeQWZCVVJ3duedb5wFipQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf92iaA+Ka4zN7Tx/65SIheL96neBRM4glT1WUb5NQEAIgVfvG0nWl42J1rDVIbrsizTD0srLXXsocH8AI+J754eM="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.22.0", "babel-runtime": "^6.22.0", "babel-traverse": "^6.22.0", "babel-messages": "^6.22.0", "babel-template": "^6.22.0", "babel-types": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-helper-replace-supers", "version": "6.23.0", "description": "Helper function to replace supers", "dist": {"shasum": "eeaf8ad9b58ec4337ca94223bacdca1f8d9b4bfd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.23.0.tgz", "integrity": "sha512-ON1sLopjZiMUVDC1swVrivWADckH045l8N+yrQw36PiuVYf0BXDV3LWqORtD4hMwlMDJHde1TcTlkvxrCC2LSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjfneLhTSNRSXuRUo2GTa5pjzgpYrlkFE2owZzwe3FCAIgCjY2ZcDyBi6cSszts/W8mDSNAHonN+PSVt8WnXDvtYY="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.23.0", "babel-runtime": "^6.22.0", "babel-traverse": "^6.23.0", "babel-messages": "^6.23.0", "babel-template": "^6.23.0", "babel-types": "^6.23.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.1", "description": "Helper function to replace supers", "dist": {"shasum": "5d8a2d4f31203f6ea13cfc2b7837dbc3ea2f5215", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.1.tgz", "integrity": "sha512-1cLqsT/y0f/2n3PdRsTMf5QWT+VBxdN5a/Y6wU2WL880/mDX6ZZ1h09HYjcCz5uB9RIPQpHPyyOnnxNbalP0Yg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxS8VB3GXWqFkhdgL3V6bzP37IAKV6iZFcARvCLz+OAwIhAIYFrrOsK+YJXwFPMzJUofwUlN6vNS+k9c/6oZ/9xsL2"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.1", "babel-traverse": "7.0.0-alpha.1", "babel-messages": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.3", "description": "Helper function to replace supers", "dist": {"shasum": "04c701909509867fa84bceeb2b0e51b34cf2d657", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.3.tgz", "integrity": "sha512-Hd9ix6mxEsrYOZTvfbwUQWa1T+Drn95FnCofCTUL6HrnoIlM0stiNDZOo9qoIJ0H/inVY8EXg2QJ8AdOyEQ5vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCn7SY5v02wLHtPomqHuvI+6x8MXNMmVXdLg7s1D7fY3QIgNGle1+UhWnGvv0qc4xGt8aDSCOlVu2oZpQ5l3qlgi4k="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.3", "babel-traverse": "7.0.0-alpha.3", "babel-messages": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.7", "description": "Helper function to replace supers", "dist": {"shasum": "73ceb5bfeb597ff2c2772a75f0aa013be58e78fe", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.7.tgz", "integrity": "sha512-FuppgsSIrj2ActfHx24Zbbjm5xHsHe6mCcZ8wBO88XeTr0Le9PqRXCQCDGaBgV5IzUDFs42bgVhXmzq0ImrGkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJ8G0BaJQtf/LhSjbL2ND+lsRhghM+avo7orz1OvwpPAiBIiBRuM9HOiH+XOUWZsMzSRhOf/SQBdvAwDSyfQQWErg=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.7", "babel-traverse": "7.0.0-alpha.7", "babel-messages": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.7", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-helper-replace-supers", "version": "6.24.1", "description": "Helper function to replace supers", "dist": {"shasum": "bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz", "integrity": "sha512-sLI+u7sXJh6+ToqDr57Bv973kCepItDhMou0xCP2YPVmR1jkHSCY+p1no8xErbV1Siz5QE8qKT1WIwybSWlqjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0EnpdKpk7cm/BzeUgVjJCUNJ/IDdxdc9dxTFkjlhkTAiAbh8ekIQX9cBy1spKtF6cBMYZ5OD0DOaXXrh57Yeae3Q=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "^6.24.1", "babel-runtime": "^6.22.0", "babel-traverse": "^6.24.1", "babel-messages": "^6.23.0", "babel-template": "^6.24.1", "babel-types": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.8", "description": "Helper function to replace supers", "dist": {"shasum": "f7354ace21088676a2c378c68671095b04d53e78", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.8.tgz", "integrity": "sha512-0ZhgLDoJiZGzynyoo0lwDX0rJLldR2BwMthwaZrbvf8BreiS8zeiElbo8xIMfoB/Sy4U3F7MTTARfHTGjt/FyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvOS3w5iKeTs2+CyfR2vkZaGmVrlnCFFEAH2plgblJ6gIhALClI6ccQnoGmKImcvI8e7TsNTxx0gIPfoL0QjmSIndr"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.7", "babel-traverse": "7.0.0-alpha.8", "babel-messages": "7.0.0-alpha.8", "babel-template": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.9", "description": "Helper function to replace supers", "dist": {"shasum": "501fba74d5bb2c169f52b1146bcb7005a0f8a86f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.9.tgz", "integrity": "sha512-d+bxwYkfr9Ni/SdLC5SQyX4AOWW8MHfdWYLrbR/pM3S6sfv6GKWwYBN27IhfIDfthVO/mhnH7jxs7ouAAcblEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMc7G5jzZ4ewNPjLTzj3smvndRVkIh1qjRAHG7rFxnKwIgD2GsuFbJUPfT+mK1imkmm6KoaLbM3B+Th+q37Ou/TJE="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.9", "babel-traverse": "7.0.0-alpha.9", "babel-messages": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.10", "description": "Helper function to replace supers", "dist": {"shasum": "530650aae6b21573cfc6092925bc853711bb1ae8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.10.tgz", "integrity": "sha512-Bi2acDqdhHWD7wnv3UYsHcoSnAH/mJ35OR6TG/a1a6QusCqzP1/d3QdVcewUDRCsqpRvg+Kz5au8St9zWAOeKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEuIFMizIH6uJRmEIsaanVhvDBHXr+8HHh7EJHSaWhhhAiEAqBnym72lPEy7kWv+i4D6q31TyqeR6+6dxe1mDWzAWco="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.10", "babel-traverse": "7.0.0-alpha.10", "babel-messages": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.11", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-mxXUjsNkATMFQXviILkdHEyFuGmg/HJQ8pv1Zi6zeQcGZkCihd6J6LZ+6fYlBjZLjTFbqz5fL3X6aVf/uO0U2g==", "shasum": "b9818f5cfd5c58deded71730d1a4bd3fc93b6bdb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYZVrMPgO38q7uOqcu8TfbVPQhlVaH66UhH5k1gfxRRAIhAJyN6daFs93tn3SCqBr8o65MQgqCtPdQW1KRHihcEwS7"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.11", "babel-messages": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.11", "babel-traverse": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.12", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-3arxsxg+FFtwiiGQGGe1DgdioMzj0wFIPbFSV1pv/3Des6fZJEeJDdsK8vvalAG2PZzhVZEQKPHYuDj4TRLHiw==", "shasum": "7076b465db69dc2fd98163e87b93823c260db016", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW8PXKAMWWUaiRuiYN/ckb1CcWvOBxhvtja4W36TzSagIgV9fD4a+uGpv1YtKZlmTW543vR1hv6HWtssqwbP/dOUg="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.12", "babel-messages": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12", "babel-traverse": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.14", "description": "Helper function to replace supers", "dist": {"shasum": "8baee2a524e81f2bbff963128bbd1b15da4afdc8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.14.tgz", "integrity": "sha512-iq2poOwdHpTFcxOnCgG94jbMQrjW3sp+oW4rPLJVKEwtQY7L+vlKRAvbzjBnEPPXqzaBVliQ0D03xK6YZmKang==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtXBygTTmp6OikcwXKZbNZriw70kRNmbulIaIsIr101gIgV84X4qEP0cBa/kXsd2gv89d+4V+YTwS2OE5r/hZOwKk="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.14", "babel-messages": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14", "babel-traverse": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.15", "description": "Helper function to replace supers", "dist": {"shasum": "8244f181b59de22df84f947645e97296547c961a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.15.tgz", "integrity": "sha512-Prl366Xvwr67+j32HZPbIbaLS85BdtGPCkX4L4KZQ17lVMs9hxXDHNhXtS0LTAi7eBHWqhlV6KKoqMXUT6dCTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKgC3GZph07RVvPk4ZH6AKRwJ6cLQhCe7ewfdZpL1wNQIhAIHnDYCY49kGkWe754XTNjUq1a6kNeQLHIXHTocndM/p"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.15", "babel-messages": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15", "babel-traverse": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.16", "description": "Helper function to replace supers", "dist": {"shasum": "fad2e069caddc4e9011202a625c825dac6ac6072", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.16.tgz", "integrity": "sha512-87GU7p9LgC71Z0zqX0HR0gxokZMriPohF7GGxFObEoMus8jI4lWkvR5mPYBwzhusKSfRAC2kb15/bfOyIp8GYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGMkV9anqK5Y8mSCTmdNgF8T6NDTPVfZYFe0ZvMfZd23AiEA5pmfH7Q/Hr7p+1+z+0jHmpjXU7oHwixgA/Z7AGDxMSI="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.16", "babel-messages": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16", "babel-traverse": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.17", "description": "Helper function to replace supers", "dist": {"shasum": "a1fecbb6ff6d91aa1d1f84a168fe9013ca142d18", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.17.tgz", "integrity": "sha512-cBH+pd5o8Qca4nt0vCY1T5mE7T8kYWWk3GbYYJANzNsR0Ub0vTE0IgiFeAlkRQkBct3yFY3OjP9GFVKTWbyCWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID6fsMxJizGWEdggiWC5Anl4amoGFGxp4fIL7W/7afE9AiAfzTg6YSAMwidKFr+C3aCb3JRYQJcy9KGXHkJhahulAA=="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.17", "babel-messages": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17", "babel-traverse": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.18", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-zM9WvoBxdNLsLsmatGA6kAqwvFFjvvKfr8SU1vX/Nya40t3z8Ag8pK4EMQPeJi5fwJK+lzzdU6dGfoDP62Anzw==", "shasum": "40a1620cd9b2f8e898c5edee772eea3a8ed99ce4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+ixmgJPuMW/26aA5I6f/zxpfpZOKRSh+8huMn7Adt+AiEAyvFi7WywiVnYUEUiAMf4bsDIRH2hx97J0AVFl4ODRgY="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.18", "babel-messages": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18", "babel-traverse": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.19", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-/lofcpgP/OaommQKeMD3GSNQjAQIYJr9SARkaA7OPkChP0x4p4EnU1PF77hfDy04N69VnAfCYhrojJLB4mekhg==", "shasum": "8c9224d65fde8a872e2c5e1d18b15d5ddf3aa212", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCF2hxDOmUGWTYQTuYrm4/0rejoT0UXWBvMgSFJvBvPWwIhAOvRTbYcUH5yWcVMaPPCadeXCHOVGQAaaC+DBkyndPnp"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.19", "babel-messages": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19", "babel-traverse": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-helper-replace-supers", "version": "7.0.0-alpha.20", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-Weh9laOxzu80CPgVDV2OcDqCbqcTIU/bQx7Z8bbEWLZ+PUV6B5GicDVKCTvyzcx0YzU8yJcSSygeIJyiYKz+ng==", "shasum": "42d091559304583651b0d49cedf6ca11b39f253a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAc/yEq1LssqXNLx183itEpRiVZ8tx1wkQkFAUn/eGngIhAOIVAKezL3fQTkVErY3a1GHg4xcVYQsOTjy0cmxVeCuj"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-alpha.20", "babel-messages": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20", "babel-traverse": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-helper-replace-supers", "version": "7.0.0-beta.0", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-mI76I8Xi9RRCXUoUplVS89m5Xbd1gL9R6jay76q7hC/yM0ACrzylkkxPdOvwVslZfSKYJ3GabS6XQB/T8/hqWw==", "shasum": "3c0ed8247652b6bc4d0fd6bd74798497036be39e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/WT+YqpWAfEYWuuc9ndgBB13vGKt5a4ATky4P9z4hpAIgVdBjWKley/IHUiwwfcCh+WL5pfz0jywo+Ew7wCkicq0="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-beta.0", "babel-messages": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0", "babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-helper-replace-supers", "version": "7.0.0-beta.1", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-WYFRDPBeAXnjEnIwc3YZOs5jPOhSR7VYchG+eeZEtiC+s1XvtJvLx99s4fPbWklJMuQz4grg6WWnhw+IhZ6uxw==", "shasum": "344ed94cb4dff14029d73a3edccd54e81381bcc7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC88b3eJb7MJ7bBAJPEijOV9EHuGT3CbGtTty61x6tgAAIhAPRZPgqqAbMYKI44tLdZDAErvbvTUvofqueJ1Qqcm/H6"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-beta.1", "babel-messages": "7.0.0-beta.1", "babel-template": "7.0.0-beta.1", "babel-traverse": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-helper-replace-supers", "version": "7.0.0-beta.2", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-Ul+KBqzKCY7TJ+Uc/UOwweia5R0mY2zO2RuTXffcA5v4fGvfYF9Olg9NPDenQkSbHe1e5ti/dZLs7tzzO8uOsw==", "shasum": "8c5441d95851b7ba86be4127ba1422b661b46c69", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBPNgPL3qBy2WFk3ZSvdYILFeObVw2QVSmT197G86EfQIgLIzmHumh9toruzA7GaX3dMp7QFS7cYH3HdEw+2AbStw="}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-beta.2", "babel-messages": "7.0.0-beta.2", "babel-template": "7.0.0-beta.2", "babel-traverse": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-helper-replace-supers", "version": "7.0.0-beta.3", "description": "Helper function to replace supers", "dist": {"integrity": "sha512-ZkTdE7XBDW0PUQkKTeax+m1JpZqzo9ze3zbqjRxyt+x328NeGLD3edmNCIxmFt86njxIo3AFfZ00PKd4g/7jqg==", "shasum": "73598401b73feff5a6689a929b77496f15d673c3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC267iRJiiQ2lY3A+XspCsrAjF8XgAJE33F8TQ7mTRBVAIhANT8IJBVazNHKXBcnOjNt9WdkXyeSxb9ugK7BoX9Ntqq"}]}, "directories": {}, "dependencies": {"babel-helper-optimise-call-expression": "7.0.0-beta.3", "babel-template": "7.0.0-beta.3", "babel-traverse": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:00:06.980Z", "time": {"modified": "2022-06-13T04:00:06.980Z", "created": "2015-10-29T17:55:45.085Z", "6.0.0": "2015-10-29T17:55:45.085Z", "6.0.2": "2015-10-29T18:09:48.695Z", "6.0.14": "2015-10-30T23:33:45.073Z", "6.0.15": "2015-11-01T22:09:22.327Z", "6.0.18": "2015-11-03T01:24:17.277Z", "6.1.5": "2015-11-12T06:51:03.343Z", "6.1.6": "2015-11-12T07:33:59.345Z", "6.1.7": "2015-11-12T07:38:24.689Z", "6.1.8": "2015-11-12T07:41:29.597Z", "6.1.9": "2015-11-12T07:47:16.549Z", "6.1.10": "2015-11-12T07:53:52.933Z", "6.1.12": "2015-11-12T08:49:05.781Z", "6.1.13": "2015-11-12T19:58:42.615Z", "6.1.16": "2015-11-12T21:34:22.973Z", "6.1.17": "2015-11-12T21:41:17.509Z", "6.1.18": "2015-11-12T21:47:36.229Z", "6.2.0": "2015-11-19T04:34:13.514Z", "6.2.4": "2015-11-25T03:13:19.478Z", "6.3.13": "2015-12-04T11:57:39.909Z", "6.5.0": "2016-02-07T00:07:06.460Z", "6.5.0-1": "2016-02-07T02:39:51.900Z", "6.6.0": "2016-02-29T21:12:34.725Z", "6.6.4": "2016-03-02T21:29:36.312Z", "6.6.5": "2016-03-04T23:16:43.255Z", "6.7.0": "2016-03-09T00:52:56.944Z", "6.8.0": "2016-05-02T23:44:09.051Z", "6.14.0": "2016-08-24T23:40:50.675Z", "6.16.0": "2016-09-28T19:38:50.073Z", "6.18.0": "2016-10-24T21:18:52.968Z", "6.22.0": "2017-01-20T00:33:37.294Z", "6.23.0": "2017-02-14T01:14:25.589Z", "7.0.0-alpha.1": "2017-03-02T21:05:46.210Z", "7.0.0-alpha.3": "2017-03-23T19:49:45.265Z", "7.0.0-alpha.7": "2017-04-05T21:14:17.258Z", "6.24.1": "2017-04-07T15:19:19.620Z", "7.0.0-alpha.8": "2017-04-17T19:13:12.645Z", "7.0.0-alpha.9": "2017-04-18T14:42:18.987Z", "7.0.0-alpha.10": "2017-05-25T19:17:42.051Z", "7.0.0-alpha.11": "2017-05-31T20:43:52.760Z", "7.0.0-alpha.12": "2017-05-31T21:12:08.502Z", "7.0.0-alpha.14": "2017-07-12T02:54:20.436Z", "7.0.0-alpha.15": "2017-07-12T03:36:37.384Z", "7.0.0-alpha.16": "2017-07-25T21:18:30.033Z", "7.0.0-alpha.17": "2017-07-26T12:40:02.443Z", "7.0.0-alpha.18": "2017-08-03T22:21:32.611Z", "7.0.0-alpha.19": "2017-08-07T22:22:16.208Z", "7.0.0-alpha.20": "2017-08-30T19:04:38.388Z", "7.0.0-beta.0": "2017-09-12T03:03:03.315Z", "7.0.0-beta.1": "2017-09-19T20:24:50.722Z", "7.0.0-beta.2": "2017-09-26T15:16:05.639Z", "7.0.0-beta.3": "2017-10-15T13:12:31.720Z"}}