"""
Agent记忆助手
为Agent提供便捷的记忆配置和跨Agent记忆访问功能
"""
from typing import Dict, Any, Optional
from .unified_memory_manager import unified_memory_manager

def create_agent_with_unified_memory(agent_id: str, agent_role: str, 
                                   base_agent_class, **agent_kwargs):
    """
    使用统一记忆系统创建Agent
    
    Args:
        agent_id: Agent唯一标识
        agent_role: Agent角色描述
        base_agent_class: Agent基础类
        **agent_kwargs: Agent其他参数
    
    Returns:
        配置了统一记忆的Agent实例
    """
    # 创建统一记忆和存储
    memory = unified_memory_manager.create_agent_memory(agent_id, agent_role)
    storage = unified_memory_manager.create_agent_storage(agent_id)
    
    # 更新agent参数
    agent_kwargs.update({
        "memory": memory,
        "storage": storage,
        "enable_user_memories": True,
        "enable_session_summaries": True,
        "add_history_to_messages": True,
        "num_history_responses": 3
    })
    
    # 创建Agent实例
    agent = base_agent_class(**agent_kwargs)
    
    # 为Agent添加记忆助手方法
    agent.memory_helper = AgentMemoryHelper(agent_id, agent_role)
    
    return agent

class AgentMemoryHelper:
    """Agent记忆助手类"""
    
    def __init__(self, agent_id: str, agent_role: str):
        self.agent_id = agent_id
        self.agent_role = agent_role
        self.memory_manager = unified_memory_manager
    
    def log_activity(self, activity_type: str, details: Dict[str, Any] = None):
        """记录Agent活动"""
        self.memory_manager.update_agent_activity(
            self.agent_id, activity_type, details
        )
    
    def get_my_context(self) -> Dict[str, Any]:
        """获取自己的上下文信息"""
        return self.memory_manager.get_agent_context(self.agent_id)
    
    def get_cross_agent_summary(self, execution_id: str = None) -> Dict[str, Any]:
        """获取跨Agent记忆摘要"""
        return self.memory_manager.get_cross_agent_memory_summary(execution_id)
    
    def save_execution_progress(self, execution_id: str, step_id: str, 
                              status: str, result: Any = None):
        """保存执行进度"""
        progress_data = {
            "status": status,
            "result": str(result) if result else None,
            "step_id": step_id
        }
        
        self.memory_manager.save_execution_progress(
            execution_id, self.agent_id, step_id, progress_data
        )
    
    def get_execution_progress(self, execution_id: str) -> List[Dict[str, Any]]:
        """获取执行进度"""
        return self.memory_manager.get_execution_progress(execution_id)
    
    def get_other_agents_status(self) -> Dict[str, Dict[str, Any]]:
        """获取其他Agent的状态"""
        all_contexts = self.memory_manager.get_all_agent_contexts()
        return {
            agent_id: context 
            for agent_id, context in all_contexts.items() 
            if agent_id != self.agent_id
        }
    
    def can_access_other_agent_memory(self, other_agent_id: str) -> bool:
        """检查是否可以访问其他Agent的记忆"""
        # 这里可以实现权限控制逻辑
        # 暂时允许所有Agent互相访问
        return other_agent_id in self.memory_manager.agent_contexts
    
    def get_shared_context_for_execution(self, execution_id: str) -> str:
        """获取用于执行的共享上下文信息"""
        cross_agent_summary = self.get_cross_agent_summary(execution_id)
        execution_progress = self.get_execution_progress(execution_id)
        
        context_info = f"""
=== 任务执行上下文信息 ===
执行ID: {execution_id}
我的角色: {self.agent_role}
我的ID: {self.agent_id}

=== 协作Agent信息 ===
总Agent数: {cross_agent_summary['total_agents']}
活跃Agent: {', '.join(cross_agent_summary['active_agents'])}
Agent角色映射: {cross_agent_summary['agent_roles']}

=== 执行进度 ===
"""
        
        if execution_progress:
            for progress in execution_progress[-5:]:  # 显示最近5条进度
                context_info += f"- {progress['agent_role']}({progress['agent_id']}): {progress['step_id']} - {progress['status']}\n"
        else:
            context_info += "暂无执行进度记录\n"
        
        context_info += "\n=== 最近活动 ===\n"
        recent_activities = cross_agent_summary['recent_activities'][:3]  # 显示最近3条活动
        for activity in recent_activities:
            context_info += f"- {activity['agent_role']}: {activity['type']} ({activity['timestamp']})\n"
        
        return context_info