{"name": "@cloudbase/toolbox", "versions": {"0.1.0": {"name": "@cloudbase/toolbox", "version": "0.1.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.0", "maintainers": [{"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "73ba7b8b43e33ac95f42f46c2091a77fc9768942", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.0.tgz", "fileCount": 77, "integrity": "sha512-6m08iicpERxlNn+NC/05PgWEN89yKdT6mdbgA0vxzfHB3VMpQZAHxEDJykDJLU5It4ZKbLxfHUxropVK+K15SA==", "signatures": [{"sig": "MEUCIQDeZ3ufkwNYqkP/4LaU1ofI1I4KfGO0zv31a7Gnh5+NlwIge7Bp283MoFAjzS/ueQKMXf5O/3GeDAe+0hThGd/D0Rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemCYCCRA9TVsSAnZWagAAuZAP/0LMnO8jWp9h16HvQblO\nCYf7WiA4Peq6Gzf2VFtQ2pa3nml8qBcpJSED9uk29eiDgb6cfIHAA2/5Reqw\np8ZxuaBUgJ2AwfhthizITT6tpno9SCR9FpZTRSQJEHYDyNF299kjgdlVRUCI\ntbVJ/t9SyiWsjAYzn/8x7FbDC6Wyl62iBgmnFZ7Hjekdmh69A2IwYDfPDhkj\nnvOiydid6mXd/10qXrtV+3N2FYTEvZS+qu6tQlBiFIFgqveWVybyezGzzHFP\nqSnxY77//mMExrplF0/nN4WD+i+HqRlvcbVGcrAsYWdI52y4rwCuhaI30XXD\nEyRaMZfu0LFBKjv61/g8ZsZYMTp7PvLXj6gtCIpGhf6eSDy1SZiH3f3NrFXK\nohD3sQWPjKHTCBjNqx3gAE6xFxglzHjNB0yq1Fn0McMSql2YYp0QGpODYLYS\nJOBdiyx9NDyr6q08t3jfyfHsGDBheE2MJbpi/5CfqLX3xPvCMUxxKeYzpeWw\nW/PCO/zFXdgN6KHYY7nPiC3pQ2QwHlx2M9CYBvhBoafDwJ7+47jtJOEf/EKb\n+uTD1iO+n2V6PDLX4hWNjjkfjFec6eO3DXMkyxEfNg9ZCp/sNABv4lqO0YpL\n9QCzHS10iiIxll+mUjnVC7h9qyfbdPZe36v1u9tSMCxUJJ8vk95THn6xVdRm\nAuwu\r\n=9vIX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "dd62cbe115ff4b432e6a1e833247212116d13755", "scripts": {"npx": "rimraf lib types && npx tsc", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "ncc build -m -C src/index.ts"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.0_1587029505536_0.1840567838936571", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.1": {"name": "@cloudbase/toolbox", "version": "0.1.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.1", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "47b95b69ddc61509b9a33833d25d3183e4914984", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.1.tgz", "fileCount": 77, "integrity": "sha512-6E8VZy5jHeZeU1u+4fO5r1QxD4NehwMjxE7pj9mutQCcG2MsRW9hFDgX3VjJxWDZ9YknYTuG373yo7JyFG7YFw==", "signatures": [{"sig": "MEUCIDz1yr8oGJNl59aGbTBpRpR+vNZNKhuWUJPDLeI7tdqPAiEAz6stR5x8faK3IkyoDeNP5iUrEw7I4C9SVWXxFUDUSKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemSSbCRA9TVsSAnZWagAAjQcP/jTSwmZYkBIkOPaLMd5P\n0WPoh84fzNIfrDWP5BKPR7N6gpC4lHiKjQJvauIB9nqbxVJRxbq8d0TotR7R\nd0Ngx4lYRNqhEB4NX+Ur6YTqYEX7+lPjziNcRSdpX8ReueiWgpHsJAKPAbnA\nBfqYWquWaOta6NKMYusmsCf2oFs6HJrzYz5H5GuMTS1PeNuEeemzHIku7NuX\nITSJOys8t0Uvh/feT18w3Ctiv2tuUnjrrGt3QoaFy3THoRwtrbHpbnMjLXBO\n3388nElKAgtL2IKnvEy+SQAJojL2ZITwYcV1Y6W1Aoqh2Cy8v0udROSpABuZ\nUML9AsUOcIB3JXsUDKNIrrYdi57DBogcel4tbAWFE22U7KPQMGnOfdO0Wa65\nZ85i6J97a5Xi8wobm11K5+PU5g9nBwwGAQHeIKvz/vPHMaULWFSfNUBJJs5v\nMrwBWapltqwRpmJkC157vCEaPlcC0tOJuoX4XdScSWFv7V2fxMrY3mnKXm5M\nezmHLJVlIshkE0tp/ZEyu4fPepsWSLSA8Zgd3qtEZ9XTSR9BK4pBmPJIfl9I\nA/m1BBQ3Notwjvqc01sabVkGS9uWFBmMz5Zg9BV1zR5nWUlfoNVa02AwZnXD\nO52h8RVKLGQvRipThiN1vNsL4gGQCZM4G5roNSX1yaK4xZzXKA6wvYbYEnna\nnBOz\r\n=Db3m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f5520ae9ea62a587fa818a371fcce5db3473e478", "scripts": {"npx": "rimraf lib types && npx tsc", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "ncc build -m -C src/index.ts"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.1_1587094682979_0.5490905122477434", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.2": {"name": "@cloudbase/toolbox", "version": "0.1.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.2", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "56034406699605de39bc11369e70322eb45242ca", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.2.tgz", "fileCount": 100, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>bipdgglQwsNRdSXvLLGuVB5Foez8oUrnHooStlu513nrpRnIYdQbKB5IJH5xbj6TK2WyMOI2LC7SiM1PHg==", "signatures": [{"sig": "MEYCIQCnYBuDfRYkGHBLqCP93P5gRU9xMvO+aIf4aUaVsN64nQIhAOLodRRFH+Cvl9NUl2KvByGFLLVPTQXZShDgqrtoWJOW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1013913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemSzICRA9TVsSAnZWagAAhU8P/RJsy/dhQkqPbQzNty6n\nO9MAXhihaUbkIR7ayHKnNz5AEpJzsuLs253ARCXTBLTiZr5ybuj6RtGeWC0c\nufgvyc6wVd5jG7/at6SrSOaKMBW5dUIBtdI4tMIIGqavlYsrUVUZfYqCap45\nVP+hsZD4GFGIh82P+KwtWx4VrZZIUWSHcPUNpXZWuG0xHkNmuvEsvmRtldyz\nzOnc4p3+vCVr+jNMX1DGYI1ySSfEdtsSnp81ycU7FRGE5aco38YXo0ecaKLf\nmKyAFFk6Ce2Rt5gLwU4c7Z5jHqO7UPHNNN6Y2OdILQ2+DZvA3OKtuzyqK7aN\n93kUyGf0Yq1lWctBA0GpN8dE7Wkcl3iWK+nw4zgMhDUARi5QQEednRrfV6GN\nusH0f162wsPqrJuAd94XMkDTxPO17AQzCJYXEslpBchszjS/dxZNqKYo5TcQ\nCyU+YfNtIUgAk0DY7ZNHIKYIHTWtX8Nxyg36vqWYYatUj8W/isNPzRp8hPT5\nknVmKIP8wAi9VVv8/17oxsu36Zom/bCMVAJF2riLbp0uJ9/W/GaYDvlmvg0x\nfbNW8V6wM6b6+U62VTS1Rpzey5Er/VbOYbsY6NpPJtbCkizfkebnu4A/FCqZ\nR34+W05YArd+DI86QdHrsXvRDCcEJQV5lZkHa/RtX6WESXyp7dy44EIWmiyI\nAXmn\r\n=if0H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "3bdebd4b843b9865af4f28a1eae0902fe724a1a2", "scripts": {"npx": "rimraf lib types && npx tsc", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "ncc build -m -C src/index.ts"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.2_1587096775811_0.33635005798663165", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.3": {"name": "@cloudbase/toolbox", "version": "0.1.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.3", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "fd37fcb81ff2f0bc8fe8bb8fe34f9e7d03e0c2d5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.3.tgz", "fileCount": 103, "integrity": "sha512-GNfz2UX//+p+h2kKB4ltDPO7vDUbAWw0YRG/X39WgbXBEEzlUKF98X7FKb/Qe+8++bNP4NlbTv0151dDvLkEhQ==", "signatures": [{"sig": "MEUCIQCU7KAO5xspYyUhipreaU4FXfpGYAUwA3qyczo+4dqyIAIgNkUouzeFeAKE6XIKNxJEBFOvMGW6iiHHH0OndvwVf4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1014942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemZQGCRA9TVsSAnZWagAArNIP/15NMFAa7RZKa1A67t7z\nNcEXbGdxby/ochweCEzx5bOiBxtLRiPcifKtE+yoaZKpMCPw6oVWhNsJxT6y\n05VjGX02gxF0mT+JZJF7R+dqfuWk4/zRN/zD4Uuc0BR2MpxHN4HsFDqNqDzz\nFyAGiTd+MIPRsPy3gl4/sVwutPOuwVRR4vWj7bxf2/uJ/X9qWnhNX3XbOSSA\nQBsDW/aEM7KXfDnr31SGiO4Kw9JFhcIt2V/VCAeFY25mar3rNxZNGZYyX2/u\nPgFJ6nvHuN20OWbEAwYmEjHiteeuUlHkavDTtjE4/RKyhLSiVwU8211GOpKr\n2MTkfXZNZnueFkbY77PEoswZiWx3VjnVBjYMKcMXsXvdiPlMBJ1FPRP2cIuK\nWaA1OZGUmzegd7IX1A+NtbhY0Dc1jBfWPhgVN3K/exzO57e0UO3rvwKp7EiW\nxaT6rhRSxmQqymdyOIvjZwpqpgsqXsAlsLExy7a19XXgl3Q970p9xDcIPEG0\nzeZAWOFC+peMyOLYxHfpjabKEPS6JWdZ7JnSggbeQhqQ0lQ8eb9637WSFEGB\nGJ1PX7YXKg1YTDg3nDFO7UuBiwnXpYHyzdD/F/EWmSt7wWZCsbrSr7AkrO79\ncsFzP48njS/va/mt+68mS8j+J5h3PHkQKgyPrbiverSIX63Wb82uevNXIRmm\nJTkA\r\n=vYqw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "3eea32a40ccbe02605931aa22d7a333461e3a1ad", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.3_1587123205445_0.39815654204375095", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.4": {"name": "@cloudbase/toolbox", "version": "0.1.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.4", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "b7d7452490ab695e41685ebacb2a2277eea2ddd3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.4.tgz", "fileCount": 103, "integrity": "sha512-FS7cJZ9hxRDZzWy/3zErtlayNWyp/I0PLSLJIo7kE0ZgtXvJJpXvJJuTK/OMgr75s+VgMp16Y8WovKm2BMdAkA==", "signatures": [{"sig": "MEUCIQCGxz0wI4GatMgRJ8O9zhHGVMc6jR2yEihkaM4Gb8+aAQIgFQYvFV1gSNyNkvCqqa+EYJxflZtT+nCZqqhe6hZ1YsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1015076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenq0bCRA9TVsSAnZWagAAG9AQAJyMX70PptXsmayMpSLp\nkFFWbba7ZYKSJjOuy+BL0ODW7I9/miNBqfsMfiKH09Ff1gjUCsXOL/gy9ATk\nElztsXbl6aVu5Q4ahcbX1KebAjr0F43wZ4FjqN5OmnmmnZnxeSJQYnIHd0vk\nkTjAq/1MgEf58DumytK4dKslc+g7GDWu5eUNuwbzUPb/c8ESNlNkXW3CwSpN\nnUdGS7f3umxP5qJMgq4OyBCBG9sF97pPBdLoQ9W3Fvuhm8GnW34atFQ0YF9x\n4g4vpcev3gvRpbIRBkdMCtIdVsaMWuUP1VxAPBP26e0uNvPNasYMwf6yErAV\nbYPChqoFpmPwX2xDZVpUWMU29laEmo1zRt/rpxmFxCNyQG0ZHdcESSRotvyG\nbT5tNHxI7lpuSzeh17w2aadcciWHGyisFOezZ0xmmFl9q885TSaSu1CSn7iC\nm/cgpcm+sZCD1a2jzu43YMatIYu0EHBFRRdTcjilCV16OlAntJ5HTLrOUaaJ\n5vUkVA0p/nwem3jO1T3BrElo6i5SvWuQf00WDrEWhsaOpAFCNPqheGt8TVED\nmnVzLil2wy+w/mgf4wPpepAROc6j9UZZ5R6C3yvy0mGO1zqz065LX0oLr0fb\npE3q5s+zqU/xeDgwr8QI1+/+GcEieVMm/fP0d2jQyW7Ny6sXO8HiT8Gpsw//\nmM5Q\r\n=d3LT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "3d6e14a1f70d5d241aff4397b239e94e7e041be6", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.4_1587457307261_0.5272035172748273", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.5": {"name": "@cloudbase/toolbox", "version": "0.1.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.5", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "3ed93bc03fc374ecae6c9a0570774e5f2de00c81", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.5.tgz", "fileCount": 103, "integrity": "sha512-KBYv+T/h4OoNVmWFFjrf7js2pt9TpBhBQFMd8c5rle/DwTf3qsrz1XabgYdreSrIxx+dPf1AVcz0uWeAsvQSIA==", "signatures": [{"sig": "MEYCIQCzzOVykIzlBzaFkNCbYHBLR8kT1XJHRQY1gWc6C2eldAIhAMQbIh7zFkWeg/Q5P1MFmMha6ZGDyWomrY48SUirAK7Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1015308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJen7gsCRA9TVsSAnZWagAAFqMP/RFbPQ5rQF3fi/5gKNOg\ngXYsnZGPeJdldw1xHWE5vYLXCGMyLESx100XpJ+61RFbKBh0HVI1bexylrV6\nWebYdgptop5Bfwqo8ks8KT41p3G9E5vYuEiAVVePqlDT7BTwlUABWbAFL/FZ\nj6JEhaxf4YSijI73+7qaiWcIokCqGXAgqCwY/PRWtGRqLyF/uY8RicBtlzrz\nVzT/QQvxAFCpNIqZ5Ab8kEj848S1gPZebe62+3imH6hlqCZsDaLvXvQtC+AH\nKXjTSl7bOnyeJoz4JCuWPvlykJso+eWhdM+bU1S7pIeBZYGuRo1u9eCJtVI7\nMBmz+PKXNML4ClKYvEA5JEp7xizn+kqoizl/ghX9/1rZrMw6xJZQHeBke2sB\n4cecWWssUewzQ3IomFn/FS9LOcZc7Wa8rr4vp7FxthEPnNmVWyP/t0mv04+b\ngsUfcCoEAfvCo2ocS1HutDAhQmzyFGV7wa9HDyfafMOnU1DUEymX5nhg5Nlp\nb1S2yCk7EGUvKrf9g+X/IBaGmJcK8b7lcY39doxKNQrh6OElHqXSyYpIP7Bg\nFIOepL1O9eR2+BQhgyWubEZ0ZJtzf5+qbdLkhUge+JOAbBXOvPhwf8Ow5cwD\nXjkuqNPsM/09DnQGmgxDNHYD3I77xv5qpdchIhS7MmTxcqSGuRq0Y5NwcHth\nIi7r\r\n=sasa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97b86e90eccc3ef8f31cf08e94f8dd17263e910a", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "@types/lowdb": "^1.0.9", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.5_1587525676208_0.5458067511924358", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.6": {"name": "@cloudbase/toolbox", "version": "0.1.6", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.6", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "c30b6d33a91740a521185b4d8a4101a0488147e7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.6.tgz", "fileCount": 81, "integrity": "sha512-SWb3Iwe2FOrSMmiF7dtn2emDRq4oEvlKkhUy1RbYRngkjcb6k2dQHl2UU2Z8MkIh0H24H05MemjTNUaYKkMTfQ==", "signatures": [{"sig": "MEUCIQCWf1LkL3SieTWu17Dy++U+nXlpJZ7JuoEjU+Esy2iOyQIgPXIGFWybTzfKH0UsguwkTAQOZIxjiNK5TNNJcdoqibQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepjs0CRA9TVsSAnZWagAAUg4P/19EYi69yu6C25KwTDNt\n3ou4Mb6LCY5mhqULZgZb/xVek3UsBu2l3DE9RrlQao09Tr3tyVfGjPXexgqU\n1AzpzcvYgme+Jt2joAXbl3mjqiOYN6A4d2aDZhKl3JhN4wbiAbjmWmo1P4Z1\nh5rYSxBeeLlnGQNbTLKivr3+bHCDtHrKwKOU0Un90wSORjTiInXgZ+C2vPDO\nTl1SqDInPlqH97OP8IbQwPqGH0mV9jC6vyrxI0tF7AWMSovXS9qU0A7ROhxQ\no3RXc31kLJPNH5VzBB7lQSTXGYMMwOgmImU0vii825sPhvjFTtZE5fYaCmjD\nlAl9dIDzFo1Djl5TCpmQsGNpeOQ6ZgXpe2MMWFcFtutzD1jtBnBjmWgwJHfd\n65p1ODJcxcL7O0RNYVzNFwla4BeYH34Vm5Fc5sx5M1MTEuNMattaT3ppG4q5\n8vg7sxJhzT+y5xDwckEjrcsmE9v8wxx0u/OmY+Q0jh989M5yy91FBRz4wvo8\nABWbvnsR0FmOXJzx9JtFOLuE6SaPSimGmdQG6jkJRMDfG+xVFaZ/2XrTIRe/\nDZfqk0wXRukW3UFjwlQrlkrE3gamIVZhGPjkeNbWOYzxw5RT88reI6zqzZTW\nAzvZPJVIDoLG5r2g9QDtOVkxC7Nt4kqCCsuumWYxHdLtlCbXCdGa3IXXqfzT\nHSKU\r\n=eYPO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b861e04a14f647085817120097103cf9bfe9a4dc", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.6_1587952436282_0.3777191208591806", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.7": {"name": "@cloudbase/toolbox", "version": "0.1.7", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.7", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "e301d7ecad5ec279a672c817fe857412a8b4cd50", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.7.tgz", "fileCount": 87, "integrity": "sha512-4Ck4mmDVt4C0jxBXhEARhf2kzynhVu+YzxFDRAM55W/uwfM18Hzt3D4iuaL1+l7AwmXCjsyLaEZiyykt46z0Iw==", "signatures": [{"sig": "MEQCIB8b5NijMFcuW3f1/tK46J1SIiZC9soo3p7E+fJaawb3AiBF7a/68zeYaTK4LGgQRQ/zzrfGFSfvCQrDkWLVjgnKvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJes4OfCRA9TVsSAnZWagAA4v0QAITazqRk+JEoKk+fxjhw\nyR8kfFDpDTf/wFTBunoTFFlkCPM558pLKYKLb+Cmrfp5gYuU7OUZkuZr8O8W\nM3Jsv7K7QnkY4knpENUYBGRe3nyYMLyS1Bg5efyo2aAFW184Zj+2AKvfuqxQ\nrJbuumrO65l8ogTDq4GHVy9wZy+JmQ2pqy/mxN5O3ynlGhzQfJruo/d99f29\nPswvWnPdiisErvOUcPMEmLWlrNQsxmIKylspurJXceEeTYYfdv/zxPh9LNuU\nh3kMojl0wk5kBCZ+SUqUjoBc/sG+cuRvkDAX1j05stxAvT4O8Xqm8K+dK2k8\nCuepvzq/fEtgGA4JILR3j3gYJ8PSfnQaf6JiQQfsRBhjRzIqhB9+lo0H0cZM\ncDi1AlcqU69ER1aFJAOQFozQnP4KLkI/1r5HI+0Cqpdm++De7UiHb1nRPYQ9\nd+f/xiCDvc7hAEZrJDRx9cGTspxtWjjtCEW+ySao0R5eKf2nQR62gajbpPEb\nV5PE6SoqrHEqtFp7N79MjRbN5mYAshFPPK6zTy8VmJ+WqqqqlZqJtOuLVzzo\nr7grPwd52Tc1e2AjlwpcXk3F3atznCwGB+cfZT+m0eSy1Xsr32hAjpeBEPRh\nAPSvH6IxtSQzPotVxSxcnNsFAkEQPjK+afHpDQhRqyFvfm1DXeYIir5oI/Yl\nZzuQ\r\n=474L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0129a36b1ee62b388abba59c32be9467f5aa9110", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.7_1588822942581_0.6184790866423964", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.8": {"name": "@cloudbase/toolbox", "version": "0.1.8", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.8", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "b1cd63a4e108523ab2b733fb6cdc27512aec7284", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.8.tgz", "fileCount": 87, "integrity": "sha512-0xag//CioNJLVNQY4EbFgbw7j3rwTEN3FJgjTT6P1vDLP/tLd2b5LsiZkJ6v82Ki+iP2njnjI3s+hxNw9CDixA==", "signatures": [{"sig": "MEUCIA/fftKAYBS8xqUtLH4iX3Ep0ylIxX9+t6UtUi2IhzH5AiEA9S60wzR7WIGCFXMIQ0B78ipZ1OslFS/NQn7lQlprGxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetmYoCRA9TVsSAnZWagAAW4EP/3Q0hN3rh9C4H8XNBAR5\nlzHb4m6A2MwOtwA887wYnvqdIJlhHoRHwU/SmihmwzIn50bcB8Pl7PzjqrTt\nAg2W0WFx5/5vAjJfd3UEVFtim9LJGS0kGmZ0o0xm/N8vIaCUcoJcRt+yxfkV\nu+sf6bUVGY3E9mEnUB54zAm4lA1/QShBUSrkDzZoO6l+XphamzdlJnCQqnxO\nHEfRdi9O8Mp2dFm5dywpxER1F1mWE7LDVfevwWGPOJlJVfswciGIMeiFEXXV\nXeCAosPo0RMHiAdZc9bJLGJNRJxuy/zbqmOzpB7y04erMRcjvDRV82W1oEnj\n4KAAOBaZwK1fwie1ctqNoCVa8fvHvszOTqFLgUmms0/98aLxA/m33OPAyNNv\nBHUXh5gIMuEFZUME5dOEQE5wwcy6IYvaGFRQ3NdZszKZalB5IiD+BYyoUf5G\n7jdpN2udHQmbtApcqDBWH4PVIziRYUvL2BYg3omGkfz6v6jCG2Tm6NqXZzMU\n3dczorDQerEb1wmbZMAGRhqo583elVBu6VcsIIB7gBo4+yvKwMKAqauQAaq6\njv4TlFf7OdmW6t3b9Dbd88vwDCYakln9ADuubNkBmYn8COyK5GsWeScNZQgG\njYCGFkc30ON+/eIIMSEcBo2NKhjp2bjzE5xn74rPenR9N2zthyGstLKRCUpF\nONEf\r\n=g9nn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a8b3b185222909e5d67311ccb28b831e72750528", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.8_1589012007866_0.15255049473672022", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.9": {"name": "@cloudbase/toolbox", "version": "0.1.9", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.1.9", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "77c9f0d9639d2ab5a79c7c67c302c0031082324d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.1.9.tgz", "fileCount": 87, "integrity": "sha512-2fPniwmcLaTq+fqlyvF579GoQGqBVISWsvhKlWSB3ma6KhPwA0Z5WFv4k9CS+0LHdV8hbvaAkl2oGum0N7GQxA==", "signatures": [{"sig": "MEUCIQDqEe9yeHa+Wb4zP4kgXIYIPnQEWFEVkBAZAPzjxSIqfwIgQUrrwDisRA6/CxaSTWBJrxLBtDHBlyWi2ADv2dgkTcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetmbFCRA9TVsSAnZWagAAYe8QAIls2BL58EwH+RWlcZx8\n13IjPkhHXjtopAakfzsQKFL8NDtgLNeNz07QeSMDMnui5MK6acqRZZNvIM7P\niLjxolc4SLNGcaABSZXpKjNtZOoqwh63Xhu0LQOxrUphO+WcgU13SabjplaM\nkODMLSVrFbPEv7np90SOkBNp6tkvJ1GMzOSEkmYhROkPvs2qxPwGYhgwlHSt\nM0qrMwEThtRNUPLZJM8yoYKSfo65XrTX78VbYf2ku3J96LOH1uE26KFcqHdV\nH+dLPaSRDsCNVXkS2WY2OYGKW99sEHBwKbf8jYGLjEMTefYRBnYoMz2tZx4j\nT4n4cmPEf6NrizZnNEpq11ywTXzcPN/N8jtiOI5Q3ybqVrwCHnowNctnG4VB\nEKgfePTgfURhwkLrLwBW69TbiSwvZFJKtKMyjqWBJ4gZ/hTiR0i6PZnUkfVj\nTK0kNO1y4lMT9qZ0ZABdaLrABNHcpVgwqaVmtWmlDDb8Xf1D0M/l8EbmuAwd\nRnFK+ExsxdXwfkSTAShj3dP2PfA9K1Qdjn2o6EfZ+qjh51MR/U1XUnYC8+NE\ni2i6gBudF6q+c/aVKaJrovqJJra6qjdcpDmMv01tKX0zDcWUD1itgdIIJiTK\ndUcevNHnp0/KrEtb1BaK5fyZ/awPb1jd1lns+sR2CyHIvAB5e+rRGlZg73Wb\nDsnE\r\n=gK1w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ef1b4384e75fd9d85c3bbc5452133f483eded897", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase.s", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "lowdb": "^1.0.0", "tar-fs": "^2.0.1", "address": "^1.1.2", "js-yaml": "^3.13.1", "make-dir": "^3.0.2", "unzipper": "^0.10.10", "portfinder": "^1.0.25", "cosmiconfig": "^6.0.0", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.1.9_1589012165211_0.3061040683676486", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.2.0": {"name": "@cloudbase/toolbox", "version": "0.2.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.2.0", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "206f6cf481d76afc1c567fc421cd06580266fc3d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.2.0.tgz", "fileCount": 117, "integrity": "sha512-oRFMKGJLqtJtUkZ/GphHK3RBTMYg9itHt+35l7RDgztS79wLd1VSFX3HzJ7H/5gSXG5xJkIeaY1wINASkPW13g==", "signatures": [{"sig": "MEYCIQDf/BJJU1RF3imOsh+zjBEBGYG5ccZhofkZtrx1lIXWmwIhAND4w0cwFNqWXAW8xIy9PU3GPVjwPDH0lAd2jMbl6jNY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexPn6CRA9TVsSAnZWagAASsAP/0r1Ao5Lmdt3BQlFRaKa\nIUT+FyCx3Mhr3fZzN8t4D+i/575MDrKr9FKODFLLgyu/4xIuvgnfIelNOH0W\n4QQEmqbuxSWzHTbyFkn5SpHSmLQbxqmCBXtH9ypFxNBJV1i/ZZ3i51eJImB/\nKSfZUHlmGh6RUqkkuchTnkXKXvQ+TCZt/5F3E7U1XlFC6yNVcz2P0HSHRlwi\n4gke/ewJxRCY4NXTXGSIL4mUm/+FPIOdyKP/a0z9QYERe1pIRLOwgsQm9dT1\ngwK4eH3qLe0APp87ai+gV/963YL1A9FwBzBvcADhcE1L86Ld7DeJaGiwlM/J\na3IhYSqrlhy5pvnpKflc3TM9AOy7HX6flL7DceaLrnx7GZ+Io8JB0bOWtK2m\nbnU2BcyHXuNCBk2jA5O+i8Tg/hDz0JDRbYMYRNr7u2emAQ9LvWmLdBywdFE2\nJBjTPK4ndUymQtrqmhW2Yiz9LdvtF6PhFl/VWLDxEzqPKBqQd9f5sv9hKyXN\nU1I14dkL9H6m5f/rFlQ0Rf10IKbeiKzGSfYoinGIt0KDXJcaDUgOq4NQCPmt\nrvnIUUBy2fDI6HLRZng9FNO0UoJWZSDF2cj3rir5FYYPGRFQDrHMt5opWNi9\nbW41M+qy3Y1lCs+qvX0qtFyGePEniNWRwT/KHR+bBL7wBVrOLamk/Lz8guPw\nCJrI\r\n=qupd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "7215b2abd84e6835cc58ba3b5794629c0280ed98", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.2.0_1589967354305_0.3223915470063017", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.2.1": {"name": "@cloudbase/toolbox", "version": "0.2.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.2.1", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "01a183bbf55c43e8c496c8900cdd9e98e17f8a08", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.2.1.tgz", "fileCount": 117, "integrity": "sha512-L+PM5Q405WxSvwb5J6M8aFz+8v5iRZytirmQc/rklg21iZ3jkPg0rgZdh+frjozcEMdAOaA9FGACPpJQMocvnw==", "signatures": [{"sig": "MEUCIQDXsT+Jg4MkQdfj8sJLna1EAex41DQeObEyQm6ISPxzTQIgXdtJjS0i3FfELt54DGam7sg+3e2r8QnSlywcCRFTsZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexRPWCRA9TVsSAnZWagAAfv0P+wTP59g+DjQQThzIWgIl\nJdpr8ksKu2C4iCRWpoF1uAifBV3j4qmm5lKjMFWUsrOgt9pZMkl4IaYHxS14\nv68Qz82ikM1JnFjPiZDaKmcuIZPVgfVuKDIuX0elRUUE8gTLyGoxas3rdKvG\npYXd6oW3UEjBkiQLfyUO0bPLv/G0ssIvpB8lD9clTSP11rqIgS8jrlHu/wIf\nYZ2BxbvaD0fSGjKkQtsBtgMUaXoBZ79m+jZnGEeZGhlD4vsTE8aI1AZTaiSn\nonuDjid15HxFKei4ZVpX/kaZnoRYSZZ7xu5SvlQof7k+/RgMQ4QAtEms4rV3\nwCwJvfdbx59NcNAtIxLYLmAqJCx2uL/HX6m0p0R9fDx93Eg+xbeJhIGdfaRu\nT0PiQUUbllC7tQ7xar/tPrnsXqcC93oDhSsPYsjIS2F//B5gLnuoiprE9S6m\nN8vZU4eJ7bh9GJf0MLyF332Zkm1+JbLUeBaALfU6tcRQOvsrCjeRkCzE3P+B\njQ1D4NDXGrzYHOVKj+WRvhP8feIW92QTTSr3S6prjkQxRLvQ2PVmhcD+yFOf\nVSbQnZ2w4Ntk6/tvD7vPaZI9GfH9ZAorywX2UKyE9ihdgcxdjZvdlWhhPVGs\nG8G08KgDL5vzgICq9x7CwWJ7Y1nZMKQ6eiwacvuNROn29Hp4UJ93fOiZTO8F\n9RIV\r\n=8gy1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "fba44c9a93ba2de49d24b7d7b4262b0c742d9eeb", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.2.1_1589973973644_0.4299165315800233", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.0": {"name": "@cloudbase/toolbox", "version": "0.3.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.0", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "a2951654840ec9168fcfaec303aa3ea808b31765", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.0.tgz", "fileCount": 125, "integrity": "sha512-LWe3tNMcrGZAl+OhvKXSht+QtYpPWJT0Bhn6lr/7CNGkCdfefraWMxDVu+/aZglMHJaq3OfpwDug8X8SB0C09w==", "signatures": [{"sig": "MEUCIQDMh5TuusRnMGbWWU7SCeaR7xc2pywHUGJ/Ots3xH7SJgIgDGNzIEugTn1yJ55+W6VfWgWsvmoChkMcgGhR+uw5i/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexnlrCRA9TVsSAnZWagAAohUQAIo8Y0uw8b/cK+txNeYI\ne8RJmN+XH/eEpcmb1/XpBBuAvxcrU0+uXPp14697WMFMLigMyaXFMMZtH9zF\nf2ee3UmkaS+ClVdCttOXJ+eA/PAfFhSIJbxGYNBGRDj+eVymqx/UAMJ/Dsmz\n5l1vU4KSLlHWtzEeHRHE/0CRt/gQqN9agSX+jxxrl6aNzVDEy0oMYlWQa7lI\n/fsy1asOK9YN3PrssmbwfLIJ286CysggZ14hxrDqb/vpLrwAGW3NTehC2Kz8\n2VIhuDlOwFioWhSCzWw3BSfOeSlm8d0iNeiV+/JognwbOhIqmiutaH+3Fyon\nCPobvqnxPslFFhXnYGIFBBLDke4/v1lfkmQFnfa2pDPGIF4/44sWvRaROlv5\nYaOdOld9TVDsdU93IMPR7b2vd2uU63rupjzGhssE8zkEknN8VX6Tc8zQZ44R\ncsxKzHEFiQtf2iNOkaDUVdj1Wt8XsOkpHyE0EdFcfSYeoHTFPKPljMivOhwb\n4KyA+oBBGdPsEjixZzu0zYM3L+YgutAUZi/ye/OUjH6/GXsN8bq1RGe4xx90\nS1xBFP0t675+5Rod4ijabQ6kPx8ToeKnCVXDiFMicnEjCWCQFDxAiGZu7Hgl\n/1momg425C0138wc/51mymLeLXZfF21WG/mK7GNx7OCiBdB4Usto0gPtyAcg\ngkkG\r\n=qkJW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "cf27a4bbdb555229d608258ce55ecfd3dd61d44d", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.0_1590065514784_0.7117726556244539", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.1": {"name": "@cloudbase/toolbox", "version": "0.3.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.1", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "da896e18d4fb3bd2e74328a1c72a3b49c1d17a7d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.1.tgz", "fileCount": 125, "integrity": "sha512-TS62f0xABJzvJBUpfTSyNX1mcxpMfCRNRLehM2wfEfva7i9LSG9pFU5nithwVqzt6lbXF4tm3w/gJ/m5Syh3pg==", "signatures": [{"sig": "MEUCIELsUjoT/Prum1jmPVadBhGHClokxXJy+doBLfsvMlF8AiEA45uLql/33gwz6KbeO5SC5Xl5T8YyEnTigz1HOFsY9q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex3umCRA9TVsSAnZWagAAcY0P/REfIVD784UnwDuaS6yf\ngiOkmO4opvZtfdrQYMka6MlSgP562r5F/N1JUFoDdIBSHQq9aJ4jq9A1rWE0\n46d8gyAqYSOZFXVHC0XixQKHgw3lO/miwsiU+m1/yTQFl2icWE17kE93r+z9\n0IQS0ByuxGKsp6iHLuvNNYImBxOj9+Yo27hPwksbIJxKao/reaN33olEskB0\nR5Sm6sYVFRt6WTg+/1jj8eIzacaTpkXOt5sfmwocl6+yyz+jCTdcehCSsNtE\nvV4bL1VVTxY2FlzDVHI9SEltoigKg2YUtBKgkhz06AlW1Gu61asiUZEAWuFe\nIpG772NJQiwNGHzYTx4/7U7ad1NdDm5bFAFnQBYa/VgmXiMf1xGJJIgov93Y\naWfTOMM1zcPslw9jM/ijvtzDIkYz9AypDg1BMVcDfwh/34PNtH3a+X9/x5tq\n24cIUbZS6tl+T7Br2Y6b0WBr5aDtHGDp/5beLJklGF+xQdbIMUWPFw5J1sg5\nsiBmE5yvqfBjdaHWMvBKw8f6ctVeOWac9+72tmNrH62hRg368e2Dr/8xTON5\nYn5ubag/VVW+u0svi98h6U1yPn3yzMhMBEqnfis4GuIBL18YRifCXxswZM6M\ncwigCQU815w1np+zmfaZdJmgeVbs0HJw+QfW0KgldCY17/LJOymLsMMqPZ5q\nP3lo\r\n=IMa+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "d7fc7bb1b61f0fa17b9f02504f38a12d943493e0", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.1_1590131622041_0.494600379070806", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.2": {"name": "@cloudbase/toolbox", "version": "0.3.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.2", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "aaca7eb0642ea25c6cc3f1b299d5a1eae89187f5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.2.tgz", "fileCount": 128, "integrity": "sha512-o8OoGBtO4WDbmU2FAuZCnw1gpna3TwY3WAVy6S0utt3s8PjEHPibmCGh4saJzJv0td0olnUl2BQiBcjDASh2+w==", "signatures": [{"sig": "MEQCIBr2lc8O1/ZQdwcRda191uACpj/ER3JAn8zqYdR1MyG7AiBpbMX7cXWaFNIaoXHi11JNLpJe2FSAaWRX27oeKPxtGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex4MsCRA9TVsSAnZWagAAkx8P/3soR9A3q+R2n0J+D6Z3\nB/YNm3bmXO21UfNP2kOH2aKEP2GANsZvq3qrwXeQI4ITNsOQH9jGqDNaY8HC\nYzsKSZVswtssGpvCN8Zvu+mFgaTtLi8FnDTrG9cItrLTpcS/0k3fHtAG8xAX\nnUpC4wZ+SWioEc9Ba2TGDsEDY+O1HWOWtHPin0rhf0M/NOddwIiQTj9BcAao\nnWRNv9SViNy1xSsB3sfh+ONqxPymsjaEhV/pkez1OOUwJytxmDBf0M0jh0Mu\n2/au/9/BF92U5PGKj444c1L0kfhctrllmY0VslhrMhlonl16/ofWI6govFuS\nwQS2ImsY67LMXwxtvI0+yND2JYnk/0CcerNeSuGqTCmBoPRS0nc6UkXuPdRx\nZ9q46xP1VzAxSz7ZCh9nLDC0hJ9xfn8/gLSGyiqdh/4hrYRUKTxWF1LSbxX4\nNUJyFVJQ7fDuMXBtTo2eY87I/EEjYdYCZNMUs9tq4p3yoZt2pjelMDr+3TKG\n7qphV9IblVw5rAGZlgN+VGUClfQPySSF1g0SWpSK8k5+UadPvYnaOfi8Gzr8\nzNJqaxMS7CZFXwTB0ZkTlCqJ3l7ynG2P+EHxXIsOdnx09g1YnRBFvPRXYWhl\n9tduSaF+O0eV8GNl71bR/zYiLUs0XNNu79J5yVwaSJfD4L1MeXNqCN9yO/Ed\nydJ2\r\n=2e3l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "355c7941eb46d098217b7c17fd84ff619506044f", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.2_1590133548087_0.013364140882707654", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.3": {"name": "@cloudbase/toolbox", "version": "0.3.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.3", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "c9dcc295238413cf1fbd65741bc131e1af0ce5ca", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.3.tgz", "fileCount": 128, "integrity": "sha512-arpxPZL6Hv9n5ipj5fIbFA6XgTytVXTOWU8e9Z4xEe1eud6jVfnWUgHyhngbC+APERJGJKUsbU8z07tK5jpvvQ==", "signatures": [{"sig": "MEUCIQCCn5xZfF4E3QtJpmZbThHt9xrMHBQbKCTmlWYZQru5hwIgGhwh/EDZWRUIIOGSc1retLtTLxPwwE6TxADZP5b43YI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex7PRCRA9TVsSAnZWagAAVAkP/0Ox9J0iyAo6sIkHxnVw\nRC7foYUTO+jTn6tndvq+wYbZuVqGPTWDCz8pn9TZaqdF2jd9bZ+w98VopzjX\neTu+TkpNOhyDiaHpW3rRlErwKNFbCO08VRQX+RxW+jNg1MlITLf5rLqgPrci\ngtVajk1proZT0W1iwzTEFr8wZPtDgB29RU3o6I8CycdyJCVwDQPzgRy868sc\nszprrId1tE6VTq5isVdS8pMThx5ypjQyT/MpTPil6GUiz3MXaP6iqc8UGB7C\nsuFIZdrRu41JdiKXRzBVC9qDgaHMXQyaMKcSCIkjJW1qqKcsB9OsjavLTvb3\ncI2MAPCXkQLO/jeDBfcoiDFpqsctt00jtv4l0+c4vtFqrugPCnVt9AG7tKLy\n8LmHQ0Vqh9R01HwnKTJjlomKUlvhdBcFf9f82863Numwu8HC8naZ39aSNVbM\nTzQvKk1y8GHUSxwspRNr7K5pe2boNqVbc4g2Kbu7Qs9YY5UHMGEioUx45Ry8\nw7dPQLGv+59w+bBIpnAQKrB/J8PkWRUqhOnwTghrd9hnsUsl71QXE+s25zkB\nIJ17wQkjp4GMLaGJowKYjXWEL5GZ0qGmLdkQYYzdGcirP5+R5/QXENc1JsaA\nKsPqsgI3VM4PpxHXuysn9WvG6CTpfk83G8oq83IgZ1tH5BD0okNXv1mCvv4U\no4QJ\r\n=8+zz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "c5496fcf715affffd1c3e285194d6869068fafc1", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.3_1590146001036_0.6916436338088781", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.4": {"name": "@cloudbase/toolbox", "version": "0.3.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.4", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "4143701b06d8c620b9922d8d72ea0922f30452cf", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.4.tgz", "fileCount": 128, "integrity": "sha512-hKTLcM2MuqgmK8ec7rQTzblWtCAyuGsAjpvt3xkISKcH+ceTTiuj8GyHF/nug2tqGuYIw1NVHG9vxyyozlR26A==", "signatures": [{"sig": "MEQCIAmivxG2fZVi+tgOhBrkjIuLuWSjGaQNLRYDza2NodqgAiByE4njma0nKxMnX4NdnhQMm0kjUHiKHKBUs7i50gEGkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJey0KUCRA9TVsSAnZWagAADDsP/jVyXDn86EtUXc8JFRFZ\nfPkJExeeJHIuMoau/EMSMvHYT9MrZuxZsAgFUQ2aVE/XzuL3sFLm0xMjWDwd\niUNmjBCFmi9ipA2gPz6VzW/QPjevJDi5IP/x/cZjd/TuNR3R4VMOHmuhNC8u\ny/35dUj+uOWhA1ufLB/UuFnIxAaP8hJ/sDN+A80cFo5x8sLOYfLcCwRTk67g\nv/1WuDB6wELV95P7SZlkFQWV3eNFzovsTlhh14L8hIFdVDiDJqr83SzrvcUu\n9yhjb5kYcZezGBYUIZho1fOmiDUfWF+jneuQ+5XNkyJNV9VXDOdINW6jRZ1/\nX+rIlv6KMrrRba0P8RCrlDcnZcW5GEEWNsWr29cbm6oI71QPAHxc4C+H15Vm\nAe5uyDCSROQUFNlpu9Y1NqtG8bRfjRcWYhVrDWtGRAYyuLVjyF0dUigg98ZO\nN2T2L2yoIUrAA2Y5XChHZNztDNmI1j3JAMHwmW6cQYqJbXaZnUzGzb7Rs6uZ\np1YfNkacmT3p6m8IkGdCUL58+IS3jsFvEsmkG5wwcNS9IiuFq8las/KLiwBF\naw3H9CkX/jcQY0PZoNknEPnrK4R3fjGBZAjKYruv5fcFXdGQ9y+3HvkXUSJo\nsa3wQnJL4woxpByURgUox1pNbAcYjk2n8LxHUARJymRdlj/aTHp1w80lW949\nchLB\r\n=VAC0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "70e9002de1f0c50210513e46aca78c9f7907e509", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.4_1590379156134_0.30231178276122295", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.5": {"name": "@cloudbase/toolbox", "version": "0.3.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.5", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "e8ff209e563b056f8d1b28ece211e79cf23b6c99", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.5.tgz", "fileCount": 128, "integrity": "sha512-6VJu7M5TQQcZH4Rfu+/uA4VRpGrHPdGWYO3gyGWRI65TB93BOT494wT990JZpR/I1BLoCr1Evzw1MjmBjwJFSw==", "signatures": [{"sig": "MEYCIQC49Mwbfppv5qJaGNgSxiz2Y65vnup0A3dR1DCYv0mIVwIhANO7adxaxgj+4weQb+hTJt4vVA13xYgaIUbGGaxzLcJC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJey3RACRA9TVsSAnZWagAAyA0QAINvs+MyBRARxbFGO9r+\nS/tvLsSyuJhcyr4pc3hmLm6zcdoUUmiangiGnpFXZUiWIXNH2JeMnOczsQCa\nM2+3Rt28xPSofrqSyq04bNrmMCv2JmGx2a8wMkuR3K/PXO0gr4MwuQ9Erk7+\nRUyWBraeDmwfZszDYoLifJ50vWdWZ1SLeZgD0fV4tYpsTDwgaY9KNjmVJDyp\nR6n5r1HlwidPLXotRZcSz0RlyCVqFNrqoIht7hUfsd2UDFeXT4pIWeH+pIeJ\nCg3JZZeuvNp0atdQyazK1ED2oL84aFqMr+ISkMXzs+NDarTGKuWseiu+g/9Q\nYPl0vvSf1fp9bnyNp6TxStxASCKN0PXs7spCIQ6Z2KZ05aegz8v6mBGoHpfO\nkvW2dl9BkUc5mno4APx/jyWe7b3x3hu2o09BNhsdT1IL/EiqcML7Db3duv4O\nWsraWg7uU6br6NgBu1mHDyM30six37JM5/CTMxV+RJehLFwc6L4NoxVilrYN\n+RyMmI/QzIj0wQ7q/3zXsa48z3I51DK5wQ8sZR4FnQ5TpdLGjzjLwcVzBGjf\nd6npqbZRMhJ2yLfog+fke0YEWqvf4WgvkEIdkaWUcz66/8vaV1S/OP+sf8EI\nxcG94ykNiHWtycR30IfJjh/JeViNcjEXmuP8sgLe00mI4itABtJxHUBI5LN9\nwsWf\r\n=+b2e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "faa9e9a3e6952f94dada40be51c1d14640fe7237", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.5_1590391872422_0.05081708224478332", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.6": {"name": "@cloudbase/toolbox", "version": "0.3.6", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.6", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "4925714386bf82b611f55dca32ace6512836ee30", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.6.tgz", "fileCount": 128, "integrity": "sha512-gFsJqGTj+QabjfGFI5+CQajavWxPV7+dkJ+OjFS/x249xEXidSJ34ZxKuDEMxW4DQ11gg1zrUTwokjo6Lh3u+Q==", "signatures": [{"sig": "MEYCIQCFjU3HSA52/c4XOpKudWsTRaMY9Wvz+ytyKRUE4TLrOgIhAP6LdgJ+u51ZghuVO+3P5YCOHl5Yczvw+2YP4OLuNC7r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezIZGCRA9TVsSAnZWagAAY/YP/2kNUtp35LjtsseyzTkG\nucbhBPnHtcfqSNqlc49zII4JvxA2rHgZQzcLkuDknGK0ebvVW9dN66qEQF/d\neZxEk7/nTeLDUbyCHJn1Z5NPcePNqlQqMeB9AzoEfSFRmx//5XAPGn4psdqp\n18nDHPLVehp0hUPxHJFfkTMRPuL4TSh1SFudWOJzJiUKID/8QmnS3jX3wlJk\nprfvpJKSn2b3xsuFXFfGZULdTJp18BhJB1+qjgMK3H+L/L4RaSbsRQTZxW47\nrpIUsX0eFDIZiMO0GVq9N2vajur2Th8+nCHhVynJN/NPDh+MYD1uGJfo4YV6\nxhs97rLd0vCEx7wPkayJTzMK+5/2J/gVh3x0AoD6Zsefh+e+P59Fz3qCXZO5\nD89Tc9+P1gMVpPpnRcFt6GmDJikrwkg8yEiaFcwgPC31uMrS9o3ulzKcVmsH\nJsJTJt9i55TkEdCWsKLoKhBRYrtj1vP+lY5lNRx71R+CkyiqtQ98jU8qVQK7\nh/j2kOWiMazJveHWFDD7yFM/rwg/QFUX0NS+pEt4lN/iCtfVE/ENKrmQCeEZ\nR4RLFwUo6L8k7QQvKn/Khwus9lrj04GLUO1+6BFDoGWVAdXJjQwjOVLzzObz\nc3UOzxypX5xPYwR/jGsudAc7f+EB0ACt4n9hcbPpY6XF5VwL9+iqNnNsL2V9\nUW+H\r\n=+jlg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "fbfeda023a0fef4e7f2f8b85b2345c202323de3a", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.6_1590462021531_0.036427476188481966", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.7": {"name": "@cloudbase/toolbox", "version": "0.3.7", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.7", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "d6d02673a810dc1bd7266d8e46d3eabffe906db6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.7.tgz", "fileCount": 128, "integrity": "sha512-xnk0h8uZSQJAtaZKdUApxVyQ6rgxnbCixt74fPwYW2yW6ihAG6WF7P4qe7EXATy/H4A4xkSN3G9mrBlmbGrlKg==", "signatures": [{"sig": "MEUCIQD/UaZp8oOac2jbgHFUPxA4SJgtdX62crorY/GIUxYcUAIgdKxk+DkT40yrGRt2yVWq6IlmqRSW0zitUPjtWI5agQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezIliCRA9TVsSAnZWagAAYLMP/j2OUwpMmoMPtUp7GC29\nMgiV4XR+F3rlMRUDdtUjXl/qfiYheMqvqJi27KkXLnXJ302ZOVflUYEeXQOL\nN7UxXcIvDWaTYC6Mje0c2Tno0sN8eMA5zmw5GTCUIBAQG7StNEM/5BKJbbD2\nPTiEjCoWWiExBS9hlv9cKmOigb1VU8vS8ogmibekYReACbgjLFit5JPx6V0O\nTbt51fjnGnD311JadCXRpJVkAoVbCEsPTwebKkgMlBI9zS29hJf3vRDn6+TV\na4KGU/FrzGQ6y72fj0quZEF6QONXFSOCM3GFtb6Ko6aCzhsrPM3MCxiBZ7xv\n9Bo/7MrFjPP39H3EV/8Si+h6POAP+fMOpeHGKi0L1UnFTuTA4sX6MDDgoL/r\nWpNNu7u8WR4xXWEv85+lmbqAPGBGDvmfS23VlYAZPleZZAMj3LWaYp+REl3o\ngGbtSqJRv+WGjwv+gnxSIgcj+Sli2PrBA/DSa4mWkVepDumMhSbYQ1vS1/WA\nRpGOBScfZO+jkLgWcCPUzDXb0GbeqcTuyHOJRoycjtzSEGPGu9NJNK6PGbKX\nCz7GAALf57wCJ6jdJGhT0XuQghltrfH2+y/hUD4tAQUKUWAXQ6FVxgq5qQnG\nq05tC4s6HKWI4PkvTI1/P7stE9cKVcxFztuQlLAbeFrJ5Oou/I6TPVVXmg4r\nOT1n\r\n=DL9H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "f816302e98936efd3adf33e2de331f587571dce3", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.7_1590462818518_0.19761868632402724", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.8": {"name": "@cloudbase/toolbox", "version": "0.3.8", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.8", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "ab8b4427cb6dc0a4f7f17654b47216f1e049efcc", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.8.tgz", "fileCount": 128, "integrity": "sha512-a2tcAVTQc00efYZC9CCWuxuUmAR8wrW10ZGJ9vlbc1TCcOB/mcyAHUstfs8RJy1kb7pIFu5bBwVZMuUV4DLJrA==", "signatures": [{"sig": "MEUCIQC+wuELRHPPK7Sr3bKoFqGCiw+doEW/KiWXz38m+suHzwIgW6YChQxkOdB+5RrfAnZ1zpl+HBfW70L3hF3FUGEnKIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezREHCRA9TVsSAnZWagAAYg4P/iDl1E23nel/r+uOH14V\nSYCelYfUIKxJEr0F4BPqIC91JotVZq79U1myTO9MOOUSyn+8zq1pOS4MUw0b\nORDBKdWemElhUaQE8n2Z27Hk+JkRJHaIRbUu2RaEDo9/pR7PVaQHBgdMW5d2\nAqRZ4s0r+a6V6tQkg8UawjkriUDJwNChJcptylht9xNmOyGZbTaOg0VgyVm3\n0Q6y00O0Ibp7uLgHODx8abfped6u8/ez0lxNx6Aca3iT04GE3M/S0FXCQp3Q\n0NR8pe2cVFaawkL8K1an6wvm2388kig9FJk8CCP2QFjBqQnsAcjokAln+ZVu\nJPq1GaqTzjlpOLcGSI+jSOC0fQdaI5tAgKqucRbpWQGh5CQiz7eM0RcWsSSX\n48Zi1pZccSW4HjN/ffZ2GHEkI7NHYvvUxnpMsBRNkXpi6yL3ch8S2zS3qELK\nX7MZXZwQkAu6SDDoqsC10PAknhPykW7j4gokXZjxVATfeYMl+5F9wWeLcBIS\nxvnrunjh3muX32QwaiZ2hOwG0ACwPS+F3Ks/d98PGb3cZHsqQ6/mj4X6ZfjD\nfFk+nZR7I2vzlPCInCWLvHSb3A8lM3McEk3b2nlbMccPFPvuKX8K+ThvgC70\nT/kSH1g9LbmbIGBU/mNf5ycSJM3xRSITsI2B9rUoFoYxfrZzc7+mg3aLJVDv\n72FS\r\n=NUiT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "907ff277474bfd2ea7d91611594f7740941b33a6", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.8_1590497542810_0.4722666574086092", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.9": {"name": "@cloudbase/toolbox", "version": "0.3.9", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.9", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "b1ce47be32af082adf333a35cd14defef6ceb8d6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.9.tgz", "fileCount": 128, "integrity": "sha512-dGF5NaU4tIy+aA5E8e0xZmzwwX/clsyaJaBuVReubJ4OOcXiwoM1DVqMvsBob6qUYBzWiBW+tWQX9+l7O3K9ug==", "signatures": [{"sig": "MEYCIQDjZslW4/9D3eB5TS8RXtnCj8nZ4wPnhherpfdx3WZDwQIhAIT2dycZLhir+I/n+Bb6gZDiLhdCSaIpsBdwAYMOjP7Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezdzyCRA9TVsSAnZWagAAaS0P/2VFCQMsFK0M+wXLZ09M\n1WA1KG3X2jQZ21SC8KQpumJZM6sVWT6j9aue9KX4iIcIwFv0HhLfaEcQ12Pw\nRyCq+nFcvftK8HBkdIyf6o9UFIDN8XdacKs3mh6DYL5xGJHPkqHUxkbr/FuP\niyVlwl2aQjMqm4Pu9vW1PjNZdpS9sA7/+CAiBb1HtiWQLIdOPMlVK3hYH/kw\nbb264pkO4EpVZphyN6RFKJWv1doevCtc5XKutXTP1kTK9YLrwBWwbH5qBli<PERSON>\nz8amZQAbiKYa5OQYdUiw6ZeRffZoWvU+px1D48BIhPiHPfET+T1KZgUSVrK1\n3h2jLNjU5bKW1yNdv83aGmoxIerupqh6dKvfnndhh8uiR5C3K9WZb2CcqcpP\ns404CP4G4Ckphlkd824mg/6DhEr3dSmnSARutY8lY9vYLM2YnFucKp8XzH9M\ns7OQvRVDoMIKvzQ+Bigr9rM+iplgLzRHPVQRpR2UlD134uXmYlM3+QS8XmPy\nKMn9H1GUzRSFbJyp1K0LpDpGGJcxCt/1x0KamqfpDTMmxFa55f5Wpy32tBVo\nHzEpEyY1dHJ99//AUoPZRZDSis2kO+fxhBYoLNH66n9i17DTvZI6Tn7jmHSq\nlnzCuqZygQtZMyBUJYyQZ1ZMl19z0uz++JLDBasRpwP6B4FpTeSx3ycip4Qi\njoZu\r\n=ozwU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "b6eba2fdcd7025c862c1a907a01e0eac6a8a8f7d", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.9_1590549746019_0.3320847029680216", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.10": {"name": "@cloudbase/toolbox", "version": "0.3.10", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.10", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "ed1d486aa31f979e2f6c9bb54965bae6671478cb", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.10.tgz", "fileCount": 128, "integrity": "sha512-96NEVtrsspR99rgAEGMF8JDOpISsjuog15dKanLJo+DMPHshJIxrTeJU1nrpW7NlSHLUcWI4BlNjwb2tDay9Rg==", "signatures": [{"sig": "MEUCIQCcUvIa2qQP1gvAi4Tal2Bv9/QMWJa3QE5hW8A4zi41XQIgDRUESVbWi2j3w22TcPAsGlnDJ3hPpp9vOvACK3ewvV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezyDWCRA9TVsSAnZWagAAbP4QAJynXTd0ITMPXLlIdGCH\n24QZ8NtYJIrUBBMJO0HN8VT2EV+akhK60dLLje3s2AExxcTvmBRdnCwaUaEb\nmm8zwdP8bxooXyAbcRJnEgZ5OK0v8ge2SW7a7/zDIGFpRC/7VFkv55hWfNp/\nX9AKa38f9VJWsomQ744zUbsvYarc4hwLTn8IIYUkxiFxwqlfmca55V4eu4t3\nBhZ96OHnS2vyu43QhRUP7nIXVHs1IJr/bFORu/duAH0sQRcA7TtEz50Ra2fx\nFXuGapRt+WhpVjgVWUUacnAdsrzp+cOFK64kTOENYq3QPratm5Wrl8w8mqLJ\nuuZbiAbz3nrEyNuT5ovZcfaujK82ZHvdnkCdpFOUeU/coJJXtP/LqDmvi/EE\n9eLLZ2BKB0OPGFzlh4qAwxSvV//8Pm+zwptHcM4Zwkomqk4liFNwQECStnGN\nf9jJTWogKunI3khm/D6tajNlEREtNox6jvb5lIe+y8sXlnQMtTSCVpcRqkkA\n+PboTWnwI6Gg0JFaqV6iBhqOd2dZUNF1gTlFkL0XK2uYagpxZQncI5l06JjB\nRgM0g1yZXw4CX69CAQIgi6QzzdrWN5GoZ6jMIzgJqm1eDEZwa9cwEdgr4K9j\njfp2qcuCfeWXCs697/rT+8JEv+ceqDMc2CMz2TNx1I72m80RhFSzIqX8UYdc\nJYvq\r\n=g3x3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "9ab4b3b49e69fb53138852ecdc9ca5748cacf253", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.10_1590632661577_0.3310857854914919", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.11": {"name": "@cloudbase/toolbox", "version": "0.3.11", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.11", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "ae795c8d5772e54d8f4dfd46b9289f6da19e0926", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.11.tgz", "fileCount": 128, "integrity": "sha512-7OeeT83ZRh4kGCziSvwb3p0K2i9XgNSULeyFisZtIEDZmCSaW4vndhpEC4LYvAYV55evtviXxo9rsrlSIwJfwg==", "signatures": [{"sig": "MEYCIQDAt2Iz5DW8SPLuZgyAsSgi+5w8qAqD0LWcyJh2PAlJIwIhAOPZOXp1wJnk6XwQC96jFPwFiJ4Sfw9PbCCAGnkl8BPQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezyrYCRA9TVsSAnZWagAAa7QP+gLDGhoIjUEliF8yh5Zv\nqduIuwtT6gJgbjIPNov2RqqUHVTTU2uIc9WdO0T354cU67vUU4p5h9ml5Ohn\nade/B16a7fwEdpZ/ML9ZBmUbsmYQhCstnFdPNZxXDNLoZ4W7KZaXHvL8rkc9\n2gjvEGhdg9DnwPdTAkVKAfh+IqyWGQabNrQoBQVXuVvJoNQhRg4EkVM3GPBs\nNEnmJxFO1W9/ri7TqPQL1fY6jo1OncZI9shXPOP1tFw7NRjpMVo3thnF4ZCt\nfW2lOztBYy4zCFfgsJlf4grnV8goJUOlQD9cBkJdY5rrlPyitoAfOYwncAV5\nWS8OnAPYalTPS/h+WG51iRcBqJ1odQik4/Y5zrnckIr740ZiHWUXEznY1sBN\n8PVJZvIMz7WWNohf/sVnrBnmdCSBz0I9DtFoUk0+RGt/DMPD93+0Bx7ypyFj\nzh/8pzWkNJchzCEGUN93qz9uGUwH23UOCvgxuGZ6eU3lQQvyu8DQYJEGz4WG\naXdybcLVVP5ySiA6uEFEUpT8jB7idJ+tmaw9yeC2O5HuV4A2UYXxrVglEeqP\n66DzsKu9bYDqN/VzYW+onEFZ2pIY6sNotUI3tEkS4bEn6LQt3wZbK19MZMQZ\nsqVGnv3jujCznGLFLugOUszyszYEoUW3x1+Z8LwPSCgMVWO2atU/TINUyBdF\nA2x4\r\n=ShvJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a293153d671c1a99a98c2764d4e4e30b1ee08bc5", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.11_1590635224299_0.30673576348026543", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.12": {"name": "@cloudbase/toolbox", "version": "0.3.12", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.12", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "d51a13a422234d60c2a64dacfc9e166a66fbd4b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.12.tgz", "fileCount": 128, "integrity": "sha512-WwMsOKREpqe/+1IMoVTpUU8kxx8o4M5zMthNpPzRLnpsOE186Q62rNnfb/pOXSfnRtFmojaGkpcFssSMODTwOw==", "signatures": [{"sig": "MEQCIGx5Qr3vDmw1jRnyRXyg6ZnNFNDdobJpD29bK8dyuy3KAiAbzZPyNjJW4Ucx/ogK0srCzIWCOdQfQ8VLJGXYXJ8vyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJez2BTCRA9TVsSAnZWagAASmUP/3Xh8NsLu+evC2iik4Sg\ngQf1YbJ9lLgMvdTDw0qx2DoGJgPFRvHbOe2fAcqZEqnYTFOSgmKEozyVcR4H\nKM2AvjW2OdWpjQj3Ma5k7H+IYy+N2mRa+5BA/cJkcyvEuUiAbrldc79a8CJI\n2qYiYR8uFrFUgPUEbH/nKnytGRAWiGxDZskN31OzyQhSyDyL/ONhIJAebYa3\nEwBbfntrqWKMFzlZpe/qYyrGao5mR/XXqHf4UCXAqsU0wfFhbObBmJjG3S0N\n/YtHJ/U5jGGqPSZxEB+TVPjfD9mu/6RzxqyrrQBQZFR2zjoSJx/8a1dkaO4T\n269tJkPzem+nxP8NDqetzZBrY2rBjtAbwpAyh+i95+qOXpeM47XffSg9hzEu\n4/G57Wsiiv4z/Tm1eqc9/k2ovcz8fWfhcwfzheB47+CLPmvJqIPYXtIrPvs6\n07BY88AEb44GnHyuEShm7qvnIHR7CbWOjBEc3UFYoPxXUT3eYJHDX0MZa+Nx\n/mar/4IjgUfX+0w6V+WKAII2dxnBM1s/hcY70lOGt/KbjYv3eMFFE8UOpZ3z\nAs2xWlVVIkO4j8hw00v3iM5zftmf+vu3j30F1prDAre5gnFr8rU5piKpadQZ\ngDn0+M/4OP7/upAeZyka3/Vv5qHNUq8aeY/DHDAPgD4UmbIQuAiCGMXs4Dzc\nIb+G\r\n=mMFy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "cdff080a100d302a042a873fe6f899c9ad9c631a", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.12_1590648915027_0.2419096027472536", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.13": {"name": "@cloudbase/toolbox", "version": "0.3.13", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.13", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "db4162a57a000a443eccb287909d59b54f91f1ce", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.13.tgz", "fileCount": 128, "integrity": "sha512-fRCI9FG2TrH/RHKhSAteZuzr2OeBzskLa8UsC4YU2DJiFl70tOSrwb8rJpD7jKC3wwOgA65dCjkM6fEUz90SkA==", "signatures": [{"sig": "MEUCIQChiHbYbU6gdboNXWmZgqsSRSNk5tWKfzeE8FMoW0tssQIgNaxehPGgMM83tBf50Oo7+jhe3ZpdIP/8IWazJhsjyNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1gtmCRA9TVsSAnZWagAA1eAQAIB1eH0fGSLmlwe7SFaM\nm4nKZipM+zP4Snbgrsms2gi7pk6XREYmRsoombyTPTdio456Zo1HtkSNfYxB\n5DNItB2ynlKULev7yA/Ibi/61JdJFHnyBsbE+oZKDSEaGBiDpu0UcZpViHTx\n94XQdOs1th0464nfkSxgZ4un+qfFM4hOrosgQ5aQoXeLv2r2xApKHtHZuU7U\nHFk6vuxa2Tg1rdPprm+lijAFPmNiuUFd7G/PwIJoLI83MnXAYSIP3p2vRaUD\nJ7z79NWGppOBHXLT6e6XqDoZBgJULl6Hmm0IqJ6sn+Ki2psb6IZA1oarI80u\nNNcs5beGftMcCdeJ7K1fENBlO+Bh2u+QBMq5YJv1QUjIpzG4NtQBeP0ui0Hx\n6P+zA95gJtCOnEsR3MV32gua2KGM4mvNLKmB9sgllibOqYw7+3HgbzFGCGw/\n+NFaNGs3UpucOq0pemAseLrC7xGNOY6/BZtsYObSkzdK+PnPnC0n312l8/Mg\n8gVKtBKWgRXAdYv4KkbdIm67P9qrAYITN4XyULqMwsHOlqA/SF57g1nVF8p+\nxIvvyOOuyPqWsp51YdL1RaRj0DHfGUh02sZBmIhl9t8S9LTNB404Ws1vdmaU\nu/cb/V0qbEmKUnjLyMv0L+WeNKqlfTRSwu18ukKSzEUWbsohc6DKh03JOYb/\nuY89\r\n=+1KN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "5b471fad815a0909bfc457dbf956d75c81a73cc6", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.13_1591085926089_0.5236200652115166", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.14": {"name": "@cloudbase/toolbox", "version": "0.3.14", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.14", "maintainers": [{"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "930a4d06d9e651723bbb177c3ce93ecd56a669c5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.14.tgz", "fileCount": 128, "integrity": "sha512-/3KDeuiVQjfqh0eubdgfzRw1Bi4tEiyROEKctq4ArRdHY60j8ZLCOfnY0ASpwIxmVWvK9vdT+v04uGr6TXd1tQ==", "signatures": [{"sig": "MEQCIBrB+2bIxHArqkvHLgOi6dk3PKL7GjDdrkjGvWMdgaG1AiAyb9uVJ3xNzqdmdjPc88n5uTK1SGwQ7fGILXZP4YBk7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2NgfCRA9TVsSAnZWagAAq1sP/R5Qt7Jg7K1b3E34mwwl\nxgPspIoNesbL5TvoYxBn0CeSE0l+0Tqix3IOtb3AR1RyEtmSYmPeQvnQSv/6\nH3isKE2Phu+mOdCfXmsm6RbxgvUTUnJcqktWJ5tnBZ4iwM1nip4RMc9PcT+F\ndNW2MPclFp7VilA7ICzybhCYWCI4CWOGaETMHSNtEhc1dBDsjglx7c77sLbL\n7XIC628oMN7QrGsEkESbae4MZYxQMrR8sYHJEU/Lw9F2Kbta2kGheM/wTZ4A\nEJ90MY7m8bKkAhMOwcuSU5gWyjLMFguwNqbAeCEozd9UZqm6XM8wfI4Fy8oI\nYHZQYRVcFK1PuZm6AFoxLP3PdNdexgV/eQ/7+1JXVnyIfaxIxb2ZmHbRdeNe\nN45eoMs13VwuPPJi+L3SURIVOFP2RVGW+KjrP6nCgpPgROJxY29bg38s1uxK\n3ieu/F5hRvY69HvvmxyW36+UzLCNGGz7Giwct7nBFcMY0FeRzkvrck4mMl9f\n8KzBRrhRVmIfe3rOOIoNzPPxQ4oXlKB+JExQ7nWWz6gtD8FVAsQiB1X3ruq3\n2NffKGJwBMhtMp5YSTwS8cCGTNoN66KVuxh1gmCHf6W7nFSRmmg19sBiUf70\nBsV6jDmhfWiBKlUgoREfAZXrQ+US85NdHVAvTWso/u61AJV9YdW5deyIIqqz\nl6zo\r\n=GOxR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a92322539071ac11792e778985aee5a7ddf9cd62", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.14_1591269407206_0.45253713451190825", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.15": {"name": "@cloudbase/toolbox", "version": "0.3.15", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.15", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "c2e3cb89cb0a66adbf12857e840e5a2d5918a0a5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.15.tgz", "fileCount": 127, "integrity": "sha512-e6ljbjgn8nI2mlsvSTIBcN2pHfuJxqIblNK05vCDW7yMqOhh/bWQSrsNn3PT6WkbAj1UNCPyr6E054hPk1liOg==", "signatures": [{"sig": "MEQCIAyMOoEE8l8VGL/Fu0dGQeCByr81oQ5Wlk9pmlT+wFA/AiBPoKkG34ZyZufsmfG0MAbcfiuYndoUIs98tofwayDjZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe63ZkCRA9TVsSAnZWagAAbOEP/AlbKbCELGTQnNJCLJzp\npShmZKw7fBJAAW0H82Y0/5aft/yKoFmPzIucMMHLB9AO6uOOV3FA6mngTGF4\nVFuBCnQDrsHKv9L86Q6S613NuvEvYJodGgZTEPSLOaCkQLpBbFA1/q9KlF3B\nvbpKNJsEs5YWHWofVo8LRPZn0cXDTgM1fgPVGO+OEAiRTNV257PXNFLq4RsV\nYezkDUsxCaLffsSHSCzZt+/omkpIhCtpF/jp7yd9Y0zMge/7Z0IyuIasIAXu\nd4YBSZb7Mu9O9zZhkdwKdXr7D0NDYQo/Hk5lonAHRY1ZF3sgjatNSC8QLZ1r\nyfAoL8sUsiybCj1Gc5TsQOqImSku75vjh+3coMpk4pbkzbmp8PBwNFzHrnEq\nDEdmkw1pU2All5EIha6FZFPi6Pb7fdLxe6kzr8YO0S2Knj31TnL819jGm0mN\n9+T42mz7p2A17VybbiN5kiS/BqWa2ArhAJW/8gGW4r8un2aMBGu16DRgmNUW\nlieDUypNo9+HlFdjT6MbasHokZGO4A4dk0MioAB1rwaBdRLMfs1xwf0O2KhD\nEATu1Comtjd3BfM4xcBZN7RI2X8s1M8/LAi6ErbI6y54L8mWU0UhXSYT0ogn\n9YUKXebvOmJXUbwIFt29QaISOQgNGxVaZ18JSHEMEUHqPRwDlLEz7I9Gs0Uc\nFWVy\r\n=ELd7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "5f4496b627cc130ff4f498d879882754640f269d", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.12.1", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.15_1592489572462_0.6597834701965195", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.16": {"name": "@cloudbase/toolbox", "version": "0.3.16", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.16", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "8ed33ee79e695e403e6254061c00c05de69e38f0", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.16.tgz", "fileCount": 127, "integrity": "sha512-x3oLhRGXtyi0bqrFLgRury8f1x/onYyu4G46tLFQzlfWGso6jn39Q2f/fmau1sJK1F1vu2udaurid2TAz5WU+A==", "signatures": [{"sig": "MEUCIQCKLeFE3Qfw1QtPoF+Itacq+4MqJC9LYXgu8Kp5HykHGwIgP1sRst7AO2XfbN5rLzj8x+FEbJ8nBrtomAjbf6Hf95I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7BkXCRA9TVsSAnZWagAAQGwP/1oxXcChQC4rFcxuxgE1\n9ugGBBl4dEf6LNFfZjoxMog8VsabpmnQTCu7LLQ3uPC6IrwsQXFoqRUD4LE5\nTFvYjwI9HPp9dSZCCaAMb+/E+DNLTC/slS9L74YG+SF5JIXEe/xOaK7tVohZ\nFMLfOraDgfTZOiBr4N/dNM7FuFE7CkRUAZBHNoSoguvOuL2JC0mbdbHQKVBA\nrupFr8Tk5K8hfBio4i3RSXU7m5yU+Rg1veeByWVhDUyurk973eCu6WOGRXMI\n/9zzTSeWpnUDS3y7B/RTKd96fiABXGTKWvXrd9+mQjGO67USKLOwGBiya7O4\nsttHcYECS+GDfdTCjMMrinQv/QARCUCdA3+mL8ST+QKaZvnHiEdIvjKG6ulG\n9mvOfZBNQqsulTjxuMpLuua5nYg2vjNzMYO49AmoDC+e8JCnHmVQhs1XbU3U\n6oIasas3IQf2YEFpCetAOLkqHAM/nm8jKbE7RjOklyGgFIM0ptbfO+2iIVWd\njJNnQuNp3aHRgwgEmyW6PH/QeZPHb8pg57NSEHKcupx28Dhr5DGMTUuvU0RH\nvJgyJAYSGPXns3KbBe8f0ovV/k/a8PW86dggTjo/04U5X0gqFniyUkUwzkvr\n8iddU+/hpzgjferEC8K/heZsvzD+P2aHfULFGDynZEUEfufMVpmj1JP4xUNv\nDxxP\r\n=UPve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "1381d198cdf267782b79b19414f01601fc11e25f", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.12.1", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.16_1592531222753_0.45325691818721303", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.17": {"name": "@cloudbase/toolbox", "version": "0.3.17", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.17", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "11b3ed74bc58cf47c0af1ee46f57fd57db37f994", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.17.tgz", "fileCount": 128, "integrity": "sha512-h02qxvrGXnkKkfXXrt95/+zTM2nyY14TUKcSYk7bycBvBcfdM2qNZVzFKi0xr4esrID1Fa7/FXMIE+2lqBzFzg==", "signatures": [{"sig": "MEUCIDWXwQ5vV+f5Cz/OFnSPh2CY6hv9LaWJ50w0NoszNtdhAiEAtrmZcvRPBfzXUEMX7Z8+CslEadgPRTQSQxE/XuiHKfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8BOQCRA9TVsSAnZWagAAPrYP/2GPnLwojNkjVf5jtOHr\nrhSDB4HulVN7Pgmr5A+iOAXvcQxb31hX41etJbDVlbjjfNk9wF6o62bSnjgU\nhKk+5tNx7QxegG4khHxanrrJHdRii2xo6dKjsFr+kHqTlgxAodsvt7gyr60G\nDmcUuCzVHrZ/M+urIaWPFRnCH3Yv0UOY9Zp+8Fc4/3KZawdBwZrndNj3KMDO\nvtHQqIJ/YCPNpzOiIzCauPX2fVG+nR/Ht2YzXncvU7Ungwr5qI5msXRRgJzx\numat7FkFZ+rTab/YJSFfV591yNhVPL3OVo+Lv25MiAmyluBcm+OeYlOOCKyF\nJ0Su9PV2riUG+7QlpypMP+E4m31qGWgRn+ANglDKU4L1Nyuev6SlRR2dA0ZX\nnn1O4ndmWsX2md2hfgvF54yir+ATJ8qTDhGz/Zme0YpCOgQnUALvxMZwCsfs\n71fTLhMJ6XZ737XgeHCoGwTZLMPem1RyohCWI+GdKIXn6lydUyJE6+WK72fX\nlB8hiumJbu4Govqufsg7kURmKiCzPKP3BunA+L64UPaVK46gCYD0oH8Zde3f\n//Eqo4poHMqFrvflHe57iydm6SFdm8g3Usu/YM7ShDPiLP72YUkEhjETHTvo\nuJdpSiGTsNgrKNuBG5pDFZCJ32dBmDwtrhGqG8j08rR6BxOanzL/UDgwh/w2\nFyjf\r\n=znwN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "25337f59930a3f8b0d71296927e4120268ff4cd3", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.17_1592791951516_0.3354418137161814", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.18": {"name": "@cloudbase/toolbox", "version": "0.3.18", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.18", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "8b497bbb5d3a836898874edbb6085db80bc9f791", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.18.tgz", "fileCount": 129, "integrity": "sha512-AqFu32fwAxGsJSdZuLGL+aJgFh2aIfFJaUGQ3ZoRKSrUWC6hutAnWyBEPmFdx1ziwxYZuCSr8xY9SSP4dV2TrQ==", "signatures": [{"sig": "MEUCIQCzwJKWNSrvJquFN2cNGuRYZp3HWfoofeZonqwRUYbQKgIgN37/3E2Km5ox8mrMUnhYoLY9Adax3twudRmCG4FEMCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8cYECRA9TVsSAnZWagAAhDEP/2vOAcj43u4PdCzbjMDD\nsSfUa+D7kN7dPtlBgmEivf/NBvkhytD28VnEGQJjGM8qaE5v+bCbwJgcljFk\nNkCQnEu47rckyOeNM8MR8M/6tCXAXDHR8dweOSZx3ckVfkPJbcd1ZweanUUy\nwu37+GSjcxPgCPHEVIuJQxugM87miXKyirNRecYSIePBZu2jxG2sJ+SxE1OL\nDfQQNCG2Ovde1qa4jOh9yTPxXq0MsXWiWTLasM/X9j8w1kGeLHBd9K9DrDJv\nGkA441fRQ1o5f8bUWkM4xcAXEQusoFVnLEuI9W3l+gnsQK+A6xXMOr4iqUrL\n8pSHLtmmqSY2f7Svv+Tp0ghcHb8OpOK4WoJvtrQkHoQVCG5LbegqbUsfaN4O\nPr9Y3u2WwFi9P+6JtirU3xNRqUvWnW5eEkODzyQoT51D/uyz8t29GJcUFJ4W\nmuzigFM6woR6nGX+LLEwRnmwPuK0vppR8WXm7ys41ZxdV4I5/aZ5Nj4P+4Ak\n4IUFsSbDLjPCujivl59NgMOnpoSFYPbPZyPNLa6nIu45M48B/4GoXhru01/l\nG/og5lZlBfqt/HszHpDGHvnUb5cAkNDoT7wgRnn7nOUItRJxUlshWPzJ3G41\nC6HEDd6om46L4s2EQbsfHQS+GDw/bNuVXJ1ophK5yrIyzCRPndNHB5fSQ0bX\noTCR\r\n=cCkd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "65e5fb586ebe0c016ff99b4cb9c2171ac3690a49", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.18_1592903171647_0.41713822544251533", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.19": {"name": "@cloudbase/toolbox", "version": "0.3.19", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.19", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "9cf05c9418521cb4097b744a56fa76de5f7fae5f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.19.tgz", "fileCount": 129, "integrity": "sha512-c3rlgRaSwCESwf5lyZkCN9/3MY7uKgOqBUvGrukpTrPKGZJDLnPSO+rvDUAC+N2GOE+W+5JGzZw58rtJdz5Cvw==", "signatures": [{"sig": "MEUCIQD7MDjQTWwRcIl5bO54iZJsECq93bqsHap60xUpevujhwIgbbMQiqF71CLWVrT42IAoIcDgCnMMNUnkTwMkXDqRLQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8sRXCRA9TVsSAnZWagAAirEP/30aY0w2TQx7Svd2ToxE\nOTORJQZw1Tiu2yRB73ThjFTXzasH4Z86BgJIO1USSxCb4H289LM8T2G8/4/K\nTSAXP7+xuEwqFwZsRlO13cBkS1kCJ/6FcqcOOqi7POot9oc4ssCbhnyGfGa/\npxgkE5O7aUPOKoDvbcnW0CJh77ljtoZ4eoRd0q33ohXodHPa2xjtmGUoDEhe\nIwZ0DGP2tylyiu3Lm+lvakwhZtbuHAqGxbUkJj2Hq6ac66PSo+lfvXUTzASI\nWavqlDYCXFzpU+auyRFTFDI236o+1j3jyxnAqPkD9GxlwR41MWc5/TvfktkS\nkcPI8nZy81WVzbrmHOtkdLGt//uip5lEuBGwR4Izfgac1RPuaAWYKGh+iwb6\n08TLGhBvmMsA4/Doh0XxZ1S4FhDlWitQduMGUy4Ndyu6auF8IQVSer+AxgMI\nG3LRZRCpixrI8E+KBFVF4yQ6o4+rdnwssCyE/lBK8ATjPCGYmx/ntjsU9vIA\nUjnKnb8ytHN8ood0FolZ92iqsFFD9Q37OKBvwrsX1FQagjEdccR3KUwEto57\nZ3sOG3SHZPHS25ejopCDKbBzXW75HuSOSg6PvLBXRJ9g9KRITWEFMw6UhSWi\nAhRvX+KkAutja/QEX+nj32DYiHXm7rlcrwJ7OCNLbUzjSPYRPCYluzX3mORA\nAKeO\r\n=P2a7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "66a944b8ee6478ecc36c1f43b3f939324b705d10", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "make-dir": "^3.0.2", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.19_1592968279211_0.06189415334251769", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.20": {"name": "@cloudbase/toolbox", "version": "0.3.20", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.20", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "0e6e50cc4134868fc569747fd3ef04e2b098824e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.20.tgz", "fileCount": 129, "integrity": "sha512-BnM8diKiPFppX8fQxknSDBoclDp7Er6iD6AdIw84REaiHHvllYxlnYaAujPXwkmDjkNte2+QJvmypE2ByxxzpA==", "signatures": [{"sig": "MEQCIH9dDCQPjbsnWH9hhjYemp87ySIfdNNhYC1lBEF29kj9AiB+594F7tnA7pUEPNEbUSZkAj61I3otGZ/J0KDW/MN8hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA+U9CRA9TVsSAnZWagAAnYQP/1Lg6lKyoZQLT2K/CWnI\ngMRZoDqYZJTfcn7GSi7H9yJJx62rZ5geCp59yuJb1V9L99O5hd7TQSYbUfmZ\noRp3HC8yF9R6Pjqwj7udC6NBAE5bS/GYlvPCWSMu7nIuE7I6b9qDPff+MKQV\nSh6rFidvGyC4NBiDjwxL9G0j58AKLminyYy5tVV8Ml45OXbPoDtaVGbJQG23\na8NK3wyAlF2I/VQNERE1Dyo0/YEbE3Jg+YY0msvPYGtb2YEJY/0ZALAhJDJX\n4rqnP8QXpP0dSFFd129sEx1q2v7FbFUxUfd1ChHiMnVuQhp+zD71bfjg1rBn\npQCfHgXx/vRrNVoLg0hASZ18Dk99uf9CdHJQM+HSLHm3nKXehIf0gFLHlyTJ\nW6gbbabySueKyCyLLePApzMM6YwZ1kU84jB4cPmPTiLiGE8I3WVQO2d3IoZL\nHNaURRdi0n3GSIDQ2sEHr1KoXZTLWNetqzyj9hlpYtatKrrT9BvtPsl29Ulp\nlKOaMjHGZ/Xzc5yJ6KN9x/P+6zYehM3qjZNzLn4afvq6JZ2bD8pykY3tbVA9\njbC2FoCVb2Q/qwRLHHF5iml2vOjMpmaF6O3CdXiODuLDYk7QMx1/ASjthEkW\nrwclwCDB66otfrtHIFU0vdfDIPZXM09S+fvaGxUFuEv9HNuE/H5tnUcfGOhX\ng74l\r\n=c3x/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "9f679673008fd434f60d21f831d209756e392c7b", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.20_1594090813392_0.3685826204558891", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.21": {"name": "@cloudbase/toolbox", "version": "0.3.21", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.21", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "501d7e94b6b073fb4e044867f9b5bda19f002057", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.21.tgz", "fileCount": 129, "integrity": "sha512-FlczbGofg/JGGaXjxGh5O1VaHQaX4flCcJdfFUfK8Vps2nQR0gEyNtN29fAXEmRv7g66jlmgfIyIvu2q9HUBbA==", "signatures": [{"sig": "MEQCIBVP7+H0soEtljSvWXxMLl9J3t33rFda3V9JJQu+YZ7IAiAYPDu3G3JYvO/dpoYbP0xd0GV+VqAqcnq2hSaoDMQZnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA+gnCRA9TVsSAnZWagAA+pkQAIlU6IabYUSA72JOAkxL\nXG/1b6IUjIpNv5tRpnQdEh9q8hSh37zFNzSE9sApYcDrdG2OzDralRT8dEtq\neRsUAgYMouPHchguYmeGBVOcysiGhJZKWnENeOVvuutidPY1gbPqMB54ok+e\n8131HOI+49wXU2LnbT3YPOGny94BOQrAb5UwFcEfnp2GkIGOec2kKnCDR7yo\nuJLa2rFH23KdHWewgdMGVrGV6vpC93oP2EIC6GgTT+otC2m2XA4FwOHzUSTV\nmY32YNF6x3YTOd7LhQtiASVPaYdRmgGtKdA81mhCuibLwPSq6U4FmES1m7oW\nZI8GztbXrQKa9DzYndcrC+vPmFDrAiZvzLgYhwGsjUvmeJufMDoXaLvgFcSK\nCpVsOhXwbgph2bwOyvpNPl7mxiK0gMap0DjSgz9GJ4nodyPOaTL+5CmXGS52\nrnYU+kvQY8HZZM6Niabh3rr+bujFEv24J5ZBKAIxakkhgew1zLp4UNSq97QO\nu/k4ZnCi9DyJyw+cIwMYNceHy+4kW8BM6Vag2rrp4QZJAgUtKu92l1R45fGk\ni6rBEyPaI6PzHqgqWSMXVZ+cp/3Yuj/AtWIDgV+t4UI9NJGs6HBeWNKfW0gB\n+HzAgUm7qvOwJdM/CSf+Gr19myGZ8CCA64m8oSf3M5GOVY035LHMy5bgyW1P\n8hcV\r\n=2T8q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "93ece4c6614e650a947c447a68c3242077d0c3a8", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.21_1594091559237_0.43636553221353", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.22": {"name": "@cloudbase/toolbox", "version": "0.3.22", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.22", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "fa6a3a96898eaf143de0d9dc81bb80ebfac2813a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.22.tgz", "fileCount": 129, "integrity": "sha512-KeXW7iZMFfFRzrgnBdM1N5dyzA7czAubzlxqIZIWqAQp8T9KjrpAJB9XF0aE3hfkiybdN1dj1NaqP8Ka6S4jTQ==", "signatures": [{"sig": "MEUCIQDQwp7JQj2z1ULxLGOi+kIF4KsELk/MBWlKWttjysv6FwIgDkPRQqZnP2SENEXwJ1zaT9q9LfIqrMprlbqzmTetdjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBCH/CRA9TVsSAnZWagAAyD0QAJwZaIX0Ov1FNPMJBv3Q\nfSMQg75SbNSJFifgrPT0XVX5oCDyLEPb55sqxA94tzvSz+N5dEkqu2mOk108\nNAmLdnBmmTU7Dc6uKjB9+u2OCg5K+xEngTN9UBbdtk77ouEzq0xRp9gBtAAV\nLA7pnGRgHlsLoKczSewNmGSC028fySS5QOxkHUMEA/ozJ7HFrlXIgLTPnLoc\njqhBOUOBG8KMr+ijGTW6FT7yMig8JpJFLlS+UfBQYMJEWm03VTyjYOtJtlcZ\nTXspgAh/agfzehZ0vJZFLBf/BfI++ZHhs38I84ZkuxwzcTRre2JxBCM9vV9v\nmW/MDsOKNWwAsQ1frtgA12oZ43vziWvV08mQ1NpADbmSW/oi3lY4f9RVCWyt\niNZtV25YNHK18LYuNbZbZbPQtcfaus5xN4sm5VIC4SEIIZgLF4JL99Z3scxW\nh+RsoRTBGMoP7Vm1IWiwLSrVff0lnnPCuBZhENH5x4Cn70MEgFhzEF+XoZi/\nNvfDQlOFgjihmwUfsVDnP2dppNjtKUDKLdsXMonPfk39kIEWYqlKCFena5CD\nKHihGcZb5Pr9Ee/7wRW0DXIZ1iC1iIpe5aJmgpSYfiaawfNrVfZet+PjP91N\nnaZcLHRt3fNxF9G6fV/mGT6OpFdxB/UKbikyhngOhuGCmVvkBeLrN7ely4TP\nSoUh\r\n=CpU/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "219728a25ed8e7490d3a743601e3382cc9ffc5de", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.22_1594106367449_0.6440045523335418", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.23": {"name": "@cloudbase/toolbox", "version": "0.3.23", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.23", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "1da247cd5196ff5fb4a6790f87722bdee2d59194", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.23.tgz", "fileCount": 129, "integrity": "sha512-P6g6KMAl0jvaBKK4TSdBRCbtCKMx0mWeE4U7FeFz15ilge03FgVWPvYKjgUMd08bOGThgVTTGchLm30OtH3c8Q==", "signatures": [{"sig": "MEQCIFzSH+uKSgSE/lum0+6aEEQwF1aPqAk8e2FCsXzVdiluAiA063slGasUOjNBHKdbXpU7gNeTPkuORzlW4E/GPSxHkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBDyFCRA9TVsSAnZWagAAcEIP/3sW0YL8jraGcA3HrfJF\n5mgwOmkQPjmSvntEWzv02gmFLDifMq6YoJL+FEvc7bZGRkjaUXENNbsi2M74\n2OjwlhMa035N8HBVEckEoDqaL/nkdVGUh85UCu9nj3/fAs8WjZAESjyRFmFT\nZYBzTyiwOO5TG+xHqO9ScoODmRLzNSzkIiIm1VxLM1lgkMu3yc6ekwSd/oG0\n9T+f+3eBnPe8B6SlceymU7ti9RReEasthoaJK1USajsFeA/ihTnT+yQ9/aKF\naNDjhHpWVtKFO+D8CSmZs0Cn5ouFmdJUMBrZ2Mf8HIM4QqJ5Tbq+pDXKdL4m\nUUKUHUtXKLwUDGqWnHw6+vWPrkop+/rGHyXT/3H5G++76mylMM4qk6DoSnLT\nRdKljB/pmboe+dls4HMjBLKOgCECi/ilwbVmMvQAcEGeYaCu5RmilbcE4mSO\nCF07Nuzw3K6XKLJBSDim8KIyYQ3p2/wV9kud5jq+wXA336GBkdnr0cw3gu4q\n+Cg45R0dL0i94va6MYlZWTmNwvHusEtCkLyHrV8k94zvg/vW/Kvj18nhBLRG\nZIOC6zN2IATm9aQE1GXWyEo7ONdRsPti5EqqHpNCZNZE/zkgDQ/fXNqidXKs\nVHtt861Ey9INqMK5LZ4sekiYLXk8lnN0Scn4H38DMTuyInySIPFXsJaqL/AB\nn8iz\r\n=S/j2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "bad843f834db35e7f61f70a6767bb2d0e95645cd", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.23_1594113157434_0.1566647843532638", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.24": {"name": "@cloudbase/toolbox", "version": "0.3.24", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.3.24", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "6802c0deea5e5f4a74745533eef79bf44cae1b03", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.3.24.tgz", "fileCount": 129, "integrity": "sha512-0MANVBFPGKcAfnKeEL7eCbZfMFzRN2HC64CUbsHHrAyjc9yDehWLcjZsCVi/XX/TICvhVrfaxKRDTPwczadOOg==", "signatures": [{"sig": "MEUCIC4JHelr6t4+/CQRIw5csCJ/laf1DVFFoMjJDyjvScGhAiEAtn1fyGv3cvuEv0vPCnanRYaM2/UOuDjAwWDpovt2UuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBD24CRA9TVsSAnZWagAAYVMQAJ3jngtjw4UtUmD1qx8R\nHIa0w6vSHbc64fMOYV09CnkZGUNW+6i9/Vhd6AyS+czF0gjjdv2seKRCu/ji\nanCS/lHnXl8b4kxVijh4d3KMVseSVu1d/4AMv7p5ljioG/UjPAz6nqNvbD5u\nerS3PHaUj4uXz+HIYZoyCp18Ge1Ht+YUp5pCZ5kasKepfRf+ZKtEreIiNnK5\nqYSE1+EQwN3eC2ff+v+kpG2JfA7eMZegqwuTTRDZaxyXbOvceflGPBOU00Tb\nmnBSEMPRUp9cBVTeEcg68fsvoqoniHkOkgmZfwVECvUwikbE2FwUI8HGaA2F\n4/gH5idE1uP4qy172WMA5R3l9j1zsIxtyaIAol6ToYhUYgHyt//viEkl1s/q\nRLcZxKViZ88zgIHCA5cgiEpTqHCUBA6OoH44ZKcCEfIG24XInW4ot7llc2gm\nxxQ7n5Ufmr0PfY6TfyQOO4bQsV0O9h7Aj6hZRYeifxRKHhWKvkd1WehVHiID\nmV95uo00pKeCsVtWKEodQxXza03YdmvZU9L++9TnNCdehxUlO53au0N3X4gN\nr07WefG9xJ8O5C00DZiDcq4rIEP/IUF9SwabOmjgaK1MOWNgSIdyX2MV9knD\nGW7Gy6YPF6IHnv+B0wjUWiwaT9sAXhCIHsJviJAeZofS00heHkSwvHtM+Da4\nt/NG\r\n=Lz7j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "8e16a748a346899f5fd65a94bf797dcb551129bc", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "buildt": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.3.24_1594113464530_0.7399681067205957", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.0": {"name": "@cloudbase/toolbox", "version": "0.4.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.0", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "148d080f081d7cd40c6c48e07449400cab26f01c", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.0.tgz", "fileCount": 138, "integrity": "sha512-TOnCZHNxFPjrZArk7kC6vCjMjCDb0gQLZFfebhkMndZN0ax6W4oiUzZuy0sBldH8KQqEGt2m592QV5pqxNvA1w==", "signatures": [{"sig": "MEQCIDO3rh98b4TOlz8Vo/XW5wkqGSAATANLjvIxBrSx065qAiBnNUoyeoyoJ0bgZh5vGMjSJ3/L2OmtJPgGtA+40HFI6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDnCnCRA9TVsSAnZWagAAo2wP/jj3Pys9vhsbRTleFWt0\ns7yf1envU95kz0BuiYeGSTzODmFz6TiGHqEOkOogTx2jk+KLWbDXkDy+YCJA\nH6V/Wh5h35GHYAOiCT3gA73un+Q2mVLPi6gsucACq5TsZQ1PFGsdK7xkIJkI\nTOz85CE0faQ0FG9UOFCpHHBXfwtylCYhH31f7sxlNPSz1gVEvxd7pv8siQXW\nNBT17vPPIvmAu6nHIJVtuWKrwf2c5/cZK1jXznSpzTjcwtHqR/PKTIgaJF1S\n+IWkpPjxzFGjjbq7t3YRfil7LB0xczDllTtnpCgxKWvC7QBeYxuRNo36wTjQ\nZDft4NgBmdJtpctHbaMSM8FQcDEXgdoAuduTTysqClB+8zDESGdv5Z4gOPc/\ngEBTBE1CPLl8vQ6PeU3qxcQJpwC1ejJ1iIiCAcWvStVe7Qb5gRTboXqYgvEX\n/Xp0tuMC6Gd3VkM1gUZVR1J5cLqMW7qj41IdfFVMrO1VJrXyPgJ25a+n2jyy\ngFfossRdRhWAFua7jQbGI2ed7wMSjvccaseLZPvqyRVCwsyqba/0SYzsE4L0\ndSU0NAXMH3JsgnbArCEFPFZaKD+hkMHsB+m4xXbWeHj4gDxHX8u7T/lEtzrX\nMUJgdWneyhxa5TLeoigIUForAof4Z8Swaxluk4EgyyM95do9PO803JePVlVl\nr2W5\r\n=cMVx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a7a49cfb072f490f5fd30e5ae3f100e90b574517", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.0_1594781863078_0.6832642301210787", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1": {"name": "@cloudbase/toolbox", "version": "0.4.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.1", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "8f399cf2d416e1d71f67ed6f5e83c382c5754173", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.1.tgz", "fileCount": 138, "integrity": "sha512-y2yyAC17jFNju1IzdGmdArYfwY933eD3ozJ6CoZmp9KymJD7vdhfrTLeMJQbFGTj0xrDM/gnSITCpAplCvFYMw==", "signatures": [{"sig": "MEUCID5YxUWKuxu3Xl8lIs5j1ohlJVRAeUyEnqSrFAvZzU4AAiEAqjaBKdm32cyWEwS3yrOG90Zz2v/YmDoayxr3YUY63Yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFVNaCRA9TVsSAnZWagAA0P8P/jH7BB9uivyPTkbMHDZg\nlhY+0mRGENUzWxhuwM+IRtIP+k0COMdPd4iHZFF0GXwOjfpl5bf1zqkl4BkT\niPBqSDATlT58klui5MTFMoHktRGt9RXg91wNN5+7rKQnewifdyDJKhLQCWfA\nfLOn3MApPtc+i3WmIB8yktCmUoZjXLhs0Rne8sKoMXtC540m4dLjjb5lplNz\n/HGQNh53m07RWQKnDxPwFpiGHQwUOr3M2GStjZ/KxWsc19ZYVX1F8RxnP0h9\nE1CEivIO7BhijSki1P1nOW+IzK1GepJ/DhLQ0ADGlDqHtdM4K43hCjef6Xjx\nL8HqLhIzhsmDhOnD/SCvJSTJsZnsLNEJyxwnkCyAVquGPoRXzJtgCzQbb5Gl\nPBhRmdfrWLliMh2aOghxOkc/RQcxWQhDxKbT86xG11fvt0NSVnsBAHo8jCbn\nw7vh2brrS4Xw/Q3ALYsmtHzv7Lz4QOUVTAD4eii6pq2iF1+iWqYd0ZxSipPf\n+aexrFJycb9LkMYlmYNo6ZeH3AEBcR4kZiZW+0xmyhWg3jWHRiaqoHLCZgdH\nEpXge4lAKGGiYuJBXq+q6ZUnX7FfkC6E62gzc5aQhmd3Hr9MNj7tbfA5L3nD\ne5dCSoS9gtz152liUwvFQdzMJwZ1mRkXBvc4TI822fEAIKwHKLgSKEHtM/f/\nhnnD\r\n=BwK1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "c23a8f23e20f8fc13f739bd3ac34ab2a0be34775", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.1_1595233114137_0.7469433968682471", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.2": {"name": "@cloudbase/toolbox", "version": "0.4.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.2", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "f6b2e03b5a6f86318e02d19409221ec8f1134645", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.2.tgz", "fileCount": 138, "integrity": "sha512-4Huc06s1EFQgQKRPIcm0HCtpwJ8JCP1DY3ay0UL2fzyZ0y+S1g9IXgpbpl/u5BlVn//f6mAAXtDq5ywVyh22lA==", "signatures": [{"sig": "MEUCIQCuhiU6KJYWSFGfLYsh2sEgdbe0Qvvg0W1s1CDAejUexAIgP/hhjziHO7+jrQiMj/AQqQ28dprZXbTRT8aqZ5m7vGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFVXUCRA9TVsSAnZWagAAsQ8P/1a/x2sdgo15Lsf2CDn6\nMwF5MhqCsGZiKU9IirljsfFt9rJAl3XCS0P9pVfHLhJIQmXJDUxhnbUVZWUi\nLQgrisUbMlgLvWf4go6gsuKydGNmRm20usru//qkpUOsZgcvTX/blsbDa/qk\nKcHNFk8TQElzsHWjDH5bQpLRQ97hB7GYjZLiA3RUTlSEtLC6IJu/FXF80A2y\nXjz4UWQ4vkrgiaHnNWIozoOQDseeGzAQhWMqwJEV2CQh/Bj1FcLmRBcxNGFm\ny2wpPd5dXylvI88ePvPECnB1Td1p+NGCGFJJDPU3cmHixxNph8l7R8OTF6gz\nvOAZw0NIv1QB5F7F6RPUBixLltXHhJrY8DGm43UpAK1ZySoCTDsjIiwCMiqn\ndJnEtG6GP+2fgJk0IsnUjJ5J9a8k9mYPiD8IVrXwugYxlcRCLPBWh5LyETKK\n8p883kdg6ru6Vp1WjUAPN7CK4isFmi14S8T8eOF9BmNgibtFToiGVIIDhxrN\n5I5m5e7uSnfp8fPz8s99d89+7IweoHqPZTDQecW/T3HE+yqF/CAlERi9KP+v\nXt96XNFWIvyebuUe6MirVG7Cv55G9dxqGFOzFzKHDo/Feh6Msb9HFYtimUom\n+5nuHWWX6RsDzPzmDDxYrDjQz/lSlvk3h1KPmZXapr0Q8JZ97tQ1Vnl5OSQc\nYleP\r\n=b8iK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "d9f07be719c9cf4a28c623d05a542569e7f96d2d", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.2_1595233747662_0.5467706501178236", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.3": {"name": "@cloudbase/toolbox", "version": "0.4.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.3", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "933688f5d0e2e3ac5e530af2d4d1cd9edeb4204b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.3.tgz", "fileCount": 138, "integrity": "sha512-FY7HZ2jQl7WFguC0ksHNMMmesl4JorMhAKNPLmhg5hUB7ZyY81pwGbG46Dv3SOIJhGAox6LzzLZiio4YClXRGA==", "signatures": [{"sig": "MEUCIQDTh9C4zQgEfuCz+hJVd3l0Xfhw86QUJgQ7KUhZ/04glQIgTuuXxHnW3xRD8mwn9xi7/G02vAmDBPqaGVUhfwp23/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF68dCRA9TVsSAnZWagAAKcgP/3/ckkdzN146M6FX02Ts\nQzHT0Rx3UUeAN5uM6KKqa1IY0NfmkwxLNzVZZVrDVxYwEe3DxlREFKRNvUZL\nxhr1m8LWmlZs0rjKfSUvk60ZHBx/aoKunmEyHDeCJ6EJAEo1Fr4n3i8qcIVU\nEzT3kQkyqfdsf/kfG3bCcZJHh0iH3bD5zzplC6prk6CBhYPYSaYY/zjv/La2\njNgD2XObhEbsUo05HGhZhjTReMmaDiknzUliyJaskkL1sEcRBgkdFxWuxm4i\nwQ6nYHu4YXFwUoD7i03ZLrg5Z5W9Ky4+3v/jxQlMkkjQzyUmE6p6UbmtDVs6\n1VDRY8o4aefSrccPO14AWSQA/fVUKmRyw57zot7fed8NER1NkkVx6huGeAnz\nm4nJW4/ZCSrGlIE2pSKCucg4vab7J3fs0fwZpNj18ulNstWrZRdjbyW+lMM4\nM7HNL5lWcekKBwZWEc/kREsxhe2gAvTPqQRJklFfr/QT2bKjqvrCaLKapAwr\noP0oN575BF/LYNtPA0a0Wb7Ee4I2C5G5ro84wAOps5lMdpFYhVRVkvtWgqT9\nTvfK/kzKC++gdYxwdSxPcy5HOv7BWerTre34iP7G0rlIAdDm4w6CcWJBND5n\nHWcOlh1x7ROgQ8HpzrK8x8tp+RsOTy+Bv/LLqQWgJDmMY/U8fF8GF0F8I6yi\n22BD\r\n=W6Nm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "4f319870fe829a5fcb78d6ff199b62e548633ce0", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.3_1595387677132_0.7842173955333627", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.4": {"name": "@cloudbase/toolbox", "version": "0.4.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.4", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "7f6ba499f0108ac55fbe85f6b1ea8034e03db95d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.4.tgz", "fileCount": 138, "integrity": "sha512-9SOhZqtPKHM697Ya5OF6ocapf5x80lmkxs9o36VzK54Gkjs5RN+bQKrwrn7edqQdd8FQXX471Jn7Z2E/7KDkVg==", "signatures": [{"sig": "MEUCIQDYjg8+4Tfqs8fzM54l9cZ46jyxxI1jgl+01WuOvJYL4wIgbXk05Izx2fuqsi1yb1ESdmQScVxnhCOqmJstdi/n2rU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHjx3CRA9TVsSAnZWagAA8lIP/j/cVjvwTAU+01xKniRh\nPb4o4n43eYvBUqOnnbRgke1oK4FFwCSjoUdB098ubOtUSAIR9loD2Ks5+gcq\nbdnKJb9eoUXZ2JrGYm3i2tO5BPDuAt1QduyfwYS43rs6lPMlrvQLn4E7eJRS\nPRqlQeT7fbn47Bl6UDpA5QpKVNaeHgbaoNO17AchnvDzuFQRt2bPEGQB4qmY\nZLxRhC2qOCDYqfULbUcWh33wLUg87WEIMn6aVkBMgMyNECq8kZu5oXcv2nrA\nmDajVpRJRdYaKzBVOuIHmta<PERSON>jbkuQ6liPunKIIkBtxmvXLmTzOiW78ZqC4w\nc26/xmmb/VuQ5V7HhMv8R3MQOZKt0LUuOaqZW/P4s1/OnwWmx/8NE2tqXrKP\nxqxZTPRh9Sid5nKZm/Q28mxfgeq+c7wSA303naP7gBhiU9xn2kmkrA+aL150\nvc74aX4KO84t8G5dxnjOKFGoehbxgXCmGwk+H5GYcnWoZn3QHF1Wz2vCzFet\nRafSXKzN69ev7GWRGFBBlfQdJRyjDIfrSBC+dM8YEP1YGJenGtoJuX8tFF3o\nboNhQjyt7GehlsYcwcFdm14kMcvgeyUgYjZjaZdSCsvxUlG6xD3xKx4RvqL7\nEhvLI0jUzYr64hLg/5m2WiqiUiJr2aIUAD90n4nP/S0aLcXNX8hATyokd9dS\nn1L2\r\n=JWs/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "918faced095046350767dadaba60b17c0711216f", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^6.8.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "eslint-config-alloy": "^3.6.0", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.4_1595817079242_0.33141921062783175", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.5": {"name": "@cloudbase/toolbox", "version": "0.4.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.5", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "5c8cde74dc7a8b02f066f51080f48553b5360be3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.5.tgz", "fileCount": 138, "integrity": "sha512-TQe20QmuhLicvU2QN/UjW9VeeLRQDPnSBFGTqlaxIz5Qny4I35ihsjnlH8vypYMIHxD0r9XHNV3xrojPVPyCUA==", "signatures": [{"sig": "MEUCIQCHQM2NY/om1vR/19SCEOT0HRYgRM655+eDH7yqbQNO7gIgdQbcNR+ny0PwY2I56Pp+IGsxtii/4Q8SH/lYFFU8gxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHn4zCRA9TVsSAnZWagAACH0P/jNohPQP8WoC6KPjO1Nu\n4FaztAPUgprEjFBrsxDkljkmsVsAv6fIeiL/G44zTFEnnXBoa1Zco5p57xye\nInuHn5kCUouEiJjTe+YJtmA/nOi3P7vfZbV2Ek1+9K+zf7zO8SRgPDkF5/mG\ni50WwbjPCuiRSq/FD2LgN5NC4VT5oMGR2KysocYuuojco4jw1rbw8GbrxAbl\nNp8ZdC/IpARBksQjYjDOZNaU46p5FW21+RinTBTCXHLxJcwC7SPoWeYFWnBP\nK/VNTRMvWokOM5t5BTzcBF5JcyKBC2WrSr05LZt954OEJJfhKuEFKuWr/Xe4\n7+evxmsYhKb+2T/0kNtsWzF9rEwknVY4+vmA9UIQiCShrl32rXpemGNX4//p\n3pFrYI8+10pkptlbhqWm3ur4S84tbE0QJ0bzruR9pOl+IkDZwV5a4X9cogRB\nL1xYO0Gh94h965DdTxEyKrTTfAwGeoQPBadLdljQfnkv9wUaF39Jb0BYm7bt\n2xGGprcycVhORAfzZ6MRISY2/GkfoXFN3Iv9//BNxCvZSfxAnHUwe66kW6pm\nMiWO6Ov9NqegTVhTKS0LsJA2oJ+NfG9Uhx8P/hm44zTG222cG0ZIIlImqNFW\nNZhGYBougD7dSLF2GUWgV48dvBVSYI2ffh085ff4YHm+9EMh/JzvrAyBRYFk\nPK8A\r\n=kYHq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "082b221ee05a41f9ed545f1aea8171476c2eaa3f", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/lowdb": "^1.0.9", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.5_1595833907393_0.2876131699137936", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.6": {"name": "@cloudbase/toolbox", "version": "0.4.6", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.6", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "de396c15b31f8981c9365c1bff3562e7ae62bdf9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.6.tgz", "fileCount": 138, "integrity": "sha512-ppfXwQD4neM68R2lkpeeOW7GTNJAW8BKA8yiq9irpN0br2UDsfca2dCO4Z6GBX1jNfe02H2mpl+XQkKImpBO3w==", "signatures": [{"sig": "MEUCIBm7eB/RN3de4I29lrKSroWu8NtZS/Sv1Jxrd0pnLzbzAiEAzQIz8oH3XLrlnes2hAUqPlZ98CqNPm3bSecncVSGDew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHp0dCRA9TVsSAnZWagAAYSgQAIDWZtUjd3kPLvnuBA0l\nSkjYwhuEGZvl50B9I9DphQa66Y1j+LTIxA3qCIkjeU2jt45BaS2KT0TsRanq\nLBK8QQDC7P1xMCD1IX75CjS/y5ofkBpscTNRJtddbzgWV2Ins2HIOxA19x2/\nrlPyEHdcaaPq2dBZQbQCmpxT7k0L6HJsmqGRqZhcIHpEm+GDk58KqXowCUDN\nWpQ+jHQCNRWE0AwYHCT6A3X7sCF2e/FSA7iEnFH6Kal+AJHYNQw19mdLXnQ4\n70BmUHsXZA7B1LXg+fAy/oOTESH8tZCpZfK5zopT2//DgDSxm2bhY5a7K7Wi\nFYCQMQmBhkJ9qfIuJRNp7/FtdV7jZystsAzLg/fty7Cdr7uwrctxsK63S5mE\nS+9wy1NUUBNpvFnnYDRj6Zc3NcTv+k0NVjItkpT7d9qzsLNx/90whEIR4zpH\navtx1xZ3Yg6KSKD6gLvIgn90lJqQAC0Ej7lZkpiMoMg7PSIqbAWFJME/DmzS\nKmbG3MxiMjdvB26YB/2ByDS9Y/evmtSIfhemKzXrPtrcHScEIwl1v0VFcKjo\nEeoTtRFauk4MfiF7HVvzP7iRrCJP/z8siT3fWhb4Zh8Y5jsyP0x0vHvHyjlu\ng/HfiF+p0VqumLaK5amzAa3e6YZVQsJpYYq+B2xa7rDpP3Gmm11WHuMcXbkH\n6YVI\r\n=rqGS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "9c340c8f9a6c1de99483db39b493910f7a5a35b1", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.6_1595841821215_0.291999879758277", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.7": {"name": "@cloudbase/toolbox", "version": "0.4.7", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.7", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "cc5d5638788e3edd2c3a29d80aaf0353a2ed20ab", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.7.tgz", "fileCount": 138, "integrity": "sha512-bisCXNJRvOZ2kyRMuYkgSXYTKgtq0RKrDN2vPKJBKpUGlBsVrutpkY448jFlGJkvet0k23yZiDlEfSyIlu56pQ==", "signatures": [{"sig": "MEUCIHkKF+D/HdlSzLnhv6CzHZn9uw0694gEIyv1Q/oRcG7IAiEAqvpRR5LaKd3D/qdEQxWnHG5UMFDsJFd+CRM4XLlBMHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfH48lCRA9TVsSAnZWagAAsWwP/RPHcZyaigQM4YL4M/pp\nkarMux224vICmlf0UyqQYjlQeYi9mxDdH441yaCEQ9GbE3p7fS8eDZizv47g\nmeYWvTD1wRoA90sSmJjrFBU1mhX3tk7MGmsIlegIp5Llone4UFn6Cqae0WW1\nATLW8x87YEIEvJaOSwGsEiUiPWSWPgBQ0hr+k/n+Ku46vGgllilkO35Rt5Te\nVUv2eRFLQOlGebf7S4xJAnBiYwn25/YvWEYXorWDPKO75LExeudtbgwEl+FX\nMBUjczdXcv4LFqsV0qxhifsbwOqtSuLSrvRMRjhVvWZT1TogGJbkmT0PJNFj\nlNOzHApMLsYGfs4rPxk2AavHO66itPqSV9KPvtrlgvWsS1PodGXFeFjrky+x\n1QMqLCOQQWzBS9zewUujxG14TKtnVWQnDKblh6GT8/Nxto/5miId+fGyKtcq\nkcvMaSDsQb8+bzBTH9emN1p/5/zKg23xIT3z0wGv04FM4gAfkaVukjLFLREE\nzFX/2iacORXHzwJa18SkVrbX4WP4v2TLtUfOFvasiPEDhgrICFTjvCvuV3WM\n1qMw8OhYaTlvjB3RiCLbmchvTIMuTLi0z1E0Zw/ZT+VBmMCzE7FXxNitEfU1\nBme68tqx4dbInFHq6c2Xso9Yohl47/MwpmBZ9IhjYtJx24vnsSnw5AU7FGbv\nmmQi\r\n=LXeW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "7f7cc1d6ea59c9e81b3fab85318a0c6815eedcbc", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.7_1595903780559_0.37352652456310076", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.8": {"name": "@cloudbase/toolbox", "version": "0.4.8", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.8", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "b7f5b399daabda5f322236e173c225d00ce4b8a3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.8.tgz", "fileCount": 138, "integrity": "sha512-eztDNkFYOmZ23rgg613GHyLzhVAHmjtdcxlQzl4QirCKX7IFd3DKtJelmuzP+V/GrGE/TMY57eTv3Tf3ZujT2g==", "signatures": [{"sig": "MEQCIBnK4eJ1DnA3pDxAoJtUtQoIl4YUdW/IzgXUQgEi9NvmAiBl9FM5caMMBxDBh2PvAbjlfXc8k+UAVqs5R9kbqPdFXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIOAMCRA9TVsSAnZWagAA0mEQAJLnFF1KIcCClk1n5CWX\nG1v51al+kgqrw7M1R9arredsi6FWQAl5jOHfMSRU7b9+MFHYefyojlxip/8j\n1jAV+HQsJLvJVbplNYSB1TNlJ7Bt+vICEWxtTI8jY3wMPFoKN+uEK7S2d6Qw\nHEuUJGbJbaFkLOcFfeM/S6b9HDhhI2RcVvTNcxhrrohqR80SVpLHcP4ZrHjO\nWXcqTpDqXg5khLkVXiJtBJDhDsgMKqUGlY99Yz7x5TKzFr3oZOTJ+cdvadGG\ncgZqPfN6DcTy7pY50KxN0dJm3e83ElBxb49OgvWHbmAx2Z84/qjFH3DcxPr5\n3k4w96RS872akzdWAdYp+zgNbngU7gI5eegooqv7bQt+voiFk+CbKiq9/Zki\nwnt+bE0k3I5NY+vLgMkSceDxOvewOVKKH6LwYaBlcYcqZyL25n9fqC15b9IV\n2g4GfK1CwtY/xXUGn/gcpU6yb3LcCxQ1VSv1/IaiBmrs7GRXuyS57EPPxnPH\nbxDfdw9fA4onQWcJtPs7mgLrI2hmeB2xuO4KSP110QPz3Onu5TkviphcxkK0\nX/Kqj1reuNli//q/wkrTDfnOAt2UafZbF8tbIS2dzZaQLL4KmWmBXiTDESC0\nk8+wCUrEVjUThl+xAeoj9Cfgnqg9H5oGxQZpVT1oOOq4p8m7p3kHIQJ/9QZ4\n5LT6\r\n=EIUv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "f04f0b1e0b65885146ec49224efd7bd9833da414", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.8_1595990028114_0.7622355089638932", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.9": {"name": "@cloudbase/toolbox", "version": "0.4.9", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.9", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "add85a1145e290931f7f973d16ffa70e170d4e00", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.9.tgz", "fileCount": 138, "integrity": "sha512-yAkpJZ0N7KE14cV/WRclwSGyYI/C4Z0ALkNA0yfRQu/HCXbuiu/4p0xx45vN/4bi2QbWdxE0Zb5qaAt7XpDh3A==", "signatures": [{"sig": "MEYCIQDiIrX6YBegT4ImXPe4PV1T8C0cno0FUpLL9+lCWM9i3wIhALPsGVDe46w6pp++ELGi+jsrBDtvbjpaX9EKYL0PHqlW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIPwlCRA9TVsSAnZWagAApJsP/RRpwkFeaug5vXIq/nmg\nk4ERgZxq7HNYCFtlHhSAQtSzSIMwklRiaoqLd2nkJQKQgSGAtA6l14yDbPyj\nj8yVQ97dPqzox7u/wIeZkt5IglFLHh2W+fST/6EJXaFzbq8gDnw33wlUaYJb\nMWjccwslgwXbpxDfwR3eAIfwtrZu648+QKbUDOpsn3J+PrpF/ZxvR2pW4QFK\n4cLMI/t5IRkKNYsdnagITeDqftaN1bnMpns0KIv8fQeOB3jxHhl2feH+nSiq\nKhaDtFi4JSo+ft3G7hktYe1nrKjqOgf1FQPjZ+7/XPhhmzYUwFP9G0vjaaBM\nFzdpJj90YjjXc6Il79DG6Be2a9Kd1eSkX9WvX2d8ZnePBSfer76OP2tqlNPQ\nOBFMYttt6Cz7SDnD9mDoCnGfwUJJ3F6RCBj5hz9cEvMdJX1RMOVz/8oH+Asc\nwd4AYpE34EfGovTk9YLn548ozjqymxrkznxIfTWuywWaz/fUNTJFiVLYWLmV\nCXZwDPTy7XgvsIkbrz3Z9fT89JHNnnKMuhBJOlHUfU7TwTbDu1/GiGVHDCSk\nc3fCF75soVrZeDqk1+SZdDBP8cgTaUX+q2uTi8o3vyfKuKVcmT3dX2//EthB\nJUZG7GqktW0I+TO5oykcmAQXbu6A2UmQX1/7ChB41DZhnlFR8Uz0tRtVqHCd\n71Sb\r\n=rcCN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "5238ebaebe8c2e85a05d58e06fcd6f87eccc3635", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.9_1595997220577_0.11063267218268402", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.10": {"name": "@cloudbase/toolbox", "version": "0.4.10", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.10", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "26883f4acd40846d73c4741e2d29b184d000730d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.10.tgz", "fileCount": 138, "integrity": "sha512-1M+ZN8z+71u/vCekyU82Ii5ZoZOa5l5VyraGYtMK6y0MMsv20T+Ua+t/fcbxeV41CpwRKiylFL/Bbeia9eNyDg==", "signatures": [{"sig": "MEUCIQCaLriS/j7OQdrRMWk+mUe5UUJLJzWSwM+aIRQ2jLOPvQIgZ0tvYilWwJcJtTD6UoW/5ozk8IW15aYI4krKHnAiIB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJ/HYCRA9TVsSAnZWagAAYdAQAJehdiRARpJWQwhRAEjC\nFmxrxjqBXc+eiROnCR8+rlqG7kpA0m/o9Crap0Mjs6yyoVY2oG6zRPARWLJa\nhcvc9DG/D+VvqHpG9T/akslQarRe6q0SzFV5jPycNjUWrCR077a9sTLEGUxk\ne8C5uCP1RXjWl03bC/pD73LO1OCcXbk9PZ1wonA8wYUP4zmyfXNaVZdyVMGz\nmgjxHpPz5b9GnDrPHktm5C9ITdGkc5bkxhvJH7lwf4VUMP5aM7oPwekXMTeu\ntVgr3fXkRDTmLqCTtmfxXND0qxt74FliqkFmZ+q3TiQfd51LR0LFdLqFdfHw\nUc/sPd6DZbzScdQPxqlUd0VqPl9HUuVYTA9tuyeC2us0NXRaKiW7CD05RsED\n5h0D5A2GYU7ZnOhveXwzQL0U9/UkZvuVVBVb0WzgLDJ09vhoqel38PKb4JhS\n9RczuC8Ns8Yz+z4nsvl2YeFM9mwXhDKKDsgN5JxMWBedpi3J9AZRkgae91Tb\nsJEv4kh7q5jR1y1YYPi2CnwgA7kDto9A8NmNDGQ8UWFw2xoECYpnj2lJ9Yq5\nHJ5WyIaNC/XLI0HeNTcrERzGgY0zqLj3NrCLMkRGf3Vs0GEgHk7FswkWoyAl\ndLxK81M3I2PJg4YyVgAAbLkKRr2czfMIdd0XI8zugInyJzSrwinBjScjKGGu\n36w6\r\n=SJLD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "0feff0aedf2d9fb6c21963e26df26ee9dfd516dd", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.10_1596453336044_0.8117213759459783", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.11": {"name": "@cloudbase/toolbox", "version": "0.4.11", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.11", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "c2c7b03c51b8742b4b580fc6e8733f3c9ce7fdd7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.11.tgz", "fileCount": 139, "integrity": "sha512-/0N0vZLKAEoOvGfbmySss4apJnxtdZzKDkPZV6ac2XJgVyLAc1jtTKt4FeOozRvp3FkoCcKclkiARkbz4pRs1A==", "signatures": [{"sig": "MEYCIQDWTguLuzJdddgNGP3Lsh/kCOiNuiO2AGIzRwFk1tGeEQIhAMRHA9ikA2DBrpnoen9JUikSr/gJg4HnmhuoyB9PoZvJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKRJ/CRA9TVsSAnZWagAALBYP/2qfjO0QSqivje89TNno\nLS+0fwl4HiwiVneMQHCIimv6RTBbWybrPaZ+f+TFtqqBgkLU6R/wvhkG2//R\nzJQlcZt6NeNrIHBbQbcZmG3xHsjQWTAt6zQkktjqHJs3neIR/U+Uh+RLhKr/\nBJM/V9slNRfpqxeVpGgXIT/DW+N0hzMLHN5LOTXjZmJNKmE2Va1BIZpa2+Gd\nqp6Dcs0ImqgpRLxfaAJK+ayl6VKw00hVTwezTwRxczuRi/6f/s/tHeIVeRW8\nVF6hgE2O/UJECFwxPZTZEHpWr5cimJUyE0CXv0kUxu8V85mfsX847FfEK5Ek\nNbrfmpvO0tNK8dyu9fc4Zf1za3z1URQZ2VeAahLV29YkaA0LidrSZgrL9Kjd\ndeV6RcJtJtTxaVZe0v+FdCGCc5kk1xM1KfmRi2GWs93Ceqck+dLLhM/z12H7\nrXrpWjICSkeIuogkjc+n7LmhHIEc4EO5z6rvBWHj9KSUjUPvnCIY09F7jn2+\nuiih3IdrWVXP2Wy+HFG5tPQK/TgsLs8KeLsm8j1vhsjyF9YC7hmwaW7J/eUx\ncnAHbBK9WUEA0fN3IVMEXVy9KFsgVJhcMyVMe/DoEEFT0UFU+hKajTyp57hD\n6Mcv6V7U395PmwJapKQAneVr+AlR5zniUEUKHFwRmqmMGuBvoJtKE2XE1v3Q\nuaz4\r\n=zmuC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "87e0eb907f8c681cb9597d87efb3f0ab38b831fc", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.11_1596527231083_0.6127030001750935", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.12": {"name": "@cloudbase/toolbox", "version": "0.4.12", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.12", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "5a74f2eade1c26fe7e4f5363a6b2b93cb160a9d1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.12.tgz", "fileCount": 139, "integrity": "sha512-W2DPMRBhX3S0BiPFHyBfLl6jPbSL9DbuAiU974jn9WA4zuV3J+yG3nGmEAdXobyfvhYy3HY7yAN5rdFNXhW7Eg==", "signatures": [{"sig": "MEUCIHPo5E4Y7IODjxwWKjH7MvD83UsQgCl9idTRM1Pdk37SAiEAtFmNORV22ROqyiL9H0778Pbt129qqDFqZbqQkKPoJk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMf2MCRA9TVsSAnZWagAASiwQAIKtvG/hUqd16FCkCNfL\n9RsK6rNu3IHjNMqWbgWvx93b2ssI81hX94nmjzVo6Oj8tkE1yiBy2pYBxfTV\n96jwkRSvr3LTbSR2tCQ7ef5CRsE3Goh0yAkFSWrfLft1HAkrhopL7JyesWxJ\n81vY64zDOo2ha04MiJlDArB61ngG8UABwQy3rs1mO9UIC+mhUgArWz3byTPd\nqKxUJzoI7HIOW32hKAFQshuLqb6KN8hLmsI9WD8/HVWrC3k35OIr37GhPUdF\nPB22mQLTRVjwZNNwi7fH1H6VrFAC/waeohZWg31HkRI+DsgqTPi2SL+GB5mX\n54qetGBDNoQHfm48uLlLhtf/lYgOcgOYw9uYs4ypwBV8AqFYr74zA9OTqhJG\nZkJR6+sfydaiFOuzRYin14Xup+00zlw/+VIgL6yEbJ5J47YuAuurg6+szg1c\nobOg4h6c56cJDSTq+YBASW96Jz2tybPZp8RnkALxc0XOH+sghq/Sgoz9qVAF\npFwg27qH5m4AetzOL28agEont0f4ekYeTGqi2esWX7jdVASHP6oCH3KdhCDu\n2ZXLik82W91qY3+p8/x3hGCBGzqCKfyYU+rz8f6oZvDD5jzjxxAHY5bakyEs\nzIf3qlnFQl9dO+VqmQE60pI7FjwpynAtoLYEpBIey+cUXpUVGDnjAPthWcnt\nKifl\r\n=TgrG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "cb449f2785311192ee27545da7040dd74162f8a9", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.12_1597111691945_0.9405338631571496", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.13": {"name": "@cloudbase/toolbox", "version": "0.4.13", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.13", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "5f73bcc5c504e9254675ab703a455ec8dc48cf47", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.13.tgz", "fileCount": 139, "integrity": "sha512-5CkiM6iosnvLP0rD3bKidSPBtN9YpGsg8wJQrP9rPnTMu2K3exj2o2zwZy69KVDBWrV1G6rfSN1X+ibZv6zWxw==", "signatures": [{"sig": "MEQCIAE9AEXQ5vYJYwTxTNbiXP6vb7XHmSSazjvrSqXiuUiHAiBty7mUYxgVoD0dL+L7W/yzZwGbxRG0cmwOxa+Y4U9CsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPzLOCRA9TVsSAnZWagAAl+sP/3J1xLP+WtZ5RGcpAvpP\nYwM9ADxWCSEVgeEdrUA53yRs5+1Dt7RjOt0dSx1bVBKu7h0YMissxcU0zQeh\nWUj+fULZ1LthemI1YMmICyj1ZWJI5WEBF5g1JcY8OB94NRwjyi00RDZEnG+5\n5MED8aDrgiT9eOhBo2gOi2+zUWJcBchEE1HSvKrZBOEYPsIwqc9s4OL2wLHi\nSH8+4ctNJSsxA3CEcDbTF7jkmZK9EvtjYoYddqMxLluSz3jffN1AHI24l/S3\nd49V+efSSbK1GkrxVAuVcooI2XYT88RX3b/MFSUgeYaj05A9rQCTUWbjy+FV\n3KdmYAyScTRm7ZInN2ELZriAYYxh9PSfqiaunYzrKC2yXrSyQQFnRrI32v7a\nLazblK1rMOElEYSwzZBT/5vS08e7yDOZN7DwEFuvMaPt/1E0OoIhu8HWr2JO\nu4y92PCwGCDQmq0qiYhG7fYowPNFwi46uwytF9Blvz3Oa0orVp97IjStBjNy\njHifSB3tGL5BEYkG1aaIA+tgsAlohKvVYkFeVdp+9N8Lb2cq7NAdj884GiIf\nfbxrcepbkAUGLMcPCQXpzqCKGfFj3oueCuUHYxpXdDPLip3xNmCAPPbYkrlP\njnS0MTmY3R4QwiokJqR1O1ut6jKYeDaoIijS4LP9uYDhGIDz0E6ysyeash2l\n2tbH\r\n=pp0r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "7cd1855558a65a1ffe2d433c7a1016547faabef6", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.13_1597977293605_0.9619882073322503", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.14": {"name": "@cloudbase/toolbox", "version": "0.4.14", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.14", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "d9562095a57a538a5dd031847bfd2f1795fee8dd", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.14.tgz", "fileCount": 139, "integrity": "sha512-8zncp5Dyt6bhCeMzltnBGoUJamafSB/PuRlsQw7rDur6dWkT+MKRNtO31haOT2krBucQfqzCD7YqnpUhg/MVOQ==", "signatures": [{"sig": "MEUCIAndLwKF8iJZgm/ePD8SJDt5KWHbUJYjsnyQ0bhFTpz5AiEAuTbqU0VIeIknwV9MDdpDYQo3CwztsMs3LIlVNl1KMDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTK4hCRA9TVsSAnZWagAAeYUP/0CTy3GpfSFv6vfSOBsf\nn3+4zjKCqhnINsBBqSDLjLsVSIXeWwSLdDiES5Sb5VZL2AS8aQW+1qZU9HhB\nJRhxZY3J5bUttJugcwotRG/b9roY/IFjIl7Xot8WKeJE5tJ7x0bR2w3ZQ6IM\n6O7+/XI+E+GghPbHGcYt1f1ByqNMH7SevD2+9WC6HW3dtoCvzZmAwQg0SOGT\nTm62064kcHwu8vTztPTVo6S39hR1pYxPhXXOrHSoiUGrIrXRUrQj2fXcDVIT\nUMGg6+9fvYQiPXvOO/bs2VVuvriY5H2Gqn7ak4y39sLo+23ZeI8Khc7eewfw\n20l7S79je5A02gQLKx2Y+5yoNEVP2mkgEnPpg92GUGukJ6o1qF3kS0z5LKRk\nWaBD9qVDEtFz5rjQcO8tZjoryxJbWNE3+PNpRl+8lKKBAe2L0GCIgE84t0Em\nyaixtDks9CJwFGAcWRlsV9J6B3bzSTLz/u/o70Z9FrvaJUkseZvgLlKpoajR\ntSsbJMB5VUGVBrRauZqB7v3yddYafG3HgjfWAlT+gs7fpUs4YMcqW1nNgwUN\n/KRVB8xZ5Zoa6gtPF0TiSx5/oS0rhOck1478nAunIrJMqvkn//fY1pYn+55M\nRqhv6WESfWdlG9ZlnsXqzC9Eoniq/qazAom1eTqlTg5qlrI72AfS2I0keGID\nVNGa\r\n=lzAm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "52692ee01582d0a21180b0ad09894ae5079b31e9", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.14_1598860833369_0.6466548483224854", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.15": {"name": "@cloudbase/toolbox", "version": "0.4.15", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.15", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "b9e6ae985900edf023a9af9bef91412e0301cc23", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.15.tgz", "fileCount": 139, "integrity": "sha512-rz5FyehHtcNeYZgMnbosQR8YI91IGSwOcowowpOu8CIEJO+E+5fvjC6O9CxNnXIZ4AoXt/7afhxv/GAmucaeng==", "signatures": [{"sig": "MEUCIBOZnu32Nmn99gakMomO+QhQRw5gim7IZ8569zdrTkviAiEA7iDqIoSnQuS3HHdVHqI15cmW/Ewlp1kMgi5Ubs61ATw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXxQ4CRA9TVsSAnZWagAAylAP/jpN3UDDyoWbhwCkN5nR\nW6azMxVGFN3/cs0TRhHPgBp1ARAPleuxvEPJJp/7fHDnPrUurykum5eeqJdm\nOAV66J1Q/46D6pOycaBVLXse9NzXgzeyXPPsJCDLoY/WG+NCDlnyTnKRNv5X\n3ihfQhLCKHCbAm6VYfDqIgwVSdAtSnpgITSTqcaaYiLPQ1iIW496bRncaddo\n7G00yi+T1g0Zq4UX7YZKARL2zILYUXvEAHLtzCsWx88DvZ/GdD1DtUjrgwU8\npm6hHK2pHDwzzP59QdU2U04gKjMVyJWPkye3TXQ/OF44kxsp6t/pkYm/ffko\nP1aqPuUJrrTbELt/3GtU0DoyNs588IouYiIfl72SAjlQI6PZBm/04nxZF0PX\ngz1FSQtrvJv2NytHmey6eamzLYKVqkw9qEKFqjIlCxFObZqDlJCeEGT+POU8\nbDS+TPzB+afa0s2HiyoyYqzrXhIiIDS3LfVS3xPOSZNjPSmLnhHl8oPTLAFB\nYczD1uDp/GZR1ftm19mC0TregYJa/e7K2t4DfTzV5JYGCDpZdG5L2joOoZ1y\nqgLdx69HTt/ajWPi+gw1hBo+sBYeLOmc/XEtZ48x3LSM1/TzNde1ii9dr65b\nMlH/hGwEGZEyIXsmGAwfOKAvSKS4b/VIW9oB/GggCeEkqUUZZNOm89prJGPe\njoiR\r\n=Cpls\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "03030493b7de83e2c6ea1577497e87091e362eeb", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.15_1600066615439_0.8274424041947135", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.16": {"name": "@cloudbase/toolbox", "version": "0.4.16", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.16", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "1b580488859a755049f6476aef2db635742aa552", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.16.tgz", "fileCount": 138, "integrity": "sha512-oJScpEU+GNk77cBzOizxCKFSXymlvDZhq7/F+Y5nNFfvATU9bMl2xNm1W7Wgs2eBxrUws5IJL7oVP6X/ZhbZdQ==", "signatures": [{"sig": "MEUCIQC+oRzIH0FSqscJ9i8CupDePVtAeoGvt+1dEgFhTLzTSQIgSFrbe/jbjfCZ/pXWooSnWUh0wOn/c9Tw7vPw3ev3g/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX1onCRA9TVsSAnZWagAAlrYP+wRirB1QbI/hVoOWFYwC\nlSEcMF9zgfeMpnOA9/ZSrxuoX8Py5UkVR5o+QuOo5Qp7oS5aQHJXKmVfMgoF\nJRwzgUMoeDqze7fVylmKuZs5vO/byJSOK5kJq4pE0/Es+iOg9nFz0idKJTSC\nRbPJal5TrxNIsv+8IiWDSEiYlh9+5WR08CfU/FZZD/LHAC1cpf1c/bFRljiE\nCKuyzC//WgoLYaRewUN/4DF6Rzna92LEPWNg9nF5+NdHE3Jc6gqbn63TkUDo\nTHT46SZ8IEpTl/AIEP4Ib1D73t5OKusQsYb7wTf+1+ndJdvO7nRo1Fp0xk2q\nIzvfjd+EqWGikS9pOCmxGZu1SvM0cohTGAkPGzGHoKvZf8afP6rkeoMg5jZb\n7I7FwxxUL5z/88vMufQyjoV+INFttCGF13ufTSS2oKAA6GD+MScil8oP5+uf\nGsD3WTv2grtBR3oq+2VY8yc1WFgRejpTGv6MURgKpFK7fkW6+2ykGfNvoqUn\nhhOl3xTqyjtxWbu9YmHIMAIzCwTMIO0YS85VYF6cENjfSu52Uc2WJ3N1T7e+\nbdJ4q/udsS3WbichhC/Fkm64WWM2lfbGUMWqxmWjptfrdQbm7gTsycmVszic\n36EDOJRMLMRb/IuGtzsBktZpLiZMsAuh2zNbZrByCI9kwni+umMRk8fihfS/\n1wPw\r\n=rzQ8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "796e0801a42a7f78d2c96fc06091d2fdec74e37e", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.16_1600084511664_0.7795603104800706", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.17": {"name": "@cloudbase/toolbox", "version": "0.4.17", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.17", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "dc433e8c0186d4f9e99ea0a6d6cf51e46b929d7a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.17.tgz", "fileCount": 138, "integrity": "sha512-rmMLN+VUfJGBUOYQDcE2QuzqhtehFoQxivwVjqh9+UGGek+PCjwDEWhYdexOjEwbc/m5EWP1sNzp2uQmpZK/lg==", "signatures": [{"sig": "MEUCIQCreWEx4/A8qWCBNiRFSoXlR485msrKRHF5xCGI8SSLgwIgP7zCcLtrog88oZqrka+RM3xEmmfL8Qmy8vYSr8bCHOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhVbxCRA9TVsSAnZWagAArnAP/RmsZxzyikT9bdWIVX/t\ny+pdpLt+jZJoQ2c78UAuvgFGuiaZe3/Y4I4S6Ct8ijq1xplXnoBnNrTiB1cd\npHHSwmUVD0GCIotKVWytIrGsEhRgTcLKPcX9sJYEXBmXAQkxrqYlJZgDUktv\nug0nj8Cy1cLgkkBHaEjk1Xngiod4ZMUYn/Mu0HLO44SFqtc4lSYJoLuW8X1c\nIjK03Tj8x1JWAA05ESC6dl/mzD2DyCJI+ZM5o6Zk2stTKweTXzkT0xfUR3C+\nd1nt3ki6W1uA/9j83ThZ4DDyr6YkWxtbOVKvbv9iA8oVP2OlP8P4Sb1vm9g5\naiav0w0yD1hncXkiLJaFiNNZO8L7RsyNlHBZhX9zBQSMhX5b7uNFUtD9qSD1\n+E03ugylUPTNKXuzT+KxrRw74ZPEo2u2t2dwRjxVbvVIiNAnyZ3JOhK8Kd9c\nka5Qr+YKOKL299ZQl0BOzA1JAenJdBHm8/aExF2PyXhWfQ1D9Xou0At/2n/5\nAW3m04dcm4PGJ5+0nPHIu3y3dyMkA7A7MWk1yzwTOI6iK/+OkoquJGx98NwB\nh2LRdvgZQLyaEIaAJsJJnlNdNRcCnDFU0wlNxL0Lkz95zjweOktaGFE/57tA\nokbIQhwkjYW4HahoVgjUHORiLAiqLYdRpTOYiLJBrmQ4wmbvYzCbvPn+2O/P\nWoxL\r\n=CLvP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a2cd0a09ccd88bd6052bb2412f718ea2e30f6caf", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.17_1602574064550_0.9213027149637367", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.18": {"name": "@cloudbase/toolbox", "version": "0.4.18", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.18", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "4582eb359b246bbc4cf922c1c274197a1b49008e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.18.tgz", "fileCount": 138, "integrity": "sha512-fycXk0HC5+HxIijuIYxrmiA1z/65eQsgZUDtzcGN6DrItKePzR7rfJWqJDDOTWHkmAg3nn5DzW1ZcqcnKfuSyg==", "signatures": [{"sig": "MEUCIQDua7ZhcnIz4DMkmd96F5CqE3gTmKmFOLEZElnNNcUyFAIgXHEx4/6gBPAwBGfhHIjF9wQmBZV3V8G+T76LkPinIaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjoJACRA9TVsSAnZWagAAnUwQAIJmenXvroP2pJqR4JQj\niz5wz6xtGUJEOoUqDvbRZWXPKe6pNDbcDUxEfbuhSSH8K4PNWWoHLV6B83m1\n2mmZQ55mYFexfVMYrrF4+OGcDFooiD7g+SXipmJapYRzKy3OCVssGgwkUh+A\nXDJuWOjLpGo5iC+01JsIqh1GFyWfcDiBBfoMlzXRX0QlMxPkaEljaUsuNZ8s\nHbepsNGNKFcfWEKUHCnhcufDJ+njMjh9WKDq9mB9b2T4KpEOV0nKo9j8Hp1e\nWti2+2orB996bFrZuuHiVRciZXC70F7YHppE0qpRzvpzyi+UCfgAHpk0++Or\nYRdjRFuL7rdo+fAnIPsVJrjwonS/izcPtuf5hdIm5i9OSFR6TH3K9sZjOEVJ\nx3EYlNUP5kBsWN9ty5MqQJgfzdsTuVOqEzKjWI40W5Nm1bA/WL0mz9oECYKq\nTJu0qY2KHZHO0avBS2z8HVoD0c13c9oWfKNvHdyRUZQelWmUS4PxlkTHxwm5\n8hJ/FjVZbhHn/PbLu0qbvMGwLftj63V6AwaphulFBPzExb2oH4os3BNboxpA\nmUZIMZaetb2T/mwgLW9lYshvQBmUx+mS+6OWazBD1d4m7TBtalrUvbnY2cLQ\nyJTtAA3YPNUOjmiGoPMMIW4Qjd6Tmf8AhVs2prtYw5Wy/eFPBpGflXZVwgWe\n8VcK\r\n=RLaX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "6fd18709c6251fee199d115df9f94033e1e16e61", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.18_1603174976078_0.96089416077628", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.19": {"name": "@cloudbase/toolbox", "version": "0.4.19", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.4.19", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "9e0e24784a074d1bb839cec8d7b321a3bb2fd5cc", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.4.19.tgz", "fileCount": 138, "integrity": "sha512-2BkmjXElO6YzsdBTz0GRPV/LYQPIG00s7iDcQ6Kaldpkg//1qznKpxaKwI4wGHcx/ZZnFEAzYAlDlmh+qBCoxg==", "signatures": [{"sig": "MEYCIQC3yX41Y0tomEHDUsG7SbT5wUwR003aLbabWL4W5yKmnwIhAJtlZ9xe/Xmddyvnl69nsdHVKBWSrYkCu2jY5z4b2J9u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjo0qCRA9TVsSAnZWagAAbFEP+wTf8ShZZav4WrSmy46e\nDAsIUHB379GVNA6YTOadKK03haqARJOv7sdA8x8aOfLsJOLqGjG18W3+Mop2\nCRLgAdhCJp5pgdHGn32VB3vq22+aFZ9UACMwiZmsKzIApHO5P1hys0CPXjO1\nfJgSa+G+mBLFrB8GR4U7lPOZtNlaGM2p1W7fXttpyErICl9JxY2hvruZGcae\njqUpDXkd66SJBiHB2tYZXxEClwPP187TwfMrR0JvN/Bo1om7aK2jOOQGB5AK\nSz85SrU7NFVCSQd3N+znlIXxRi16Ip+wjQGyAvIZOOckJtxiZfNL+5ZK4Khi\nWbPgQUFoV98LM/u+A8A4iTr5cbfgkeKqp1kttGrmrIhFLpL/rDJ9hspT+ICM\n8bjae6PC6cYv204KCeaXZvSMt3RiGkZaVHpUc+rT6R1hS6+EoK5XVfdpWB0v\nmgNUqaqZFDIGFfFmi9Revb3/h88PiSaVkpMoQyZWy3V+hsOkst182hc7mHom\nRhXyzY2cHAs+4GPtvyOGoZuv+Qefn6fHQz1t7Dz6peFtO/N5x0wn1POtPOFx\nKFBMrqnrIBobj/qiZOhsXJodEIJRNuP0smpX4ECHkMmmNV3cEqT/lXNphDpF\n+Kqg6qQgd+NCuA7SDgL5nJCb6Ybhl9kIG7QXU3pQhikvvTit/Y5EtwmZg6el\n1ui4\r\n=FcR6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "7ecb57bdae8544d49ad3ea2c8938595d5be5563a", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.4.19_1603177770024_0.38190184472216604", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.0": {"name": "@cloudbase/toolbox", "version": "0.5.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.5.0", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "4debe68da2d590c92b31f6c44269d31afffc8cd6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.5.0.tgz", "fileCount": 138, "integrity": "sha512-3mq5HH/p7PaQebWg+pfdEkqC2EAoJ6Hrzht0bY4bus6JIMMJlZdQlxKiCnUgu8PRm/SGm9jJAFWYIv9GsYRUQQ==", "signatures": [{"sig": "MEQCIE78WpJs03gt0a8iWWiFFnikdESMtIfoaByzeJgYA6MSAiA1KGspL3/PnHyPf5CPNU00oQATNyKOZjLnyfdwIIKzEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrK8TCRA9TVsSAnZWagAAm/UP/1bbIkHPK+mIWMVN7w/I\nyROm9SYqjtAgeWZlqogbmagKH3vPCVQLZrLLQfU/QjtyOh3NBR5CATMy+Fhw\nG/j6xBzuPMqOctEWbUFO4Ha/mrdrjE+DdUfYeVwe+ZvtFG4gNjjER94YjTHY\n6+X60I/ZWVkn3bj1ix15lWWK/p69NYpUpani1QThFkU3N9LRfhPdvOXAMiJl\nIUde2g6XO2jT++OuH/edm6/x9v2RpNmVfYaWAq2ZCkWEPH/bO7tgN/AEk1CT\nQjv5iyFfn0awRcKMa7wimGF8HTZSzA/ySRpFnO5FUmexRlYQu+ZfQDWAISVU\nCOPprj5tag77WNFph/O1mkCCH0eYhYNUuHA20dR4YUadrHJhP2/StXb47L6g\nCwTCT4LJkPqCtE1KsRe1dF+4F0TkYQaNjgSvYjfjBhW+Iz35qDuuLufooh4G\nc7crQCaNFZtMrlgwNq1JNzRlUmffsmXu7jv8rCb/YT7aHlHW9mdbCD9OW8z8\nQAztABz6xeqLYw+1NqIKv256Yu6tKrvLuOq7fBjbUjyvRsicTJUEIAF/gKnK\ncFi56/Eyf5FHw5fRUR8+j/fZO9VNvjPynFeKdB+cPHvTXHSIXieaHbO/nURt\nP0ViGKEZxGshpNfLGDXmZHWfTLR1mITZIowaiDCB4XLkLsW/ECw9ytTUrvE3\nQOjK\r\n=RUBS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "c9c14e46c9e810aad8ae368f5c6f4d86378825d0", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.5.0_1605152530606_0.801578097952262", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.1": {"name": "@cloudbase/toolbox", "version": "0.5.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.5.1", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "5231e4382cc27386905ac55e72c4faddde61dadc", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.5.1.tgz", "fileCount": 138, "integrity": "sha512-CSguYqbJAj7KcQoB6fBaBpSowq8pR3/nHNCQZFMQY3P5VuyO35BZ5WyMtn5ZxSWrSXscsHZrKSWVnIq/Rel6Rw==", "signatures": [{"sig": "MEUCIQD8/7gMDu1QHkb85831EJSFdFaEVy28rPZ4HHhAobJ+aAIgXY7wpHQka8WEPsVjQgZEtmxgrcYFO3me/nx+m3QGhUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsm9RCRA9TVsSAnZWagAAnfYQAIr3g4bND0QJd/+feafz\nG052Dtto+nK4RfA/pPaBkEeVovscpIMDh+FTnun8MAjgImGcGbNGrIHs/TLh\nAl84wrFaWz4uPe4n8oxLbmgvZgyRBRspkeWH0QiXnNOU/Y9059sC4nu9HbeL\nxUwiVVCYt8K6RrACtl9i1QGUmf2MzAoxi9jEcwTqgrh8zATsf/m7opGrRn7/\nEzRNW9yAXAVd2uF6Zo2CER0WCKNymEHuPOEpv2xtukgd0LSG5ZW8eKq8bOZ6\nCInFwDAmjQKre1wgl9BbTsnBuPLloOUYmgnroZTeafuG2MFyugxl5rXWyFk1\nQFJ4DAAn9tkr1TaByGP6YP5QJeSD/t3mRj0BZfqjJ/gQtFd2QvKudYy93IhO\nqkTIfiSmtOo5m5OD+tlIxM+kSg1rP1WBIspaPBrznVPChXNEXzVSFjbmg5uT\nQGYuZ/Ebd2QmKYqd9aWEU8W+BdbSmwmDu0PDKp2VwK0OHdDeEzQDtVCfvE39\nfrYLoPqLyGOGjUKynIJ5wOO0sPJejUUP7+Mv3MIlfeI6HcuxBDdnWnruzZAH\nmpNYvIE0YEZXLbkmxTdk1kEnuYg2Ry0rNYeW5bfPLHS6C4WPfEiVcm6sY0Eu\nfGIVsfXhrdM6Itek3psG6F4qin5H2IhYO8/t+R7lRHZw/DisvNW8izsl0xzO\nXfwX\r\n=5mkM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "fbd9e0a43689932d55ea306f82ec654eea529f19", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.5.1_1605529424717_0.5923503506085301", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.2": {"name": "@cloudbase/toolbox", "version": "0.5.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.5.2", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "ab3983defd68f9363e1a04c49825dbc358c84836", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.5.2.tgz", "fileCount": 138, "integrity": "sha512-omaLIuyI6+lKc7QiWMkzZPZAxpwwEdMPvjcqcFtL5naACBoX6230Ri1kv8/dixoEKRSylZg0AH4SVChpvpIYwA==", "signatures": [{"sig": "MEQCIAKwEIvpwco0Mb0VzG2IaNhByQyeINHa+k5JuzfVHiJ5AiAFemBGrKKUI6KOykDw47GuefnZBuOWDQguBkUKT6/S5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftQQbCRA9TVsSAnZWagAAaSoQAJKEonhFIXZ+AOHKZzUO\nKvrqofsZW9JzbXZJQlxo1fn0aFUXbGi6llzqF8RA1Gpt7EpktxXEA6w+DhHR\nC39dQGiH6SZCfk4tOZk2+Z8/iMum0m2k+7WCiNISiqxggVPtTVIhiSedJlnq\nljguLouEzg5KVviBgJkMLRFmjBz9SnNbrxsmUuuG61E3IU115egKGUyCUjqk\nmvuenJ1fZh5NL9mIKkbUvxVRG++AW9hGqeeqoZ1vCBftUBKVUDfT4nwIkhQt\ntydxX7KxDpLZMGjXVcT4LKNCp9BDRThijJ+Ikhngu00oAXaVSPVX73knSD5U\nUf8RhHTofIdt9P/fDEiuR9sO7DolGv+WVBPGbWKLs94p896sVlDTYcZSnbQY\nYjHT+WyjJ0RnxoWjMWSsDBKIeZlQq/BaKV+z5GYFuCrxlqGQ+A9jVRoIoNLW\nKLM0brq7RkxRh1Z/Koh6Uj44v2/8Qmtu83T5bzhRu2Xd6oITBSf6jk7+xbFX\nRROk9LfYMCkT0mpxndVr8Sw+gTR1XGkkaubwc2EPcrCKSHSUWWObNW53Rhr8\npKu4NaOGVgVwYoHo3dtMGJumSMa/X0rWLnbM7PC4XvvTGBIqdm+9B+xcN6vC\nn4dnm4NflStcg0cFEdOc3fAeHRx04IisUHCSl7t8NMA8hTFiAVqKmpNluDJx\nveOv\r\n=/xhq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "13e3342ffb9e13fadf9976722b6e7668323af3f2", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "^7.0.3", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.5.2_1605698586810_0.8820486033756025", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.3": {"name": "@cloudbase/toolbox", "version": "0.5.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.5.3", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "6581c040ce29ade7500b4997bc4548fe222bf6f5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.5.3.tgz", "fileCount": 138, "integrity": "sha512-5VGBEpe7kfel/zQa85tDtYaFMZqzpgBW3LAroxgRUiHwMrWHEjYJ9D/aSH/icRyEnZmZLAXsm3B75upI5J+Hpg==", "signatures": [{"sig": "MEYCIQDXwSdADxk+uPwzI7T4Y/S/usCwAL5SHb482o1av9+96QIhAL3t93zqI4MZosRWaB4/mht5nAYYC4QHoQV/6eNIceL+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftzXUCRA9TVsSAnZWagAAeDwP/3kFHmgu4b7+2/o9jgiD\nTVfVl1Xl+8oJiG6CLC3g9Ksbcb+u4YtAkUX9WCyjifm6Ku9uvrkKF9EV6f2R\nV9FuzXFS6NoVAIjPIlv+4nrHA8FKFUjYPtt+DagonvtkPtA+cENwm6SWltG7\nVab2y4cu0p4Y975WSFs4wuCIoktmOaGSTSCczRQi900joZBhAPm36x1MVZ7n\nO66VTpDyY9P3nRp/8BErVRvwOaWHMSygQnYF+0N99poDGFVe0zS0yzAcLfeS\n//aCcKjV3nCWJcakGYaRJXa331KZHbbHFM5e8FK0Tsl+aMTHV/2S6N6QP1Be\nVa+CnVo528Ger3LcPNoFhtI9hQezj13uvP2iMhXFpxaK6216ba+ufwgKLDa+\n/YgSWWDdk5IFeGiW9AujEdEkdmXLONEHH7yrymdmcVKUedip8AYzJeerCmDw\neS5xZdtfhQHCj/T47+TFks0RGwhCOL6mxjHDfWYHeMG87t5ob38Jp4DSbrBT\nXcDJsj2wgD9sBVeGBX4307I/+hzk2up730kCmRLmPs+84RCyQIcX6s3RaMg7\nK3YQYIgFXlx+WP76agfCWBeL025Vjj2EWLxUsM4bg5mdlOCyjFeTGBmBXRqP\noa73l+crjsrIrl7oZ2jdV5d5MHlCopgysAV+s4wRAM/8NI3KCWkLp4TZ/Rm1\ntPqA\r\n=O69H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a55310af0fc52870b69728382d4167581ffd21f0", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.5.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.7.4", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.5.3_1605842388218_0.8157129722971428", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.6.0": {"name": "@cloudbase/toolbox", "version": "0.6.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.6.0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "36d958ae99421b087ae6b1a905c0af3a7030f6d7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.6.0.tgz", "fileCount": 141, "integrity": "sha512-qa6iz3I4Pi/QlZ46XImwwW+TTQLo9ITS8eAhvTLfbE0VVH3ygS3kIYX2JmAfp112p+CzXJYqNgWAxZzh1wT9OQ==", "signatures": [{"sig": "MEUCIQCP7ex69vd96Zch993B6ILFkjGfW8tFIMCXrW4GP8IQXgIgMlSXBxAt/KkdAcngJa8WtoXrZ49jzhjM/oMoQVMWJWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6bf7CRA9TVsSAnZWagAAcvsP/iuiEQHYVv/xIGUxnfSP\nFYn/nM8jtVWLvsSEmlzyE4knDPMxs7r6BBhHhVALdwMHJEKe0oumDScqQpdp\nInseProHjvGvBXNuzXmIlzF6u/pbqNbHjbrvyrd5/EDUe8hHlrcj77X3S6bc\nDGeJFrmv4uGAsAFaFbRaiwmkQa138ig1fvwFvtjcRqo36fZgr1Zh701oP5jw\naydPW6p+1eJAq+bOZ+2A0fvMCTokO4baEsREAjvuxcUpi38HGWn90ihHD0Pj\nrnE8+cAvNGVhuSGLQavz//WphvggX1CSeXxQHeBPLyKXLxZaKrIBUf0wPte+\nFFGlrqP0mlpfiIAhi4Nt4WW/hc0Xvd1ywc7sbdTM5MgP/i7AC4772H82Ru9B\n9ZiYFfEVZ/+Z+ivqIXJQTj6cnlhiPW7rf6pvXt99myCk3quspCn6hSjfAyOe\nCZamzwRrtsnAcqjwVUp4unZGzPPBawkFnEyfPpJKKyJqrZVyOCLKZsoWSFUQ\n/XtGcyyQtIRnIfhuVupQ6cJqsmJdioAnvvcNpyZFb9mjqraHphbklnu/5K2Q\nMVrgLs/4xZsQFMu9hw/PNCIbapKk33qhk3wZiiDjMu9MNDZsiPzosgRy1gp3\n4dSoIe6Hmz2Dn1WunNB8TTTE2XOPj9rxsWQSibDf4+QaUG9WYbqVkTjNJ4T7\nXWTG\r\n=+DrG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "d706dd72aec3d5e9470666faa31640017716089c", "scripts": {"ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.6.0_1609152507008_0.047580972187426074", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.6.1": {"name": "@cloudbase/toolbox", "version": "0.6.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.6.1", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "219ae6ef66ba37d554cad72ea5db0cc586390054", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.6.1.tgz", "fileCount": 141, "integrity": "sha512-zAZIpwiXdttvgxJpEdTg3lSPXFhDhtkQd0QAFibIzbbwuw3YqAYBsa5XU/xIXpYTfdVqVKSE2OYD66Hwt9WaSA==", "signatures": [{"sig": "MEYCIQC6iOm2FTfvv1ceT2C6zkHV4IxghC+0gWhQ7MRdKuR6YwIhAIktfRvhHfHBLx+lZy/AbELiKHoYV4ymj3N0TRLQbZ2i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9SNYCRA9TVsSAnZWagAAOd8P/2g8MPsWPAaQLxrOYXkt\njwnheZfzySBT+BGLb7mN7W6aukEXvlYwtWXbtbqhemLBZfLMB96pDdfg1J1+\ntXbAo0t98LNZH4Wqemp1F5Dw4wKASNFOqW6SVPB2mZs16HPPJSlkUHt4knmd\nmv6j6oCu0A1YIp/g1zHqGP4/BriFA4fEckoyC+6sPouvoBYoGpyd9YyaiKOI\nmk7VFUDfNRkxL0F1g2LQuiysVi6+DB4PbN2I3W6NjyQ/pNZgcZnQfaaR35R2\novD3A84bI4xf20e4glr209/XazfxI9wjsqpmFggO9I/fIb73mMQssletf+jw\n5lOMuaw9mkBULZKQ14A1HMaho8JG3h2ss1LPVgOcTCos8RYOJLdof46HEhzN\nq/PqRlnHuffiTRGt6TgCjh/BbDek0zhKAcrgcaq9ala2KX3gt7aOSeFbuGcn\nBTtTdwx2m1kZw6AnarcxfXbc+vig4fe5mNl8nRtLzDqvEo25ymx2uiVd4/mk\nSCSBkLX/MwtInAxEPdqfV8auVijHwVh0zuoUAecokYRa4RYKKs9DotOZ/QPZ\nOyN3aLKYBRKw28gIu2iZBWkvC4Mer19fkxBnHBw+BxvqIbWdGcj2hTNuHQOZ\nm/ZQupvMNFP3wh4PelHC7DTcbbYK5WgKyqD3yaSr1UbGRERMsQ1MDcvGIg27\nY7fI\r\n=tTbC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "e9be66a875183d0a9b1eb7d1257e7eb54904b9ba", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.6.1_1609900887947_0.23701098204498816", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.6.2": {"name": "@cloudbase/toolbox", "version": "0.6.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.6.2", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "221b119dd4d3f014c9e6f0a0baac9f828eeaf00c", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.6.2.tgz", "fileCount": 141, "integrity": "sha512-S6YpCB32xZH/R7Gk3y8aXiAj/tYXbx/RB9EEIxb7jq5YLOu+IQsQZYsqCkbgWnQwRbk+p9WvyXLh1MjPVuiNeQ==", "signatures": [{"sig": "MEUCIB3gWu/qrgHau5NaJCQZAj/9ORG8ryNubiJoVid2NnoSAiEAzE6+xRpwdCZ5WDISuDPukqJy6Q2tI7fJ1vEvWKZLBTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBTJ/CRA9TVsSAnZWagAAfi0P/1DpunnU7XpED9LwtK7P\n8Pw1Ulry0GGUXxMvMnxcWicO+CQ8oKZg1uHvWT3Dq2ZssXuDCqGm2f7bxKDM\nQ7pBT4xjwLgXIqX5/WT0VYGGgyuXaKOQEy5h+uMYRGbSfXyswi63mlyqBoWB\nVEk/CVrZe8FTZx6J7EobZ3QV1wnSL8CM83lFvdqbLGJYr/bx/8OMjoKQS037\nLIVCKyvP48kGhVq+CL7UdxEQ5RAzGlMTrFWGNv2Q3XzNySHpAeF2+x4gOF2X\nYLAVYOxJLZcFettQKMgP06ESrZrhIRKDwtjXhYHG6oe1F+J8YnopmRnFXYSh\n+2khd70OJ8FzL2DGBWx9I1gLHeA6PRMcndWUx+A0Jp2xREQqbNuA2fcJ0DVw\nP8DnAxqYb/5dJyeKPKLDL07BzNSRgUbjSR5bdA7sPmgM5xys9atdnjHbLeaL\n27TZrTAcDWLvpXIkyU4Wkywfl7X0hSGO9xYWl07SBU3gNn1Nkt6ml3NDFU04\nx6UJZRnfI9wmCRMLeNeVPiBnycAyEpvQIE0e/hVR75KVThmBz4H8dYC2iRWO\nBiU3xNfIIzh5QYYg/eeLoXhE8lbnzonKjr9aXQI0WghDfnnjEyunjEdnuKT/\nZ07N/WK2BlmVLYl1KRTgknBgRh97+OQXmb9y6PkVroVE+HyQL385WtNyBc4S\nX4sj\r\n=zjl2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "59bddcdfaebc4ae89afde176b2abe23e1c9d6564", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.6.2_1610953343380_0.8338192323923781", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.6.3": {"name": "@cloudbase/toolbox", "version": "0.6.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.6.3", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "d36a1ca9ec82425db3651a83ff89f25ebbb0b099", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.6.3.tgz", "fileCount": 141, "integrity": "sha512-JH9a95kpjZsShhLTAOt3PNoSEH45mUigeZjj3Gb/0Uvwod3YzZItmUiejVbMJAz95saOK007qHJdzd5hwHhbTA==", "signatures": [{"sig": "MEYCIQDR0nUflWGlU9UIB+AXaGJfUh05pH+zRuEm0oaYkGBxBgIhAL5DbaC15d3zP73X2SJFJqIzI1sUQ0iRwa8hm2ub3eZy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBr1HCRA9TVsSAnZWagAA8soP/1ChbqwS79jxmdpUIRnM\nhGET4QyxkrmjJLnOVwsn5a4ZaQ/zvlaB55EgNuV0IgMF6Yi4Gh8Il+8RdMO9\nPGFC+MBXeoKjI3KYywp0SLzg+wCGa/gz7aIH4GFyaPjOwgVkRYsg5dP0rz+i\njawo7DW/Vrx1/kmsMbUH2b5+m8MbewjB+F3qx2oIfs573GMXfpUV386YcGT9\nbDtifO0EV7lTNGzvbl0OIvs6cj94WqX9s5b/S1q5unxeKvXBz3XlRV7BoHI3\nAczB6ZWQDngJufXL+OyTBlxkciCU/l4tDN0Fy8/rxONY8vIfwk6SNQPxLq1g\n7iYAxDsg9YaaWvDmd2t3WjUABak5pG7S/ugo9Vv6lgYcWHM7MP2ERe6KomYk\nnAczHMsxsSuRHFIit71bl/efspFRAKC5cdnIsITsiOWTsmyeaAfzuq0PmAYJ\nl/3knSEL2J2U9AMTmh1+9b0rUqcWyGpHQyLwxGUTK1af5GauVfeceDa+M9C2\n/9ZZBOkXXjv+8DSBA8nybdCr3tSSFWRampUBloWUSbiVfYngDpryWKHeL2Ay\nHfsOZtVkv0PPld68Ry39pPzSPflhfWzUixrUIKh1bUmuLPzm/QqCKqB7hP7J\nNxJin298xi+Oc/1vWRKKxzfUvSLhw91XREffiO5KkCgXWt2MURJtsvmOt6ly\nTLWm\r\n=z4Jj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "1e193d643430610569a9f5ba6993233f4547a3cc", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"del": "^5.1.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.6.3_1611054406616_0.28245186836791536", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.0-0": {"name": "@cloudbase/toolbox", "version": "0.7.0-0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.0-0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "6b5aea9a34c2bca347ca1a8a5c51c13a2772fc1b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.0-0.tgz", "fileCount": 163, "integrity": "sha512-XaGmkcVwDy1R2oJI+8gRuUIw2SjRN3uDlGpSJ9VhB4Mwg+itZPE63cEWiYw/AIZzM3akOzFn1ATy0qqcwZ9NYw==", "signatures": [{"sig": "MEUCIQDHoYdVwhELm0nlHrLa1XlWwe47EGrehe04Rqt2KOSdHwIgQhnqLkYFVVcZkCmTI3AP/3e8N3xTA8HOaAaz4UqRib4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgF8eECRA9TVsSAnZWagAAncAQAKN+JJqeZDsSow0YugXY\nEHRpknUKK6cgQtavSGHlbgbsZL3X4/wdoaFCr6lKLpgareg5XXrXt9iJsrRe\nr8Eyp/nZbH4JFZkfvU4ZKwbEIlOX9qxGkjFTaJYguc1ZU0SuqjMwZ6ezM0zw\nWjgNW+dzI9PCEeCiPNBYtQgVtmY5f4WpqhLQCQhKq1KrOA5sP7bOxkn8tCGU\n4V5xeGwWO+/dYLGTVCX4wKleOp3JmMblbuuclZRO2d+cRzwxIJVv9sF0e9/u\nniMPh6bU33Lo7suYK0S/8koGdsvwIToihhK4uFx+R4mA/YRQLXVKFEHW6w38\nxlFAPOa1zTGJMgIpRmr3bufeeT8rf3YAOT9Ut7vck2BEQVMkwG4UbdL0SKhg\n8mziw3TfOxevGnIEbIs/ZAdRIeAg+Cfsayic/l8iYWeefm9/pRUohyk7gLq5\nqjAr391lPL9QSPRC5pB1J6DBN1xPOJk39VvR5DnQrFeK2kcxETpKYBTHjKh8\nYVmLkTDKhNp6V8HYUxUBpAj1xAAKXShkCiLZidj8yr/CxG2fXKUepDvniGtC\nehO6kr8wXmUVJRH4+43rR5Q7S2Xe4q+COKNaiipsxpO02ry/WJrhqxoc5Qd/\nBLJ7xgqftHBirTFIIZO8IZASjdSOmeV/imBSclZIhfr2pJjgsUz4Z+YDkNqG\nVphq\r\n=CCwv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "5a3247d8709a2aa52d006c698b91fd5d0f3a0340", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.4-0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.0-0_1612171139787_0.1872928975392183", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.0-1": {"name": "@cloudbase/toolbox", "version": "0.7.0-1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.0-1", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "9a4308ad1977b1ed6a3c32a1cfc16cfe015888d4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.0-1.tgz", "fileCount": 163, "integrity": "sha512-cPpTUwh2ArNvwYUUz6z95NJEQc/MoM3sPUvqQoCb4DcpFJSRdUKE8dti87D4xykw7nblnD7JItKe4m8mSSirDA==", "signatures": [{"sig": "MEUCIFEgmvC6rFYOt5n5Pbu8ps8Fu4dDPXoeJC6b3vCZ/wQoAiEAitNPSXXSEtOeu1SVdcpHCc7dnpbTfop0speJ76lVYtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgG7u4CRA9TVsSAnZWagAAJHIP/2ZBPr9bSMblhhe2LSJH\nmtXPq5NjqcfpV5np4CYZmEI/KfRUf1RVV5qhGH+aGyD/uXGZsBXgj5WSyFOc\n41ROBjxpZl+yMXjlKYkem0958vdoIMIti0/S4isXLJAXrTnNzr+v7mjhXWg1\nqpGHOkuVHM4KaT0GXomHLl5xKlFjStXtpN3Uhu2Oa6G5CO/TQCeJAd7WsqSe\nw4YF56GqDC/ZiXJC51V+XyUx+ZFpZYogw5MUDLqljRbVlWor41vSXCmXFvwU\nDcekYQ5PzFjrjA6G/gXo4mVVyJwPLPHzUZsiEVnxg4eVe3H1WGbx2tJMf6/w\nsuhqx5vr4PAlEixC98+cNi17lGdAk67cyMxi0bAWrlQJlD79v1abjgOaUyyc\n3OURVn3Knun/KcyfHNpz/4/iWJI9LMeLD1kmQu2e8ZnwbneUTdAZ1DoqW33n\nvYDi5+b9fEnQkcnBNUHVl2Oigbhfl4vHBZ8HeOQ5hdmKD1jOmAYRpTuw2nXZ\n6jpSvhkOUTl9NnfM621cGAcaR88qsJQiufSnv+++QKUgl7KTGhbf+68oWJeK\nSQnJPtxwfIcqqSQFwokVC4aZmXh+xXM5VxcJwR0dRHKS/1SOWWrV+/ea0cE/\ntY7z+8zhUhMCgJK9t6WLWm9ki0j+T5c0vxdhQDlPWqg5YnephvKo67LeYpdC\nn9WL\r\n=3y75\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "5a3247d8709a2aa52d006c698b91fd5d0f3a0340", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.3.4-2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.0-1_1612430264030_0.46914926253261613", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.0-2": {"name": "@cloudbase/toolbox", "version": "0.7.0-2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.0-2", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "8ecf6a5645534ae25f9acf1ff4e42b50146e4781", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.0-2.tgz", "fileCount": 163, "integrity": "sha512-3tv7CxypEoXyNv8ZSLzvhzMFLL+SA3pduoCACKpbzZFZ7zcFkND9CEsKnotzHAM73Ts1gnMtlxQ4GAQgC5tCsA==", "signatures": [{"sig": "MEQCIGqPY9Oo72PtG+rLelTs24i8aKxW3jcyM1FZAY9ZgYwqAiAzd/xga41sXzcfLhzRC0KYMETlQdHzmyCo0pFA4QKCLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHAX0CRA9TVsSAnZWagAAaucP/0fme0vUINR9xMHKqVY8\nKp32a+Wxd3t4ESqk/QKt7LHes5qTVf3exU433RfDYEYJVHYtBbnOOEVCY7XZ\nOhrLDUyx6zatesMxs/CRq5q9CCU4Z7O6wetDKvsff+zZ+UW4bevZeBGmBzzZ\nxM2sIRPr70cvWHx7zajfeGCSzhwiXvqcL8krPyN9pDik7WApb37BCKFPzKUN\noR3bWNNdGSIwKuhktTGavNeFmEGzmq0Ca0dTgZ2v1TOOk529as+tRGawfgeH\nLtpvfyeGazgpTDJh3G6gKf2VSymkKxFPrCniEoJuk9Pwo3u+k9sL/JwhfSCd\n1f8/SaRVwTGjdNVj9WgFoJ0DEMJEBd0vyy8SI1zOLFx5cyndIWVT2s9+mqDz\nZVJOPL8HW1+oF7WrRRcfUAwqRsi1/utitIE9Jk2GqUy3kLeLsh/00i8NmY4j\nFoyUwaN29d0/n8hXE/DJU+WNQ4nFeDBTaFsJcm2IeopTDUL1YMfH4MsR9WeN\nDYm8L8hjHkiDy0kFAJDEkIabmjCEcEYKbfi6S3Nk3/ty3Ygh4ltAc9IgvlsG\nzyIH9fejn+exhhW/UIoJLj+Hj/e65YVuHWE99CwVQtwhpQ8yADkOnQR8i0B5\nJtQJw4IPLLITE/KBP0Rjtyb8JwJ/i+7+Uoedsfm/C+2Tx0xaRt43mYArE+48\njoLY\r\n=kY1G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "162726e8d36d3ff6dc3c4cf8e988d0cf7669d22d", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.4.0-0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.0-2_1612449267747_0.7320690287516365", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.0": {"name": "@cloudbase/toolbox", "version": "0.7.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "f55d039c77bd1bfb0fb9e137eb0c440741d20676", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.0.tgz", "fileCount": 163, "integrity": "sha512-BBIJSSiZZdxjzd0SqMFOockId/xo885Uwm9xjptlYG+4x5LcYNunaRsU/21Pj4qgawYyXTg0BRD0i0Fj68VyVw==", "signatures": [{"sig": "MEUCIHrC15+Gg2uZZZZr/5aPo6KoyuIrkDWujpm1VJ1g3vjeAiEAoHEzTTPiD9+rQ+mQaU5pbXcQOgdgWRu5w3zdPfhq2Fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHLzMCRA9TVsSAnZWagAABDAP/1/be6FmxNIxNISwNzxE\n3WGYJnmTY4x6U4366l6szxfN3nyLQWEjHyyDKD9nYfWBjPUU+H5644yLbfXh\nEnhK7Vipm+PhqVV1LgWf6N82703TnMYEfgYBGHkJJ+2YWrpVseyb6vsVHpPn\nbPibM5GqfkQVoNpLmpPvAoVPdE2Y2Or3EjlTHejBcezdXeBB2GQQj3Fsqiwi\nTYngrv4FwK5gm+FTovg3sYOBaBGykr5mnkFjGvfe63HNF3J2tMiuTB6ILCHR\nuspi7YIy65MZUE60KFoT+R38cpY+rK4N6ONhGvMud+eJdp0cWrQgp3zs7uIW\n2y85fSnLr4SGvOYja+M22IxL0nQXS+z70dKFaAUYBS8ISjpo4MmCOuNcH1MN\n2rgqo3IydXDrfT8F/RhkhiF4QFoirnLJZ/EPGj/LRSl+DS7AlMrGbaN3br/S\n0a9/iFrwvUOnxwRhFt/QfJDpeszK5j4m9qEOfxQn08kB5GAQ3bwWzXrHpH/I\npZaDZ/MsuZTCx+nXdTFV3Y2PMiIUEFEj3SHYIY90Jin2XLaGPwZTHdGVe1JQ\nddm7v9+zGoCyn+OZGHQyTNI+tRZ4p3GN6n6IVKhMXW1Lu+1mLNef6KOcMXAM\n1zTWluDlI8uhfdm0XbDyOxaqIQPzXKWrnSJ31O7lCcElULBePNHCgUX/gwZE\nWbKd\r\n=g8rx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "d8f44685c3f0bbd657bf6e8e09c086032a3c6d4d", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "@cloudbase/cloud-api": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.0_1612496075410_0.3866352313560659", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.1": {"name": "@cloudbase/toolbox", "version": "0.7.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.1", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "d70bc87b11f4d36b4c5a174bd042703bbd9dc09b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.1.tgz", "fileCount": 158, "integrity": "sha512-I85w5X/4zTkboXSclb34QKDY9ScLcTVRy6RSVmYxVmOm+aekW8fFy8O5BUo2Is+nJngCvTbOz2S2gsm1/ECPgA==", "signatures": [{"sig": "MEUCIQC9iNdy4HlULKDLGK2Gd5B26B4DzzAQC7mJhC5EEQw32gIgbB7lEBLSB6knC8qIgBuy+kdmS4jW42Fd1lTkS06ZaCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 501222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIQN3CRA9TVsSAnZWagAA9rAP/ju9AJ+FXLGaQiQAgGwA\nGmzTGsKGkF51BE4RHVVtjpg86cIHj4uIsf/4Ub3djb8AomViEFgwo16D3n+C\nEnpW89B5UdIKGQ4/RtTfQmIdd3uEmQAS2UCnlUUpF9AVoPY02OOGaYw/T/48\ndf1U5CR+hH3wTOcLzNtJddE/yKkBfLhZOFovvS74rvMOn5al5O4/kiSL4+Po\nLOJHy/vg4J6cDkAB1MaY6G3CSycmKzA1e/PZr3r1CUsK6ebmVgmLh8h/cori\ncHQzFrOtbC4Y33WvqhRxWUiWo/R3oWWnDooMpjsXBFM8CnZvdWNf/yrSvAyv\nXp557E16zw+hjvydAxhHTmW5Ao5X79zBb1yFxkDYFdDk+XHvUGj4hS5cp2Wl\nTIbKhkzbuqwrg/3zydCb1Befzg7Tb214oKwo3nN4uoBFTjmE8ETgLKjRdMIo\nbcKjfr4s3o4IrCkgwhb0Tchz5AXrw55XYzFz5lsYAbJKvQQ/w9WRArM5iD5G\nfWKOfsbIVRGqveUWfviJyEVQcetdHjdIvOPdq+9oBdb7F6v2XLawoX3L/BLc\nm42Xc1zGKjz4VB8ZQ1Ig597F84h+xIDC2kHBE/t4LGg0lokUK661qyse7n0W\nPOHicJ2zJeuyJjBnp63VViIBnWlJ9fNWRR/0XvybkCy9tP5wQ290i+OkU+Z6\npuLT\r\n=ZYGc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "59cf927e6105f54c868ce66fe9bbe050970fda56", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "node-fetch": "^2.6.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "@cloudbase/cloud-api": "^0.4.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "@types/node-fetch": "^2.5.8", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.1_1612776311178_0.9611205933756803", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.2": {"name": "@cloudbase/toolbox", "version": "0.7.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.2", "maintainers": [{"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "08fbd074afc6710c94632465cf3fddc8b3b48a92", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.2.tgz", "fileCount": 157, "integrity": "sha512-uVPtvvKoQI7joIY4y5se4J7XoQN66MJFXDiRtdYYi4wWDnAWuVALnJZKuKBufc0YWDnJY99xErYTPJPnF/43bw==", "signatures": [{"sig": "MEYCIQD0rjFPp/M9Ymr2iBddmbRA3Gkk368Jmibe+XE4Hes81gIhAJo4ZvtYRhvLdccVcbYUenJRD5jJlBxf4QDHrcusUNt7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbVWGCRA9TVsSAnZWagAAbrAP/0ACyb81Qlk62gu/U4pu\njA5UlIXM56AhP/MuJvwCM+v9bE1eTFygxGKIaXENJqX8AkC9XRshAxIoEydH\ndOsNk3kk3wkUYLqI6IWptjqEX9tdmQxABBQ6W5wFP37ZDQuUXM6Vb1rVpVFY\nB5yoEurLJh9dYlfFPD80is0QHh0MaXS8Jyk7e4s4d/PN2BiBKjeW/ir2hWPW\n7RsfCUahfLE1GyV3Xvhj/U2zlf3QTvyoD24RWfPQK/7HMTRRIxANXcT6EjdU\ngCJ9sqAZgwHRfc75tKHquxtwph0UVKSc9dXmmWO3NssUdYPCaF6fUBv0x50J\ndSBiPyA7zG+aRA6EvllbtGbTuViRSCVmzH9Nokz7MIH6MARU+/bacU93sZCa\nR+QztyTuY7Idbn38xd8Ic7Q5GLdpRT1xVPCXV9Jo5tOUaPqQYhpsPGgRw5g6\nq0TzZNR1H1uQjFKAMX3F3WwhhhR4ZwV+C6X0zQQRb38v1eNXWpMT/E/Y+PiE\nFooKhXQHVaiPO/tEE0o8wOHRefrrDQrYU0UYOTGFph9WEdhJhDDIJRRG1D0J\nNrjJt/YX8BAAbF7uJ1sqIuINCZDjBcsd1qYxWmxFgmKMzNnf91JYwrmRmAHw\njBbU7Iu1FeUXNoA7uZvzBz3BTMbpLYSKKHApyAqBPUIo7et9YJEZN+zLSUeH\nqaFp\r\n=A6+G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "a003f5e80200b1f91bd94d06bc06cff8c1b26884", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.11", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "node-fetch": "^2.6.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "@cloudbase/cloud-api": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "@types/node-fetch": "^2.5.8", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.2_1617778054085_0.7421065837444343", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.3": {"name": "@cloudbase/toolbox", "version": "0.7.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.3", "maintainers": [{"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "a45a64c8f00539bb34c23ff88b20c665ac2fc42d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.3.tgz", "fileCount": 158, "integrity": "sha512-ZvZF4Ugic/iyYeHypy1xIUgXldLe1yKE4Xb/l5uqQlSz732MFtJC9HdV9PymfXodsKo2FhTndAhGQUaB7vuBGA==", "signatures": [{"sig": "MEYCIQCwhx1ggojKZR+v1H1DL2h5dhvTmRiVHxYoBZARFE5wYAIhAMddA5zNc7EpTFmshs4RxrBNimd606htyNrWEtaKQJXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqTz3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosIg//cf+75blGj+T9+bky0xDllZUVspxiMqrTEllShLa0f0xQCpEm\r\nUHF/u8uV0qFzVCwHParzSUcSa7i/r8raB1w3TJIvcOPEIWklwAEYq7DKiafO\r\nnYI7u6V7w2h15kyDbx5nK9Z+FOW3KCMpDG0uxn8gSmJgWl1qdcnDG9D7Rom4\r\nH0WeyIBsl97ypAfUyEKr42vNMvYyTXlPFjUb/ROYDVQvcHKXhDmqjwxuhrwW\r\njmQk9qIRe87qSz1ghh/4qcdpvV7zgb9wwk1rWTvKOqc1FTOs6/hnOwmsnprF\r\nqQzU0JxZaYHEipQynpZs75QkxLeSLNDayhSLrNJjiR73W+6c/Cudt9zyIP+3\r\nrJfdpqeH/aP5IBIIcCEeNuxuP0btlnk1VXv5iuH1oBPtvB2dzToByfJ5Uezw\r\n+VPc9XWtkhucqt27Pp0u3ZxBtaTE9ytILDvepkQgsh4dieA/xwQ1PZAm6BYY\r\noPiVRWKT8/DjOgNnPv9vaDJCFrvdfrkBLe9pU8nPLEOCd1BW8sVpUdc4yOQc\r\nxWsUvk6Fj1d3xqFUtGPFXe0CZ9rlxLNb41mI/LLTUetiHJkC/Hh1s5e3Lgsd\r\nsLE6rGn7s2fR0EubZUe2OFhvKtXGaNy0G/Y2qhoxZ2EMjwbg6RHEdWYBoKwR\r\nd4AwSlp5ZYWXS8n5eE/UCP8duPgirXd2VqY=\r\n=UOZW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "types": "./lib/index.d.ts", "gitHead": "5e8711d110007bd2534a654e8b52cf2a5eaed557", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "woodenstone", "email": "<EMAIL>"}, "_npmVersion": "8.11.0", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "node-fetch": "^2.6.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "@cloudbase/cloud-api": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "@types/node-fetch": "^2.5.8", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.3_1655258359561_0.2331287348698723", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.4": {"name": "@cloudbase/toolbox", "version": "0.7.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.4", "maintainers": [{"name": "bingg<PERSON>", "email": "<EMAIL>"}, {"name": "vancece", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tsail", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "2d9bbebe1d85f0899c480429fe9f257b7ba8206f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.4.tgz", "fileCount": 158, "integrity": "sha512-Qh0JxhQplOE4CitvuI0C2VNsCe6xCvsokH5DuWt48TgTzxglgCWjG48Feef+Z+bHsZmEq21ykadcEd5k6CTohg==", "signatures": [{"sig": "MEUCIFX2GqVQt7y4GMQliOAONAyNGUcPDjKpNr/YbLbwvIhEAiEAz+VCu9K/BdQbs+IEZouoDPc8EntgnN83WwWpm4HEx3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhroCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0Aw//ZYkYy50hpDj7XqVIyPsHdn3YbJxN/LuxEt8KqivdOT1l9xKO\r\nSV29YlH/6yaVnxxwR9Zo2nZoZMDV8YX+GaTdY1I8dECTG09sJhNXhpKHXKtT\r\n6NCWVerLDK/3pwlf9VRbfwm+jdLrB2MVACzC8/6aGlcnkXYLVByHPk/b3FAr\r\nKnsix24YBDgG1hVSc86F+j2gTLJWOlIv2OHa5dgSrOECOgnlPahlH5iEk5BV\r\n1gpfxDzfkwtr6RpLcuFHS10hsHEBrera+yVNh+wpdRmRlrFG+C8Qn17Yq+Mz\r\njGDqLytkZj9aErjhZdytU8VRZyfRM1/FZdhvzzGI4QRtTzO7M7tLQZKKctF8\r\np4ggOfObvlqhnd9W+qYwktZbVXVAkbML67zCF74krAAwKPyqp4/gQ/DSD3Ji\r\n8Z93FoAuuTZkrpl5eDM86JsAlXy6pQGb4FUjTFKR0elT7wbJt2K+VYjVPygZ\r\nrBU0Yp86WqXNiv760r2Hv2Nkz3eKZQ+2pYTHTGj1FZMVVIruTswMp9yzLoYR\r\n4796jwkjlI4lkmFPAV2c5P72R7DHsjDBH4DNU23MtomxXa2oRcQEaUg0C05v\r\nsVa8m9aQ8oXRyYgQWNZ/K3fXDT68GPB0rCipIwBuNmWTJV5wGETsB8+4MDm+\r\n613qUYqyag7NJl81KoUMmZbuTZ/0Bx9M1v8=\r\n=pi+s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "gitHead": "687491ee40b41e8660ad65214f102147f9a412f6", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "woodenstone", "email": "<EMAIL>"}, "_npmVersion": "6.14.17", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "node-fetch": "^2.6.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "@cloudbase/cloud-api": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "@types/node-fetch": "^2.5.8", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.4_1669773826342_0.6196891109904983", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.5": {"name": "@cloudbase/toolbox", "version": "0.7.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/toolbox@0.7.5", "maintainers": [{"name": "bingg<PERSON>", "email": "<EMAIL>"}, {"name": "vancece", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tsail", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "c9f38ba098f57d23cc37ef5435d0403a7ce7e7cb", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.5.tgz", "fileCount": 111, "integrity": "sha512-7RZhAbR2ZkukoPIqqwpZrEJwAPeTBmR1n31YOXNrFjo5lfjvP43HbXik7n6zee5zDej5ctwOwk9BKL4TqqYrcg==", "signatures": [{"sig": "MEUCIQC/AmhBQMQ/6p0vbAgR8NxLilAVcpWpWFMP/V8Bw9LYfQIgYEI/H0XpSpegNLCVlcT3suhVhq064XPlOgQkuHv9pwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHBpOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotuA//SLzc9GEndAq1tFMEHPeP9mVSk5R039hmfVdOISXO24Cm9yS3\r\n+htxf2v3VGkdU4dUZUHI9yi0P3GHhyYIioS9UV3bozzjWClTyQTXBONzabm/\r\nyicA3oeawMbL5hIdtaJ0+szZUumHHmzUaJhRpVvP9/k6FpFh3yf5rhEvUnKz\r\noug6JDoHDE7kKSZ1Z6dpYKGzWBQknWePP7rbJwFNcbW71rhta2Gd9BcjugUP\r\nf5OD9kbAkGVRxlxVePzKxT4twFI6jkxMIJfI//Jm7j2gXELNYUKiMDW46Hu4\r\nuVAMU879wvS/iBTeConYAg1CC+0Vh11hwRYOvk69MEa2bM6piFpNPHuMpcbA\r\ncKVuvgM3HFj6QzLOozJMjTSuv0wIfxJ+DwK+3PorUZArEgD8Axl4Sx4tVpyO\r\nqMOWMMEUGB6dDa9VotospOr2e056YEKhH3SToonaoXVZE9SitcNUDs5Zf5qz\r\nXBI4pLKs2bTypJ/cv/V61V+DCKgF4n5/BB3legEPLDGPgUpClyVLPV2o5WQe\r\njNG5g0BcEEwyME7AeKLYw2Aq2UnuT385p5DuhbMXWtyYTTw/br+2KCy3pzIA\r\n1cLCYq4DYCIZdKHHByqoraWW6NuMVihz8YjuqlzWtK9o9k2jk4KPEVxRBAv3\r\nfUhxiXifX+lrEUk9nyhdk1f1qpu2t76s0UA=\r\n=Es8y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "types": "./lib/index.d.ts", "gitHead": "668b08a5883d3381605b040814ff4d6c87a82f40", "scripts": {"fix": "eslint --fix --ext .js,.ts,.tsx ./src", "ncc": "ncc build -m -C src/index.ts", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "rimraf lib types && npx tsc", "analysis": "webpack --mode production"}, "_npmUser": {"name": "woodenstone", "email": "<EMAIL>"}, "_npmVersion": "8.11.0", "description": "The toolbox for cloudbase", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"del": "^5.1.0", "ora": "^5.3.0", "open": "7.1.0", "yaml": "^1.9.2", "lowdb": "^1.0.0", "yargs": "^15.4.1", "dotenv": "^8.2.0", "lodash": "^4.17.19", "nanoid": "^3.1.10", "address": "^1.1.2", "archiver": "^5.2.0", "jsonfile": "^6.0.1", "make-dir": "^3.0.2", "mustache": "^4.0.1", "path-type": "^4.0.0", "decompress": "^4.2.1", "node-fetch": "^2.6.1", "parse-json": "^5.0.0", "portfinder": "^1.0.25", "log-symbols": "^4.0.0", "@types/lowdb": "^1.0.9", "import-fresh": "^3.2.1", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "@cloudbase/cloud-api": "^0.5.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "husky": "^4.2.5", "eslint": "^7.16.0", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "webpack": "^4.43.0", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "webpack-cli": "^3.3.11", "@types/yargs": "^15.0.5", "@types/lodash": "^4.14.158", "@types/node-fetch": "^2.5.8", "eslint-config-alloy": "^3.8.2", "webpack-bundle-analyzer": "^3.7.0", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/toolbox_0.7.5_1679563341910_0.7255538189333111", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.7.6": {"name": "@cloudbase/toolbox", "version": "0.7.6", "description": "The toolbox for cloudbase", "main": "lib/index.js", "scripts": {"analysis": "webpack --mode production", "build": "rimraf lib types && npx tsc", "ncc": "ncc build -m -C src/index.ts", "fix": "eslint --fix --ext .js,.ts,.tsx ./src", "test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "prepublishOnly": "npm run build"}, "author": {"name": "<EMAIL>"}, "license": "ISC", "dependencies": {"@cloudbase/cloud-api": "^0.5.5", "@types/lowdb": "^1.0.9", "address": "^1.1.2", "archiver": "^5.2.0", "decompress": "^4.2.1", "del": "^5.1.0", "dotenv": "^8.2.0", "import-fresh": "^3.2.1", "jsonfile": "^6.0.1", "lodash": "^4.17.19", "log-symbols": "^4.0.0", "lowdb": "^1.0.0", "make-dir": "^3.0.2", "mustache": "^4.0.1", "nanoid": "^3.1.10", "node-fetch": "^2.6.1", "open": "7.1.0", "ora": "^5.3.0", "parse-json": "^5.0.0", "path-type": "^4.0.0", "portfinder": "^1.0.25", "query-string": "^6.11.1", "terminal-link": "^2.1.1", "yaml": "^1.9.2", "yargs": "^15.4.1"}, "devDependencies": {"@types/jest": "^25.1.4", "@types/lodash": "^4.14.158", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.8", "@types/yargs": "^15.0.5", "@typescript-eslint/eslint-plugin": "^4.11.0", "@typescript-eslint/parser": "^4.11.0", "eslint": "^7.16.0", "eslint-config-alloy": "^3.8.2", "husky": "^4.2.5", "jest": "^25.2.1", "rimraf": "^3.0.2", "ts-jest": "^25.2.1", "ts-loader": "^7.0.1", "typescript": "^4.1.3", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^3.7.0", "webpack-cli": "^3.3.11"}, "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "_id": "@cloudbase/toolbox@0.7.6", "readmeFilename": "README.md", "gitHead": "5778867bb0a7272fac57e18130aa36cb3db985eb", "types": "./lib/index.d.ts", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-KOL2r+awbpt4IeIuIn3GKMBcY6ND1Ozj0o2h/OG6JWYcTrvPuAJ4hJHQNwkJPb6oKbxJVOUSqTQcPq4wpdWRJw==", "shasum": "a2d349394a7ea5627628fcafafe4c46cb323a648", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/toolbox/-/toolbox-0.7.6.tgz", "fileCount": 112, "unpackedSize": 155113, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEpxLTd//EGVZY6sJ98Zae/1wQJYkLG6HKM0csXvCfO7AiEAsjNFDTPF/6hyVcAYnfKe8P0TsYC5gHLIxfs5K8FYn0Y="}]}, "_npmUser": {"name": "areo-joe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/toolbox_0.7.6_1753527808180_0.9115755971759174"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2020-04-16T09:31:45.267Z", "modified": "2025-07-27T10:22:47.136Z", "0.1.0": "2020-04-16T09:31:45.779Z", "0.1.1": "2020-04-17T03:38:03.111Z", "0.1.2": "2020-04-17T04:12:56.035Z", "0.1.3": "2020-04-17T11:33:25.682Z", "0.1.4": "2020-04-21T08:21:47.544Z", "0.1.5": "2020-04-22T03:21:16.439Z", "0.1.6": "2020-04-27T01:53:56.393Z", "0.1.7": "2020-05-07T03:42:22.692Z", "0.1.8": "2020-05-09T08:13:28.000Z", "0.1.9": "2020-05-09T08:16:05.361Z", "0.2.0": "2020-05-20T09:35:54.420Z", "0.2.1": "2020-05-20T11:26:13.769Z", "0.3.0": "2020-05-21T12:51:54.991Z", "0.3.1": "2020-05-22T07:13:42.215Z", "0.3.2": "2020-05-22T07:45:48.200Z", "0.3.3": "2020-05-22T11:13:21.171Z", "0.3.4": "2020-05-25T03:59:16.288Z", "0.3.5": "2020-05-25T07:31:12.590Z", "0.3.6": "2020-05-26T03:00:21.650Z", "0.3.7": "2020-05-26T03:13:38.678Z", "0.3.8": "2020-05-26T12:52:22.914Z", "0.3.9": "2020-05-27T03:22:26.132Z", "0.3.10": "2020-05-28T02:24:21.691Z", "0.3.11": "2020-05-28T03:07:04.428Z", "0.3.12": "2020-05-28T06:55:15.202Z", "0.3.13": "2020-06-02T08:18:46.242Z", "0.3.14": "2020-06-04T11:16:47.337Z", "0.3.15": "2020-06-18T14:12:52.596Z", "0.3.16": "2020-06-19T01:47:02.882Z", "0.3.17": "2020-06-22T02:12:31.646Z", "0.3.18": "2020-06-23T09:06:11.737Z", "0.3.19": "2020-06-24T03:11:19.342Z", "0.3.20": "2020-07-07T03:00:13.558Z", "0.3.21": "2020-07-07T03:12:39.390Z", "0.3.22": "2020-07-07T07:19:27.601Z", "0.3.23": "2020-07-07T09:12:37.549Z", "0.3.24": "2020-07-07T09:17:44.642Z", "0.4.0": "2020-07-15T02:57:43.202Z", "0.4.1": "2020-07-20T08:18:34.254Z", "0.4.2": "2020-07-20T08:29:07.784Z", "0.4.3": "2020-07-22T03:14:37.233Z", "0.4.4": "2020-07-27T02:31:19.353Z", "0.4.5": "2020-07-27T07:11:47.489Z", "0.4.6": "2020-07-27T09:23:41.365Z", "0.4.7": "2020-07-28T02:36:20.762Z", "0.4.8": "2020-07-29T02:33:48.232Z", "0.4.9": "2020-07-29T04:33:40.840Z", "0.4.10": "2020-08-03T11:15:36.180Z", "0.4.11": "2020-08-04T07:47:11.214Z", "0.4.12": "2020-08-11T02:08:12.048Z", "0.4.13": "2020-08-21T02:34:53.844Z", "0.4.14": "2020-08-31T08:00:33.483Z", "0.4.15": "2020-09-14T06:56:55.617Z", "0.4.16": "2020-09-14T11:55:12.089Z", "0.4.17": "2020-10-13T07:27:44.691Z", "0.4.18": "2020-10-20T06:22:56.198Z", "0.4.19": "2020-10-20T07:09:30.206Z", "0.5.0": "2020-11-12T03:42:10.787Z", "0.5.1": "2020-11-16T12:23:44.848Z", "0.5.2": "2020-11-18T11:23:06.990Z", "0.5.3": "2020-11-20T03:19:48.363Z", "0.6.0": "2020-12-28T10:48:27.239Z", "0.6.1": "2021-01-06T02:41:28.105Z", "0.6.2": "2021-01-18T07:02:23.500Z", "0.6.3": "2021-01-19T11:06:46.766Z", "0.7.0-0": "2021-02-01T09:18:59.995Z", "0.7.0-1": "2021-02-04T09:17:44.144Z", "0.7.0-2": "2021-02-04T14:34:27.919Z", "0.7.0": "2021-02-05T03:34:35.529Z", "0.7.1": "2021-02-08T09:25:11.343Z", "0.7.2": "2021-04-07T06:47:34.321Z", "0.7.3": "2022-06-15T01:59:19.758Z", "0.7.4": "2022-11-30T02:03:46.597Z", "0.7.5": "2023-03-23T09:22:22.140Z", "0.7.6": "2025-07-26T11:03:28.376Z"}, "users": {}, "dist-tags": {"alpha": "0.7.1", "latest": "0.7.6", "beta": "0.7.6"}, "_rev": "300-77a825fab4567a07", "_id": "@cloudbase/toolbox", "readme": "", "_attachments": {}}