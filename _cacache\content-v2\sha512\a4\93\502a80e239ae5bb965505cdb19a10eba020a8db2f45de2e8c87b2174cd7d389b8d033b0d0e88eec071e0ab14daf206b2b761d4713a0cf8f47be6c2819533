{"name": "archiver-utils", "versions": {"0.1.0": {"name": "archiver-utils", "version": "0.1.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "34c56ca6109204f7895634ea49bcc3a82c61c361", "_id": "archiver-utils@0.1.0", "_shasum": "77dea7b263b9a2719a65a987db5845d5b4161e62", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "77dea7b263b9a2719a65a987db5845d5b4161e62", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-0.1.0.tgz", "integrity": "sha512-hO8Ix+cbFcV/gWn8JpG1A45j1CAlku2PnKnzgl2dO3aTvJq8X0lLlRLUg/4erNbhJKr6AaMriSooz8K7wtF8gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDamXSdOP8XkHjgjHg2HLsb5OD4ycXLaa491f2TbiYZywIgQ5kXhhnh+Co7nykWKgcI5A+DQfvLoPnvWLVtYXZr9fw="}]}, "directories": {}, "contributors": []}, "0.2.0": {"name": "archiver-utils", "version": "0.2.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "90521f91a1029c05fcc77d6421b541730981fad3", "_id": "archiver-utils@0.2.0", "_shasum": "c29ff8e2bc859bfef80c04b95070943d0931fe22", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c29ff8e2bc859bfef80c04b95070943d0931fe22", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-0.2.0.tgz", "integrity": "sha512-FmTi1jllkJSl/nS5dwwBL49aDLKd/9ujDWfkul70D/+o0r9l06AWW8BF23vr9m0JSzYC3Zqjvqi2sSanY3t9AQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPJkXiJc5lelwqvNx1Bg1C79Iu8qxQRZljlenje1058QIgXLBUDaKDWDdQO59asZQ4mIYA1LjZ9EhtdVSTMT11L/k="}]}, "directories": {}, "contributors": []}, "0.2.1": {"name": "archiver-utils", "version": "0.2.1", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "readable-stream": "~2.0.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "7698d996e11ab4d47c97f462b73a323b21aa8ae5", "_id": "archiver-utils@0.2.1", "_shasum": "2c860a265cf195979838a5e5b666573869707f98", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2c860a265cf195979838a5e5b666573869707f98", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-0.2.1.tgz", "integrity": "sha512-5XnHOSaVcVID5EIpTvkUhAOotJuUZM7C0+/qjB/MbXCP9EMFV96eZX9w5BCYlgQsVyq5NfpVOsH5cyFJfj4KGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcUyWVAPVmMNXiLbtG9yCSlIbaLrHqg0jdCbiawdOxfQIgB3A83u9UjZSF2El7/qunJqU/E2dCIUYDxe9kNoHh2ms="}]}, "directories": {}, "contributors": []}, "0.3.0": {"name": "archiver-utils", "version": "0.3.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "~6.0.0", "lazystream": "~0.1.0", "lodash": "~3.10.0", "normalize-path": "~2.0.0", "readable-stream": "~2.0.0"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "rimraf": "~2.4.2", "mkdirp": "~0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f3badc8ad8fbac9a9d2dff964d5b77559a51718b", "_id": "archiver-utils@0.3.0", "_shasum": "9d85340892fe9e7c6c1b1b6af3c33692cf595b70", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9d85340892fe9e7c6c1b1b6af3c33692cf595b70", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-0.3.0.tgz", "integrity": "sha512-9i6LUyP7sAnNQvUrSV/4ZYMTd3z0eArt2kl38nsOUzdD3iGnt7NQaD8LgmlTrpAaB4qqL1dkUyimujbEdW1r1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfkwimETILaCPM3mEkPmdkpL9VFX8Q36oZVlF6shB5bgIhANh3PywGw3MBfJcMwZxgaGv+gNbh1e7ftxIbOfpFzGql"}]}, "directories": {}, "contributors": []}, "1.0.0": {"name": "archiver-utils", "version": "1.0.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.0.0", "lazystream": "^0.1.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "74e491890a202b7f98a822403bb4d88fc907091e", "_id": "archiver-utils@1.0.0", "_shasum": "22be682aca6834631b91f9be2c9cc7b724e28f1c", "_from": ".", "_npmVersion": "3.8.5", "_nodeVersion": "4.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "22be682aca6834631b91f9be2c9cc7b724e28f1c", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-1.0.0.tgz", "integrity": "sha512-5ppE5u2xahEMwAsS+Xx6hvYDS/yGbJ/8z0Z49dgDrTwGHkKLcE95gK3O3x/S32KCaorR7Ix0I92lCDt6FBPj6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDdtyfTC6FSPZ6Kg6g7RE9n8ydNjjz+tJF45dUhDGpugIgDFRT7D7SoAqO9zKsKPf2aW193GQh4wqQaT8iusXOR+g="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/archiver-utils-1.0.0.tgz_1459915768504_0.8756498962175101"}, "directories": {}, "contributors": []}, "1.1.0": {"name": "archiver-utils", "version": "1.1.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^0.1.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "d2f155a19c260f8cd22d3c5a9adeec42a123184b", "_id": "archiver-utils@1.1.0", "_shasum": "ad65177f892f885efc8eb9e51a8598dfbfec2f62", "_from": ".", "_npmVersion": "3.8.5", "_nodeVersion": "4.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ad65177f892f885efc8eb9e51a8598dfbfec2f62", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-1.1.0.tgz", "integrity": "sha512-JcTGDp8aSrowsfMO7CwdkEKOy31Lf6XoAztjiiaWIHTZYx6BesusXOSSp5lFvl2/9U2UzlHqC/nuZmkO59HuAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEcbPMjfVpwGZu+T0sfSi82j1NNMMAMrZUjqcZ1y0y9VAiEAtF01vmevJ4QJcWHdC3hbFpWqO1nkPgJ2e/eKj62Oo7s="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/archiver-utils-1.1.0.tgz_1459917452104_0.5923407501541078"}, "directories": {}, "contributors": []}, "1.2.0": {"name": "archiver-utils", "version": "1.2.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^1.0.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "dc5e632827f8c90820fd0429f9d64a12a3be61e0", "_id": "archiver-utils@1.2.0", "_shasum": "81583e2cce54f16c09ab0f00b9b814dfccbe4f78", "_from": ".", "_npmVersion": "3.8.5", "_nodeVersion": "4.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "81583e2cce54f16c09ab0f00b9b814dfccbe4f78", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-1.2.0.tgz", "integrity": "sha512-W0JloMLe13Ix98wMSz8joLYu7VGpN0gKRxaYWxym6M/JYGyEgJGj0ZxwktxAesj+u8iKkFhJqOA8JQkicjfRnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHfMacCDJ3Yay77v4nef/rn8cApxNGKz09dP4WiLTwpwIhAMp6rxQOirInkp2baKKepb745Wvyff58YoO4w7yGr+QC"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/archiver-utils-1.2.0.tgz_1462459491392_0.18480336316861212"}, "directories": {}, "contributors": []}, "1.3.0": {"name": "archiver-utils", "version": "1.3.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^1.0.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "rimraf": "^2.4.2", "mkdirp": "^0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f70a0a3c2c83f128ce8ac1497cfaf6ef4ca8be4d", "_id": "archiver-utils@1.3.0", "_shasum": "e50b4c09c70bf3d680e32ff1b7994e9f9d895174", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e50b4c09c70bf3d680e32ff1b7994e9f9d895174", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-1.3.0.tgz", "integrity": "sha512-h+hTREBXcW5e1L9RihGXdH4PHHdGipG/jE2sMZrqIH6BmZAxeGU5IWjVsKhokdCSWX7km6Kkh406zZNEElHFPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDne7szbFFEH55f68ishoVPAbJjtCNdREgCE0bU2Ep7JQIgSmwFURaA0XrXywQJ191aPpZBP+nxfVCNrvemCGEuUhk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/archiver-utils-1.3.0.tgz_1472323339684_0.734533135779202"}, "directories": {}, "contributors": []}, "2.0.0": {"name": "archiver-utils", "version": "2.0.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^1.0.0", "lodash.assign": "^4.2.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.toarray": "^4.4.0", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^4.0.0", "mocha": "^5.0.0", "rimraf": "^2.6.0", "mkdirp": "^0.5.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "6ccfea2a67f43d271df5bf26c515f8e95d0dead6", "_id": "archiver-utils@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JRBgcVvDX4Mwu2RBF8bBaHcQCSxab7afsxAPYDQ5W+19quIPP5CfKE7Ql+UHs9wYvwsaNR8oDuhtf5iqrKmzww==", "shasum": "5639818a8b5d89d0ffc51b72c39283cf4fea14a1", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-2.0.0.tgz", "fileCount": 6, "unpackedSize": 14001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfd8sCRA9TVsSAnZWagAAYo8P+gIFQfRaW3YaGjsDdK4r\nBeaaaf1RzbHDMjF+C0l9kLA0faPBCWgI3EYzszhqIDMCgVvn/dtXKPg+VOej\n5/bQHJ9oPcQjSw3eCqEU/6xpVwqnKMhkZHP7VZO3Ho7oL/c0hV6NDfo0+T7l\n6GAH39TN1d7ZA8vd6IeA+Mu64MNszKG4t1+JJllROTvogoGXF/SuRIJDtZvo\nFJQaov/z1obVl2vNnLBs6TSTM0u5Ux86njJYms/IQjuOZ6C2lnS2lrsDOWrY\nIWn94HIFFP9BIXLBesuV8sL4z46pdDR5TzdaBEdw9BFcJe31GNsHN6MxFPoo\nSCAmWQdX32CR5iweJApfUyMsw/f1W9xASJCyx6W7gIs4v8RX4RFaTzn7akFT\nI/tlgKSE2Geq8BQHy/7BNeZlfcPFYevUyPBiAJSxUvW2PYegq9rsqLgchR5m\ny57EqO583wX3R41VXxdOp+fDWq2bCdtQAfVQgaC4BR9TMP/9Hz9aUcGrk2d1\ni8LBvgLJxuhs6fQwxkybDBSlGEw+tUDUzNHvuVHBdfgJi3RupEH7PkWubFBy\nhju+5xDRKeb3aEUyJ7aIofZB9FoGiw0cYvRr1g99T3o92ZzEwufGY25COFg3\n9bXf1/po8WzyvOaCpE+wrZc3tN8hgiYK9ftw61yYM36LvUnhwre9LXOUzhd6\nSPUQ\r\n=Ukn5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF15+Ewj5lKxvHAu4z08tzzN8TfU9HmTDDMr4tSUeylcAiEAlq40WmBKrEFPs8Nj6Q+sjEHk6gtW7DggEkNM2foyQZA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_2.0.0_1534975788090_0.6479018751288879"}, "_hasShrinkwrap": false, "contributors": []}, "2.1.0": {"name": "archiver-utils", "version": "2.1.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^4.2.0", "mkdirp": "^0.5.0", "mocha": "^5.0.0", "rimraf": "^2.6.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "f4ddc89191586f088332f6a60f0c8f0dc48fb884", "_id": "archiver-utils@2.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==", "shasum": "e8a460e94b693c3e3da182a098ca6285ba9249e2", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-2.1.0.tgz", "fileCount": 6, "unpackedSize": 13919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMklwCRA9TVsSAnZWagAAWRwP/0A17PbFkND/ygOZ2jSK\nkf7ZGs8mxTmRAPrMPxglZZLfkKHadLyhbSzsgq4bHe1T5M0zBlRWls6vKfH5\nkNKcCWe6Zg9frt8TKMUuflAjZxLvBUuAkHf6Cy1Z1oRI/whYjehwOZTdIdwn\nj59RLAWsY3d+OiSiTa40Pv06EOkxZBuDoNvPGpnBfLBAGeNNWRGXDipae48B\nTWwW7MLSYBJI4Rs2CtKaKaTP8ZkgFLZI8fBKeiObeZ8OcjovxvYnfKhfxF/t\nAF3ROJKvrNnNKFeyINLzksFNaI7S1nmZba1SCTWXjh6kG32TocVFHe/VdVd/\nGLNPSjHGZ3neAMvBqgE0/7GuuQX71nr4OX6WydWhXs1RZEdOlFHNET1vQbAq\nkH0ALRyPUVfwORrHC0r1rq4uNrnpDdZ+hke+QXjmrxe2DqeQpNzwuA+US7d4\nokNA8uv5qbJCJ9v/uV0fXrn90XB3Zy2SNnv6iBPSCl3FoMo0xedFLVo69Hx3\n5sN0EEpBAf19CftU5uXN+cMW12m4iMBC/WuUiOgQfcV50IXUMGn7x936jk6a\nH2kr20Y+JX4ue7jNrO4dNcxUU73O/SvPUWZcw/YPvE/5Vl/SY7ohCWASTIYx\nUEfrAMkOq49NcTr0RhOf8MT6+qWMo2UseG/DQIP4SDzgV0Oyg15S0g5V85ee\nGcWw\r\n=pN3i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkl+kA+JDnYsJco4SS/d7IOZNNeFVyr/QERHMSGi4wzAiEAiKOv0dxMCCUTxr93vn+kacMZuh3Eo9H+iuoEC5K4VLs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_2.1.0_1563576688106_0.9979622031767903"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.3": {"name": "archiver-utils", "version": "3.0.3", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.7", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "3.0.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "4b5e6e3e8644deae12a642a6402899e625ea2b1c", "_id": "archiver-utils@3.0.3", "_nodeVersion": "16.20.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-fXzpEZTKgBJMWy0eUT0/332CAQnJ27OJd7sGcvNZzxS2Yzg7iITivMhXOm+zUTO4vT8ZqlPCqiaLPmB8qWhWRA==", "shasum": "0531e6f64078a325e591886773fa80d72c97df19", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-3.0.3.tgz", "fileCount": 5, "unpackedSize": 12650, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXzb8M5BeCVuZ9ntQbHQN5QQyv7lnkmHkdx0ZWADdmVAIgGV96m9c6Wyyck5mAUgt/8OvftcUwSP4D84h/95JpTow="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_3.0.3_1692279334986_0.7438000844278605"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.4": {"name": "archiver-utils", "version": "3.0.4", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.2.3", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.8", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "3.0.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "d612581467d05c5248c2856ab1dd4fd498e0e58d", "_id": "archiver-utils@3.0.4", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-KVgf4XQVrTjhyWmx6cte4RxonPLR9onExufI1jhvw/MQ4BB6IsZD5gT8Lq+u/+pRkWna/6JoHpiQioaqFP5Rzw==", "shasum": "a0d201f1cf8fce7af3b5a05aea0a337329e96ec7", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-3.0.4.tgz", "fileCount": 5, "unpackedSize": 12650, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEkPdSfGphCEVkC+5JCS1QGtHG+Lh5MYMT9+i8PhAEYhAiAJlvT4B0xk4ORkXZPrWw8wgmSw/WteR0YwmXsq69JLwA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_3.0.4_1693627676504_0.5781541189751878"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.0": {"name": "archiver-utils", "version": "4.0.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^8.0.0", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.8", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "3.0.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "27ece5da099f77307b6589db9823c509e68a6550", "_id": "archiver-utils@4.0.0", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-nak5U3z/Gk6BySshGWORO4ETOpTiEW9JLJx6+8/tF3Rwt11LHst/qnXCGhsOuP3WDS7IPPAMTXCAkgrSQyopzw==", "shasum": "9bbc58cc2634e10933099d0e9baf98635b68e867", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-4.0.0.tgz", "fileCount": 5, "unpackedSize": 12654, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwZIuv6LHwWZwMm0oynh4xHREEGuO96Hva5jDrtLbbVQIhALLHtDxFUKc3kHzrDoypQlurAPGvBHH1CzEyDFXM5RtN"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_4.0.0_1693700172455_0.5808266260522739"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.1": {"name": "archiver-utils", "version": "4.0.1", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^8.0.0", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.8", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "3.0.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "gitHead": "e3426bcab0bcfa0e7e67b35815ba5980209116a0", "_id": "archiver-utils@4.0.1", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-Q4Q99idbvzmgCTEAAhi32BkOyq8iVI5EwdO0PmBDSGIzzjYNdcFn7Q7k3OzbLy4kLUPXfJtG6fO2RjftXbobBg==", "shasum": "66ad15256e69589a77f706c90c6dbcc1b2775d2a", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-4.0.1.tgz", "fileCount": 5, "unpackedSize": 12570, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnaKwr0S2+xW6a6j4omVPuHu1yhZlwiye/FJmtRUqvvgIhAPO4CawEVuU+NcNXdDO9E2LBtD6QnTRCJtGf69qcXKWd"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_4.0.1_1693772622785_0.5272838967578644"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.1": {"name": "archiver-utils", "version": "5.0.1", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 14"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^10.0.0", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.4.1", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5"}, "gitHead": "695387cdc4816a3d19edb6f1d5473e6395479567", "_id": "archiver-utils@5.0.1", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-MMAoLdMvT/nckofX1tCLrf7uJce4jTNkiT6smA2u57AOImc1nce7mR3EDujxL5yv6/MnILuQH4sAsPtDS8kTvg==", "shasum": "2ddf49c8b03394cd5a6d45642069f6e924290e3d", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-5.0.1.tgz", "fileCount": 5, "unpackedSize": 12496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiHasIC8SgKvDK8pxmmCMhBRY/000ZQX799W5E86lnqwIgFx96JTyS5pFCHP1KOXW5JQnp5T6g1yiPoMe4Y7SSR9w="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_5.0.1_1709010765624_0.9862720700781054"}, "_hasShrinkwrap": false, "contributors": []}, "5.0.2": {"name": "archiver-utils", "version": "5.0.2", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "engines": {"node": ">= 14"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^10.0.0", "graceful-fs": "^4.2.0", "is-stream": "^2.0.1", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "devDependencies": {"chai": "4.4.1", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5"}, "gitHead": "eabac607931a87058a78d4890337a8973517701b", "_id": "archiver-utils@5.0.2", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-wuLJMmIBQYCsGZgYLTy5FIB2pF6Lfb6cXMSF8Qywwk3t20zWnAi7zLcQFdKQmIB8wyZpY5ER38x08GbwtR2cLA==", "shasum": "63bc719d951803efc72cf961a56ef810760dd14d", "tarball": "https://mirrors.cloud.tencent.com/npm/archiver-utils/-/archiver-utils-5.0.2.tgz", "fileCount": 5, "unpackedSize": 12552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFCGVstRIuskN2zE1xTuqdc0hx6lbzxnNmKavXmVbmdKAiEAq1UOpmnT7n3ERZiVthXsbU9XsRa5e3ZqLiiCxLVIL0c="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/archiver-utils_5.0.2_1710038024207_0.3070352680186512"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2024-03-10T02:33:44.800Z", "created": "2015-11-30T02:29:27.010Z", "0.1.0": "2015-11-30T02:29:27.010Z", "0.2.0": "2015-11-30T03:00:35.164Z", "0.2.1": "2015-11-30T03:04:41.102Z", "0.3.0": "2015-11-30T19:23:02.559Z", "1.0.0": "2016-04-06T04:09:30.719Z", "1.1.0": "2016-04-06T04:37:34.291Z", "1.2.0": "2016-05-05T14:44:52.889Z", "1.3.0": "2016-08-27T18:42:20.711Z", "2.0.0": "2018-08-22T22:09:48.203Z", "2.1.0": "2019-07-19T22:51:28.302Z", "3.0.3": "2023-08-17T13:35:35.134Z", "3.0.4": "2023-09-02T04:07:56.684Z", "4.0.0": "2023-09-03T00:16:12.792Z", "4.0.1": "2023-09-03T20:23:42.950Z", "5.0.1": "2024-02-27T05:12:45.831Z", "5.0.2": "2024-03-10T02:33:44.373Z"}, "users": {}, "dist-tags": {"latest": "5.0.2"}, "_rev": "13216-abdb02280dfad31a", "_id": "archiver-utils", "readme": "# Archiver Utils\n\n## Things of Interest\n- [Changelog](https://github.com/archiverjs/archiver-utils/releases)\n- [Contributing](https://github.com/archiverjs/archiver-utils/blob/master/CONTRIBUTING.md)\n- [MIT License](https://github.com/archiverjs/archiver-utils/blob/master/LICENSE)", "_attachments": {}}