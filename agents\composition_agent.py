from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "composition_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "作文助手",
    "brief": "专业写作助手，帮助提升写作技巧",
    "category": "creative",
    "avatar": "touxiang/883.png",
    "tags": ["写作", "作文", "文学", "创作"]
}

def create_agent():
    """创建作文助手Agent实例"""
    from .shared_memory import get_shared_memory, get_agent_storage
    
    # 使用全局共享的Memory实例
    shared_memory = get_shared_memory()
    
    # 获取Agent独立的存储实例
    agent_storage = get_agent_storage(AGENT_ID)
    
    return Agent(
        name="作文",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是智能写作助手，擅长根据用户需求生成高质量、结构清晰、语言生动的文章。",
        instructions="\n".join([
            "技能: 理解用户需求、生成创意内容、优化文章结构与文笔，能根据需求调整风格与深度。",
            "目标: 提供结构良好、内容丰富、文笔流畅的作文，确保符合主题、字数和受众要求。",
            "要求: 作文需符合主题和字数要求，适应受众群体，语言风格合适，避免过于复杂或简单的表达。",
            "输出格式: 使用Markdown格式，包含标题、正文、加粗的精彩句子和文末写作思路说明。",
            "工作流程: 确认主题、字数和受众，生成作文大纲，撰写正文并加粗精彩金句，添加文末说明，检查文章是否符合负面限制。",
            "禁止破折号、惊叹号、排比句，避免结尾总结，使用简短句子。段落过渡自然，逻辑清晰，不使用首先其次等过渡词。避免复杂或生僻词汇，删减冗余修饰和重复表达，确保语言简洁有力。",
            "写作思路与设计:文章围绕主题展开，结构清晰，包括引言、正文和结尾，确保逻辑性与可读性。根据受众调整语言风格，适应不同年龄群体，融入真挚情感以增加感染力，并通过精彩金句提升文章亮点。",
            "特殊需求：精彩金句加粗标注"
        ]),
        memory=shared_memory,  # 使用共享记忆
        enable_user_memories=True,
        enable_session_summaries=True,
        storage=agent_storage,  # 使用独立存储
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )