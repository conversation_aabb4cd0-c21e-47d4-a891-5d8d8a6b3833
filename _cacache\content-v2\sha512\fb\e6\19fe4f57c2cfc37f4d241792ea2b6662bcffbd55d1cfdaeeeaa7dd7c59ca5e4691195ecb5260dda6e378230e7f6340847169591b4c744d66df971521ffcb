{"name": "is-promise", "versions": {"1.0.0": {"name": "is-promise", "version": "1.0.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "_id": "is-promise@1.0.0", "dist": {"shasum": "b998d17551f16f69f7bd4828f58f018cc81e064f", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-1.0.0.tgz", "integrity": "sha512-KZeH2Fbi/kGB/ymt7mftW9ja36yrtReK12ShLFmJ3thgYl2FnH6c8CJH9g1tCPt6gssrjvmewdxUUGMqQH2zJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTJ4/jiQl8xE032MVsyndQnCOF9ZzdROF1sLBlYkGt5gIgGGmPElXCdQWIzJMwXXr8trZvISQp4ZNHf4QRWfpQyaw="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "1.0.1": {"name": "is-promise", "version": "1.0.1", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise", "_id": "is-promise@1.0.1", "dist": {"shasum": "31573761c057e33c2e91aab9e96da08cefbe76e5", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-1.0.1.tgz", "integrity": "sha512-mjWH5XxnhMA8cFnDchr6qRP9S/kLntKuEfIYku+PaN1CnS8v+OG9O/BKpRCVRJvpIkgAZm0Pf5Is3iSSOILlcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHsUZEGNM6U1+qO8Bq01nBBYlXj2EZYoakNjw3U9H5uOAiAMI805CCyEkr0HAsOUBu8UcWX5sJiyiiDP7cJz3UmPaA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "contributors": []}, "2.0.0": {"name": "is-promise", "version": "2.0.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "gitHead": "a2172115022e7658573428d9ddf9b094f9c6d1b5", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise", "_id": "is-promise@2.0.0", "_shasum": "058b6404ba57c4df03c92ed987c000bda58753a6", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "058b6404ba57c4df03c92ed987c000bda58753a6", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-2.0.0.tgz", "integrity": "sha512-vx0kdkFowTkotEPvgwP3ig1UuzRcwgalWkvfSIa2Oh44r/Txk+IlGOa5iY7vIU2C1wGuO3wOXA4twVQKTCI8RA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCs0obJ+NoNljnNRhZeS046Ii6UD9k0Of0iTDaFIr8S+wIgf1f5nHuvUGosQVa3b9QRY7+TO+OlY4QMYoWQTPanemU="}]}, "directories": {}, "contributors": []}, "2.1.0": {"name": "is-promise", "version": "2.1.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "gitHead": "056f8ac12eed91886ac4f0f7d872a176f6ed698f", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise", "_id": "is-promise@2.1.0", "_shasum": "79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-2.1.0.tgz", "integrity": "sha512-NECAi6wp6CgMesHuVUEK8JwjCvm/tvnn5pCbB42JOHp3mgUizN0nagXu4HEqQZBkieGEQ+jVcMKWqoVd6CDbLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzV4q1JX7Ip74OxHn6+M3oIhNm0MgNKNrvZVUfTh71NwIgD+F7w3uhzsn1R6gS5bK9LMdqiVjaKLbB34z9F7z8ll8="}]}, "directories": {}, "contributors": []}, "2.2.0": {"name": "is-promise", "version": "2.2.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "type": "module", "exports": {"import": "index.mjs", "require": "index.js"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "^1.0.2", "mocha": "^7.1.1"}, "gitHead": "78eec0c382cb94dd8332ed475a865d7ce2ebad2e", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise#readme", "_id": "is-promise@2.2.0", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-N/4ZxZGjDLAWJQNtcq1/5AOiWTAAhDwnjlaGPaC2+p3pQ+Ka2Dl/EL29DppuoiZ8Xr1/p/9ywBGGzHRPoWKfGA==", "shasum": "3ebfc546cee7064c314686279cc9df7bc2724715", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-2.2.0.tgz", "fileCount": 4, "unpackedSize": 2668, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepFE7CRA9TVsSAnZWagAAg5AP/0d28UgaZdfgfwpLx6fn\nsOL6XFZA5INhErVVBBnnLIAcVCzGzW9NDBsv0ZenjbEYwa97pWPjjzykSGmB\nH69EtOM26bN5O9dsQiF6m4xLmY07AzPtV2gefB4jSmUaFR1XKnJz3wIgmrj/\n8a0AkuMoSoesIXFwwkzh+MGPjpUvAbdqnqg5idsR4/EkLd33IiJhdzblp07g\ngI+h6CBtcZgnQf8kZL7xa3o375R3x0k2NCSbGGPNRlcqo7p/Z6effwscPoY4\nmFrIar8P1HW/AbnfEZi43ciQfUpumsbtKxp0aQ4KTvK6blPdkUhbUlTIXy0J\nLbdgnrBud7NCMGoldGvmU2vigRLvsxC62Zw1N5ySyPO2mW+v8X4BdEFcWKsC\nn2m1vVqkbuQvYi0rP9UB5KMpPAq5FrTdeaeAuoc6PCXU8mU6WQbh59BvTLNU\nz7kTs+01QfggV6nJ9kXXziGIMRbuiGZlf2P50DAAvlRtOps0uMNqkvA8zT4Q\nJcANCnkuKGMaXWab20v2KiUEYf4f4UPCfF+3lEfyojtao1MS6Qgs/RGrmUmP\nHknDjv+LibEgjtUJMlyy+gAAhk4OsjbPn+xKSt5pFdw8LZWSCPLuZSM+xtTX\nnSJKTy6aJijUPKGp45LSKGAGoMsn8r2Wfz2I1hJJ3WiKJptO8pAJHh4qBsG/\ny75u\r\n=SKAF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfsjP/jZJn0xsIeVx8SKDDMykamxQ29qfep/rNVlsQZgIhAII5F6KGsPKhKEsWap8L7HazWbKkkYk7+n8Rs5r/OYXC"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nathan7", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-promise_2.2.0_1587827002797_0.20873413054169054"}, "_hasShrinkwrap": false, "contributors": []}, "2.2.1": {"name": "is-promise", "version": "2.2.1", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "exports": {"import": "./index.mjs", "require": "./index.js"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "^1.0.2", "mocha": "~1.7.4"}, "gitHead": "0b69f52ee73c9f6ad4480f02c9bccb14d2038656", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise#readme", "_id": "is-promise@2.2.1", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-KckoDDHnZgTg3vZbOKo5xwWgIQqjSt5jjg5bcvyScaNwCNpqqWE+yg9OOoYHBzjCp9kyqSGrJBvWLEyXk6Z6ew==", "shasum": "0d624554bc3bb09d4896b3802ec9a093731b4442", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-2.2.1.tgz", "fileCount": 5, "unpackedSize": 2822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepHmJCRA9TVsSAnZWagAAXPwP/1HWxtJeBtg/ruy8lRts\nGCADEYZd0y+YjTu+dfFB7yyHcIAYKStbuLQzfSu8Rm65eYWFhq8PmNsKTWe0\nRSzZoOJwMjOPEQUxIkG/g4PG0DkkXUnq+HsUaHc/vWxVUTtkmqD1SMZ9mVwl\nh51F2ZiKAygsuWgkYOWmg3T6USF7G2EdCQs1qWobKq1vB4nhqKg7Xfg+ZrIV\nhy1ZFUZcb6P5wABy6ch/CAbc3eMaLsu5ZkwpzYZtQwGLEV/Jd9L84z6/wxUa\nWwlgSctbjiLDjBQH5KChkvz4GIPP2jJfmlvsoFiOFQTO6z2nonl6Z0Ti6JKv\n8ZqUGAqqzl+/MbsxMBQCFIcskQTaAGtCGM2qUt9jORxK+jY0H0qv43qNOUYT\nMoPK8KCpT2pC8OUHGscsDI/eE5IGh2rVpTU0Uq6OcrGirOVF8z6DnVOsxbSh\nX4izCULILmyM0dlq+5VyX4SnNRb183537AVIYVQqflU0pFlijgwlSje0LeZA\nY6iYE8aNPLlvXi9/uLE5q6K3/vQTO1H0GNHz4daeXWMSHYtdN4zYHcvKofCO\n6mWjdmGBiwKXGM0xVkhjud8IblFJctpE/IXVwfpN9uTiqYJ1xOWipZmWU0gF\nUMvCANRlvH/evoaGf8qZSH32fO8zPaDcQjhP1fqmdhoXhFIEVMhR/Md7392Y\ncYsr\r\n=T7bI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEvtLrsvFchfP3ijdgQGep7MSU7mPcvLAj9QwPJtKTpQIgV0dN4GnLNOXfM4ES8Ooq7nPdK1OaozaN1y2XxhbbMX0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nathan7", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-promise_2.2.1_1587837320858_0.5308716980788573"}, "_hasShrinkwrap": false, "contributors": []}, "2.2.2": {"name": "is-promise", "version": "2.2.2", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "devDependencies": {"better-assert": "^1.0.2", "mocha": "~1.7.4"}, "gitHead": "68d3d6871806162772b33e9629908ded6c8bf0b1", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise#readme", "_id": "is-promise@2.2.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==", "shasum": "39ab959ccbf9a774cf079f7b40c7a26f763135f1", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-2.2.2.tgz", "fileCount": 5, "unpackedSize": 2745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepHyGCRA9TVsSAnZWagAA6HEQAICtZzNJstS2RlYacK+3\nnmon7YnoAt8Ti9h3Rx5EhdUMqX3pounIMZjzCnuDpYrwMUOgwnKgTfNQFQir\n4EZtAJxjDzc58CEBaGqjbgDKU/TPOaL4AXN0a9E6YXEqDkU3uKVf3qytFu3F\nekL7Km7WpUFg3Ta+LlKDQv3LV0Co/3b6joBCWqUdzP39ulGBMPkFBEg3oDTE\nLjMjPN7Jg8RbmE0MlKha3iTDAQBS7I9FhUy9gMbjBAZDvMU/+aeLvLr8rD8G\nfwpt4d5z+m2HnVr2ncMvW1T59ihOyhT/rtj/e4ekNLn3uMLfzIlchV3/RnwP\nUYtwP2SMatsPOdYPbFSVq3zTbu9U0BiQzE39BrwNgHlWi9iVREsE5XjBBSYj\nT8VfcEZIgrZmjBjNfyJBonOYdoRiN9ywkJ2MqntwqhGL2kKnN2LVLgL0Xbx3\n/L8CuMGNzM4iQQ0+bzVdNP+iq4jFtikobHMojbqiMHQ1CAj2v5PTztcI0wFc\nDSssEdXNYluQ3ygxI3nc4su1/auvfdn4N5umRcicScCLBb/ni7FW6awFrtB0\n0YdJM2sQWJG4RAwjUfUpQloVgATnpxi0OmmYoWTpk37X9/yQNezlA8sdnKTd\nXZHuV3G1zCBiLOxrRhUIKC0TL/6lenqLdTi5WNb64iIqb9r5/ej20QCqHE1y\nYZlJ\r\n=IJ5W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEs55RaaY1kG+21fXCfy2I5eziPki8p0klLBsTJql+oDAiBlYj2W97lcsBUgbGNH8cUJOvuUk0dVTGggMsTSc98Wkw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nathan7", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-promise_2.2.2_1587838086005_0.2998250461878085"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.0": {"name": "is-promise", "version": "3.0.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "scripts": {"test": "node test"}, "exports": {".": [{"import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index.js"]}, "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "1fc71a47126ab90c6c09c978ce5f09323504b091", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise#readme", "_id": "is-promise@3.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-aTHJ4BvETyySzLhguH+7sL4b8765eecqq7ZrHVuhZr3FjCL/IV+LsvisEeH+9d0AkChYny3ad1KEL+mKy4ot7A==", "shasum": "1f88031af842d9203dc1777cba40411e848f9beb", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-3.0.0.tgz", "fileCount": 6, "unpackedSize": 2949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepY7/CRA9TVsSAnZWagAA6LUP/2cS41OnvGmuS1WJvyuQ\n+en59qO5GxNynbIH/sjqp72N4mhJjg51qmF9PUhp7e3JQpZkoiLtfKTpPYBa\nYJWHpKy3pGqUWmZqoUf+Xbtw46CGge18LpUEw1XZLlIu2QwKAOHnAGZf2Fo3\nDZc2e0QzsT5t+/XMMOZLHcexdzyb3E3kaRF/B3NjcJFlJAu1t5SJTuVnKuyv\nD+2lJjsIyQxu7HMhN5YVBiWA92RdPXAIZxh0GmMyxe9bEO9+cZa4g3CvCdDT\nSRGvKieAJVW2nRgaQys7o7J8VTyWYh/5VG5HeoQTslwEQYKvhjd0Rrg+yx/Y\nYVXO6FgLCnwevwUXABjXpUK8VUIxj7XcoUOXiK33LkSzJud0nKClUqNZCV2p\npnQgiVqno7ZbWB2d3OxJ6ud5kcr9YWh7HZv1IkMny7NQgNopvlses+Jf02YK\nDBAm5PG5YQlRUVJyHx1Keg08QakWciuDX8zH8THGNRRKpTPLWvJRB3xxX4uG\n7OgLhD83wleNvYzjGfdnUZyRlOQO4IwFlOr9kIT0dTcicuYSAUXOIVs+uJmb\nWtyfOyL54KPGMUMb+OOf3YojWEpobwo8alGfrfLkNWBuMNazl2VSBWG6wfk1\nXK1P+/LscLxTD1JAMof230c5mzl/VuT18Tmq5cSjEkx0SABKm53iy2/DvqpR\nKW2B\r\n=iVG5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHbxsfpj31wJAZuB9KYwmOHx5W5GqS8IEMyGzvhTkZHPAiEAjvy3fyaiNpmPv+l1OBAbdH7Ys7GeOnnyn9fRZWvCcaE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "then-bot"}], "_npmUser": {"name": "then-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-promise_3.0.0_1587908350738_0.05295193614336613"}, "_hasShrinkwrap": false, "contributors": []}, "4.0.0": {"name": "is-promise", "version": "4.0.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "scripts": {"test": "node test"}, "exports": {".": [{"import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index.js"]}, "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "5a240f639ff89f9cb938bbaf3aef59f15ddd0117", "bugs": {"url": "https://github.com/then/is-promise/issues"}, "homepage": "https://github.com/then/is-promise#readme", "_id": "is-promise@4.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==", "shasum": "42ff9f84206c1991d26debf520dd5c01042dd2f3", "tarball": "https://mirrors.cloud.tencent.com/npm/is-promise/-/is-promise-4.0.0.tgz", "fileCount": 6, "unpackedSize": 2958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepvt/CRA9TVsSAnZWagAA/BAP+wZn+hpcRVqAD7mp8A/G\n270SNJZeKyTNXi8IyT2ZuJYQAXjPdMkUDLWbg8ffL7iU6gg1RcOTX4BFerpg\nr7Wsdy4p1kEDIuvSy/WZUQvIam4Qd2az21dCb4Yt/b/80T8KqpkqRCGIdT2p\nNExjxZR0ozP3uePkc309reb8/jdQEPQWFUAiIKosUDZKa1fM1GWmmC5udHJT\n18SfiS3kOUPaNWaKv6B0bep+Z/zlwaKb2eQzvZJW0wjnTxM4nS6xy3pZRMz2\nxNGTGxoXRHnbU+b3YE66FE1EIxri3I+LvcFIGO/LcyDq8ql6XkUyTTvCA2pM\n1ssygtvQ2a+5NLVoXfhh+40sRHN/bpu70pwD7s/y2zcQkNFyK+gfIVrSsy24\n+fsjeMkwpbBnz847ME1ouUb3wZ4CknUSbbGwSl0hwdh40cF/BPi84RqJi0qY\nuCle016vj00g9Y0iAcJs/nu/MyJjjrsLmyr9YhR6fP1SBXGz67ja0z9fz1Vb\nP8DP+QYSHUefLb3XtKH880Zk/UnjhS/HgPT10n3rPvd16JyTBlc24Ntyfl1E\n+W3QFi+A36aKrbNtcoX+i/Du1V+hvFizrV09x3NWq4/Zwqfww6pUX00PLmxe\ngPRIi0zFOj53e01DsPGdkRWzpFy1d6U0S/z2iCLgidzuNjf9vOiWjhBhUOxn\nJL8F\r\n=9K8j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDK709pshYmjFDem5EYehH3zVc8U/DovUGsl1CvT6Z6HwIgJBLEuCVFPyI0B6/130ub5KCI/2kh+STsVWYXrm4f7vY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "then-bot"}], "_npmUser": {"name": "then-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-promise_4.0.0_1588001663226_0.004173280577370431"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2023-06-09T21:37:59.592Z", "created": "2012-12-23T16:25:38.676Z", "1.0.0": "2012-12-23T16:25:41.263Z", "1.0.1": "2014-05-20T13:38:55.043Z", "2.0.0": "2015-02-10T10:06:19.012Z", "2.1.0": "2015-09-06T18:06:36.412Z", "2.2.0": "2020-04-25T15:03:22.986Z", "2.2.1": "2020-04-25T17:55:21.062Z", "2.2.2": "2020-04-25T18:08:06.121Z", "3.0.0": "2020-04-26T13:39:10.847Z", "4.0.0": "2020-04-27T15:34:23.345Z"}, "users": {}, "dist-tags": {"latest": "4.0.0"}, "_rev": "7571-b00ad8ba14fa3860", "_id": "is-promise", "readme": "<a href=\"https://promisesaplus.com/\"><img src=\"https://promisesaplus.com/assets/logo-small.png\" align=\"right\" /></a>\n\n# is-promise\n\n  Test whether an object looks like a promises-a+ promise\n\n [![Build Status](https://img.shields.io/travis/then/is-promise/master.svg)](https://travis-ci.org/then/is-promise)\n [![Dependency Status](https://img.shields.io/david/then/is-promise.svg)](https://david-dm.org/then/is-promise)\n [![NPM version](https://img.shields.io/npm/v/is-promise.svg)](https://www.npmjs.org/package/is-promise)\n\n\n\n## Installation\n\n    $ npm install is-promise\n\nYou can also use it client side via npm.\n\n## API\n\n```typescript\nimport isPromise from 'is-promise';\n\nisPromise(Promise.resolve());//=>true\nisPromise({then:function () {...}});//=>true\nisPromise(null);//=>false\nisPromise({});//=>false\nisPromise({then: true})//=>false\n```\n\n## License\n\n  MIT", "_attachments": {}}