"""
统一规划Agent
系统的大脑，负责任务分析、规划和执行协调
"""
import asyncio
from typing import Dict, Any, Optional
from agno.agent import Agent
from agno.models.openai.like import OpenAILike
import sys
import os
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from agents.shared_memory import get_shared_memory, get_agent_storage
from agno.core.task_analyzer import TaskAnalyzer
from agno.core.agent_selector import AgentSelector
from agno.core.execution_monitor import ExecutionMonitor, ExecutionPlan

AGENT_ID = "unified_planning_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "统一规划助手",
    "brief": "智能任务分析、规划和协调执行的统一入口",
    "category": "workflow",
    "avatar": "touxiang/planning.png",
    "tags": ["规划", "协调", "任务分析", "多Agent"],
    "capabilities": [
        "任务意图识别",
        "复杂度评估", 
        "Agent智能选择",
        "执行过程监控",
        "结果整合输出"
    ],
    "supported_formats": ["html", "markdown", "json"]
}

class UnifiedPlanningAgent:
    """统一规划Agent - 系统的大脑"""
    
    def __init__(self):
        # 核心组件
        self.task_analyzer = TaskAnalyzer()
        self.agent_selector = AgentSelector()
        self.execution_monitor = ExecutionMonitor()
        
        # 使用共享记忆系统
        self.shared_memory = get_shared_memory()
        self.agent_storage = get_agent_storage(AGENT_ID)
        
        # 创建底层Agent实例用于对话
        self.base_agent = Agent(
            name="统一规划助手",
            model=OpenAILike(
                id="deepseek-v3-250324",
                api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
                base_url="https://ark.cn-beijing.volces.com/api/v3"
            ),
            description="你是智能任务规划和协调专家，能够分析用户需求并制定执行计划。",
            instructions="\n".join([
                "你是统一规划助手，负责理解用户需求并协调多个专业Agent完成任务。",
                "你的主要职责包括：",
                "1. 分析用户输入，识别任务意图和复杂度",
                "2. 制定详细的执行计划和Agent分配方案", 
                "3. 监控任务执行过程，协调Agent间的协作",
                "4. 整合执行结果，为用户提供完整的解决方案",
                "5. 在执行过程中提供实时状态更新和进度反馈",
                "",
                "当用户提出需求时，你应该：",
                "- 首先分析任务的类型、复杂度和所需资源",
                "- 制定清晰的执行步骤和Agent分配计划",
                "- 说明预估的执行时间和预期结果",
                "- 在执行过程中提供进度更新",
                "- 最终整合所有结果并提供给用户"
            ]),
            memory=self.shared_memory,
            enable_user_memories=True,
            enable_session_summaries=True,
            storage=self.agent_storage,
            add_history_to_messages=True,
            num_history_responses=3,
            stream=True,
            markdown=True,
            show_tool_calls=True
        )
        
        # 设置事件监听器
        self.execution_monitor.add_event_listener(self._handle_execution_event)
        
        # 存储当前执行状态
        self.current_execution = None
        self.execution_events = []
    
    async def run(self, user_input: str, user_id: str = None, **kwargs) -> str:
        """处理用户输入并执行任务规划"""
        
        try:
            # 1. 任务分析
            user_context = await self._get_user_context(user_id) if user_id else {}
            task_plan = self.task_analyzer.analyze_task(user_input, user_context)
            
            # 2. Agent选择和任务分配
            assignments = self.agent_selector.select_agents_for_plan(task_plan)
            
            # 3. 创建执行计划
            execution_plan = ExecutionPlan(
                id=f"exec_{task_plan.id}",
                task_plan=task_plan,
                assignments=assignments,
                execution_mode="sequential"  # 默认顺序执行
            )
            
            # 4. 生成规划报告
            planning_report = self._generate_planning_report(task_plan, assignments)
            
            # 5. 执行计划（异步）
            self.current_execution = execution_plan
            self.execution_events = []
            
            # 启动异步执行
            asyncio.create_task(self._execute_plan_async(execution_plan))
            
            return planning_report
            
        except Exception as e:
            return f"任务规划失败：{str(e)}"
    
    def _generate_planning_report(self, task_plan, assignments) -> str:
        """生成任务规划报告"""
        report = f"""# 📋 任务规划报告

## 任务分析
- **任务ID**: {task_plan.id}
- **意图类型**: {task_plan.intent.value}
- **复杂度**: {task_plan.complexity.value}
- **预估总时间**: {task_plan.estimated_total_time}分钟

## 执行计划

### 任务步骤
"""
        
        for i, step in enumerate(task_plan.steps, 1):
            report += f"""
**步骤 {i}: {step.title}**
- 描述: {step.description}
- 预估时间: {step.estimated_time}分钟
- 优先级: {step.priority}
"""
            if step.dependencies:
                report += f"- 依赖步骤: {', '.join(step.dependencies)}\n"
        
        report += "\n### Agent分配\n"
        
        for assignment in assignments:
            report += f"""
**{assignment.agent_name}**
- 负责步骤: {assignment.step_id}
- 匹配度: {assignment.confidence:.1%}
- 选择原因: {assignment.assignment_reason}
"""
        
        report += f"""
## 执行状态
🚀 任务规划完成，正在启动执行流程...

*执行过程中会实时更新进度，请稍候...*
"""
        
        return report
    
    async def _execute_plan_async(self, execution_plan: ExecutionPlan):
        """异步执行任务计划"""
        try:
            # 这里应该调用实际的Agent执行逻辑
            # 暂时模拟执行过程
            await asyncio.sleep(1)  # 模拟执行时间
            
            # 模拟执行结果
            results = {}
            for assignment in execution_plan.assignments:
                results[assignment.step_id] = f"步骤 {assignment.step_id} 已完成"
            
            # 更新执行状态
            self.current_execution = None
            
        except Exception as e:
            print(f"执行计划时发生错误: {e}")
    
    async def _handle_execution_event(self, event):
        """处理执行事件"""
        self.execution_events.append(event)
        
        # 这里可以实现实时状态更新逻辑
        # 例如通过WebSocket推送给前端
        print(f"执行事件: {event.event_type} - {event.execution_id}")
    
    async def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """获取用户上下文信息"""
        try:
            # 从共享记忆中获取用户信息
            user_memories = self.shared_memory.get_user_memories(user_id=user_id)
            
            context = {
                "user_id": user_id,
                "memories": [memory.memory for memory in user_memories] if user_memories else [],
                "preferences": {}  # 可以从记忆中提取用户偏好
            }
            
            return context
        except Exception as e:
            print(f"获取用户上下文失败: {e}")
            return {"user_id": user_id}
    
    def get_execution_status(self) -> Optional[Dict[str, Any]]:
        """获取当前执行状态"""
        if self.current_execution:
            return {
                "execution_id": self.current_execution.id,
                "task_plan": self.current_execution.task_plan,
                "status": "running",
                "events": self.execution_events[-10:]  # 最近10个事件
            }
        return None
    
    def print_response(self, message: str, **kwargs):
        """兼容原有Agent接口的方法"""
        result = asyncio.run(self.run(message, **kwargs))
        print(result)
        return result

def create_agent():
    """创建统一规划Agent实例"""
    return UnifiedPlanningAgent()