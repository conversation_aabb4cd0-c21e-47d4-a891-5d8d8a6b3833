#!/usr/bin/env python3
"""
Agno 统一API服务器 (v3.1.0 - Lifespan Events)
所有Agents, Teams, Workflows均通过扫描文件夹动态加载，并使用现代lifespan事件。
"""

import sys
import json
import asyncio
import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import List, Dict, Any
from fastapi import FastAPI, HTTPException, Form, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import uvicorn
from fastapi.staticfiles import StaticFiles

# 导入Agno核心库和新的配置管理器
try:
    from agno.agent import Agent
    from agno.team import Team
    from config_manager import config_manager
    print("✅ 成功导入Agno库和动态配置管理器")
except ImportError as e:
    print(f"❌ 关键模块导入失败: {e}")
    sys.exit(1)

# ===== Lifespan事件管理器 =====
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    在服务启动时，通过ConfigManager动态加载所有组件并预热缓存
    """
    logging.info("🚀 服务启动：开始动态加载和预热...")
    
    # 1. 重新加载所有配置
    config_manager.reload_configs()
    
    # 2. 预创建所有Agent和Team实例以预热缓存
    all_components = config_manager.list_all()
    for agent_id in all_components["agents"]:
        try:
            config_manager.create_agent(agent_id)
        except Exception as e:
            logging.error(f"  -> ❌ 预热Agent '{agent_id}' 失败: {e}")
    
    for team_id in all_components["teams"]:
        try:
            config_manager.create_team(team_id)
        except Exception as e:
            logging.error(f"  -> ❌ 预热Team '{team_id}' 失败: {e}")

    logging.info("✅✅✅ 动态加载和预热完成，服务已准备就绪! ✅✅✅")
    
    yield
    
    # 在这里可以添加服务关闭时需要执行的代码
    logging.info("👋 服务已关闭")

# ===== FastAPI应用配置 =====
app = FastAPI(
    title="Universal Agno API (Dynamic Edition)",
    description="Agno统一API服务器 - 所有组件均通过文件夹动态加载",
    version="3.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件和静态文件托管
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])
app.mount("/static", StaticFiles(directory=".", html=True), name="static")

# ===== 数据模型 =====
class ChatRequest(BaseModel):
    message: str
    agent_id: str
    stream: bool = True

class TeamRequest(BaseModel):
    message: str
    team_id: str
    stream: bool = True

class WorkflowRequest(BaseModel):
    input_data: dict
    workflow_id: str
    stream: bool = True

# ===== 流式响应生成器 (简化版) =====
async def stream_agent_response(agent: Agent, message: str):
    """单Agent流式响应"""
    try:
        # 发送开始执行的事件
        logging.info("🔍 发送事件: execution_start")
        yield f"data: {json.dumps({'type': 'execution_start', 'message': '开始分析任务...'}, ensure_ascii=False)}\n\n"
        
        response_generator = agent.run(message, stream=True)
        for chunk in response_generator:
            # 处理工具调用信息
            if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                for tool_call in chunk.tool_calls:
                    tool_name = getattr(tool_call, 'name', 'unknown_tool')
                    # 如果是call_agent工具，提取实际的agent名称和详细信息
                    if tool_name == 'call_agent' and hasattr(tool_call, 'arguments'):
                        try:
                            import json as json_lib
                            args = json_lib.loads(tool_call.arguments) if isinstance(tool_call.arguments, str) else tool_call.arguments
                            agent_id = args.get('agent_id', 'unknown_agent')
                            task = args.get('task', '')
                            context = args.get('context', '')
                            
                            # 从agent_id获取友好名称
                            agent_name = get_agent_display_name(agent_id)
                            
                            # 发送Agent调用开始事件
                            logging.info(f"🔍 发送事件: agent_call_start - {agent_name}")
                            
                            # 构建详细的任务信息
                            task_info = f"任务：{task}"
                            if context:
                                task_info += f" | 上下文：{context[:100]}{'...' if len(context) > 100 else ''}"

                            # 计算任务和上下文的字符数
                            task_length = len(task)
                            context_length = len(context) if context else 0
                            total_length = task_length + context_length

                            yield f"data: {json.dumps({
                                'type': 'agent_call_start',
                                'agent_id': agent_id,
                                'agent_name': agent_name,
                                'task': task,
                                'context': context,
                                'message': f'正在调用 {agent_name} 执行任务... (任务长度: {task_length}字符, 上下文: {context_length}字符, 总计: {total_length}字符)',
                                'task_info': task_info,
                                'task_length': task_length,
                                'context_length': context_length,
                                'total_length': total_length
                            }, ensure_ascii=False)}\n\n"
                        except Exception as parse_error:
                            logging.error(f"解析工具调用参数失败: {parse_error}")
                            yield f"data: {json.dumps({'type': 'tool_call', 'tool_name': tool_name, 'status': 'started'}, ensure_ascii=False)}\n\n"
                    else:
                        yield f"data: {json.dumps({'type': 'tool_call', 'tool_name': tool_name, 'status': 'started'}, ensure_ascii=False)}\n\n"
            
            # 处理工具调用结果
            if hasattr(chunk, 'tool_call_results') and chunk.tool_call_results:
                for result in chunk.tool_call_results:
                    # 清理和格式化工具调用结果
                    result_str = str(result)

                    # 调试：打印原始结果
                    print(f"🔍 原始工具调用结果长度: {len(result_str)}")
                    print(f"🔍 原始工具调用结果前200字符: {result_str[:200]}")

                    # 如果是agent调用结果，提取实际内容
                    if "## 🎯 任务执行结果" in result_str:
                        # 提取实际的agent输出内容
                        lines = result_str.split('\n')
                        content_started = False
                        actual_content = []

                        for line in lines:
                            if line.strip() == "### 📋 执行结果":
                                content_started = True
                                continue
                            elif content_started and line.strip() == "---":
                                break
                            elif content_started:
                                # 包含空行，保持原始格式
                                actual_content.append(line)

                        if actual_content:
                            # 移除开头和结尾的空行
                            while actual_content and not actual_content[0].strip():
                                actual_content.pop(0)
                            while actual_content and not actual_content[-1].strip():
                                actual_content.pop()
                            result_str = '\n'.join(actual_content)

                    # 发送清理后的结果
                    yield f"data: {json.dumps({'type': 'tool_result', 'result': result_str}, ensure_ascii=False)}\n\n"
            
            # 处理正常内容
            if hasattr(chunk, 'content') and chunk.content:
                # 分析内容类型并发送相应的事件
                content = chunk.content
                
                # 检测调试信息 - 新增
                if '调试信息:' in content or '🔍 调试信息:' in content:
                    logging.info(f"🔍 发送事件: debug_info - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'debug_info',
                        'content': content,
                        'message': '正在处理任务信息...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测上下文信息 - 新增
                elif '上下文:' in content:
                    logging.info(f"🔍 发送事件: context_info - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'context_info',
                        'content': content,
                        'message': '正在分析任务上下文...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测思考过程 - 改进关键词匹配
                elif any(keyword in content.lower() for keyword in ['分析', '思考', '确定', '考虑', '计划', '分配', '调用']):
                    logging.info(f"🔍 发送事件: thinking_process - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'thinking_process',
                        'content': content,
                        'message': 'AI助手正在思考...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测任务分配
                elif any(keyword in content.lower() for keyword in ['分配', '指派', '选择', '确定调用']):
                    logging.info(f"🔍 发送事件: task_assignment - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'task_assignment',
                        'content': content,
                        'message': '正在分配任务给专业Agent...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测产品分析
                elif any(keyword in content.lower() for keyword in ['产品', '特点', '功能', '优势', '耳机', '降噪']):
                    logging.info(f"🔍 发送事件: product_analysis - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'product_analysis',
                        'content': content,
                        'message': '正在分析产品特点...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测平台分析
                elif any(keyword in content.lower() for keyword in ['平台', '小红书', '抖音', '用户', '文案', '短视频']):
                    logging.info(f"🔍 发送事件: platform_analysis - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'platform_analysis',
                        'content': content,
                        'message': '正在分析平台特点...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测工作过程
                elif any(keyword in content.lower() for keyword in ['正在', '执行', '处理', '生成', '撰写', '策划']):
                    logging.info(f"🔍 发送事件: working_process - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'working_process',
                        'content': content,
                        'message': '正在处理中...'
                    }, ensure_ascii=False)}\n\n"
                
                # 检测完成状态
                elif any(keyword in content.lower() for keyword in ['完成', '已生成', '结果', '成功', '最终提取']):
                    logging.info(f"🔍 发送事件: completion_status - 内容长度: {len(content)}")
                    
                    # 检查是否包含"最终提取的结果长度"信息
                    if '最终提取的结果长度' in content:
                        # 提取具体的长度信息
                        import re
                        length_match = re.search(r'最终提取的结果长度\s*=\s*(\d+)', content)
                        if length_match:
                            result_length = length_match.group(1)
                            message = f'任务已完成，结果长度: {result_length} 字符'
                        else:
                            message = '任务已完成'
                    else:
                        message = '任务已完成'
                    
                    yield f"data: {json.dumps({
                        'type': 'completion_status',
                        'content': content,
                        'message': message
                    }, ensure_ascii=False)}\n\n"
                
                # 检测整合过程
                elif any(keyword in content.lower() for keyword in ['总结', '整合', '汇总', '最终', '整合结果']):
                    logging.info(f"🔍 发送事件: integration_process - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({
                        'type': 'integration_process',
                        'content': content,
                        'message': '正在整合结果...'
                    }, ensure_ascii=False)}\n\n"
                
                # 默认内容
                else:
                    logging.info(f"🔍 发送事件: content - 内容长度: {len(content)}")
                    yield f"data: {json.dumps({'type': 'content', 'content': content}, ensure_ascii=False)}\n\n"
                
                await asyncio.sleep(0.01)
        
        # 发送执行完成事件
        logging.info("🔍 发送事件: execution_complete")
        yield f"data: {json.dumps({'type': 'execution_complete', 'message': '任务执行完成'}, ensure_ascii=False)}\n\n"
        yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
    except Exception as e:
        logging.error(f"Agent响应错误: {e}", exc_info=True)
        yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"

def get_agent_display_name(agent_id: str) -> str:
    """获取Agent的显示名称"""
    agent_names = {
        'xiaohongshu_copywriting_agent': '小红书文案专家',
        'week_report_agent': '周报专家',
        'daily_report_agent': '日报专家',
        'month_report_agent': '月报专家',
        'schedule_agent': '日程安排专家',
        'composition_agent': '作文写作专家',
        'official_document_writing_agent': '公文写作专家',
        'new_media_copywriting_agent': '新媒体文案专家',
        'xiaohongshu_creation_agent': '小红书创作专家'
    }
    return agent_names.get(agent_id, agent_id)

async def stream_team_response(team: Team, message: str):
    """团队协作流式响应"""
    try:
        # 简化实现：直接运行团队并流式返回最终结果
        final_response = ""
        response_generator = team.run(message, stream=True)
        for chunk in response_generator:
            if hasattr(chunk, 'content'):
                final_response += chunk.content
                yield f"data: {json.dumps({'type': 'content', 'content': chunk.content}, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.01)
        yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
        logging.error(f"团队协作错误: {e}", exc_info=True)

# ===== API端点 =====
@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "3.1.0",
        "timestamp": datetime.now().isoformat(),
        "loaded_components": config_manager.list_all()
    }

@app.get("/api/agents")
async def list_agents():
    """获取所有可用的Agent列表"""
    agents_info = []
    for agent_id in config_manager.list_all()["agents"]:
        metadata = config_manager.get_agent_metadata(agent_id)
        agents_info.append({
            "id": agent_id,
            "name": metadata.get("display_name", agent_id),
            "description": metadata.get("brief", ""),
            **metadata
        })
    return {"agents": agents_info}

@app.get("/api/teams")
async def list_teams():
    """获取所有可用的团队列表"""
    teams_info = []
    for team_id, config in config_manager.teams_config.items():
        teams_info.append({"id": team_id, **config})
    return {"teams": teams_info}

@app.get("/api/workflows")
async def list_workflows():
    """获取所有可用的工作流列表"""
    workflows_info = []
    for workflow_id, config in config_manager.workflows_config.items():
        workflows_info.append({"id": workflow_id, **config})
    return {"workflows": workflows_info}

@app.post("/api/chat")
async def chat_with_agent(request: ChatRequest):
    """单Agent对话"""
    try:
        agent = config_manager.create_agent(request.agent_id)
        if request.stream:
            return StreamingResponse(stream_agent_response(agent, request.message), media_type="text/event-stream")
        else:
            response = agent.run(request.message, stream=False)
            return {"response": str(response)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/team")
async def team_collaboration(request: TeamRequest):
    """团队协作"""
    try:
        team = config_manager.create_team(request.team_id)
        if request.stream:
            return StreamingResponse(stream_team_response(team, request.message), media_type="text/event-stream")
        else:
            response = team.run(request.message, stream=False)
            return {"response": str(response)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/workflow")
async def execute_workflow(request: WorkflowRequest):
    """执行工作流 (简化版，非流式)"""
    try:
        workflow_config = config_manager.get_workflow_config(request.workflow_id)
        results = {}
        context = request.input_data
        for step in workflow_config.get("steps", []):
            agent = config_manager.create_agent(step["agent_id"])
            step_input = f"Context: {json.dumps(context, ensure_ascii=False)}\n\nTask: {step['description']}"
            response = agent.run(step_input, stream=False)
            results[step["id"]] = str(response)
            context[step["id"]] = str(response) # 将本步结果加入上下文
        return {"workflow_id": request.workflow_id, "status": "completed", "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ===== 启动配置 =====
def setup_logging():
    """配置日志"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

if __name__ == "__main__":
    setup_logging()
    print("🚀 启动 Universal Agno API 服务器 (v3.1.0 - Lifespan Events)...")
    print("🌐 服务地址: http://localhost:8001")
    print("📚 API文档: http://localhost:8001/docs")
    
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="info")
