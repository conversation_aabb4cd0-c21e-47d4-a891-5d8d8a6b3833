<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手 - 对话</title>
    <!-- Markdown 渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            height: 100vh;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8fafc;
            max-height: calc(100vh - 280px);
        }
        
        /* 状态显示框样式 */
        .status-display {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 15px 20px;
            min-height: 60px;
            display: flex;
            align-items: center;
        }
        
        .status-content {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
        }
        
        .status-icon {
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .status-text {
            flex: 1;
            font-size: 12px;
            color: #374151;
            font-weight: 400;
        }
        
        /* 不同状态的颜色 */
        .status-display.waiting {
            background: #f9fafb;
        }
        
        .status-display.thinking {
            background: #f0f9ff;
        }
        
        .status-display.analyzing {
            background: #f0fdf4;
        }
        
        .status-display.working {
            background: #fef3c7;
        }
        
        .status-display.completed {
            background: #f0fdf4;
        }
        
        .message-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .user-input-section .section-title {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .thinking-section .section-title {
            color: #0ea5e9;
            border-bottom-color: #0ea5e9;
        }
        
        .tool-call-section .section-title {
            color: #f59e0b;
            border-bottom-color: #f59e0b;
        }
        
        .steps-section .section-title {
            color: #8b5cf6;
            border-bottom-color: #8b5cf6;
        }
        
        .result-section .section-title {
            color: #10b981;
            border-bottom-color: #10b981;
        }
        
        .content-box {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .user-input-box {
            background: #eff6ff;
            border-color: #3b82f6;
        }
        
        .thinking-box {
            background: #f0f9ff;
            border-color: #0ea5e9;
        }
        
        .tool-call-box {
            background: #fef3c7;
            border-color: #f59e0b;
        }
        
        .steps-box {
            background: #f3f4f6;
            border-color: #8b5cf6;
        }
        
        .result-box {
            background: #f0fdf4;
            border-color: #10b981;
        }
        
        .content-text {
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
        }
        
        .user-input-text {
            font-size: 16px;
            font-weight: 500;
            color: #1e293b;
        }
        
        .thinking-text {
            font-style: italic;
            color: #0c4a6e;
        }
        
        .tool-call-text {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #92400e;
        }
        
        .steps-text {
            color: #581c87;
        }
        
        .result-text {
            color: #065f46;
        }
        
        /* Markdown样式 */
        .result-text h1, .result-text h2, .result-text h3, .result-text h4, .result-text h5, .result-text h6 {
            margin: 16px 0 8px 0;
            color: #065f46;
        }
        
        .result-text h1 { font-size: 24px; }
        .result-text h2 { font-size: 20px; }
        .result-text h3 { font-size: 18px; }
        .result-text h4 { font-size: 16px; }
        
        .result-text p {
            margin: 8px 0;
            line-height: 1.6;
        }
        
        .result-text ul, .result-text ol {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .result-text li {
            margin: 4px 0;
        }
        
        .result-text code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .result-text pre {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            overflow-x: auto;
            margin: 12px 0;
        }
        
        .result-text blockquote {
            border-left: 4px solid #10b981;
            padding-left: 12px;
            margin: 12px 0;
            color: #64748b;
            font-style: italic;
        }
        

        
        .agent-call-details {
            flex: 1;
        }
        
        .agent-call-name {
            font-weight: 600;
            color: #581c87;
            margin-bottom: 4px;
        }
        
        .agent-call-task {
            font-size: 14px;
            color: #6b21a8;
            margin-bottom: 4px;
        }
        
        .agent-call-context {
            font-size: 13px;
            color: #7c3aed;
            margin-bottom: 4px;
            font-style: italic;
        }
        
        .agent-call-status {
            font-size: 12px;
            color: #7c3aed;
            font-style: italic;
        }
        
        .working-status {
            font-size: 12px;
            color: #a16207;
            font-style: italic;
        }
        

        

        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-bar {
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #64748b;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #9ca3af;
        }
        
        .status-indicator.running {
            background: #3b82f6;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.completed {
            background: #10b981;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .streaming-content {
            min-height: 20px;
        }
        
        /* 输入区域样式 */
        .input-area {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 20px;
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 800px;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .input-area.hidden {
            display: none;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            margin-bottom: 15px;
        }
        
        .user-avatar-container {
            flex-shrink: 0;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .user-input {
            flex: 1;
            min-height: 60px;
            max-height: 120px;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }
        
        .user-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .send-btn:hover {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .secondary-btn {
            background: #f8fafc;
            border-color: #e2e8f0;
        }
        

        
        @media (max-width: 768px) {
            .chat-container {
                margin: 0;
                box-shadow: none;
            }
            
            .messages-container {
                padding: 15px;
                max-height: calc(100vh - 180px);
            }
            
            .content-box {
                padding: 15px;
            }
            
            .input-area {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            🤖 AI助手
        </div>
        
        <div class="status-bar" id="status-bar">
            <div class="status-indicator" id="status-indicator"></div>
            <span id="status-text">准备就绪</span>
        </div>
        
        <div class="messages-container" id="messages-container">
            <!-- 内容将动态生成 -->
        </div>
        
        <!-- 状态显示框 -->
        <div class="status-display" id="status-display">
            <div class="status-content">
                <div class="status-icon">⏳</div>
                <div class="status-text">等待任务开始...</div>
            </div>
        </div>
        
        <!-- 输入区域 -->
        <div class="input-area" id="input-area">
            <div class="input-container">
                <div class="user-avatar-container">
                    <div class="user-avatar">👤</div>
                </div>
                <textarea 
                    id="user-input" 
                    class="user-input" 
                    placeholder="继续与AI助手交流..."
                ></textarea>
                <button id="send-btn" class="send-btn">发送</button>
            </div>
            <div class="action-buttons">
                <button id="new-task-btn" class="action-btn secondary-btn">🔄 新任务</button>
                <button id="back-home-btn" class="action-btn secondary-btn">🏠 返回首页</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        const UNIFIED_PLANNING_AGENT_ID = 'unified_planning_agent';
        
        let currentTask = '';
        let isExecuting = false;
        let currentStreamingMessage = null;
        
        // 页面加载时获取任务参数
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            currentTask = urlParams.get('task') || '';
            
            if (currentTask) {
                initializeChat();
            } else {
                goBack();
            }
        });
        
        function initializeChat() {
            // 显示用户输入
            addUserInputSection(currentTask);
            
            // 开始执行任务
            setTimeout(() => {
                executeTask(currentTask);
            }, 500);
        }
        
        function addUserInputSection(task) {
            const container = document.getElementById('messages-container');
            
            const section = document.createElement('div');
            section.className = 'message-section user-input-section';
            section.innerHTML = `
                <div class="section-title">用户输入</div>
                <div class="content-box user-input-box">
                    <div class="content-text user-input-text">${task}</div>
                </div>
            `;
            
            container.appendChild(section);
        }
        
        function addThinkingSection(thought) {
            const container = document.getElementById('messages-container');
            
            const section = document.createElement('div');
            section.className = 'message-section thinking-section';
            section.innerHTML = `
                <div class="section-title">思考过程</div>
                <div class="content-box thinking-box">
                    <div class="content-text thinking-text">${thought}</div>
                </div>
            `;
            
            container.appendChild(section);
            scrollToBottom();
        }
        
        function addToolCallSection(toolName, parameters) {
            const container = document.getElementById('messages-container');
            
            const section = document.createElement('div');
            section.className = 'message-section tool-call-section';
            section.innerHTML = `
                <div class="section-title">调用工具</div>
                <div class="content-box tool-call-box">
                    <div class="content-text tool-call-text">
                        <strong>工具名称:</strong> ${toolName}<br>
                        <strong>参数:</strong><br>
                        <pre>${JSON.stringify(parameters, null, 2)}</pre>
                    </div>
                </div>
            `;
            
            container.appendChild(section);
            scrollToBottom();
        }
        
        function addResultSection(result) {
            const container = document.getElementById('messages-container');
            
            const section = document.createElement('div');
            section.className = 'message-section result-section';
            section.innerHTML = `
                <div class="section-title">最终结果</div>
                <div class="content-box result-box">
                    <div class="content-text result-text streaming-content">${result}</div>
                </div>
            `;
            
            container.appendChild(section);
            scrollToBottom();
        }
        
        function updateStatus(status, text) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            indicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }
        
        function updateStatusDisplay(icon, text, status) {
            const statusDisplay = document.getElementById('status-display');
            const statusIcon = statusDisplay.querySelector('.status-icon');
            const statusText = statusDisplay.querySelector('.status-text');
            
            statusIcon.textContent = icon;
            statusText.textContent = text;
            
            // 移除所有状态类
            statusDisplay.classList.remove('waiting', 'thinking', 'analyzing', 'working', 'completed', 'error');
            // 添加新的状态类
            statusDisplay.classList.add(status);
        }
        
        async function executeTask(task) {
            isExecuting = true;
            updateStatus('running', '正在分析任务...');
            
            try {
                // 添加思考过程
                updateStatusDisplay('🧠', '分析用户需求，确定需要调用的专业Agent...', 'thinking');
                
                // 添加预估时间提示
                updateStatusDisplay('⏳', '正在连接统一规划助手，预计需要 1-2 分钟完成整个任务...', 'waiting');
                
                // 调用统一规划助手API
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: task,
                        agent_id: UNIFIED_PLANNING_AGENT_ID,
                        stream: true
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                updateStatus('running', '正在执行...');
                
                // 处理流式响应
                await handleStreamResponse(response);
                
                updateStatus('completed', '任务完成');
                isExecuting = false;
                
            } catch (error) {
                console.error('执行任务失败:', error);
                updateStatus('completed', '执行失败');
                updateStatusDisplay('❌', `执行失败: ${error.message}`, 'error');
                isExecuting = false;
            }
        }
        
        async function handleStreamResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let currentMessage = '';
            let resultSection = null;
            let lastUpdateTime = Date.now();
            
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop();
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.substring(6);
                            
                            try {
                                const data = JSON.parse(dataStr);
                                
                                // 处理不同类型的后端事件
                                switch (data.type) {
                                    case 'execution_start':
                                        updateStatus('running', '开始分析任务...'); // Update top status bar
                                        updateStatusDisplay('🚀', '开始分析任务...', 'thinking');
                                        break;
                                        
                                    case 'debug_info':
                                        updateStatus('running', '正在处理任务信息...'); // Update top status bar
                                        updateStatusDisplay('🔍', '正在处理任务信息...', 'analyzing');
                                        // 如果有详细的任务信息，也显示出来
                                        if (data.content) {
                                            console.log('调试信息:', data.content);
                                        }
                                        break;
                                        
                                    case 'context_info':
                                        updateStatus('running', '正在分析任务上下文...'); // Update top status bar
                                        updateStatusDisplay('📋', '正在分析任务上下文...', 'analyzing');
                                        // 如果有详细的上下文信息，也显示出来
                                        if (data.content) {
                                            console.log('上下文信息:', data.content);
                                        }
                                        break;
                                        
                                    case 'thinking_process':
                                        updateStatus('running', 'AI助手正在思考...'); // Update top status bar
                                        updateStatusDisplay('🧠', 'AI助手正在思考...', 'thinking');
                                        break;
                                        
                                    case 'task_assignment':
                                        updateStatus('running', '正在分配任务给专业Agent...'); // Update top status bar
                                        updateStatusDisplay('📋', '正在分配任务给专业Agent...', 'analyzing');
                                        break;
                                        
                                    case 'product_analysis':
                                        updateStatus('running', '正在分析产品特点...'); // Update top status bar
                                        updateStatusDisplay('��', '正在分析产品特点...', 'analyzing');
                                        break;
                                        
                                    case 'platform_analysis':
                                        updateStatus('running', '正在分析平台特点...');
                                        updateStatusDisplay('📱', '正在分析平台特点...', 'analyzing');
                                        break;
                                        
                                    case 'agent_call_start':
                                        updateStatus('running', `正在调用 ${data.agent_name} 执行任务...`); // Update top status bar
                                        updateStatusDisplay('🚀', `正在调用 ${data.agent_name} 执行任务...`, 'working');
                                        // 如果有详细任务信息，也显示出来
                                        if (data.task_info) {
                                            console.log('任务详情:', data.task_info);
                                        }
                                        break;
                                        
                                    case 'working_process':
                                        updateStatus('running', '正在处理中...'); // Update top status bar
                                        updateStatusDisplay('⚙️', '正在处理中...', 'working');
                                        break;
                                        
                                    case 'completion_status':
                                        updateStatus('running', data.message); // Update top status bar with detailed message
                                        updateStatusDisplay('✅', data.message, 'completed');
                                        break;
                                        
                                    case 'integration_process':
                                        updateStatus('running', '正在整合结果...');
                                        updateStatusDisplay('🔗', '正在整合结果...', 'working');
                                        break;
                                        
                                    case 'execution_complete':
                                        updateStatus('completed', '任务执行完成');
                                        updateStatusDisplay('🎉', '任务执行完成', 'completed');
                                        break;
                                        
                                    case 'content':
                                        currentMessage += data.content;
                                        
                                        // 更新或创建结果区域（限制更新频率）
                                        const now = Date.now();
                                        if (now - lastUpdateTime > 500) { // 每500ms更新一次
                                            if (!resultSection) {
                                                resultSection = createResultSection();
                                            }
                                            updateStreamingContent(resultSection, currentMessage);
                                            lastUpdateTime = now;
                                        }
                                        break;
                                        
                                    case 'end':
                                        if (currentMessage) {
                                            finalizeResult(resultSection, currentMessage);
                                        }
                                        break;
                                        
                                    case 'error':
                                        console.error('后端错误:', data.error);
                                        updateStatus('completed', `执行错误: ${data.error}`);
                                        updateStatusDisplay('❌', `执行错误: ${data.error}`, 'error');
                                        break;
                                }
                            } catch (e) {
                                console.error('解析流数据失败:', e);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        }
        
        function getAgentDisplayName(agentId) {
            const agentNames = {
                'xiaohongshu_copywriting_agent': '小红书文案专家',
                'week_report_agent': '周报专家',
                'daily_report_agent': '日报专家',
                'month_report_agent': '月报专家',
                'schedule_agent': '日程安排专家',
                'composition_agent': '作文写作专家',
                'official_document_writing_agent': '公文写作专家',
                'new_media_copywriting_agent': '新媒体文案专家',
                'xiaohongshu_creation_agent': '小红书创作专家'
            };
            return agentNames[agentId] || agentId;
        }
        
        function getAgentIcon(agentId) {
            const agentIcons = {
                'xiaohongshu_copywriting_agent': '📝',
                'week_report_agent': '📊',
                'daily_report_agent': '📅',
                'month_report_agent': '📈',
                'schedule_agent': '⏰',
                'composition_agent': '✍️',
                'official_document_writing_agent': '📋',
                'new_media_copywriting_agent': '📱',
                'xiaohongshu_creation_agent': '🎨'
            };
            return agentIcons[agentId] || '🤖';
        }
        
        // 移除不再使用的旧函数
        
        function createResultSection() {
            const container = document.getElementById('messages-container');
            
            const section = document.createElement('div');
            section.className = 'message-section result-section';
            section.innerHTML = `
                <div class="section-title">执行结果</div>
                <div class="content-box result-box">
                    <div class="content-text result-text streaming-content"></div>
                </div>
            `;
            
            container.appendChild(section);
            scrollToBottom();
            return section;
        }
        
        function updateStreamingContent(section, content) {
            const contentElement = section.querySelector('.streaming-content');
            if (contentElement) {
                // 将Markdown转换为HTML
                const htmlContent = marked.parse(content);
                contentElement.innerHTML = htmlContent;
                scrollToBottom();
            }
        }
        
        function finalizeResult(section, content) {
            const contentElement = section.querySelector('.streaming-content');
            if (contentElement) {
                // 将Markdown转换为HTML
                const htmlContent = marked.parse(content);
                contentElement.innerHTML = htmlContent;
                contentElement.classList.remove('streaming-content');
            }
        }
        
        function scrollToBottom() {
            const container = document.getElementById('messages-container');
            container.scrollTop = container.scrollHeight;
        }
        
        function showInputArea() {
            const inputArea = document.getElementById('input-area');
            inputArea.classList.remove('hidden');
            inputArea.style.display = 'block';
            
            // 绑定事件
            bindInputEvents();
        }
        
        function bindInputEvents() {
            const userInput = document.getElementById('user-input');
            const sendBtn = document.getElementById('send-btn');
            const newTaskBtn = document.getElementById('new-task-btn');
            const backHomeBtn = document.getElementById('back-home-btn');
            
            // 发送消息
            sendBtn.addEventListener('click', function() {
                const message = userInput.value.trim();
                if (message && !isExecuting) {
                    sendFollowUpMessage(message);
                }
            });
            
            // Enter键发送
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!isExecuting) {
                        sendBtn.click();
                    }
                }
            });
            
            // 新任务
            newTaskBtn.addEventListener('click', function() {
                window.location.href = 'homepage.html';
            });
            
            // 返回首页
            backHomeBtn.addEventListener('click', function() {
                window.location.href = 'homepage.html';
            });
            
            // 自动聚焦输入框
            userInput.focus();
        }
        
        async function sendFollowUpMessage(message) {
            const userInput = document.getElementById('user-input');
            const sendBtn = document.getElementById('send-btn');
            
            try {
                // 添加用户消息
                addUserInputSection(message);
                userInput.value = '';
                
                // 禁用输入
                sendBtn.disabled = true;
                isExecuting = true;
                updateStatus('running', '正在处理...');
                
                // 调用API
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        agent_id: UNIFIED_PLANNING_AGENT_ID,
                        stream: true
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                // 处理流式响应
                await handleStreamResponse(response);
                
                updateStatus('completed', '任务完成');
                isExecuting = false;
                sendBtn.disabled = false;
                
            } catch (error) {
                console.error('发送消息失败:', error);
                updateStatus('completed', '执行失败');
                updateStatusDisplay('❌', `执行失败: ${error.message}`, 'error');
                isExecuting = false;
                sendBtn.disabled = false;
            }
        }
        
        function goBack() {
            window.location.href = 'homepage.html';
        }
    </script>
</body>
</html> 