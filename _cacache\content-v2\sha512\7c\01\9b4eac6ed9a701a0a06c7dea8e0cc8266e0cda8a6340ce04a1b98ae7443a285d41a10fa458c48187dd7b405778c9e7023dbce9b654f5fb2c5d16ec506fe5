{"name": "restore-cursor", "versions": {"1.0.0": {"name": "restore-cursor", "version": "1.0.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "1abe672e88a954579a8bf9b1da4c2b1530c76c0d", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-1.0.0.tgz", "integrity": "sha512-Lmtz5OKaTZeznaBeG1mXj0yCcJB/EkXaCyT9dUXB4MWuBzJ0Srwq+emBM9G4OkEm0H9vuxCe/UFV9gkj4myhOg==", "signatures": [{"sig": "MEQCIBRce7zsvuRP7VgDrMAYiLw+RoGCxtPj5eAtDjXBrvSmAiAy4Y9fBvxTT7fS6NBpSMJ0Drs2eqtQNfegGa8361obUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "1abe672e88a954579a8bf9b1da4c2b1530c76c0d", "engines": {"node": ">=0.10.0"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/restore-cursor", "type": "git"}, "_npmVersion": "1.4.9", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "dependencies": {"exit-hook": "^1.0.0"}, "contributors": []}, "1.0.1": {"name": "restore-cursor", "version": "1.0.1", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "34661f46886327fed2991479152252df92daa541", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-1.0.1.tgz", "integrity": "sha512-reSjH4HuiFlxlaBaFCiS6O76ZGG2ygKoSlCsipKdaZuKSPx/+bt9mULkn4l0asVzbEfQQmXRg6Wp6gv6m0wElw==", "signatures": [{"sig": "MEQCIFjnZ7rKEqFYtmNXlUCUCft6RCXwQH8zijSU1s6SdO5cAiBGU4pK9T3PIg66a2pY9lNgKVO/uqcJQ6VQB6RkRKCtOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "34661f46886327fed2991479152252df92daa541", "engines": {"node": ">=0.10.0"}, "gitHead": "91542e5be16d7ccda8e42a63d56cc783d2cfaba2", "scripts": {}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"onetime": "^1.0.0", "exit-hook": "^1.0.0"}, "contributors": []}, "2.0.0": {"name": "restore-cursor", "version": "2.0.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "9f7ee287f82fd326d4fd162923d62129eee0dfaf", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==", "signatures": [{"sig": "MEYCIQCCFsXc15QBKZwvC1HfkWJ56Y6pU06bN9QJ3ZnOijJNHAIhAKAK/ELxVAcCs0AE2VDWESasrWuo4jaU7DTM5UIAm9ov", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "9f7ee287f82fd326d4fd162923d62129eee0dfaf", "engines": {"node": ">=4"}, "gitHead": "0a0d317b421cb7f89d496ad95e2936b781b8f952", "scripts": {}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/restore-cursor-2.0.0.tgz_1483989430842_0.5384121846873313", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "3.0.0": {"name": "restore-cursor", "version": "3.0.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "5dad57d9e4b2321b68108229e6d06e9ddd95c6e1", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-cbaEHnMdPPd+tCDKIRLJq77Wfx8IHofucftmQyamq+IOdLwj7AcjmHY7QlkT97hrVNP3E87KT1G/W0MbWSrxug==", "signatures": [{"sig": "MEUCIG4s6ir7jGP7M5BE/XMwc+/nw9x6hZb250ApatcnmyYXAiEAqxSBgyqKQMlSVxgnhkfRUho+KIJEImpyx+eYBAZB3QI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctojZCRA9TVsSAnZWagAA3Q4P/32MCcBn9ZSJVfNwgdGq\nmJQYMZuv3fAeBTYAw8HCbYzmBk387sYgCWMwC3G/zTRpfb9t824KqSfZ3As/\nxVz22KvpCarK2VbS1CB0R+EK4Ishxa6XJGWZmtuy07DpzC62q+UldQPiwme0\nGdb5GTJ7hE0hmf1P/6IMRU/KL/j7tFPZ2A4j99+UDX1gPNDszELrD2Go2WMK\n7XFO7XOHgM61z1SokQfrLLzDkjaJ64FGWUkGtLaC+YKKMrMvML3b5Pm1gXgZ\ne9taMkGkFYjujgfBjLApOjgUTtnIMp5MG2R3XBCaE7TmIYmRtUwZ8Rp4Ohb/\nWVnnaSdIOi7a+tmAXx3/P/7jJ7lO4NxPlepXXnXc+JPUtx9cy0MdXWopc2eG\nvnFgW305mTZflPek+z4gXnZKh07g1znpdgvDyvTc0H3SARyL/MGs0aTOsSXP\nBFABFS5OmQ+H64EzD8pKMgNFAZAYfG0Fi5j8IZ+2bSp0BeXJxk9Bf7u7hnvl\nk6f7oq7oneX3vbBzGd/9Klu1l8IFGgSn0zBq515cK8WbEQUJzE1nydkSTSE0\nJ+dcBwUsdNyaDUFdICNXoatPerbedhfHfOa1BLnANNLGNYJ7ElkhTcRlazRv\nO27B/mwJtmtYmbfB9u77hmCGhQImEfpvnrJf6flwYaf+A3QCvAuz3TyoecNY\nyN4O\r\n=YJ1G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "d3078dc39e02f290d0150ea79a7fc6c303d2caec", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/restore-cursor_3.0.0_1555466456462_0.8980970673394233", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "restore-cursor", "version": "3.1.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "39f67c54b3a7a58cea5236d95cf0034239631f7e", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "signatures": [{"sig": "MEUCICugb6lqqFvVYUQa2ERRTNEoT9D3mjS0oFgof2sLAJF2AiEAgxs1wGSTxlA55p/2gMSu4Wz+9vYA8WGPjwjWnNrMpn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwqVQCRA9TVsSAnZWagAA2TcP/2o5tdXHzonttB2kMpGB\nkZvh140sz/236YxgC5Avoiyedm67nX2xH2VZ5yWrH9VJ6aQgT3dSCkSSo9qE\n1tkt0G+SjPGTC/DL1MHlMVxq/+ARue6Qq0NgZjAtEvplKs2TktbzWwvHIM56\n1qb96WtM40/rH0PW+DeeEGR8CGis9ZAmpJAGDYR8Bk9vKSLf0OSOILZArhF0\nflIuADIUqWSpZT3Wq1kIvvbF5FLs2Q7myaLVJStZqT7NC4rTVqIgSILBvI/q\nIWp5rYaFD+vcyJYxOW/fJYs3sez/ORWggRZ5AtEt3lLUYFQhh0nPlkflClLN\nj2E9PD2wJTtThGuCxVySpLtJhAFJfcn/v8W4pwqQVFIvhaIGphWjmmwShJG3\n2epdrMkJH4z3OKEfhhr9OiyBR7ymDyd/+cwb2kuixBuVNcPezipmkfhYWr3i\nwv+Mrb3l+Xrh96HXgw423YsS11vRx/69//Jc7h3ZFKkg/3vBcqNq235lHAuf\n3pOIF5UqeJusj5BQvu4r/N+QJbprR8ROmGY4VprpTzg7+IpUsKeDni3CNtW2\nv4yshL+FjV/XkKH/nAVqqheZPUVXQ+WWp/+aAYLio5os7N8Z7543vubbVID0\n/kD7Q9MYMh5NJUN53mdkyUIikaKBTSBBYF1ObyNJUmcSPoOkR+yc8O//X0Tk\nUJ0a\r\n=0rVp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "32accb3425dbcde0b303583b9137857451b67045", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/restore-cursor_3.1.0_1556260175458_0.920227550808894", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "restore-cursor", "version": "4.0.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "519560a4318975096def6e609d44100edaa4ccb9", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==", "signatures": [{"sig": "MEYCIQDkVq3fqguMc/X3rnQGBYtnVoSMqJK5hXSUUWltXqqMLQIhAIi5AJGAOWQJ/yXr7D3Ka8Tu3I9TewjcYp7ASZeeO05Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhI/aZCRA9TVsSAnZWagAAeAYP/iG0yhhQqQv764rYLv1G\nn9SE9wwBmOOrHPLPjOAF7ev/NH1nIU4gCf3rtx0DXEAcvGEytAID8zOKNzi5\nTJHpdkcwzimefNlpfvE8jbu+/FV3YCYkvBuzEOs0fpK/XnhQGScUf/QH1j6i\nHJpw3fFbYjMAN4Plr1fqL8KgMdrFKhcuOfPLlaR/WKxhLMn7NmiK2Vm5I37G\nI5bmggcGLTSoeeDkY0qxNIISetRNURKvf3Etgl7JEmUfjWHxd0zo2gKNY34p\nxcgbkJUeYP9YWwng92aLZx7D2YJML7bOIdMY1KfCMZtYLnG0jtjJZPMnxFiv\noWJUnRh9QxfKULoQ7TE9yGE99f2SlR2QoAmdVnHk6lhV9C35inZY4e43UAYl\nUHde/Ffpn60TFY+oKywwKG5rEQwZ3CJr57OQrrI6g6/kHktlds38ZAkIANZB\ndfNyvvi8inehIVd+g0jbIVaqI20DWcGc1ynZiF3kOVoKm7aoySXFKxplRoF7\nNKEZQZOKURjdfGuT1MWipW9LcmGgDf1o7YYeex0qDc8BzS8jOVu0H/PlU0tI\namdfJ9m/OWsOrcUzkTmW2mvJz3ksBeGa2mvgxBybLOMml/YmFObyC9P08ShV\nDhHdWUXy7qyM2uNJsTsh/NtGVt5fZNkoS7OOsj66RNIZdUSW19pj7RQGOrtZ\ngZAO\r\n=ziss\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b9c449568992c954bd71de0ab44904544757545e", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/restore-cursor_4.0.0_1629746841602_0.3479880774893622", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "restore-cursor", "version": "5.0.0", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "restore-cursor@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dist": {"shasum": "7fbb65122d6c25489ac2fda910f2a0f063279834", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-Hp93f349DvdEqJFHiPyzNzVjT7lDDFtQJWRotQVQNl3CHr4j7oMHStQB9UH/CJSHTrevAZXFvomgzy8lXjrK0w==", "signatures": [{"sig": "MEUCIFQDkyq01m3VE68xY4spsToLXhtN6gDV1iWbLDtFiW9PAiEAx+dYrSAH/lCluzBlHEM687iGtGqLLVQxkYnFkowWaPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3080}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "cd47a38cc6e0d1cf9f2045c67d01650662e98408", "scripts": {"test": "xo && tsd && node --test"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/restore-cursor.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Gracefully restore the CLI cursor on exit", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"onetime": "^6.0.0", "signal-exit": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "tsd": "^0.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/restore-cursor_5.0.0_1695886462962_0.3947316525542488", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.1.0": {"name": "restore-cursor", "version": "5.1.0", "description": "Gracefully restore the CLI cursor on exit", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/restore-cursor.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && tsd && node --test"}, "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "dependencies": {"onetime": "^7.0.0", "signal-exit": "^4.1.0"}, "devDependencies": {"tsd": "^0.31.1", "xo": "^0.59.2"}, "_id": "restore-cursor@5.1.0", "gitHead": "58261091deb9769fe6a4b02feb4e857de9cb924c", "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==", "shasum": "0766d95699efacb14150993f55baf0953ea1ebe7", "tarball": "https://mirrors.cloud.tencent.com/npm/restore-cursor/-/restore-cursor-5.1.0.tgz", "fileCount": 5, "unpackedSize": 3129, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjTVh15wjgTQfpKcIA2w0KduJPS1HiIssWLruxw9T2ywIhAJztFpHI4X0oRAFX1SGQg3pLdH1RskVNcoNfow/Zrmmg"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/restore-cursor_5.1.0_1722002202014_0.6651924632559372"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-08-31T11:36:05.610Z", "modified": "2024-07-26T13:56:42.345Z", "1.0.0": "2014-08-31T11:36:05.610Z", "1.0.1": "2015-09-18T13:38:14.630Z", "2.0.0": "2017-01-09T19:17:11.062Z", "3.0.0": "2019-04-17T02:00:56.570Z", "3.1.0": "2019-04-26T06:29:35.632Z", "4.0.0": "2021-08-23T19:27:21.792Z", "5.0.0": "2023-09-28T07:34:23.222Z", "5.1.0": "2024-07-26T13:56:42.169Z"}, "users": {}, "dist-tags": {"latest": "5.1.0"}, "_rev": "5410-bad444e3f39e686f", "_id": "restore-cursor", "readme": "# restore-cursor\n\n> Gracefully restore the CLI cursor on exit\n\nPrevent the cursor you have hidden interactively from remaining hidden if the process crashes.\n\nIt does nothing if run in a non-TTY context.\n\n## Install\n\n```sh\nnpm install restore-cursor\n```\n\n## Usage\n\n```js\nimport restoreCursor from 'restore-cursor';\n\nrestoreCursor();\n```", "_attachments": {}}