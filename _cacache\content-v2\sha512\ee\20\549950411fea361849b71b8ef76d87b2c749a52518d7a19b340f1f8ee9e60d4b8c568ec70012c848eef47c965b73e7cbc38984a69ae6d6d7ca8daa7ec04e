{"name": "babel-register", "dist-tags": {"latest": "6.26.0", "next": "7.0.0-beta.3"}, "versions": {"6.1.4": {"name": "babel-register", "version": "6.1.4", "description": "babel require hook", "dist": {"shasum": "c5015a301df5dfd19c137d9827ba59215abc1583", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.1.4.tgz", "integrity": "sha512-be/C3+hIJurHoOghECxwIcYtQhodyqbeBnh5ALJMtqSyN98kUmqEIon4neHQX2yfI3NIp0n1NK+eSggG9stdrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHjI2KZU7P+b9LTXCmDz43TAvP37b0+T5lz+MPpu5c3GAiB9IcQ7uSsDYRhMGGyGuUQ3bklPOQg3ET4j4vALAfWacQ=="}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.1.4"}, "devDependencies": {"babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-register", "version": "6.1.17", "description": "babel require hook", "dist": {"shasum": "e63acb3dadfd34f03c6e8df6283e19b93c7abf2d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.1.17.tgz", "integrity": "sha512-1Uwob0s0ov36wAoTaLXij4lDa2IYuJQhiAIUUhKdZlPOUDFnMo6hP4O8I51JVCQrn/+JUt9Y28u0lAzp1fQeuw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYupLFy+wl05mO447bertcwVhfFuJTFjBRuIMFm9DZOQIhAMCasWNbiLy2Od0FOqNP2J9filIDfe1XbTyCT/nfToRr"}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.1.17", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-register", "version": "6.1.18", "description": "babel require hook", "dist": {"shasum": "19eb28bcbb56d161bc80ab71b5b126f38b97515f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.1.18.tgz", "integrity": "sha512-kAfC73y6PDsfvJyppbfEpAH8H+oZmqWzCuRXRsgPSfSkwIF7JC2yz5fqS6MMLNNSer5vfu0INW0QPAMWuuE+sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC0c6uZZ+lp597uMZOshlE+y05PplN6P38XyUJUUOb2SAiEA+wtEY3WHnKPY9hCgXzM+XFXADkW/hsHFHuoJB5Mk7Cs="}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.1.18", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-register", "version": "6.2.0", "description": "babel require hook", "dist": {"shasum": "181e3ddfbf97b30770d7de70e0a8f48560460e1d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.2.0.tgz", "integrity": "sha512-k13bGdBbuHHVH5k7TdJcPQq+hMjuC6DYRJvIip4TquuS+2Y0b32cu2r/+rO2jjldTTnKyQP1XspJIZ6XFwnrLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFn4aGYSyGb+Du7TYHZXdCx9o8u63i88N6/l7OW/Vo6VAiEAuTkicgIZO5wpHKFb1yIT71gaYM0BM3MXPVSrBnXrZrY="}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.2.0", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-register", "version": "6.2.4", "description": "babel require hook", "dist": {"shasum": "75dd4512c15479c69578507b3d62c9b5a284be31", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.2.4.tgz", "integrity": "sha512-JxeD3IDk6pi6YBguwh9azhadhzh4fEDh7VPV8uJ6u2WB0T4SKGlYcqedwFg49hQqilqgTa+BtgbDNJANnxOaOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFnm2U4UAfPrLNWbbwq912oyMoY7INXzKuUXNvXwJx02AiAlD4zxJ44mR1HKpQbQAFnBs4dbV7iqFEbayJmsharCFA=="}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.2.4", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.3.2": {"name": "babel-register", "version": "6.3.2", "description": "babel require hook", "dist": {"shasum": "442b6e03b69bd26d35149791426bb761c2fb45ca", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.3.2.tgz", "integrity": "sha512-CnKbJEidkm4zgOGm09K9wSFQ3eZRyOqEYh8y+Vd8W2KZ1TKP4ut/5OdeEi8RuHszWzrEZYBHUDI7cz8wh33BEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDycS7vEM+nC7+4xwLgGHcMQhevuilQzbog37ot21iIUQIhAL/ZayWapIG8hw01HFg303jQ5lwWWOOFMyl+JkqlJi0r"}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.3.2", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-register", "version": "6.3.13", "description": "babel require hook", "dist": {"shasum": "858b77cd7765aa5a82a33c26bbb1bc7b264713bf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.3.13.tgz", "integrity": "sha512-kWvY/3yHQVUBmQyQvS3AQOsAWg+kZF6IHMYntex3506x06+p0Jb+AnzLb6Ofkho2nWW+aq5FP0mZYneyC89xgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmG+hm7fWo6h3o8YNiB0VWc4K/2a0OOTwAsUqVFSuJfQIhAOaAUaP3/pWFL/r+WauAt2iLKgeudyE8fn7a6RfafGne"}]}, "directories": {}, "dependencies": {"core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "path-exists": "^1.0.0", "lodash": "^3.10.0", "source-map-support": "^0.2.10", "babel-core": "^6.3.13", "babel-runtime": "^5.0.0"}, "hasInstallScript": false}, "6.4.3": {"name": "babel-register", "version": "6.4.3", "description": "babel require hook", "dist": {"shasum": "fd6413fb1a9eeda20290ba7005bc5bb8cb20782e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.4.3.tgz", "integrity": "sha512-h+erIZCSVwTHZSQAuEY91jzYNkTZzENbcYShSI9M3smxwQj7lW8Z9vEwQDfNMB3LOrYEwbtggM2PKWy8DSykNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFXtuR37bfv7IHY/HtnhXb03DF2qaqlQLjvxFkOeaivgAiEAo55uEigj6L8rR5pvXLSDW5r83y4RaH/j1JvWV1fRqVo="}]}, "directories": {}, "dependencies": {"babel-core": "^6.3.13", "babel-runtime": "^5.0.0", "core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-register", "version": "6.5.0", "description": "babel require hook", "dist": {"shasum": "c91c7f21f1f0ef9a56e779b730da794bbc3fb1be", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.5.0.tgz", "integrity": "sha512-gth99dJ/e3HUtbZnfpY2x3NA7B2jb4vktaqPTjrdzW9+/iiWEhKY1aWKbC+4ZtqhK7hgzbOcLFNEpPS2HLCjgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3GrFKNRl62lkp/3o47nrI5AE5pHss/ca6t4b8NmmPQwIgVNj8yARHPxK824yxeHIIy9r4p4yLQcFNvaPPBz4+vi8="}]}, "directories": {}, "dependencies": {"babel-core": "^6.3.13", "babel-runtime": "^5.0.0", "core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-register", "version": "6.5.0-1", "description": "babel require hook", "dist": {"shasum": "3beb4648765cd36180ab635a0c92542d8ceaa54e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.5.0-1.tgz", "integrity": "sha512-x77r0A/RVEh/MocRAnRpO5+wTWOtEW77puENjA5Q5feitHX9Mdt+BNNEUIWoqlCl1b3NDDSLVQHwQ+qDa77g4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvWKzP39S/eqc5liEhdNMaCSq3w/MvYAOOHyVgOaaIxgIhAPb9/GhHEvV2gpvs6NXcthPx0rSFKxJ3vn3g2KvmnldP"}]}, "directories": {}, "dependencies": {"babel-core": "^6.5.0-1", "babel-runtime": "^5.0.0", "core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.5.1": {"name": "babel-register", "version": "6.5.1", "description": "babel require hook", "dist": {"shasum": "e895fe12bb02081f0a3019dbcaceacc51676aab9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.5.1.tgz", "integrity": "sha512-1+xaOWcWctEXsNJExwfSOgh/yi+07mwB6ocuYJQH5MGtVEHb0qiWuZDFXsIozVKGtkUVVLPa/ntEasMc8+xk7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0xxb43SZxrykfEEshCh0ZUVKqKlTqaupd0XCejjY6BwIgCGrrg8fqiTQTqZXpc4t2ui2dmR5PZ0D6Ey8KYddGN7E="}]}, "directories": {}, "dependencies": {"babel-core": "^6.5.1", "babel-runtime": "^5.0.0", "core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.5.2": {"name": "babel-register", "version": "6.5.2", "description": "babel require hook", "dist": {"shasum": "54983d575aba14205fa940fdfb2f5b5370833b5f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.5.2.tgz", "integrity": "sha512-8Xk8IsecA0KnMWtj6XVzs2lt6PKEwwivk9ZxzXQwcFa02ticREgNrJqDFyi4oaG06tTFllHvMK41TAFXH9Qa7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD40O7GF/VLjq+K6U6M4ol8WX9iQihdJEGitxEyuUeZ7QIgf9+t5AgIeKlzzKKvZN9wilT6uhYCVfSHbFvVYVDMo2w="}]}, "directories": {}, "dependencies": {"babel-core": "^6.5.2", "babel-runtime": "^5.0.0", "core-js": "^1.0.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-register", "version": "6.6.0", "description": "babel require hook", "dist": {"shasum": "ca849b592d92dbf31e026b5f8cf2fcf15a2a27a2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.6.0.tgz", "integrity": "sha512-MrYD6Lyc+C9U2j/KnA9EOTRqN2lwMvI3fgSLjvCTYVNbHtaDCeu+0FotH1JrqWC1Vp5QXzz7xFRdlz4GYMWzoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFlaN94tMh7dulG2O9GyDWhe7sqlqofnKwYcWYtICX4gIhAI6nWIbkQXm3SmfXMA/EWbRk26FRfTD0+xEl//X4/SMe"}]}, "directories": {}, "dependencies": {"babel-core": "^6.6.0", "babel-runtime": "^5.0.0", "core-js": "^2.1.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-register", "version": "6.6.5", "description": "babel require hook", "dist": {"shasum": "ae314c21e862e3673c8c3432465bac73a8f62701", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.6.5.tgz", "integrity": "sha512-1NlvcFX14glLYbM6+/aK9rUthPciySQRwejHjWPUtpcOEutNw/c7B2mdYbBnYdggAUluFrJ381Nxcd45qIXdDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbIuGF1dQ7ba6SSceMeugNl/aV1vPqUOP426skN3IXPgIgf2oP2CUOGCIvwtbnstI3VBFjXf3k8MeQvuO1CvWR6LI="}]}, "directories": {}, "dependencies": {"babel-core": "^6.6.5", "babel-runtime": "^5.0.0", "core-js": "^2.1.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.7.2": {"name": "babel-register", "version": "6.7.2", "description": "babel require hook", "dist": {"shasum": "4dec809ba2d4ccadd185efb2c0a3f560e1f6c8a0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.7.2.tgz", "integrity": "sha512-XEN/KE1RVVbu3HqDOgwlIXCqvakXkO1vi4434oimPpEJKWiflBvNRIQGOLpCe4BUev6wGe2jwZhsEPlSaFfuRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKf+nrgr+QKlfJtrINQZcwp5B8LPNIL7OV5YGILVru+AiBMF1NErmEcy+DfUdx8goB07/PQLEKYPQdy5zz0/VC+eA=="}]}, "directories": {}, "dependencies": {"babel-core": "^6.7.2", "babel-runtime": "^5.0.0", "core-js": "^2.1.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-register", "version": "6.8.0", "description": "babel require hook", "dist": {"shasum": "fa027da807a93b944329d1715719e72866d84cf1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.8.0.tgz", "integrity": "sha512-u2K54RxhPyuM7SELQtVgNZNggwog71xy/EKkiszMpTCUwsQSzWiNcHHeUp+xelPnTAWA5l/prT0sJhXVu9KSXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGYxCrODjp4Z2pl0qfgWilCkogbKZk0BVbiucPmcxHvwIhAKhdZcH9GaDodlE83/Fd1ZE4PGXEttKXt2p6CnbqWrx1"}]}, "directories": {}, "dependencies": {"babel-core": "^6.8.0", "babel-runtime": "^6.0.0", "core-js": "^2.1.0", "home-or-tmp": "^1.0.0", "lodash": "^3.10.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.9.0": {"name": "babel-register", "version": "6.9.0", "description": "babel require hook", "dist": {"shasum": "dd5f3572ef5bd4082ca05471e942e4a91b162ff0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.9.0.tgz", "integrity": "sha512-8D7EkmY4giQJnjGFbwB7dQJaPVf2f9TRMCsIu1AB1tKvwsVN/PMPUdbJAkKs09TjbO0l744XLp3UM+nAans6JA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5+pfGMFcqgIIU2zrzADcBH1YaVoXZtiOp5HTCNCu+LgIgXEHXPM1wA12DjYroBktV99hCGBLbmjMCd9VAgqtL1CY="}]}, "directories": {}, "dependencies": {"babel-core": "^6.9.0", "babel-runtime": "^6.9.0", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.11.5": {"name": "babel-register", "version": "6.11.5", "description": "babel require hook", "dist": {"shasum": "d40de86b99e8f105e3b4a58ab711f2143375c923", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.11.5.tgz", "integrity": "sha512-xAv/6aDv26ttACEsd5kUbs8S8aOeS11wlw1IfvQwcNkN8m2LtXH9ih3p51yQCFQTgQO8cO3cbffVS05SanYr7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFMOP+6oBVmXFMSa6YIB27tYUHea0/ZxTDPqg48xO3NBAiBi0sHnzYC9JlTWTJtxvz2azFtNICKHk7mBhojw1S9IOg=="}]}, "directories": {}, "dependencies": {"babel-core": "^6.9.0", "babel-runtime": "^6.9.0", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.11.6": {"name": "babel-register", "version": "6.11.6", "description": "babel require hook", "dist": {"shasum": "d235f6102b9350fce6384064e0c12d6892680c46", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.11.6.tgz", "integrity": "sha512-QWGZCi2pk/8+3yT90PxgkN+e0njKcRo5y6RDRGrheTK19Bi2lJg9oKj/T0dY39YbqHumfRpSQ/IJyBSH1DOU0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLYlXKyiMFzIC7M7np1y5NPSh8JLDGUvC8jaTpZzC2wwIgS1QG5IoA+Tx4HoD3GEqxfmCX9OnamrNRfYzZpKjOS0Y="}]}, "directories": {}, "dependencies": {"babel-core": "^6.9.0", "babel-runtime": "^6.11.6", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-register", "version": "6.14.0", "description": "babel require hook", "dist": {"shasum": "1261f94ee375808b564e24a800cc2179afbf0067", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.14.0.tgz", "integrity": "sha512-NFW56kXAQtkfrWknSKniMhWN+M1PvJa1k4Q+WZHZUJGYYUD5Ona+bWRVR3m8p4GHbfyTUL/OywFMNCNo3+1ZLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTTvKnMW3dbtJt+PxAJAFUOTfXawbAdXBS7DEXdCdqAwIgEvLuyp9eOoCdRJKAbOharn/Ihsgt1rD4aNXrSX/G1u8="}]}, "directories": {}, "dependencies": {"babel-core": "^6.14.0", "babel-runtime": "^6.11.6", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.2.10"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-register", "version": "6.16.0", "description": "babel require hook", "dist": {"shasum": "0d1c12aefb0bc839fbd5b3ecb4eca112b9d20ed1", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.16.0.tgz", "integrity": "sha512-eFbkZEo+nLMT2V83Rv3cxFjd5D/CzsQk9iXfhYSdHAmL8o7SLbYgxCQaUx55vMSBxjrWop44H5NgfK7+FrC3Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrKayTZEAsg6RrZTikSSg+ABTI6Pa0mcTt1UsICWbfJwIhAJj/d0mZBYHwkvtkcHiZqVfM3AYSV6lLS8PiPuKv+e41"}]}, "directories": {}, "dependencies": {"babel-core": "^6.16.0", "babel-runtime": "^6.11.6", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.4.2"}, "hasInstallScript": false}, "6.16.3": {"name": "babel-register", "version": "6.16.3", "description": "babel require hook", "dist": {"shasum": "7b0c0ca7bfdeb9188ba4c27e5fcb7599a497c624", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.16.3.tgz", "integrity": "sha512-NzOe2zCogYL/e/2tSSneOGQwiMjNT2dKNyrF5R6h0mFSkC7wftG7nPDd5GHrRaPGjhyzOXQx3L8R7qtPftU2zQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6EH38M/FqlwsUe7HItjFDKLkVkkJrvK/ap+sGCaCkMQIhAK1rnE3XWIL6sU8j8UPFUvd3nYCYO4lvMuem7vGywLUo"}]}, "directories": {}, "dependencies": {"babel-core": "^6.16.0", "babel-runtime": "^6.11.6", "core-js": "^2.4.0", "home-or-tmp": "^1.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "path-exists": "^1.0.0", "source-map-support": "^0.4.2"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-register", "version": "6.18.0", "description": "babel require hook", "dist": {"shasum": "892e2e03865078dd90ad2c715111ec4449b32a68", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.18.0.tgz", "integrity": "sha512-2wxuq90cFwZ9vqoWUgw3KMfI+z9sSdR8PBNAAErYTyHwOtUkQOoAtq0rw7RDFm/sqrCj22dgvAviZoD5l4Z8MQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID42tCC6cYgC+DiFm4X+3GdWWGsUbR3xOEQIoPuCMP7kAiEAqagKUN+WnhwFK9p4jkzU3jLNFnhxK4uioybB3L248Ts="}]}, "directories": {}, "dependencies": {"babel-core": "^6.18.0", "babel-runtime": "^6.11.6", "core-js": "^2.4.0", "home-or-tmp": "^2.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-register", "version": "6.22.0", "description": "babel require hook", "dist": {"shasum": "a61dd83975f9ca4a9e7d6eff3059494cd5ea4c63", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.22.0.tgz", "integrity": "sha512-ALVfGD08/OM+uLr8a6MZznowO5ayhIDsJeKiSnrlpggThUmq4j3f5Dpq8IamQDfBemzzSqhyfbHyg5ATyEMgZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGk59kha0mQ3qOCx3Zk8pkML4jChmijSmk7z3ZmRI2qsAiAK07txJt14J2MqDbxzCojSUxl6o+uwiZG/KWTyA2jOaA=="}]}, "directories": {}, "dependencies": {"babel-core": "^6.22.0", "babel-runtime": "^6.22.0", "core-js": "^2.4.0", "home-or-tmp": "^2.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-register", "version": "6.23.0", "description": "babel require hook", "dist": {"shasum": "c9aa3d4cca94b51da34826c4a0f9e08145d74ff3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.23.0.tgz", "integrity": "sha512-2Jd1fAhdXCB2UgGD1AzXbqDVRlvJrQ3K5ACzT81iO82yRA0oBJ8dLT7dNxCcd7tiuZt2R5uRN/8jpobDDpa5Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGy5QtDkAm4JfOk7A/FZFEAZVxbecrKXrItdiBPLPR34AiEA89EaJ4d8t9Oqw29Vd423XyUr4ukEhlEBaJ6lAkgMdqc="}]}, "directories": {}, "dependencies": {"babel-core": "^6.23.0", "babel-runtime": "^6.22.0", "core-js": "^2.4.0", "home-or-tmp": "^2.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-register", "version": "7.0.0-alpha.1", "description": "babel require hook", "dist": {"shasum": "d4f8600e773bbe9ad47c2e4f54c0209dff318cb0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.1.tgz", "integrity": "sha512-bJaiJVB+qSzWRFnBmOVkasZ1ADYZA4EzDsscb5OIGrNcSopewzR4G+ZXngQA3SoTToXuZjSWvh55Thl/4AWTcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEeIbRUfy1qg/cgHG5SIunthvdMe76MooLTk5uJ13nNIAiBgNv56qA2zLOGWxxYemwC1ksSFA+tmQj902ljSiGN4ng=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.1", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.2": {"name": "babel-register", "version": "7.0.0-alpha.2", "description": "babel require hook", "dist": {"shasum": "020a7ba7384792ca3ee8a44c5c14bf38501b4edb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.2.tgz", "integrity": "sha512-20TpYHH8elA+MX/v/7tDWsjp2MEiezFz9fZqQSCi0v838xPb7sxzHJL4JbED54z8jaHomLVrQVtlVgfZRUbpuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC9eJHOTzwcXWgp+C5PdDvLgi9SWenGvHpZLb7SuXM8nAiBs2jirj8ADLcUgpz51FHs2ePSpH1uKQRVp/p2R6euKQQ=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.2", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "6.24.0": {"name": "babel-register", "version": "6.24.0", "description": "babel require hook", "dist": {"shasum": "5e89f8463ba9970356d02eb07dabe3308b080cfd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.24.0.tgz", "integrity": "sha512-1gJ7tNPw1lx7ShOtVVrXu4VxhwIxKd9wOS3mQ3QCwsYthow0MIcVyHd5CTMYmZtk3Wlmol4kD+Y3ciLJVMkkwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFd+R1I9tyIldGUa43aWGs3GGK4+EyB6pRrshQ6wdFJOAiAMqziVyVjuvdTDVqzIsXGLe3GrnaojQOoTdn8FgC8g3w=="}]}, "directories": {}, "dependencies": {"babel-core": "^6.24.0", "babel-runtime": "^6.22.0", "core-js": "^2.4.0", "home-or-tmp": "^2.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-register", "version": "7.0.0-alpha.3", "description": "babel require hook", "dist": {"shasum": "ecdb4abcf3cfe318984f202db3cab0fb2fb0c362", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.3.tgz", "integrity": "sha512-5ZwlrXnX1/OwpNpcA+KajnltLKNchLXLXDRVfzURpn309a7/Vu5JqiZHGAOyqFL+hgObAXjGshP6bIhefVGAMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFAUB0v2NI9TzNHpn5DB0R5zHDGn5EmLByOWwiKt3dTBAiAzOpOZwz/wrCn7fsGu40EMuHkQQnvJa30KEghtNKsNyg=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.3", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.4": {"name": "babel-register", "version": "7.0.0-alpha.4", "description": "babel require hook", "dist": {"shasum": "854226be7b64e13bb5cf070decc66c064ead70bf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.4.tgz", "integrity": "sha512-m4aW6AGjhJcXLseSnmFyF+u044zZ76ct9o4ZSa9ACZJGxyZLx1SmpZo4Qr3rmTEwJBNJ9Ue5wLv4HiRBRovJSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDR8CwX71c/Oew77LMq0mQGL0cRHXFfks7NVbSouYFwmAiA81GBf+7oM+guLYAqHr+rhiiu07k4CWRGm1zPGy9kqLA=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.3", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.6": {"name": "babel-register", "version": "7.0.0-alpha.6", "description": "babel require hook", "dist": {"shasum": "adb337fdfca6d74c90d51232b5b3d8f532ca91bc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.6.tgz", "integrity": "sha512-ur9sER1tQBYWkoPjjjQChSFmuB5S3KIfymTuANEQ7Nngb6FNdNcCsc5I0aeT+0hVDaoClyPrx99AhCfByXERPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvdmfneE8Qyh9DrrxsNLmJtETuvn9adoLip88riB8YHwIhAI26DdHVJlORTxwM151rg94Pqrdy5/BJbTa8Ca4pEpHO"}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.6", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-register", "version": "7.0.0-alpha.7", "description": "babel require hook", "dist": {"shasum": "e3f1de53740f270052088f6fbbcd1b5ce3f0cdee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.7.tgz", "integrity": "sha512-sxv/O9k/3AOwMcRUV+xodI3g0VVHBOA5cANA7t/XUSlEo7TcO3ifHYZ9FjreF0lOdY1+IY+vkBZb8c4ZbdjoIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDAPcQlhG8E/0xI0XUKcyhSoeqQzMDZNHUCQVcKDY1Q0AiEAuOy9r9dUPWVEDji10NGTSwly2hWVlbwAtuQyB5EO0ww="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.7", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-register", "version": "6.24.1", "description": "babel require hook", "dist": {"shasum": "7e10e13a2f71065bdfad5a1787ba45bca6ded75f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.24.1.tgz", "integrity": "sha512-mr00+i4WOTiZoG1+CYfemP6mW9ym7Hw97EtEx60Z0/uTADXXPjVc9D4xXYi96zyCZMBALF7EjC0cVAS+63updw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkLBNtof0A6vvolexRg1c57gF6IG8jKmHBlntEmx6e0wIgFd22eUdIPAhhnA4qNiJWEb9zO3/DZsZh1tCbP7If2Ig="}]}, "directories": {}, "dependencies": {"babel-core": "^6.24.1", "babel-runtime": "^6.22.0", "core-js": "^2.4.0", "home-or-tmp": "^2.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-register", "version": "7.0.0-alpha.8", "description": "babel require hook", "dist": {"shasum": "cfc0f829447a78a9ead75cd1dc02e0f7a3a56ad3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.8.tgz", "integrity": "sha512-Fdi3<PERSON>ajzob8ssLOHTu4KDJrMvG/J9Ec8RNuRcRzQANRaR3sFLTOD5mt9wuboXwM2n4znlibG3dQ2nLVo659g2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHKqaWie4teFmgkduHvhmeNjOWu7JWbl0k1+VtozjdaQAiBocxfof4uiYdvPCoJe1+jL3DDnRts+/ULN4RJIL2t2/A=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.8", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-register", "version": "7.0.0-alpha.9", "description": "babel require hook", "dist": {"shasum": "580216ae64b58f009273ac413c87eb9ffe97ead9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.9.tgz", "integrity": "sha512-SrKTOsr8caKXL1SBNtwp+0YHSYWN+XGvHp3hsZwSQmdR4ECVB3gy3sEQVa3jlR3bK3VGC4tEfwVravZhqOjanA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0YdoRLQua5YPosi3DsHHwJKgtAaI+PoEsblDV98b9aAIgV4xYlvrjWCvkYbpDfdsXvof55OwsT4ZzGrCcl1ztHAg="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.9", "core-js": "^2.4.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-register", "version": "7.0.0-alpha.10", "description": "babel require hook", "dist": {"shasum": "cb94a7460e59e8727d607ec15dfafb3290466c92", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.10.tgz", "integrity": "sha512-i4KEdOgsVX3r0ZTxf1n6yKzJMMgSPGNEWmMDCWo5JAfSDNKlZ+BxMOOT6IQqmhbBNNWLQoVwm4SMxa0ZBirgdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH6flUW9Mv9wPRx4Z5FImlnqw00WZMh1exv+/7OdxhA9AiEAnU+Ma2cxHCwggZxGuGzxRQBNTDS2c7vKL1iW2mCJ7rg="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.10", "core-js": "^2.4.0", "find-cache-dir": "^0.1.1", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^1.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-register", "version": "7.0.0-alpha.11", "description": "babel require hook", "dist": {"integrity": "sha512-6pm15xDjWDj4dPTgGSL2rWU8SQaJHbEirs+kSSU9ODXsZGrbaViuqUVQ4xT5RGqmiNaX1jOskQFfndivcfHerA==", "shasum": "a7c5ba8f105b5f1d1e651df112e73b78957d647d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDe+2b7sGbmBiqBZFgPNB6+rXDI6E6L2EW9uvk3QofgwwIgaoyS3zl3rrOnIZNzp+3M2sJpKhiIaUWVq6X83ZlM2ws="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.11", "core-js": "^2.4.0", "find-cache-dir": "^0.1.1", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^1.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-register", "version": "7.0.0-alpha.12", "description": "babel require hook", "dist": {"integrity": "sha512-qwTKZZtGZdo4tyWP8ep0I5pAxOfBU/4STfKPODK/si/AcIugpEQx2RK4YXcGuPIA7w1IwoSVhBxC7ESe+pWxVA==", "shasum": "d7fbae2981567b360607bb31783906d692cd7f6f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvqfvwPBKU8JffJ4SDuq4KjbhQiEmgx/QqwqI8zM4mzAiEAp/KtR/cbX6DbElAZqR6vBDREqBvZd0F0F2E7myZvVMM="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.12", "core-js": "^2.4.0", "find-cache-dir": "^0.1.1", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^1.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-register", "version": "7.0.0-alpha.14", "description": "babel require hook", "dist": {"shasum": "cbdd988e27ea7025caf18a734f45b79aa8a9f710", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.14.tgz", "integrity": "sha512-Q6FBZ44D8IDy9/3d6w8gJga6jVI/qzW7jj7uLjM4NsMrkIO6fvrJcpeToDyRavXGzbXQ1eeMnCKb1NYWeDYKbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGj5M/CFrnyDZBVqVqexdsnfpqAacaimQQMwGmJySUScAiAL+LeydoXpdGTKBoeIQQ7MbXslduIflMdFq4tFauuuRw=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.14", "core-js": "^2.4.0", "find-cache-dir": "^0.1.1", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^1.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-register", "version": "7.0.0-alpha.15", "description": "babel require hook", "dist": {"shasum": "2f5cc86c29eab9fbb003aa88426d5e279e053f38", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.15.tgz", "integrity": "sha512-EIHl1fUUhaE/igCKCb6b/T4lp15eTF39e/dCzQkdlH3JVscRuMmy0aK4tyhyakYRh1R6VtKWBSMCo7IJEvZhmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFFiBFIxVuEsnXiW1b03tRoUumcHlZbOJ6/dJ4nhW4aPAiEAvHsZzPc+nHZXMSBwvuuFthytGjwk5hLCUIXbq03dVdU="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.15", "core-js": "^2.4.0", "find-cache-dir": "^0.1.1", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^1.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-register", "version": "7.0.0-alpha.16", "description": "babel require hook", "dist": {"shasum": "20fdabecd8935e691b312007d24d2460cc43006b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.16.tgz", "integrity": "sha512-eudsVXh9BUf+j6XebrKdhwAt1H/tj2jmWyDaWtplw3trWNgG9k6B2KGqQYbkRFS78M6jWJ1DsHe1nkY3QjAgeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGa8oarTghQhctSVc36JomUd7VJ0MDdey9L13HSHThEtAiEAzR9KK2uoMvP/KQ3BID+HGabRqxPut2w17Gj9wKku2qM="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.16", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-register", "version": "7.0.0-alpha.17", "description": "babel require hook", "dist": {"shasum": "bb0cfed4a4de4386f181188694e03ef5c1dcf146", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.17.tgz", "integrity": "sha512-kCxoOytDeIyDDc7U+ETwNchCduf4f2bbauNkiTcKy81FiiHGwmlTNfAcQVwZTkpyNJanCJwNhpi91BmWOWV4+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDfUsxVNAWHuWUMHFVJDwZzgBBl9eSwR4qnGWm9K1erAiEAtsC2Us0ifhJi/zqSpm2tBuAqwazagetfJu5Zna2tFH8="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.17", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-register", "version": "7.0.0-alpha.18", "description": "babel require hook", "dist": {"integrity": "sha512-KD/CipuYoKiXhYf2oueCsefjBIuwy0q/ykcsbFUffBtDnhZYKUcn7HDNvkyqhGOsySmMXD5qrFYe0AiYKaYWrw==", "shasum": "cd06f208f0f26adc4fc9c71f4a8e22df971dd6f4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtZiwWCrPSnsl34mMqVR5CpXItL5cFvG38GAemgHTJVAiAcgp5C7fgqNcBrbNdZd+nYF0wmmiR7HapQKeL07OiMPA=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.18", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-register", "version": "7.0.0-alpha.19", "description": "babel require hook", "dist": {"integrity": "sha512-LcnfZSI6dk0NIQ2DxvV6xDdu60DrZytJ9d4G45vgWSl93stppVOnSCyditF3Y1yTWT+bXyjhUwJDtzjhMyG2Dg==", "shasum": "1f743e2949fe1c3fcc3fa63f205917799abef27e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3epKHrbp9JHvMsGz7ThKveQUKwuWUd5HjureK8xBisQIhAIW1aDbgTan0jtJazykmvKw93Jp5Gxsw+OfH2ISTI/G6"}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.19", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"decache": "^4.1.0", "default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "6.26.0": {"name": "babel-register", "version": "6.26.0", "description": "babel require hook", "dist": {"shasum": "6ed021173e2fcb486d7acb45c6009a856f647071", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-6.26.0.tgz", "integrity": "sha512-veliHlHX06wjaeY8xNITbveXSiI+ASFnOqvne/LaIJIqOWi2Ogmj91KOugEz/hoh/fwMhXNBJPCv8Xaz5CyM4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEARsI3UyvMyAQs5J38YlsSziWPsyu2tqH0l2UCvuRd9AiAcIo3xvUKE7/RbwyF2Q+h//h1aEdkKZ+SlS8e2lJreVA=="}]}, "directories": {}, "dependencies": {"babel-core": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^2.5.0", "home-or-tmp": "^2.0.0", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "source-map-support": "^0.4.15"}, "devDependencies": {"decache": "^4.1.0"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-register", "version": "7.0.0-alpha.20", "description": "babel require hook", "dist": {"integrity": "sha512-UPffU2BhkVLATAnT4B+rJbGXwlrHFiTiEGcfoWuyxJU+sg+nioY4Io9kCq2ghF3VNsGFie+DcWpb5nSCf2AwTQ==", "shasum": "bc062f5f02a4f06b39868312172732a5b9c35e27", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7rb18xJnigCLmJvpt1JIp8/L3rEafR+YwFLKD5/iuVQIhAOl/v3L6Am/2Na9rbkLwLVK5VQBAAZk3jw7aw/MAUxP9"}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-alpha.20", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-register", "version": "7.0.0-beta.0", "description": "babel require hook", "dist": {"integrity": "sha512-byvdj3KEMdGIbtQ0pSNHN8TeXsF8y52cD7ITSbiXh4weVHPBd8BFXgFKOc5QsdV85TlPdZrEHMQBpJtGaQYH/Q==", "shasum": "fd4301ce61afbc99ad42759d27a428c3feb1af89", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXRShhYmIvsmqsKEtlO07IC84zof8on3eVONC0W9z6/AiA/xj+RahhkfPpCOHeXGDfNygTZxr9+3Lf72JbhE0cp4Q=="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-beta.0", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-register", "version": "7.0.0-beta.1", "description": "babel require hook", "dist": {"integrity": "sha512-03WkZ5tQRBaySuudPwxJ6ibN9NJi+NVul8P3QtqL5D55YEzUr3gKO4HpiKafKJDRWQ2XvF4YSjyyzeA99u598w==", "shasum": "d54872b4065c726c6c7a419f70af119d4c6b761a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtxAEBir6O3D5dDnJSOkKZDSnDK35tUe3+L00P7bVUiAiEA23aTNVLZuwR+CrDaBpapX+iogLhjrIUFdF50amLwSF4="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-beta.1", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-register", "version": "7.0.0-beta.2", "description": "babel require hook", "dist": {"integrity": "sha512-d8GTqgcCnsbdbOvsz7xTGrP3Y8ekTfZLsoMB8Lu8TzLeqxyUiUsqvK4TRPoPrT/vHWDlfnfWOYcB4Dvg9v8lGw==", "shasum": "735423f7c4a9d8df8786b32962168d51e2973a09", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG5jrYvLJ7geQZhYqycD3crP7pkzh295fjRnrjIJBmSbAiEAubEdb7cPJKOJ1wFG5h25eeH12+Ncnz1CGSZlJx0CuB0="}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-beta.2", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"default-require-extensions": "^2.0.0"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-register", "version": "7.0.0-beta.3", "description": "babel require hook", "dist": {"integrity": "sha512-9H/TMXFuG7iDGEs/wakN9lFLmfpqNGg8YHzZ5Tah5UER7veLIPEspAwxKsGNA/4+X4z30tyBNoBhW6JkC9AKFQ==", "shasum": "9543472da6f016b39dddc8166f21841e2445e0c2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-register/-/babel-register-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh/7XYNARSUYlch9he8GMfT4Gs+ORuzu46A2thJyPo3wIhANd9Psk+5V04H25y3S/o1Se9RSWFCIzOEdxuIW1bPLJH"}]}, "directories": {}, "dependencies": {"babel-core": "7.0.0-beta.3", "core-js": "^2.4.0", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.2.0", "mkdirp": "^0.5.1", "pirates": "^3.0.1", "source-map-support": "^0.4.2"}, "devDependencies": {"default-require-extensions": "^2.0.0"}, "hasInstallScript": false}}, "modified": "2023-07-21T15:42:41.164Z", "time": {"modified": "2023-07-21T15:42:41.164Z", "created": "2015-11-11T10:45:52.301Z", "6.1.4": "2015-11-11T10:45:52.301Z", "6.1.17": "2015-11-12T21:43:58.825Z", "6.1.18": "2015-11-12T21:53:06.709Z", "6.2.0": "2015-11-19T04:34:35.277Z", "6.2.4": "2015-11-25T03:15:58.225Z", "6.3.2": "2015-12-04T03:48:21.687Z", "6.3.13": "2015-12-04T12:01:23.509Z", "6.4.3": "2016-01-14T05:56:41.851Z", "6.5.0": "2016-02-07T00:08:27.640Z", "6.5.0-1": "2016-02-07T02:41:50.099Z", "6.5.1": "2016-02-08T02:18:16.777Z", "6.5.2": "2016-02-12T16:30:25.350Z", "6.6.0": "2016-02-29T21:13:09.009Z", "6.6.5": "2016-03-04T23:17:15.410Z", "6.7.2": "2016-03-10T22:41:32.919Z", "6.8.0": "2016-05-02T23:45:24.286Z", "6.9.0": "2016-05-17T18:49:49.251Z", "6.11.5": "2016-07-23T18:09:39.679Z", "6.11.6": "2016-07-26T22:12:29.053Z", "6.14.0": "2016-08-24T23:41:02.224Z", "6.16.0": "2016-09-28T19:39:05.725Z", "6.16.3": "2016-09-29T15:46:57.290Z", "6.18.0": "2016-10-24T21:19:12.542Z", "6.22.0": "2017-01-20T00:34:27.744Z", "6.23.0": "2017-02-14T01:14:36.481Z", "7.0.0-alpha.1": "2017-03-02T21:06:19.623Z", "7.0.0-alpha.2": "2017-03-08T18:08:49.245Z", "6.24.0": "2017-03-13T02:18:17.261Z", "7.0.0-alpha.3": "2017-03-23T19:50:13.144Z", "7.0.0-alpha.4": "2017-03-23T22:06:50.036Z", "7.0.0-alpha.6": "2017-03-27T19:09:41.506Z", "7.0.0-alpha.7": "2017-04-05T21:14:39.035Z", "6.24.1": "2017-04-07T15:19:51.098Z", "7.0.0-alpha.8": "2017-04-17T19:13:29.575Z", "7.0.0-alpha.9": "2017-04-18T14:42:34.834Z", "7.0.0-alpha.10": "2017-05-25T19:17:58.361Z", "7.0.0-alpha.11": "2017-05-31T20:44:04.529Z", "7.0.0-alpha.12": "2017-05-31T21:12:19.110Z", "7.0.0-alpha.14": "2017-07-12T02:54:28.074Z", "7.0.0-alpha.15": "2017-07-12T03:36:45.191Z", "7.0.0-alpha.16": "2017-07-25T21:18:37.462Z", "7.0.0-alpha.17": "2017-07-26T12:40:10.577Z", "7.0.0-alpha.18": "2017-08-03T22:21:38.902Z", "7.0.0-alpha.19": "2017-08-07T22:22:22.221Z", "6.26.0": "2017-08-16T15:54:23.130Z", "7.0.0-alpha.20": "2017-08-30T19:04:50.348Z", "7.0.0-beta.0": "2017-09-12T03:03:10.842Z", "7.0.0-beta.1": "2017-09-19T20:24:59.828Z", "7.0.0-beta.2": "2017-09-26T15:16:22.828Z", "7.0.0-beta.3": "2017-10-15T13:12:46.077Z"}}