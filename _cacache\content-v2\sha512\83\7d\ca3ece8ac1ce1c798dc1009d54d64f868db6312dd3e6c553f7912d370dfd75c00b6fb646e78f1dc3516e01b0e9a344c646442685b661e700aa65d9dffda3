{"name": "@cloudbase/cloud-api", "versions": {"0.1.0": {"name": "@cloudbase/cloud-api", "version": "0.1.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.0", "maintainers": [{"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "d2e89f12a3ac01a379988eb3eb0b80640b5e65d2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.0.tgz", "fileCount": 20, "integrity": "sha512-Q0Jwto2TvsAZlRaFb+oNmEw3I5ByFP/V/yJLYqVOcvql4Ib3FZfCy///3RxOfb5qa2UPRu/Ao89wiKDmPyEqsQ==", "signatures": [{"sig": "MEYCIQDJdrMpCCdxP0FDqCbWCMVnXO3nQI90Fh7Lm7lg01joegIhAKThJd5hNTagece1+wiC+TU4pKuhmeIuVaxpm+Qf7huU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefV56CRA9TVsSAnZWagAA3MUP/3ynJr67NinsWjh7kcQT\nylYXJWUaU1LHBqRzsE24xij2z82j6lXQ4QrJVYrJyDox3s1S/IvAphx66Zw6\neE28UmcJ0CXck7LGyE8wYGZ945chRDfJR4Ho8+gDlA8g2YxBuPY3Z90Ary5m\n+wOaAA3+tUFybOGhORSQky+tRdGzb4Y8C65/u2jJ4aNudeXxiAEvkvuuJE0z\nb1g/gvDOupb0eYaExpdyL6M9cE1mfN0b1D9UpvvYHc2I6Gz/WQD8cUuVFL0v\nAtOQuOSJINxQ9WqzSVu7K1f4JqPX0GUKpQbYfvUVoUn03F+VW8RAMtOnLw+6\nOU+L8RLx1S81ehzTycs1yfEE443Kfktaoqd41e/PnORA2WxU/kRK5zkNooYO\n2OM+0LLNUk2C18rRCyI79yBp1D/VSlLxKyIYAt3MaTYNyRuMOe20LeAk563+\nQ7Bpr33yUaQ5GTt74bMMr+vnG6Dlmul4+YgGqWG/IsuQ7DYQMxWz3sustbaH\nCIpmGmvIzTdw0SA8+0xHhYFrxBIqoyrtmqMbIMJHtNM5eXRZK4uYJ5STgGN1\nngGU21N1WnRlwwz7Qy+9Dk0LJ+YzuizS+RxiKzfjXEp7KnZGYFptwFBEFVmk\nJdDgreVqrpYY3otjHG5azmdbbnVnUZMWmuYXtWzK6wLiPyGAXI5t3eqfwzAM\njGXI\r\n=lk0L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d820306b4ec5c9cbeab4ef21f472254b2084f3e8", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.0_1585274490102_0.7638985868204833", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.1": {"name": "@cloudbase/cloud-api", "version": "0.1.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.1", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "f6ef31f24d3b82ee47b1288c588e35f61cd5c435", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.1.tgz", "fileCount": 20, "integrity": "sha512-OLgX9aTs3zp3sY5gJrIg6acCXsHEObmBqv+Yf7ckSbJSaHs1F/rteLqarhnArIFu/A1skasxpBEOSfweQiYQUw==", "signatures": [{"sig": "MEUCIQD6ijZ3uf/evedZEhxTVK/vSxWnGVzF+5I2lqDt339FZAIgd9g+sFcQxoj+B3u2QTEZXoqIW6BjqCGNit/7CRtV2hA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejHGwCRA9TVsSAnZWagAA+BMP/iN9Ggf592bhqQcLW+nU\nBMjgifLYgqIG4FfRuMwFEpD/0rrAl36bXyqTzQ/PcPuTjkMI8VCT1HH8XdBO\nwfAzh6aMAr+fKxqrHTnAJEz0l5KQjK0Vv2zjQh9gGDb6RrCMtv9Rny/wee/b\n7nsMHrv/g8k78Am4HlkjG9p6UKag+OU297p2eX5HbhVOisdX0N10bxomrPnH\nVLbLDOwRMrB8jbcuN/KK4hroCA/WwhBU8Aaj2eLo5J9fhmQlgK05Ov6RzuSr\nvrsX9vMkeBFjmneLGnrAt40wtcnkuJ9+lEmdG3RDQmX9zRDQ2wvNdYlt4GyO\np3xK8O4dq/VxO17MzgfJc+U+nSU6NuLfTo1H2MtRlzWf6fXdSPL7WOGESyl/\nTXd+lo+hwmpUEHHwKTVv/ovoTY4k+1N6Yg5wan3At0E6kxy8BWjAqziJ0/Do\nXTBlAL1m/a5LMj1+oI7N1faN+oCR3Ckac0UseQ0dakySE4qSeWkHeDwuKS4p\niJGY4VukPt6ihEDuTG5qoP+bISSbAMr6C3Zs/hzllKAAnIfVK0Poeew3Kd1w\nfnqrX1IvHZM8iuy3zWCDpaWuk2eh2SdRYFNQ2dB5h+gW7iGlezVRu9d0JN/v\nhLxccpDmFn8tXUpAA0Bnhnfe6lshe+vAN43eEli+HgoNhdxp2wa0QSWKXvWn\nsIN/\r\n=Ox8b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "94410c35bccc8998945066da22e0a444941c7ca8", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.1_1586262447774_0.9501724186573726", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.2": {"name": "@cloudbase/cloud-api", "version": "0.1.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.2", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "8794ab0f094ab2da8459e73fb6b0898b7ff28f19", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.2.tgz", "fileCount": 20, "integrity": "sha512-FaPP1+I/YAj2i/G8k4dfar0FhNcpTEWO2h+rpIrHKQW1z4P1ulUg5rQS8SICqyELi5Wz8mVC0mu6tFhG1Ws3QQ==", "signatures": [{"sig": "MEYCIQC/arcYhdCbqDzExPmmQYdQZ8hUtTtj7WqVf3W/sKYdYQIhALacMfxdmUjDAvzQ1wLZGfedYic3eq1/+0mUfiXA7isf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejHKdCRA9TVsSAnZWagAAXsIP/0kG7zVp+sfpJHkJTi9t\nlyzqU0+SQ3kalZXoG/HZR06Grm1+SN4Q5Q4Wx3XDeMTM2KgiwFpmnzVbmFsD\nAhHJnUG4tzYkU+ejlhZHPdWD7XXt0XWSYiQC+BevJL2oGyNP51oiZXFermE+\noSNj+to8hM9kz6pCy809/tszJI0c153dgoci5M6VkK2XD0Yht3RMQXN/plZu\nVwkhbkYDj3d4Xji4U40ivO8WBjv5ZejFdRjbl7uMeHGrhY4FZGU6qT9iUAE0\n7U6yRkewREfUt2Fn3QMPeLAiEno76IRS/m0rIv+BgfrkWTKkKoesvk2ZxnP3\nsdw0DTOcDxmoXz5rSF5La5p+RByubZB5uCuE1np7x4EGHQjk4KFn8jQ1x/mY\noJrVTJH0nXoxlhSs5PNHY6AEp2DM/4aT80FcdEK8TggOSVx7tJ17uH2eXGqa\niNd1PYn0nT+dc4ZSex24zfkJRdPtazguRtktGfFyrFH6HXN7QFJnrGKvuP0r\nD/+/DJ/yvYgHKh89hBKrI/4E01uJXdieR9ANZd6EF3Hm8DDZlYXQuCC8JUgl\nkwmvCtjdHA7Y5KZ1YO0skM7JkJq/0/tP4RRulvAjJ0l6LUPNGmsSiiCUCite\n06OY/w9V8gx6bWzydeMFJ57stjJyCMes7NT2ntSVsmfYdeYvjx+9KaT3IesX\n3b9V\r\n=Yzh4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9d37ab0c8d11bba573b08e9609932a99940d7947", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.2_1586262684587_0.9229943308253126", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.3": {"name": "@cloudbase/cloud-api", "version": "0.1.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.3", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "0cefc93ba1fc3462577d3120ff54dd2343d1751b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.3.tgz", "fileCount": 21, "integrity": "sha512-5BvCeUhwvtDJpOsdBoWLxrR5X7WqVkO0cYrxVrqZL6/2DFSHhTIoLtuGSLS5TDQ2mF3zZj6inLsscAYqLxXp4Q==", "signatures": [{"sig": "MEYCIQCcTWsOh1+4N4XDSCr8T8/OFsRDO5gGJ/Yzp8a4cyh54QIhAIRJEsb4QjpJqaxJn6eJPNGo7E108QBJl1xAL3A+9Rjo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelBbTCRA9TVsSAnZWagAAYI8QAIxj5y0tN2hccdvjcPXE\nbB/6lhXbUfeInvwNEPVHheKUm4KbyFR/XlPdRAZY5kYXJ4CQeC6Wajy77Hxg\nnk5Cb1jux5atrexuxMVmRcYtDMQcA1C06KOEFf3XP+aGAWrxjjba9pybEzMV\nmJk6HYtYm3Xx5ZaLWU0LuZ4nJpZDWrm3UEEO1CnOYtxrMri8Oks0JsIevpFy\nLvqBR1ed0cHFAJT68obehGGZh/BtOqv9i7wO7304XtoS034wX8r9R1USdlv1\nqtcKnyTeiwZOKlDGRZSeUupI4ntrbym7AI7z3M6tToeQdukbsSlGoAxxPSbp\nuSyM1rKm7M718OpwL/HW4uoGt+41+uPHesefVGBKV5DGhJzePdR+UT3gZVEe\n+P+sP5Zejp1sW5qQn397GRMJVsQEEluTIk/NXxLe4Fsv2/IyjJc0xZ92HFY5\nay+X6D6M1x2HbREfWwzDISqD1z5lCxUjPVP3wcWL2//rRJyLXvOHx5uumaSC\nrAVe1hRjDfE8X4Zn84ng4KLFQT4JHj4+N5g+FNAPSq0R7HRum4jCKMbDmtSo\nFmyBxa7P/+ZWMz/4RlhRfXZT70h0sWKQemGZSIrJ6WvC6aI6yFF24TzeMRvq\n52TWtNUyICvvoBhRrQSaDJ0SMs201oA3rXkG8WSQ1LEGjgO6Ilj44g/Mgv3I\nExte\r\n=nuaQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d29bc6e1389e87a0b86c779088079034ef9869f", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.3_1586763475333_0.9730858279174279", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.4": {"name": "@cloudbase/cloud-api", "version": "0.1.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.4", "maintainers": [{"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "089eb1889417c66390475e8bf5bb7f2fe3d677f7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.4.tgz", "fileCount": 21, "integrity": "sha512-X9dsAOCIgZQcWROJwa+wM8h2pqamk6dDyXHs98fVOn+jy4Zgmu3iIDehuGTPj/k95eyiSOnLlcZKAD2CmkgDPA==", "signatures": [{"sig": "MEYCIQCsbSpnVvfBkqj0eJCNLYwBMLXsxNLiVcrCGviOD1kJdAIhAPYXVP2rGMqJofeZVcjE5cCkFsHPSUu0Bhamnx2HomkG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesi4LCRA9TVsSAnZWagAAhJoP/3+9AuBd57gaDn1jG7Si\nqP0Gc+wb/MT/gqfjzp8tvo1RKOLJ+z/jtklf4MKnFdVbWBdIliB0o4B1auag\noO+jT2EHNJKXtLh+CqXhNfRaPsvedsHIdKA8q0+mXkvE2zwzhGU92tzNGKtY\nnaU5SXykC9PLcOHNK8xQ0VJXgBjOW6Cww5HDg2OVY30pE9a5lKcYnFUh6q+C\nfl2w3utg9/4Zh7VXmVxSnSIGuLFz/6MglSe/OZx3M+UMgriL9t2J39zDhVMS\ndG2jVPmlgm7hJeFf/ojXNOnmyrhs/2cQSai0zctWLw1mnO0sG+ZhjMBgOp7N\ngEnM1XlvbTHg415CxbbW8ywMXA+AZQP+xrDowJnDGwKLlz7WoKAVDyCCghfy\n6kg6fugR8UFG/d+Jvc+GhtdTLKn3kYJA8jBCGM4NX3MM73h/P2jxZZQXfdAe\ngVAY4Sjz3bCqEGAqGQtbJx3pXXOzEbtqPcuaP9qMqqaXBnGkN4JHRGDfBrhQ\nzRb7gpwbZRXg3aoIIx5f3X4yRzVN10vQ6cAWct5uzImUd8zKW55vGTMj0Cfo\nkUg63PhdX+VF2mvIExuRmsK7YRbSW7FggyS81WZ80fS8bcESnDszn0n3VN1t\nEums1a7IaUXnWB6OhFosFjzjpFTR/p+nANG6uQCGQZBvtl4g90/CV2IfoB18\nnoO/\r\n=WFog\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "20ef0a04cf600c653eff38c804fc64a4002df5b1", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.4_1588735499301_0.01773077832114578", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.6": {"name": "@cloudbase/cloud-api", "version": "0.1.6", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.6", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "f5700ddc2e045f04997efbd2d9ecbe6f28948ffe", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.6.tgz", "fileCount": 20, "integrity": "sha512-Fs3IDbNlDmfYT2ZkAjB52nQ3C6ZHCdTQ5u7g4y3tV90am37pJT7TVF1Y8xEa5DvpfULfnalyrc2PjrD+Yof1xg==", "signatures": [{"sig": "MEUCIBrRnsPZt0RjPfSggmLDuWYh41lqGOM8MFcEFlRmeB3AAiEAnjsgsPSjyrGT6k02IyV1USM+Eblul8UaLg4Rkz4lvKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6slzCRA9TVsSAnZWagAAxa8P/2mk9Hubw7iNkDFzQcsI\ndkajT3TGb4ZPgXmG0kFlnWmUNAVLgc9BK9BlYI2C5/vrsFXzXaBjvXJuz5O6\nvyM6LPZV0jjWukCRUG+sDEmJC/NhsT03TFKJbuwFG1u80fDRJM/PECKySGAE\ngr/fAHKY8C3F3De5QF8b02ymT4Rd4rcIEaNGJcGrwc8BHxWPjZCi3UGdGonW\nWKTgQTKmZyMxxqE2vXi8tR/gXKYSeTx99TFkCd05iAYZMFeluuo2MyAN9Kjm\n3k38q097UeT0rw9r7G87BLHYbwhyAQSKBQRCCdz6MKsCeq1/pD5wUN7mOxh7\nHaxn1D14daW1WngpfR8IZffNoT5YsvJF1KSbCUalY/ftm0UczljI1nXqIZNB\n2Dx/rAu83bywb81eQXSz2d/Hfo9WEFKG+Rq5x0zYHmBgailMj5+iNa9RGX4D\ns417KL/bSaBbQlmyvkeuQn/VrMRhhLs8cITDXwRFTKsSjobhV+szJToe8zo0\ndHjXjBl7FQWsKwnIcocj4m6+UR53wszhqZz5Mgy8YD3aL/DG3/8La7gesdEo\ntaH4I0sRYxg6yA0jJQjTQn3Ghm7JPrqU5U6FleseE7GWozHMLL1GILm5nUMV\nucr50qJcHWbN+i6ReH4pyZ1CJa2549G2nNxCvcWyZJ4cOaKiBQVFgC2I3wHi\nLvRz\r\n=v23B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d8e22aa27062785fc20653ad4c4ad77f41ba7c53", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.12.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.6_1592445298658_0.2042648591182119", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.7": {"name": "@cloudbase/cloud-api", "version": "0.1.7", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.7", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "dc0a87cd07a7b92ac6229c466b84de0a3e7df7a4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.7.tgz", "fileCount": 20, "integrity": "sha512-igHtHcvbcXOM5va2z0dogAC/lvvLEKevdL8Ma91RsJgRViR6xU//VvrV3kxDWOnZX7LiImB19jJ04Zi8Nyo/SQ==", "signatures": [{"sig": "MEUCIQClsHPdPUOLu8OhP772BiCZx0qmdfstmvlK5TSfqvLvXgIgWL9sKDZemKcwXWprZuO3wzsZwvMoD2vuMfwiLQR1Yrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPRw0CRA9TVsSAnZWagAAetIP/AkAPIfn7Wn6tZvbjCsY\n1isGFNnhgw77gkgFSIXXWAqfGPTUiYUy62jJugFSkWH+y7PeUKhzeTSJStFb\neOcHuaCaqy3BCTit4PrMPRqMbs7j/BfnaCf50LIX9iPMT1rlxtlLNSQbfToH\n7wMC4Npu1VIvtIprd1LxhHXqNdSS575rRRosaZHaWjqk42XT1JbZ2OgVj60c\nOFWDHEACKXGB4EBa+8o1ZHrEBw872Kv1pNG03cZrhCfYr/JfZmJx5qHAAAs1\nRJhQZxAqk5x/KSDmT1I81xH0AR+XTDt9qPV0ktUqgTtJcg2nm8/zZEqZxXqy\niiMOuVDpAZn8B0iOQ0dA/iOEPTEm/Oqu5gcfbvqHT0+7m+odcpOyEffZ1qll\nnOi5AlrY3NXBKMzdWT8t66HBooTMxH6LT/a5VbQk6hGMcLjwNt7zA7T1mkUi\nvmmCZuP/7ognTH1dujzFE7KpA233MyWPhYhLfin2b8c2+LVuMd94Cv/e7Dny\non3kLH1AM++xVKYA0EEixP0ZGwji9LSRyi46SGcPJ54y7UrwiqX1Kam7g2Ld\nRZ2DnfmYXBcsCvN7JYRoSeKe+Sy0beMXlOcuL05mI2et7JKcqHfr+Mv3W+S3\n11LWWIqvH5rAJJE6CZuM9xVV90TcpXvIG+Yff0CrIQIQLg9u2BhznSSTQu6B\n66xE\r\n=eRwT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a48f0625ca0b986059df6f3e8eac4a5bcf938ee9", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.14.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.7_1597840435882_0.04740672139421087", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.8": {"name": "@cloudbase/cloud-api", "version": "0.1.8", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.8", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "355a3717a56b0ff728e58e02b4b45b8eeccb2ced", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.8.tgz", "fileCount": 20, "integrity": "sha512-Cnj7MA6Ne5Fv6fXJe4ABJHBvQl7rZWWNVnQuMCpLrySNkJZZk0kweIRzCNrhr1l/1NgbiIiMF2M36nMtWRpjCg==", "signatures": [{"sig": "MEQCIBmPyUbvAzG/gBZM11qO/c/1DIPRl2lvpeIHm5NZKwncAiA+P1wfMhPuQimoTkztlMkyoh6ond6+Etp2oSMb+P38vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRi3YCRA9TVsSAnZWagAAAYEQAKGS4GyTRyPnqsKjG4Bx\nj2UlCD0QtgFgNKSeYnAdZjZTmSJseqQvzlLfFpMz1jxObLDuvq3+DW4VvRja\nQDATaExab9U597UBh/Y7XCeQ7qcDqth0+6d1a3rsHIPddn+67yYEexUNtbME\nuQl3Zf6Gy0N4Y7zljqu/08+xZu34cZWMSiTs6OTBi9KHUvI2bDKTIiXN3Y08\nJrC8Hp7nnL9w0YEr1G9XEYDm5fzdz7N3gvhg9LmaSPbcWLH7TchBiWaQjqfN\naUqcdKzzhVIFe5lvIK3wofYeuKhWOqkbYfbimwD1+AQhO/jPew2DxNVJ6TZI\nCCVV7TRAVG7muvDzhmAiNuymFPRmUmwJ1fmJYjQmrYJ1fN0jx+jPVOzgxlP7\nXZkpnhtEwrtnIyas1nQC59omLKCw+ItA4hHqU88nfW7vp/+yYhAx1tyHUInH\nChKu5q84sPcZMRB/p/gfcZKMDhUCYCpj4Tiwh+1KEB3uyTimbpdV/7PyU0wn\njSV4abWcjDpq4cxO2Wvdva+XKyz1JIE2n8Y0xZaenEVQkZrxcNgrs2Xczh5s\nT4yUhDf/mYLW8k/r4U42W95cVPuz6lJkIg6I+GAFBoiKSOD0dPNx/EZDCHly\nLRjx3anQ914+ZbOPAEDV8UA9gwnbkmEJFgk9VM5n2oOlAjUh+pXF4dWrXQ4J\n4/S2\r\n=+7Oh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f06f94c91c69d5b7562270dab5e5ac1cf08f8657", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.14.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.8_1598434775864_0.33005283563735044", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.9": {"name": "@cloudbase/cloud-api", "version": "0.1.9", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.9", "maintainers": [{"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}], "dist": {"shasum": "138b8eed5694c275d6835af3f9b2a93aa2b4ccb4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.9.tgz", "fileCount": 20, "integrity": "sha512-ucJTcesPGY8hb2QiAIoZJobbhI/c0G16IiUoPMNxPUAxQS9x2vqgLfaZqEOzeGKdfgtqUd/T4xjN7DDq6tHt3w==", "signatures": [{"sig": "MEUCIFNhv/2Iltc7sIphUf1t17asOyyeULsYtp2IuIQ8o2wwAiEAlQZmaFmuBYyljWKbJlbV9qbWj8Y3gnOmhvXY/oWU78Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRl3+CRA9TVsSAnZWagAAICUQAJHoMjhEQ4ZvS28GSMLM\nBe9PVI2BVUFqeKFZJuijemJJKluWUWg0sv5EYE9YNNjIJAIqE56g17DOm68A\n7dIokSSjC/wQiC3Me0xuNJumhRplrpVOSlkT8GKmWYSdxdNXj7DdmQ/TYao+\n/L9nEAmUXURbcwk6XmcjRmlyJrKTWpWxbU597Ctgsefs7WYpJ+Quk8l5X4vh\ng696m67wS/Vfa6XoYq09Hehs4Yno2qwJ75SoBKIP2w2iDiZj4oSyTxcn8iFX\ncKZCNMnIe1eeS3nZM2tXwsKd+m/SE9RmSpZ+aG43mL5lmNS3Uwq8CCEy8zTp\nEo5VMWfCx4pmU/JZUmG54/Dt0QYtjI93gPXHMEks/aOWuwhNcVjIhkEbUflt\nCaOW84iJGAh3EcLs2fgwPCYn+n+dITOfvqE9wcy0KASNk4rj3ra8P4KDNhoG\nYI5kRYDW6pRnTuc+BnFLM3UvtzvYhYQNYwmtnlsu2Pch/ZjhP7Kuit3mNLHa\nFbv2dXBgbWNBZxbetPfC209hl+r160vHrRWkH1nsDOK2t5EY41Uh3NvDnBRm\nUMTmam690qsETmNc0Gkg/DLFO5ZasJr7st4mM5/d0m9ODzuYrDk07I3qDKbe\nYbBi1j03rQLRTky2qSLkFbL2IWZurPHAeH4jamA1Rh4KPwLZx6pRoEQ7IRXv\nLSkt\r\n=3i/I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "11f2e36bb8bc25a24e130497ca19bda1159bbe62", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.14.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.9_1598447101667_0.03176080002758774", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.2.0": {"name": "@cloudbase/cloud-api", "version": "0.2.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.2.0", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "23463754d0118a02290412227689a9b44810a126", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.2.0.tgz", "fileCount": 20, "integrity": "sha512-NjTQegC5V5lSBb6szZtMzyoOYdLuAAC9mvUqg5nsoBOLcB87yFxLjTIBzCGn71CHdQofF6UCibvViDH8BrJv4g==", "signatures": [{"sig": "MEUCIBwzLyLOfbYdq0HJ2Jl2Bqe8/IQ44IlCUVXuD7SMncwFAiEA1p6F6DIe5E9ordRBMhJgf5ze9CqtCqVu8wQm5uzYInY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31536, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjltcCRA9TVsSAnZWagAAlRMQAIR7jLZvvsOMlIrKSQ7U\nLlZ2eV3o+9TPovjfrMAqvqepgZ1HHpolG89WYNkGfhZwVGx2WO6UMmLptYPM\ncisLFs3mZ0NmLWKCS7BrXdzANIYI39jq/nkDHo5teIGiuPETP6ox80Ep4Y0H\nFUnbozxfXseluKXgszRTEVVjJi2XbUbulqmgBsceWUHps2nh0y6CNXMpDNWL\nF5IWxnW8c7Z+euiAVH9LxS6+oUFLCWoVSo/Tdbp4lst1iqc0SyjUUiwUtNE8\nvjiLMJOAh3Rs5uZQakUzkKj8RVYabyVTCB7Uu3AnEtUYETyXphbWgXsFMK81\nhpyKptOKxXLstdFa7EWaPOOOKRvMcPYvYUjqXbCELaTeFdXP83e9/FIG2/MX\n5o61O7Xm+0cYlW9E9914eG5TsLJ/W2NIn4HP7kOtW3YVtnuzJ/VKJmu6I04C\nIlYgr8PV8h0o6WOR4ryY/Ts14qHPgqcLdssiG0auperiKIcjstIp0INmWycN\noZZQZ14TLM9hWQBM8jv25GERxW/+6t4CCNJZ+rjq3BIXx8On1Tw7vuQ1Fn0m\nu0chSf7Nfs+fg+r+bahhC+TYp8UEzXG7dtzPEXHqGQPSSumUQn9HvPWChhca\ndC0meRkVYaSOB4qWQiiVb/QFOogV0UZDPbkG5NkFWOgEjJT04DV2Kk6rR2lp\n5gNq\r\n=Lw4X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "c53f513fa198e0bff7b89b50c4dc752acfafbd9d", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.2.0_1603165019909_0.5498441248251411", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.1.10": {"name": "@cloudbase/cloud-api", "version": "0.1.10", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.1.10", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "1ac2ecb2cf6149b08c694077a4e9562306ef01b9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.1.10.tgz", "fileCount": 20, "integrity": "sha512-FdT5RAhkfbY5/fQpi+YLaxHGmIqdxXAS1L7lY9NpcX0IP3JWgIK9CP129cjthUHG697922adJ/Yrxjv9J/4sLA==", "signatures": [{"sig": "MEUCIB34vW/T3L+pidHon2H8FrM5bB4bb6e7GLHGxQ6NZH+IAiEA47EUrQMSJlQtEYutM+CU2OCtitPAvcu3HgVxnwcP8sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsnzJCRA9TVsSAnZWagAARiEP/24jTUoQPb7uFEJdErSn\nDYIpxxj/qBkXRF7jQm45QTVbOTSOJ3JEPmeHCsxz0rtGdIDYyAf7jKEJ26Vr\nQWw9J5dManyAZn1vXEzhgwNmDoXsOS+/BntAc1Rk+YWqsCalraF+tlzJlJcW\nDXkBXagXK1IdRSatyGFNUedOuaWMKavJ3h8xOwwRFlQecplVNUjSCscweRSN\n+o1p527YTauYEAnLeMiIyterdT5rLcT+WwZj0XN6jbLqoF4Xj7YhSL/6gql4\nks+tw/y4xc3Rj0GUBoXoQHCz/UX4a92tDLbKmVn/CHMcwPSQNeukuI9PwnMw\noOtl+Omkm+wvhW3kaUIPPP3qL/0V6dbi6UEHt+YYenU26NMmbaagYiatnYTo\ncs8878XPuJLKQc6fuhuTRsaYm2Iwbjt4b6tKBTEemkV4zEFTQEpeHZXZpn36\nzZs1PorKf5T4hiFjXKKu4tk7HbtIkOKZ5JUiJW74zAN4y7tGhBeyYDchypNG\ncJUxmm4lvJd+JA80p2hTtJlWgut8LLznpo6T75CwU1HAgmfoJDIc9O6bNQDp\naSyLUqXfH4ERBfjTsxQ4waJHi1Qgj6co0rn+nJJnInUM0BOTYXU8XwODpR2z\n2ZhyQiO5NwWKcvsid3LNrFO66IBU+6PJijFAfRvN4fDQWhzz8vk2YvUq8RGK\neUfR\r\n=uELp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d880397f85940fd2d69f5c7ad45ae72441dc0944", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.12.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.1.10_1605532873012_0.05564900239027959", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.2.1": {"name": "@cloudbase/cloud-api", "version": "0.2.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.2.1", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "c663f31e45b56cc0690d3ff7caea4862e44ac1c5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.2.1.tgz", "fileCount": 20, "integrity": "sha512-JbMFz36FllRLCWrR85Z6I7yByxoTvlVIosNDWsKOinjW+kY+XQeSupwxLLkqvXETlfVsm89lHegA0i6tvxZo1A==", "signatures": [{"sig": "MEUCIFLlnCO7zQTHeDdgr8A+qLQ3sZiQM6V4wM7e51MW9BZjAiEA0PBHcBvoef8v6UpELMZgZcLnLuT3uWYBjS0dC9OwckA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsn0uCRA9TVsSAnZWagAAOwwQAImyJQQdY/+Ja/CUx8rJ\n5y9cDp2+zIO4XECCrWGb0Et1y6u+3l/7u3u2WtY+OXXx+t6WhlT/NHjFkjHo\nM1y606JcoeFeF5V2ojEl+ykGyuMceT/M4C2H/XW7WJARQkTaisprR0ICRidj\njEfjYKKmcPxOhnylTgaU884Dv5l0VX5vcxPxrwTgM/QWV/0Jvrl3xVX2F0Ds\nebXZ0pT3cQfceFV5bs2ES7H2TvgLOzVQ9KLdYo7xej2gJ5ncbNaLdIErIfqz\nzCcPwvhfvunjK5oY2pJPUXB7kpS1F1dDhKlpOuZmcWSQlFfJ4F9y2V4eUs2I\n/UUfcIZziomOtYK15Xc4FjoqaMdLF1XJFLwQrsOi6T4EvdT3/Lm8MTgRegZ5\nEJ1KTdOxVfloGyBq5kvGumBlEUdBXhh3UkTyOPSX+glA1Ig0ZpfebWH7PfrV\nREfeAyAe/QgGMwM+C1W39LT0Tj1pFn27x6EhU+Z4U1jtrbG0niT/tNt/BN+x\n8yElGw/phhyvi4LTcY+wVm3HM2hmYoq0vuJHOzKxeJkmY0lu0uE/Dvjfn4b5\n4mrOeoRSN0buZJzBcg1G1n8HsNyAOGtbvAc+fg2E314fLZ/qdXXihND008Ln\ndPjvF8l4PwND7f8IoRefC+iLhGkhPKyrbqSs90UkImYxw0pOIBcaLwt3CGwH\nnrEL\r\n=TJLr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "98298824619e0d8a711bda0940fbd3c697876e07", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "6.12.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.2.1_1605532974145_0.9725505204041736", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.0": {"name": "@cloudbase/cloud-api", "version": "0.3.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "0fab15aa0a700f20df1239bbd6f39f0f10c1ab07", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.0.tgz", "fileCount": 20, "integrity": "sha512-JKJGwQW8nebTgMYTD3ySEgawOUmEQkzrj6bh2/WbnklFxGmZmfpzEYX9hFyATXVSjEsjSGF7AHi8QBmsLlg/mw==", "signatures": [{"sig": "MEQCH1c4XO/9vDR8cxNSmyEEwbfB/DzUJ/XI3M/0+dhzicACIQD7Wt9K8P66o+nNPD+gCpLqdFjFPfyvmmghfkO5crBfHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6Vl2CRA9TVsSAnZWagAA3uMP/2QLoqwNV3GkFV6oUAsD\nl0nScWjIZug66pxf8+Kc5y3pyP/cZLnOclOMRY91CzMO++pj7cylFFa4gaMF\nXQO2QBvjO/8Ngu12J6jKY3fklYbzXKynewd7drY2W7+NlPCw1TWaYrbJlhjj\nCfzAdG+RuRLbXHvA5AUoAXLLKiW/dwDMxsWUvSNCPFKthCpujSq9nAgb84zV\nvgVymMPvXauuv7hZHrYYbh4H2lJvPZYqvWsyjRUpwsnWQFVbenYJOQXv3RJ1\nBfBEsJHKjA11g5fxKnWXGaLvxJ5EEGsYpVrM+M5VkWecnXF/F4c7eQY16tTE\nHr9VF9MCljsi1K1K46M1Vse63tpbOdrqfkqdMHo3XwUU7kfSl3gqpfPrD9uW\ne+bhKwIfoSDByNQDvhIcEL8CJYERaMV8gjmYSTBor9b38h4P4uACjq/Y/gQe\nIj+VMMj3StAw8N38X8ULie0zsvtH8E9LtvOqgxqhOB+ei50FsesbOz2Fx+Yv\nJGFQZZNN+xAgzFro0BaM5W8oTbdmLOP4fDjMLkS4sx32fDll/ee+zY+40Rt7\nnTCQZC1DlH3To+/HtgggbAO8foZLxjbSpKY5MXTQLCIiEWZeDhmhzR0rRkwL\n+NoGLvbPrX1pZSjZLkJSkdqiK7xcj/t7u4urogv7oxLQb2KGAV+G+L/meXfL\nfatC\r\n=Oy02\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "4847c009dbef90f041442a4a6b63d8fb5eee8f60", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.0_1609128309145_0.4142200780528975", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.1": {"name": "@cloudbase/cloud-api", "version": "0.3.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.1", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "d5837321d76ba2427423e4ccef30fa76119eec5f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.1.tgz", "fileCount": 20, "integrity": "sha512-qR1kMncRS7V8VO1sdc4iycqe7fKG+t+0T9aUq816CaXIGE+KlpOgp7LL5/2RQs6Tdyu09BoRYWj8ODo+6I59vQ==", "signatures": [{"sig": "MEUCIE2ury5qG92yeKsmU2Ig7ay4o2aHDR3Siybc8aV1t187AiEAgaJPBb2hTA/8ccc+cF3qW5gcJ9FwJI1mt2oKcjUSyq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6ZSpCRA9TVsSAnZWagAAyLMP/0NVIoVPTwIL0VVkJLSn\n98tHtIrQlYWOiCZB6RRzQ8YzGY5DWncIiU3fj8Ekc6DnkArqRIDXds2+L+VZ\nI8yFxkk44rBD54BRiF6QzetSBCxTviPl619ahBPsqfd2jEJrIFTqawWAG+fs\nsAuhWvBBsF8J0kyafdgYM62UVun1l3CxuliI9IuPbNIAbx6kwu0fhJ9gDa69\n/ty7cPf59FojABnC5AQF8EQhlkWa/NXmcMlpGqAvpEYezMvn+P7oRQ0I0Euh\nWOHlwtcb8i5VhGA87Yho8ZPkJQSblck+Q3s7Ez46OYuGvs4rbHaxXtbDL4pQ\nP36VXQgfaaQc2GrgcJfodq7ik82+1UpDzzWSvH/7UH/jmbYupY9FDEQP3Hzm\nY8lOATHVTGnP2W4URgOWApPXs6XZ0yG7VIipIUYQKvsdJjuJQpMGQ3wRmTcC\nS2KbudRTwlOhadoZX85JlRl25tHg9SeCDoNEqxBpokZXWKo69mxrZO11Hyfn\nZdwf+NCXhEAjjG1cTwyfMf65+DLJlj7eFXdnF5setdoLP/GK95TUnWvmMNSg\nU7U/HFyC6N9vAJbVGO+3hezJMDKZ8THlrG2rnYqHtLcsl1i9r/K3R4GDysFZ\nD5MxAV/nUCCsfS1JgO/yEen0HB7WpqVd9v022Y0J2G7uLLJZTN/r2hrZAIwS\n/hVc\r\n=4RUQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1c8a5d50cafa36f87b01d6ef9beb3d9ed17014c9", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.1_1609143464563_0.2617436982261421", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.2": {"name": "@cloudbase/cloud-api", "version": "0.3.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.2", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "43f02caf53bad4c3252d1e2076197910de91c53c", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.2.tgz", "fileCount": 20, "integrity": "sha512-RuW0zMC1h8T/JXe+ThiawEVegAMql5i7dpF+UGJ9rKjHUStVYWZelh51MJq9oqENxwskQ3gI4SOinfboqvC/gQ==", "signatures": [{"sig": "MEQCIFkT6PELGVt0S9I/Hs8YJZWUnmUR+rHipCFhjj2IxTuoAiAESw6LCc07xzKUXIfmPYfjOma2GQs9vmvqdb7HzHM+iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6aUKCRA9TVsSAnZWagAARwIQAJjgFcwa40+p6qxaPmOG\nOsm9xhCKhohfYajoRLj7ZDWOX5sjtA52NdftwaONsV1PP66y8ScxgMsdZVU2\nPZ0A7e0rCwNfK8eri+q08Gn69NbDAe6V2xqRzR9DuCS7nmctKC1nfItrjQhE\nqYDBr7J0UPfYO/2+x4FVgYijr2xeAqOXDMZb13cRN5qlfWAGIls6dsCE5LQI\nzGXsCZ9CxlxhoAhQAticAvAlS2Kc3nmVcGCxNYhDjj5mnJykqi2VfqRUr/u/\nLN226CZF/vatxGeudnCinxFrgY/M0UTQ1MBPfo7xmcUtMR2KezvUVX6rpgQK\n0KydxNqU/BiBrxa6za+/HmYAKOigJlzgX1Zydpud2DiVqcAXuAz59ZOA2prn\nX5+jzS7JU52YFMuRkWslpq8egEb73JyjgwAtcPdtGcZD0rmBX/bMXM6lvT1n\nxGHi91kPhfPPp8ppSbORORB0PNxxtd7oOUyefGkz3DaWEGwOjLmEPKSbKbHv\nHTXMnR3CWHSax/JicwDxZV2MX1zRDTeuhMEM8UKQ1byFNrQRghRWO9UjifuT\n3Kcm7K6OvvrzLzyMxiTlPxweMr017X/HA5p2ljcSL50pwBz0xdjmKVUJg8oO\nXpYws4HJBjYARVxZwt/WT+F3hTNVMMYQs6SOqtAMifOQlTYNWF9G0HU6TBL4\ngnY/\r\n=4zVf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "98b81e8452f889ac4cc0c91aec785ea71aeeec28", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.2_1609147657709_0.9853219965550664", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.3": {"name": "@cloudbase/cloud-api", "version": "0.3.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.3", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "70a3877795e6cdb58e56e7c458d199ad8ffa7075", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.3.tgz", "fileCount": 20, "integrity": "sha512-ZIM/OnxFogRLxPG6p7aQHK4LvNMT9W0PmCAfRuVvg+fPjtuoCg8QxCBV4SSr8H0gqz6ULK0MJMM6smMbBdsHtw==", "signatures": [{"sig": "MEQCIGEiRQd/5vx+Rxrh4cOdZLl4u8ApaBiPqcijjUsKofHVAiBwVQXAMYL9SJrU5FVZzgK91ekHoAeoyGgTzHqg6JAPMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgATb4CRA9TVsSAnZWagAAnI8P/3d56oz1qR2NYUij+itz\nCDRGga1qcvGD/5Ni3aDZ0ANJb2bKDQwfEsvbh7nVlE0qDGdhxkHjqgD84YU5\nfnPz5XZxDcV2j3wIKAtEDwyWwROJ1lIJcpo56/auN1vTbaGmKsqycP0HkD5n\nJofXD8PCZo5CyfICPTFlBQZ1KfzuIji4fLieSq1IzP8CHslyQ7Bbe5Akv1zf\nuLI/3vShIMIQE1sv0vFHjmckhGzrDk+CkZdobjaw/bebUVGqXvHXWgWWUP1o\nF0G/SgbP/4qYgf4yBQUY4p3x7LrmPIO02Sc8YDLiOPGDbYFBFk0z9xisssLj\n+hoRNYlP96rgW6ub0I4VQxpFRY3GjylaIxcTGqdCS5SkorSuTi/48wDDSruz\n4V7K0OEQdjnayNQd4VNzYZSPZE3r9QLQeVAcZWPZU1AVp6CrDOZkkLyAbi/x\nV2xUWZVL1gap3QkXvtO6m3cQIFnliPmw/XITxpSYhucqmrovQ8EOEu1cUx54\nQ5ZWsTsowGRe3BXlUY9pE7b6eD+YaTyaEcFnK+k5i9kaJgWwRenTmcoXJa9/\nhhsjy1NRNk+iP2jblalpK6+HuCx3MDl66NcNmmkU+wlKyET8ERXOQK+rFVCa\nH2Pi3KaMBgRl63oWckfGVwlB453OC6BxrjyX1TXPA7sD551k+E25vBhoHRVU\nKW3r\r\n=Kz/q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "592b2d0fca6ef08e5828a8f519e46a0291e67180", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "wuyiqing", "email": "<EMAIL>"}, "_npmVersion": "6.14.3", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.3_1610692343959_0.3303897821161417", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.4-0": {"name": "@cloudbase/cloud-api", "version": "0.3.4-0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.4-0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "4237be30053bd7f525dfd5be08722dd2472a6be7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.4-0.tgz", "fileCount": 20, "integrity": "sha512-R/ZKXTfOLeIFerELpWCBBcLLAnpIHJ93YXBE/SvDjh3TotLeb4thpA3uJjI90NDUcGBzFQ7d2qKbk97daKEDCw==", "signatures": [{"sig": "MEUCIQCBV2CD1qeeeQQN054dzydbHplCimQTvrDS+whL7SDvXQIgMlPpZtqEH0vrDr0jGvnbaZSoeLMSjK2V154iigZErDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgENABCRA9TVsSAnZWagAAFKsP/0ubTxCBGpVrZqnDppNv\nPKUuB84W7vyFXp9i7AhkN9PBVFOODY2jnsWPRwXWzYMCghGFmaO33UDuRtGr\n7R0fUJTMBH2WWDAyzqXlZjIUds2XQOjyH9glUJ2CK/IC6f758yUks9pbeBRz\nbSxLKf/DZ2KZmRYNij6xQ/uV7ow+/lgrDG7pBphQgqXYk0nVi3Nu0sLO3GqH\no/W+aRXhJr3GEvPoWliUfaKkLmguBgoUcNigvpABaOE0rf6uzSgxYP7Cmyo0\nqHcwVRv/vLYULIif6fHaIx9GyNKtMsHpZhvEUFfzuUE6qYTacmRoilgmGRvt\nKJtgTBSGv70Kj2/qiOwOcMEVGaKoid1hdGJ+syJvSn0dUjjMMUWcCGo7wj84\nWe8cwldzbspINXyMOqxDJ1kRSFTXMG+8gshbxa8Bugj6KQL5dxoi5zAAqfc/\njxfysDTEhl44y7ey1QyxeFfdgGRqtvOzfMsV6zuylC+UeTnnfKzzb/3+GfvR\nJs84+dYY8UCxTFA87tUGipO8F+dBKm7wjXV7GuxPVOHHGbEhnViuXJPwBJYb\nfQtmTmDncIQ4UA58vZvCe6S7cS62wzgMtKNl+pvI5JjS3d0eGGpYsNtRMV2n\nowSbfmWQUywsCkiwWPteAb1rN9iqrHL0PNlNB3E59PZgc5oXYAyinfGUkt8o\neMWJ\r\n=7qlX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0c5954055a8a5755f71d5b5164292e94cadb8ade", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.4-0_1611714561355_0.3108192222652377", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.4-1": {"name": "@cloudbase/cloud-api", "version": "0.3.4-1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.4-1", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "f5041f773fc999b0ee6059effe9e908236a58bf0", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.4-1.tgz", "fileCount": 20, "integrity": "sha512-<PERSON><PERSON><PERSON>fBdWygAWxR0Ep7dlxAe4RhNsI8Z6TKeasizPh2N80QNtcJ4A4mhYoceQDoAC322g+PiAOTnis9gHEoPNH7w==", "signatures": [{"sig": "MEUCIFMuVf2podifLV5rPzy7lsEjXkm3lh/DdQpqcYEn7UNnAiEAs7HfmIBGhxFtrfTWvTxdhVypw2xuvNA0ic4mjr0Cghw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgG7OGCRA9TVsSAnZWagAA2RcP/1d+2FzyRA70gCqd9umk\nOhB+X7zA8ijpCcjkjGc6XsFefzLoF8CgAHNtI4d4nwEwohFhDsa2nQLlGSbu\nctbyfRkL/DMn74FlUxVI+lRtOqee2kVpmrDbpxb9wqoY4CRkHCKTIpLTcRH/\nWigbacjO46kmBsUGkVu5mVDaW+GgGLJYE8Nnhr4mbd05Uxefmu0DcQ1CScyc\nyBNORzC3yN1XYAwHtFHZqfjVOWvYR9vjcc3RJm94d0Ze9YtQvPZr5BbIKAK5\nomDM5jX9EUxrM2aBDSx+0OjSFB3NBNuHvym/yBLCJZ8pKTPy2wct6bUkGZWj\nNRUFqK95J+Unf2FpQmbVqZJhhUbwJvw7lTEiQMrubptf5o/SsvvCzfGe+d4J\nJY+mBd82Zea5r37WRPReNchucUJIgXHF0dn4uEc+nw5nxr3g4fjWpcj/o7ox\nEChkWGYsm7PPYPIszKvGQgJ1uvONRMlN8BpHOGWLiP/NXePMgf7peesXLgdr\nfvzyOknYo0xkR8Bvv6lCzpL0kyrXlid6kr4BDP6IFJa1jTKzeFse9y5DVfNy\nq8zHPTEY3rRRC2RvWwWc6RkCqplhZaZy7MbDMCXtJoHfcq3QjLOWaA+mjZFu\nOWKGNJOd6GT7u3oDVYlCZjFWRVCdOzIRAw64qa4MX6G5YVL5NkGI7D6rTS2l\n6ksk\r\n=/qXe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0c5954055a8a5755f71d5b5164292e94cadb8ade", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.4-1_1612428166268_0.3596518225652252", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.3.4-2": {"name": "@cloudbase/cloud-api", "version": "0.3.4-2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.3.4-2", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "6bfba1219bf0e3925ea77bc9f857f7f66f88f673", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.3.4-2.tgz", "fileCount": 20, "integrity": "sha512-nDL5qaqkaTESU6KSBrRBPrPYh7cOC0aYywJdIQe4sAsV87Fq2KPlqAZzGB5ODIC4R72/ZPB9bLDXONRTr1nZQQ==", "signatures": [{"sig": "MEUCIQCaonDdPg1mDphRoEPrwMDjdeAEr3AHdhhz/GJhxryyVgIgS6PwAywQCBkGO9ySKc/xy5x/ISFTVldyGHAOWhbdhNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgG7mRCRA9TVsSAnZWagAAH7wP+QFdoCGV1cADit9ft2D/\nYNBZagYKi3a+u0arCo0QJjGKrimSR0SJznY/gAprgMk/EIhhqn5bLmPsrSLX\np+2QTeGR3DsW++tTJWP9pJVH0IlicUcOz7ZAsDwikYPninukI1m+lpPghrQL\n7ayhI7KUIHZpIy05NXokSntyMmljkGI5+aNosv3ulL3PtvlvnCc4Y/m5694P\nnZBP/qx9IfeusuGQHnT5TZEx/D5QDir750gVcFQnf47YwmeaslDOBpD6gku+\nmQnPAxSvqeTWrAxsxP4DKpl0BTlmnzB05cEjhS0ZKzDDzwZrHlaBeYQ/bvX5\n0hUIM/xx3ovjmVHCr2UTO1rx/KpKR8kFgE+OtPeWdfnxbElIjPHOw7L8D1yh\n+ASLl8kMgW3Tus7r26TlE2cQUSIu2l7CysYbAE3zSc8GElk1KRrTMUnQqsVw\nBLucoEX/zwYCaaaJAmoMCCUuDGGkvPE1jO3Opw2zgNo4qoDzRduYbuKNTQYm\nBf8fuwC65niJoHp8UQRxn5BLw89zPSEZN+PlPq73Ao81iL7f8l0eUaC1Wa46\ns9JehC6ZTF94fdnfMRD0UoIEaXDNEgk2sLXxP/sHE/BFmeV1Sj7UaHLoYaYC\n5fo7wn6FTCdOkT5nuMMYDfEschLpAhrQ6H/8GJSv6ngjn6Ts7omhY/lAngzS\nOHl2\r\n=RN4n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ebd9d0726d474915f499c58769465b5150f42e1a", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.3.4-2_1612429712769_0.6989240536390704", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.0-0": {"name": "@cloudbase/cloud-api", "version": "0.4.0-0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.0-0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "dc09896c9c82377d2c8e59adb283fd3c29ebcf7a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.0-0.tgz", "fileCount": 20, "integrity": "sha512-i4Jn7sZUkE90weiEn2GKDx1m7Dln+gan1IkGq1ySm7DH18d8s4NuC4+4RMQe7x0DBuHdGaSl9MRJSk4EMJB1Iw==", "signatures": [{"sig": "MEYCIQCFL7sy9KI6t2RQstkmGTW+n/gvF4PDljm3rb3ftKl9rwIhAKqjKdta48WQK7UC+awUkN5wVX+osPO1BjFkH3cHS7MK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHAU/CRA9TVsSAnZWagAAZBQP/iUD1SMxHIDlDiSWf2yH\nOIPAp8xFQCEUvI8Tv3fekx2AjvCtd4N+ICZUvUHToNrHqCeHCye4JJNNssU8\nqK28XFMVuX7hsBIRELyi165KtHzIECBeKqaSo9SomTn1I56KfJbacxW6Ne/r\n5ebN822uFgWDLf9M6SNArY3sGEqtBHMRg2xLC3W5qDAkxR77kf9GoHdzbmds\nkVFkAwc2QTeyF+/U+qtCbWQ7KTW248JTBtPh6AZAWPhtXgZDFrVlc7uPqfip\nmHBzXRVIhSwnwxM6+eKUFELB86uOz9BC86Duvq4g3qjg8HsTQu2azCeyYcrW\ns2xiI5bhXNotJMjvtbhT+JiDDyAz6qqjoBGgrD+rQYr3Z7pZldBHTSqC/Ckj\nyBRJTvbgfgRGtMZAZJks2XaXYyYLWMC3Qkv4sOFlP2hsvMKGRNcwRDsXGlqG\nePYJrkIS/O5ADnh+gtzQZq6vxdLd0c3oMbH4rl1z185QC6XmycbyhQK3JL1Q\n3EU335jUSF44PBTWekhqNETJuwsjwi3G13/ME9M1hy7gAJUGd5CEF8r+NpQ5\nDtKpiqgE7ofiywCP91+TYfNTvNdHiFVU/AHYE0DSes5Gk91o+ksnIx77e0R+\n3Qml2zxlJ2ErLtJlddYSYVsy/CqnA0AihbL6bA5BYof+IQ7fr2+Zaxz40d0R\ncGxw\r\n=D/+H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "e4d57a14f5b4b04359f88516728e7d34a4f71205", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.0-0_1612449086514_0.982291987596811", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.0": {"name": "@cloudbase/cloud-api", "version": "0.4.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.0", "maintainers": [{"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "7af2e1cb4530e18f0193e85f1f104193433de0b7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.0.tgz", "fileCount": 20, "integrity": "sha512-D/01OvznG7vEzDhoUDAh7Y7CRTHu1vfyTsrnp05R/J7u4FYkst6olM2/3ILJaGpcPirqaYkD7c5Azt74E7L/NA==", "signatures": [{"sig": "MEYCIQCdhjba4WymsrXR3yH/Dq3GaaOeTMHW8Hra3w/UKgjrswIhAIDgWn9VGS39jlNHY8LlexTgpxNMbXpvOCViXJj0ysfm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHLsbCRA9TVsSAnZWagAAgN0QAIg1f02oBhGCwO8WEr3S\noVduHzcon6E5qUns/Hxl0htI6ldqA4yzYQB7fw2Kb1eS+cT69fA9cguXVSgK\na/1giHTGcwNmrQvpee76WYQsc1xPZvZdWQzmtNGe4icMLOj5FytCMzEDI7F4\nXZMTMIBvM6lEqKFiSknvNf9eGvPLilhWGBJanQ4nzWnGR4dys155IAAFr+o2\ndSa75VmHU2+q9b1eN3L/60kedCL0YrzCjHyYPpXmPBUZRWZ09YIEqP4JSzkB\nw7CKmJEA14LgUIlV8inIfE6CM7cn0KnpBnXXKm9NeUMcyVPQAQfi4NQ/c8AR\nfdga9pGH6USrcGxg52n3PHqB6RB+7odWJKsqKO1Ya1Eu168lW5ia2ept8E37\nlhQqomnvddv0nLK6/v3g7FUWXmji/mYt4a97IwfHK/lNk3UeJ+rii8/fPQFP\ny2KtLmKVl029BnZUgox2HSX89PF1r0NTU/+NRJKkBSRpcTKVyhMF+3+l3joU\nAyFBlLIopgy3WVIXmH00cO9WI+QPivhAsUR+hw92HxKxLZgH2tZDRlHAqGCf\n7Lk6FHWLTEVYZpSKHZGk6womB/TBf1ikVnccZuGjwfzqb2YWmHwB7gH6oC7N\nuJVYGHbcL8NJPi+7uc184+fgTP6fFOooBdTEALre+A+IS2v9pKxn1RMrXQHp\nMtyF\r\n=O9AL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "970e0b93fddc7a4444844cdf6bee6df0aba6f171", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.0_1612495643181_0.24499934830357617", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-0": {"name": "@cloudbase/cloud-api", "version": "0.4.1-0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-0", "maintainers": [{"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "95987559276b4e21bfaf2f3e35eb311b59f26340", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-0.tgz", "fileCount": 20, "integrity": "sha512-a7RyRuXI6x1DTgc9Kxhxg8Ng+NwZYoEOj7KDJyicDma+W2QlbVze9wp+p+RW5ugRGxHnTG8OO1+Zz5FssSR7ZA==", "signatures": [{"sig": "MEUCIQCjA79v7s6sqK8Uo+sL6VuO8Fa2cT0Lacv/s+vt34OV9AIgA+xSyYXRUD7tKt1MblXR7+9aAyWLbH9HetAXdT3tl6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35458, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrxQICRA9TVsSAnZWagAAmN0QAKPW5k33Dn9GYU+FEjti\nHmaOg3HVjxg6XCfEXkdS/PJIl0OuFFLEtRUfHnEvw/G+Y7PPOTxqfM5sQoLi\nhg0i6pg2wbZFBa7KNZM0Bkc1f9FIcB8i5zTU/ZPHCZk8KVRKrmtnTgyHshcX\nm1TIuCfcpFCGmwFXb1pSuNc0wIoZlRHXnAP6VpE0RviujbGXURfOE9o/iz60\nRnm9Ocisjmp7v/AKCHmt/Z3fL+Xt8Nw5hlAycmqHmvIT9/I9kfiQRPB3FIOE\n3tfe63i8ekadV6ejKeDt29z7aEk19Uhfc5mijf1S/w//6NObsni3Q2vJZ7nI\nqicpMNAWkCUiokX1uQP2qu+caYkheJ/83TXAEFUwTHWY1Us28sr8wVBHFKNe\ngblcvtY7qwVxpx9rh5uqrfLSXW4uzaIKBTjJ5lcOTXdaNoGPNpnxNA+qQTHP\nBaxpxn3eEaBvYoxYMNFBE0IYKA4BgFmZ7vDAnogsFX4IdELCURphBt4+VLqT\neeko7t4ARHrisQHBLXILJLI4HTscxETpHlCl3G9gTN92CQpWiYqHnNPh8lZ9\n8L/SDv6a6uyO6KmI71A+nKKAgwnJCjFFZI6vlQ5zUj35HO77UfDX1aDNtpWW\nveBjSjbCAmD4YCUfOcMcA/9DqUhn2ZI7d3bADygIBWenWPQQimT+olmYNL2o\nRshF\r\n=Riq2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "970e0b93fddc7a4444844cdf6bee6df0aba6f171", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "zijiezhou", "email": "<EMAIL>"}, "_npmVersion": "7.0.15", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "15.4.0", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-0_1622086663608_0.49231691189928206", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.1": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.1", "maintainers": [{"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "8d9c9dab07d421e1389a9e25178d477ccee39d7f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.1.tgz", "fileCount": 30, "integrity": "sha512-txT3lsi5I4v5IOInTqaT2AvCH8ESvlgIWAbJ6THAfdULercWXOswG/jqzoKL/og5RUMaUzh5sZ2v7dNdLa5SMA==", "signatures": [{"sig": "MEQCIAJaIVmgz2BfQs/E6khK4YiLjTggUH8CIg5yrLynMmnmAiBIjU6/IU2wCZHiKpb879RVhqRxI6LiZfZorKbukZR/ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5DmbCRA9TVsSAnZWagAADNMP/RAdvBCZXXX8sAAlEmIL\nZ1avRr4zAXgC5sODQmJt3960p/FYk52sd3k3R+zeLIRH0VdXmu+EJc1bAdGV\nMWfB1WHr4oyx2YjYKx1wh+7y+Pnt6clQ9TEdrBQya/P2hCA45vsfjlEqd6mY\nhZ8dZvlHpcON1eO3wdD4mDkRi70VEMO+l5xspWs0Trm0+DSV78BjK2MAKeni\nnf9tBY323y4WAY/xoNU/yhBxo1M+XPah20HDEflDS8zvOH6KKB4GFjAUqAoR\nzm3PdLa5ZyvxxhjBsihrtxzGtgNcPTaW24wsfk7MpKy8tIhYjlaI4ZyQKQSf\nuqgUelqY/2N4IpX0xViVETh/umu6ZzfbXxBLzvJxNZucz67DzIFVbWzVmDpT\n93rntNe+yHxul7aZFnHUMxcwj58uujvzOWCHo3BVRi2Kcru45CZVpwwKh6xh\n3yaVb1M8aqPoKqgCKnkWBHIDp9EalkHZlYIYsdG/D2aS5I7LKNJegTAKwsLQ\nkVExyo7EzULS9eUMKOzQRv7NhUc+ehsLr/8BATjdBu0c+6t4JITzF1ROp71P\nAg/+rjabHVYcX/8JaiR9JzfPWKCUNtvy2YSn5WQyvx5OqV008qThP/MaPHZt\n6JkTyFtpIwYgM81vuswtJo76KBvUIkwSTtpRjy3Rp4IsyTgEJAqB0hrmZYXC\nJGvD\r\n=4l5Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.1_1625569690971_0.1066289990051783", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.2": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "00accc69a391470710079ccdf111af314204f2a3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.2.tgz", "fileCount": 30, "integrity": "sha512-njdfSZrMSVNnNfGjELUXQwEoYXU8NlT8l9S1ke917zMsKpDYgv7P9c6uPPcqAew0VqNTHn80xKNLLrEbFGGi3w==", "signatures": [{"sig": "MEUCIQDsYuZ7yVOSnyikVxyOvukd5Gpmm4p4v0u2SnTE+VO1oAIga3IMrJDGRr6B/xhszJXZ75/rWSvN8ociPBROfcqJ69M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5rHBCRA9TVsSAnZWagAAoSgP/3X3HwgvOTyJv2LiwQ05\nttI6E+bAbyj74KeBmyTyRvW0OWFSPh58uYONVjDCZlKsEKRq/EhTya0BBJTw\nYCO0QlmE4q7bEu1P9+2BjZXDEUSp3zGoL18f3vhOhk2aa2gy3T/Tho1XPVh5\ngre37c7eXyllHu76BQnqHaynkOtegzFNcLbcwpUvQdjSbKfOeVsRReO5HHJD\nfDXz7sg5Vj2fi09u+FbETamMplg4YUEJt55wKmOIXcLapOf8LGtz1/blmlRM\n6opjpP74jezMFRJPlq32m8/dT8Q1dCwy8Wmu82nJ6Q4U/tGhTJcPMXIvPii+\n13Tc1sWf1wgoIy37pNNFAS9uiBo2ad/MUM2xU9qHl0CVbBrA90DtSRwatDh5\nOqKvZXFrsZqVN055Yl8kivrjTB6w+R2ODOndr7nsVAiIvkSEK1FTzE34rA9C\n2oucXJR+WNyJhNwLX6962gF1KYRT3lXybHkuxyWBoq98X2UZxQzuZSN6lM5F\nRwQtjXJEI5MZNSfhmJZ/Q92pEvpXPopM90m88WSoYczemB6C8gTTogrYyMO6\n+szwLDeYlgg1zHoioiOZoEtDDGmwnCry+rJn2zNjUNf+K6WO9YplHRdvd8fG\ntpUQPRVBY3OE5XKzMRwr945k+vMQqHEQyvUlv9hel93wthUHx5rSuRNgihmO\nXgZK\r\n=97Tt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.2_1625731520827_0.5860376143879922", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.3": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "3b14bef744aa72619a143697a271571a3b5f1093", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.3.tgz", "fileCount": 30, "integrity": "sha512-IO119MNkjTVIAlPZSZzhkJi8tgpX4q/nK9yEW4ROo0LHUgiycPmqaFGN+rkMtLmCP+tIRuYrMPDNCw7sYLmZHQ==", "signatures": [{"sig": "MEYCIQDiuFbqDYv5U0aKgNQlNBbAYpy7nTfh59QMFpUV9NzrdAIhAJqS1bNjp+nZlXnHOyOIFrzpuIe6ZA5VUKPksYhUZAzc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5rrPCRA9TVsSAnZWagAAyK0P/287SPOABZW0TeqhiOxa\nQQFecPcj12DWFt+Pm4eC53QCjIR6AwJ2SXWJNh4gvETAIsqMZTIQgV2AvNzS\noUmHNM4mew1CLZO7MKGgtu/jNIeWqFidZ5YP/vkthjV5RyXkhSGJJOH7aYiq\nHS84CvSgEEeMH/kRIrukhjFuTM9Xtq8diFsV0EycRa/+bDwcyTVErR8ILJLN\nC6gBjosdTep3d/eulcNQpbMCo5pYa7FGPCi+54riHYZcTgL6XNYtT1SAOUs6\nebNTVPPnqnsHttYQGvcD2IrZUlmm4dp6hPX+0puXHAfoLBIBgem2K0s/tOAa\ne7gem/vXU9abrBvkGasC8lvNIOV5BIhNG54nnz1YdlDc8WNH+l9QB+IvWhnn\n2H27LNOwGkWneIQ4BGZfZDDb4/9AsdyiwlSZX/Wg+m/uzANQOq0F7RkHaJd1\nW2VZNH9i57x2WnPC+CoQrb6ak0AEV+mCbc7vWuR5gxo340sDXVTQPqkoLhRE\nGieDkTxfzvC0vCsFxxp7lILC3AH1pnEOk3lEPhYdG7awHB5Ys2NVYvAmuGyE\nwS4qCVITHKgB/Uqjo11e6Mz+MbpTu9cGz298AUbPjlO5EnTBVH1xh+yakFI9\nmyTitfADz7OMBbsnqK+Y4mLy3MGg5jhqATAs8LdVtM/GiKJV1FcN7ea9AAL6\nqK1j\r\n=1dud\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.3_1625733839269_0.2954845818224714", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.4": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "8ae069587b6cfccbc2dbfef1ab04b810c65a17f9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.4.tgz", "fileCount": 30, "integrity": "sha512-XObfMyA9O0sXu8J21m9sEBABAD/acOFG1mUKACk12X0Yag0MhS+bhQGZp1YudGjKUYeu7myZyk0UDO5pBPL8Jg==", "signatures": [{"sig": "MEUCIG+45gErZFOnJvnMelh3Px4lVykrCSm/hxThSZwwi88iAiEA7oAMI2MaQaLIjSVNGlg7d6GM+oqlX2IgWW6IH1M2Cfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg67ZYCRA9TVsSAnZWagAAiiEP/RRrPZOLGUMA+2qvVE5T\nz0kBtfSqMLvdrm9Y4ii0bf6X9uWlRvYG3lWnbDwCyNWFe5aBJPjh0Jm3j0Zr\n47FkzDBSUmlCjwOU/KQVAErAoUvAOnBm6Im2dcFpemLy2tuFjWXeiFX+xqz7\nj7wtX1KCBQKavbLe+4USDkA7pAaev0tJRC+HUqsVhVE4ERWxJMtcjW98NHXi\nQfndDdx4m09j6ZAl3DPQs1mktC1tYNCv0AHZmIxEK2OqeeP+7JfgXWBfha3s\nDh4HkNtfeKF8pTZSenMrIquRRwEdd2m68B+qpdhPp9Din3ApXh7ek9TQuqZX\nZhokPb+IdYU2G4LVwLB6FJZDkZx/MebZPjT02VBkzfI2+RE1hAHFJHiKATCp\nrr/cWFpBp9EMPWQFc/97AFlqKNXy0t1nAnaoTNC/yKd+ilOa0KylCa4KDUMM\nSZJajERjiwu7VPrDocs/dgsGtpLYLFLsmfi6uJkTX1G0OHsUiReW3NqZ12Jp\ndSP/UIl1Bwmnx8fwXnMr/hnoBdtisx36NDtoGqZ8/OUyVemh3fjxK7JO4kzU\ncIV75jiEWK+8Mb1pzDBejzzykaFOrjiCdRlTdCfms+JhUGuv0VnSWufJAxQq\nybFe1q3iWe97HT9ypnaETX+UioeyjZEJlMgPO6mYfo7EF8v7PKHarmQIAWZU\nUbA4\r\n=+kKF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.4_1626060375946_0.8383817870002948", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.5": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "fcea37241fe555c15db407ed00b04dcf7a560743", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.5.tgz", "fileCount": 30, "integrity": "sha512-yIirWR4ZARY0qpL2jh1sgkdq5MBmrarL89SUK9acmFLYGG/Mw7S1gZgin9Hup0ZESac48ks5yH+g30KEmGLHHw==", "signatures": [{"sig": "MEUCIDpdKfbAf0/M55dTxVUHckVaRE4BsQMQALFMntiOJWXvAiEAh9PZ4Ta2Q/IuOIgnXJ/BQJMmipv0OFJRFpcRYUcOBus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7C6vCRA9TVsSAnZWagAAyloQAJBH3cq90L2aCbAJLC33\n+jANdKXqeneT8xHuqyO9PJOglCWVxUUMMUHKTDyYBWu77tVwLVGRew4rPh00\nuPFj6e3nnPRuNS4SgbspDhcFAKKQfx9Jiatlg2q9pcRgO8y79s+7N+FPLM/b\nZL1p9ZZNW6QuV/bnBiHLTJQLN99jlbdkGCto0QedyTNCrcoEEoSXieS8hJwB\nc1GkJzfYBcTN+A3E1Vfea0TnHTxpHxyGkVjKeRCntmfgjnfPrpO7enec1NYE\nBir6d11+UDSHBUmj8xCl0QkYPSxbavK9Cy2CrWW8gVhWZvfCIHZbd35BWLtk\nW/QHK+jn9EPVPdkERu3M5U1ZimEwIZ3rgwU5rGpxeH9IVOCJmA/QrzbO7QBu\nndVcgICNqfF5+1svQ4dLG0W2CaJfV1H8epOspVwD4r2BZyKbAVcQne0ETYrn\nzpn4GzeEICnSeKEOkCQoPyRO5opzytGRpnOyt5ICLUaQlmIYO9GodeBCbLII\npmi9/vPxgFywkAdEu8tjYsWrMQl3Pmlt2z8v7VdZjqn9JOxdYmXzasfIItF4\nrGoQP5Kjjfcbh/cJSHV8SNNb8G/IwcEXNROFqQQGPIX9bdCrPMgKBHAj2g6m\nwloSRrmMVRzZcgZJJVRgecUZxWweXIRcOpkN22C2rLcxAWIRt2CD0c0V7XZ0\nOeJa\r\n=7Ah4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.5_1626091183122_0.8970646696652076", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.6": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.6", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "c908f646f543d1960907086c2167ce581ffb5b9a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.6.tgz", "fileCount": 30, "integrity": "sha512-ORQ/JE+u92QQ2ktvu6K4y77F6Vu60Kdb1o1ARcta0J5Qu7DlTUc6zUyovHa2Ggrdypq94CpqsE47SbYNPTcA6w==", "signatures": [{"sig": "MEQCIHgS4Xee1AXwp/l0Z+AI1rwbQnHLVqyliMClJ/M1NU2YAiB4DmOW42mkIGGn2xfXeWZUnKIeZAOIXTh8wEaQMi2bQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7RYuCRA9TVsSAnZWagAAU4sQAILaNTyomvi9nDCW1Ej7\noc9wsWLqnzNrgWtpjs5sCSEkmCm/PT+zwWZ34rskF/qiJW+eiYu7AXwXVJLf\ngI6eYyDkYbiztkL+JqTsor0roz9tU8er0DXcWa5/FynocOXNL8+xKJo6r2KL\nmD1875lq2G+B/ot56JfzqotCE/0gIxERfsYR38RuE43or83+my3a2DWLj+0u\nw+7KPouWkHaT1Ajzr5f2nlUg5/+Rn7S0Nldiw2NnQ0hnG9VQ5YgqEq6dG1LU\n04M2J1gpzzD5KkF9OrhfVYWDmEJ9uMKL+IChRlvQLhzCJPpkhXOTzQVfae5T\nDZiWs8VEOs/lMiEeAAwV04VUjXKIOoTUr76EvipdfvngH/I/j8xBbsazfnlO\nY4phOiTzsY5rUho5lb2zYGW6UckyjvYf6LhysIPhFhGlOVFaO3PMrlmE4l/D\n0SD1F9KHCRx2ClMYaO5lnOHxCwnfSXqmI3bWmUYXKVDVQgpQL7byNs9UTcNd\ndI09dd2+p+aJViB0LalJnCyX9VoRU2dMHoB7WiPMa/NX4mo+sD1Q2EGhLTi6\nnDoEty8jaeaZz6O5Ov3VxD3FKM1RJdzQFUE2OgBTbLmoyTY3pxSbPEhldQYA\nsDgiTXi61GxkVDGo6Pb+HXb1X1mpMp5+0VL89kQ1NT/W/2T3YSg9Dptbsgez\nmBX/\r\n=TwRP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.6_1626150446489_0.6172382600877568", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.7": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.7", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "002089636b6989797a05379cfc97cb3aadc0952e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.7.tgz", "fileCount": 30, "integrity": "sha512-WofuDOOwWP0V25WQ7lkkFHhbbqmz4DOHFlxjCpOex6nKcbyTRdgNeBOvRUxRP0hkT/+hlSAJXOuxebgvR4A+8g==", "signatures": [{"sig": "MEQCIA85KmvAhdaFkgIJ1GMX/vQLQILzdd2lqyNGI42Nalf3AiAXMv8aYC8HxlgmPIX9b3bU5YU4iaTJ7Sm2MS3APISCFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7q7bCRA9TVsSAnZWagAANOUP/2lw5XWlJluW9WNsvLqw\n30TmsV9pW0Tbv0CnbeETfBFjXFWgRpK12/IGj4hX82YiHPBuhxdNDbN4gyhN\nerDleWCzRve9uUL5/Bmc3nU/BRc34SkJIhotkz0kx6wSAfeKV1mOz6cCoDFw\nKPo6YN4ZyNxvHBrsyzwyJIRwlX713y1nRlWxNputGXVCyFkrRCp9gu6NbCeA\n5DKwqElazkVeGzhiI33g3ZTcdti2vJy0untzRBCZn6Z6QOxtyLDtYhkwwqmw\n7otGjDRC4JbZK3eGsNF4a7a0aRha0/53vHjPvgfwVVXSUG0o6zsUasw7Koc/\njkQmx9m7hrMSf9BzWzfS9lb8Cj7QhjpI6O4XNpv5gUO9jh+4GKmD1Ndc1nWm\njqcfVef5VmMSiNPdr64dvX/LYPYYu89AjxEoEt9pu7KqO9+ymxKwKCHxwkv8\nThrJblDDgnz13gIXjXEpOwdJ3Gl355/5Qw+2pH/XA4Py6kZRTY//+S0LRKtI\nrg77TcBd2MosGkUktPcCM66A2GSs54Dr1sS2oTToynU/iB+r5Lxzy2GY8ipS\nePIP8MT7dErhLSImEMM/ffgCE/LAiHUWpTy03Qk4ei+rhRaRE6bYXs504o0d\nvcon5nxtVasCbgb8lzNutOyqfMXQhDWDmP+oVpewPSm/rgEmAge6S+D4ykj6\nNdqO\r\n=yLeD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.7_1626255067419_0.8586995021777044", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.1-alpha.8": {"name": "@cloudbase/cloud-api", "version": "0.4.1-alpha.8", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.1-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "b69dbdfbf7dc711433536ecbf5b2ecd6b5bedd41", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.1-alpha.8.tgz", "fileCount": 30, "integrity": "sha512-cnIwCs/g74Dp2Bgj4GE5aN1mFMAc6A9w//jNayiEtzmkNRe0llKc0tZzRK3LXR1LJY3HiNgxDMehjql/AxtzWQ==", "signatures": [{"sig": "MEYCIQDvpNwdFIzju2QsU/sPlWEy/50g1ak7ZsD99EwAQu09aQIhAKnk7i4GA0CZP1FAmLfthIF+W80LSpSqikVgSyEpEfYu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7u56CRA9TVsSAnZWagAAo5sP/1yCo/WwZA7IjdNtGs4G\nRimTBOrr1EzKWWIMo6KEMDnbUmU+5RzUgZwu6Z8pTN4F8JX0S7Px+23DkIZw\nKPepN85koLhNGps8Ao+ABK0HMIJ30DLrJ8Waj6u0fMEGI29vd6zdgSkawi/y\nJFMy0IgmrJB9KoxcFigiFy8CbCiZSHnvsdt8cZD5b8A1rKi+Q7LGja9hNeql\nkpG7YxmNmSmBf85LRJjMbYtnfb/N/F0ENRaaRg+SyJRmbTsNdvYe13ZingsJ\njnTjHzcCSdSS5MgZFqp3ldC9qDaLkeLtgeQVnX2z0qG97R21h1JkRMnFbKHU\nqas4rUTG6KuXnxYXFgK6aLlG7/WBvOXfsjEfW1yoqykkfBSg61d9cWCJ13OV\n0k+p2Gmk5eWKT/PsPEgBwE54Zdx4JOdIqiW9DHOHVWuPiwHLcs249wFkGH63\n3quJBCRFFqExVZQTSYu/qiqdEXwV51dqLxI34pr7pfC5vlIy0vlmFgT9P9s7\nEpglJuNzPy8PVThvfEBJd2P71w9UJLLQl6JIoepqcN3j830NSJaCgZyUhnYJ\nxIyHzbc2AWaEfEDGedMVtWlVJO57TL9tjPxR82UyRtH+ry0weaVXA3MYgJla\ntkdJHv4YZYyEKUs4pZb8GL/XeJhXq15V6lQUYBMrIz0mntJt1O4pc9ZHxdAz\ndsOs\r\n=oGSf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.1-alpha.8_1626271354495_0.21811889897394443", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.2": {"name": "@cloudbase/cloud-api", "version": "0.4.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.2", "maintainers": [{"name": "fengkx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4007702ae7a52a8632df9e0254518892bcb4d98f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.2.tgz", "fileCount": 30, "integrity": "sha512-Lg0nbVzb/1lXRfJsiXq6UptVixQ3DbAONqgZduNbSycKwIFzEXIvbwmxNBocaZZZQXfP/tbtkstQJ8UpefElLA==", "signatures": [{"sig": "MEUCIHsSDT4EZjM4TFeiq4dVSqGI3czL/18H/TKHuh9iKIibAiEA7tdJ8ijPtrpWdhDwXW46AojcabDY5XEpFqTt9uPUL5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/muyCRA9TVsSAnZWagAAZn4QAIzyNln1XzwaoJk+rQ5q\nWvVaayF8rkJmYUSfv96JrehczUScZjr0YUp9ETzYmpT+snc/+q6jaRBVmJru\nQsWMIK42wbu5/Ju59hNLhsXkfwK2UaCJy7lNq+Hzy6zFxUj2mqlcGHieghNm\n3UKUVdpV+fpHEvYySNiIWJX8cRX8rsMpo9oEIRUttKCRIfnT8BrhjOpxw6/C\nNuLVKwx3v0Qs1Rr671gas6BVUkMz1rrbZRVdO9YmYpn7+8NxSqpH1A/kOrlI\nNTQ1p0/cohnIZoteuZajw8d47rKgMxvcfTXiRxM+FA4YkISajGYWpOH4xBzF\na2Ef+9ZHGWCjnTJ+P6YwvvgcBgQFq9pePN0q+RqCTK/9ESaN4B1UUWaBG3zT\nGZ3b3pAo+xKpgvJdV/OKWUBKyKkP5BsAMN3ur2BhtpkpkX4i3uy0Ua9R7V+2\n1hcmqUnMwMqfayeOQtygFgsV4CnTe9mmkl8eXJakO4k+kFminIaE61PEZbo6\n3Mm8vLzhIXFsJFVI9i3i5w260LWQX3SMdVGyvo0BQMEm939wQLYcvpbW+9CI\nEDS96FmOnL4UEm14g6sL/tSXIWy/IrX7oAsvoKRdzRvB78OVrd5/uMPpHpkB\nRVy7fy6nIKABxRNC0vdgejvki3GGQZW2gkFZoWwFMw63zxOhii/gqWRfzi7G\nZkX7\r\n=Xtl3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.2_1627286450321_0.10854477273913488", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.3": {"name": "@cloudbase/cloud-api", "version": "0.4.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.3", "maintainers": [{"name": "fengkx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7f9ee2245e479aa54444b77d7d41d3703db67374", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.3.tgz", "fileCount": 30, "integrity": "sha512-SOYUrrJZ0MrG1njFzoSSvskJiL+s63BXn/WJbdixmZz7vC2S8lrCDoVzhev2GYMWJSvAAj5kZxhcg9qszEeWlg==", "signatures": [{"sig": "MEUCIFSEhAFiD+Cw8gzdKc0yG12tpiyTISA81td3aR6+Os+YAiEAwVvI7jFSnPnpxeZujwrbk0ubs0FsYr5ZgTzeoMl4H1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/mwcCRA9TVsSAnZWagAARMMP/3o5byUh49rKel9dH+5x\nXlXbSOaJ5HoyS9qr2q6UJT3cZ4BX1U4VW2eahOcjdqJ2HhK6aOxO13RWhdAI\n7ol90QKTYKMGrIJddeafRMBHHZf0WRxjIr+ZylU6SBNfJ7Q1mUd0hk1gkMuM\nYMRj/nPN6SFK2EXccUercB6KmKBH9yi2hzED4n3Pfld2RpjsplYPw/dLUzla\n+2FkzH27LBLJ5pNNFvNq/PmXcnXZWtzBuU3Pcr4t/sBbWy/fIul/QKodG711\nJ3qR5GTgf3QDOpb4cs7iy2/j+2mZDF5ircpytw3VK+bL6cL5RdXQLA5oAuRZ\ndr5A+b6tMPN0MjpD7SIZVc3bR23tOYLAYFSt8ZDoFBRmWnsyvgMBhKFZBTqU\n83JeN59ll76uCPzeXKmDoljccDeRLn0qkFRKqIqgpWHZT+2VBMN/jMlenVtn\nJS618ptMq+biK4yrgNxh9YnpswH4gSgXnq2KiFj8lEQWoEZa1apHJylNlJgY\naoQSKN5gn9s0D3DfTL994YL06tNvoYoOChnxpB2w+tolnp0udkwKI+sMYPyV\n0BPXialRzgIVrw4c6vID42CNaL1FUTgw/6pjz/IDM4GZ1MyuoYXMUSBC58Oq\n6Mavj/TsOgC1ZyBnuXyeluaEinBn7yNxUm5QSPoHztF7CIGQ1AQeHhvAOVf4\nyVVt\r\n=5C/j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.3_1627286556547_0.1821296835284243", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.4": {"name": "@cloudbase/cloud-api", "version": "0.4.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.4", "maintainers": [{"name": "huang825172", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "643a18173017a2e538ae51331953a5dfcd195105", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.4.tgz", "fileCount": 30, "integrity": "sha512-p8ovoZzLUG+BHUEB56zCCm5K6XpX+I2BmKSNFfXYrKEEwkF0l+blaWvDFuxSSWJx86YHgjpehFHNq3wiGnSiZw==", "signatures": [{"sig": "MEUCIQChqC/bXCtu1bSqsygQDK6bRx4alPRVhRWhI5vtvBYHLgIgaFk/vphqFKxyRyTnVju+0PZtA8uWle+li6PN4Lf7olw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEgOdCRA9TVsSAnZWagAAzi8QAKQGMJ7I0fhyWORlf9FX\nKC0dxMhlSSrWEZkfwP4bjtwLvF4luPXYgd69CrKVY3uuIJjcX4CGUDt3lR9H\n8bgBhITMLO6/MksiKd5Qv/x20I7oZtFcvmNXXvOCyk+7vVzNq/Yt9wLUZhPe\n6+5Y64j79t9H3Kd3MtpzCCWbQ2MDfYDG1wSra6LsZfKZ6aHS0raxbGcgk0IF\njA7+NIp5MUmFMpePqGeTP8yfdgbGpKXL25xSFWjfmep1RZgo0ERd1OymHkkK\nZd0sVOdpTxvVifOJUWxUre6VtQZIv1hCK7UzS/gtHii0H+iVfZ0i5136YQNq\nr4ab+gBLUXfSKKBQndg7UJiqk99qVULUo/oxgWUKfeF1Lo/C6bjNW4yzZVB+\nvK3a2IB0uUgjvoiIiaR920rsipUF2qQlD/I05SA2E6d4Q3L8byR1lRibUEGp\nZAmzgcvbXbKeXqqaF2HyqSAjxZxCbqfvu5Hol7v1U6PVW/nmXCYp68aBg/CA\nnhgmxvQWLN/H/onCgwreHlkrNOHngQnEVWzc/XJnf8BTY7zjgqYYklKOo/Y4\nBZeJvl+eBdApj43EVBPa2DDeFLzLDxmf1nuRNW7e0xN2YjKWtzFsLOjIamL5\nUnYudBLZExRrk3Pie8Fe4JHt7cWFEFlUrjNtVlN+z/KbwsLprDhWWEFLdGrB\nUz7q\r\n=vBps\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.4_1628570525257_0.6047444339352821", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.5-alpha.0": {"name": "@cloudbase/cloud-api", "version": "0.4.5-alpha.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.5-alpha.0", "maintainers": [{"name": "huang825172", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "zhoujunpeng", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "815a9a31403cfa3c71671a943a107726c01a1528", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.5-alpha.0.tgz", "fileCount": 30, "integrity": "sha512-l9NA5G3wsANuwucmF8AsqPX3eiI/Fdpb5n++rJVhXaOzBUB6vtaxxuD9CpZKE0Kv8Vp7jE47No7ukAzBFuSu2Q==", "signatures": [{"sig": "MEUCIQCudSAYNCvmrjOIPTwQFy98CId9OQflwToAn27fXB5lmgIgeWgomTooBi9co/OTh8UXLxy+7C3oCNHZQKRosWYOVDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55031}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.5-alpha.0_1634206451184_0.8736254945660067", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.4.5-alpha.2": {"name": "@cloudbase/cloud-api", "version": "0.4.5-alpha.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.4.5-alpha.2", "maintainers": [{"name": "huang825172", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "zhoujunpeng", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "1b03ba342a91ab1d805a4991b732977eb74aa367", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.4.5-alpha.2.tgz", "fileCount": 30, "integrity": "sha512-oDtcSaT4JlUImkIfBHin4vEMDsY3V60hun0KVbZoFvm5kBjsCvd+ph6lQFQJ1OS25vAJl5ca1MZxFRWW55m34w==", "signatures": [{"sig": "MEUCIQDegnH+MsaAR1C5i2D9/Tr7t+umFa9HkO0jKoSYLpmjVgIgUObEag2zoOlHWZRFqslV+OoTWbG0YnrENjk0py/Rx1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54802}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.4.5-alpha.2_1634631592863_0.6474329000615215", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.0": {"name": "@cloudbase/cloud-api", "version": "0.5.0", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.0", "maintainers": [{"name": "huang825172", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "zhoujunpeng", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "cc6884ea67b2ba53dbbc95130ac28912346e504f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.0.tgz", "fileCount": 30, "integrity": "sha512-zVeZQLzzpj5GuHTZSYTDq0Pys73bWp72vXdIOMCes/sflQsna9D+z6m+aelxJ7/OMNiIiSaimMYn/LFsTtkZcA==", "signatures": [{"sig": "MEUCIBmIyWMELdOIeXdnsxRr/1WMZXe5bbCt6ffyWuttrpqRAiEAlXFEWIE+K3GsrdhrmZ7mbr3nZ7ShuihcONZ+yfYjig8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54794}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.0_1635335960412_0.6447464870337973", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.1": {"name": "@cloudbase/cloud-api", "version": "0.5.1", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.1", "maintainers": [{"name": "loong<PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "huang825172", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "b47db0c6dc57456301a48e234efdeac82216d288", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.1.tgz", "fileCount": 30, "integrity": "sha512-IgfKEkqtT2dskm0H1L3kvbder1yLqdQ1oILP2VsNs2A8rx16JJ0pa2GCldDno3n1ruqBFVYehjvxjIXnaoPd6A==", "signatures": [{"sig": "MEQCIBSPdUsPguefBNUu5EVp0P6aRyRaTljSOYazdC6b2ynLAiANkw35oU4u8xluPwjg3phnim8HDN5fkjcPgKlG21nsQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp0stCRA9TVsSAnZWagAABy0P/ivs/kgnKqHCkuNHX5Lh\n7yylJqWXzRtDv0NMjoCN90LC22r0gigOZJJLAEM86zkhavIWM7EXediYz8Ag\n/2UJYM36cT/srOlKc8SLz89WQSmCKrY6ec84KsBwwa6JG9QYyG8o1LIVZfp5\norMjUyDgPuSslfTA+JQwTytJP6sV51xNGT26YmR5WSQqYDpTtuxppxfGulVy\nm7knOiKAQhCdi+mmFVrt66MI4QezI2i8B1B/vi5NfUDM4ei0g4te0jd0wIdt\nzbotqlTeaiLd9PuUUoHqY4/TUUq3PW8WMVMF+ns6CosO14eSkIC9EwKInlm+\nuCAQCFoDs/ZRvhEAtYT+elpMXMdZ289JBK4JcTxm1ZnWmwH3EzLKEnvHvwAO\nsIOPXtGLfN5xtdvtOc/NBPf0Xx+A0brcgNdmNX8b97nmJjWXqlTotYjsBQAe\nyUdmS2/nH8gQjxiUSbxnzQMjJtd534zZlRd0ELR7uBcOkpsni4tyUy0Lrz9f\ncimqcX87qfIwKXFFpXCFvxBMpsDHMYdDKhJ7EbGIe9QYUmF32ioEml/TJszC\nsnHHv8kRCQwaRFnAWmfXs7FZFplEAVnjqr5dRMfFavjJG429ogh+jt6uhELS\n+pqMuPkGqfG5sW9nz0eGI7WKXA/iIQtj5DiyFX2hbS+moS89QpheVLpSVAGa\n59i6\r\n=j3gm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.1_1638353709123_0.8465035191263546", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.2": {"name": "@cloudbase/cloud-api", "version": "0.5.2", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.2", "maintainers": [{"name": "loong<PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "huang825172", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "9031a6a089467bf8087127d8b6118ab6a93b74e4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.2.tgz", "fileCount": 30, "integrity": "sha512-aEBNS377+b4cs2vPqlp43ZuzeB7K59MCJno778SlV8cROxV0BPPDpZVS8IMjqmUapbXt/ixRpoOmdZBFYyJimg==", "signatures": [{"sig": "MEQCIHqoFZ/lmTS2Iy6bdhKzFvn2ncAinJ6rgFRvdtA11P8+AiAmW/YTZaTPOWZWn0S3nzb7wPaS7/6tegVdWKbouD4GVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwwkoCRA9TVsSAnZWagAAFrgP/072sMlExf7kHKryGVJ4\njq0GJQlCHUpVRG+tafjE81VHwdMTfyqaoqzfFQ8U6lQAOiuegftteApwB54j\nEyQl9Ir3H6aybgzPcXBbyqdC/OQRnBORJVgHgNcxVtfGKs8UJegVyWLJZsbQ\nHnVoZxVm81EaHjta+bh49nrVd55GUOux/vSQXyRDUKeZMAd62BtVDSdn40SK\nIPYQMviYop+YOL0p0d4WigvHZ7iozurM6pHOX96vd/OGRq1Gu2/FDuU2Kqu2\nbex4rwpJQZnZ2Y5PGfZB4S7yrMNTGrufYPUyyt6VCUgsGgoXy6yQGa5CJq+D\n5PAUc8Xe7m7Wujjz5xe/VNzBkdWMaodwu2MZVk10HtEVe4/uCSBwZBlStW/o\nAxNlzEuJsw+b/iMAFHEGyKrWd1i8QHl8fQMQHSuRtBtRSMS8MH9UrvqZNyev\ngdVGmrQhlOTAmSvw87Is21W8x/V8a6pnEHPll0INpmOxGLX2pO75j2i16A31\npPDb+91zCa2RxFgYBXlgi7j0K2uFzHVNIgwQJXlztaIba3Pg3gl3FED8ZV/c\npBQQU2KTo2NnZBSCqTVXCqaWWvVOg5s6SO49KEUc/XgnvOYJxy7rTRBD8Isp\ncn56kFutGLGKY0ETOkQk58v0+uunNDjuUZHjxxuhuSu51RzvT8x/p+w20EZO\nT5F3\r\n=siFg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "evecalm", "email": "<EMAIL>"}, "description": "The cloud api request package.", "directories": {}, "licenseText": "ISC License (ISC)\nCopyright <2019> <Tencent Cloud Base>\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.2_1640171816454_0.5486586939733142", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.3": {"name": "@cloudbase/cloud-api", "version": "0.5.3", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.3", "maintainers": [{"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "9f0ec5fed5d8d7baaff39d4bf1030a43ce462e73", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.3.tgz", "fileCount": 26, "integrity": "sha512-F1XXOA6pNyj8BhrgnFp3nRvmiaOU7K/0m6/Fvx63ABv4ie1HeQMFsxiPzMma66aCnjL0eLd1a7pgTQDer93B2Q==", "signatures": [{"sig": "MEQCIBZNRJuIVL9T/Bj6hxa/GDcvWkiac8OC4I0Kj5sEIvmHAiB+iLf1TTFmC9UCFu3dtWjwqNhcVYMLIjxY1Xgu4lJSXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisSVfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4Dw//bJ9wzvNZ+0mLy2aTl5+ApTn1tiSiT+3vyS8aXigtZ0V3cjrk\r\n+IjVkJSjGydz0FMRvLkn9sl3N5+lJQrfGvcUzXvMVhL1IC+3R5+WwUO+HEHB\r\n9LgPxpICAlRgz3oUAfYm6btw/8iELCbS3b2qJLIOdpTmTHEGuV2xhma/jAhY\r\nVDZfS1J9BXRX4KTBvCujOXvAYTfpJjMpGxrpFwSzIOaHuVKt+KijlgQ/0WJE\r\n5AdDiwx3+DeE7ye8lnsOGltd1Ohcy9XBGxJEItNd8PSgLMkO+x3zCY2Iwq1c\r\nSPTgEBBYiX2w+hZpS/2uESCWGaTvIKf8BzbaxQ0jOP3F6UAP04ZehFhx0tiN\r\n7fYN5O6sFQVovp4Gjcz9y/NoCWjy2gdZ6GMImwycIO97af0x+j5/8w/HCzgk\r\nYm+QXR/F3V285QP8/b+lGfA6/F0i9GTighd9ZNxRFKiwbHhwfQTbj+qxipJ9\r\nu0Mymf9XTL4ssBje/KAy8sGpCOlEnYCvji+hUARWdUPY/O80hKRNNkM2Y7Ox\r\nCM1keFZV5O6Wi/LylDdWoHQ3d3dy4fdwMJ3PvHUpBIjC9oqib0M2OnCoZYkK\r\nQ10T3z9WDdHDwRJ3IyI5a3O+2Psy8/SoNJyqhq4O3LCw0asEN9zpZ3Jyr95N\r\n2JOJDkxI02+7fqnJhYKQYtorZ+ArjUfhdv0=\r\n=zmFG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "953f0e540b1bc41d5fdf25fccbdeae3a00027919", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "woodenstone", "email": "<EMAIL>"}, "_npmVersion": "8.11.0", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.3_1655776607022_0.3188106233163184", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.4": {"name": "@cloudbase/cloud-api", "version": "0.5.4", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.4", "maintainers": [{"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "e578d103c05dcf7dc1f250b01d3dbda732f8749e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.4.tgz", "fileCount": 26, "integrity": "sha512-/E4IZw4hp3ihW2MxIR9usCzJAydC3YH7x5kFjkeavXHSNOxsKqaNVNzwgESXR5La/2AeUYobGVO4rYitpaCuew==", "signatures": [{"sig": "MEYCIQCK1gaPOy722BC3AaIanThkhW++qQilU/lrCTrvapZB2AIhAK98RiX/MdRtfV0cJgm2PJ2Sb+BLCi9irHZtTsjUaOuQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisoh7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuAw/6A1lrDVwdG6BGgSvyD945I2OTM1vR3nxnmHC/gPn/2/48gItW\r\n/yr+BugEp7aeUx2qzk7s4As1jfiJv0kn30We8jAAkTVqpN4Px1F21Lq81OvQ\r\nQLMai8HgSPCOKoHEvI/55GmxP0OCPGuXCo5dnZ5RUSOCcvvvJu8qRithA0rA\r\nB4Yj3L8uFG5X9onopoaUx0o2N/HW7ckUFHT3D9DJdGxsd0dXf8232RZTsXDQ\r\nbYBY2ezEF7wqc9KtxqmIvnuHXRM6ifU03oejX6mUOkAOWkojjIEVK2NgzMnw\r\nlH2dJwhMcGnaXo67E0aFKopWnYh3+K6+ZpCrUdcQ3UxzkX02x1kb4wj7EguO\r\nrZiwnm743BbKh1g5gUUiqOUS2+mmThfC3sBkcPU1qb/weEuEXz/xMZy4ffnk\r\nMTCeKLf+yJ19zDy1Ar1eY4KYa3ThW72zSFxYo4Tw/3BO6rO0qw8xaiePbhxJ\r\nu8RjLk3Tq0SU3Wd+Ftzikp5HogAoPrskDFM5vNx8CbLDDHJvHPK7v1uwDZgM\r\n5Vqu+6Z+c7kcuR8qXAKY85MvPUudOQGD2fq97Nm+o14atw5xfVc1AqrsyubF\r\ni3KvyfrcwSGM7BqzeER5rPdR6Ioqg/UTRxLBe2m8dcilDPwxdI3Hx+IzSgIv\r\nzJcCn2QnSDeYhovwWU5qW1lllFEasYTCBsA=\r\n=PpWn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "e86b58d3b55ba8064c66027e649ca40b98d11124", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc"}, "typings": "types/index.d.ts", "_npmUser": {"name": "woodenstone", "email": "<EMAIL>"}, "_npmVersion": "8.11.0", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.4_1655867515401_0.761469152424779", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.5.5": {"name": "@cloudbase/cloud-api", "version": "0.5.5", "author": {"name": "<EMAIL>"}, "license": "ISC", "_id": "@cloudbase/cloud-api@0.5.5", "maintainers": [{"name": "woodenstone", "email": "<EMAIL>"}, {"name": "ceoyp", "email": "<EMAIL>"}, {"name": "xbasesdk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rosefang", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "greengrey", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "evecalm", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "d6f26bbe86fd3cd77e8f786653189f097ebfa13a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloud-api/-/cloud-api-0.5.5.tgz", "fileCount": 26, "integrity": "sha512-skNsOirrXPmQjL5NZpwfhM+kgEljYQQVetJRxOnhjeA3HVadNdJMejeXoGjOP9zua6b1YFPguVkLv+cYjw5jFg==", "signatures": [{"sig": "MEUCIEfiTkvHihpvNCLrBL+X5/thyZms1zFh+UplZbYDfO/xAiEAsBqcsASEs/hJuf/mdiJCgyN7ErbZTk+1z8mXLFAOH3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJistGCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNcxAAh/gRiAaJrlywn1MyiKtcIq+p/DeGzOUa/SMhMUlZpObO2adf\r\nvhLh4hCWIQNJjAgkpY3YMcDEWNHIxs5tAoFunjTX2n01wRZXSv7yEbnruuRY\r\nbgvcfu1Py9sa5kvuKkJipXXiHUXsDb8x2kELoaO+OofYJXtDT9L4L1H8BYiG\r\nfGkc22BLM7eNKfmnFTbURStsRUu6baE0xUs39P9tC6AVIT1uL0XaoeKvXfbF\r\nxTtYniNcB5CeOMLmqjpU46Sg4NkE17CiliR0PbP+/WzKOWfX668N7C7OGMYM\r\nx+qzScUECAuvBcUY+dwoq0A0+8P1P+jb30UIMOm6Ae7rwBJLda2rtCIi3veF\r\n3fUkWKuvoZZsqQiUnH8RpELWTor5GlXFb3NlV+RP6hBU/EE6dDqx39vW252b\r\n3SyX8r99Q5exHvQEQvwI4HKo37X9WuxPfeuZdLVMYhpzUpRa4itseS3F5Qx/\r\nF2QvmX2p2sWeTjHMIJD4NRtKusBRVoEsojIMcjJ0zuj+rrJofAPp12x+K132\r\nOOuQkmjzl9bICOrEfYhtwvlWkaBpO5UwV0xu8kH25iMjpMfDJ7IwOCFFlhqf\r\ndOYgAyGbZAPImrzaKUzcXRf4QFmSDbrLmjeX9kClFGd3Ur3DvWMgbI4192St\r\nTIojQbMRqVLb5+qpoIUtF9DKP+hcMJ6SBzM=\r\n=Ds8o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6adf8fbb723e812e22e024a8ba809ed19d506a32", "scripts": {"test": "jest --runInBand --detectOpenHandles --coverage --testTimeout=50000", "build": "npx tsc", "prepublishOnly": "npm run build"}, "typings": "types/index.d.ts", "_npmUser": {"name": "fengkx", "email": "<EMAIL>"}, "_npmVersion": "8.12.1", "description": "The cloud api request package.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"node-fetch": "^2.6.0", "query-string": "^6.11.1", "https-proxy-agent": "^5.0.0", "@cloudbase/signature-nodejs": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.2.1", "eslint": "^6.8.0", "ts-jest": "^25.2.1", "ts-node": "^10.0.0", "typescript": "^3.8.3", "@types/jest": "^25.1.4", "@types/node": "^12.12.31", "@types/node-fetch": "^2.5.6", "eslint-config-alloy": "^3.6.0", "@typescript-eslint/parser": "^2.25.0", "@typescript-eslint/eslint-plugin": "^2.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cloud-api_0.5.5_1655886209937_0.5441083101792252", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2020-03-27T02:01:29.892Z", "modified": "2025-04-15T07:26:02.456Z", "0.1.0": "2020-03-27T02:01:30.255Z", "0.1.1": "2020-04-07T12:27:27.906Z", "0.1.2": "2020-04-07T12:31:24.797Z", "0.1.3": "2020-04-13T07:37:55.469Z", "0.1.4": "2020-05-06T03:24:59.513Z", "0.1.6": "2020-06-18T01:54:58.810Z", "0.1.7": "2020-08-19T12:33:56.142Z", "0.1.8": "2020-08-26T09:39:35.987Z", "0.1.9": "2020-08-26T13:05:01.836Z", "0.2.0": "2020-10-20T03:37:00.090Z", "0.1.10": "2020-11-16T13:21:13.198Z", "0.2.1": "2020-11-16T13:22:54.267Z", "0.3.0": "2020-12-28T04:05:09.424Z", "0.3.1": "2020-12-28T08:17:44.698Z", "0.3.2": "2020-12-28T09:27:37.829Z", "0.3.3": "2021-01-15T06:32:24.143Z", "0.3.4-0": "2021-01-27T02:29:21.457Z", "0.3.4-1": "2021-02-04T08:42:46.481Z", "0.3.4-2": "2021-02-04T09:08:32.933Z", "0.4.0-0": "2021-02-04T14:31:26.640Z", "0.4.0": "2021-02-05T03:27:23.406Z", "0.4.1-0": "2021-05-27T03:37:43.746Z", "0.4.1-alpha.1": "2021-07-06T11:08:11.093Z", "0.4.1-alpha.2": "2021-07-08T08:05:20.949Z", "0.4.1-alpha.3": "2021-07-08T08:43:59.431Z", "0.4.1-alpha.4": "2021-07-12T03:26:16.177Z", "0.4.1-alpha.5": "2021-07-12T11:59:43.328Z", "0.4.1-alpha.6": "2021-07-13T04:27:26.646Z", "0.4.1-alpha.7": "2021-07-14T09:31:07.538Z", "0.4.1-alpha.8": "2021-07-14T14:02:34.657Z", "0.4.2": "2021-07-26T08:00:50.480Z", "0.4.3": "2021-07-26T08:02:36.701Z", "0.4.4": "2021-08-10T04:42:05.393Z", "0.4.5-alpha.0": "2021-10-14T10:14:11.368Z", "0.4.5-alpha.2": "2021-10-19T08:19:53.038Z", "0.5.0": "2021-10-27T11:59:20.566Z", "0.5.1": "2021-12-01T10:15:09.251Z", "0.5.2": "2021-12-22T11:16:56.570Z", "0.5.3": "2022-06-21T01:56:47.151Z", "0.5.4": "2022-06-22T03:11:55.637Z", "0.5.5": "2022-06-22T08:23:30.140Z"}, "users": {}, "dist-tags": {"alpha": "0.4.1-alpha.8", "stable": "0.5.2", "latest": "0.5.5"}, "_rev": "5566-b0bee8140bc071a1", "_id": "@cloudbase/cloud-api", "readme": "# @cloudbase/cloud-api\n\n云 API 3.0 请求封装，仅适用于 Node。\n\n## 调用云API\n\n支持 API 秘钥或临时秘钥鉴权。\n\n```ts\nimport { CloudApiService } from '@cloudbase/cloud-api'\n\nconst service = new CloudApiService({\n    service: 'tcb',\n    credential: {\n        secretId: 'xxx',\n        secretKey: 'xxx',\n        token: 'xxx'\n    }\n})\n\nservice.request('DescribeEnvs').then(console.log)\n// 或\nservice\n    .request({\n        action: DescribeEnvs\n    })\n    .then(console.log)\n```\n\n或\n\n```js\nconst { CloudApiService } = require('@cloudbase/cloud-api')\n\nconst service = new CloudApiService({\n    service: 'tcb',\n    credential: {\n        secretId: 'xxx',\n        secretKey: 'xxx'\n    }\n})\n\nservice.request('DescribeEnvs').then(console.log)\n```\n\n结果\n\n```ts\n{\n  EnvList: [],\n  RequestId: '09a1ece6-7cb1-4a8f-b8b6-e4cc066d1fbe'\n}\n```\n\n## 异步 credential\n\n在某些情况下，你的 credential 可能是异步获取。你可以配置 `getCredential` 方法，异步传递 credential 信息。建议结合 `getInstance()` 方法使用，避免重复请求获取 credential 信息。\n\n```ts\nconst tcbService = CloudApiService.getInstance({\n    service: 'tcb',\n    getCredential: async () => {\n        // 你的异步逻辑\n        return new Promise((resolve) => {\n            setTimeout(() => {\n                resolve({})\n            }, 1000)\n        })\n    }\n})\n\nconst res = await tcbService.request('DescribeEnvs')\n```\n\n## request()\n\n定义：`async request(action: string, data: Record<string, any> = {}, method: 'POST' | 'GET' = 'POST')`\n\n使用 request 方法发起请求。\n\n## getInstance()\n\n`getInstance()` 静态方法内部是一个工厂函数，会根据服务类型缓存实例，能有效避免重复初始化实例带来的开销\n\n```ts\nconst tcbService = CloudApiService.getInstance({\n    credential,\n    service: 'tcb'\n})\n\nconst res = await tcbService.request('DescribeEnvs', {\n    empty: null,\n    b: undefined\n})\n```\n\n## 清除内存中的 credential 缓存\n\n在某些特殊的情况下，你可能需要在不退出进程的情况下重载 `credential`。你可以使用 `clearCredentialCache()` 方法清除内存中的 `credential` 缓存，使得下次请求前通过 `getCredential` 函数重新获取 `credential`\n\n```ts\nconst tcbService = new CloudApiService({\n    service: 'tcb',\n    getCredential: () => {\n        return new Promise((resolve) => {\n            setTimeout(() => {\n                resolve(credential)\n            }, 1000)\n        })\n    }\n})\n\nconst res = await tcbService.request('DescribeEnvs')\n\ntcbService.clearCredentialCache()\n\n// 重新加载 credential\nconst res = await tcbService.request('DescribeEnvs')\n```\n\n### 选项\n\n下面是 TS 定义\n\n```ts\ninterface ServiceOptions {\n    // 服务名，如 tcb scf\n    service: string\n    // 服务版本，如 2018-06-14\n    version?: string\n    // http 请求代理，如 http://127.0.0.1:1235\n    // 需要准确声明，本 SDK 不会自动解析 proxy\n    proxy?: string\n    // 超时时间，毫秒\n    timeout?: number\n    // 基本参数，会被拼接到请求参数中\n    baseParams?: Record<string, any>\n    // 身份信息\n    credential?: Credential\n    // 获取身份信息的函数\n    getCredential?: () => Promise<Credential> | Credential\n}\n\ninterface Credential {\n    secretId: string\n    secretKey: string\n    token?: string\n}\n```\n\n## 调用weda后端\n\n```ts\nimport { wedaRequest } from '@cloudbase/cloud-api'\n\n// 使用示例\nwedaRequest({\n  action: 'DescribeWedaUserId',\n  credentials: {\n    secretId: 'xxx',\n    secretKey: 'xxxx',\n    sessionToken: 'xxxxxx',\n  },\n  /** 环境ID */\n  envId: 'xxx-xxxx',\n  /**\n   * 调用的weda环境类型\n   *  预览-pre，正式-prod\n   */\n  envType: 'pre',\n  /** 用户ID */\n  uid: 'xxxxxx',\n  /** \n   * uid 类型, 1.tcb 2.微信 3.企业微信 4.weda内部\n   */\n  source: 1\n}).then(res => console.log(res))\n\n// 方法定义, 返回原云API Response 内容\nwedaRequest(params: IWedaRequestParams, config?: Partial<IWedaConfig>): Promise<any>\n\n/**\n * 请求参数\n */\nexport interface IWedaRequestParams {\n  /** 请求方法 */\n  action: string;\n  /**\n   * 认证信息\n   *  在tcb云函数中可通过 tcb.getCloudbaseContext 返回的环境变量获取相关信息:\n   *    TENCENTCLOUD_SECRETID\n   *    TENCENTCLOUD_SECRETKEY\n   *    TENCENTCLOUD_SESSIONTOKEN\n   */\n  credentials: ICredentials | (() => Promise<ICredentials>)\n  /**\n   * 环境ID\n   *    在tcb云函数中可通过 tcb.getCloudbaseContext(context) 返回的环境变量 TCB_ENV 获取\n   */\n  envId: string;\n  /**\n   * 客户端IP地址, 方便后端进行统计, 无可不传\n   *  在tcb云函数中可通过 tcb.getCloudbaseContext 返回的 WX_CLIENTIP(微信侧) TCB_SOURCE_IP(云开发侧)拿到客户端IP地址\n   *  详细文档 https://docs.cloudbase.net/api-reference/server/node-sdk/env.html#getcloudbasecontext\n   */\n  clientIp?: string\n  /**\n   * 自定义请求ID, 方便前后端调试, 无可不传\n   *  在tcb 云函数中, 若可以保证前端一次云函数调用, 只向weda后端发起一次请求, 这可以通过 tcb.parseContext 拿到云函数的请求 request_id 并复用该ID\n   *    若不能保证上述条件, 则不要复用云函数请求ID, 否则导致请求链路出问题难以定位\n   *  详细文档 https://docs.cloudbase.net/api-reference/server/node-sdk/env.html#parsecontext\n   */\n  requestId?: string\n  /**\n   * 调用的weda环境类型\n   *  预览-pre，正式-prod\n   */\n  envType: 'pre' | 'prod'\n  /**\n   * 用户ID\n   *    在tcb 云函数中获取 uid/uidSource 的方法可参考下边的函数 getUserSource\n   */\n  uid: string;\n  /** \n   * uid 类型, 1.tcb 2.微信 3.企业微信 4.weda内部\n   */\n  source: 1 | 2 | 3 | 4\n  /**\n   * 方法的其他参数\n   */\n  data?: Record<string, any>\n}\n\ninterface IWedaConfig {\n  /** 请求的接口地址, 默认 'https://gateway.weda.tencent-cloud.com/wedaapi' */\n  url: string\n  /**\n   * 请求目标, 与 url 意义类似, 但可通过设置为 prod/pre 来指定地址, 优先使用 target\n   * 可以是下边三个值\n   *  'prod' 正式地址\n   *  'pre' 预发地址\n   *  'demo' 体验地址 \n   *  url 任意一个url\n   */\n  target?: string\n  /** 请求方法, 默认 POST */\n  method: string\n  /** 额外的自定义头 */\n  headers?: Record<string, any>\n  /** 自定义http代理地址 */\n  proxy?: string\n}\n\ninterface ICredentials {\n  secretId: string\n  secretKey: string\n  sessionToken: string\n}\n\n```\n\n\n在tcb云函数中获取uid及uidSource\n```ts\n/** 获取用户来源信息 */\nexport function getUserSource(context) {\n  //  context 为tcb云函数的入口函数接收的 context, 必须传递该参数, 否则解析不到用户ID相关信息\n  const envInfo = tcb.getCloudbaseContext(context);\n  // @ts-ignore\n  const openId: string | undefined = envInfo.WX_FROM_OPENID || envInfo.WX_OPENID;\n  const uid = envInfo.TCB_UUID;\n  if (!openId && !uid) {\n    return;\n  }\n  return {\n    /** uid 类型\n     * 1.tcb 2.微信 3.企业微信 4.weda内部\n     */\n    source: openId ? 2 : 1,\n    uid: openId || uid,\n  };\n}\n```\n\n### 调试\n调试时, 可以通过在环境变量中增加 `NODE_DEBUG=weda-request` 来打印出详细的冗余日志", "_attachments": {}}