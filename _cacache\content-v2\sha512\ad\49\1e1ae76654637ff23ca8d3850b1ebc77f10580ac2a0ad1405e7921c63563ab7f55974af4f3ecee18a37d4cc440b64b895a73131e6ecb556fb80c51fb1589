{"name": "url", "versions": {"0.4.9": {"name": "url", "version": "0.4.9", "keywords": ["ender", "url"], "author": {"url": "http://www.joyent.com", "name": "<PERSON><PERSON>"}, "_id": "url@0.4.9", "homepage": "http://nodejs.org/docs/v0.4.9/api/url.html", "dist": {"shasum": "d8ae654e43779d5dcc120c282cf51717b62427ba", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.4.9.tgz", "integrity": "sha512-j4tQ6rucN/zC1uoZbo7ghLaxi2LrFhC5ATI2i94qeX4pyrLRa70cr/nMThx89iM/5nFqpaGnNmcFd40d1fE7KQ==", "signatures": [{"sig": "MEUCIHkgxgV2MBpBPyOswFoO1QfcHZACGjI7aH0c4m5x+qFtAiEA6rapQef5z0UzmETKYsQ6+A/rKxZvjDIclK0KOyQNxS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "engines": {"node": ">= 0.2.0", "ender": ">= 0.5.0"}, "scripts": {}, "repository": {"url": "git://github.com/coolaj86/nodejs-libs-4-browser.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "Node.JS url module", "directories": {"lib": "."}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/url/0.4.9/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"querystring": ">= 0.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.7.9": {"name": "url", "version": "0.7.9", "keywords": ["ender", "pakmanager", "url"], "author": {"url": "http://www.joyent.com", "name": "<PERSON><PERSON>"}, "_id": "url@0.7.9", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}], "homepage": "http://nodejs.org/api/url.html", "dist": {"shasum": "1959b1a8b361fc017b59513a7c7fa9827f5e4ed0", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.7.9.tgz", "integrity": "sha512-/jp/XVvp2MdJaLWe116q//********************************/4FrwN3I86rDbtC+BC9+m85+wUGRWlHg==", "signatures": [{"sig": "MEYCIQDgQ2b1uizgXHE6EASS8UYc28WGCOHPWElcxDHkrseMRwIhAMsl7yW1fXdhgY8nIqAT2RaLK96hxIQYfUqmP3PUT3wu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "engines": {"node": ">= 0.2.0", "ender": ">= 0.5.0"}, "repository": {"url": "git://github.com/coolaj86/node.git", "type": "git"}, "description": "Node.JS url module", "directories": {"lib": "."}, "dependencies": {"punycode": ">=1.0.0 <1.1.0", "querystring": ">=0.1.0 <0.2.0"}, "publishConfig": {"registry": "http://registry.npmjs.org"}, "devDependencies": {}, "contributors": []}, "0.10.0": {"name": "url", "version": "0.10.0", "_id": "url@0.10.0", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "fa391953a4a89eb9d94f54b823fce1ce98c9413c", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.10.0.tgz", "integrity": "sha512-vT6U2BufB6ixZC8iELR5q/T2z/6xWsf6OXSrg+ANJY90B15zFSS2B7vTsSGM9Dzcsy3YFTs++go+llanx2k3mA==", "signatures": [{"sig": "MEQCIDuctLS8DCkPyVDzeC8ifzmrOddVG4qm7XAK1rHpaZBtAiB7dCz4jrReM4r1ZTRdZkm5mujS6EecNDSeuOvBjwRZKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "dependencies": {"punycode": "1.2.4"}, "devDependencies": {"zuul": "1.6.3", "mocha": "1.18.2", "assert": "1.1.1"}, "contributors": []}, "0.10.1": {"name": "url", "version": "0.10.1", "_id": "url@0.10.1", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "d8eba8f267cec7645ddd93d2cdcf2320c876d25b", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.10.1.tgz", "integrity": "sha512-ADxffdyUpVmS7pcoveHZ5vm+vvVUJRrSx1hAN/9H/ZEaQ7LDMs3tqOYIWDlxEa2JHD6c3ghbLZ934FdbnW6lpQ==", "signatures": [{"sig": "MEYCIQDAhEWU6PjkGnvCqnB6dNa1flFjkkWsAsyfTa7Nx8clkwIhAMfvcpfWbO95pPAKMuVUDvoC8Axy/EEswI8uXv5pCzPM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "_from": ".", "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "dependencies": {"punycode": "1.2.4"}, "devDependencies": {"zuul": "1.6.3", "mocha": "1.18.2", "assert": "1.1.1"}, "contributors": []}, "0.10.2": {"name": "url", "version": "0.10.2", "_id": "url@0.10.2", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "68621d6929ea1cad344ebf135d82fcf7eb1a7469", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.10.2.tgz", "integrity": "sha512-9QPsXhCIJ9I/WQO13jrSgeGugMiTAADu79JT6povpJ5fdkJzJEKfnTM7Y72coB2HpLLXyWUqOJWI1Rdpjnc2sQ==", "signatures": [{"sig": "MEYCIQCjz15i1nKzTAnQsw7AxvnokTZhxpXax4iK0wre/AuQhgIhAPQXlpscaVlHsPEs4GH8NSqSlPef0ilek0r13Xn3/YHF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "_from": ".", "_shasum": "68621d6929ea1cad344ebf135d82fcf7eb1a7469", "gitHead": "9a64b9ab8703d1d38d1a39793bd9841224962eb4", "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "2.1.12", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"punycode": "1.3.2"}, "devDependencies": {"zuul": "1.16.3", "mocha": "1.18.2", "assert": "1.1.1"}, "contributors": []}, "0.10.3": {"name": "url", "version": "0.10.3", "license": "MIT", "_id": "url@0.10.3", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "021e4d9c7705f21bbf37d03ceb58767402774c64", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.10.3.tgz", "integrity": "sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==", "signatures": [{"sig": "MEUCIDWB+0t9EYitoTFI/SMZmiLcoaleQ2cI7okXldycXoMyAiEAy+67dnhq0iDrCQ0Vfa20Z7FSxrw6/Rv4PvDG0X7aAN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "_from": ".", "_shasum": "021e4d9c7705f21bbf37d03ceb58767402774c64", "gitHead": "575b428ae37eb43f243d0228dea1b44ecf744f3d", "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "2.2.0", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}, "devDependencies": {"zuul": "2.0.0", "mocha": "1.18.2", "assert": "1.1.1"}, "contributors": []}, "0.11.0": {"name": "url", "version": "0.11.0", "license": "MIT", "_id": "url@0.11.0", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url#readme", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "3838e97cfc60521eb73c525a8e55bfdd9e2e28f1", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.11.0.tgz", "integrity": "sha512-kbailJa29QrtXnxgq+DdCEGlbTeYM2eJUxsz6vjZavrCYPMIFHMKQmSKYAIuUK2i7hgPm28a8piX5NTUtM/LKQ==", "signatures": [{"sig": "MEQCIQCBvBrecNF/nIK/HcqL7DCUWr5Q5puxbNbmi5ggym82hwIfPXXp9sTCX/2IaboBA9l2do2VXQyhJvI565FQMtkY3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./url.js", "_from": ".", "_shasum": "3838e97cfc60521eb73c525a8e55bfdd9e2e28f1", "gitHead": "7f82d6b09acba099163fede09b4226b4e55a5377", "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "2.11.1", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}, "devDependencies": {"zuul": "3.3.0", "mocha": "1.18.2", "assert": "1.1.1"}, "contributors": []}, "0.11.1": {"name": "url", "version": "0.11.1", "keywords": ["parsing", "url", "analyze"], "author": {"name": "defunctzombie"}, "license": "MIT", "_id": "url@0.11.1", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url#readme", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "26f90f615427eca1b9f4d6a28288c147e2302a32", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.11.1.tgz", "fileCount": 9, "integrity": "sha512-rWS3H04/+mzzJkv0eZ7vEDGiQbgquI1fGfOad6zKvgYQi1SzMmhl7c/DdRGxhaWrVH6z0qWITo8rpnxK/RfEhA==", "signatures": [{"sig": "MEYCIQDhrWl4n+cci3bfpmQns6dSXuypNbbn/qTtpd7Fk1GgywIhANTFbEtKKIk9dj4CAeD0fcWMlwGMNSXQr5zcI3wxqEo/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77547}, "main": "./url.js", "gitHead": "44e47dc1bef1da0646df28e22edb9c464758aa66", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "zuul": "zuul -- test/index.js", "pretest": "npm run lint", "posttest": "aud --production", "test-local": "zuul --local -- test/index.js", "tests-only": "nyc mocha"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"qs": "^6.11.0", "punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "zuul": "^3.12.0", "acorn": "^8.8.2", "mocha": "^3.5.3", "eslint": "=8.8.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/url_0.11.1_1686461533103_0.5549889617211954", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.11.2": {"name": "url", "version": "0.11.2", "keywords": ["parsing", "url", "analyze"], "author": {"name": "defunctzombie"}, "license": "MIT", "_id": "url@0.11.2", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url#readme", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "02f250a6e0d992b781828cd456d44f49bf2e19dd", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.11.2.tgz", "fileCount": 9, "integrity": "sha512-7yIgNnrST44S7PJ5+jXbdIupfU1nWUdQJBFBeJRclPXiWgCvrSq5Frw8lr/i//n5sqDfzoKmBymMS81l4U/7cg==", "signatures": [{"sig": "MEQCIA6vL7XXABDGuhvjbhoA5aArQMBLIUN2Fc+92bVLx/69AiB/DCfkGsQubct6mff/4unrsAUS4jKWdbWSaAQwWTKmOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78011}, "main": "./url.js", "gitHead": "a6ddc4eaa4c5f81fdada0e1867153e06c5c7778f", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "zuul": "zuul -- test/index.js", "pretest": "npm run lint", "posttest": "aud --production", "test-local": "zuul --local -- test/index.js", "tests-only": "nyc mocha"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "20.6.0", "dependencies": {"qs": "^6.11.2", "punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "zuul": "^3.12.0", "acorn": "^8.10.0", "mocha": "^3.5.3", "eslint": "=8.8.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/url_0.11.2_1694195391248_0.6019315144503488", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.11.3": {"name": "url", "version": "0.11.3", "keywords": ["parsing", "url", "analyze"], "author": {"name": "defunctzombie"}, "license": "MIT", "_id": "url@0.11.3", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-url#readme", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "dist": {"shasum": "6f495f4b935de40ce4a0a52faee8954244f3d3ad", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.11.3.tgz", "fileCount": 9, "integrity": "sha512-6hxOLGfZASQK/cijlZnZJTq8OXAkt/3YGfQX45vvMYXpZoo8NdWZcY73K108Jf759lS1Bv/8wXnHDTSz17dSRw==", "signatures": [{"sig": "MEUCIDzv3/T6ysx70Py4/eCAOrTt6RpQJXQACwCL9NYnyW2TAiEA7MLQTykTVM0OjVtMuVCnTfWwvBE6yU2aPZ+3xlYHics=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78276}, "main": "./url.js", "gitHead": "204fd46a47ce98cc38a82d938e7fe58b05172adb", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "zuul": "zuul -- test/index.js", "pretest": "npm run lint", "posttest": "aud --production", "test-local": "zuul --local -- test/index.js", "tests-only": "nyc mocha"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/defunctzombie/node-url.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "The core `url` packaged standalone for use with Browserify.", "directories": {}, "_nodeVersion": "20.6.0", "dependencies": {"qs": "^6.11.2", "punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "zuul": "^3.12.0", "acorn": "^8.10.0", "mocha": "^3.5.3", "eslint": "=8.8.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/url_0.11.3_1694731117162_0.9161831275851848", "host": "s3://npm-registry-packages"}, "contributors": []}, "0.11.4": {"name": "url", "description": "The core `url` packaged standalone for use with Browserify.", "version": "0.11.4", "author": {"name": "defunctzombie"}, "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "main": "./url.js", "keywords": ["parsing", "url", "analyze"], "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "acorn": "^8.12.1", "aud": "^2.0.4", "eslint": "=8.8.0", "mocha": "^3.5.3", "nyc": "^10.3.2", "zuul": "^3.12.0"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc mocha", "test": "npm run tests-only", "posttest": "aud --production", "zuul": "zuul -- test/index.js", "test-local": "zuul --local -- test/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/defunctzombie/node-url.git"}, "license": "MIT", "engines": {"node": ">= 0.4"}, "_id": "url@0.11.4", "gitHead": "455a3e2106bf254498615efc50a8dd5527be4132", "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "homepage": "https://github.com/defunctzombie/node-url#readme", "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==", "shasum": "adca77b3562d56b72746e76b330b7f27b6721f3c", "tarball": "https://mirrors.cloud.tencent.com/npm/url/-/url-0.11.4.tgz", "fileCount": 9, "unpackedSize": 78318, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzQ9WUV1k+MOLYZA4LW7fqr41iWo7Xwlsz7zhfxRP0JAIhAJ7yNI9s9NThUvl9QCXfMzHedc4IKlFI4GbIy+nIwOdn"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url_0.11.4_1722023661748_0.4636352260901089"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2011-07-07T22:16:05.456Z", "modified": "2024-07-26T19:54:22.049Z", "0.4.9": "2011-07-07T22:16:05.853Z", "0.7.9": "2012-06-19T20:14:13.396Z", "0.10.0": "2014-04-21T23:45:22.022Z", "0.10.1": "2014-04-22T02:04:20.272Z", "0.10.2": "2015-01-02T03:40:11.078Z", "0.10.3": "2015-02-27T18:37:21.243Z", "0.11.0": "2015-08-27T15:36:57.674Z", "0.11.1": "2023-06-11T05:32:13.290Z", "0.11.2": "2023-09-08T17:49:51.442Z", "0.11.3": "2023-09-14T22:38:37.401Z", "0.11.4": "2024-07-26T19:54:21.898Z"}, "users": {}, "dist-tags": {"latest": "0.11.4"}, "_rev": "429-3c1cf46f7abc2821", "_id": "url", "readme": "# node-url\n\n[![Build Status](https://travis-ci.org/defunctzombie/node-url.svg?branch=master)](https://travis-ci.org/defunctzombie/node-url)\n\nThis module has utilities for URL resolution and parsing meant to have feature parity with node.js core [url](http://nodejs.org/api/url.html) module.\n\n```js\nvar url = require('url');\n```\n\n## api\n\nParsed URL objects have some or all of the following fields, depending on\nwhether or not they exist in the URL string. Any parts that are not in the URL\nstring will not be in the parsed object. Examples are shown for the URL\n\n`'http://user:<EMAIL>:8080/p/a/t/h?query=string#hash'`\n\n* `href`: The full URL that was originally parsed. Both the protocol and host are lowercased.\n\n    Example: `'http://user:<EMAIL>:8080/p/a/t/h?query=string#hash'`\n\n* `protocol`: The request protocol, lowercased.\n\n    Example: `'http:'`\n\n* `host`: The full lowercased host portion of the URL, including port\n  information.\n\n    Example: `'host.com:8080'`\n\n* `auth`: The authentication information portion of a URL.\n\n    Example: `'user:pass'`\n\n* `hostname`: Just the lowercased hostname portion of the host.\n\n    Example: `'host.com'`\n\n* `port`: The port number portion of the host.\n\n    Example: `'8080'`\n\n* `pathname`: The path section of the URL, that comes after the host and\n  before the query, including the initial slash if present.\n\n    Example: `'/p/a/t/h'`\n\n* `search`: The 'query string' portion of the URL, including the leading\n  question mark.\n\n    Example: `'?query=string'`\n\n* `path`: Concatenation of `pathname` and `search`.\n\n    Example: `'/p/a/t/h?query=string'`\n\n* `query`: Either the 'params' portion of the query string, or a\n  querystring-parsed object.\n\n    Example: `'query=string'` or `{'query':'string'}`\n\n* `hash`: The 'fragment' portion of the URL including the pound-sign.\n\n    Example: `'#hash'`\n\nThe following methods are provided by the URL module:\n\n### url.parse(urlStr, [parseQueryString], [slashesDenoteHost])\n\nTake a URL string, and return an object.\n\nPass `true` as the second argument to also parse\nthe query string using the `querystring` module.\nDefaults to `false`.\n\nPass `true` as the third argument to treat `//foo/bar` as\n`{ host: 'foo', pathname: '/bar' }` rather than\n`{ pathname: '//foo/bar' }`. Defaults to `false`.\n\n### url.format(urlObj)\n\nTake a parsed URL object, and return a formatted URL string.\n\n* `href` will be ignored.\n* `protocol` is treated the same with or without the trailing `:` (colon).\n  * The protocols `http`, `https`, `ftp`, `gopher`, `file` will be\n    postfixed with `://` (colon-slash-slash).\n  * All other protocols `mailto`, `xmpp`, `aim`, `sftp`, `foo`, etc will\n    be postfixed with `:` (colon)\n* `auth` will be used if present.\n* `hostname` will only be used if `host` is absent.\n* `port` will only be used if `host` is absent.\n* `host` will be used in place of `hostname` and `port`\n* `pathname` is treated the same with or without the leading `/` (slash)\n* `search` will be used in place of `query`\n* `query` (object; see `querystring`) will only be used if `search` is absent.\n* `search` is treated the same with or without the leading `?` (question mark)\n* `hash` is treated the same with or without the leading `#` (pound sign, anchor)\n\n### url.resolve(from, to)\n\nTake a base URL, and a href URL, and resolve them as a browser would for\nan anchor tag.  Examples:\n\n    url.resolve('/one/two/three', 'four')         // '/one/two/four'\n    url.resolve('http://example.com/', '/one')    // 'http://example.com/one'\n    url.resolve('http://example.com/one', '/two') // 'http://example.com/two'", "_attachments": {}}