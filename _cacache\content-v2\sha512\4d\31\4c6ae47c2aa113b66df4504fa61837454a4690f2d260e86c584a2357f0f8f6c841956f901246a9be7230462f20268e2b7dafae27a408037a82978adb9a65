{"name": "babel-plugin-transform-async-generator-functions", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.16.0": {"name": "babel-plugin-transform-async-generator-functions", "version": "6.16.0", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "38eae52015db9844c2b987c742157506772caa60", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.16.0.tgz", "integrity": "sha512-KkjO9MPXEG7mBuHVbfUU1L2NKzDcHjk8n2pKL4i5/34UAjISvxCYuPlRHZEJ53kXeby/lTSxmufNYxMiKbEgvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxUieAYN4TUrf5x0FZYaqaFC2m4hIjXAGHnq7dRoaZsgIgQU0/MP37iPEG2AuD5BJT15x3duv6r+FeQqC+Aw8G/k0="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "^6.16.0", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.16.2": {"name": "babel-plugin-transform-async-generator-functions", "version": "6.16.2", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "6b7ad53fa78e5049f650233d8a7ee8f716f08e1d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.16.2.tgz", "integrity": "sha512-8uvRMNIVEU3KHKtRSCwdphlMulwzWD7nYfv+McLM7BZSe+ycGPSzMCtm5oe9tLwZI2riA75UK8oRQf1Fo72bEg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYA+4oqNI1kLLD4muFmabLINISvs5lPYYuBNU9ZG9KwQIhAOu7V/FlQB8xRhVIgQgvf2nwekXIFEndMBSHVY0eedx0"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "^6.16.2", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^5.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.17.0": {"name": "babel-plugin-transform-async-generator-functions", "version": "6.17.0", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "d0b5a2b2f0940f2b245fa20a00519ed7bc6cae54", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.17.0.tgz", "integrity": "sha512-B5mn7aJfuFG/aZMYRwGzdvIyFCQpFZuqTHeMTABK2OjyQdlg4yTk51U9eJtaPEluJsNcqNobdnjndLjO13/ntw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYhMdagb49VbVWA49u1S5m0ynrHiNlj8fjONilUSz8AAiBq/x/IPRSYxc1KdIasjynR8XIuOw5wSIaptBlT4o4G5Q=="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "^6.16.2", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^6.0.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-async-generator-functions", "version": "6.22.0", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "a720a98153a7596f204099cd5409f4b3c05bab46", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.22.0.tgz", "integrity": "sha512-Np9MlJcblWSRkh3EeXyDt+652S+tZL9mR2Du5AM++O3pwJIVETJwgQINXU8HTbATOzLh1r5xh4wfd4DRTl1piA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDlVqtxPW6XJdQMQSXmL2L0769S68bHCoPo2VsvQkWidAIfEnv3eAwJOtt8cY+H9F+o+X0vQo+c/ZfTUcBRVtH56g=="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "^6.22.0", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.1", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "677ce91d0a8d1f7264691c9c5c300597f7896995", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.1.tgz", "integrity": "sha512-wE3xKfjB0Ym8NAxSIkVT7C+z3eSGgoqUh1Lb8xqCD1UmlfU1y4MuVZZZODkHq5z/6WtxmGe8odOzCPSv2smZqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH6NlvuDCfKgw6xJY4YSmhHq/9QI/mT9v4w5gLuUuJHAIgcVddIaMl61P/RmtrknlkJnlK92HoiOkSrenHIkw8UVI="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.1", "babel-plugin-syntax-async-generators": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.3", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "ba61d374ca649e9cf48f5ddb70c893d87d3af31e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.3.tgz", "integrity": "sha512-WzA+RwzbT9ZqPuV+JcjDm3oNtDcyNOLogaRgSmtviFet63y5La3txDgXQlHLLbg++yGvbG55GIyEfoV+pqInIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkKK1tFuDbOh6L55Di21OJtXAHMGmFwXUpoiQ3Kd0CYQIgD/SmzsvnuZJACOKbhpOukr6mjgv6XoEZ9gTcp+NXzsw="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.3", "babel-plugin-syntax-async-generators": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.7", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "da52dbbc22fd0ebe76ee212cbebfeefe7b6f5612", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.7.tgz", "integrity": "sha512-exgHBnIf5Mb8qNSPwWwSt4pvB1Us0I2F3ytvNq23qbVB537q+UqtetjxdkBI9pqUmYZ7HKoU+IrmPzn3ET8a5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdZczLranA4RmcWk0ETmeDsxVkDEYlFescKmhLLsQ4DgIgArD6Fgyfg4J8x4dJ/z3Yopx40XiXt5OGVKWcJVanHJ8="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.7", "babel-plugin-syntax-async-generators": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-async-generator-functions", "version": "6.24.1", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "f058900145fd3e9907a6ddf28da59f215258a5db", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.24.1.tgz", "integrity": "sha512-uT7eovUxtXe8Q2ufcjRuJIOL0hg6VAUJhiWJBLxH/evYAw+aqoJLcYTR8hqx13iOx/FfbCMHgBmXWZjukbkyPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmHaL7gqJkxdl1bHWPQ/AZWJtFziXsMoXtrf2mnobEwgIgCYdMexGjErNtpZuYJHzc/Q9p4w/Z5BpYUx+VO7K6Kdw="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "^6.24.1", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.8", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "6c656bd22ddbd90ac28d813a613ac54c9511b289", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.8.tgz", "integrity": "sha512-bdoI+c0ISNQ3qeoKuM+Y68SA0n5As1hgUIiKrdYo9Um3Xsm7L2HZ8NPzpOiICgVSUaGblsTR47g8QBOgpL2oSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJzO4MrR23XscKSqEcjINCXAg0BlCFlOnDqCcrd667RgIgNxPTszP6Z4yb0Y/EDdqqnTmZR2dOGJKovPyUD5lFLcM="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.8", "babel-plugin-syntax-async-generators": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.9", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "24bc94904b70adf4174ef3b85a0f222fcb11f937", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.9.tgz", "integrity": "sha512-ZD2dplqHDd73c89BXcPLzXWaF5VfaIaNTw+/Z4SqM39+RMylYjHbbQsNcVwm8IHmXzL/sdd2OxxVr47XR6Kp8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFrgFmHH/vDKWKLdnxugssqEkwPu//8rQzp+qYJlIWFrAiEAsIjvgoTKXKU2ZugmMziWVupt9LTixZ5HJ36GjQDXWMg="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.9", "babel-plugin-syntax-async-generators": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.10", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "dd7381177c6d61d55b74f194a0141b3e307252b5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.10.tgz", "integrity": "sha512-ioikes2rZTbbdbEBnHFEcSvWVs/3Jj8hFzWIjJL14cg0Cgi7pFY/lz77JYjbj/wdmdwWO9YublKE07ru7uPmWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjD5ZUDMtbqw+HBJbj2fkoAo31bpQeqd9j3OwlHE/XAAIhAMzm14rMY8chzJa3fwfQ2GTrXk+QaopU81KLEOqvEITU"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.10", "babel-plugin-syntax-async-generators": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.11", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-X7IllHyj1R3Dco/rjtsN+fpl68da+M+Y/SMRA8L7K6DPr7W1g/FCtRse0Am/m1e82ZAGYVHxCjvM7jNISTwrhA==", "shasum": "f7b7d42335bc53c014d3de9e1d7fd7f0f7cd02fd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCT7IcwrsCDpLz+NsbzbHIgRxKiM8b/bZbLmuE1H6sHbQIhAMVP7IuCGAb1+uIUT3pc8wtj6bzSqxSYT0tVQOZe5rkJ"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.11", "babel-plugin-syntax-async-generators": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.12", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-kraB0kjfkriCHK04/ltS3nuhwVSpf0LPXxOf36DNjAhoHm7Y00MFcBSNyEgql+hjw/gmyEYnNzSiWg+7qGJU0g==", "shasum": "ca2a5882a92edb346815067d5109c53c30b6e58b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPWjAgnINILKYHxgraimzHGVEnfAGdsszwfeX67mMGXwIgQDIe5kbkRQTzW/uosLAh0qdUEHd9shignGPTAEsLnno="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.12", "babel-plugin-syntax-async-generators": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.14", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "fa5072e922b3c623657d15b021784b7cf86a1984", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.14.tgz", "integrity": "sha512-1cDm259P02US2QB4D4L8Z3GwEo4sS3vZqhtXaZlBfmjgUOq8QfSiAUNUd0prNG76xl5ip+jcaKmRKinF7xqxDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+NAKw2oCmnkPiNH6eiwJTGO5SwgYaNXi8e1dW/9UnjAiEAz4GHoChfNvLTAxARmGJ2EEFDNeu0q8QKAR6c4fbJJVo="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.14", "babel-plugin-syntax-async-generators": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.15", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "275eb94bde199f3fe6558ec7c611d925515bb7be", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.15.tgz", "integrity": "sha512-ek5p5WsgZ7rDh8nQ3CiczO9AsmQA908n21Wo1qy2rBSvdjWZLazQpfKIH7z4+ZyvrssYI5LrcZ+7XNm5CpYG0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC80vudPSJjZROiBEmngbB1Lp2ZCEBu5IYvVo+M5je0AgIhALsGJYU8/2z66x88pwLMFwLejkFcTtlqNfeKVpKKis9R"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.15", "babel-plugin-syntax-async-generators": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.16", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "9657573eae8079cd60e74d996ff35a9130570dcf", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.16.tgz", "integrity": "sha512-Wt9dHGUkhSFqs805bYz7YbD4ipEnQMEoVy2XNv9AUnxGBSmvUI/xgRwdphMaKMYRzZoURWg6ee+jH0+SdpFxCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLFdw9fgYyE8SKgGSgbFh8RLULo1lBm3S4TJA98hAicwIhAPM84xJL08VWLqCSX4N4tx0WWR7kjxnKCGbEZ7zzEUXI"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.16", "babel-plugin-syntax-async-generators": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.17", "description": "Turn async generator functions into ES2015 generators", "dist": {"shasum": "a169318ec1e1077f9df133633e65f76be778cc75", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.17.tgz", "integrity": "sha512-Xye8iJ8rXNhc/chdgtpE7X19Uc7AdLJH1sxjI2SYxhAV6eRWU2iAznVoku0Oop1f8RltRTmzQY5GbQyyegH/8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpudHCjrKCGEPKKMywpFvdNMkC/HZT55ijjXGLVC8AvwIgShHZgTCFPO3Phq45YXx8ybernauockY/4kYz5GKuUys="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.17", "babel-plugin-syntax-async-generators": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.18", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-KAwYVSk5ejlZP8KATki0O7XpjsqP49SRL60xgcIiTX2p2BkFHBz+cOILh/ZQY2U7QARubEldwXskvdoG9AGpnw==", "shasum": "4353aadccf5a8c4c409341cd8e871428bdd20522", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChcJMQREzt9aqIJMy5SmD5c5BELnkaHIBcgl6Vi+N/0gIgGdAdIU/Ba4hv2AEK61CJwrSzPPohugUT6T7pdWz5uw8="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.18", "babel-plugin-syntax-async-generators": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.19", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-kfBThrNsBlf5bU1AvtjpMJ5oq9gcPkBR1wO3coCMDIf5AN1h04oX7uKQ4rZiXey0zOfz2DDxzKv96DnsQv4uPw==", "shasum": "9e9b7d0aee6b09f58756c484bf3f472225a551c2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCea1CyY5U/UB4b0tNAy9gZ1QxFpgDRv7Hwl0IA0ZvIjQIgTidW/zFf6YgV8L+facQezWohyfkC7wekCQi3qFgvacQ="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.19", "babel-plugin-syntax-async-generators": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-alpha.20", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-o+fjl0eOBhuMQrbn9mfaNpcJem3Ihad3qtXdP4sXAze0HJQYfPdo4M+ovOBmcx+U3y+4ZN9IJblv1Zo9ZTtSQA==", "shasum": "7da0d3510b4e9982f675ee3faf80499e633e3846", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGf/GYmv/UDLUy0NpfnQ/DCHY/VTcgM1MJZNcoqIQWP9AiAuYAmkYpdhzUOmLx5m+M9NMh3CXY1xjW45F9oRSz01IA=="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-alpha.20", "babel-plugin-syntax-async-generators": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-beta.0", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-b2jdI+i2CW/5iu/vb6soWCBgxed02vdy+I+Go9omsUUKHC1lgoSmSHIKuaKdVi/fT7ALRwZPWFsXxIiucXDUbw==", "shasum": "df506a7b90fe7a707f8ca8f309435df6b651ccf4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh4a5EBkXym5Z4b2IdIwc3U0fqDfHtmen4iRSC58cgWwIhAL9SYnfOfMxrWbNNWY0Bt0OaBEAfoqUaItTU8MsSPt1w"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-beta.0", "babel-plugin-syntax-async-generators": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-beta.1", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-CeTaDFScMvxVVA3h8ilVtOQX4RLmU+j6DCtbB6LmQ7cXaqBn5WwRiNtAPi3M5K3li6qAvA863TWeYiLw2F5opw==", "shasum": "302f800233cc629dcaf3844d6171413de9bbc78b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGR4/++m+hVN+f9cIygXU1LRhfmkmgjYn3kViSSDzChbAiEAnKTaAMey8ZJwKnL/2WbRQ2NxzYTEixhVkmU0DwiwVno="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-beta.1", "babel-plugin-syntax-async-generators": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-beta.2", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-LQ9Hn3LMcA7zeMZsPsb4JLkZupHhUF3yR6jIPE56yrPorikInYafz5qS4W7VfWgl/atxmC1AE/ntorH7R//hSw==", "shasum": "0fe554857b69a0dd4fb52fe41ad268c739dbf313", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJozO6MWOjMiA6qIDu3wlGKrTLO66g661DRsW/qt4vKQIhAIaH0OZh7Woknn7x4SReqLSvvvKFdXMeLyf5qrZ2644O"}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-beta.2", "babel-plugin-syntax-async-generators": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-async-generator-functions", "version": "7.0.0-beta.3", "description": "Turn async generator functions into ES2015 generators", "dist": {"integrity": "sha512-sIVkKihVjq930ONro7sMo080kyIznrEDdMmq4JX1OYtDVV9OiiUF/yrHnc2tBrldOzTYyD3iPeIskTDuRVAnfA==", "shasum": "0e6079201bfd7030c281983ed4e7662f6f986587", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH5CathXt1FN/awbzg7EpWTejeGiP3rCKghUwVZobz8mAiBdqkVABVsg2NAHBv50BvTSlyWWNjP9du2sHpAAI8wZ7A=="}]}, "directories": {}, "dependencies": {"babel-helper-remap-async-to-generator": "7.0.0-beta.3", "babel-plugin-syntax-async-generators": "7.0.0-beta.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:01.371Z", "time": {"modified": "2022-06-13T04:04:01.371Z", "created": "2016-09-28T19:38:52.572Z", "6.16.0": "2016-09-28T19:38:52.572Z", "6.16.2": "2016-09-29T12:46:48.504Z", "6.17.0": "2016-10-01T19:23:22.291Z", "6.22.0": "2017-01-20T00:33:43.589Z", "7.0.0-alpha.1": "2017-03-02T21:06:01.111Z", "7.0.0-alpha.3": "2017-03-23T19:49:57.352Z", "7.0.0-alpha.7": "2017-04-05T21:14:33.745Z", "6.24.1": "2017-04-07T15:19:37.629Z", "7.0.0-alpha.8": "2017-04-17T19:13:24.243Z", "7.0.0-alpha.9": "2017-04-18T14:42:33.254Z", "7.0.0-alpha.10": "2017-05-25T19:17:54.137Z", "7.0.0-alpha.11": "2017-05-31T20:44:02.064Z", "7.0.0-alpha.12": "2017-05-31T21:12:16.610Z", "7.0.0-alpha.14": "2017-07-12T02:54:30.479Z", "7.0.0-alpha.15": "2017-07-12T03:36:47.619Z", "7.0.0-alpha.16": "2017-07-25T21:36:29.182Z", "7.0.0-alpha.17": "2017-07-26T12:40:18.320Z", "7.0.0-alpha.18": "2017-08-03T22:21:45.678Z", "7.0.0-alpha.19": "2017-08-07T22:22:29.063Z", "7.0.0-alpha.20": "2017-08-30T19:05:03.695Z", "7.0.0-beta.0": "2017-09-12T03:03:19.256Z", "7.0.0-beta.1": "2017-09-19T20:25:01.263Z", "7.0.0-beta.2": "2017-09-26T15:16:26.223Z", "7.0.0-beta.3": "2017-10-15T13:12:49.211Z"}}