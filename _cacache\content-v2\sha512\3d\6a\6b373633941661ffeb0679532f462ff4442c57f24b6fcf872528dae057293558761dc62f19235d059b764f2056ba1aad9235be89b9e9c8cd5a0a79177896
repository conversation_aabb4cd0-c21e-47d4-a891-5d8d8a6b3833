{"name": "@cloudbase/signature-nodejs", "versions": {"1.0.0-beta.0": {"name": "@cloudbase/signature-nodejs", "version": "1.0.0-beta.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "_id": "@cloudbase/signature-nodejs@1.0.0-beta.0", "maintainers": [{"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "lcxfs1991", "email": "<EMAIL>"}], "dist": {"shasum": "231d05f99cad01bc464f68239557c6a5b9a7e7a3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/signature-nodejs/-/signature-nodejs-1.0.0-beta.0.tgz", "fileCount": 22, "integrity": "sha512-gpKqwsVk/D2PzvFamYNReymXSdvRSY90eZ1ARf+1wZ8oT6OpK9kr6nmevGykMxN1n17Gn92hBbWqAxU9o3+kAQ==", "signatures": [{"sig": "MEUCIEQv72t4tp1xs/yajH77oRFO1T8pms6b9WRr36fmog/DAiEApkF5+fvzWaN8vIu4TgrVxU3awFH7QEwYJfudo09pFA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd74rPCRA9TVsSAnZWagAAQdIQAImp1iYQ+rCdQRGFSR2b\nqgLMzR+hqNJc2Bs21BxNG31F4H97Caejd5/wxzhMJQ6uN/wEuAYar1TG775M\nNd0IJO9bkZbPEPenSRhJGZo6ZI4QUXsIeScHhwCkZh+MsFY2Ouk6FnFyIPmx\nZN1OeXJR0Q+Gv6ZB0otTkVk13EvtiS9Z4KJukzhUsWThxYbb4UtZAcnX5bf4\nRu1HN5d9ScQAJkqHF0chGyWCbmeBlckPlEPajJuxOpU6AWsRbPKmyvXntOzj\nDisv6fnuHlC8VK30pMoLdCdm80B1yRhcTxuox9i8Rf5w1VqJs1ifQ6Db7+VI\nKbgZ9yBYFwRJDAjVSvnyyubZ7thH+SyABCZwy5Pnxel7ujeiXgoWcQ39MDUW\n/v3KN+LbU6IeAO0ZGzWABejdMUfsubtIzgY5B7ggVfL+kI12gnxd4G+xlBW5\n0O8dueUvdtHU+ZkQu9vyZ3cARThCVweFqjlzdCfUgSXYHRhzPbfv6gP5JNIs\nJ+HGRQmgVRdX2vA9n41tgSIMFw8N+TjsOMfa6mh7jfiLwq4TzrqroUAim/YC\nZY+859xUOfVkmUelW4fg7H/QoN+yi7c2WMdMoPANeFeQN7epDIHTe7cpQdKu\n3r4pilYKrfUAqiO4AG1pFD1ubiqj6xgVEXmGbMlnB3P898xIO2CBg7s73HLv\nYD0F\r\n=oQ2J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ef63e4a9b0ac6141d36f8f022fd579c97b1eef10", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "_npmUser": {"name": "lukejyhuang", "email": "<EMAIL>"}, "repository": {"url": "*******************:QBase/cloudbase-signature-nodejs.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "cloudbase api signature for node.js", "directories": {}, "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "_nodeVersion": "10.16.1", "dependencies": {"url": "^0.11.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "@types/clone": "^0.1.30"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^6.7.2", "ts-jest": "^24.2.0", "typescript": "3.5.3", "@types/jest": "^24.0.23", "@types/node": "10.12.10", "lint-staged": "^9.5.0", "eslint-plugin-typescript": "^0.14.0", "typescript-eslint-parser": "^22.0.0", "@typescript-eslint/parser": "^2.10.0", "@typescript-eslint/eslint-plugin": "^2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/signature-nodejs_1.0.0-beta.0_1575979727363_0.1692169661172933", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.0": {"name": "@cloudbase/signature-nodejs", "version": "1.0.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "_id": "@cloudbase/signature-nodejs@1.0.0", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "07b543b85191a19414625f660eb71976c9a0629b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/signature-nodejs/-/signature-nodejs-1.0.0.tgz", "fileCount": 22, "integrity": "sha512-J5itnq+Af67smp/6NskZeGNC1fRRwt4IoGUEavNH4/pOg+hiBx+FDEyadgaeWnCuXGpAue/AeGq4d4K/YYRylg==", "signatures": [{"sig": "MEYCIQDE5ChpxV2aIYs+utw8jv8Ore6Q/HzmEIr3xjGNTp8DEgIhAIK5Wg5s0QRJvBRaKHghx7jG0X8EDRN7z8JnU8kSbRYj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmQJLCRA9TVsSAnZWagAACDgP/1zNOZlljunYNZ9A/khJ\nQfomHMRixCHAk2q015PVDdUDQzEHUintzbb8f3xfj7/5sA7VGyhwlPkYY4MU\nfdL1xGffDG1yQwltkbbMkVuwlTGAFejrQjDTo5MrwVNzaszvvKesT6GEJUGA\n7jbeKFZZxmg9bGgwjhRBLiE40cXzAhc9ExWOIBSNr62DBkOSR0Muq8mlMu/z\n/GF/FRxzTYlyenQICdUTHjffiB9bj+CSpFd83sRdU9fwo4YUP47n4BUynk6+\ni6RT1tqUq1YASIDTpIrJ3kOe5sfrrarXF2hzByYiYdT8CEdidVBeawicuJs7\nYLkBR0YOjSq7g+GKzQLJXgAVqIO+ozPeS05/i8YNIPnNeEZkdw+Np6qJ+G+7\nNP/5fNc0CK1xW5VwY+sdUbriwmf1DkCg9F3g54XYTzkG+XwaTGPE7AGt69/I\nEkdyxtyB339LGbCqS7WW2z6d1jVQBnzIvMXSweMacImpM18EwEnnypXRNSKI\nBkRqyocRgmMUOvD7i/0ZBSzLQrXx8ZgmdIfZKo0mxylRW9d04gESBKazYsnY\nQZJDDsiyL/omU7qUY1VebKFDwgnQ2ER4SpInJVEuqbh9Cen/DufJIK9VTLuq\nNL5f8ZGcbCTPS26G103cjZul2wjyJbDwj7KEjHSCEtJT0Ga/Fo94NHQb19NV\nRCDd\r\n=pJ5/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6ae9dd96bbe4e82f6eca4b05d85c6ad7795e4870", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "_npmUser": {"name": "godbmw", "email": "<EMAIL>"}, "repository": {"url": "*******************:QBase/cloudbase-signature-nodejs.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "cloudbase api signature for node.js", "directories": {}, "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "_nodeVersion": "14.8.0", "dependencies": {"url": "^0.11.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "@types/clone": "^0.1.30"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^6.7.2", "ts-jest": "^24.2.0", "typescript": "3.5.3", "@types/jest": "^24.0.23", "@types/node": "10.12.10", "lint-staged": "^9.5.0", "eslint-plugin-typescript": "^0.14.0", "typescript-eslint-parser": "^22.0.0", "@typescript-eslint/parser": "^2.10.0", "@typescript-eslint/eslint-plugin": "^2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/signature-nodejs_1.0.0_1603863114690_0.6628649590346822", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.1.0": {"name": "@cloudbase/signature-nodejs", "version": "1.1.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "_id": "@cloudbase/signature-nodejs@1.1.0", "maintainers": [{"name": "zijiezhou", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}], "dist": {"shasum": "479c6bc8b14dd6339a8df5b7f5a10d50ecca615e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/signature-nodejs/-/signature-nodejs-1.1.0.tgz", "fileCount": 22, "integrity": "sha512-Uqj7tbeAufyAl8xxHr0BxmO9mcfjYqs/FNTDif/38KeRRB438kRlnqd06nXNkJdJWgryw0xQGDxiywVxbAaLxg==", "signatures": [{"sig": "MEMCICJdwAgBwVAMJIEsbu3xZ/NiYxx6mHYTHjURuBmuQCG/Ah9d96f2LY+goMFvPaRsH9h1Z8emlTA02Z93jzlAXPFU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmRooCRA9TVsSAnZWagAAo90P/RwqfmVStFXKDgoPLehR\nHlk4AN2ea7kgNUTNWGtCaHSqj2Wj/T8engEFfLQq/KowT2czZB+r6J61D8xF\nq3NQb0WMekD/zsjQm4EVokQGSdwoNqd/vgrtqEBjlQM74kuYsKvwLsakILMK\no4Dz0fFNkptJauG/iBpDEucBvhdTt3ZKqqRzbAvZSUMBqNHGbfvuM5MpDQth\ngq6kcrZcyMqwLhtgximzAP8NSUXnQBWCy2JhqYjhiz3dGBBx+KfQ5G9zfWPl\nXEb9jqKwZeDyhHioPBRUyMelJ4tWOIl6BqfZQQemCM4qBt8qo6QfQW8hVo9p\n0lbiG/MXT51MX8LXY77xLKvxINA+deZKMqH7HhJaIddo1fSBLqIHKMyV1qtM\npm2GklGhX7fUjQ7JCv70CIEbFMSYLT4QwLMqccws+1G+kYmUvRodwcIT8JLS\nHrMmjCG3gRlOP3x3UDKt8gwVqvjJ5KLUtag9WFIslqJyjWMV2MGOmuGpdYZp\nbvw+5gXOZnAhlB7t86cNkhlpJYo6iOl0pYbhEFb4xf3U17gIMKTEGqKFPJua\nWYzXvFR/lwYVHKXxHP5IULj8IIgIQ1T61yAVO/jVuDeeJBbUKXxgxkg9ePMv\nSrfad37ZZuseigNYPt1xfuvPgOP9uyOm+TQ0WE00+wSCE5rADut7B2DF0nO8\nZYgA\r\n=hrLn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "04d79d6898d5540684ad947407768f51f81f2057", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "_npmUser": {"name": "godbmw", "email": "<EMAIL>"}, "repository": {"url": "*******************:QBase/cloudbase-signature-nodejs.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "cloudbase api signature for node.js", "directories": {}, "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "_nodeVersion": "14.8.0", "dependencies": {"url": "^0.11.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "@types/clone": "^0.1.30"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^6.7.2", "ts-jest": "^24.2.0", "typescript": "3.5.3", "@types/jest": "^24.0.23", "@types/node": "10.12.10", "lint-staged": "^9.5.0", "eslint-plugin-typescript": "^0.14.0", "typescript-eslint-parser": "^22.0.0", "@typescript-eslint/parser": "^2.10.0", "@typescript-eslint/eslint-plugin": "^2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/signature-nodejs_1.1.0_1603869223891_0.6729484416808769", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "@cloudbase/signature-nodejs", "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "_id": "@cloudbase/signature-nodejs@2.0.0", "maintainers": [{"name": "evecalm", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "jimmy<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ianhu92", "email": "<EMAIL>"}, {"name": "lukejyhuang", "email": "<EMAIL>"}, {"name": "wuyiqing", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "ihardcoder", "email": "<EMAIL>"}, {"name": "withnate", "email": "<EMAIL>"}, {"name": "godbmw", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "zijiezhou", "email": "<EMAIL>"}], "dist": {"shasum": "7aba18947cfbe4705f5c4a268360d0df2f3ccf26", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/signature-nodejs/-/signature-nodejs-2.0.0.tgz", "fileCount": 23, "integrity": "sha512-eFtRiZuD21+/s/4opejhcN8msFLBK5POwkT445yyA47kps35qXrHGqlLQgt00bmRU4VtjwN6LHr2O3PjHjWW6Q==", "signatures": [{"sig": "MEUCIQC6UUKN24h5umkx+D8tMI7zQU5ntkNb7NkV57UWC/o/jwIgOktYqHsUupzsAWYB3V3eEC7+Uu0DsMcrmUVO5dVPLPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwHYHCRA9TVsSAnZWagAA3R4P/1yJ3R6uJRIGI1KIIbbc\nWeMEtpdbMGpEypL/QqkDkR42jwVsEg/IoZna81B59w+1w/BPkEwHMe4MekTA\nmVugTS9U365dJWsyaU6YVCECTB+ooV9XWxzdArWduApySc9wjK4i04G8PKl1\nkcTd7mbut2nd82fghKwF6+8r8JARlEiMQgxQwAkZ3B5skfwhQag8hvtKFYNE\n2SnURZwQzrP8df1TfGA4/69jGyQKVF44ol4bfc56kliIYrRzeutJMDrvT8Nk\nlPnbB6eSVEtOIg/7df1SSkOGuXnDSmYYY42Y+KNri85x3SxnBHRtIYytwiHg\nvf563AoZP0n3qryZKbSybo2ZsxBjCXkDKRfuXNisrVZR747s6WfTUZQswet0\nXiqT2sZ7QwErYEHbKunbxd3/kHihIVcsRuki/gp0JbZxwOo+vjvJXuOrwpDo\nn3soWjlvri42MHUHuR6D1ZLdd8NQKTBYhP63RU2Y66l4X8DoBjtaYSwI2js3\nY3/00ly1pbPZc8WyRthFXPaAJH5FagA4PhN+V4JN2Q49B9MY1LQTV43iZNn4\nBxgfkcE7orkGhrBQ5pYwtAEYh0vzKIWCc31F2MguW1arrN/sgM2LjBldFDpS\n+dafhE99ljX12myXX0mY6TmmAmCijY8vJ9NL3DKl+QovIfW6JGNSzgeeVJmq\nApD9\r\n=2wTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7f9d703690c0cd2bc4c5164215c84fc9ff7e7569", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "_npmUser": {"name": "godbmw", "email": "<EMAIL>"}, "repository": {"url": "*******************:QBase/cloudbase-signature-nodejs.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "cloudbase api signature for node.js", "directories": {}, "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "_nodeVersion": "12.22.1", "dependencies": {"url": "^0.11.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "@types/clone": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^7.28.0", "ts-jest": "^24.2.0", "typescript": "^4.3.2", "@types/jest": "^24.0.23", "@types/node": "^15.12.1", "lint-staged": "^9.5.0", "@types/is-stream": "^2.0.0", "eslint-plugin-typescript": "^0.14.0", "typescript-eslint-parser": "^22.0.0", "@typescript-eslint/parser": "^4.26.0", "@tencent/eslint-config-tencent": "^0.13.3", "@typescript-eslint/eslint-plugin": "^4.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/signature-nodejs_2.0.0_1623225863500_0.37758442247865687", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0-beta": {"name": "@cloudbase/signature-nodejs", "version": "2.1.0-beta", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "_id": "@cloudbase/signature-nodejs@2.1.0-beta", "maintainers": [{"name": "yuzhen", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bob<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}], "dist": {"shasum": "29bd6f27f63c1c4ff7ce0dfee5e9c9096bc58035", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/signature-nodejs/-/signature-nodejs-2.1.0-beta.tgz", "fileCount": 22, "integrity": "sha512-zWjdf9fz8j8UJPZwV8z8Mey+JVy2P17VLfG7uq17lXatmsJE2we0EaD5rs5kJh1dpobWXIJ0Vi8LaOufx+FmdA==", "signatures": [{"sig": "MEUCIEQtEdmpMiPxFl4yiBro7jnoOSfjbYjoAg6J+2z8wW4XAiEAzD3IfebC8DrIAz/p5LrTDHSxceOpYNpGVWB2p5JdayI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25314}, "main": "lib/index.js", "types": "./lib/index.d.ts", "gitHead": "93c5f574763760ce6b2328f31ba133f2e021f06a", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "_npmUser": {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "*******************:QBase/cloudbase-signature-nodejs.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "cloudbase api signature for node.js", "directories": {}, "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "_nodeVersion": "16.20.0", "dependencies": {"url": "^0.11.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "@types/clone": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^7.32.0", "ts-jest": "^24.2.0", "typescript": "^4.3.5", "@types/jest": "^24.0.23", "@types/node": "^15.12.2", "lint-staged": "^9.5.0", "@types/is-stream": "^2.0.0", "eslint-plugin-typescript": "^0.14.0", "typescript-eslint-parser": "^22.0.0", "@typescript-eslint/parser": "^4.29.0", "@tencent/eslint-config-tencent": "1.0.0-beta.5", "@typescript-eslint/eslint-plugin": "^4.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/signature-nodejs_2.1.0-beta_1711367014754_0.3530456247669147", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2019-12-10T12:08:47.172Z", "modified": "2025-04-15T07:26:34.805Z", "1.0.0-beta.0": "2019-12-10T12:08:47.452Z", "1.0.0": "2020-10-28T05:31:54.873Z", "1.1.0": "2020-10-28T07:13:44.056Z", "2.0.0": "2021-06-09T08:04:23.721Z", "2.1.0-beta": "2024-03-25T11:43:34.909Z"}, "users": {}, "dist-tags": {"latest": "2.0.0", "beta": "2.1.0-beta"}, "_rev": "7259-96d8ff150a0b73bb", "_id": "@cloudbase/signature-nodejs", "readme": "# @cloudbase/signature-nodejs", "_attachments": {}}