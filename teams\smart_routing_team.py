#!/usr/bin/env python3
"""
智能路由服务团队配置
"""

TEAM_CONFIG = {
    "name": "智能路由服务团队",
    "display_name": "智能路由助手团队", 
    "description": "根据用户问题类型，智能路由到最合适的专业Agent",
    "brief": "智能识别问题类型，自动路由到专业助手",
    "category": "team",
    "avatar": "touxiang/890.png",
    "members": [
        {
            "agent_id": "schedule_agent", 
            "role": "日程管理专家",
            "specialties": ["日程安排", "时间管理", "会议", "提醒"],
            "authority_level": 2,
            "order": 1
        },
        {
            "agent_id": "composition_agent", 
            "role": "写作专家",
            "specialties": ["写作", "作文", "文学", "创作"],
            "authority_level": 2,
            "order": 2
        }
    ],
    "collaboration_mode": "route",
    "decision_mechanism": "best_match",
    "workflow_type": "customer_service",
    "output_format": "direct_response",
    "routing_strategy": "keyword_based"
}
