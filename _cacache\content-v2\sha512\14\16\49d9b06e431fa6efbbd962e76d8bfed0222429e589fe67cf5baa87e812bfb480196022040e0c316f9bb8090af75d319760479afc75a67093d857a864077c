{"name": "compress-commons", "versions": {"0.0.1": {"name": "compress-commons", "version": "0.0.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "784399b3249f07ee077fc62865ac869e7ca87bf7", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.0.1.tgz", "integrity": "sha512-9WMAic3zaVLx14pO3jcoNKtdEowQVzt7Yj+6A/kCM1Z8nXrYxhJPlpFZ9AD8dgRC532d8EQprdFac5NXCXSEJA==", "signatures": [{"sig": "MEUCIEXtThGlOZ+sqE3EgWa9XcCLMqGZm+5aoyK0C9pkpvsIAiEA/BkcOp/kWH2WczilqkVTBqFU/L/cGA8XxPNxbb2GW/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "784399b3249f07ee077fc62865ac869e7ca87bf7", "engines": {"node": ">= 0.10.0"}, "gitHead": "44d57026a9a26504c19b0d98ce0a64c2fc3059ec", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.20.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.0": {"name": "compress-commons", "version": "0.1.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "b4902b13da70c0b70e94379727858915705bd039", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.0.tgz", "integrity": "sha512-MUBO3+iatIPFw4SrwAiyXmto9pp1eFH4KdcSI9+A6cawNjtNfCwhAbKssnjgZLFV+YOwlvl3UUqJQ6Y1Jxq4Pg==", "signatures": [{"sig": "MEYCIQDUvnXudyDtcdS8uYw2LCqQQkdYWKXI/OCfKeFKiJeFxQIhAIydNru9lpBbNd/NJc/sj3VKvgdmqIA0DDeFnczxVNjw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "b4902b13da70c0b70e94379727858915705bd039", "engines": {"node": ">= 0.8.0"}, "gitHead": "a27e88379543a4afe6c2ef6eee0d7ee6043ebf72", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.1": {"name": "compress-commons", "version": "0.1.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "431ac2562471c2327e8eaa60e31ba87a3bbc6629", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.1.tgz", "integrity": "sha512-x1BHsb6rttH8fMqKmvhgWcoG0vZybPOLl7nm8RjgP2+pPFZFK8c123NvZsHlVZG7u86rfilNgArTiRoypnsQ2Q==", "signatures": [{"sig": "MEUCIQDfM37oRXX/Z0Kr3ceiKPQmatCmRlt1D9/YgnMpA3bl1gIgcBCLQ4UGvzBVIZG6F5GB41si9NIxdNOG211Y3YqgSsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "431ac2562471c2327e8eaa60e31ba87a3bbc6629", "engines": {"node": ">= 0.8.0"}, "gitHead": "b579fecb0f2f9db3f21f725eb8496aec81c74c0c", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.2": {"name": "compress-commons", "version": "0.1.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "470415d4b1f58897027976f77eb180ffedacf0e5", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.2.tgz", "integrity": "sha512-7KO2KcI9vtHByvjCS6zUMXHwnlkWS69FoHP/JjflTABKJY71VFz6adottvy7rlhi8V67+FdEP6PBPCRhd6lUBQ==", "signatures": [{"sig": "MEUCIDCZKUBE8NnRuCnUAosWTPI3JycbheTZ6lqyFBaZGHGzAiEA72PClByHg0wzdyfIRbL84mFw3V3xAGS1Ca8nn1xAeFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "470415d4b1f58897027976f77eb180ffedacf0e5", "engines": {"node": ">= 0.8.0"}, "gitHead": "c74c4774741bc800e52e5bb1ef895a9b3f955409", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.2.0", "readable-stream": "~1.0.26", "deflate-crc32-stream": "~0.1.0"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.3": {"name": "compress-commons", "version": "0.1.3", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "839134efe68d2a2d5c40bf4ed45c20a074cb4985", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.3.tgz", "integrity": "sha512-QDD0bMA24omJ1oSWCPpx+4IseHOLaZiF6Db15x6bVZkzTjYbxGNtVtZDd+WBou7BQ8LhYevsqDJs9k/GcxJQPQ==", "signatures": [{"sig": "MEUCIGKFVK5hF/NmEkPbMUCmX8Odi4/C4R5F3O/D4+mYMntIAiEA7Wc8O7VJk+cPPWPWTOwp/SZleWp/Xts+G0p6mfwGeBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "839134efe68d2a2d5c40bf4ed45c20a074cb4985", "engines": {"node": ">= 0.8.0"}, "gitHead": "1c09c0f0bc23a8d20238a54e6bbcf3864142b3ee", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.4": {"name": "compress-commons", "version": "0.1.4", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "ba244e014e1102d1857550c2c4bca58d85a0a110", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.4.tgz", "integrity": "sha512-6rsoGRSFlFQ+yeRT4IEJ1eKFtzUiOSMW2Uyt4m/E/0gHklAdG86ItBZhxOQJmsUeRRgIdEHvaJVAg86sdmK82Q==", "signatures": [{"sig": "MEYCIQC6pHsEqRevn0TcZ92jUZx+8FZVbjl56Vh/NsR5bm487gIhANSMk/ZB6c6Z6pMnSGzNN3uU1rx2yZ6+KDTz/I5+E87O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "ba244e014e1102d1857550c2c4bca58d85a0a110", "engines": {"node": ">= 0.8.0"}, "gitHead": "8cc51d88fb588c93530988908fed2964d1508981", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.5": {"name": "compress-commons", "version": "0.1.5", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "b6bf75b4bc2dd301827af039a30bb6fe86d57f03", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.5.tgz", "integrity": "sha512-+oAmsSO58OfSAMURhbLjS18w1ywAa7WE/gH1aO/PkYtdYhqwsvs1cLEsaDJ5OJlNsYo2YxsT6/+Y7s3oP8DnMw==", "signatures": [{"sig": "MEUCIBbKhvQiiN7uqlSUGVJ+al0XCU+Kb/XohfhFu+wxp5FtAiEAq0rU+2k38ruN3aZsoKEOIuPFKWEYvLsCJ0tl2iM1xZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "b6bf75b4bc2dd301827af039a30bb6fe86d57f03", "engines": {"node": ">= 0.8.0"}, "gitHead": "91c76feeb9564f4fb0dd62aea755017c1ae418c6", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.1.6": {"name": "compress-commons", "version": "0.1.6", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "0c740870fde58cba516f0ac0c822e33a0b85dfa3", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.1.6.tgz", "integrity": "sha512-TiSO1gzpHUM+UAKHK+THSSmqAFIx+6mq66jK55YS2kJ7RTiwO+1LwdHNcDzgEB2iB1KYf45aaOgjpY3PnAh1KA==", "signatures": [{"sig": "MEUCIQDuSbzsyceAAD3o5g0l10GRDzlhjBLKxcfLiB5eVPOx/gIgBbA+LmjutbRnEYTRrHMaElXPxfbujoVjoP1x/hklpM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "0c740870fde58cba516f0ac0c822e33a0b85dfa3", "engines": {"node": ">= 0.8.0"}, "gitHead": "3575877461837f7ca9181e2d0737b001f42d880d", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "a library that defines a common interface for working with archives within node", "directories": {}, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.0": {"name": "compress-commons", "version": "0.2.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "6e43a28c1fda93b781310f97845e91884911f7a1", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.0.tgz", "integrity": "sha512-Ds6an3QKT0ZjADQ3FKz0l1Y+OmGNfI3d9MnQ/xp+cVijeeUiUs7vRjRJ2piTYFAVCRD9Fww8b/RmLvcpMbyLMQ==", "signatures": [{"sig": "MEUCIQCaWweqtFMuhdUyT4uhoNoSZ+C1pQUha9cfXITuo5F+dwIgNzu42Mx/2ihTBawDi0mYMy9Q2Fg/cAeDq9vqa/JsY0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "6e43a28c1fda93b781310f97845e91884911f7a1", "engines": {"node": ">= 0.8.0"}, "gitHead": "0e20d112b7c909fc0c2dcfcdb8b593bce2c87de7", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.9.1", "mocha": "~1.21.0", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.1": {"name": "compress-commons", "version": "0.2.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "9e6efa8772e5e75be75357ad35711dd5015b7ccf", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.1.tgz", "integrity": "sha512-Z0jGEifxFnsl8b8CnBYkhe7hjyeYKeRgKvLSSofiZdpx5s7GdZhskVvoLsx8O3bp9iOpdQHJgfE8dnayt1KJrw==", "signatures": [{"sig": "MEUCIQCj4Oc+6sYu7z5eQlvYgi+6Ogi/HSPNNOEARSpOoZ8rKAIgHKYJ/Q5GddUreumS9cT6p1Jv/9VnhbyohJYw13pJ6LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "9e6efa8772e5e75be75357ad35711dd5015b7ccf", "engines": {"node": ">= 0.8.0"}, "gitHead": "594fc16681a078e7bf31cff4425fd0f31eec8510", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.2": {"name": "compress-commons", "version": "0.2.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "ec94addfc0037e0276602435a17fd22d40937421", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.2.tgz", "integrity": "sha512-Bh0+uvt8sXnt7O2TyW0E2sp5EP42j9nxS5PMyi9a4diqsa1Qel16JFvyapPYPX9LDDLFvPAYZu8BnaDV2T7XpA==", "signatures": [{"sig": "MEQCIB7KQ9yR7gniFgwkMK+edvJWOynRc4j3uh6+q/ZVspQqAiBxsWKA3sKmEWU48N6UFLnauz4yX8PAYAvammh9Aq+siA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "ec94addfc0037e0276602435a17fd22d40937421", "engines": {"node": ">= 0.8.0"}, "gitHead": "14c4c9bae387eab8194163762f0f57ecee4bdf99", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.3": {"name": "compress-commons", "version": "0.2.3", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "b5401a55d2c9350e336aaa00542392e6bb46df28", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.3.tgz", "integrity": "sha512-M8cAbg/QE6PQmWY3ccQX1c+Ow/dXNsACZbh21HSX4ojX+/tYru9CZ1OD/WkQuDTyksaDPyitG+Wr90XG8VUgzA==", "signatures": [{"sig": "MEUCIQC/rTkPTtDCfF9uSBzu26LoTFy7wbEpebqFbmCrd6gLzAIgdgDhi9pu4hSwU9tQgfiYdGwk0/meY/ZKtLO7tmT08Hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "b5401a55d2c9350e336aaa00542392e6bb46df28", "engines": {"node": ">= 0.8.0"}, "gitHead": "327c0fb0e3fad6ebe617f22851bb094879837140", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.4": {"name": "compress-commons", "version": "0.2.4", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "0cdf9bfbe2e528050a05df4aac503277b23f1bd7", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.4.tgz", "integrity": "sha512-L3oaA9xcVP9+fZigJTaUGwk173uQS5XFx842Vmxkdf96BRw67hXR6E7RLZkDJ0cXw7rJgUsd08p3ykrBOj9y6A==", "signatures": [{"sig": "MEYCIQDEvUEe5guMkzcm9F+CBkdp/aCGZtlmKuzgN92ZGNZ59wIhAOjSPmPHlLaIfcOeqWdZJLJXCooyq01wtLV3Xbov7mFr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "0cdf9bfbe2e528050a05df4aac503277b23f1bd7", "engines": {"node": ">= 0.8.0"}, "gitHead": "71094290fe4757874e06f44f0fa69e03c2b14a41", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.5": {"name": "compress-commons", "version": "0.2.5", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "caa47955c0e73639a704823feb7ba6b5af5313a7", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.5.tgz", "integrity": "sha512-g3iOhfQYMenGgEW4cLSYSpdNweAlRI7FRdqAStHpM42x/x7LDHmR93EDyWGbXi5rIb5ZbKYfxWAaBuOunTrQ5g==", "signatures": [{"sig": "MEUCIDpjEPPeG/4f/WvXlb3CGCkoJX/x5Qkz/VJB79iBUFwkAiEAhfYTPlVo1hwFrPlFZoikhAP6RuQ5EXZmU39Ej4ZRl00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "caa47955c0e73639a704823feb7ba6b5af5313a7", "engines": {"node": ">= 0.8.0"}, "gitHead": "ee99b768b17e77eed796b2eff5a4094e5dfac89d", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.6": {"name": "compress-commons", "version": "0.2.6", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ctalkington/node-compress-commons", "bugs": {"url": "https://github.com/ctalkington/node-compress-commons/issues"}, "dist": {"shasum": "e1183973f640cc20519f48428fcf52e3b7901738", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.6.tgz", "integrity": "sha512-ID24NWdCZE8qarR7nmFRwn8HG4OOXVqzBnruG1CEDijYFcA83dZb0aMGr+0/twvekqIBIjm/vgXnx1P9NWOpmA==", "signatures": [{"sig": "MEUCIGG4lKXeWJhkmAnXPKlLHAQ1OWZeU8MzQ+uzgwVOi3MVAiEAthtC35+CvcmMzqL3HBhNqq1EHRbDG+0z3FiT48JBY+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "e1183973f640cc20519f48428fcf52e3b7901738", "engines": {"node": ">= 0.8.0"}, "gitHead": "e9df6a2902010efd24f30f88adb9cb541b82621b", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/ctalkington/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/ctalkington/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.7": {"name": "compress-commons", "version": "0.2.7", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "0c68d0eb4242dc0042705b7591d29a5e995adc5e", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.7.tgz", "integrity": "sha512-Q+7DZ<PERSON>5jL27sgh9bgYCZfFy5mC6XShw651HjxkYErxYF4z5HjiI7Hf9kBI8rPTB8L09l11GWp081PCl40BZqA==", "signatures": [{"sig": "MEUCIQDr68Ju0WePU/+PnHe2T0hF9vikX5OMHm7rW56FZuEMKgIgdyAYBYYZ+C7XK3jeijveQ6IUjgStM76MIV73ETiySnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "0c68d0eb4242dc0042705b7591d29a5e995adc5e", "engines": {"node": ">= 0.8.0"}, "gitHead": "a5e5c01424894053993eb4b0aeadfe9b7992475f", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "devDependencies": {"chai": "~1.10.0", "mocha": "~2.0.1", "mkdirp": "~0.5.0", "rimraf": "~2.2.8"}, "contributors": []}, "0.2.8": {"name": "compress-commons", "version": "0.2.8", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "_id": "compress-commons@0.2.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "607cde20a7bb66b65f40868c0f74bfac6c52f740", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.8.tgz", "integrity": "sha512-i7bRqHSMWLdKQN6PMQxyollOxqylmkP86Ge38Qh7mUhjlG9fxoRi57xR0B6LF68eON+DzKIMJT7mOfO3kUcPvA==", "signatures": [{"sig": "MEUCIQC0CjtaPWs1lrj/5sKLK6n0S++79jD7FvbxctuPrUN6KQIgCacDGWSVx79V7DmeIePjvmC8hvcSRrY5P3q2lPFdTYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib", "LICENSE-MIT"], "_shasum": "607cde20a7bb66b65f40868c0f74bfac6c52f740", "engines": {"node": ">= 0.8.0"}, "gitHead": "5f9edb697912de97bd7ce6325b5ef9516cd25bc2", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~2.1.0", "mocha": "~2.2.0", "mkdirp": "~0.5.0", "rimraf": "~2.3.0"}, "contributors": []}, "0.2.9": {"name": "compress-commons", "version": "0.2.9", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@0.2.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "422d927430c01abd06cd455b6dfc04cb4cf8003c", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.2.9.tgz", "integrity": "sha512-Q+fB/YztsPFUK0GjtWIDamac9XFrlIU23PVjpgp+Fa7g05F6mq4J43mg3EZDFhe+DjnmF/jZS7S3kmLaIRhPBA==", "signatures": [{"sig": "MEYCIQC0OyKoVi1s9aW2Vh27WzCtdu3SV1ylXnNQ+bmQf3fiLAIhANqTQi24QAfVmqL6rMcdVukL4IV0JroRPI7/dhT4l96u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "422d927430c01abd06cd455b6dfc04cb4cf8003c", "engines": {"node": ">= 0.8.0"}, "gitHead": "a6e2a81a5b250a6b127ee12fa5f1398689bcc9c4", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"node-int64": "~0.3.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~2.1.0", "mocha": "~2.2.0", "mkdirp": "~0.5.0", "rimraf": "~2.3.0"}, "contributors": []}, "0.3.0": {"name": "compress-commons", "version": "0.3.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "97093e2e193f7567fa13203d4b8defcd5971a519", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.3.0.tgz", "integrity": "sha512-oFRQjZ8ROyPJY2CqdpUEHm62uyfOFySHW90QHXsSKYhFqvoBIlDaceLJDRw0mE2Dhu6GTAXPqlFqs5xkdyo5vA==", "signatures": [{"sig": "MEUCIQCZC05ZEftygW3qvJFjhUMSJPLHW53QyITutZIotG2O4AIgWum2IraE2D/rQ2xFREgQfuvAAu0B23tKQoJcyqYKYb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "97093e2e193f7567fa13203d4b8defcd5971a519", "engines": {"node": ">= 0.10.0"}, "gitHead": "dc957e2e95839207bc756a950fb222eb001f41de", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"node-int64": "~0.4.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~1.0.26"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.3.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "0.4.0": {"name": "compress-commons", "version": "0.4.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "6af8b155ba3c56398c39d14589b4f0b0dfdba09a", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.4.0.tgz", "integrity": "sha512-SUtkwsgafWUHPruMkHgXP3N7dL1nYy/k/3/O656XmJZdnyuupLh43IUaxp1iXhwCk2NoSXvUPY6p1Gg6mr+3IA==", "signatures": [{"sig": "MEUCIQDD7HQV3ix0VfJuqjUcCmqg4rcY/udg0HIm1TKlg161OwIgHvwon9Lifkz/eO5UPZsZadEA19ZiG0oofVwASvM/49U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "6af8b155ba3c56398c39d14589b4f0b0dfdba09a", "engines": {"node": ">= 0.10.0"}, "gitHead": "e00156f5291ff2d9e08feef8f29c6217ede83a6a", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"node-int64": "~0.4.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "readable-stream": "~2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "0.4.1": {"name": "compress-commons", "version": "0.4.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "6d4aac6fba4af654ee4f80ce61159a87b5d98121", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.4.1.tgz", "integrity": "sha512-MFIYeb3VWlix2vQDmdd0/Sgl6BWC+K1wRhCk1F1i3J1OTwX+PJf3l0ilHn6ECslPIuk3q99n9OuLE3zRXvVc6A==", "signatures": [{"sig": "MEQCIGrx0BpVZ4/w+1ZcSFKCPF8dI1srQ1D7tDljhXB7EZOBAiBduHKF5NlXsfzHtAeTJfvFL/gFWnep7F80NxAQHri4eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "6d4aac6fba4af654ee4f80ce61159a87b5d98121", "engines": {"node": ">= 0.10.0"}, "gitHead": "5dacf567822ea0ad23eca0eb09b6a21c559b84c5", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"node-int64": "~0.4.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.4.0", "readable-stream": "~2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "0.4.2": {"name": "compress-commons", "version": "0.4.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "1a0847489f0d713d0dd76343c0c2d055dc9cd892", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-0.4.2.tgz", "integrity": "sha512-vOpPmcEyBQeCjJF/7cGCXRPguF1MUTe20oi11uZkLC6NMmMkwN5mbU6nJ6xVYuCM+0dnttlJfCDXND6DcASbSw==", "signatures": [{"sig": "MEQCIEf0ZzsXN1Lmt3uN8eDkQDPlIw5mTBuad8i0Jx8Hr3nTAiBzdhCLtpBHS03DLSXh1NQxHVDUYUIl/pVEBne06laVeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "1a0847489f0d713d0dd76343c0c2d055dc9cd892", "engines": {"node": ">= 0.10.0"}, "gitHead": "d1c62f5891ee68e4ed2fa66571e1d87e3f0db7eb", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"node-int64": "~0.4.0", "buffer-crc32": "~0.2.1", "crc32-stream": "~0.4.0", "normalize-path": "~2.0.0", "readable-stream": "~2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "~3.4.0", "mocha": "~2.3.3", "mkdirp": "~0.5.0", "rimraf": "~2.4.3"}, "contributors": []}, "1.0.0": {"name": "compress-commons", "version": "1.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "eb11bc5f56485dfe508c368e4bcda399d1eae06b", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-1.0.0.tgz", "integrity": "sha512-7nAFke+38Igd9a8cZJS1lXbR89Y5W6IDWS3zXTYtrmkjY6cw0lZfO0ImIogZ7BgAOxQKowWy207TkMUwnQuvug==", "signatures": [{"sig": "MEUCIQDT9jw+wOLyuTROgmF/nOqTdyVkzk3U3aq28Rn/XZW9kAIgIj0/Q/k2DJU2EQE01g0ffKmHtYpriO5qwnXYjBdl/Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "eb11bc5f56485dfe508c368e4bcda399d1eae06b", "engines": {"node": ">= 0.10.0"}, "gitHead": "a247092bc3a77b1a0739554ca6a7cf810612cb3d", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"node-int64": "^0.4.0", "buffer-crc32": "^0.2.1", "crc32-stream": "^1.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons-1.0.0.tgz_1459915608873_0.4079626954626292", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.1.0": {"name": "compress-commons", "version": "1.1.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "9f4460bb1288564c7473916e0298aa3c320dcadb", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-1.1.0.tgz", "integrity": "sha512-KUqWogolHagukw9esQyDGS65EBNbohRpBbrINRB7HTID4o9IdZ0pahB/xwk3LYri7IHb4qjfEDXrzW4qL1DJTQ==", "signatures": [{"sig": "MEYCIQDFt1kRGellbYcAWHBi7Z19vQ+BkV1NvHgsuyLMpraf/QIhAPiNlaavZnxc7L4bqDokJH7Wq0z9Hq/OJsSDVRsc8Esj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "9f4460bb1288564c7473916e0298aa3c320dcadb", "engines": {"node": ">= 0.10.0"}, "gitHead": "448041945474a3e543578f8548f9c4cba471225e", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"buffer-crc32": "^0.2.1", "crc32-stream": "^1.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^2.3.3", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons-1.1.0.tgz_1472322432534_0.46580693405121565", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "1.2.0": {"name": "compress-commons", "version": "1.2.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "58587092ef20d37cb58baf000112c9278ff73b9f", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-1.2.0.tgz", "integrity": "sha512-HLNsAIcf/82HGfGemqer+Sc7iscULwzSATNzV2MaH2GcgovYJpdE7ee9xo5uyTsm+vjVFqC2HjJ/0cozrd5S3A==", "signatures": [{"sig": "MEUCIQCcJtRuTCI5Bo3Jj10mCoUkP0zBHSet21uS39Di1XLxgwIgO9eoUb1z+TilIasZ+Mp7AWL3bviiKG7hHBenVC6zsac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "58587092ef20d37cb58baf000112c9278ff73b9f", "engines": {"node": ">= 0.10.0"}, "gitHead": "259d1a1785b14fde269ec0f87168172bffee511e", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"buffer-crc32": "^0.2.1", "crc32-stream": "^2.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^3.2.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons-1.2.0.tgz_1489892045829_0.4045467278920114", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "1.2.1": {"name": "compress-commons", "version": "1.2.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "03bed4571850bf874b4283a73726928768f6472f", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-1.2.1.tgz", "integrity": "sha512-Zb6pEF7wNeZKcElR0dBwioWwZ5+vcqFWFSjjES6SD/G5ozJ+YHg7mHYkBgbN7YEBv/krzmf1CvRYHtgPItPMBw==", "signatures": [{"sig": "MEYCIQCmA9+RSw+juNuX+/GqJ5uj+ewgIgqIqZzy4ieNNQiVGgIhAO/0Ci+tb+KumcBASonmjxLPtV1r6hExxOfpitJ46UuT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "03bed4571850bf874b4283a73726928768f6472f", "engines": {"node": ">= 0.10.0"}, "gitHead": "8fcfb4f14db7f49689696d3864d1791ac27d32bf", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"buffer-crc32": "^0.2.1", "crc32-stream": "^2.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^3.2.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons-1.2.1.tgz_1507409957235_0.22769984253682196", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.2.2": {"name": "compress-commons", "version": "1.2.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "524a9f10903f3a813389b0225d27c48bb751890f", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-1.2.2.tgz", "integrity": "sha512-SLTU8iWWmcORfUN+4351Z2aZXKJe1tr0jSilPMCZlLPzpdTXnkBW1LevW/MfuANBKJek8Xu9ggqrtVmQrChLtg==", "signatures": [{"sig": "MEUCIQDLTqFVw4uf/QeFPg9QTkAADKkBq91neUpoezrvhaJAIwIgLoKQ4/N1DjNMAl9kqTYjpDPS3kgd6GLksBdtHo8GZhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/compress-commons.js", "_from": ".", "files": ["lib"], "_shasum": "524a9f10903f3a813389b0225d27c48bb751890f", "engines": {"node": ">= 0.10.0"}, "gitHead": "8fcfb4f14db7f49689696d3864d1791ac27d32bf", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"buffer-crc32": "^0.2.1", "crc32-stream": "^2.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"chai": "^3.4.0", "mocha": "^3.2.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons-1.2.2.tgz_1507661532060_0.6104233136866242", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "compress-commons", "version": "2.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "c555107ef865eef0ba8a31fe56ec79f813ed3e65", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-2.0.0.tgz", "fileCount": 14, "integrity": "sha512-gnETNngrfsAoLBENM8M0DoiCDJkHwz3OfIg4mBtqKDcRgE4oXNwHxHxgHvwKKlrcD7eZ7BVTy4l8t9xVF7q3FQ==", "signatures": [{"sig": "MEQCIEGSZGphvFzuoJqBiRbxPoh/lDiNLO5mYbs2ph4slLqTAiBQ4qbZgLxEkAi12TqFa3yCGrZr84zgGrsmgcZJTFKKfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMlLWCRA9TVsSAnZWagAA2wAP/1wz3w6jMx4kuk9OaanR\nE8CzGWLao8ngrIg4Uq+iuJ82gFq1dTceln6f8j4XdlJhUqInf3jTgl3yFTz1\nidwWTmdNdzPREu6ZxK/A4/PV3oW653fnomjH2g/TgADqj60XIPqvCUivJYTd\n2vQoso9pQIT33jJxCrqvhR0QWCVkdnwuexrP3y9H8wTlUM9Wpum6KhqXmxFf\n/nQvfgpj3bwRaRMbL5pY0MIe37NW+X92HYsH1lj5jCtQMhliIF52g/gX3XYl\ntdi9YymHHwFp2PbPZlaG97GkxxBlJhgnm6iOSlhHpJzuUCWS/7Wwsvp3TOsi\n1i5eJS92tiGnmybrMhMNdFXYJnWEyWv/qh3EIc4iTAc5nKy7koUyC+9lG6pK\n0vRZ8g4YbeKBGhFWFlx+y3vIj4k/vKYgi/V9sKx1fXl24drkkxps90UdumEp\nWcRoLWn0bbavCk9feABlRCVkrO3s5N4ORWaowLk0kqqO5+l3ZtbIMtgycXo7\n9wqk8nJnoPMGZGhyt6ULFI1wwzgU3u+FtNerPNdxONhVg9M+gXnRQyqOzyDv\n5tgXYMzcdWwbYb2qwRly4ETLRweqYCe64FS83MR4pCPKrIRPDd5R+8Rp86bU\nf/IawHjSKdumIW7LMHv4A9HHuYyTVpBnhVKC7fJnsJei0uSF2oRqFAsDNF7K\nOjjd\r\n=Mppz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 6"}, "gitHead": "5bfff1e3fb4c4eadc5425039a74df8734fdba8c4", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^2.0.0", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^5.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_2.0.0_1563579094218_0.8715348190271233", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "compress-commons", "version": "2.1.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "f9f5633b7470bfd236e03242cc0d8d70ded49f48", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-2.1.0.tgz", "fileCount": 14, "integrity": "sha512-H6DhMQxI7NpvOGAjjop90bIOsGIZ8O1ewdKuS6ZTlnFA4YMpIhyajc+8fyEz4Em0c3iLjkOVfPJ+wtvy3RksQA==", "signatures": [{"sig": "MEQCICf8AXSdgUYB2eRyl0UIKPtnjASql2oo41xPKq+vZJWaAiAQ3nx0SV7DKRj6gU3EfvnKziPcYSGK+EuOzgTzApapwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRF0VCRA9TVsSAnZWagAA2zkQAIwXvP3LIdtqOW5ZKmdD\n53ZWLMDyz8yGvG0KPPyMAX33K4GdRAd/KX/fNg6/V9vLlosYZvjrs1qwv4m9\ngRvvnt3qXQB9oXKfz01pVgY1+APAlifNVrp/ZQLIpB+XI8dUEMnETBqml0LT\n6JauAfu3NyF39bAs6WzAjDEt7nWjQrlWU2Zs2A8ZOWtJq1nhMpplprpNd7jL\n+t3ppV0OXf0O28LQg/4R8ynoq5KWvDtYERoh26iv9l0+ktVhgKeq+r6ZUhQU\nDwY6wZjQVoVesEKrlnIpPul77UzfHwWeyRJPTszRRf2LX3HUoOiRydRU2o2B\nvDmgIkfpju1hcpYYuoa61P2ciWrj9XAwy5BNWyMSkKrMNcgp73WPq5xitzpp\naKO48ID2vYlNKbmOhIYD7DYX5KpmoEvpjM/CLSF4r0QtLSKqIeDeQMZ1NGiK\nVR9lG2QVuJuIc8PveFy/6BCFP5QlBuwpl79FqvemLV9ms2B6s0YeB7nqUW5l\nIAoF0uLHDaXUpoiSK3oz+eDQKzeNGR2CdSzxK+H1RmixlGOvAczuhCcIZ0fr\ngA8ZEDUoOkj03VT1JErNwqjC+9xRvBt3KdPdOEJPyeDCQtyNlKmV6zfRMQwp\ncI54d3sT0+5jzR1RzOO4DnRmGQial33W4/AycXEjmYToUs3bwGhrjSxhq6iP\nWpfo\r\n=utuI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 6"}, "gitHead": "111421cc109aba4199d9644f3e78a5202e6d0421", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.0", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^5.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_2.1.0_1564761365259_0.7557260244917219", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.1": {"name": "compress-commons", "version": "2.1.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "9410d9a534cf8435e3fbbb7c6ce48de2dc2f0610", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-2.1.1.tgz", "fileCount": 14, "integrity": "sha512-eVw6n7CnEMFzc3duyFVrQEuY1BlHR3rYsSztyG32ibGMW722i3C6IizEGMFmfMU+A+fALvBIwxN3czffTcdA+Q==", "signatures": [{"sig": "MEQCICF3Jp7V9woatKpMGRrwsBblHAKx3fpxZWK5OtIea3pYAiBdlbFuSt0SURsf3auWZUcS+khaUyZuaenf5cN+OPLgiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRL8bCRA9TVsSAnZWagAAJocP/AzghjktsNPKSv5iv/v5\nz6kFTK8UD9aXFIPrED1H+uRD7vkUX0t7JI0HyWFJMKWKY6xLb5DkOceaCo/a\nGIJ0lXj0zkKzsf1d1eXDdxlgGD8zK27pcxrIYqrxbHUVjHm2ZHRVeh+ajoHI\nMZRdP/JZWtn9CukFfV1JOujcEgBaRzVKg9JxyTPmOTH9TkP+KICm9dTM7OCM\n/t/RsfRB9kSVqU5cClj4C/bFNDIgAjwKCOSehTQZkZSUHoPpmDwt2lEvExQv\noZxyh5WQQeVuowCA27TY0jGRhfOFtjSXrcoWXQ1t3xANZrt+C9BXacU7jFSy\nmtnzI9Rc2ByA96YHBVNMzxhBGu4J62ILON/dIM+2jaYXCFtsYTjmwJIJl0BQ\np6HItlwjaLaYHZojgFVAks86VyJF/7tf3igYOtI0d/J0p3GLZHSoJhlRnr6I\n57XRGZh9WLg2koUR/6ZhShaepYadTXB/ljj7P9ZloPnZbMxC7LLQMy80Ombs\nATm4fiKRbWOCS6fie3feAE/BhJ7zC6JhUaYbny27758ZIcGHKqifpBodHCGs\nFqu58SQLUNa+PXUmzT52ZZAbb6BSMSYywaP83ax/zZ7ClAeLIYLlpqkAWATm\nLBlfsIkVQ67WwmRW6vWtgCoZGQumMU050ADtykVuRrgvBYOMpqR0HyNtgQJm\nWTFB\r\n=VUk0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 6"}, "gitHead": "65e5beaea85e1d5a249690aa29d1e4ee8a351eed", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^5.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_2.1.1_1564786459208_0.7223081045421358", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "compress-commons", "version": "3.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "833944d84596e537224dd91cf92f5246823d4f1d", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-3.0.0.tgz", "fileCount": 14, "integrity": "sha512-FyDqr8TKX5/X0qo+aVfaZ+PVmNJHJeckFBlq8jZGSJOgnynhfifoyl24qaqdUdDIBe0EVTHByN6NAkqYvE/2Xg==", "signatures": [{"sig": "MEUCIC+tSRhTogi6gJwUlKHAgMVAqTxTtXtYAq+ZaOfkbZrLAiEA5cc+VzusTeQ0M8HUCCsCXsyCGRfPUdXJ/4LWgJmjJZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelieOCRA9TVsSAnZWagAAtgwP/0yHrhDbU+YEMQag2xGy\nNteEQV+7nBpufwj7DMPA1hmFwcdeDwAWYHWmKtWhVX2lzeg4MaLMaGUPuUGN\nCgLwO3L0mNbWnFB5ikNd8N5dZr/fL3WOP/JDJ+oxC/EDEt6tNBmrCr3ISGb1\nZm9bZy6qvVGZqbW9T/nht078klaoU98/Ha8N5wX8/YlMy9n6XUMsM82oWcGg\nX7QHm85ZT+2QqeXmCm1MxCqqpSboZ1vPfAeMiGRWlr/vD+iRKDfyjfvVtZGc\nqp+bSEjCD4vhjhnieHonhwnEfiRkR9DTLvPNSh8bDNMpNgiRLPEkZeaL4hhG\n/zfASo71IEFlb22vV6Vfcta8nevKDqkt2gAxw/x3FHxJGvbQ41aN6don5bt0\nWGY2k+T3CRFAeBKkYZIpcVjzhgdzVoF75Z8yUnjp8ECCCkrpDj29zvfMtLRg\n/4iuGUeao20ce3pS0icq2KF66FXSME6o3GOjOcIYlacLx/kqsomKMqumJRv9\nLisIxJgJeSRQeqKpkZ/2OYwe5JUAKt4WZyqe0aR5b50Rwm1v8+gTvVzWOoTb\nycIOBzhBmHIthTwm0zJetfM9JiM9XexI3Ihm3PGcv71UpOtpYF4EXxIoAj47\nRC858TAC2NRIDA1YSS3sXGhuH4bQGuMJJKYTjig9iRqvQ3n2gbgl/fzAqfSv\nKJ8H\r\n=0Y7q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 8"}, "gitHead": "dc78128656856bde453558c9d6d78f9ce3d61348", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^2.3.7"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^5.0.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_3.0.0_1586898830088_0.5465702524113092", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "compress-commons", "version": "4.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "81f2c654fa470a132d15b58f205ff249af9faa83", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.0.0.tgz", "fileCount": 14, "integrity": "sha512-YvIrRuDUnJu4a9yvBA/ks/+sJGN6C+uTU8ZEp30Snb0IWiYVSrsj6xxdLx8ff5upC0JSRB/LYip0TT5wItBfeg==", "signatures": [{"sig": "MEUCIQDiITqGAnVnXAfXMA2X1OHJ1mXX5u9wRVDlVsOCb+p/8wIgMznaFmiHpS+jLAZCOzY0JoaQqR9/KaeeskP5DIdDor4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37338, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEzHRCRA9TVsSAnZWagAAizgP/0Zh3LxQOAJe30rd6rno\nU6dHXpkNJV5oYcWlGqE34v6LDqqKmCFBLplZOQHWLLKMxYGV728VDzRLAv0E\nxvFim9MKjDDxqcR3Sfiv8UP1VypEdj49CEbkC8t59e4BwlB1hHT+L8kAxy6q\nFZh5jQzfT/3n1ygiR+Txa48ns6B/+sTXMSBN9YU/N2mOr0DFnVkdu6ASR3yG\n8z0XGtQ9mDROOTxJDOludb8jH+OhvDqDaxOJCya0bs9VrOTN53eKYw++3ONW\nFtb4rFoTraFouRSfNO+VxVUGToEskcCrMsJuX1dSzit2UlqK06zWFEvCFnOl\n6TTMy+yjL+ECwjfa5j6DT8LrtMYWMVNJf7BwGfEp2WmvAZfa3hOQEUDdcw0a\ngmj7/mZSCULIg/qSqkymbLULFEV6hiJcjzAmziGZrneTYejykP5fh8UK2mgn\nPplEAYvY2ozSfdYrRKV5rmXkZaw5rZ/kp2FCLswW/gBnq6PUw/V+RcYE0E1A\nGmx5mhqY1nEbc5io8TmvCHMIN/YyskoIlCcrWW1W5HCuWoT5wzKq0/UvVgNu\nznnsRhMdFOkhVsMfEWefQyaZLskw+G5+pL/YR7kYa8RiLagakg2xtiujmsrC\ndTVDpfueassk9yNdxyBwj0Nh63dQaPiUq6Y1T7FO3vDyYtblVPZVuE7Q1uIL\ndmXc\r\n=Acl5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "080004b1d25a163697ef2790c5ced35a97e5a732", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.0.0_1595093456682_0.05147138069378965", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.1": {"name": "compress-commons", "version": "4.0.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "c5fa908a791a0c71329fba211d73cd2a32005ea8", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.0.1.tgz", "fileCount": 14, "integrity": "sha512-xZm9o6iikekkI0GnXCmAl3LQGZj5TBDj0zLowsqi7tJtEa3FMGSEcHcqrSJIrOAk1UG/NBbDn/F1q+MG/p/EsA==", "signatures": [{"sig": "MEUCIB1gGViNjj8Mwng/YSUIq2wIn0hSTL4hxpWJEP0oDo4yAiEA11vB5OLERNU7zOhxdkYS5Hiygz3AZFpq5ckLzs3m/II=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFlKYCRA9TVsSAnZWagAAqwgP/i2j8Yfdyn5rgnrm/cO0\nTe+Bc9hLFbhtzoHr/YEf6GMGtlgyCCf2kFF7d5zm18mFCkzIaog/ESoiySzv\nKYXwEXwwlx1etpOd2mzHbwWy3jIxE7tBfywZ/yJ55yJ6ZQkAJoOE+5qjlDkU\nr0cVMR4rwGhenFzPTZ/MZne7nIXOnJIyGXvXsHyh4TLOlj0vzWxniIFuLOEo\nEnxNngzwvEl29Wmx+ChalGAFwXk++CVMGQRYHC0/qaxyrMfK9ok+kr4WkdIf\nTz5jbzeOabWC+g20x8CG8zQYItYJwtfPvRFGTsBBv6I/cVfgj63Xx8WvZ6LP\n4TL1j/b+pAqvZOM2dNEZZy3hN/rqlk4F9CZl8wxt4Hi06AdkW3SE/truj6ti\nJQvIUkby5tQlPbd6Ll28MN03DpUk7Wg1vGYFgArvxDwnpc9ZZczAaF2tUaFT\nGv4sNNkg7DuyXpRiUwTNNIsgfUWPJ96wAnRJqET6ciiUdwME11l4r5dqge/s\nw6mH6HxibA/U0Se1X5kcwnnBy17x4mU42cKwcz6ALyTYN2fF2KZQ4BEimzul\nCw9y/plCeZEcj3+DFPaXNMuFRDDCHr6qBAPeFID0qoiqxF6x3evP0jWG1M2c\nsr5xvCDLh5Er0pFMbwYaIwh1kDJI+SiygYWyjpjEdtU6ymvb6c4HMmPmS3p8\nUe++\r\n=43Q6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "c00d7b08803a75fa8e325be4f98561b2742f914b", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.0.1_1595298455720_0.16060173352325435", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.2": {"name": "compress-commons", "version": "4.0.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "d6896be386e52f37610cef9e6fa5defc58c31bd7", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.0.2.tgz", "fileCount": 14, "integrity": "sha512-qhd32a9xgzmpfoga1VQEiLEwdKZ6Plnpx5UCgIsf89FSolyJ7WnifY4Gtjgv5WR6hWAyRaHxC5MiEhU/38U70A==", "signatures": [{"sig": "MEUCIFsL0uvwEU79CPhIsGhl1/FT8amH5hL0+BtAvTkLYM6LAiEAntncBVsct5tm6HeGLKK8GCaC36FWIqqNyYwW73ioZ7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftfC+CRA9TVsSAnZWagAAka0P/jlmPQ0fATDk/+gt2wmu\ng9v+E8Rluj24Re7wVRzNALkGTAE3eDL/KFFsEfTROv4P+1c9oCmvNR4fv7Eh\nf5kQA39GkFDWuCh74otK7aD2IZ7E8b1hoKp4RxZ4sWBg3Ew7kb5gIGBWoTfQ\nX13zp0ujNibDAo3X3dtL/u7jRLC8mbbVafKa48wBETMzinh/wFcwC2iu1s0Q\nDZXcRR3Q4swfHp0kB5EowqSsWCxHbJRqYD2Nu0jHnEUEun8SMaCCr9juErEW\nAaruvGu/rjl31p/KAiNUq7vtHJ+nVCdR43pv4gG341dF3jJGgrOKIqRECyPY\naOO6X4vVrbxt3cBN9nhTof0B/EmNdomfhWiShu+1ByEuQDf1g/zW9Mos4wjK\n4ECr0GSVFB0UauqC3087jhMOeoo3L2GBuANGCWcF3C5U7dpTe/IxoCPpNC34\n1R2RACy926x0adXRE6+9TlTL3CXCPEHkp8H/UbPZwegzVDIMvaDMdIzEgcG0\nP9HXZdx24g3wL7fAV223wZI4kLmKEkymuIwLIuizKayJQqQAPWO82qT/Ly2r\nDvrYmndXUMbSUCdCZmu+Fkj31D+F6GfbW5Gl5FU/fV4EVED69Y79qDZHybE7\n5aqeuWG0D3EavmXqp9tlGXg8Y0fuLNXS5OAukyZutS78S4igGzBMtos45UrK\nOJle\r\n=8Oax\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "4da87cd952ba8e0ec301962f0349575a16ead238", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.1", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.0.2_1605759166107_0.63652976267991", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.0": {"name": "compress-commons", "version": "4.1.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "25ec7a4528852ccd1d441a7d4353cd0ece11371b", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.1.0.tgz", "fileCount": 14, "integrity": "sha512-ofaaLqfraD1YRTkrRKPCrGJ1pFeDG/MVCkVVV2FNGeWquSlqw5wOrwOfPQ1xF2u+blpeWASie5EubHz+vsNIgA==", "signatures": [{"sig": "MEUCIHXkLO5syyUGlw/tUJIrvWBfErlHfTK7TdXBsink6HdKAiEA+K9qP5YQ2rzannN/mcMJXSXxw/WqKir+FTm6k13MnwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPwmpCRA9TVsSAnZWagAAi4MP/3DbwBOYJ4/r1V4zkVg7\n/ltwyUlvA4ujdHg1MaTPCE9+43h4kXS9S6DqCE9l/qJXrbwDy7nsHCDEzFPO\nRaSRZncTFXrdhmQRoE9jjlf9vt3jl+vc3CgxUp7OJ00LRzJyK/Y2zG2pGLON\nDv2o0lMgU1KhHzVkc7L4nCF0QPpk4D/0jxt1zjeFiZiHI7y0JVJd94ifeLWq\nnPmRsF884RE+/+kLxbYWLJGxrc5yqmUTj08x04EYK8cq6Cnv5YTJC2HYApjI\nV4NdIncsyyYFZQtcGqabhIqfClwQBbFtWGtkpb8vJCnK9yb/MnDtNs76w3V2\nXZgSf7GgTf6GkokHZGX8iWHCpdr1Abik2VQEwHs0YUBGH9DIP1yzBfL00QAj\nFSG1fT/5X+XPur8RmT/8wIQtOVjlaw36pvJqP5mr83G99FR3XydScKyRA8hV\noNOn68rgwUTti1zn40ZnRzvA7JHqD8yFdvNl0OOLGcDgWXonViX65HKHOA9b\nt18Hy4Xdfbj0voetRGmG7L2QQRGQvE4AIV9Ir0L0aAE95odEVrqF2BL4I6lu\nrowDjtc798wt23C+x7ClJVmL43Wc/+sCI3Rb/fm390beNb5h0lsF7nrIAeBI\nayVCkvzTgcronGydz8E2ZnOnF+HeW4+2slxdBE9sPRo/usnai2EmWu8j62Tb\nM28g\r\n=QwJz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "cb8b59190971774c7163a5b0ce405724af265005", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.20.2", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.1", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.1.0_1614743976846_0.4123471500666145", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.1": {"name": "compress-commons", "version": "4.1.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "df2a09a7ed17447642bad10a85cc9a19e5c42a7d", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.1.1.tgz", "fileCount": 14, "integrity": "sha512-QLdDLCKNV2dtoTorqgxngQCMA+gWXkM/Nwu7FpeBhk/RdkzimqC3jueb/FDmaZeXh+uby1jkBqE3xArsLBE5wQ==", "signatures": [{"sig": "MEYCIQDbFEw442L8jOJK9iltrXevkpQrBFKqoyPKQ6ChNcJuoQIhAM13W8svs2ltXxFzvrx8m8kv7jeoUUXb37yvB0OsziDV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgs9d9CRA9TVsSAnZWagAAw4MP/2wMb8neTzXwz6FXOq1p\ndouS8aC4epfbvc0VZzXAexLIDRDT2shPwJYRG+5R/eGl7tOHEj1KpghdT9Ig\n56U6JhUSu7YOlBcXGEpfxvHaC0oS91RjtR3rr0B1X/xrCyvxfvjev+BMyWZ3\n9e6ut4IBS0E5LK6fvpm0H+T6/3bExbw0HSGG1nG+f6ouQ//z+4eiklFy0u7w\ndw+vilt2TirqkelwijuVC8QngwMqL7w4Jh0nv/u2DHYUzY1XaPRouNv2mhG0\nDD7opuKlmwhf3t+FAT1C8qh2iD9spLmRmP3G4Nw29o0fY388RGdvkVO7hja9\nqq8JjsgqRVMektYRk47gOvSWz4Mi0tett1wLyQAv2kFrDdnEy5RNlgZiwej2\n0SV8NuhAjYe/IqvrUByPyykcxhJchoiWoY9gJ9Ltrm3ofRAptNHGrOqGhaHB\nfCx8jTAHLMIrVDFlkohOCKNz/RiKnBxfbCEvY/Hle6kn9qScYB3Ra+0LbeoV\nebVI13AWe/pf4dSwOx7Rp3IpN95jLYMcczLU0Ns/mJhKh3Tb6NRU/A7e1x03\nfLTQgtKwmFf7HNxkaRaI1xnjM4rY27OwKFSDCZg/lg68mvWSNfWVFG3DTJNH\ndlZQ4lylbI7QfwEGY2t+vjIE02AYTsIUeFKMPcsrPR7WHcCcGd9XJnDqfdDD\nOgr/\r\n=Ic2V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "8ab831f09fb2ddad69f88d272f3bf007d1e3b6c6", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.0.0", "mocha": "^8.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.1.1_1622398845218_0.3187406579335157", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.1.2": {"name": "compress-commons", "version": "4.1.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@4.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "6542e59cb63e1f46a8b21b0e06f9a32e4c8b06df", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-4.1.2.tgz", "fileCount": 14, "integrity": "sha512-D3uMHtGc/fcO1Gt1/L7i1e33VOvD4A9hfQLP+6ewd+BvG/gQ84Yh4oftEhAdjSMgBgwGL+jsppT7JYNpo6MHHg==", "signatures": [{"sig": "MEQCIAVztg9dbt6TirbYnI6P/KsIeGB53dSpDS0vvPmQ6amtAiBr6776lU23bdJ/+OeclcZlxpsYUiZ3gtNTEwe2SdMacQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38329}, "main": "lib/compress-commons.js", "engines": {"node": ">= 10"}, "gitHead": "ed12d0840b6a11b0b79b7f75510dccfac4825344", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2", "mkdirp": "2.1.6", "rimraf": "3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_4.1.2_1693694070751_0.7305730608367662", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "compress-commons", "version": "5.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "b8a2f8fade8489e171789656f9959eb316c04832", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-5.0.0.tgz", "fileCount": 14, "integrity": "sha512-FULcNlNFLGVgy5XBoCvDKEGyN+3/eTK9UAzuofy80JSDMuV7qxiqcTJqGQJN8FFfkgzI3lz83PKYZ3Wvf6Fp4g==", "signatures": [{"sig": "MEYCIQCRJ/mWjl65iPS/FT4lSwxrVTD5nvbWRL26Qlk44waDeAIhAM8WK7g80xOMs8YM5grF87ImvkLZpqa4kberQ6fhWsvO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38470}, "main": "lib/compress-commons.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "f23f463b800c9cf47f9284a138369f54dc2ba135", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^5.0.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2", "mkdirp": "3.0.1", "rimraf": "3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_5.0.0_1693698946174_0.6886033910835394", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.1": {"name": "compress-commons", "version": "5.0.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "e46723ebbab41b50309b27a0e0f6f3baed2d6590", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-5.0.1.tgz", "fileCount": 14, "integrity": "sha512-MPh//1cERdLtqwO3pOFLeXtpuai0Y2WCd5AhtKxznqM7WtaMYaOEMSgn45d9D10sIHSfIKE603HlOp8OPGrvag==", "signatures": [{"sig": "MEUCIF6W2GG2ScoeInOWcal5HiAWAyKnerrGw+Lh3OnuQmGVAiEAswzX5lLLPnMOYK15WoIjtnx3DrAI8p7NqW+C62JoJ9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38594}, "main": "lib/compress-commons.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "cc7fd02fe1f07afeb7975a34d365c2f16a24418e", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^5.0.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2", "mkdirp": "3.0.1", "rimraf": "3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_5.0.1_1693775539186_0.7657612922732713", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.3": {"name": "compress-commons", "version": "5.0.3", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@5.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "36b6572fdfc220c88c9c939b48667818806667e9", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-5.0.3.tgz", "fileCount": 14, "integrity": "sha512-/UIcLWvwAQyVibgpQDPtfNM3SvqN7G9elAPAV7GM0L53EbNWwWiCsWtK8Fwed/APEbptPHXs5PuW+y8Bq8lFTA==", "signatures": [{"sig": "MEUCIHL5QfpDCD/yBTsPotoW5rYKU1LaJLqfTj6dZazVTvGGAiEAiv8aCao5+rfgyLRqo3dqpepqVHN2JhrYwiwsj/5Y6aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38654}, "main": "lib/compress-commons.js", "engines": {"node": ">= 12.0.0"}, "gitHead": "15d65ddf9dddce84ca6753c3e9d6c6e8d5ce353a", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^5.0.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2", "mkdirp": "3.0.1", "rimraf": "3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_5.0.3_1709010570226_0.5489108100952995", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.0": {"name": "compress-commons", "version": "6.0.0", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "9a89dfe2898f8b366cb2b715d6998dbd91f7332e", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-6.0.0.tgz", "fileCount": 13, "integrity": "sha512-t6IJvJfBdf7ZvRENJeRFsNq9KaVOlUbQUwA9mlCR6fLa1NLVLbuLobXOKSNTjLZFms7PsqUFNwwRpQNu1go40Q==", "signatures": [{"sig": "MEUCIAG17r8rWB9V2S+JJ1AIU826saHGOyPK/GEVvPc3pdySAiEA0fJAMPKX7CTS8QZ+DaER+Z1M7f0KfQVKuhtlLcqFDjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35709}, "main": "lib/compress-commons.js", "engines": {"node": ">= 14"}, "gitHead": "7df4739dfaf1a41bb86bf906148137b4e07ed533", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^5.0.0", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "mocha": "10.3.0", "mkdirp": "3.0.1", "rimraf": "5.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_6.0.0_1709013250645_0.13007902003106153", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.1": {"name": "compress-commons", "version": "6.0.1", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "2c28949834ca230e21fcfcd421f1160d66c4b61d", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-6.0.1.tgz", "fileCount": 13, "integrity": "sha512-l7occIJn8YwlCEbWUCrG6gPms9qnJTCZSaznCa5HaV+yJMH4kM8BDc7q9NyoQuoiB2O6jKgTcTeY462qw6MyHw==", "signatures": [{"sig": "MEUCIQDsMnLmLt9b8Hg+xN3GmnGTVIFpsWWh0THT+SAfJDWWMwIgFAX0qTOJ2R2uR/z8XyQXEmG/ntnb2QRRd98yHKOdzhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35709}, "main": "lib/compress-commons.js", "engines": {"node": ">= 14"}, "gitHead": "3c95a398b70d09452bdadbda07a83407050e9897", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^6.0.0", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "mocha": "10.3.0", "mkdirp": "3.0.1", "rimraf": "5.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_6.0.1_1709219120394_0.11146646696407947", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.0.2": {"name": "compress-commons", "version": "6.0.2", "keywords": ["compress", "commons", "archive"], "author": {"url": "http://christalkington.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "compress-commons@6.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/archiverjs/node-compress-commons", "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "dist": {"shasum": "26d31251a66b9d6ba23a84064ecd3a6a71d2609e", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-6.0.2.tgz", "fileCount": 13, "integrity": "sha512-6FqVXeETqWPoGcfzrXb37E50NP0LXT8kAMu5ooZayhWWdgEY4lBEEcbQNXtkuKQsGduxiIcI4gOTsxTmuq/bSg==", "signatures": [{"sig": "MEQCID8hgOyYNGp91Gpc/w4gwdyjDDrwYt/2YROE7xXYuuJxAiAGgJEia1mw1G775pS7VE7PTvWXegIT+EeHUuLZJluQsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35726}, "main": "lib/compress-commons.js", "engines": {"node": ">= 14"}, "gitHead": "fad80a7cd74d641df4073dbefd974adc8b120fb5", "scripts": {"test": "mocha --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/archiverjs/node-compress-commons.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "a library that defines a common interface for working with archive formats within node", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"crc-32": "^1.2.0", "is-stream": "^2.0.1", "crc32-stream": "^6.0.0", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.4.1", "mocha": "10.3.0", "mkdirp": "3.0.1", "rimraf": "5.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/compress-commons_6.0.2_1710034633375_0.7747287531331624", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "compress-commons", "version": "7.0.0", "description": "a library that defines a common interface for working with archive formats within node", "homepage": "https://github.com/archiverjs/node-compress-commons", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "git+https://github.com/archiverjs/node-compress-commons.git"}, "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "license": "MIT", "type": "module", "exports": "./lib/compress-commons.js", "engines": {"node": ">=18"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^7.0.1", "is-stream": "^4.0.0", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "devDependencies": {"chai": "5.1.1", "mkdirp": "3.0.1", "mocha": "10.7.3", "prettier": "3.3.3", "rimraf": "5.0.10"}, "keywords": ["compress", "commons", "archive"], "_id": "compress-commons@7.0.0", "gitHead": "0295b96a46e4e885264fb14a8e3cfe660d11126b", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8WWFRMWaa37dwjWCxDcmdx6sxfjQTAEQ6s96BWqX9WYC6Mgg95EvwPYS/7QGX3txkst7TD1jIL2HCY9AixLGfA==", "shasum": "91b7a3f4e75bb81eaec419cd8e02730b158b68fc", "tarball": "https://mirrors.cloud.tencent.com/npm/compress-commons/-/compress-commons-7.0.0.tgz", "fileCount": 13, "unpackedSize": 31687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7tSbfiCFic5/VXn4DM8NXeNOPBT38qzH6uXX7HvEtbAiEAm29W1fy42SpDUej53k4+P7TuPn+ITgeoBIAv+hLNqcc="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/compress-commons_7.0.0_1728869838047_0.9508276965474665"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-08-22T09:03:48.830Z", "modified": "2024-10-14T01:37:18.490Z", "0.0.1": "2014-08-22T09:03:48.830Z", "0.1.0": "2014-08-23T14:17:04.063Z", "0.1.1": "2014-08-24T10:17:26.427Z", "0.1.2": "2014-08-24T12:26:03.058Z", "0.1.3": "2014-08-26T13:05:06.696Z", "0.1.4": "2014-08-26T13:36:22.094Z", "0.1.5": "2014-08-29T20:25:17.184Z", "0.1.6": "2014-09-09T00:54:55.757Z", "0.2.0": "2014-11-30T19:32:02.174Z", "0.2.1": "2015-02-04T08:43:33.573Z", "0.2.2": "2015-02-09T22:57:12.882Z", "0.2.3": "2015-02-12T21:08:56.313Z", "0.2.4": "2015-02-12T21:43:12.686Z", "0.2.5": "2015-02-13T01:10:36.276Z", "0.2.6": "2015-02-13T22:35:49.457Z", "0.2.7": "2015-02-15T01:04:32.921Z", "0.2.8": "2015-03-25T22:57:45.925Z", "0.2.9": "2015-05-20T15:02:33.436Z", "0.3.0": "2015-10-17T20:44:16.312Z", "0.4.0": "2015-11-24T22:03:24.192Z", "0.4.1": "2015-11-24T22:22:54.624Z", "0.4.2": "2015-11-30T20:00:21.762Z", "1.0.0": "2016-04-06T04:06:51.487Z", "1.1.0": "2016-08-27T18:27:13.549Z", "1.2.0": "2017-03-19T02:54:06.454Z", "1.2.1": "2017-10-07T20:59:18.171Z", "1.2.2": "2017-10-10T18:52:12.950Z", "2.0.0": "2019-07-19T23:31:34.339Z", "2.1.0": "2019-08-02T15:56:05.434Z", "2.1.1": "2019-08-02T22:54:19.385Z", "3.0.0": "2020-04-14T21:13:50.263Z", "4.0.0": "2020-07-18T17:30:56.792Z", "4.0.1": "2020-07-21T02:27:35.820Z", "4.0.2": "2020-11-19T04:12:46.283Z", "4.1.0": "2021-03-03T03:59:36.987Z", "4.1.1": "2021-05-30T18:20:45.357Z", "4.1.2": "2023-09-02T22:34:30.969Z", "5.0.0": "2023-09-02T23:55:46.408Z", "5.0.1": "2023-09-03T21:12:19.408Z", "5.0.3": "2024-02-27T05:09:30.371Z", "6.0.0": "2024-02-27T05:54:10.854Z", "6.0.1": "2024-02-29T15:05:20.529Z", "6.0.2": "2024-03-10T01:37:13.533Z", "7.0.0": "2024-10-14T01:37:18.314Z"}, "users": {}, "dist-tags": {"latest": "7.0.0"}, "_rev": "13385-84bdb6eef637775c", "_id": "compress-commons", "readme": "# Compress Commons\n\nCompress Commons is a library that defines a common interface for working with archive formats within node.\n\n[![NPM](https://nodei.co/npm/compress-commons.png)](https://nodei.co/npm/compress-commons/)\n\n## Install\n\n```bash\nnpm install compress-commons --save\n```\n\nYou can also use `npm install https://github.com/archiverjs/node-compress-commons/archive/master.tar.gz` to test upcoming versions.\n\n## Things of Interest\n\n- [Changelog](https://github.com/archiverjs/node-compress-commons/releases)\n- [Contributing](https://github.com/archiverjs/node-compress-commons/blob/master/CONTRIBUTING.md)\n- [MIT License](https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT)\n\n## Credits\n\nConcept inspired by [Apache Commons Compress](http://commons.apache.org/proper/commons-compress/)&trade;.\n\nSome logic derived from [Apache Commons Compress](http://commons.apache.org/proper/commons-compress/)&trade; and [OpenJDK 7](http://openjdk.java.net/).", "_attachments": {}}