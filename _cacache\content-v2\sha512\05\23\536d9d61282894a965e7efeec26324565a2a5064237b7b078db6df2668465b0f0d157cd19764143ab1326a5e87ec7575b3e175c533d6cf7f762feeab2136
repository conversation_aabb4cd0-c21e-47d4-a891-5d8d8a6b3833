{"name": "babel-plugin-transform-es2015-modules-commonjs", "dist-tags": {"latest": "6.26.2", "next": "7.0.0-beta.3"}, "versions": {"6.0.2": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.0.2", "description": "## Installation", "dist": {"shasum": "b126a81e7d2ac61172c664ce1a5f8592acab0c2b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.0.2.tgz", "integrity": "sha512-F8Bm6i9ia0w5W7tzSgSpWF2sVe2Cnh9MdMMNW3QDv8Q3yIn2EP47I/iQa1D8XwR79tPNnjx2HFXgPVMCT6h7YA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhnmR9QGoB8lQKqrvxzlyXDMlSDTRR/es6HqhEYqgG2AiEAzcfdEkLunbKJLlHSLeH/wYFWFKD6cNMVAmCfR8kswn4="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.2", "babel-runtime": "^6.0.2", "babel-template": "^6.0.2"}, "hasInstallScript": false}, "6.0.12": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.0.12", "description": "## Installation", "dist": {"shasum": "835935d623f412b6e85cfa08323de5845b3e9c6b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.0.12.tgz", "integrity": "sha512-nXcoUCY5X4G4HU8Z0fdQMmJKwf/EwhlpE7fkVNRachAhfR2+ZpFhH3nvcY2WPZEOeSrkDBjVSADs4drFcF2dnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBeYuxBm2yc3eQ8whJrOLIPcgM/4Czc9qH4RsgPdSr+KAiB6N1EqMfwaBsp89mlYC2B8vaTYBXeTpuPxp16FeeGgVw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.12", "babel-runtime": "^6.0.12", "babel-template": "^6.0.12", "babel-plugin-transform-strict-mode": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.0.14", "description": "## Installation", "dist": {"shasum": "6a534e418c1af8b541fb3daf16c0acb7b6fb8fa7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.0.14.tgz", "integrity": "sha512-NAFMK2HDA2FZYGsrJkEcmESGXXxtHEwhh/JR/b9abujSQUAgbkhSk+tUskQkPWNiWQjygAPKnjr5rTFBsemJOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7jO458MryToQ4jqoW48CZ4NNBUyRIhixAsbrWO4JhtAiAo2TBi1Fr+vkV7vP3WfQakqkJIxWxgmR1Z6FZW59oYiw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.14", "babel-runtime": "^6.0.14", "babel-template": "^6.0.14", "babel-plugin-transform-strict-mode": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.0.15", "description": "## Installation", "dist": {"shasum": "013d2d0e78b67381746514c711d98ee926b8eff5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.0.15.tgz", "integrity": "sha512-o2FcjkP5KZcU+povcR5akLSCWsz7DL5H5uoSl+CZw6a60YKChKSeVKJPaKljUcKwf8Oqzhy2sMEh3EzKmFPMbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuObRZ5XoYCm6LmIRwt28aHJDXxKCZX43V4LILkC8O5gIhAJ6lPwS7ZRnPNYLN8U20UEVakYBb2T676RUVLF4KJ31p"}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.15", "babel-runtime": "^5.0.0", "babel-template": "^6.0.15", "babel-plugin-transform-strict-mode": "^6.0.15"}, "hasInstallScript": false}, "6.0.18": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.0.18", "description": "## Installation", "dist": {"shasum": "2fa29fa3379597cb6c70ce20dcf16bd7b23009e6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.0.18.tgz", "integrity": "sha512-dh55aDhull7IqDQP9DLzCJQ7wksxUJO78djE2yrOEiLwjigto+/lOoGqToVj1jUORHuioM/s5uRBjSnii1AHRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhHIdW9Vay5lNmq7Car2PDq+ClTBZ9qPIggrRJYyfQaAiAhnRD4HmOafr9257PmuYDNzy+oxfPqYWme8GrpETmg+A=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.18", "babel-runtime": "^5.0.0", "babel-template": "^6.0.15", "babel-plugin-transform-strict-mode": "^6.0.15"}, "hasInstallScript": false}, "6.1.3": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.3", "description": "## Installation", "dist": {"shasum": "209af0b161b76968bb791a8c5daebbcca2a0c227", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.3.tgz", "integrity": "sha512-z1fJAFnFMma8ujdP9Jq2oQGVdTV0ai/S69PpRCBrgpABrKG6lhCHoTmv7D7BEgwsf7JFbxKswLLqP6FuserKyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvUQUnYWqaMPj7MbqD3Et9vPE8CragLvSAlw7med7GtAiA+Y27duUnY8e1SYTHzrsmQ+D8k8jFoRmj7vvf1fMFW9w=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.0.18", "babel-runtime": "^5.0.0", "babel-template": "^6.0.15", "babel-plugin-transform-strict-mode": "^6.0.15"}, "hasInstallScript": false}, "6.1.4": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.4", "description": "## Installation", "dist": {"shasum": "6265cceb41c3273b989284311dedcf7236b8325b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.4.tgz", "integrity": "sha512-pi0/XFxRdHtT0XX2Rgjq1QyseexnV6GUFwBeOL4hJQxYjjFeIPfwmEwphyTvqFMmfLUohMg0+/3VvkvNIyG8vQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+WnceoNQcfdaidT26cPX+CYA2yvhWoUSUJNJGZFc8TwIhAOxQ/ptEMe9dgV0jdQNBmw2CuWkBqL20KjlzCfNxGID0"}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.4", "babel-runtime": "^5.0.0", "babel-template": "^6.0.15", "babel-plugin-transform-strict-mode": "^6.1.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.4"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.5", "description": "## Installation", "dist": {"shasum": "f09187c0479cb4e03227e0159be39598797a5eca", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.5.tgz", "integrity": "sha512-nXJAMrsnJNkTGEmORv03+YBd0wdBOHYd474O15NwndynBpwKPwnXCd6nLl5d7BXqggynPg0hb9Z/iiNn5EoZCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICrmLWUQMSKSrpxVlU5jPHok24wf39WVA0fOoGvlnIPQAiEA9oBe/A6upXw0e3G6Lr4OUb3e01MZXpPBLLKM4N0KTko="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.5", "babel-runtime": "^5.0.0", "babel-template": "^6.1.5", "babel-plugin-transform-strict-mode": "^6.1.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.5"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.17", "description": "## Installation", "dist": {"shasum": "254255d62920ee30ea1f20af807078f18c0fd113", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.17.tgz", "integrity": "sha512-36bgzS75LoJmw8XM/fXnSwUgdHEi4xUP8RmfFFyJteP70+Dtex9CtgiPfNhLmbOECizGIIzPt0PsQVrMFAd3Lw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2ZihHx880652IQY4m4dlbpHaSkZd+a3Sr4F/vPPQoKAiEAofq5GyamwQy263vDlR54Op+QYlk8Nil3djLX0Jtt5Hc="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.17", "babel-runtime": "^5.0.0", "babel-template": "^6.1.17", "babel-plugin-transform-strict-mode": "^6.1.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.18", "description": "## Installation", "dist": {"shasum": "eac495b08b3e790fb6c6c15d745ca5c5ac4675e3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.18.tgz", "integrity": "sha512-QcKPAGfzNknmpeBReuOh1bH+EqG9zlb9x4O/X2d3+SYHqU5DE1R+LeecCUqSSCrVXTO6NWkOSFlLAqYte2AZQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7WAdFXUy7H9+7csqcl77Ev6ZcA+x7rxEMmob54lsy9wIhAMcR9BhTWGwNuNS7fUhTO9KfDyUGilw1k9EoI2443Oln"}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.18", "babel-runtime": "^5.0.0", "babel-template": "^6.1.18", "babel-plugin-transform-strict-mode": "^6.1.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.1.20": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.1.20", "description": "## Installation", "dist": {"shasum": "146197f9c7d944b8b46cd136a7a4c3f5a25b6fdd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.1.20.tgz", "integrity": "sha512-V7jt/qjENPoWqimBIdWEIquZOLYleoDdkartvuYuVG1VMxavbm/bQwGoTRJ3pIAQjaosO85tdutdhrv/RDiWFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAG6zwsexH4tRPuU/R6n6waDuQC74UAJ6XZhgbXarII6AiADw7q8g1MNzjhzgaGSqqkYNh40W4yklu76NxF0k3QvJQ=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.1.18", "babel-runtime": "^5.0.0", "babel-template": "^6.1.18", "babel-plugin-transform-strict-mode": "^6.1.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.2.0", "description": "## Installation", "dist": {"shasum": "ab689c5c5a4cf8b25b9a07b4c048e5e37c086213", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.2.0.tgz", "integrity": "sha512-Ep0YRqpL0G1eDBBOssAVkPgYx1FLpuarkvdsYG0Pom+Ea30vcm4wlr2dNhRgvKKyaWaSI/5O4uOcfSSBXcLSdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQHFRVSYg7bHO8wMICGI71m+V7abFtOv78Sy1RxCRMiQIhAIoXcuuNagwzfW7v2NRwZsr7eoqvzZ6AZS3NkV4P9QHu"}]}, "directories": {}, "dependencies": {"babel-types": "^6.2.0", "babel-runtime": "^5.0.0", "babel-template": "^6.2.0", "babel-plugin-transform-strict-mode": "^6.2.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.1.18"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.2.4", "description": "## Installation", "dist": {"shasum": "2ca4ede7a6c28c9419442bde28c02ed3f9cd0d59", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.2.4.tgz", "integrity": "sha512-UbHh8o2zAoG07vAsPhgQYEUS9H4xVWD8d9zioyDHVQxxPeIgjXLex5+4QE9rf7qgxAKCZhwl8mYxHJnJlWURUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID2B1xancs5mG2MxbSuBGxPt/seyWzmlTBmHyhDDOZvJAiEAkUDTR4Y+IswwboG/WyRL2sNomfoZsuFiI9iax4IiyxM="}]}, "directories": {}, "dependencies": {"babel-types": "^6.2.4", "babel-runtime": "^5.0.0", "babel-template": "^6.2.4", "babel-plugin-transform-strict-mode": "^6.2.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.3.13", "description": "## Installation", "dist": {"shasum": "82e9430bf84fcc248d5404c0a8026e64d0e8aefb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.3.13.tgz", "integrity": "sha512-Nga/WYo4pmJgqfRFR9labfNVpVp3d7YfGjHXKD5nKWFbmvjRPsaAaeANlg2DisobFqiK9woa3Le9rqiTXR62Uw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXjpykJZi6zo5jPo6f2Jp7IT26z+Y6SSeZf5FtTtz+wAiBK30D9yCXyq7DEvZHYwsoo7DMBf8A6bVz8HqEK1XL/NA=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.3.13", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.3.16": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.3.16", "description": "## Installation", "dist": {"shasum": "f6a913e47392ea61d6fc573713916befb6784788", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.3.16.tgz", "integrity": "sha512-jfpYfHPXpFZYcZRAA+gqtu2y+gW/uHEOXoNVahwgOmIX8kBSkVMBOt+AHXVxknjjIZ/13KXG1qXTGuO2/YdqQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSTQ3LlBZyaxt045kLFUPDrVq6gpJFmQZeEMd+IHoM3gIgeo/JbEnYPHSJJ+JpZEmUBTG/f4NNtoXhJwHkP0YWBTw="}]}, "directories": {}, "dependencies": {"babel-types": "^6.3.13", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.4.0", "description": "## Installation", "dist": {"shasum": "5ecf6334dfaa232b4d091cf9511d68712447a637", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.4.0.tgz", "integrity": "sha512-J08DBh85/Ev6R1fdz2BZoe1CaOWMLGSCW2691wM26iGOon0pCTDWaQGT3ub/qxT5KSZJwY9Fz4MOHvYAMBIaaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICaMjex/4U8rsj0iL/FpbgwLaCkGxVxqBaXBemr2H/hiAiBd8E7fTQXFfgJUAk7T32NCfB1LvjZuw8POP0d37ad9Cg=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.4.0", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.4.5": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.4.5", "description": "## Installation", "dist": {"shasum": "6960ac14e0b2b1f22021d13c396f16cb6d0a82d8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.4.5.tgz", "integrity": "sha512-7TLzqCrtWfH5DYxHIQMxRHq87rN0R0XQ6NO30vMQ3utee1mTMfpS+ulIi2Zk7+oqrJtjTyJ1A/AvdiQeMO0AXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3RG65vkWhGm2ZBsFAHTgxs+n5RMz0MGX6HjMA4fAP7gIhAJbSA9gYBvHNjuAMgD3nbcULVaRqzfTsaq1llz72g13z"}]}, "directories": {}, "dependencies": {"babel-types": "^6.4.5", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.5.0", "description": "## Installation", "dist": {"shasum": "45533db197836c55fa233d4b6350c07eb306007f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.5.0.tgz", "integrity": "sha512-e8HkblNyesVhQFL/TsHTMmCwBtm8Rq/rvXbjwvFD1YHWiR8oQRWf3v8U3z83vIPU9e9sboh8cdlEznlDrqnbYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqjAb3ERx/hFy4Wm0V6So2LBNL9VaHPY6o4XpU0DXKewIhANf8hwJd58NUtV+Py2zD2tKVWoW1jkmAeWBgJu9hAkIb"}]}, "directories": {}, "dependencies": {"babel-types": "^6.4.5", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.3.13"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.5.0-1", "description": "## Installation", "dist": {"shasum": "92e63434093784fb2b4bcbc0ffce4eeb36d7f137", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.5.0-1.tgz", "integrity": "sha512-MRPEc5jESpAAqY6LK6gW/+qeOdnwby56RP3uqpjH9wPFC5v8Lc0y+MnoC2pgn7k+zV3wyiqaH/kty/6X1lWfEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDjBj5ZP/cj2y9U84qWj8ojTW7kcyKuf9yhaRr0xzY0AAiEAklM3NFBRwcObwHAL++hSrgMP40/4N0dI4gFQNYTD89g="}]}, "directories": {}, "dependencies": {"babel-types": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-template": "^6.5.0-1", "babel-plugin-transform-strict-mode": "^6.5.0-1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.5.0-1"}, "hasInstallScript": false}, "6.5.2": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.5.2", "description": "## Installation", "dist": {"shasum": "2f65e0d17ee006aa589bed06f0fc1ce06e4239e4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.5.2.tgz", "integrity": "sha512-mxIxsRMSvIDL0KX7ZEnVeKBXsATtHBoHKFm0ml5lOYvJCucYN28uaJg4SYaXO0p1VEtQUHmBdQdIMqkLyBtziA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1QoBmHMXLMo3lbRL5WD/xb4fE1NC1WFMXHmzcVBo9NAiAc0o3pOksG+GK6nHGDKfM2RQC651OhGcKv84NFY6vxAg=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.5.2", "babel-runtime": "^5.0.0", "babel-template": "^6.3.13", "babel-plugin-transform-strict-mode": "^6.5.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.6.0", "description": "## Installation", "dist": {"shasum": "0079ca0e69a587dbfa5ddf9b4a9f887ff00b7bee", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.6.0.tgz", "integrity": "sha512-9YgNgrgIfRX2dFijhegoiqyl9xQpklTGg70pc5OC6Y/aVnJPksFN7CLNOLaryeFgU61t+zAS8TLkvCds+8KJTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFMe7cUDTNTBfleExupSsxGnsjPxNmm8Zr0AoT7LwSAKAiAK04MvXXWAcm1yIY0aYafg0+V8BmoL5K1h4Wm4TiSu+Q=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.0", "babel-runtime": "^5.0.0", "babel-template": "^6.6.0", "babel-plugin-transform-strict-mode": "^6.5.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.2": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.6.2", "description": "## Installation", "dist": {"shasum": "6fe4abba8d0d7bc41fe25f0ccd18e8f26da70791", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.6.2.tgz", "integrity": "sha512-s1nn94LOF41nleEyU4J10tHuZ+2hdAX8i3+K9RXIu/8Fp2v4hWvCTaJUA5fnemBmet0gLLD4gWLZssDDOPI6+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHTXMOmfgoQSBHfnF5APY//Bjhx4HJWziOE1b2Jmv3ygIhAOPLoFioejhW0SnO428MMFhMsu66B9uD0laKvNv4f7qY"}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.0", "babel-runtime": "^5.0.0", "babel-template": "^6.6.0", "babel-plugin-transform-strict-mode": "^6.5.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.3": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.6.3", "description": "## Installation", "dist": {"shasum": "8da82c1688185638669fb520c9b91387a2ae414d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.6.3.tgz", "integrity": "sha512-gFOAoFRj1cUJ+ow4+2/c66rw+cOcwixP5HrQFDOl9wl93zCg7ln0k7AdwnUEZveltqph+flczezGbaCRzkHE3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSysW8OkT6oN5x5YQlCT4eKn8d7ZlQZxqaFkC1xoOY2AiEAxAY2wGvkpl0VNqh9zn/Ebvap1D5Pgh0sU7HQizfhX0Q="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.0", "babel-runtime": "^5.0.0", "babel-template": "^6.6.0", "babel-plugin-transform-strict-mode": "^6.5.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.6.4", "description": "## Installation", "dist": {"shasum": "ab060326947dc32235b6b971ec08e1de06b3d0fc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.6.4.tgz", "integrity": "sha512-fNsr0XNJAl4QK1lBHrDEMQ8+jJA1MmPXzSLtENaAVNzcKNVSenoB0e1c+5/P0NDqohDMpNNQZ39DB+Ijrc/T6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEjWFpJY+FEKfXe/NYYWn9jr4MO6tVIzVk7SWTXNjWaUAiB3ly0qSKFmwUVYC+BYEp32cN7iGgsaUgA3WEu0JwHZRQ=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.4", "babel-runtime": "^5.0.0", "babel-template": "^6.6.4", "babel-plugin-transform-strict-mode": "^6.6.4"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.6.5", "description": "## Installation", "dist": {"shasum": "83fd915305809b99627a7b98072b3cb700a41423", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.6.5.tgz", "integrity": "sha512-ShKM3ZC4mQjubDy0PT5GTyjl8oKHNOkMQxfM7HfZGQ0h8GKSG5wbkIcsUUm0BIV40eMRqTUvbjv4F1bJgx5sWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPxduFe70nl9WAGhpYWDRRHhFsyjqmWUNF+oIAHXHNdAiBemcOtgFds5yISAH12KLLRHxn9jik1AtTg152boZCR5A=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.6.5", "babel-runtime": "^5.0.0", "babel-template": "^6.6.5", "babel-plugin-transform-strict-mode": "^6.6.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.7.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.7.0", "description": "## Installation", "dist": {"shasum": "6a141940c8e0c3fce7d010444b52c1a9753f6fdc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.7.0.tgz", "integrity": "sha512-Qz/x5aaw3XBuz28BhINghHeURQBbDZ5hQRVkAAtmcnRO5Mj9+btl26xNT29KqvIfQHfsCIY6gE6T1LrlUlnH4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPtHJbkMgkKaL6xm8KTY5r6LJzqJDdOsCjUi+hA28/vAiAji4Q2/2ZyLKyHHZc7NowJ7pxxVMeBYefd8NdZsjAnuA=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.7.0", "babel-runtime": "^5.0.0", "babel-template": "^6.7.0", "babel-plugin-transform-strict-mode": "^6.6.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.7.4": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.7.4", "description": "## Installation", "dist": {"shasum": "df65f39cdd1f5ce442cee63e676467a6e5916e46", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.7.4.tgz", "integrity": "sha512-6pOCy9w/C88dbZ2W86B8kbYZ9uHO6UOSExJ7WVutVkV/oS2uATm1gOlbb1s1xWnV0Q1zWhnG86OPp7rpXCDcDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVTHJC/0P6xpZ0RYupWrUtxBXQW+6uKLF3jt/HsfRb0wIgBiiqitqYorTRx4vdJFefT2k0biQRei890Ros/6I0A8Q="}]}, "directories": {}, "dependencies": {"babel-types": "^6.7.0", "babel-runtime": "^5.0.0", "babel-template": "^6.7.0", "babel-plugin-transform-strict-mode": "^6.6.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.7.7": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.7.7", "description": "## Installation", "dist": {"shasum": "fa5ca2016617c4d712123d8cfc15787fcaa83f33", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.7.7.tgz", "integrity": "sha512-muDDxPtOseOAF9/+zxIYbLRy/PRAKFHHt9d8PeMBOU5JJ9ipqEJNubOncXHphlK3t1D+idabP8kFhBo2YJqOlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDv3TVSEfTpnvk2dhvG1ctvIKZ8XSvt3s2AaJyr4fRRAiAXhbj5wkbvLal/EqN5+ExqWrtym02tzFBHAD7mXjPGMw=="}]}, "directories": {}, "dependencies": {"babel-types": "^6.7.7", "babel-runtime": "^5.0.0", "babel-template": "^6.7.0", "babel-plugin-transform-strict-mode": "^6.6.5"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.3.13"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.8.0", "description": "## Installation", "dist": {"shasum": "69cf172ae5169004212c470d119dc846c8417111", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.8.0.tgz", "integrity": "sha512-Tyl2/wXgbBLZ3YwhHxRe6I251TfIAiphCqzPAv+I5PZXecRUbGnkbV3gEIw49E/CbzUPMCmkd5HxN+9TT+divg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3Gg6My87w5ma0zKp5/q5DGq4AlerPUbs320zjJu+avgIhALYKvPoN0KTyeHBGTxzALaefhfZgdOdrOncKyCD8IYtr"}]}, "directories": {}, "dependencies": {"babel-types": "^6.8.0", "babel-runtime": "^6.0.0", "babel-template": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.10.3": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.10.3", "description": "## Installation", "dist": {"shasum": "e4993a455518ca1a3c580bfda35c074e40659c5f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.10.3.tgz", "integrity": "sha512-c1QLihCqK8qI283qTROF4Bkw+Cc29vQRrR9tsadUnqMRyvCW0U3FWPrFEvMJh3jQUZMPormE/3WYsHK82F8u5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeLCX/HbGYTM1BgHYgNsO+ABdViDd4Purd71K6rMdGHwIgfZB3WVcFx01pozg5bSBUmy1COP3W6fLsKdr8r19OC24="}]}, "directories": {}, "dependencies": {"babel-types": "^6.8.0", "babel-runtime": "^6.0.0", "babel-template": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.11.5": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.11.5", "description": "## Installation", "dist": {"shasum": "202580c24f286359eade67685bef6e2c6416558a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.11.5.tgz", "integrity": "sha512-q6miV4hPDAKX7oUwk1RUOhApjQKF/OPOfhxjuCwUTriJnGH/8zoMLPxkSSTy9JyC6lRM5+zdnOsDNT8LvQ16Gg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH931lzsoWr591PQl709C6OuNiqlBjekb2avHmTB5Ks7AiEAxgbT4vIqXjlhMxALvU92kZuu7JDklBSH4LSTM2jHjoc="}]}, "directories": {}, "dependencies": {"babel-types": "^6.8.0", "babel-runtime": "^6.0.0", "babel-template": "^6.8.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.14.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.14.0", "description": "## Installation", "dist": {"shasum": "db731640c67ea6ebaecf90eb9cdabddb584aeb36", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.14.0.tgz", "integrity": "sha512-fVfjHRlCZACrpeV6eo8Ed95kq1K2xLwR00DfSkfp/9rNa0G2uXTkElGEcQ299urQQkQZFIBhj8kZEV6oJktysA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMGyM0EeH3/A3kZw/b+wsNSTaCAjlNTAGfRpRLoPi/PAiEAreU5cA61/Po44+gyXud1Tl/z29pAx50DVRi0XGJlatU="}]}, "directories": {}, "dependencies": {"babel-types": "^6.14.0", "babel-runtime": "^6.0.0", "babel-template": "^6.14.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.16.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.16.0", "description": "## Installation", "dist": {"shasum": "0a34b447bc88ad1a70988b6d199cca6d0b96c892", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.16.0.tgz", "integrity": "sha512-S9/RjERTgjuvorn9X1XZcyM9YIpypOTvl1gcrHdb8/GybYyRsm7PzmV7BAHhv7UrcUVx3kayOM5iQZtycm2HMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm9BoXfyd6q8FZD3sudFTnT5Nkty2Ks03OuOgbbake/wIgSg5ok3tmlGJ5DBCVUyu1J2jIAJoX9onaezq/LTC/7BY="}]}, "directories": {}, "dependencies": {"babel-types": "^6.16.0", "babel-runtime": "^6.0.0", "babel-template": "^6.16.0", "babel-plugin-transform-strict-mode": "^6.8.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.18.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "c15ae5bb11b32a0abdcc98a5837baa4ee8d67bcc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.18.0.tgz", "integrity": "sha512-K1g9dU7rUBl4MeAz9lq9MknrzLisJgoYL51Zxrx5MdyAHURpLHQRBMx6s3YXfBjWuHPg3K3S6uhMshdpsyFyfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1Kd+S9BoP8C939/O6Y0QnVU9wsq5ubbnquGFwgILcxwIhAL+xdYKGdIXBUEs1869qz5K/nwOFnF2FDzu9GgmmfvSi"}]}, "directories": {}, "dependencies": {"babel-types": "^6.18.0", "babel-runtime": "^6.0.0", "babel-template": "^6.16.0", "babel-plugin-transform-strict-mode": "^6.18.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.22.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "6ca04e22b8e214fb50169730657e7a07dc941145", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.22.0.tgz", "integrity": "sha512-GOnHZgNkBpbEUOlnrbCOOZj2pWKC8a7u8ZtlxFckJMiXfgvWab43/0KByFMfbHx1m5NDf4soV5pLSYh/SQF2Wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0lUujwRVLH0ucEMjKN0lNhjcMNYoyw5mQz/TKUDmcbAiEA3ITjzxip+qy22l8obw6h7UrUp6GIIJ65CD43JMfvC4Q="}]}, "directories": {}, "dependencies": {"babel-types": "^6.22.0", "babel-runtime": "^6.22.0", "babel-template": "^6.22.0", "babel-plugin-transform-strict-mode": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "6.23.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.23.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "cba7aa6379fb7ec99250e6d46de2973aaffa7b92", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.23.0.tgz", "integrity": "sha512-hGtbK7id/NWs0FB0ifnIKQtkX5giq5AL4cjrAvx0s/Rb07JJ0pFTDN54A44yXbxTvNDQEekXxFMQo+cT5m96Ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdsRR0bP2Xnyp9GOXFgKZG2BuJu5ig1CSCADuoQdWK6AIgRvC58qw2NlCkb8hGaB8TugQFh0pHxTVNIfxCNuaZXbg="}]}, "directories": {}, "dependencies": {"babel-types": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.23.0", "babel-plugin-transform-strict-mode": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.1", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "ff6f036af74337cc64a9bb661e16563d551c9cd8", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.1.tgz", "integrity": "sha512-zV40Kru3saxCjXbTcAwBWkF/75Yk35qZLOPDad164wJ+5lLX5cVq+TCAzuk6ErUGa7KYNbhsfOLI0mrfU5PjkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaDkkEdj6xhKfjpElyQk17Fww0tI64rwPB6fgWJtdx5gIhAPnrY+l+IvRN4R9ZsRLZrQI9MZar+JsicpGieTjW/Fnw"}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.1", "babel-template": "7.0.0-alpha.1", "babel-plugin-transform-strict-mode": "7.0.0-alpha.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.1"}, "hasInstallScript": false}, "6.24.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.24.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "e921aefb72c2cc26cb03d107626156413222134f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.24.0.tgz", "integrity": "sha512-ThvbCu0ZtM4HiI4wgTKB4s5WmcAiYNfN5Lmzb3T7fJot71cBmYTk3u2fnoX9vc0A5wVM5IMrlLsBk/kxcpuK4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+ZDU0lP6q9L38DMPhiGMoAdY6sV+HJUD03zXvrb+QaQIhAMHi5f2WyHGskeHPYUxOJK8/LJL0/0jWqMobLcr++OSf"}]}, "directories": {}, "dependencies": {"babel-types": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.23.0", "babel-plugin-transform-strict-mode": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.0"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.3", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "9d5b06621da4f6f665345d4ecc86084f74f608ce", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.3.tgz", "integrity": "sha512-XPknz0Xkbbx3uF0jZtf0/3ip4ViHlIWxB1y7KPsBEbiA4I3kbM3BmgG7C7bNEuyR9DUj1w0gJWLC5NoswvP34Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRTtf6e75ROSYi04t0is+5SGTeRWRujH33Muc3xh0dXAIgZF3Febxypplxg7kBqC0D7e23DrNlFl5VEMAG1GVyYI4="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.3", "babel-template": "7.0.0-alpha.3", "babel-plugin-transform-strict-mode": "7.0.0-alpha.3"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.7", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "8980ff65ada36f8853950878c80e1ca4d1188e90", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.7.tgz", "integrity": "sha512-kIIra21UwzyTh4ZDYXMn/gGerxkXsMIEns/a7fO4gjdNdkKh5pUcSk9EjqFxkOIcfsvA3mIRFrQ5cbYXxc1klA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICSydpWW6lJD+is35i59wB/oJeilYrjBwvv0W6BtNvhaAiAyH2eJ2TwTdben4GbGyDu7XL5EpCVvW3wU6OTAjMroeg=="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.7", "babel-plugin-transform-strict-mode": "7.0.0-alpha.7"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.24.1", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "d3e310b40ef664a36622200097c6d440298f2bfe", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.24.1.tgz", "integrity": "sha512-nVZMj02WXtBhlY+DKxrDhCo6uD0TSwvLXEq9NFxR9ERBfyQEEPi2CgWLnwJKn5otObFTm2u0j+LPPogfCMZeTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSySPZl6s94uhj08QZLDMmSYFEVpcDgdGU5D2zUlA+4QIgY/t9CvHlCLOYLxHXVeZM88hUHdOcQTA8OjY2U/XQE5k="}]}, "directories": {}, "dependencies": {"babel-types": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-plugin-transform-strict-mode": "^6.24.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.8", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "e86f997a8c36fb2d2f6641fd20903087929d5e91", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.8.tgz", "integrity": "sha512-gPHTqEm3PBuOkYyUmNdL72ds9HJbK4LeV4Hb2k2N0XDxTR1f3ZFIzTIHaG4dx65XjSwBgZk5KwPIfvlNddDeUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDxLXhoEiZaZV6ZHq3kAj0HhSYCua/BMFNba0BKtlAMcAiEA+GL6Ussv64FfZ/8mNAg6wCH3XpWnFcBmRTyrQnDSr2k="}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.7", "babel-template": "7.0.0-alpha.8", "babel-plugin-transform-strict-mode": "7.0.0-alpha.8"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.8"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.9", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "590b1461aadbe0247498f93185f54e1ac4914a0a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.9.tgz", "integrity": "sha512-3wdRCaoj2SG+HJfRDxhjeTD6PSeDPrtRH/LQ0vtyLFRm3G+Lpqakfb0bc/yIctDBzrBo89PRbRUQigQthN+8VQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjbJ9cRBD3jWydfmH8PVmMowYsH4RfgJt+7qlkF6yiaAIhAPy8pkTwg9KcSdmhkX9DJ3p0cY3yc6CjmTNp6zF9oPJ/"}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.9", "babel-template": "7.0.0-alpha.9", "babel-plugin-transform-strict-mode": "7.0.0-alpha.9"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.10", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "df396b09d461730ecb71ec17786430752f927043", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.10.tgz", "integrity": "sha512-MKGYhTpV7elAfgx+kcPzYSKHBBqNnBKRmKtuTcB/iVrVTwJIFsxxfNl5dCfJXzLAumyTzZPnFqZBonNRKfhMeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPYC+VZnmGi0yOA0ClgUwVMD9l9Q/DAVKCNbwzefumKAIhAPj3tAs7o3EXymtNQRGBUStZnWRNHvPI460Cn0stgeZm"}]}, "directories": {}, "dependencies": {"babel-types": "7.0.0-alpha.10", "babel-template": "7.0.0-alpha.10", "babel-plugin-transform-strict-mode": "7.0.0-alpha.10"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.11", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-0nTBGaWRFyrVpoaSknheKVYn4qt5RE+K0vDqMJwP8fFPumntR8mvBCMRbOIyv08P2oCguLghn+KXe115ZHkUeg==", "shasum": "e3748c25e0c282a6a2aad4b06b748924658c4ff0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEA9tY2v7KAbVF3665RUPeiDSJryNXvPotT4V2W/jWiUAiAqIOKT5lQqMRZe8i3vlwLJ0T1iRsSCuhnMwXTtUc9K+Q=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.11", "babel-template": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.12", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-HfR1Ajesv1dY7L6XRtC/7m95hgoYaYVMkq2aUcdJK9HTq0opfp9gwxIuBcoy7fLE03cHPRIMg3V8wkAqiCYGFQ==", "shasum": "73441a8dfa0e48c17ce02afae95444d996dde2cb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2YqVnANlVIZRsccU60gq0UHIqsmSm41oSe+r2btHNlwIgeajgZtlvp0ErYkgGZLOndoaXAVXB+bfp4zmbUqontU4="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.12", "babel-template": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.14", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "1cd9341139da25e71ed71a88f84080a362b45871", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.14.tgz", "integrity": "sha512-sNhiNL2Ed+kx3jG3YEZFEOyOg1Q74EjqQzmGSj1q+oPnj226oraAxiY8jKUv3VgoCh/9uCLYYCEwKG9PZzbWLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDh/LCGTxkQvagfG1+6ZYnIPgpbIkDIvIgbauvwFXXGjAiEAwSLPzdjK27huvna7E+HvP9n+TPrnHIvzaJ/4yAl61s4="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.14", "babel-template": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.14", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.15", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "6d65506527ea239ab8447629fdd57fb14b061775", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.15.tgz", "integrity": "sha512-BLTjIb8cnps2wkfuBTs+MqZBYtbTCXq2haIHkJ2rJxVnVO50K2ksZx5urmFNGx+jdUfD6ZLWEdk/au6PJc6OPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8w0PorgU+W7Sh5jpxOxXHtXauDKTs2FOWO6M55COiaAiEA++mVaRnCi0TwK0DS+xcI5E7C7vnppxvF/PAkrUeJTxA="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.15", "babel-template": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.15", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.16", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "e5467bab7fa802224901a7b12baafac9b2f88f29", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.16.tgz", "integrity": "sha512-Q0EQ0NM7UOc7jxv7RLr4r/ZfVkjcUM1JtlIIRXMxll9qr5fRwxeQKP+4+A0qKmv7Z+RHs44GPoaRUdsPfuNdfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHq8XmJuGdD/tS33RIJYw1pH5nXPjCJoBAgwiFQEO3ucAiEA8NnPeQe8mTDrd0KZkGP5rNZGMFLM7B3i/QR6y8/shKA="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.16", "babel-template": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.16", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.17", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "b32f5f18fb6ab249f7207e129d05499990f93000", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.17.tgz", "integrity": "sha512-6SZXl8Jeu8HLD6PXmmI/2POqTuQtiTzhdJsJHmrb7bZsLn1HdqRiyMr/kz9rnftsgWSzNjS+palkRAQuZHgWkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHjHGnXDPacq1eL7r/vDVNI6uv/gv/jXyZCjvNejnrwSAiAda5GUHxYU2/FC0J/EG36CdNtk8m1UneszSgUKbJTQyQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.17", "babel-template": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.17", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.18", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-POpX8WQPHsLLzMT7z6C9kUm/PGS4ir6WEgckF9hl6vXMdQQQiB5Mkt2UDNVQODNHipnKeVDsSv/jEdu4lZacrQ==", "shasum": "86548d8aaf15ee5b1bdb489df04591921c8ee7eb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClv/+aXcP9Z7kSFCh6f3HX9EVFeccuh9afkPcRxBiOLAIhAMi02prC9zPnQp4/NWM0YSZZrgZhalR+V87RL6AH9JuZ"}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.18", "babel-template": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.18", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.19", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-DIRp/vKPxcQiVhxcsWKjBtag3weJl9wanliUNdNqTyCD0+5fLAIKLqONU8CasPr5OH2uSfCMRo6w7BF3bzO7Dg==", "shasum": "7c3fe8db55592995ceb17260a650640c22f8847d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2NwZBfE5jnSRgT1/rf7zxIyiJOIhxnlLrMjkLq/weiAiBR4gUtuqsaB9/AhJTvFp46Nz8l/DWGE8b1u+TwE2y1Vg=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.19", "babel-template": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.19", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.19"}, "hasInstallScript": false}, "6.26.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.26.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"shasum": "0d8394029b7dc6abe1a97ef181e00758dd2e5d8a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.0.tgz", "integrity": "sha512-t33zrLpy4zDVZ/MnEk5GK4j4VAbEeLgKXM89RZPqSuRpuoZCqdxN8OgePBHKR7eBM9IyW5oCLL0xbw6H7I/qaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+1kXavec+REIe7z5LbSQVwQ/Jvite1uGsSX2W+3PfxAiBdL9p8Q4PFyQh9pue/BfJc518o6JrlPFMRwKgiSJes6g=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "^6.24.1", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-types": "^6.26.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-alpha.20", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-2XXArm8R3JX47FVLtRhiT9gQh8SdKFNpcwtB7vEXHEAJ9uUkgAWeUmKaIdcgJjVx0TSBO+1IM6s366aJdtqBvg==", "shasum": "e2ce9108c1216eec1acfaedc1d328eab821c2158", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGqJC2Id52jnDYQg9zJrnwkhIDoeckylcM7tznMs7oEZAiABy3568KdLZp/phPTJf3oPV1mcPmaoEme8VHggE5NfbQ=="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-alpha.20", "babel-template": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-alpha.20", "babel-plugin-syntax-object-rest-spread": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-beta.0", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-Lq9jwPpLxO02IC9ltaJgv0i1zI7mdYdd9mEL2PogvzedHbLNJhuj3clEXaajyy4euQIQpTdLla0zpe6+unREjw==", "shasum": "2cdb309e0f631c82b25f91bf1a45ab3acc37640e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIARNfDZa3S3NYTJJDiwObxQnF5MakRIzMpGTpre5hrNlAiEAx6XUD25nKnOw7Fs1PwtwIdQP5+aUNTmwNdLG8lo4w0E="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.0", "babel-plugin-syntax-object-rest-spread": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-beta.1", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-<PERSON><PERSON>+i76sx2ZwjqDA9uO/BtTHm7DCUyETtNcDbF2H+toFM3Oky6X2DTbBPbKOe1SXzqhO/3ZgZUPxJJhwzIHUuQ==", "shasum": "1b64bfd5be0026d8fd7c15496b3174ad3cd51cf0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEztF0eY4S0DkgjowsaVnSDM8wI4XaVr5EoCLVrFb0WQIhAIEnmk+Q72DIw42DQoEAHGGLlylxrQYMmVQXXY/9A7e6"}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.1", "babel-plugin-syntax-object-rest-spread": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-beta.2", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-n8vd5Y84q55Bmd/Kl4rooJuMuRGwmJOnhV9kPeowteVpM+kHplU493TS42EVswpoyqEQeaprSW2Na9JzWQQdqg==", "shasum": "2e9ecbadadeaf40bb19109baee586aac9a97aed4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3a5HbkZc2jZ37q5da531uc0OtSqEHu4l7Y6BRyaIHxAIgLaW7E1oE3zx64+ZPh8uROh2aP5UTvFy8lKMBKkS5ly0="}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "devDependencies": {"babel-helper-plugin-test-runner": "7.0.0-beta.2", "babel-plugin-syntax-object-rest-spread": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "7.0.0-beta.3", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-RcRIKOAerrm2M5+w8ITVoadLynjU4inaw+VtZwq1G10VBNn2bbik7hfXWdgnTUdHE8h3TGAafHX9Iw0Zxxuhrg==", "shasum": "d53ae18f16e0b6a50ab65cdafbe3fa51a22f39b5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF4+vhnfFtSDlSvsOsI5531ye21CCXH4R4914Xf+WaGEAiEA0aYp5hPfSX9ZGeVobmzqiRdgALNUJ3L1kVoDFA+GAxo="}]}, "directories": {}, "dependencies": {"babel-helper-module-transforms": "7.0.0-beta.3", "babel-helper-simple-access": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "devDependencies": {"babel-core": "7.0.0-beta.3", "babel-helper-plugin-test-runner": "7.0.0-beta.3", "babel-plugin-syntax-object-rest-spread": "7.0.0-beta.3"}, "hasInstallScript": false}, "6.26.2": {"name": "babel-plugin-transform-es2015-modules-commonjs", "version": "6.26.2", "description": "This plugin transforms ES2015 modules to CommonJS", "dist": {"integrity": "sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q==", "shasum": "58a793863a9e7ca870bdc5a881117ffac27db6f3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz", "fileCount": 3, "unpackedSize": 28366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4gG1CRA9TVsSAnZWagAA5h8QAIFZNrkSToxujndP3gvf\nUS3GQ+efYJANxMVm3cCOavaycGDgV6UsA3aMTfJ+039gAnadYMjawyF6PxfM\nIwJr68Kr7uMikLffi8ry3SwdanoNAPofpa/ukNa67I3CK6uu6dASSXRvavj0\ntCNRqHklK3+eVqgTc4bOoM1XNtIXTIKTc8CzV/QTiqz7KX4cubxe2vCf+gbP\nagUwQvFP2WbUjEPY2M1G4EoVfM2unfubiH1mANpgnJMmadK1AKaLDvD6gV+O\n40+NlYGlSA9HmPIJtxVGrRVGdRHPQ1o99uEbVFzhy3EW4I47xQPP522fppN9\nEWMMhfGGiSKw+YS6FU1tIS2W1/QI3gAHFykUE9m9xw7b8EI0LPB1E+C/Yf2U\ndcVidoniz67hrit+uApKwlbf9dY1Azl77m4dxTMhCt/tdyPO6dhYKXjqqFIq\nFv9POEVCrefdwB/Q+WqU6RTrpK1qZsIFpiP/sG+R0sz7ssPVjP4s10yyYx7Z\nFKdnj/hfHR9cddpm+GTziqRZFNjX5QwMvleEBfUU9vZt/gIeUpwNRnC1lf/W\nGv9EZj2Gh6zyJM0aoG4LmiTix7M9HfHxgRXdhhmV0piSsKyYnkoZ/ZECFa5/\nxd2X7q7VQ5bFaCJhfTsX1sDXkqjUcRGf43V/6T5gCHNBGf6SGU2fz2ua4NEa\ncUG+\r\n=MDo3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuvoNiIPFIVAl4+40uXbJi7fIG9ZEzLq8pbVJpdlrQBgIgOxAVRrLOWasV1Bn9NyXRDdvjo8rCSLLU7N5omEqEYP4="}]}, "directories": {}, "dependencies": {"babel-plugin-transform-strict-mode": "^6.24.1", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-types": "^6.26.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2022-06-13T04:04:21.338Z", "time": {"modified": "2022-06-13T04:04:21.338Z", "created": "2015-10-29T18:14:41.905Z", "6.0.2": "2015-10-29T18:14:41.905Z", "6.0.12": "2015-10-30T04:55:30.324Z", "6.0.14": "2015-10-30T23:37:55.487Z", "6.0.15": "2015-11-01T22:10:16.334Z", "6.0.18": "2015-11-03T01:24:44.934Z", "6.1.3": "2015-11-05T11:31:38.220Z", "6.1.4": "2015-11-11T10:23:47.685Z", "6.1.5": "2015-11-12T07:00:15.205Z", "6.1.17": "2015-11-12T21:42:14.609Z", "6.1.18": "2015-11-12T21:50:09.877Z", "6.1.20": "2015-11-13T11:39:21.637Z", "6.2.0": "2015-11-19T04:34:23.173Z", "6.2.4": "2015-11-25T03:14:28.533Z", "6.3.13": "2015-12-04T11:59:16.317Z", "6.3.16": "2015-12-09T04:10:48.158Z", "6.4.0": "2016-01-06T20:34:48.107Z", "6.4.5": "2016-01-19T23:02:58.267Z", "6.5.0": "2016-02-07T00:07:40.279Z", "6.5.0-1": "2016-02-07T02:40:49.519Z", "6.5.2": "2016-02-12T16:30:15.426Z", "6.6.0": "2016-02-29T21:12:48.344Z", "6.6.2": "2016-03-01T14:08:39.106Z", "6.6.3": "2016-03-01T16:40:54.212Z", "6.6.4": "2016-03-02T21:29:45.835Z", "6.6.5": "2016-03-04T23:16:57.391Z", "6.7.0": "2016-03-09T00:53:04.416Z", "6.7.4": "2016-03-23T03:37:49.097Z", "6.7.7": "2016-04-21T03:09:29.081Z", "6.8.0": "2016-05-02T23:44:45.783Z", "6.10.3": "2016-06-18T23:55:24.086Z", "6.11.5": "2016-07-23T18:09:39.217Z", "6.14.0": "2016-08-24T23:40:55.077Z", "6.16.0": "2016-09-28T19:38:55.700Z", "6.18.0": "2016-10-24T21:19:05.433Z", "6.22.0": "2017-01-20T00:33:56.292Z", "6.23.0": "2017-02-14T01:14:25.159Z", "7.0.0-alpha.1": "2017-03-02T21:05:48.308Z", "6.24.0": "2017-03-13T02:18:15.221Z", "7.0.0-alpha.3": "2017-03-23T19:49:48.414Z", "7.0.0-alpha.7": "2017-04-05T21:14:21.172Z", "6.24.1": "2017-04-07T15:19:23.876Z", "7.0.0-alpha.8": "2017-04-17T19:13:16.231Z", "7.0.0-alpha.9": "2017-04-18T14:42:22.453Z", "7.0.0-alpha.10": "2017-05-25T19:17:46.154Z", "7.0.0-alpha.11": "2017-05-31T20:43:55.433Z", "7.0.0-alpha.12": "2017-05-31T21:12:11.338Z", "7.0.0-alpha.14": "2017-07-12T02:54:05.152Z", "7.0.0-alpha.15": "2017-07-12T03:36:20.044Z", "7.0.0-alpha.16": "2017-07-25T21:18:15.036Z", "7.0.0-alpha.17": "2017-07-26T12:39:47.131Z", "7.0.0-alpha.18": "2017-08-03T22:21:22.996Z", "7.0.0-alpha.19": "2017-08-07T22:22:04.500Z", "6.26.0": "2017-08-16T15:54:19.441Z", "7.0.0-alpha.20": "2017-08-30T19:04:18.089Z", "7.0.0-beta.0": "2017-09-12T03:02:47.833Z", "7.0.0-beta.1": "2017-09-19T20:24:45.703Z", "7.0.0-beta.2": "2017-09-26T15:15:55.519Z", "7.0.0-beta.3": "2017-10-15T13:12:23.059Z", "6.26.2": "2018-04-26T16:43:33.470Z"}}