{"name": "@cloudbase/cloudbase-mcp", "versions": {"1.0.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "ab1c5415cbf1eef41cb2a5678c8f1fadc1a1ca23", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.0.tgz", "fileCount": 1, "integrity": "sha512-kqXPxLItQcqsGEFUK01ZHy8JQTKIGJOaEiZTmp6TRma6ij3RiygWxPfCYUtEW+o4BVCy02niE0wNLenfJU3xOw==", "signatures": [{"sig": "MEQCICwFcFVTcF0QXzk0anb7JoUg7tLWtZacDDFQ0iZ45GRMAiARaUJolVAhodezxsxLcyUnMynRHUfVrd0utXKg7pW1VQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 708}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 build/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.0_1744890552652_0.07811557082561849", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.1", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.1", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "11241341ef23acb2c6d7939ecfd5be246dff85ce", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.1.tgz", "fileCount": 2, "integrity": "sha512-P7QT+WwGsLaqm7JcSd38iO7GmNMsnQUilYO9XUjCHhUf0FxHYSYRUXZ43wn4DnDyn4Gf1I7/M7mWJXsvzStlVw==", "signatures": [{"sig": "MEYCIQDjaA5M56pQd1feQfs8V/mES2xafJWzTJcGpbEmYxJ8SQIhAI84S0fyS/tlg1xSApsPPElhsixFuo7t0jPAWti7P8wR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11557}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 build/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.1_1744891672329_0.3595781981468882", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.2", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.2", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "90342a6c9248586f81a86597f56e10c56923f292", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.2.tgz", "fileCount": 9, "integrity": "sha512-3SVSfNFeyr2SyBxzCoka5Rdm5W3//SRTvoMOQ5JYQAo/9euBAW4mwDTqwC4LypEItSyl2/MowJ/uDBAPbES7GA==", "signatures": [{"sig": "MEQCICwc0FTkmBYWwufJdrruoTIFnk2iVl2z6UI5D3wchmVkAiAz2kf0q4x0ftBGXwNcTKvjE8vhMjEd7dniYVI5A3ujlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71596}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.2_1744892755217_0.8882358745940624", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.3", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.3", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "390bfc0134442602f87fcc5212291609a3c26601", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-cQvGptIC7wxn25mviuVVzWtQFCK5RoXL66VkjvXGxqPL0kqhRVUVyU71sPMlvzr89o+hrjmgNJDPfvir+h33gQ==", "signatures": [{"sig": "MEUCIFTXB+9Fr0u5AKj1jaVRs0kp3LmligFNueDzYrjcJLokAiEA2w2ELS4ToSfWsk3GtLat8pg7puEp4nVHm+haIYXuZ38=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 111348}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.3_1744892854961_0.16922040748683598", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.4", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.4", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "301a82dc2417aae97acea7f8e1f47ab973e4a012", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-F9nqOFh9K4cCH2en/O9NGCihcGq7sCCrLTq1Gr/zT5jXuV1KA7k+82gEodLSJIKMNt3z2HYZ7mKQKB206qbmdw==", "signatures": [{"sig": "MEYCIQCJQDPNz/oeHVNvjKqGeJ3RdNEMtObTNvr3hlTv6fmT9QIhALSbhSPUAe9SdVIUKlFAWN7H/b6xVcKpG3vfprwIaQLg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71616}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.4_1744893668019_0.8141898185266232", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.5", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.5", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "371701718771d5cfb8dee45f9c494abc8e3eb10b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.5.tgz", "fileCount": 9, "integrity": "sha512-JxgmfBAhCcf5FgZCJ2Ey8Ko9uoOcNn0FVyAlSGFBr3+/m+PCC/0ckboIEmppsUItDE7UwB2lqQc8Ns0qSpjYqQ==", "signatures": [{"sig": "MEUCIC4coYfr/ptvFDumakfUHAotPoMPxBw07klxitupefWbAiEAgUzxHZcNoP2RcIk0UfTZr240KTK7uOqBKq9DChAFkCQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71692}, "main": "index.js", "type": "module", "gitHead": "c88aa8b643fd7bb13dc4637495440ea4f27f45de", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.5_1744894046529_0.01142671834098219", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.6", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.6", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "776b947b699c14770e1877787f47fe4cd4f366aa", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.6.tgz", "fileCount": 9, "integrity": "sha512-+TSEgdHdysBWV1t7mmJCn1p8oF36YD0Urd7Zc5+3GRRJ5banxY8eLG2W3spBQ1MWqWdFz6ugxACyxDhy3L5viw==", "signatures": [{"sig": "MEYCIQDMB4DALZkHteVgdf/tL4xVKjdHt8n5/JBoesjNoL4ATgIhAPRXG8tk2DPeh26B7yPGn6iMr8jcLN8IdwiNiwzRqdeB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 72340}, "main": "index.js", "type": "module", "gitHead": "7d729403e9a085f6c7d6a18b6b263fc1834a0d5c", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.6_1744964489387_0.134561692576185", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.7", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.7", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "06016b496225edd61caae886b603144575286fd5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.7.tgz", "fileCount": 9, "integrity": "sha512-rtkEuoEbTQCp+nkdxCw8BvVyAWrxdAXKWW57jxJPmVPcFyXtnzggXurTGg0pujTVxaDrBNmmwfig9ZT6P3R/0A==", "signatures": [{"sig": "MEUCIDJZPAgFgSxTp9hgppFs4TkVBKFETSfFIZiCZ3+Q7kMRAiEA9rs9oDNoey7Gq3fq1xYs30PlA3paj6FffUSUxfkoMQc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 72736}, "main": "index.js", "type": "module", "gitHead": "fd5c300b07aeac2106e2290c10641a0b2b8bcb07", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.7_1747137597498_0.6898301302146741", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.8", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.8", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "475f3d652126babbe5cc2cc28d3ed5ceca9d26c1", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.8.tgz", "fileCount": 9, "integrity": "sha512-eR4HkQNdgPB2G4Jb6+PQGMmwCYoXDAyfO6Imr1ejAc7nAYMR2dOsFDN/7Y8w4fIf03gIxSRxtxWBt01ZBPd6Fw==", "signatures": [{"sig": "MEUCIQCWWCGOluvnYp4IjWWongUExikWCkfBdbqIMSdA99mTuwIgfQ5MtVDTMbuvBTRH3pYUojjOXmauacL+WWHtrHX1kVU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 73024}, "main": "index.js", "type": "module", "gitHead": "fd5c300b07aeac2106e2290c10641a0b2b8bcb07", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.8_1747138031930_0.40065984033698965", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.9": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.9", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.9", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "3b876e03e22c87514dc3e078fd0bc31e742396e7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.9.tgz", "fileCount": 11, "integrity": "sha512-K8Yw0eZAeF8NP/7/FBRsqfcqXusfWyyGrjgCO9e1F8vY2Yk7WHVuboCuNa2bNfeLMgJEx/YahxlMR8f5eyQCXg==", "signatures": [{"sig": "MEUCIQCS1qYi8zO8IrtMsDzvjiX182yVZ3zGnyDf2n5M0aTqzwIgIF+uCkdayS6I7UsqFrrNHfPUA6U0qHWtpfJp93cemI4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84011}, "main": "index.js", "type": "module", "gitHead": "fd5c300b07aeac2106e2290c10641a0b2b8bcb07", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.9_1747216167918_0.10510290607030104", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.10", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.10", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "aeb892781af278cf0ccfcd3bfe24a99f40345f15", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.10.tgz", "fileCount": 13, "integrity": "sha512-K4EO6urK8vB4NXBIaBqfOr+6RpamwQKt7wiAOBIEEm6EaL99t2NstPUMDM4g8SGbWPex4hmwbxvvfQ56eaAtOw==", "signatures": [{"sig": "MEUCIQCFRTdIEdP4zoIeS4VDDd+tI5XJ/s7UZCZSBpPL7syKEgIgGHct3GoVlIbz9vCBzvNCmV2PZ1CXMv/RR6BlVugP7Ng=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86617}, "main": "index.js", "type": "module", "gitHead": "dd3e48dfb4c68921b0bc2a5ffd39cd8728256918", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.10_1747880342877_0.3234106851879077", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.11": {"name": "@cloudbase/cloudbase-mcp", "version": "1.0.11", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.0.11", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "dcb21c8c6e7aac07a564ec4ee7fa14e8e0bcff2f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.0.11.tgz", "fileCount": 13, "integrity": "sha512-NxQs7UeiSafGs6r/jHe+CiHn3xMPqdXNFBvhtyNIkvkp41J6PsB1XBPCHzHvZMisHFlngs4oQXZvYa+PiTLRng==", "signatures": [{"sig": "MEYCIQDqKThPL2ZPHruXb3ekn96aSlimJ75MnBtv6LmxoRP/6wIhAItNRyvHMN7RpXdSNHhrRfJOI7aiCKEj+mlaz8/dRNvm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86662}, "main": "index.js", "type": "module", "gitHead": "c1d0715f08c82f6183c3e6e6686769977efe34bd", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.0.11_1747974436813_0.23078265022418898", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.1.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.1.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.1.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "7773a9d0072b4124aba79b2ec6417c029ff6e44b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-2wWu/1Ayxh6qQE8tMWhgGvxdhSleSpHBrIcWCeqz/ILV25ntf1EUDvbXCe6g2FNv9zDU8unHJ5Ss96ewm15+MQ==", "signatures": [{"sig": "MEYCIQCsyKlkADdMvSV/IvzzQKPzwHVCiHOgyynfJzoqH6m0ZQIhAODgPvfKzgsUK2N+4oHFme94ZmyQiAiSZXhMw5ai+HZC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85389}, "main": "index.js", "type": "module", "gitHead": "456b812e805382d1f45eed53b05f52dc32e385d4", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.1.0_1748004753794_0.5102349551896177", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.2.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.2.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.2.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "efeaf763517c08744bca5a330a07433954ced516", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-hbubF5QjN3BMR9lQJ67Keg1vNQLkUBg5fyyBfyIsfVb2Qmmg2qW5MksvJAhELk7tQefouSYZPPri+5YAhdwNzg==", "signatures": [{"sig": "MEUCIG+4AlCSWbG5ogY4LHzrQK+F7Jed7ruQEbBmPi8l2K7BAiEAhWPqFOnF/2D25ZxhT7YoFHE2hEIDqRCgW+C79wssw+Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86384}, "main": "index.js", "type": "module", "gitHead": "77c0e523d7711851a89bf8732c9e72a82c596859", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "^1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.2.0_1748336395873_0.5827606239441689", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.2.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.2.1", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.2.1", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "f667f5d2a9173126f31c3a6f11c9419e45169ae2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.2.1.tgz", "fileCount": 13, "integrity": "sha512-zRwtkjfFozw7SAzPX5OLBdkf2Vf7hkeuSTUhaz5UPJeyrqE2JGlJJWNKm7nZKmvFnL6JF30Bzk9Yg6cI4qBjbQ==", "signatures": [{"sig": "MEUCIQCbBLGyZBAyULCODQNvQMMFgUH7FxssXPiR9DmrHDbydAIgZPB4IekCrA9FtymABHJErOHzrV3d4HAxKVnHx4ny8Ac=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86754}, "main": "index.js", "type": "module", "gitHead": "d2de6555af8816670c01338320a47df3be2f8bca", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.2.1_1748350556524_0.07812760152179288", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.3.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.3.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.3.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "544d53ce55b1703f9b3664d4e55d5208c0d251d5", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.3.0.tgz", "fileCount": 13, "integrity": "sha512-ZQCLU9NSG4pjsuavIU0CjDibSDPVoJoucjBv9WuuYKpgKS7uDpBRYHjYrrjZ0V1cksqjXSLKq/oWcY30vsXFbw==", "signatures": [{"sig": "MEYCIQDMBy3JBF2OZsxOcdV0chaAiGbz7O0K69SIVoAoxJU9uwIhAKpF7FigpaCJrVMZqAcN9NrjmfPGYJSpUobXgEWnyA0k", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88465}, "main": "index.js", "type": "module", "gitHead": "de3f2fddf819afb8437a864a1a838b64ebbb3074", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.3.0_1748436403892_0.9176457815026495", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.4.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.4.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "ae1b8f9618ca328d2c9d5250cd0fef01146e8642", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.0.tgz", "fileCount": 14, "integrity": "sha512-iCjFOkI5PlObpzGcn6h+YaIWlHqmORz1Uc1+DjFPQ4CpXCSIg3y9C1Q2S9cCwSULE+JeHvwNCFUqMLAggYoOEA==", "signatures": [{"sig": "MEUCIGOlnscKEd2Jcrf69ZRCeukISXpadzBtu/ZbzmJdgiDmAiEAp+5JqooocUb7LzL8xne9T4QPD9nFMWXzb/CE6VW2M5w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 92558}, "main": "index.js", "type": "module", "gitHead": "64107002160d4791148829962daefe0ab9dd4f7f", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.4.0_1748599113010_0.14478897255236034", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.4.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.1", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.4.1", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "1d883d47899d0bc17ea57fa73e390db659c10de7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.1.tgz", "fileCount": 14, "integrity": "sha512-j17GGk1NmiPVETViuheyhPaHrGl/R8iPQGOZrbSUNY5SBlGJQXCih7w99hLtjTphTT/jNEXYzdlyAvoJ0iLrbQ==", "signatures": [{"sig": "MEUCIQDze+/SRApTkkVJS8wJvxF79uF9jHwoz9ETtkVJZ63j1gIgAQFyR/L7TCiTcVkrPh6tUxKu9mluN4M2NEtpUXtEhyg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 100545}, "main": "index.js", "type": "module", "gitHead": "fffd16a120642d35dd115539301c05b12ffdbf9e", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.4.1_1748609340750_0.06583110688286475", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.4.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.4.2", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.4.2", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "780e3d330c44334fc53396fbe34523d92cf056f6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.4.2.tgz", "fileCount": 14, "integrity": "sha512-GybbYC2YW9twmzTlXYRnb2mYwUC8xF0Z987naN61ex2zX28XFO4sITnC9RAkDjdL65MgU6gdzI9Nh4T68e1jHQ==", "signatures": [{"sig": "MEQCIF9PDC3vRCKDsx9uqr23KyXEcibqjxYklUMJ7T90QJFMAiBHifestb2brM+m6GUa/Y0LnzcYQAOAALDTVqjD1e+Zew==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 100752}, "main": "index.js", "type": "module", "gitHead": "0d65f10e304c3f15dd6800caf26a2e237a6aa13e", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.4.2_1748609512506_0.28599823382349676", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.5.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.5.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.5.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "106b70fd37ba5f6cbbab8020fb6d658006f128bd", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.5.0.tgz", "fileCount": 14, "integrity": "sha512-0oyx/XRSTVf+7thc4J+ECRlSx4WWXew5YIB5MJkFVSzG+wPiXkUJi46ffnh6Jf5ytcGvu7qRYPR62/Ezby24Cw==", "signatures": [{"sig": "MEYCIQCfAIebhkuJHlrBJR8T8gNdRThbbtFgyvMqSZxLNI+OywIhAO5IHSxQBVGWaBE7RrgecEQhGK6x0Q6UD3uSDp0B9aT4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 100748}, "main": "index.js", "type": "module", "gitHead": "6550745feb4b45ef4dfec4cb8055dc0400b082f9", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.5.0_1749010554184_0.32953628089590303", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.6.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.6.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.6.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "b5d5bfde56afa30ac2e67298c4ab0235c84e4428", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.6.0.tgz", "fileCount": 15, "integrity": "sha512-CKzeETKBRK8WcHcCY2OMQusadDmqKDBDRfg5lV6raVxU7gaK2+yBlYhYsGOrhqcUNHyZCCc6l/PN1woB5RfL7w==", "signatures": [{"sig": "MEUCIFkJ3dD1RSlc3iRlt15VOnl4socELx19zQghu3bBlaHcAiEAnj0lvf4gPNPfzB3iZQUZlXvfK3sxN76L8JHZ71roM9k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 112533}, "main": "index.js", "type": "module", "gitHead": "231452948a8d0a865e29100bf2b9c6645ba96c48", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"zod": "^3.24.3", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.6.0_1749199133740_0.8000590424855412", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.0", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "936d589b5b1eed97c53b16bd7698e207191e3969", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.0.tgz", "fileCount": 18, "integrity": "sha512-051UXDkh20f4AhAVEqMh/qpM0WY30QZNurh6xwXExD8kWfRKNLpQ/apFmfw6EUfMop0h4Rs6LpooEgaNlH/uOQ==", "signatures": [{"sig": "MEQCIDVr3Pb7E6c8SvlfO1R6i8Vq+YMw7PwX8IZB3A9akh0TAiAvuBQXlHMukfET3yL0fpGMq85P5w3ffwoxwhC0ChaHng==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 187663}, "main": "index.js", "type": "module", "gitHead": "eab102446d12a03481aa5c606e1e17811014e025", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.0_1749528861285_0.9742384799042301", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.1", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.1", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "b843bab7b16f49dda09433100fda4149b2027d1f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.1.tgz", "fileCount": 18, "integrity": "sha512-BF6CA83ga2+ZgdLSEVZGvhKJ2FYFqCERUDvbOaDG/Vp4e1fY5hw7JXjW7HQGon9sGzBLs/OT4wmfBN2rhKxI0A==", "signatures": [{"sig": "MEUCIDikyw988VQf8kTqpPJpDmFYP3wpX9O2N7VnWi8n8TyQAiEA362Q7CpR6Em7QJE72N8oOCqkRqgLUN6H3zJ1bPCZmBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 218220}, "main": "index.js", "type": "module", "gitHead": "9012550b3c358593b343bcdb6209cb5149e26bf7", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.1_1749547393066_0.07062280314306268", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.2", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.2", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "811320d47e3ffdeb49390d15549cee4fbd491b65", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.2.tgz", "fileCount": 18, "integrity": "sha512-FVT+cOZpZiK3d6fZi64yFbX1zbAD8T2O9WMHohRIT2f7kPVZfydFiGOiD+URa7rVgpQjL7xC+x4QhsJ/9QIYrg==", "signatures": [{"sig": "MEUCIF7vTUP98RDFa/oazuYcBivvELKtMZQqfwqzJ9PJP0IXAiEA4TlOq7bgSnOKqugAQiqN+YNvv/JMbhL+x3wcIcFqEIg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 219599}, "main": "index.js", "type": "module", "gitHead": "f6f0d6d721f6ef2098536fcca7a47a225600b6c7", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.2_1749551101368_0.012569859113288784", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.3", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.3", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "2aeac2ae81271f098f5fe2d8f8cf8b619c348d7a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.3.tgz", "fileCount": 18, "integrity": "sha512-lX/7dpIrgA+odfpKwQrjA8AS2StqFnzeHO4YIIoxtfB6b9rP9KvXwtE/PwCqV13womXPINySsXhKcLj3eu7u3A==", "signatures": [{"sig": "MEQCICAQwD3LlwR8pw3S7tTxyKJQ9GzAT81+JH62mvIMKF5ZAiBvDyJUiou4byQy37ps0lYbkTz66qxaGVzrt8oIhf9vZg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 219660}, "main": "index.js", "type": "module", "gitHead": "2dbc5fdddd29ce6b3d6bf2686a07bd2707b753c6", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.3_1749613394507_0.4416651158783076", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.4", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.4", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "c83cba032189568ab6ac20f1c18b22a386b3393d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.4.tgz", "fileCount": 18, "integrity": "sha512-6oPsG4+pGVOqNwweVk1o7lLy3//xErXXyEiyrqX1CWXFA/4b8vl00T5oeX1tG2phd6KfiR8qTNMQ51ordE/Pag==", "signatures": [{"sig": "MEUCIQCSdAE+Y3KnRlaeTkLKpqD7RKM26WlegUfn/8vYLm6N7AIgOHtwciqcEY/VeWVbnu94vtttX5nP6ISIhgW37sOYBH0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 219882}, "main": "index.js", "type": "module", "gitHead": "e3cb41ba8c1ec0bea7d333627cbe542f91c52008", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.4_1749630943135_0.5715580667524649", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.5", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.5", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "d74b8bef106d2b4fc419d21eb9ab158d3ce075a6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.5.tgz", "fileCount": 18, "integrity": "sha512-10NrD5gy8cTMPlj9dJ+2V+scwCgD7YnQTWDcD3ZwSLYOr7M0HJa+iML4zwpw0aWIsz3PdHMwfKNVCVTnUehtoA==", "signatures": [{"sig": "MEQCIG7c7NZn5THQDXnzRLNlx2fP953+oRqe7VAOgiw62TxtAiBNisqXMNOmBk9IU3TdFpar/StDyBc297F0N3pttYQjFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224073}, "main": "index.js", "type": "module", "gitHead": "d1975c1a0f5184661b9bf79f621ed398f4e81c47", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.5_1749786586913_0.015099979251204543", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.6", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.6", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "78805ff77b7ae6a8dfb3e5f8b12fc6cc1093b53a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.6.tgz", "fileCount": 20, "integrity": "sha512-7BrdNYwWsqr/k0hIWl/EP03mmshhZJ2KDI/R5U12EerDZTNPKeUnRIbXkZ7VaKJaN319UieCj0X3JwvkWGNwaA==", "signatures": [{"sig": "MEUCIQDl+W1gIGYaW75jSP6QWobOCAdJM+Y5z6jx9eUqvp0mgwIgSBG+js1tpCjH+Vi/xFooPzQywrFkxwqd7dyWv2bP0+I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 259701}, "main": "index.js", "type": "module", "gitHead": "65872e4babf12a2ba5d8cce5cfe3e516f08c35af", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.6_1750241311473_0.07572063970293996", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.7", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.7", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "0a28eee73537d434e9344121a1ad22cf455b79e3", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.7.tgz", "fileCount": 18, "integrity": "sha512-ilzgJp/BF70ywsIExYpxVfp0Lhu2fWg0Bf0UA4PArAjXqNJiR4u3yCq08FkA0QRnriUA12kdQco96udLbTLxiA==", "signatures": [{"sig": "MEUCIQCeP+19cn9oueyH8CmKoiW4KLz9b6/iHh8zWNiCp/zA2AIgMsyVRQo/8hPuALDg077gy6iPAS1a72Gep1OfEh/ENAA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224073}, "main": "index.js", "type": "module", "gitHead": "1123792eb996490e5a19c236eaed735856fe0b2f", "scripts": {"build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.7_1750245978726_0.9493349710579098", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.8", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.8", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "bc10d70480d40da8aa1f9c11cf3e3a4cf7ae5ad2", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.8.tgz", "fileCount": 20, "integrity": "sha512-gB5/4Umb2wFHdH7rr4L/LTtgr9Fqk725hAIcVl2iCDDWywSTPDuEs5d+HxQrtTjRpxbopY04IMYscXlBWfJHjg==", "signatures": [{"sig": "MEUCICPEiiM+tmUGkQfa+mImMou4lb+xwFE9ZjgR+HrWlYhJAiEAiDFsnpFHhQCllmCFRLkXRl47yp4x5zAxeHfFhYh3RM0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 263291}, "main": "index.js", "type": "module", "gitHead": "30187041e6a623e43982f88428141834eb8abcae", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/index.js", "build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.8_1750320485459_0.4185206925503173", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.9": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.9", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.9", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "07bc2426ab8afdc7ae3d76f3216c32c0e2028bd8", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.9.tgz", "fileCount": 20, "integrity": "sha512-0jCpGRuvY3dJDgs/x5abJdUtC+8fxfRqbyvJvtcVMOLFIo32F/fw9QJ2xdoMMS57EQtREm0O9NKa23ieVRjWDg==", "signatures": [{"sig": "MEUCIQD0FEuuhIQJNublsLjlcDQ5dubUy0JZheK9PgT4S7RLzgIgPMxVlJ2w2aoOx9B1MbFAKeEnc6AWR8j5TI1lXPetf5k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 263284}, "main": "index.js", "type": "module", "gitHead": "9bfcf918a0bde398a2e2477272869c1f390dbd5b", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/index.js", "build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.9_1750334479354_0.37922595760554345", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.7.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.7.10", "keywords": [], "author": "", "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.7.10", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/index.js"}, "dist": {"shasum": "8bed6438bbe1eaba1add7baa698f8076aad9643b", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.7.10.tgz", "fileCount": 21, "integrity": "sha512-/HlJxEQ0tA7uKvBijEhDKLajvzlPPgF6Q6+KlAoQOYo4nAJ5sQZsKW/ihW46lfdQm6PZD1q5+cpDasvtHvKo3w==", "signatures": [{"sig": "MEYCIQCRHpJwkoZzuokskN2yF9lrcodQN4pcleVFbRkoAlJ09gIhANJFarOIKfLhlY5/XS01F7Cr7eJ0rrhh/eZsd1xv380n", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 264361}, "main": "index.js", "type": "module", "gitHead": "448b1dbc37d6661f0ecdcca6e3a9c9bc66de0fe2", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/index.js", "build": "tsc && chmod 755 dist/index.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.7.10_1750393815421_0.8149032723240133", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.0": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.0", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.0", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "542a9d8fa456a7411ec249210bab6c7b9ef00b26", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.0.tgz", "fileCount": 86, "integrity": "sha512-mTbXvxrNjVzI+hJ5135YSYDSiQLWmZKpjkBNqtQri8Pf4v7qk6qU0pc2jn6IhF8WjWxHGZlJ9LRS3vGFU6uKJg==", "signatures": [{"sig": "MEUCIQCAP/rB1wuRO2YJt65JFB7VT8fGbrKbO9KWUrtYHQvnMgIgW2/XiY3Up5Zvu1PdzgR+RJzEM+G6vS0BJiGc/gJ44uQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 410541}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "gitHead": "2369ff314746a4790189864b86c4c1bdd96d9d21", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsc && chmod 755 dist/cli.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.0_1750650060963_0.2563444343127168", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.1": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.1", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.1", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "b2bebb8a6d22185e41873397bc93bd2d48a0d412", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.1.tgz", "fileCount": 86, "integrity": "sha512-LpOwEKAzuZfHzHEcPXp+5Vuywjrads0m5TyWjWfnL3jh3qnGM2c78l+sfQD6RHq6a5Wyy363iw0CWrKiFUOcSw==", "signatures": [{"sig": "MEUCIQDZSUum6InU0D0TfELHrwDOtKzHMWzCKIgrthoE2UGVkQIgHupsLmkHSCFw+0B6eeVZGx76WHVe78V42uG325RDwk8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 410577}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "gitHead": "2369ff314746a4790189864b86c4c1bdd96d9d21", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsc && chmod 755 dist/cli.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.1_1750650782584_0.3769159364808998", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.2": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.2", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.2", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "ee9340b5fc27b43c07714f863ed9dfca84538e0e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.2.tgz", "fileCount": 86, "integrity": "sha512-ytOkx/i6H4yOfROTJdueofL3bPAQzArb5IJU6FyiGl4lbtUHoAj9JdaQbpPqW73w9eLrwXAl2DQGyR5OxkhhxA==", "signatures": [{"sig": "MEYCIQDkDq31BtxbuXM8L5zWeu/16rXV+orCHwDE2AuH58VDlQIhAMalQhPv11/RU5CpP+QowgmrGGDrRH2aU2kU6qlIV23w", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 432718}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "gitHead": "4a91e00b09781ee59f210b7edd68b1f8bc8d759f", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsc && chmod 755 dist/cli.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.9.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.2_1750748236622_0.42568738937632267", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.3": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.3", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.3", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "90de03919d23de7ede36a850f16e500e091210f7", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.3.tgz", "fileCount": 86, "integrity": "sha512-9yMFUCg94jwEVV2f6lQNE52arMhrQwnDT3p0t8PLF0e0reb9KiCrDl7tg9paqcQ7w/tAJDXT5H6ZcsjsoLJjAQ==", "signatures": [{"sig": "MEQCIHv988rmJCqpRhPyoAE6ocB9fxqqbjrDi/lqBruuwZOvAiAbqW24tFoy1MTatw8/9PubCqJAhf/qWRo09EvgJYkKWA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 429912}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "gitHead": "4a91e00b09781ee59f210b7edd68b1f8bc8d759f", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsc && chmod 755 dist/cli.js", "clean": "rm -rf dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.3_1750749501142_0.05495112219840381", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.4": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.4", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.4", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "5e61fed5253875b11bc03cde954886aa9d3bdfba", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.4.tgz", "fileCount": 14, "integrity": "sha512-ZPDsY6v9uX7NIkJDKDq3PBgp1+zJZ0lWNKjKtXF/Y0xbLP8x/tGyPPvsgYLCXBEFUI/BNPNKArqw9Rme6inzLg==", "signatures": [{"sig": "MEQCIHjK52QRespnkJy8GOyIFgUGKlbARVGuP049W27WX7ycAiAWFWtxIxGuuuZUcQIHKM8HRqourwR9lGAqsZP12v1suQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2216414}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "6ecb576a295505e2ae2c0365be659e12a6008e3e", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsup src/index.ts src/cli.ts --format cjs,esm --dts --clean --sourcemap --splitting=false", "clean": "rm -rf dist", "prebuild": "npm run clean", "build:legacy": "tsc && chmod 755 dist/cli.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsup": "^8.5.0", "@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.4_1750824098621_0.7064279330525098", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.5": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.5", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.5", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "83a5e1aeeffaed644d5b8d30b7e3f7d1fd60b09e", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.5.tgz", "fileCount": 14, "integrity": "sha512-cIW1RsA6V0JfDDAhBrSbctGsl7YuEZTr9XPtYrZceHVpbLfiLwmGGw/fCu1YL6kfTt6Z1ptHZjqFgderVqH2Qg==", "signatures": [{"sig": "MEYCIQDwF5JmIw+b9jSLa+gmai8Wjq4PvtrhsZKKdTKehUzcVgIhAPEhJudZhPjTFGKgwjVa02KH5G9YhlNryf9De1ufmgpw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2230148}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "407a7889c818ce7bf1da4bb155bad406e907bff8", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsup src/index.ts src/cli.ts --format cjs,esm --dts --clean --sourcemap --splitting=false", "clean": "rm -rf dist", "prebuild": "npm run clean", "build:legacy": "tsc && chmod 755 dist/cli.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsup": "^8.5.0", "@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.5_1750926922710_0.5291418647153154", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.6": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.6", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.6", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "89b248df572526e1847f032fe94bcb7091bb5e00", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.6.tgz", "fileCount": 14, "integrity": "sha512-YKiS2uY0bnvLoueiyAJjNX+QB5Py1YKY4C5w8OIwZTknHpWuUv8DzX+lsmuQ0YwFj7mG8t3bhdD/V0WJHsaRdg==", "signatures": [{"sig": "MEMCICbkArkBELRc8XIbItKEPmt74GkQdY45z80b7aRWKg8RAh9uHeqcr4NzaeujuiDoDLXgKVILbXgkoS10+6C2Qy4O", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2232712}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "c8496a4a00f19af8e178a66f7d34224cda7c6ee6", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsup src/index.ts src/cli.ts --format cjs,esm --dts --clean --sourcemap --splitting=false", "clean": "rm -rf dist", "prebuild": "npm run clean", "build:legacy": "tsc && chmod 755 dist/cli.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.5.0", "@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.6_1750928339272_0.8198932803339913", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.7": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.7", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.7", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "7bf5f345ca1923f4c1be9e57175dd3ab487b65ba", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.7.tgz", "fileCount": 14, "integrity": "sha512-1gtvzpx56EnSragzJ6Gr+qQ+W+eEd6erjOL4VJxIcRb/2ftWapwFMHgXo1kjhz5IclRGNJKlQUMHmXdAaXjXfg==", "signatures": [{"sig": "MEYCIQDRMRCiMKMxWpyaeVXn/mQf8Xte2qtSnvtA0Dor/xGjjgIhANI6Av2eneuN+H0HSli9/oPOCXkh9mVE4Wko7+rMAkf7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2232812}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "0b31a39eebf07b3a0ba918b54cbcd079f8d37725", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsup src/index.ts src/cli.ts --format cjs,esm --dts --clean --sourcemap --splitting=false", "clean": "rm -rf dist", "prebuild": "npm run clean", "build:legacy": "tsc && chmod 755 dist/cli.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.5.0", "@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.7_1750992681873_0.5020599107153296", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.8": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.8", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.8", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "5cb12d882d5588fc3a0e9c60d6421c46fcb5ccda", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.8.tgz", "fileCount": 14, "integrity": "sha512-1Jpi7Y5O2koPHl9fGi2x3tXtzyCVGSHPMQusBOIqN5V7mv5Cr6ANZ4q+nHq0ESkrM8sKOZrpag+JgqLhgj2K0Q==", "signatures": [{"sig": "MEUCIHrMmychbN06fhM9AOh+T1c0sDvcMgqk5TZsfSL5l3pkAiEAw2OkUCFxJ+seifaZIYalRBx/SEJQALGPHkJ3mRXDMXk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2234208}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "0b31a39eebf07b3a0ba918b54cbcd079f8d37725", "scripts": {"test": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "build": "tsup src/index.ts src/cli.ts --format cjs,esm --dts --clean --sourcemap --splitting=false", "clean": "rm -rf dist", "prebuild": "npm run clean", "build:legacy": "tsc && chmod 755 dist/cli.js", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.1", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "express": "^5.1.0", "unzipper": "^0.12.3", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/unzipper": "^0.10.11", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.5.0", "@types/ws": "^8.18.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@types/express": "^5.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.8_1750993614182_0.7245829860720931", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.10": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.10", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.10", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "4934f1246d0b6f36eedbdec3ad5526b5fe9a69a4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.10.tgz", "fileCount": 9, "integrity": "sha512-9HL/Ks4sDWhLfP9C4pgn0EbD+hD6t8wa4KeMM1fpbN25dgMTR0yCCKLPnLGLW5o879/dx+u5NYzcCRxWwatxEA==", "signatures": [{"sig": "MEUCIAf3NmuwdYL6FKbLTmd0Z+O39tYrtveUKtslhKe1iDNXAiEA1ac6SXtxRiWroYWTk1K+CPxC0Jw+/AVyFGYBCimzlms=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14999126}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "6656fa214d1bf0eeff1fd366804204350e4d64cc", "scripts": {"test": "npm run build && vitest run", "build": "rollup -c", "clean": "rm -rf dist", "test:ui": "npm run build && vitest --ui", "prebuild": "npm run clean", "test:watch": "npm run build && vitest", "postpublish": "dxt pack", "build:legacy": "tsc && chmod 755 dist/cli.js", "test:coverage": "npm run build && vitest run --coverage", "prepublishOnly": "npm run build", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "8.19.4", "description": "腾讯云开发 MCP Server，支持静态托管/环境查询/", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "adm-zip": "^0.5.16", "express": "^5.1.0", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/adm-zip": "^0.5.7", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.2.10", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.27.4", "vitest": "^2.1.8", "process": "^0.11.10", "@types/ws": "^8.5.12", "typescript": "^5.7.2", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/express": "^5.0.3", "rollup-plugin-dts": "^6.1.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-commonjs": "^28.0.1", "@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-typescript": "^12.1.2", "@rollup/plugin-node-resolve": "^16.0.0", "rollup-plugin-node-polyfills": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.10_1751542724331_0.37664738971416334", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.13": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.13", "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "_id": "@cloudbase/cloudbase-mcp@1.8.13", "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "bin": {"cloudbase-mcp": "dist/cli.js"}, "dist": {"shasum": "56beefab5542aa351fba3e0f4c8dd4f42898e15a", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.13.tgz", "fileCount": 9, "integrity": "sha512-Ac8CpaFOpzroZldNp5UxftHSNg6hRhYmLg+QnQVmxO5aDflIo10p1OOhDfxuH843o6Ju4AQrlUw8aDg3t0hasw==", "signatures": [{"sig": "MEQCIHCg4OYZwZc/lU/Z37OTJxfRKceKldVHS/bJzQjGBGehAiAlSTbUPgd5/UjK9hGZ1H5N89z5PLXvxpNmGnddE/KMSg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15552658}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "dca84bb6b0027edf113f84447e1f33030e42de2b", "scripts": {"test": "npm run build && vitest run", "build": "rollup -c", "clean": "rm -rf dist", "test:ui": "npm run build && vitest --ui", "prebuild": "npm run clean", "test:watch": "npm run build && vitest", "postpublish": "dxt pack", "build:legacy": "tsc && chmod 755 dist/cli.js", "test:coverage": "npm run build && vitest run --coverage", "prepublishOnly": "npm run build", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js"}, "_npmUser": {"name": "binggg", "actor": {"name": "binggg", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "overrides": {"tough-cookie": "^4.1.3"}, "_npmVersion": "8.19.4", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"ws": "^8.18.2", "zod": "^3.24.3", "cors": "^2.8.5", "open": "^10.1.2", "adm-zip": "^0.5.16", "express": "^5.1.0", "punycode": "^2.3.1", "@cloudbase/mcp": "^1.0.0-beta.25", "@types/adm-zip": "^0.5.7", "@cloudbase/toolbox": "^0.7.5", "@cloudbase/manager-node": "^4.3.2", "@modelcontextprotocol/sdk": "1.13.1"}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.27.4", "vitest": "^2.1.8", "process": "^0.11.10", "@types/ws": "^8.5.12", "typescript": "^5.7.2", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/express": "^5.0.3", "rollup-plugin-dts": "^6.1.1", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-ignore": "^1.0.10", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-commonjs": "^28.0.1", "@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-typescript": "^12.1.2", "@rollup/plugin-node-resolve": "^16.0.0", "rollup-plugin-node-polyfills": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/cloudbase-mcp_1.8.13_1751601119397_0.9270583434672857", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.8.14": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.14", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.js"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "rollup -c", "build:legacy": "tsc && chmod 755 dist/cli.js", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "5c7123f2c7c6b3a8ed76cbc89fc40c8330795c7e", "_id": "@cloudbase/cloudbase-mcp@1.8.14", "_nodeVersion": "18.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-zVWeEAad6N60rNsrHQtb4OLP3K0PTuHGfpLcj9PvBaiC32jY+IUfFMrbMHoXB4r/5L7kPB0TQWqgEJSjv526OQ==", "shasum": "c76be61da97265bac1f180577c24d70971e5bda6", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.14.tgz", "fileCount": 9, "unpackedSize": 17149518, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBAvbvk6jIWUMqiBj416WHWZbsGZgn4sykQ0rcAIOIIBAiEAj0NSoAKdMh06hmL6YP0L3AT6LcJfaXgT1lXFf0emh/0="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>", "actor": {"name": "binggg", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.14_1751632695885_0.7769611956408282"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.15": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.15", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.js"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "rollup -c", "build:legacy": "tsc && chmod 755 dist/cli.js", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "32f8fc07dd54f3fdb972df8da99502a50a35d4a6", "_id": "@cloudbase/cloudbase-mcp@1.8.15", "_nodeVersion": "18.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-Q7znWNdOk+BRtFD172Vi+9nGY9+mJYMwkwnK6rSVQazJ+PEnvX+0fQ4zxeJEbTHap0bn8BfC4Y5sfKt3/BszBA==", "shasum": "0ba9ab81aa3db6a2842787687365fb2e3cbc4d8d", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.15.tgz", "fileCount": 9, "unpackedSize": 17162602, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCPuRl221u5ty9H9lOjh2jBdQUmvBNMa7A+VjzcNGFv0gIgfQop+KiYdnTswIV4qLdndL1rBfw6/95MRLjpAeG8S9A="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>", "actor": {"name": "binggg", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.15_1752053133480_0.9182535216851606"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.16": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.16", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.js"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "rollup -c", "build:legacy": "tsc && chmod 755 dist/cli.js", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "11f9e4773f088e2be28e1493235b7d485bc9db16", "_id": "@cloudbase/cloudbase-mcp@1.8.16", "_nodeVersion": "18.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-3y06SB/4rGPxZWA9rKjo7rMu6ZIDEyjAau0/+qp/GO1LvFAPGj6LcM/uwGR3hkPISdwdfrQufV3pBKjdCIi7Fg==", "shasum": "7cc825f5861b312152b8de6aabdacbbc7c656c7f", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.16.tgz", "fileCount": 9, "unpackedSize": 17169566, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEs2SWiMU0HUgc8pDS62IDF1QbDosq2M7a5EV3kwzFjiAiBiwQibz8rrgQ3nIUAP1HSi8gyC7JyQPudLV3Ma1fai6A=="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>", "actor": {"name": "binggg", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.16_1752062257126_0.2141195908654907"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.17": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.17", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.js"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "rollup -c", "build:legacy": "tsc && chmod 755 dist/cli.js", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "open": "^10.1.2", "punycode": "^2.3.1", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "e0036d6a3323b018f1ae1fa536278c83e44dc7e4", "_id": "@cloudbase/cloudbase-mcp@1.8.17", "_nodeVersion": "18.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-VqXxTFRjheZZoVkJ6RseAazGDYWfY/0uzveW4NBMiYvNfp6fy+3IK7PzDUybMvwEus7SCAfjFTsKph7ibIL+ug==", "shasum": "aae85bd29f1c2edca0d5343f18db0ebaee4c6024", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.17.tgz", "fileCount": 9, "unpackedSize": 17169403, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDwophE1/qHSGN96EQfDKZvBGm1eARstmdASTBbR4ZTQwIgCiofviWP1bcplh5c/IJ8EQZ5sn4AyS+wN9eXoapZSKo="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>", "actor": {"name": "binggg", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.17_1752161514275_0.01530440978048575"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.18": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.18", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "9675161ac55833f4d5febf4aff3dbafdcbad59f5", "_id": "@cloudbase/cloudbase-mcp@1.8.18", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-cAg+nl7GvJkjSfSuRT9FqdD0hiCzuB/g/uy0ONJGgHSrbLdtUyAeEBLeiWbwYy5R+G0SAIjB+YV9LXd5H780cA==", "shasum": "737ef1765746f9c9a86b78abe5950adfcb7493ac", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.18.tgz", "fileCount": 11, "unpackedSize": 8230229, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCID/YWJsdlROWHhQ4I9gx+F2UDdP+IoUoWdZqAdYC1uIyAiBHSnPfnhPvKeDOL9T3QiriL7VuZqiKUDh5BbU0rkCoZA=="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.18_1753069003908_0.3953372937570774"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.19": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.19", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "c3f67ab45a801fbaaa91fcaf8d6bf2771456f009", "_id": "@cloudbase/cloudbase-mcp@1.8.19", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-uQhA0/cqkB7p830dn6OLwwcYeY6IZnM7u/dbJ3+rsxrEuCMjZDODaveVpAh9MPe7QBoNN40qALovpqUsYc9KeQ==", "shasum": "4a95e73f2c81e9d5f09baf894717360cf2034852", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.19.tgz", "fileCount": 11, "unpackedSize": 8230268, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCcpP06YKIJnxLAgISBRbRAZpWdqz014UGUTn/6J2kD7gIhAIEXOrlORH5B6jpFKjGZnMgC91HVavLcNCXcfLhILFhB"}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.19_1753082992662_0.7940193254652184"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.20": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.20", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "dxt pack"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.3.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "c3f67ab45a801fbaaa91fcaf8d6bf2771456f009", "_id": "@cloudbase/cloudbase-mcp@1.8.20", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-VfmLlbyP3EYgO3JmnklBow8RyZ3MhoyvpKZoI5O7O+6gQ9Gs9JfC3R4iiyded5nbJv7MmyaUF3lLouy05kobUg==", "shasum": "9dcd4d87dd93fbffab3eaee4c1ed0fbd4fe736a9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.20.tgz", "fileCount": 11, "unpackedSize": 8229546, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHrunq2KgB2RhzcpxRfS9iGBUDIFQEuFIn90VaXmhFX6AiBzHadf+H7zC3klyngs6/H4hOZv3dNyY5ZhbwUSoogAaA=="}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.20_1753083822069_0.8542204592108629"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.21": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.21", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "fc8f4bab96ac33cb2fe019b358fe06bf171b4aea", "_id": "@cloudbase/cloudbase-mcp@1.8.21", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-YMcXpPCkxk9zZ7q9p/1rgxldd1F2CJYKiMGJPWEL0kxrwXcBB+vEUM9ooCL/olLabcwWTr3zNwNv5HS52iIa2A==", "shasum": "ce3a65204653eaafdc824d40838d1c48bb309cd4", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.21.tgz", "fileCount": 11, "unpackedSize": 8245727, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDAwVQdB1cGSxQhG6d3T7G+jVZH7qyoyTA5JceX1xQrOQIhAMtPSnBlQf5dMWAc43cRmojPofmU991ffn3C30P8IJZZ"}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.21_1753177211773_0.1131880169950299"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.22": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.22", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "7f2e9edc9fefc7d19da1f56b6d1b1a3804f30bba", "_id": "@cloudbase/cloudbase-mcp@1.8.22", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-c3e/L/aHd4vjassLg1eJpSdzza/6iU5SDAr5L5RVY35SW1gdMtndlAyfZat2WjsHpNvY4Sp3xcxDY9uwyWAAGg==", "shasum": "7428119aac442b073bc3435a4628750e65c36dec", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.22.tgz", "fileCount": 11, "unpackedSize": 8203141, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDxHc4BnmpQO4T2P4vXSYEN3WiyjuW/HLm134joBeQdfQIhAPitgvZFWS1jEi+nSqqRSQ/gIptWj1jCYGlRqssoAc+k"}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.22_1753263791470_0.06302543593405341"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.23": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.23", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "_id": "@cloudbase/cloudbase-mcp@1.8.23", "readmeFilename": "README.md", "gitHead": "b3ed7f035292e3360bf7a7a0c89010abc75580ba", "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-yk/q/TXEdwhf4FUzo+tv8u+xkLu+P1zwz/3F/wN7bcHQlIwwohjXEiVGehIHlwhf2rOwKTbwVaKu2D9JnRdIKg==", "shasum": "c5af28993b3d79cd23690935aa147b74100bef73", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.23.tgz", "fileCount": 9, "unpackedSize": 8201925, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDziYdb04iTR7hzvzuxsjYy50nQ5WYPSV0ZoGSwbEGD/wIhANPA6gRtc6AXjYPzhSeeyzzekw/ZdVSx6cxy6aFM/vhZ"}]}, "_npmUser": {"name": "areo-joe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.23_1753510062368_0.6297230515016214"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.24": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.24", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "^0.7.5", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "_id": "@cloudbase/cloudbase-mcp@1.8.24", "gitHead": "86294352751c7632554bce5d4663755946e2cbb0", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-tyUsvmdBG+JlvXXocW2MBtdA5N24vDrDnTFiTvPZmgdlSW22VkjESnd0JYJ94t8akbVjTtiEy40mVeEcici89w==", "shasum": "cf83b6df6fe8067bde213f54d6dfdada98d24dc9", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.24.tgz", "fileCount": 9, "unpackedSize": 8201803, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDVC6shAZCaGQao7Rty95PpH7QGoQSw+s/mO+epBpj/tAiA7SgdAna8AV54AigKS3H7vxBy/MJMqjY1OIYpaGAl1Jg=="}]}, "_npmUser": {"name": "areo-joe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.24_1753524057023_0.12239449523096324"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.25": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.25", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "0.7.6", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "_id": "@cloudbase/cloudbase-mcp@1.8.25", "readmeFilename": "README.md", "gitHead": "86294352751c7632554bce5d4663755946e2cbb0", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-uHYSF824fwbWGx2pZDTlt/OaXfE+SK4NsBo6/H3vAkUnrhOK/hN+ee+qiUK7ZIHDoa/L3Ed2XKmezQhjzma4gQ==", "shasum": "48106491f2a4bef6a88aec854211d2e217647240", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.25.tgz", "fileCount": 9, "unpackedSize": 9595372, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCAtzwBYKb/R5UUKetWZNE8Nr1mGFWecjxX6JbuY/3qEgIhAIuDLL/ntQ/Xl9bNYWOvF1jMFv5FNPlRXgDK78+BWpCo"}]}, "_npmUser": {"name": "areo-joe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.25_1753527984260_0.8929013257812233"}, "_hasShrinkwrap": false, "contributors": []}, "1.8.26": {"name": "@cloudbase/cloudbase-mcp", "version": "1.8.26", "description": "腾讯云开发 MCP Server，通过AI提示词和MCP协议+云开发，让开发更智能、更高效,当你在Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "bin": {"cloudbase-mcp": "dist/cli.cjs"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": "./dist/index.js"}}, "homepage": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit", "bugs": {"url": "https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "npm run build:webpack && npm run build:types && npm run build:permissions", "build:webpack": "webpack --config webpack/index.cjs --mode=production", "build:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/types && cp dist/types/index.d.ts dist/index.d.ts && cp dist/types/cli.d.ts dist/cli.d.ts && rm -rf dist/types", "build:permissions": "chmod 755 dist/cli.cjs", "test": "npm run build && vitest run", "test:watch": "npm run build && vitest", "test:ui": "npm run build && vitest --ui", "test:coverage": "npm run build && vitest run --coverage", "test:inspector": "npm run build && npx @modelcontextprotocol/inspector node ./dist/cli.js", "prepublishOnly": "npm run build", "postpublish": "node ../scripts/update-rules-version.js"}, "keywords": ["mcp", "model-context-protocol", "cloudbase", "tencent-cloud"], "author": {"name": "TencentCloudBase"}, "license": "MIT", "dependencies": {"@cloudbase/manager-node": "^4.4.2", "@cloudbase/mcp": "^1.0.0-beta.25", "@cloudbase/toolbox": "0.7.6", "@modelcontextprotocol/sdk": "1.13.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "cors": "^2.8.5", "express": "^5.1.0", "fd-slicer": "^1.1.0", "miniprogram-ci": "^2.1.14", "open": "^10.1.2", "punycode": "^2.3.1", "unzipper": "^0.12.3", "ws": "^8.18.2", "zod": "^3.24.3"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.12", "buffer": "^6.0.3", "copy-webpack-plugin": "^13.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "process": "^0.11.10", "rollup": "^4.27.4", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-ignore": "^1.0.10", "rollup-plugin-node-polyfills": "^0.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "webpack": "^5.100.0", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"tough-cookie": "^4.1.3"}, "gitHead": "4742968eef3e1915ccc44a3a6d5db69f34e47a70", "_id": "@cloudbase/cloudbase-mcp@1.8.26", "_nodeVersion": "22.17.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-/U32oNq4byd8cOwr4RtsDX6e6dfJl33+ZcQo4oa2l17iS8C55Ou7YOgFSWGGzlPvKJolKmtkQ+Ryj864FL0HXQ==", "shasum": "e81c6eba83ff7e40168062408ae9928e2913a0bc", "tarball": "https://mirrors.cloud.tencent.com/npm/@cloudbase/cloudbase-mcp/-/cloudbase-mcp-1.8.26.tgz", "fileCount": 9, "unpackedSize": 8233579, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCCoipbcKwCL0MwF+Rib5tiHVM4CEnt3SbdDU3vnLXdWwIhAOCTMjJ48gY2N7GQixdP18d+dL2aTCplf/wcdBILGRwL"}]}, "_npmUser": {"name": "binggg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "daniel-dx", "email": "<EMAIL>"}, {"name": "starkwang", "email": "<EMAIL>"}, {"name": "y<PERSON>ng", "email": "<EMAIL>"}, {"name": "binggg", "email": "<EMAIL>"}, {"name": "fengkx", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "justan", "email": "<EMAIL>"}, {"name": "woodenstone", "email": "<EMAIL>"}, {"name": "miusuncle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "areo-joe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cloudbase-mcp_1.8.26_1753780915807_0.3983229151062275"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2025-04-17T11:49:12.575Z", "modified": "2025-07-29T09:21:56.422Z", "1.0.0": "2025-04-17T11:49:12.817Z", "1.0.1": "2025-04-17T12:07:52.498Z", "1.0.2": "2025-04-17T12:25:55.454Z", "1.0.3": "2025-04-17T12:27:35.179Z", "1.0.4": "2025-04-17T12:41:08.176Z", "1.0.5": "2025-04-17T12:47:26.737Z", "1.0.6": "2025-04-18T08:21:29.539Z", "1.0.7": "2025-05-13T11:59:57.670Z", "1.0.8": "2025-05-13T12:07:12.193Z", "1.0.9": "2025-05-14T09:49:28.102Z", "1.0.10": "2025-05-22T02:19:03.049Z", "1.0.11": "2025-05-23T04:27:17.008Z", "1.1.0": "2025-05-23T12:52:33.975Z", "1.2.0": "2025-05-27T08:59:56.110Z", "1.2.1": "2025-05-27T12:55:56.704Z", "1.3.0": "2025-05-28T12:46:44.074Z", "1.4.0": "2025-05-30T09:58:33.189Z", "1.4.1": "2025-05-30T12:49:00.939Z", "1.4.2": "2025-05-30T12:51:52.692Z", "1.5.0": "2025-06-04T04:15:54.350Z", "1.6.0": "2025-06-06T08:38:53.902Z", "1.7.0": "2025-06-10T04:14:21.475Z", "1.7.1": "2025-06-10T09:23:13.251Z", "1.7.2": "2025-06-10T10:25:01.594Z", "1.7.3": "2025-06-11T03:43:14.704Z", "1.7.4": "2025-06-11T08:35:43.327Z", "1.7.5": "2025-06-13T03:49:47.116Z", "1.7.6": "2025-06-18T10:08:31.663Z", "1.7.7": "2025-06-18T11:26:18.910Z", "1.7.8": "2025-06-19T08:08:05.632Z", "1.7.9": "2025-06-19T12:01:19.555Z", "1.7.10": "2025-06-20T04:30:15.611Z", "1.8.0": "2025-06-23T03:41:01.131Z", "1.8.1": "2025-06-23T03:53:02.791Z", "1.8.2": "2025-06-24T06:57:16.783Z", "1.8.3": "2025-06-24T07:18:21.316Z", "1.8.4": "2025-06-25T04:01:38.859Z", "1.8.5": "2025-06-26T08:35:22.947Z", "1.8.6": "2025-06-26T08:58:59.501Z", "1.8.7": "2025-06-27T02:51:22.086Z", "1.8.8": "2025-06-27T03:06:54.470Z", "1.8.9": "2025-07-03T03:43:04.630Z", "1.8.10": "2025-07-03T11:38:44.587Z", "1.8.13": "2025-07-04T03:51:59.630Z", "1.8.14": "2025-07-04T12:38:16.195Z", "1.8.15": "2025-07-09T09:25:33.748Z", "1.8.16": "2025-07-09T11:57:37.419Z", "1.8.17": "2025-07-10T15:31:54.515Z", "1.8.18": "2025-07-21T03:36:44.191Z", "1.8.19": "2025-07-21T07:29:52.891Z", "1.8.20": "2025-07-21T07:43:42.260Z", "1.8.21": "2025-07-22T09:40:11.979Z", "1.8.22": "2025-07-23T09:43:11.664Z", "1.8.23": "2025-07-26T06:07:42.610Z", "1.8.24": "2025-07-26T10:00:57.246Z", "1.8.25": "2025-07-26T11:06:24.525Z", "1.8.26": "2025-07-29T09:21:56.066Z"}, "users": {}, "dist-tags": {"beta": "1.8.25", "latest": "1.8.26"}, "_rev": "1058-c6d72c7efdc3fb35", "_id": "@cloudbase/cloudbase-mcp", "readme": "<div align=\"center\">\n\n\n![](https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/mcp/cloudbase-ai-toolkit.png)\n\n\n# 🌟 CloudBase AI ToolKit\n\n**🪐 用 AI IDE 一键生成、部署和托管你的全栈 Web 应用与小程序、数据库和后端服务，无需运维，极速上线你的创意 💫**\n\n**🌍 Languages:** **中文** | [English](README-EN.md)\n\n\n[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)\n[![npm version](https://badge.fury.io/js/%40cloudbase%2Fcloudbase-mcp.svg)](https://www.npmjs.com/package/@cloudbase/cloudbase-mcp)\n[![NPM Downloads](https://img.shields.io/npm/dw/%40cloudbase%2Fcloudbase-mcp)](https://www.npmjs.com/package/@cloudbase/cloudbase-mcp)\n[![GitHub stars](https://img.shields.io/github/stars/TencentCloudBase/CloudBase-AI-ToolKit?style=social&v=1)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/stargazers)\n[![GitHub forks](https://img.shields.io/github/forks/TencentCloudBase/CloudBase-AI-ToolKit?style=social&v=1)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/network/members)\n\n[![GitHub issues](https://img.shields.io/github/issues/TencentCloudBase/CloudBase-AI-ToolKit)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues)\n[![GitHub pull requests](https://img.shields.io/github/issues-pr/TencentCloudBase/CloudBase-AI-ToolKit)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/pulls)\n[![GitHub last commit](https://img.shields.io/github/last-commit/TencentCloudBase/CloudBase-AI-ToolKit)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/commits)\n[![GitHub contributors](https://img.shields.io/github/contributors/TencentCloudBase/CloudBase-AI-ToolKit)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/graphs/contributors)\n[![CNB 镜像](https://img.shields.io/badge/CNB-CloudBase--AI--ToolKit-blue?logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHJ4PSIyIiBmaWxsPSIjM0I4MkY2Ii8+PHBhdGggZD0iTTUgM0g3VjVINSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48cGF0aCBkPSJNNSA3SDdWOUg1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIvPjwvc3ZnPg==)](https://cnb.cool/tencent/cloud/cloudbase/CloudBase-AI-ToolKit)\n[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/TencentCloudBase/CloudBase-AI-ToolKit)\n\n当你在**Cursor/ VSCode GitHub Copilot/WinSurf/CodeBuddy/Augment Code/Claude Code/OpenAI Codex CLI**等AI编程工具里写代码时，它能自动帮你生成可直接部署的前后端应用+小程序，并一键发布到腾讯云开发 CloudBase。\n\n\n**📹 完整视频演示 ⬇️**\n\n<a href=\"https://www.bilibili.com/video/BV1hpjvzGESg/\" target=\"_blank\">\n  <img style=\"max-width:  min(600px, 100%); height: auto;\" src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/mcp/video-banner.png\" alt=\"视频演示\" />\n</a>\n\n| 🚀 **核心能力** | 🛠️ **支持平台** |\n|---|---|\n| 🤖 **AI智能开发**: AI自动生成代码和架构设计<br>☁️ **云开发集成**: 一键接入数据库、云函数、静态托管<br>⚡ **快速部署**: 几分钟内完成全栈应用上线 | **Web应用**: 现代化前端 + 静态托管<br>**微信小程序**: 云开发小程序解决方案<br>**后端服务**: 云数据库 + 无服务器函数+云托管 |\n\n📚 [快速开始](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/getting-started) | 🛠️ [IDE配置](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/) | 🎨 [项目模板](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/templates) | 📖 [开发指南](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/development) | 🎮 [使用案例](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/examples) | 🎓 [教程](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/tutorials) | 🔌 [插件系统](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/plugins) | 🔧 [MCP工具](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/mcp-tools) | ❓ [常见问题](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/faq)\n\n\n</div> \n\n## ✨ 核心特性\n\n- **🤖 AI 原生** - 专为 AI 编程工具设计的规则库，生成代码符合云开发最佳实践\n- **🚀 一键部署** - MCP 自动化部署到腾讯云开发 CloudBase 平台，Serverless 架构无需购买服务器\n- **📱 全栈应用** - Web + 小程序 + 数据库 + 后端一体化，支持多种应用形式和后端托管\n- **🔧 智能修复** - AI 自动查看日志并修复问题，降低运维成本\n- **⚡ 极速体验** - 国内 CDN 加速，比海外平台访问速度更快\n- **📚 知识检索** - 内置云开发、微信小程序等专业知识库的智能向量检索\n\n> [!TIP]\n> \n> 🚩内置支持 Spec 工作流：让 AI 编程更工程化\n> \n> - 内置 Kiro 风格 Spec 工作流，支持 Cursor、Claude Code 等主流 AI IDE\n> - 需求、设计、任务分明，自动生成 requirements.md、design.md、tasks.md\n> - 摆脱“拉霸式” vibe coding，开发过程可控、可追溯\n> - 让 AI 协助梳理需求、设计方案、拆分任务，人类专注决策与评审\n- **Spec 工作流已内置在云开发 AI 规则中**，下载最新模板或让 AI 在当前项目下载云开发 AI 规则即可获取\n\n---\n🚩 **快速上手 CloudBase AI ToolKit**\n\n1. **启用 CloudBase 工具（MCP 配置）**  \n在你的 AI IDE（如 Cursor）中添加以下配置，即可启用 CloudBase AI ToolKit 的全部能力：\n\n```json\n{\n  \"mcpServers\": {\n    \"cloudbase\": {\n      \"command\": \"npx\",\n      \"args\": [\"npm-global-exec@latest\", \"@cloudbase/cloudbase-mcp@latest\"]\n    }\n  }\n}\n```\n\n2. **一键生成项目模板**  \n在 AI 对话框输入：\n\n```\n下载小程序云开发模板\n```\n\n如果你只想下载特定IDE的配置文件，避免项目文件混乱，可以指定IDE类型：\n```\n下载小程序云开发模板，只包含Cursor配置\n下载React云开发模板，只包含WindSurf配置\n下载通用云开发模板，只包含Claude Code配置\n```\n\n支持的IDE类型：cursor、windsurf、codebuddy、claude-code、cline、gemini-cli、opencode、qwen-code、baidu-comate、openai-codex-cli、augment-code、github-copilot、roocode、tongyi-lingma、trae、vscode\n\n3. **可选模板类型**  \n- 小程序云开发模板\n- React 云开发模板\n- Vue 云开发模板\n- UniApp 云开发模板\n- 通用云开发模板\n\n👉 [查看全部官方模板及说明](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/templates)\n\n4. **详细教程**  \n👉 [查看完整配置与使用教程](#2-配置你的-ai-ide)\n\n---\n\n## 🚀 快速开始\n\n\n### 0. 前置条件\n\n<details>\n<summary>安装 AI 开发工具</summary>\n\n例如 [Cursor](https://www.cursor.com/) | [WindSurf](https://windsurf.com/editor) | [CodeBuddy](https://copilot.tencent.com/) 等，点击查看 [支持的 AI 开发工具列表](#2-配置你的-ai-ide)\n\n</details>\n\n<details>\n<summary>开通云开发环境</summary>\n\n访问 [腾讯云开发控制台](https://tcb.cloud.tencent.com/dev)开通环境，新用户可以免费开通体验。\n\n</details>\n\n<details>\n<summary>安装 Node.js v18.15.0及以上版本</summary>\n\n确保您的计算机上安装了 Node.js v18.15.0 及以上版本。您可以从 [Node.js 官网](https://nodejs.org/) 下载并安装最新版本。\n\n</details>\n\n<details>\n<summary>可选：设置 npm 源</summary>\n\n为了提高依赖包的下载速度，建议将 npm 源设置为腾讯镜像源。您可以在**终端命令行**中运行以下命令：\n\n```bash\nnpm config set registry https://mirrors.cloud.tencent.com/npm/\n```\n\n这样可以加快依赖包的下载速度，特别是在中国大陆地区。\n</details>\n\n<details>\n<summary>可选：清理 npx 缓存</summary>\n由于 npx 这个工具本身存在一个缓存的 bug，可能导致 CloudBase AI ToolKit 安装问题，您可以尝试清理 npx 缓存。\n\n在**终端命令行**中运行以下命令：\n```\nnpx clear-npx-cache\n```\n</details>\n\n### 1. 快速初始化或增强你的项目\n\n我们为你准备了内置云开发最佳实践和 AI IDE 规则的项目模板，推荐如下两种方式：\n\n#### 🚀 新项目推荐\n\n选择适合你的模板，一键初始化：\n\n- **微信小程序 + 云开发模板**  \n  [下载代码包](https://static.cloudbase.net/cloudbase-examples/miniprogram-cloudbase-miniprogram-template.zip?v=2025053001) ｜ [开源代码地址](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/miniprogram/cloudbase-miniprogram-template)\n\n- **React Web 应用 + 云开发模板**  \n  [下载代码包](https://static.cloudbase.net/cloudbase-examples/web-cloudbase-react-template.zip?v=2025053001) ｜ [开源代码地址](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/cloudbase-react-template)\n\n- **Vue Web 应用 + 云开发模板**  \n  [下载代码包](https://static.cloudbase.net/cloudbase-examples/web-cloudbase-vue-template.zip?v=2025053001) ｜ [开源代码地址](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/cloudbase-vue-template)\n\n- **UniApp 跨端应用 + 云开发模板**  \n  [下载代码包](https://static.cloudbase.net/cloudbase-examples/universal-cloudbase-uniapp-template.zip?v=2025053001) ｜ [开源代码地址](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/universal/cloudbase-uniapp-template)\n\n- **AI 规则通用云开发模板** ：不限定语言和框架，内置 CloudBase AI 规则和MCP，适用于任意云开发项目\n\n  [下载代码包](https://static.cloudbase.net/cloudbase-examples/web-cloudbase-project.zip) ｜ [开源代码地址](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/cloudbase-project)\n\n#### 🛠️ 已有项目增强\n\n如果你已经有自己的项目，只需在配置好 MCP 后，只需要对 AI 说 \"在当前项目中下载云开发 AI 规则\"，即可一键下载并补全 AI 编辑器规则配置到当前项目目录，无需手动操作。\n\n如果你只想下载特定IDE的配置文件，避免项目文件混乱，可以指定IDE类型：\n```\n在当前项目中下载云开发 AI 规则，只包含Cursor配置\n在当前项目中下载云开发 AI 规则，只包含WindSurf配置\n在当前项目中下载云开发 AI 规则，只包含Claude Code配置\n```\n\n\n### 2. 配置你的 AI IDE\n\n> [!TIP]\n> 温馨提示：如果你使用的是模板项目，所有配置都已经预置完成,请按照指引进行检查和开启工具。如果不是从模板开始，需要按具体的说明手动添加相应配置：\n\n以下工具均支持 CloudBase AI ToolKit，选择合适的工具并按说明配置：\n\n\n| 工具 | 支持平台 | 查看指引 |\n|------|----------|----------|\n| [Cursor](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/cursor) | 独立 IDE| [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/cursor) |\n| [WindSurf](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/windsurf) | 独立 IDE, VSCode、JetBrains 插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/windsurf) |\n| [CodeBuddy](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/codebuddy) | 独立 IDE（已内置 CloudBase），VS Code、JetBrains、微信开发者工具| [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/codebuddy) |\n| [CLINE](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/cline) | VS Code 插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/cline) |\n| [GitHub Copilot](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/github-copilot) | VS Code 插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/github-copilot) |\n| [Trae](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/trae) | 独立 IDE | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/trae) |\n| [通义灵码](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/tongyi-lingma) | 独立 IDE，VS Code、 JetBrains插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/tongyi-lingma) |\n| [RooCode](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/roocode) | VS Code插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/roocode) |\n| [文心快码](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/baidu-comate) | VS Code、JetBrains插件| [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/baidu-comate) |\n| [Augment Code](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/augment-code) | VS Code、JetBrains 插件 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/augment-code) |\n| [Claude Code](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/claude-code) | 命令行工具 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/claude-code) |\n| [Gemini CLI](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/gemini-cli) | 命令行工具 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/gemini-cli) |\n| [OpenAI Codex CLI](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/openai-codex-cli) | 命令行工具 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/openai-codex-cli) |\n| [OpenCode](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/opencode) | 命令行工具 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/opencode) |\n| [Qwen Code](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/qwen-code) | 命令行工具 | [查看指引](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/ide-setup/qwen-code) |\n\n\n\n### 3. 开始开发\n\n\n在开始使用前，只需要对 AI 说\n\n```\n登录云开发\n```\nAI 就会自动完成弹出登录腾讯云界面以及云开发的环境选择\n\n后续如需切换环境，可以说\n\n```\n退出云开发\n```\n\nAI 就会清理本地的配置，后续可以再要求 AI 登录云开发来重新登录。\n\n在登录成功后，可以确认 AI 已经连接到云开发\n\n```\n查询当前云开发环境信息\n```\n\n向 AI 描述你的需求,进行开发：\n\n```\n做一个双人在线对战五子棋网站，支持联机对战，最后进行部署\n```\n\nAI 会自动：\n- 📝 生成前后端代码  \n- 🚀 部署到云开发\n- 🔗 返回在线访问链接\n\n开发过程中如果遇到报错，可以把错误信息发给 AI 来进行排障\n\n```\n报错了，错误是xxxx\n```\n\n\n也可以让 AI 结合云函数日志进行调试和修改代码\n\n```\n云函数代码运行不符合需求，需求是 xxx，请查看日志和数据进行调试，并进行修复\n```\n\n## 🔌 插件系统\n\nCloudBase MCP 采用插件化架构，支持按需启用工具模块。[查看详细文档](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/plugins)\n\n### 快速配置\n\n```json\n{\n  \"env\": {\n    \"CLOUDBASE_MCP_PLUGINS_ENABLED\": \"env,database,functions,hosting\"\n  }\n}\n```\n\n\n## 📚 教程\n\n### 📄 文章\n- [1小时开发微信小游戏《我的早餐店》——基于CloudBase AI Toolkit](https://cloud.tencent.com/developer/article/2532595)\n- [AI Coding宝藏组合：Cursor + Cloudbase-AI-Toolkit 开发游戏实战](https://juejin.cn/post/7518783423277695028#comment)\n- [我用「CloudBase AI ToolKit」一天做出\"网络热词\"小程序](https://cloud.tencent.com/developer/article/2537907)\n- [我用AI开发并上线了一款小程序：解忧百宝盒](https://mp.weixin.qq.com/s/DYekRheNQ2u8LAl_F830fA)\n- [2天上线一款可联机的分手厨房小游戏](https://mp.weixin.qq.com/s/nKfhHUf8w-EVKvA0u1rdeg)\n- [AI时代，从零基础到全栈开发者之路：Figma + Cursor + Cloudbase快速搭建微信小程序](https://mp.weixin.qq.com/s/nT2JsKnwBiup1imniCr2jA)\n\n### 📱 应用项目\n- [简历助手小程序](https://gitcode.com/qq_33681891/resume_template)\n- [五子棋联机游戏](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/gomoku-game)\n- [分手厨房联机游戏](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/overcooked-game)\n- [电商管理后台](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/web/ecommerce-management-backend)\n- [短视频小程序](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/miniprogram/cloudbase-ai-video)\n- [约会小程序](https://github.com/TencentCloudBase/awesome-cloudbase-examples/tree/master/miniprogram/dating)\n\n### 🎥 视频教程\n- [云开发CloudBase：用AI开发一款分手厨房小游戏](https://www.bilibili.com/video/BV1v5KAzwEf9/)\n- [软件3.0：AI 编程新时代的最佳拍档 CloudBase AI ToolKit，以开发微信小程序为例](https://www.bilibili.com/video/BV15gKdz1E5N/)\n\n---\n\n## 🎯 使用案例\n\n### 案例1：双人在线对战五子棋\n\n**开发过程：**\n1. 输入需求：\"做个双人在线对战五子棋网站，支持联机对战\"\n2. AI 生成：Web 应用 + 云数据库 + 实时数据推送\n3. 自动部署并获得访问链接\n\n👉 **体验地址：** [五子棋游戏](https://cloud1-5g39elugeec5ba0f-1300855855.tcloudbaseapp.com/gobang/#/)\n\n<details>\n<summary>📸 查看开发截图</summary>\n\n| 开发过程 | 最终效果 |\n|---------|---------|\n| <img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-001.png\" width=\"400\" alt=\"开发过程截图1\"> | <img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-004.png\" width=\"400\" alt=\"五子棋游戏效果\"> |\n| <img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-002.png\" width=\"400\" alt=\"开发过程截图2\"> | 支持双人在线对战<br>实时棋局同步 |\n\n</details>\n\n### 案例2：AI 宠物养成小程序\n\n**开发过程：**\n1. 输入：\"开发一个宠物小精灵养成小程序，使用 AI 增强互动\"\n2. AI 生成：小程序 + 云数据库 + AI 云函数\n3. 导入微信开发者工具即可发布\n\n<details>\n<summary>📸 查看开发截图与小程序预览</summary>\n\n<table>\n<tr>\n<td width=\"50%\">\n<b>🖥️ 开发截图</b><br>\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-005.png\" width=\"100%\" alt=\"AI宠物小程序开发截图\">\n<br>\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-003.png\" width=\"100%\" alt=\"小程序开发过程\">\n</td>\n<td width=\"50%\">\n<b>📱 小程序预览</b><br>\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-006.png\" width=\"200\" alt=\"小程序界面1\">\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-007.png\" width=\"200\" alt=\"小程序界面2\">\n<br><br>\n<b>📲 体验二维码</b><br>\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-008.png\" width=\"150\" alt=\"小程序体验二维码\">\n</td>\n</tr>\n</table>\n\n</details>\n\n### 案例3：智能问题诊断\n\n当应用出现问题时：\n1. AI 自动查看云函数日志\n2. 分析错误原因并生成修复代码  \n3. 自动重新部署\n\n<details>\n<summary>📸 查看智能诊断过程</summary>\n\n<div align=\"center\">\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/turbo-deploy/turbo-deploy-009.png\" width=\"600\" alt=\"智能问题诊断过程\">\n<br>\n<i>AI 自动分析日志并生成修复方案</i>\n</div>\n\n</details>\n\n---\n\n## 🌟 为什么选择 CloudBase？\n\n- **⚡ 极速部署**：国内节点,访问速度比海外更快\n- **🛡️ 稳定可靠**：330 万开发者选择的 Serverless 平台\n- **🔧 开发友好**：专为AI时代设计的全栈平台，支持自动环境配置\n- **💰 成本优化**：Serverless 架构更具弹性，新用户开发期间可以免费体验\n\n\n## 📋 常见问题 FAQ\n\n如有迁移、集成等常见疑问，请查阅 [FAQ 常见问题](https://docs.cloudbase.net/ai/cloudbase-ai-toolkit/faq)。 \n\n## 💬 技术交流群\n\n遇到问题或想要交流经验？加入我们的技术社区！\n\n### 🔥 微信交流群\n\n<div align=\"center\">\n<img src=\"https://7463-tcb-advanced-a656fc-**********.tcb.qcloud.la/mcp/toolkit-qrcode.png\" width=\"200\" alt=\"微信群二维码\">\n<br>\n<i>扫码加入微信技术交流群</i>\n</div>\n\n**群内你可以：**\n- 💡 分享你的 AI + 云开发项目\n- 🤝 技术交流和开发问题沟通\n- 📢 获取最新功能更新和最佳实践\n- 🎯 参与产品功能讨论和建议\n\n### 📱 其他交流方式\n\n| 平台 | 链接 | 说明 |\n|------|------|------|\n| **官方文档** | [📖 查看文档](https://docs.cloudbase.net/) | 完整的云开发文档 |\n| **Issue 反馈** | [🐛 提交问题](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit/issues) | Bug 反馈和功能请求 |\n\n### 🎉 社区活动\n\n- **每周技术分享**：群内定期分享 AI + 云开发最佳实践\n- **项目展示**：展示你用 AI 开发的精彩项目\n- **问题答疑**：腾讯云开发团队成员在线答疑\n- **新功能预览**：第一时间体验最新功能\n\n\n## 🛠️ 云开发 MCP 工具一览\n\n目前共有 **39 个工具**，涵盖环境管理、数据库操作、云函数管理、静态托管、小程序发布等核心功能。\n\n📋 **完整工具文档**: [查看 MCP 工具详细说明](doc/mcp-tools.md) | [查看工具规格 JSON](scripts/tools.json)\n\n### 🔧 工具分类概览\n\n| 分类 | 工具数量 | 主要功能 |\n|------|----------|----------|\n| 🌍 **环境管理** | 4 个 | 登录认证、环境信息查询、域名管理 |\n| 🗄️ **数据库操作** | 11 个 | 集合管理、文档 CRUD、索引操作、数据模型 |\n| ⚡ **云函数管理** | 9 个 | 函数创建、更新、调用、日志、触发器 |\n| 🌐 **静态托管** | 5 个 | 文件上传管理、域名配置、网站部署 |\n| 📁 **文件操作** | 2 个 | 远程文件下载、云存储上传 |\n| 📱 **小程序发布** | 7 个 | 小程序上传、预览、构建、配置、调试、质量检查 |\n| 🛠️ **工具支持** | 4 个 | 项目模板、知识库搜索、联网搜索、交互对话 |\n| 🔌 **HTTP访问** | 1 个 | HTTP 函数访问配置 |\n\n### 🌟 核心工具亮点\n\n| 工具类型 | 工具名称 | 功能亮点 |\n|----------|----------|----------|\n| 🔐 **身份认证** | `login` / `logout` | 一键登录云开发，自动环境选择 |\n| 📊 **环境查询** | `envQuery` | **🔄 合并工具** - 环境列表、信息、域名一体化查询 |\n| 🗄️ **数据库** | `collectionQuery` | **🔄 合并工具** - 集合存在性、详情、列表统一管理 |\n| ⚡ **云函数** | `createFunction` | 支持完整配置、自动依赖安装、触发器设置 |\n| 🌐 **静态托管** | `uploadFiles` | 批量文件上传、智能忽略规则、CDN 加速 |\n| 🧠 **AI 增强** | `searchKnowledgeBase` | 向量搜索云开发知识库，智能问答支持 |\n\n### 💡 工具优化说明\n\n我们将原来 40 个工具优化为 36 个，并新增了 3 个小程序调试工具，现在共有 39 个工具，通过合并相关功能和新增小程序完整工具链提供更好的使用体验\n\n🔗 **想了解每个工具的详细功能？** 请查看 [MCP 工具完整文档](doc/mcp-tools.md)\n\n## 🏗️ 架构原理\n\n```mermaid\ngraph TD\n    A[开发者] --> B[AI IDE]\n    B -->|使用| C[CloudBase AI 规则]\n    C --> D[生成代码]\n    B -->|调用| E[CloudBase MCP]\n    E --> F{检测部署}\n    F -->|成功| G[云开发平台]\n    F -->|失败| H[返回日志]\n    H --> I[AI 修复]\n    I --> E\n    G --> J[线上应用]\n    J --> K[Web/小程序/API]\n```\n\n## 🔒 数据统计说明\n\n为了改进产品体验，CloudBase AI ToolKit 会收集匿名使用统计信息：\n\n- **收集内容**：工具调用情况、基础环境信息（操作系统、Node.js版本等）\n- **隐私保护**：不收集代码内容、文件路径等敏感信息，仅用于产品改进\n\n可通过环境变量 `CLOUDBASE_MCP_TELEMETRY_DISABLED` 设置为 `true` 禁用数据统计\n\n## 🤝 贡献指南\n\n欢迎提交 Issue 和 Pull Request！请查看我们的[贡献指南](CONTRIBUTING.md)了解如何参与项目开发。\n\n## 📄 开源协议\n\n[MIT](LICENSE) © TencentCloudBase\n\n---\n\n⭐ 如果这个项目对你有帮助，请给我们一个 Star！\n\n[![Star History Chart](https://api.star-history.com/svg?repos=TencentCloudBase/CloudBase-AI-ToolKit&type=Timeline)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)\n\n![Alt](https://repobeats.axiom.co/api/embed/60598d4f0cad83043b6317528e0fa0691122003d.svg \"Repobeats analytics image\")", "_attachments": {}}