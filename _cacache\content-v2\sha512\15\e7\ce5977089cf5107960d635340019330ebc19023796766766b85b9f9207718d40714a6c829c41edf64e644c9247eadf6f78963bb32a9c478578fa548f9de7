{"source": 1096543, "name": "json5", "dependency": "json5", "title": "Prototype Pollution in JSON5 via Parse Method", "url": "https://github.com/advisories/GHSA-9c47-m6qq-7p4h", "severity": "high", "versions": ["0.0.0", "0.0.1", "0.1.0", "0.2.0", "0.4.0", "0.5.0", "0.5.1", "1.0.0-beta", "1.0.0-beta.4", "1.0.0-beta-2", "1.0.0-dates", "1.0.0-dates-2", "1.0.0-regexps", "1.0.0-regexps-2", "1.0.0", "1.0.1", "1.0.2", "2.0.0", "2.0.1", "2.1.0", "2.1.1", "2.1.2", "2.1.3", "2.2.0", "2.2.1", "2.2.2", "2.2.3"], "vulnerableVersions": ["0.0.0", "0.0.1", "0.1.0", "0.2.0", "0.4.0", "0.5.0", "0.5.1", "1.0.0-beta", "1.0.0-beta.4", "1.0.0-beta-2", "1.0.0-dates", "1.0.0-dates-2", "1.0.0-regexps", "1.0.0-regexps-2", "1.0.0", "1.0.1"], "cwe": ["CWE-1321"], "cvss": {"score": 7.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:L/A:H"}, "range": "<1.0.2", "id": "NkiBfSlUi6dpakEO78PosZEiPQc6rpUjM51sbmpgjq7M7RMjj35IcyRGCrObaq+hfLkjhZliYjg0Qig2VRrT/A=="}