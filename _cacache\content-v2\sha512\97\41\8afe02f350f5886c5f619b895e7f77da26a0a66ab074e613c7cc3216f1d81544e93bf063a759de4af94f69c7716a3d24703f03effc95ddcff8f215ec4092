{"name": "portfinder", "versions": {"0.1.0": {"name": "portfinder", "version": "0.1.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "portfinder@0.1.0", "dist": {"shasum": "07ef1c071b93737e952b8a53fdde5127aafe4b47", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-0.1.0.tgz", "integrity": "sha512-ATrej09y8KcOYQaZRQCVGSJOUCaTd3vO7niebtajU0OB8Wpalebah94fzyHIBA4SgWJTO1JA4diQScQlh12fRg==", "signatures": [{"sig": "MEUCICirCd6+jwVNthmkHZopZddha82kI/Rz2lvEUNSjWrMEAiEA1J3qpavdCgmGoHIXZ/IvltBzR+hfoesQzrKkRiFNSMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/nconf-redis.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/portfinder/0.1.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"async": "0.1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x"}, "_engineSupported": true, "contributors": []}, "0.2.0": {"name": "portfinder", "version": "0.2.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "portfinder@0.2.0", "dist": {"shasum": "96fdf989e246ea7876c20356aeaecb4c83c9b6a2", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-0.2.0.tgz", "integrity": "sha512-fdSFt+eBb8hCYPkEuv6Hm2auuaEipxfs+Owmph8yO17x8ImjwZvudXzio4UWqAC3S94oilhQTHaBtGv2o6GwlQ==", "signatures": [{"sig": "MEYCIQDCj1K7zO6s0hVJCTgkxnhwt0zeZTXz+YNXmJhxmMH+gQIhAMy1l/bosSUjNz9Fac2mSq0XAjiO+3+Gf2BwIfp5uSq4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "**************:indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/portfinder/0.2.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"mkdirp": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "async": "0.1.x"}, "_engineSupported": true, "contributors": []}, "0.2.1": {"name": "portfinder", "version": "0.2.1", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "portfinder@0.2.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "dist": {"shasum": "b2b9b0164f9e17fa3a9c7db2304d0a75140c71ad", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-0.2.1.tgz", "integrity": "sha512-U7dHe7mfMMP5cIwbqBHh/likunxAEiBsQLiThUWjyeRK6/C3um2uk+VGiekUW8q3h+s793gdNfagCM3aaXMeOg==", "signatures": [{"sig": "MEUCIQCKZy3DjLPhKy3wb8PvtTLOeSP3CloxJNyW+kuR3ZBb6gIgbxIXqSRuV4xyaKxi4El7IrrUkntw4zw+d/mojvrOg+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "**************:indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"mkdirp": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "async": "0.1.x"}, "_engineSupported": true, "contributors": []}, "0.3.0": {"name": "portfinder", "version": "0.3.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT/X11", "_id": "portfinder@0.3.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "f9f2c96894440c5b5113b84e0ad1013042b7c2a0", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-0.3.0.tgz", "integrity": "sha512-6i0yyV4CyEehe9TWH+4Abm+xVTvjGZXR6kDGDUVXoPEyi8BMZv8W296Y+UVwAXP7aMGwbZFHqMOHIGIjh1CEDw==", "signatures": [{"sig": "MEQCIEeYgHY3oFRdsLHvsnUaQbz95jzs5l41xyokFURma3XcAiAUetrpMW8viAz+ZgmRe1bsnBma8QcbDK63aNzg0oxGbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "f9f2c96894440c5b5113b84e0ad1013042b7c2a0", "engines": {"node": ">= 0.4.0"}, "gitHead": "711121557c2f9fee81ff3dc12042007875b65aec", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "**************:indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "2.1.5", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"mkdirp": "0.0.x"}, "devDependencies": {"vows": "0.5.x", "async": "0.1.x"}, "contributors": []}, "0.4.0": {"name": "portfinder", "version": "0.4.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT/X11", "_id": "portfinder@0.4.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "a3ffadffafe4fb98e0601a85eda27c27ce84ca1e", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-0.4.0.tgz", "integrity": "sha512-SZ3hp61WVhwNSS0gf0Fdrx5Yb/wl35qisHuPVM1S0StV8t5XlVZmmJy7/417OELJA7t6ecEmeEzvOaBwq3kCiQ==", "signatures": [{"sig": "MEYCIQD7Q6W2m+GtuiYS8dpI8wY1eMql0OmIg7bd6LehxWSxhAIhAM14VuL+S2imz2FkZdAVj33I+ian3ErX+4cf16zXVWY7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "a3ffadffafe4fb98e0601a85eda27c27ce84ca1e", "engines": {"node": ">= 0.8.0"}, "gitHead": "8c3f20bf1d5ec399262c592f61129a65727004a9", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "**************:indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "2.2.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"async": "0.9.0", "mkdirp": "0.5.x"}, "devDependencies": {"vows": "0.8.0"}, "contributors": []}, "1.0.0": {"name": "portfinder", "version": "1.0.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "5bcb212ff52874d76c99bf8408094c153ccf2b95", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.0.tgz", "integrity": "sha512-2lI/OdfU4/83+jLcOotAAlgKYVlflVeLhNgxfH6ILPqHBH7iZ2zw5aTMbAHALobZKdbalOEivOMjjVfPRTgY+w==", "signatures": [{"sig": "MEQCIERlDEeG+gPakke4nBqZeX5fELggW8VPDdcAfYxkk1/GAiBIub1OZhB9vchBJ+HPh4hiIM68EXCYXpf5RQyOxKcUyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "5bcb212ff52874d76c99bf8408094c153ccf2b95", "engines": {"node": ">= 0.12.0"}, "gitHead": "120b4175efbfd11c65f0344f51606a2e80fb2e1e", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "2.14.17", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.0.tgz_1454482380124_0.1727100380230695", "host": "packages-5-east.internal.npmjs.com"}, "contributors": []}, "1.0.1": {"name": "portfinder", "version": "1.0.1", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "de84effc166ec86d73a22f51c173d886cd89a529", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.1.tgz", "integrity": "sha512-dhedw9fgdmuLpp9JAB+1qa5r4YFJc4r1vwbWkkkdC7JGuhy6wZgiRmIN7MKxV9h5iJoD2WE9jxIT5D+79UCoUQ==", "signatures": [{"sig": "MEUCIQCjIvjw0XYkjIN4RUDewxTf9lITpokP5fnZVfSPha3BpgIgOmupwAVCdncyZMm0IW/Fxv7spno77ZR+tepMwa69zKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "de84effc166ec86d73a22f51c173d886cd89a529", "engines": {"node": ">= 0.12.0"}, "gitHead": "669ed062561cff1c522341977e9b9cdb1688d3cf", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.1.tgz_1455095292823_0.15774197573773563", "host": "packages-5-east.internal.npmjs.com"}, "contributors": []}, "1.0.2": {"name": "portfinder", "version": "1.0.2", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "7ab68b4aee0ceda34925ef0af34732afa29059d0", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.2.tgz", "integrity": "sha512-dhqL18U9HcmwCb5ERHRvpflOj8yOAeMKzV5TtK5MyFtpm6vlT6O8lYNu6gke6zr6aDsfYyeFuXl/0IIspv4cUg==", "signatures": [{"sig": "MEYCIQDpt28DxstuYgwpRbX7kP/Iw3NIF/XzZggPmzRDK5pdQgIhAJLYVg0K24FYlpUnosxGBM6xumSOejTRkZZP+Cq8rDR6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "7ab68b4aee0ceda34925ef0af34732afa29059d0", "engines": {"node": ">= 0.12.0"}, "gitHead": "51bb0c79e1eaf3d6d593aa68e94f7c9d31cfd4cf", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.2.tgz_1456889346627_0.3661695315968245", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.3": {"name": "portfinder", "version": "1.0.3", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "77944c1049271215fda7452cb1152bb9c3f05ba7", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.3.tgz", "integrity": "sha512-4Lm2EsBC0Z0C5rFoXHoON/QFFLzPZzMWIvaL2d7D+JUZLCNV0CQU6fTymCxhwBiEtPqkUDXA9XvvLqxdRZ+D4Q==", "signatures": [{"sig": "MEUCIQCA8pZYgO0CFzEjf6cctaXfmUpp/bcjPaXA8DZIDKz9kAIgYJG1+XpZ9euc/H98If2oFtpsLoD21duO0iyTL5XdFiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "77944c1049271215fda7452cb1152bb9c3f05ba7", "engines": {"node": ">= 0.12.0"}, "gitHead": "e1dba743c3e545970f1127b1a21c0b73d96ef679", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.3.tgz_1458762791212_0.5817707516252995", "host": "packages-13-west.internal.npmjs.com"}, "contributors": []}, "1.0.4": {"name": "portfinder", "version": "1.0.4", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "db3f2f354624cbe79ec933d03a8ba58f5022d2f8", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.4.tgz", "integrity": "sha512-828CKEFMrGBBfcEwERflz5oVRVBAjdZgcDrziTwgCyC7mFTMglqkg1EoyxKqx9d+kR0L08NFPdRxzCdeOICYuw==", "signatures": [{"sig": "MEQCIHQeuVI5JN7Jbo5KcFAE/yXZ+1esY0nBPX+Iz4r8H6YUAiBdTIoJizuj542XnXlchH9zjBR1W/vSnldrWRj5HzyIAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "db3f2f354624cbe79ec933d03a8ba58f5022d2f8", "engines": {"node": ">= 0.12.0"}, "gitHead": "3c5ba8205433b2d3756adb4ea29407e3c0bcd391", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.4.tgz_1468539599515_0.5078083828557283", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "1.0.5": {"name": "portfinder", "version": "1.0.5", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "71551c57af898fff89e2875d42dce843ed7e2ab4", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.5.tgz", "integrity": "sha512-B2ZvXZM2jUsK0+Y/+3VH2C0fJ0cnQIa6KuSNQ0ItzEbz4kFq3IuI8Am7JlAHgoKz83U+lJxFghPZxjVOsEIT8g==", "signatures": [{"sig": "MEQCIGNSHbCVND70uKeQnTFjUjJ4sq0Us5jSLEcP0pRh8OxHAiA3S2po8HSyl0MBTMlwqlIkzUXA50vGZfE5tmp0Q8NBHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "_shasum": "71551c57af898fff89e2875d42dce843ed7e2ab4", "engines": {"node": ">= 0.12.0"}, "gitHead": "477e737ee9b70daac1af26b29ea1b6371dd61b42", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.5.tgz_1468642872704_0.4795233248732984", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.6": {"name": "portfinder", "version": "1.0.6", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "3f1922c8e2063714787d3d38992aa263471c9afc", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.6.tgz", "integrity": "sha512-GfVd1pCapPJgXY9CI8M28clsyyfs5yKHPRLm9NQTN+rbMMNd6HbR2gRD05fR5Fh4CUgczKJK5qmdvVFm7BGLXg==", "signatures": [{"sig": "MEUCIEmIL2FdenK3ijmFBqwQY1DZdx21iIKXg1xxKnLH6FvpAiEAn98CYPg07ln2GrvNHP3l6NDmZaYzxVcYU42x/4fjvPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "3f1922c8e2063714787d3d38992aa263471c9afc", "engines": {"node": ">= 0.12.0"}, "gitHead": "b215f125f9c693f4ddd2fef151ad00e96388467d", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"async": "^1.5.2", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.6.tgz_1470595161645_0.8010770883411169", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "1.0.7-beta.0": {"name": "portfinder", "version": "1.0.7-beta.0", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.7-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "d32e2b6b40bd396a2971badafadfb2812fdccff0", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.7-beta.0.tgz", "integrity": "sha512-2K6mB3uRkww9yKEq9F1t4TojCWkl6IzteBB/CK+5dkfemFUR4zK00NiZ5EICdWCSX1+4ZLFhIq122rEt76ErcA==", "signatures": [{"sig": "MEYCIQCZ8RWMv7BKn7Afx/8wxnDkf3WEsqsmjDMfIogK00cfgwIhAJ5WzfGv7S2eG88Rsf1Skp8kZhbWfXU9GryWcJOGYAGD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "d32e2b6b40bd396a2971badafadfb2812fdccff0", "engines": {"node": ">= 0.12.0"}, "gitHead": "a2aa5c8dff5b74df1dd89ac5d027e5a7022a543b", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.7-beta.0.tgz_1471331522140_0.8923570814076811", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "1.0.7": {"name": "portfinder", "version": "1.0.7", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "d486d2553c85ad22667f5c7841f477c64c7c1b38", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.7.tgz", "integrity": "sha512-9gI9BdOwVD9uyAp+1cQAR8g6JCg/PLsBeHmlbgi5bjVMfVaQ74/xeYwHjBI8n+POGH/FCu5JKzIcUkCysSewfg==", "signatures": [{"sig": "MEUCIQDz0JavhSTJAZRyMHuuGNe5lVgKus499i8zottei3izfAIgZaAoseh7bLZDXfysbcaNCspZxc8XhFVUd7N/2P5RSrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "d486d2553c85ad22667f5c7841f477c64c7c1b38", "engines": {"node": ">= 0.12.0"}, "gitHead": "39e39603ec1f5dd27d5fafa9ea1dc7d2e5a558fd", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.7.tgz_1471365625716_0.8825314422138035", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.8": {"name": "portfinder", "version": "1.0.8", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "b08b2ed226719d682b6f4158eb8e5b69a2561f71", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.8.tgz", "integrity": "sha512-nyst6vwWHQphnsuDLvxfir2AkocgyGkFW9+uelDG6wn+IpZGOOj503i9CEB90noy6ju62f+BYWMyCC6hB9FuhA==", "signatures": [{"sig": "MEYCIQCwpGT+btIK5Ky8lpMChM7BWIHP3v3iK6nS0LaeyWLcJAIhAIEGIHXd/KI9EU+pG0kFOV2EFC9Nj1jJNl1DVuMyQA6q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "b08b2ed226719d682b6f4158eb8e5b69a2561f71", "engines": {"node": ">= 0.12.0"}, "gitHead": "5972fbf86c9cffb21f39a79156779c5234ead7b4", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.8.tgz_1476742507432_0.70188731350936", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.9": {"name": "portfinder", "version": "1.0.9", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "b1ac8755d092afc0433f1c4832fa17d6d1f5d830", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.9.tgz", "integrity": "sha512-P0wvyanpLMvTGE3t7ZVu7ZBIkHmfI6Pbs+dhj01tDOjBAIqw5YKksA7oJb9GdYDIatlVXzya1r1jVQy88WDvkA==", "signatures": [{"sig": "MEUCIBeuHVvIsAmpbbOMxrUseUNnGdoJ9VTnXVp3jQ3+r5l8AiEAiLs0j973ui+kimGLt1jDtCue4v4uhbsURsxSM8zk1RA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "b1ac8755d092afc0433f1c4832fa17d6d1f5d830", "engines": {"node": ">= 0.12.0"}, "gitHead": "6be611bfb9602b4ea7a8729c94cc96b45084feac", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.9.tgz_1476834312144_0.7961409303825349", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.10": {"name": "portfinder", "version": "1.0.10", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "7a4de9d98553c315da6f1e1ed05138eeb2d16bb8", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.10.tgz", "integrity": "sha512-lqIIF/vjPpZ1IoByAHwwMrAesp/6UwWfqY9g9BsPmoh3scstFSJlpdfKKW+U6SE7m6y2YWRBcNrYFdmzeWnfyg==", "signatures": [{"sig": "MEYCIQDz8mKjyJjddSa6IKXNkclMM/rm2iYulJz/uHkI2P299wIhAOvyKHUn6xZAIipSosztBBU5ol0Pw6DQUxZQ4N9eQJkF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "_shasum": "7a4de9d98553c315da6f1e1ed05138eeb2d16bb8", "engines": {"node": ">= 0.12.0"}, "gitHead": "c5dd2cc76bd26d4771cee9e0637dfa27a7f89f24", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.10.tgz_1478824320733_0.8213196084834635", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "1.0.11": {"name": "portfinder", "version": "1.0.11", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "0163f3ec84d61f7604fc90121e53985ef30f0323", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.11.tgz", "integrity": "sha512-p+YOo5hHaz1+r5+UBD5Bphnhnpg6xQB8n/fjoCf6IsnrgS6e0n9bsn6nAwbEaqKv8YA8VmKx5S2VFQK3Uvo99g==", "signatures": [{"sig": "MEUCIQD9Gt9P/yZNONGfwknHSsPxD4Vvx2wsCYocz7/VdIEplgIgNe6pV4s8jv35dwYRANrhISkBQIrxMvgMVOUJGYqHQu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "types": "./lib/portfinder.d.ts", "_shasum": "0163f3ec84d61f7604fc90121e53985ef30f0323", "engines": {"node": ">= 0.12.0"}, "gitHead": "3e92c594578d2f3f20502e964f08b5cb4a45260e", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.11.tgz_1484679284465_0.22177174501121044", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.12": {"name": "portfinder", "version": "1.0.12", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "94a65114bec64433f9ce2116da33158238ce7ab4", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.12.tgz", "integrity": "sha512-n7d67ldd0PMrsFOHz6RuXw3XSy49qSm3vZe6mMbFc6IU2SgensqrxfzSUHPFqQRGf+L0hVbDgUc2NkLsYvMP8w==", "signatures": [{"sig": "MEQCIDCCH4Vai25GQ1rzfAmKDmBWOSw3TvAej0O2vbbiWMaXAiAj9pSm+lr5dadDL4a4heZgQWiV0i5cImJJ35j8hx1WjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "types": "./lib/portfinder.d.ts", "_shasum": "94a65114bec64433f9ce2116da33158238ce7ab4", "engines": {"node": ">= 0.12.0"}, "gitHead": "ffe3f01ba29c5e72789ce3640e1e5052c9a3e199", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.12.tgz_1484683935100_0.0860381000675261", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "1.0.13": {"name": "portfinder", "version": "1.0.13", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "bb32ecd87c27104ae6ee44b5a3ccbf0ebb1aede9", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.13.tgz", "integrity": "sha512-ULY4nnWaco7FwsQh6V0Gm0wTvMcCAT3GIsadt8Gqrrc4XJSXkC9XLHzAE1oMAtVS68jnrAjDypYfVPVP1JeTmA==", "signatures": [{"sig": "MEQCIHHT4Kc0yIfZ+FTGKWj0fsA0Eui3K8t4JzDz10SObBqWAiATQcuA9JwFgZH4ybWU5cW8YMzyJbG4UkWxJ30pHIFEgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/portfinder", "_from": ".", "files": ["lib"], "types": "./lib/portfinder.d.ts", "_shasum": "bb32ecd87c27104ae6ee44b5a3ccbf0ebb1aede9", "engines": {"node": ">= 0.12.0"}, "gitHead": "20161e8e00355099c90062a069a7aa68dc9bcf9b", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder-1.0.13.tgz_1485873781112_0.17709231283515692", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "1.0.15": {"name": "portfinder", "version": "1.0.15", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "28990f8a1d2794a5709010b25d9f6d90fcc2fa14", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.15.tgz", "fileCount": 5, "integrity": "sha512-THpr3TlxP1bPXp7jtuxk43cs7zyckFyO5AiNf93X7e67xs26BULEqaBxXILG9LULOHaKQwkR4cvrlhzVyHV/TA==", "signatures": [{"sig": "MEUCIQCXl9G5hNISU6uxh9lCoYTihnYxSfGJ0VJHM1RP83Nb5AIgTHVxNSXsNh4iKw0/Fiterjm/f8XIF/sv5ga3wUnhYJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaG35CRA9TVsSAnZWagAAKHIP/3Zi2puvHWuwFfcjnS54\neyEKu6yKOq59vxfkc0hCGvr7JKZTUzuzOr+pKWC8sq2NjzZstxQ5nX8fgvpb\nf4d9J5L7XHe2NvrZHmJfhfemfr6KHni/0dDH2GOh1O3pp98M4xAbPubJHb7j\ni38cuBJ+RzvPGda5xkrIUy82V3Kq5OEdhCZQ8nOUUQ/Zw9wgwB+O1WEBkUgH\nsJgO9CFGZ56EUPDqXonIr793FrMe5bYs1EPQOZamuaxJpjRIdatqGdv2Gbm7\nfwpHrodqDRYaBaalZgoNeEaaa9ExdXJZH+5LVaOIdutzsmKJJZjDNAdLzMmY\nQM+lvPBcTxh59iTxQgJdaYybCZBJv182FCzv6jgIQBWZu/rkEQHI2lU4dszx\nZgar3gbdlhvdNa9LAHzP8xe1tuVgnZhz+8MrHThHZ9yk61nifXrxz7scYqWf\no/B8RAIYaGlx6WQ018zRWIpa0WcLyJ2jKQoOay7NNg7Y82E2dOfq7Pgiyuui\nFIuz4+pvQIjwFwKUqU4nktUS+xa3GNhH/7ft6kSaMzoZNe3FpybNY7rnch6g\njfrYuZBIYJ/NYyooYH/opmoTSoaLHZOFcQt6qSCB6rnanF3OqDLCrWgeddvk\nvXVQyol7Ad8qouWp3/Gwj0qChLAU8tICEYIAFGn1r9bTFwmvA1zA1VZIJwVw\nQjJe\r\n=W6Ip\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "files": ["lib"], "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "955298c827cc662c3f9319ddd4f59952812b35bb", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.15_1533570522158_0.5360536961453843", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.16": {"name": "portfinder", "version": "1.0.16", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "a6a68be9c352bc66c1a4c17a261f661f3facaf52", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.16.tgz", "fileCount": 5, "integrity": "sha512-icBXCFQxzlK2PMepOM0QeEdPPFSLAaXXeuKOv5AClJlMy1oVCBrkDGJ12IZYesI/BF8mpeVco3vRCmgeBb4+hw==", "signatures": [{"sig": "MEUCIQDTSkPOrGXMLHGPa3vVPeAd6LsUy8Wjjw0cBfwoWcuWegIgdM8u6pXwiChaez/p1evPxq97tg1tNvtbW5hkXcEFidQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbacOzCRA9TVsSAnZWagAArIUP+wVgAkgPYZW9WM83x+/Y\nBdzhmJaEEYNaepF0aYe9XkbAsrNMFFeq87sB63PC44HXRbTFi1/mpey2gP/y\nv69RtmOOimBxbNDZlvMhGtkI0KIEx9GbvyucOJ7wL8eXbeqXn+PWc0iXqLW5\nY+aOp0Row5E9v1t9+e2jFVgE6QwZCCDQIipiKPrq+3V4XWeJuoa1y6CpHehm\nIdsBewAnknTS/5Z50Af1lFj+SVbUpt8jnC36ByL4dgtpk8WUMbl7cB+iy2yN\nO1veMkh8mWJOViHzUqfmiQFiko0Z7dypRvI0nkDHMOyDmrevtqYbQ0CKnHhh\niHfJW7Pa0ZU2s2WzRwRt2QHptT1b1Au1PtrVNdTWLmNy6PwEcvDlX4t10dTx\nwAtWrRy12bIpKgvQXjuGP0Y4lN8NrP+E7ZO3Urf27WPf/GOWjaKmRiuy4amK\nyEKncTMZ2ekESO7rgIEfT9IYdZQ7G5iFYOcIhxZq9ZvZceat6gHlPahEYnrT\nqdgaETIyE16RJHl3h5lNUW+fXLvfhoTYY4h159GcngveAV+7YuqZavfYAfmk\nQ2yxdyghKekQBNbXxtqxTFSBLR6cBtoJEQ+STkiiHqJZZbhShioz/iZ8Nl89\nT+gd2vACvR+bzjgHRUfyIQQNfosS/srZbQJEiODzNM7P9JgfTIUwSpqxz+IP\n5C9K\r\n=D1Ey\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "files": ["lib"], "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "8189856aac0f50656224393bf61cdb7cba950ace", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.16_1533658034239_0.738972112551346", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.17": {"name": "portfinder", "version": "1.0.17", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "a8a1691143e46c4735edefcf4fbcccedad26456a", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.17.tgz", "fileCount": 5, "integrity": "sha512-syFcRIRzVI1BoEFOCaAiizwDolh1S1YXSodsVhncbhjzjZQulhczNRbqnUl9N31Q4dKGOXsNDqxC2BWBgSMqeQ==", "signatures": [{"sig": "MEUCIFVhdhGc6OJkZ87ZY86WJfvIZSgR+vMY2h5/1cry2wkoAiEAp8qOcGnMZdfVaNrxjWXtNuEsnvGWLQLMs/CK30lmHcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeiU8CRA9TVsSAnZWagAAmDMP/0WBlSqPvbodilXnwTOC\nTMXCUZ3ar49r2HXjv5Axh3bXmnqPbLMblAgEVvWklMcGLl3v0rundxYmNy/V\nDnbegtobigReq1NWcTOtNnXtH301SK2SRxEVbepQ65GV6ascytFaxfKv6D+E\nmm4MqZ5lzAuWqWKnGj/6wPXZ8el9aE5dfNrmC/oqFlA6TE94Ueqrad2S45yS\nzJnPLt63MrfytWnc/kf7Rl05U8WKJ0y/DRLRC58FbdS5olTI/0aSVmwISb3z\nNMox+wWgzjLiY3ym57V5AHcekC/Qo17OUsPIxIayDZdJlFq9rqqurmhfNvGq\n9TWUwz/J6x+wrbMexAViI3hmZ0ULHTVu2phAW/E9lJXKlxh4WOpOZZuys8fq\n3xJLXGrjOUyIYtftHcRj1utq7F0br0hggVdml9EQiCB35AuIEiasCOe9J6ip\nO7+yqIEy+t7xkoS/U2W/FuFytFKfJdmJo9onyJp6ID3XQlBVBR+dm35P823o\ntHZIHTH6MwRqxUDRxTkQGklBcMjQaEezxsFpjgObmnWwWjjToEQ/ZVu0Exxq\nhYkC1otF3XP9XMesRZfa5kockZkzfyTlX6DkrF6KNtE7RqCP2NgFpktVbWke\n6fapOBVGkbNHV/0ruKw/Bwh0Ap69oteJGakQzUHmHX7jPLCccCDWZZBXr9eR\nEJ/M\r\n=+j7M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "files": ["lib"], "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "a4e69434de304b6094111eeaa559c8075bd0660d", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.17_1534731579734_0.6744418147856364", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.18": {"name": "portfinder", "version": "1.0.18", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "cf1106ff336fd4329b7ce32fda7d17d62c6bcf37", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.18.tgz", "fileCount": 5, "integrity": "sha512-KanzLOERzKoX3En5yTiV8K/arnU1ykYVokmtEn0PgCzqKZG9489tqW8ifp9+v3/VJZ5YDjvDt/PAP5WaPgk7FA==", "signatures": [{"sig": "MEQCIGxrYiBgLVbOr+Ik09koGpums+cHRZ7O/UynczNDNuvXAiBmyM9XjLbajL8Gkx6w2gbTYX0RpQuX/LXUdhsqeLLDHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbx8gDCRA9TVsSAnZWagAAsa8P+gKyjKiFmNJrhQiw+kVK\nWhc2YdcxNPUY4XLbww6znml5oyB0EiX1o8KeTMH4TsrDBq47vbFpPA0mvfNd\noBgKQi1uIcHMslpwUOEUc1zmDFg6Xi5y3LOMI/Rh1DWmqVRaLPbJZU9lN2rx\nq5Kg+fCCN+vvvUz1srwBoS10XD5F3VEc2owzZy5ceKOWFz8t6TfJ0VijZOMZ\ntc5iu2Y55x8asjXcJqmkc4K4ewZKR6zRlpSZQvlPn4b8g22mDlX2jY4qw8XT\nb+nmkI3QNeAf/uIW8kZxdU1JWDM64qKBryHY4S7r9yJScmKeJsYjddyNrT3p\ncml6OuM9/YYs2jldSqkbQ/MQgt8l5zFz49F+Tnp0DhJNMTWV8TGVnOR/4or0\ntW2MwPg60tbCf/xmB+NDdUB/buesaoLtVmstfOEpcTXs7xbT+A+SkIqx82ff\nUuQ010PzYaRJfOyu6ztoSz5M1hGw0/ePwmDuAEUPYsEcSf+1hvXnsbs0Tsq0\nhxp31XB3K2DA2FCEmW8l1ZrgKEuCIhLi19NS3Fxt3T6BKPicbXJbkVLU5OLR\nkrsv3qptSBn1NwRAV7aoaVBxlAeNtsJRiPvo5Or9kJrHvEnSrfscgsqm5TTW\nWBo7aRLu6exEjLQLa0s0zIM6dIyMi7DmMtRdrB4d4PEkWPBpewONiL27P61t\ngOCx\r\n=yZD8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "22113f250004874a00c714f14ff6121b221c20d0", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.18_1539819522685_0.5335505842980621", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.19": {"name": "portfinder", "version": "1.0.19", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "07e87914a55242dcda5b833d42f018d6875b595f", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.19.tgz", "fileCount": 5, "integrity": "sha512-23aeQKW9KgHe6citUrG3r9HjeX6vls0h713TAa+CwTKZwNIr/pD2ApaxYF4Um3ZZyq4ar+Siv3+fhoHaIwSOSw==", "signatures": [{"sig": "MEQCIDVde/VHHqqdLtvzjF6pInA9EZL+TwWJmL9G0nf8PcsDAiACGI3muk5rry8/5oZmmohAC6eUtaNG+dgY7vntl8obpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzoYKCRA9TVsSAnZWagAA8kIP/1EJUzTVfAm6J/QxtNQ9\nbO//2C5Bhq9sok0BK7i9qGSYV2IqcWEBc4fOP4zQOU5Y0kf7VJCzi9Gv78vb\nX1ui7j/eJZgRLoWpjohYpzH9M7NaiEluHZ18Xj5OJCiH9oROXa1oEC91N1tn\nskZQlNmIBL5iZyIbmQBfHjFKojjhg3uO0sZhd0iADS36FHp+DShqW1qpiHGh\nEfGL4T2+cghvwtB4JyIbAEsWQxK4xqL1rUXgZKyZ8nV1eNmacl/enoqzbOki\nkIBWNz9LrdSavrcN96CReZmPupvC/SXY9lJNSxPVkVIsuOzIVJDCQ2QRu4Jg\nV3HbTf7cWAvVRiPjV7wsI5MED0p/8TCtdGxwvIyJ9UrddeH0YRtgtclo2XNh\nkf0eE82ioIqFHbJaQqmbXvG0fPornEgBFFQzAxlmeCHCE/Opqe11P7Tp+zOy\nKe16Fuh/hgvbYTyxXBd4iSRUTJBIM2f7HN11HDQiy/eDFj9c5zWED5eqm+Fv\nh2D5AatOs+R9JAJxf6QBnldwPBq9yAMzrYrKqTh2ZGPtitX4MZUcRJcp42A/\nb9l660XyNojp3YWwIJZ3IcFH/DCP56jTMEFiKumPRKct7VFquJKeGBqdfsqg\npgA3zCsWHW8h3DKCbS1cDbjTXrRt7uk+yYFtmIIpW95RFcQh8MitjXzQ0E6D\nSLgu\r\n=8ZuZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "f0f71e38e563742565006a538573076d8375a0cc", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.19_1540261385866_0.15080079056810458", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.20": {"name": "portfinder", "version": "1.0.20", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "bea68632e54b2e13ab7b0c4775e9b41bf270e44a", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.20.tgz", "fileCount": 5, "integrity": "sha512-Yxe4mTyDzTd59PZJY4ojZR8F+E5e97iq2ZOHPz3HDgSvYC5siNad2tLooQ5y5QHyQhc3xVqvyk/eNA3wuoa7Sw==", "signatures": [{"sig": "MEUCIQD0VgoeGGBbFcEOyZgXVZ4U2JwKdvy3AkD+bj22voXcAAIgZRG2dl5Wdjhq7CVaWltLQwWDb1rdzsWT2uTgJk/nKKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAcKGCRA9TVsSAnZWagAARs4P/jZxKfnJ1yAfsGPhu43h\nORZ4r+09ewUOx6frGlbrYSe2iszuJQMUT8BYZxSUmQfzie+NKX9Zz1tNysld\njRncivEGGk2pBIC99/IkCXnOFw3V4HEfqTEqgsE8eIx2Mzfn4WB5vm3IhnAt\nBXpwB2O06j/cTJDVfLAS1dAAdNdl71u1xRqeCUxqJPr7Sa4zzTCA3sANxNnv\naWQ4Q5hk4PpprJrZc27b5LS6A+f3ycQNnvs9NqYFjJUkC/psgeA9icWNRDM/\ne/KQWn0Hfjvw7zTMRqd201OcZ573Z1jhd+d6CfvkZxXYAFUN8kFfWIL80jb1\nFaNDCYq98ACJ/PjFkMKVN8srKF3qWABYEISlkrYhY1TcilrjS0QV0DOuvxTR\nCqj20CIVkcl1XRHfat+y1cD/OsV4kL6AyTKAKo1Snba3qulcj4UKCrebbbYJ\nyYB4yIn6uRkv98rj7PBD6WyWEFy3lKk3pcRhjDMZzujS0/ajoNIPkY+SGqee\ndUSUNyjYSN4u+MRP1cf1L6CjdB4LmBhEaoASjltJEcrYjZBuEFJSauJQzDlS\nNoS27BZmRfSu5eBCIjD989lb0u7kM2/b6PiHsALi2mOCb+x1XL9zuaYXubnl\nzQwgS9ea5kAxPGJUNjp2+Z49Pg8pBj6IIggZQ9Ry4mQTZtbBPGx/2NiZcag/\n/cw1\r\n=wFih\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "8d0b757c3c318499f9fd1f8d865e8e7784801204", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.20_1543619206224_0.999012403569189", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.21": {"name": "portfinder", "version": "1.0.21", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.21", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "60e1397b95ac170749db70034ece306b9a27e324", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.21.tgz", "fileCount": 5, "integrity": "sha512-ESabpDCzmBS3ekHbmpAIiESq3udRsCBGiBZLsC+HgBKv2ezb0R4oG+7RnYEVZ/ZCfhel5Tx3UzdNWA0Lox2QCA==", "signatures": [{"sig": "MEQCIBoYXIVIGjA3iaAWCKkfKQl1aoFbyf1Xjq2MS3UCPSudAiAqrxx0y8GXGeZfNX2XmrUTBC+86WB5wRBhSy3e3rSlFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKJhlCRA9TVsSAnZWagAARMMP/3ADs4Knwf6pfUHJoRAd\nwTFCqtmweDMfYww7i+RWDv6P1yZh4nyFvKhb0a7AYIQurbKx28advkjB1irt\n6QKACRPOTxChyLG4KYNogAre+NFls+TDtn/eiY+pE9l+fhJGakaToj6Nm0hX\napSd7VxOHojTnRsml0wCndpPs6XDWxM8+TJaziKCRf4x9IXHpwTT+8O0SFwV\nJC3sp8Z2AQ0OppdThB0if6V5LdpYZ7LlHKKNlmdww7UuR5dwlyl/IbRNuMVM\n8NhqaaqK8MF8uA5GkjT9zAIxfchzqZata7JAtF1nOmhu2uLxWTLwTNSy+VtJ\nkn5BhkL3DxTUWRpykeozXHcGh43W5SFkqPtJnIfApSSqEghAlSL6mhR9ERpH\n3EKoEpJBGXfNFbNL3aLS0SfaplY4UjMHz9w4Ay23wqnYrT/53qjXaQys4qJ9\nszgV+O0q2eolkqaxJOdO+ObS8R1NjYbVhnWXv4jxhXdIroxla14kUpju/4De\n/ByeqBZBNDCF92Xc/l2wEMDgK8pbOBpwFaetM32W0J+tFuhBUnKZt4EWm+aY\nb8+1gPpejGZmCsU4os6gvaJeddquBgaMCFYCuerhVKKoW/0qgv/Z+/Rxw8aH\npNYsTPoVpDQ9YINZmyjrF1HWUIzhRMnKniuBuAP087u9tO38c33pK5RfzNIg\nd/oU\r\n=QZZc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "25556862945514cf890d9b8cee9020ab3bbe6c5f", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.21_1562941540245_0.5450296733377984", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.22": {"name": "portfinder", "version": "1.0.22", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.22", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "abd10a488b5696e98ee25c60731f8ae0b76f8ddd", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.22.tgz", "fileCount": 5, "integrity": "sha512-aZuwaz9ujJsyE8C5kurXAD8UmRxsJr+RtZWyQRvRk19Z2ri5uuHw5YS4tDBZrJlOS9Zw96uAbBuPb6W4wgvV5A==", "signatures": [{"sig": "MEUCIDUvfi6OJkcUeTNNPt8FGQA4bfoukz8fRvCIabKCf+KhAiEAg4yrHPEgIpZdxrYNz1bd+pgUAYEPzdVdWcSLhEgeeJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdV9PpCRA9TVsSAnZWagAApbUQAIhz5zMWLdxF6gKCNNq6\nhP1IVSjLsgn7JHGUvAYUKppVW589o2uzMulpeH5qu7lm79W2cKhXkuqB5Iaz\nZnESx5IU5QKok03RiN5WxRDxo1JmB8uFNKL+nk5Xxe4L2daF6LG1BM0ZYURa\nnyQ20sxMUELfMttwwZEPc15JMpeyoSqaIByIeW3b0P+k0zCU49g9mo/YShXs\nnrBon++VXJBelk8X6oxsxXxfkBwo/LCEAWMWi/txJhMrcfK825SDA2T4lXEN\nnKrgRIxifbw+Ryet5P3OevyeiK0F1yTuMHWm5WNx62KQqAdGOKd3xJNVkL7r\n/mSQs7/qbu5AbMEeKxoc8KhnfBKieDTR8SRODtApvFS6YxcqF/nkP7N8x03w\nfYvUd7QbOITVcydAG3rBL92g+ZasXDRkOhQuIm2EvWpI+Czwl0AqRbGgYQ3q\nfXRZhbGbnlyZegatPd35ALkYY84Nlbf41wwHX9BVeYFExZfelxvLG6QDJ9EB\nPkVW3e3MUrsmt7Lkv4baDH7woZ6qZ5HjWZX4UKh08lGvzJC1YmWnSFaL2xuD\nayfwW8q0Bg4RE4nuba+xhzDKtirdE+n2LYpQvWUSWwgYmERec7PEhYvQPpMe\nIu2zdxLrrEyUc2WOPGLh+t4mfNiMs6Kcpr652d5WjPdg6izfibSYcYhFmNWC\nqiSH\r\n=I6yd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "f2fa9d8a0c7144bb23ebc81269cbf92d9f462946", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.22_1566036968795_0.8818524221746387", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.23": {"name": "portfinder", "version": "1.0.23", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.23", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "894db4bcc5daf02b6614517ce89cd21a38226b82", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.23.tgz", "fileCount": 5, "integrity": "sha512-B729mL/uLklxtxuiJKfQ84WPxNw5a7Yhx3geQZdcA4GjNjZSTSSMMWyoennMVnTWSmAR0lMdzWYN0JLnHrg1KQ==", "signatures": [{"sig": "MEYCIQCNY+RejNNcMG8bG8/IRN7dhEHp6XVOMzLDyGn/cl+UwQIhAPPtILfF8xt2DG5PQQQvS8xTCba3o5ae9yF77ijUOF6T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWwSGCRA9TVsSAnZWagAAbdAP/2SBoOTbJZuzrJcfan36\nZaan4sI5PItTmX4i1I5yI1Nd1hEhKmhN4pZCD5IdyckI5nulTnIOzT5/5nKl\nopFmXiJuJ4/+ePplHlWnet7+IYKr3p4ZvF9F6NhtJ5tD9+rJ5aHjvurrMSOd\nyKN00nNf2LetN/w5TZ+ypws8CjoGSOgworp3n/AJDt4J6MrMmE67dwcys5Wv\nblzDUffqK8ti1Ju6bN9IZXFK585QJI5ayy1HZwJ9nlu0IjBpk2xYNMbyKrVD\nB4RbTmLFFznguwLpJOs6GSXH7c2U8mtWqXHxvpXqc7DWFe2VI6aXqSkDSf6Z\n7b4HaPbrip+jc2ASscsjbOheMOJSWRKG4KDPQ/JIWcD/hG44PACiu4fVWpVl\nlEm8pUYwtkT86XtM+E6DSFE4fi96d5/lad5nelGAZdLKdk7CuXkTf0XUCugS\nJ2zHpDiW9h4M/IyF4KbwrqjKO7ucBXA54R3e/o7/aqkHw/R8+sq7ecjIE8lM\nNSoqIbzNBUnOXMrqMczY7FOfZ9ksh0ZBDmUM3fIaWjET386kxHkpntc4eAZi\nRKhRaQXiEWDAfSICXIvSCx2zPL+vXkAtVR2KaHJmyyQ0skuRWUmdBHjpUcBJ\njpqEsV3DrOmL333pLL23ZQCRPAHMf3sjEy+IZtwok6okLDJEqwv1h0IR6/o4\nF3H/\r\n=1JNa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "727ee4e6943ed0b25a8e1a5fb93c8ebd65729ef2", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.23_1566246021540_0.4401182672890174", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.24": {"name": "portfinder", "version": "1.0.24", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.24", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "11efbc6865f12f37624b6531ead1d809ed965cfa", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.24.tgz", "fileCount": 5, "integrity": "sha512-ekRl7zD2qxYndYflwiryJwMioBI7LI7rVXg3EnLK3sjkouT5eOuhS3gS255XxBksa30VG8UPZYZCdgfGOfkSUg==", "signatures": [{"sig": "MEYCIQDT+irVOMxzdqSVvFqeNV5b7Mqx/EXuTlSWkQD03UrpxwIhANTaU5pS0R5k0NOl9hWcLC5NGKYYMpbcA6Z8u0oH1IH6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcWJsCRA9TVsSAnZWagAAyMEQAI6U2EC/hUTu4Jud2+xR\n/5C6dfeJxYeNia9zTFVuRd6Oj5fBaFCpofKLMZTCIXYAcuSp4Vz1W0Rr2izu\natEZ7wadRa7lP/0oVMDkjjTQFoQHZrTZSyQUTEhPbBou63w6qgtJ8oHdYBgo\nS9Ab3w3YOl1ZQB0rSgoSiiqSSyeoJpHzbfK/o+BnpbBPp2GsPAGpYYi2Guy4\nidZw9INI6u9+nltkb/g8tRYIoK5Kwlotz7R6gKn9iPBGDU/rgaqMYB3wzK5G\nYhpgY9GMouul3YFgCv5EneIqcGZj26SmrsSmrT4niJ4rH6BGYdRUbSJU9wiq\nzfaIoy82q/l7oc0nmk3TnWtau6nPkctQq2i3qtxUrA4cnoulrzKGoi4C5AW2\nAV+DhvUBUKNyZ+TW7GOr37c8GAeO3eJ9TgZVVLei8aCc9Gre4ORHSjPF38uv\nXLYNTBMYb+H2ijLjBMPWRni/FApfv7zk4kkpj2IaKl5UD7ryLUqpAChx7rPT\nCCVAFHlwiDflJS7V73qPkyEglZletH8RNR/wPOp5NoPJiezBnoFUU3vyMGfe\n5wWTznMh4F2PsM0RlXgWY5O2X+sKOgjO5sUq9QPRSvL3nmXXnjpvbvGa2tso\n/ld/idRi5aKyLyue0KX9ukVNtKnjy9Mo6manumKoy7ioB0StBCy6N0Jk1OFS\nXTnl\r\n=tM5z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "f4e4ebed357ac0e1f3df8dab413133e565523a26", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^6.0.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.24_1567711851492_0.07352392535543406", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.25": {"name": "portfinder", "version": "1.0.25", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.25", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/indexzero/node-portfinder#readme", "bugs": {"url": "https://github.com/indexzero/node-portfinder/issues"}, "dist": {"shasum": "254fd337ffba869f4b9d37edc298059cb4d35eca", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.25.tgz", "fileCount": 5, "integrity": "sha512-6ElJnHBbxVA1XSLgBp7G1FiCkQdlqGzuF7DswL5tcea+E8UpuvPU7beVAjjRwCioTS9ZluNbu+ZyRvgTsmqEBg==", "signatures": [{"sig": "MEUCIQDI2vjkFNqQiB8dTAnazwrE05Z27qLjTAdWaAEHJPO+WwIgHIy1SF6wudG32ZmFYW8VHjs6DOcAoSVy8ENzTHy+xR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpkA2CRA9TVsSAnZWagAACmUP/107q/fcQoJZZfLiqF1k\nmlQXZzy6Qz1RrgJUoA/LxhrVh38C9P1yTMemwolvXgNQ87hvbj/Xj21X4P3k\ngYRZ2Ce3nNYwImMpFem4CNz3w/+rTRwJWCdTiVLV8iYeDdeKlGKjED0gg1K9\n2Xq1rKYNjZQlkL9r3ASAU6LTOe0T+gkvCPEM+vPHiQsDhBk9Xf0xSsO8MoSY\n9tw5qTV6BIMgLLmJWKOU8gTomLvQlEV5YU1mhdDQxAFsJuZbY9qpN7Vum0AV\ngunCfNFMJ8HUDO8W4O4Ko7wonGPveEu4KftrNBRkWTNCaEwR6GUyPfLBvjJr\nw5rSinFMwhMWoxZHIDBeHYZn0VKeF+SpTB/06/Dq+c2f+OP6IJJRfj4LDiyL\nVrZDmz2rGTs/4ccD0JcFyt9oKXKxJ01lfx2UgISEBC9GRPiPo9YL0d1GGgaw\nuAeqMZjw1TXkmfp+my/5GkZoojwsjkQcL4lPgATD6CmJhgqOegOAOfaOKjxG\nzMV0YFBpmJHdBP09n87jVxqKBwdX8D2sDteyZzCJjYtLWNuyPf4UNS9ZomHq\nlLmSG1lDEcZI8u6L0MTyHohkqgAe5sK1TM4go6K2OOO2ztYkXi1DLwMMhm7J\nSof1tnBoDB93ctZ5ZlRHkb6uyKdA8cr/GIw6v1r80RmEyoAV6QngU2Ng7Q94\ndXIh\r\n=Cyn4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "e89425234f6794bad6ef0dea1e7d13b834fb3c01", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/indexzero/node-portfinder.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.25_1571176501472_0.2935766816563923", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.26": {"name": "portfinder", "version": "1.0.26", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.26", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "475658d56ca30bed72ac7f1378ed350bd1b64e70", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.26.tgz", "fileCount": 5, "integrity": "sha512-Xi7mKxJHHMI3rIUrnm/jjUgwhbYMkp/XKEcZX3aG4BrumLpq3nmoQMX+ClYnDZnZ/New7IatC1no5RX0zo1vXQ==", "signatures": [{"sig": "MEUCIQC4kBkmIv9BIAOk/wKU9aav371sX4eddCwIKrGL6RukBwIgMU3u+UTabsCxrbDsnuZ/T2cuxg2GvzuEx1ezUwZF5i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqJkBCRA9TVsSAnZWagAA/4MQAIqdFso6sAK+lOodo0d4\nyh4MEqHviwpqP14+MQdN6IK+FTtKO87+JpTIyd9ZC+lIVP8fQ3xZbwx20qtl\nLaQr0z1cw5Ax479Ml+9G8iyuYjLsQDqOJ7JPfOo0kzuThIV/mcWxHz9fqLys\nhJVFh+qgS4+pwAsLEki/lqviD2kQmKBlC4MqTkW2d/CWzimIn15UiEpOm2QX\ngxcUmApha83loURIGVLkAeNI+gN/T4Ca4yAVtLOygNjk2TjZUyaT8gUuRFWz\nEyto+LU4MLojFdxNUBqzMp+Ao92x8b2p8aEAd6DHSPel66tdJ4CsHIAAHtEk\nJPOKE6+0xH7VoqQQN2kBPCKsEil+hqN7XDs1HrLea3ETlsnmQ8Yfi6EOhvmB\n7ExaFw1PfUdqqazDa13jvcBioxOG9IL20Awyf+zqc2AlBray2QZOO4iOWk0B\nN4SM56axRq0iV69zGLhmsGOlgk+APOKTOCUQSCe3si2J7ZrayBm7QS9llpgZ\nxIZy7jP+YEr4WgFBiN+bT8ad1rhHxeZuJ9UYPiKgO7VVus2N3OezSBwJ++uc\ng6wBOxW8hpGs4ZN22fOpXpHNPI9Rc13NuIjEfB9HaDlUEv5xqANU+kVzo7kr\nb2EiwPjuF/pWtCw6l3ZQmmDrMI4ASLbHrQ7fDsXM+UIr805QdlqR+nQmn2M5\nxYN2\r\n=Pwiv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "3709e43085335c9ffa462b64a0733740b9a21d1e", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.26_1588107520962_0.8334711827775307", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.27": {"name": "portfinder", "version": "1.0.27", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.27", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "a41333c116b5e5f3d380f9745ac2f35084c4c758", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.27.tgz", "fileCount": 5, "integrity": "sha512-bJ3U3MThKnyJ9Dx1Idtm5pQmxXqw08+XOHhi/Lie8OF1OlhVaBFhsntAIhkZYjfDcCzszSr0w1yCbccThhzgxQ==", "signatures": [{"sig": "MEYCIQD3jn/noRG3zxPsLvj0fI9oXn903QjD3zjoISTaQnLtOwIhAJzLxUD6Au8ru2IiDrRM33uXOFkgvK3zmURkzP4FbgL9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFPTjCRA9TVsSAnZWagAA4y0P/1AOTG6oqT96UCJn7gn7\nbCSOZPwTUfxJCH7Of/US7OYRW0NE+xZlqRcd9e5KD2n8FiAsRIwn9PcEvDr1\nK4N5IYUfKd440wAEbKX6WvBiEu2JD3+N+xcsXdt2cqkI91RTBSFnVR97XTJZ\n5q8rI4lxL84Vn0FoGOkvSzWrLHzvSk1Pk+LW/2R1M7v8e6JW+YV+9Wxe0RfI\nU1p4qmufLTMeeB/iDKi4P84o4dHHWhtyuWlMOdFSDX45kRJevwIhiTBmWwd7\nuFRYzsEYx8EILs9a4ERJjy04e5wQPOnhdAfcwrAANshNoNqmNW2ZpP4xDtXm\nBBoCkJZpzNWh5ZLoizaeVzV86LfTyQBjck4zzOnJzYwIdxu3w1bGVbX8DNdk\nL6SNvRKpdlZep9JVS3kaYvJUJ+iZre2atWrebSN59IH8ARTwapTCCFAR7Sjn\nFdAdG9L8MSO3Mv18Z2KC/4WbDWKumTuOtF8c0fEuetvgmnl3326ypLf6S8oK\nQEsnXYHM0vTtYIhWzb/H7MycSyTSc8kdhng+raX/uyYUwp+XI0mbRs8ObvW/\n0lyKxnSkwgfUt99CpvC2XPFa/cVknEV/BsPJ1d0yp309l3IIiWLydSq09yRZ\nOqe4zE6KlAuk6H1t/bb1u0Funyi0cRnPHbvJXgqNXOGTgnfGI7dQz1V55rQZ\njSbC\r\n=JZKL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "170ac7f28cbcf2f81eff902e129baeb3f93fe81f", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.27_1595208931065_0.5102827953009685", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.28": {"name": "portfinder", "version": "1.0.28", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.28", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "67c4622852bd5374dd1dd900f779f53462fac778", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.28.tgz", "fileCount": 5, "integrity": "sha512-Se+2isanIcEqf2XMHjyUKskczxbPH7dQnlMjXX6+dybayyHvAf/TCgyMRlzf/B6QDhAEFOGes0pzRo3by4AbMA==", "signatures": [{"sig": "MEUCIQCeu3xG6xs3xQf82EnMEf1IhNw+pSQQFQpT3ipcfu27uAIgSiHLgzAgg6v+6ru8uq/Yv15DqR7Ret9yU9xsI4AP3pY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIRf/CRA9TVsSAnZWagAACrIP/3/umAyiNj7dFpIpmx8u\nbZ2ZiBRsarYLtS6OOYDmNn3BfQlLblnIIfmAAIRToNKWtKdAkXaRhn+YfIXW\nN8z5rPIY2RYqefxKgDOxBeyofKrw9LHlSB3u4BAXbET5nFKGNcGhAAVgfrWi\nuz2veBCefkKYjkjQqqWG/1mx3AuMLrfE10t9FYwoHNQuqT1HLwLe8C/mZf36\nNGUN51MOdssFF/NlLfX+nUtML1ZL18sUOMGNdpC8K7tvuLopqlofst/tmrlE\nxtKtZx6654Q2x8it72H0MThsky+3IEogJCrKA6n51e6TJT0dymZjdr9xkauq\nhshZ7jlDyGRPTrB5u7TsCnOcFkKsgzg466nC/t8BotRHAk9kD23RRscI8AOI\n35JhsipVS1wpWYdUZpHUlN77VwzHdBl8TL2pUYz1X8vtLVNZTQduVRWGDTUI\nlpzRAKrfeYlsEl/z/YWI689a1YiZgG4WKphgh1wHhVq8ZZplMzUcdlNccbWV\naNqxOhXoQLUzFST+9C5mlEHzqAtRYnltTP0V7Slj6gJ2PFaAsdjxv2f2kh3j\nZB0POe2uEuBzwEoAS+pCZx+Wz0iwaynEyaHGsOGkBAOn9zV66vZDCMc1NF4/\nlwl+FMekfioeyyB7SKakj9/jFtSCyMXBSc/bYC516CrsTfqYSqfX383V+J+2\nxeLT\r\n=496T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "8895293a96a41a454b6648b50ecc7bff7f13fc22", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.5"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "vows": "^0.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.28_1596004351259_0.8561920579977917", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.29": {"name": "portfinder", "version": "1.0.29", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.29", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "d06ff886f4ff91274ed3e25c7e6b0c68d2a0735a", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.29.tgz", "fileCount": 5, "integrity": "sha512-Z5+<PERSON><PERSON><PERSON><PERSON><PERSON>fshB9Z1pN95oLtANoY5Wn9X3JGELGyQ6VhEcBfT2t+1fGUBq7MwUant6g/mqowH+4HifByPbiQ==", "signatures": [{"sig": "MEQCIHRZwxFb7GIOBZ249KzjBuXcNHjWwQgpee+RJTX19jGdAiBa8pd+7IZCFTI4Chcnz31+c345OqJtMS0FvY+d7oA+ZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7f2PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLLQ/+ORGdPICNcC6QBeS6y00XiAUPnDsV4gH1taZ+hsEunKsxCKiw\r\nw6fov88t5ES6Vh6//2oqI+FhqeIM1Ke2vHL+hE3nv3DdxlQHMUKK2P3ME3V7\r\n1n7WdJp7K3r/t1xL0z7xOtLGmtFqNgHMOPB5hu1o2UV79YouxpDMtcOr83qF\r\nluNyOv/siA531Pyly6/Ia9OOcCjI7MM1vuh8D/sfXJB+EQcBSuFBXDrfUaLE\r\nBwhNR8mzZy5wpwxO8cGZeSiJGwUcoiXtLE1IhpSUJIf4jxhpIQXYsQ3ySJRN\r\nixwowioqwOhy6LbGW2wECSbvUkBP1T+t8xzc4gMtYlSN/VYrlu3wNhs8EjKe\r\naKNj2LCkEh8NBEiu4mg+++mizGMokZag844kikQ8eAtp9blVJzt3DpuJbOtZ\r\nUrzZKpJYBsWbdamxY8ZPsfeanRIZhn1EScY+YplXHO2dWkg+c+BivetxiSHg\r\nfrXKTCu/bpD0YzMiE072A+Et6oLm9K9mnFl7E9UrGv12uRKzM0waAaOu0a96\r\n8UQMncfsyWW1JFIhVA00Td01JBp+f5oaMdmt6+2vVwrGlIqoctkaT7nhAD60\r\nLuWVN5N/EC17+zumZfYovbwj2q35S6vFXzS6PqR7QUtpI9nbra5v+212O8wM\r\n82NGdAy9pWSFaHr3OASDr7BIXbvFIRaRZX0=\r\n=J6gq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "946f3e1a37f22c0f1a2c10d9782f1565019fb832", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "8.15.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.29_1659764111007_0.8547514883192402", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.30": {"name": "portfinder", "version": "1.0.30", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.30", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "f4b8a16d44ed822008f5585a6b47fd7f397d6a5e", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.30.tgz", "fileCount": 5, "integrity": "sha512-ppsfA0rK3vWG7gnRv9JDSmbMgIxk1a3JAtP1H9v9FYEUHD/Gmypz7MPki1KYn5+TIRHHnkPGT2+XsZRdZOFpbA==", "signatures": [{"sig": "MEUCIA1W9WY4VYWX/7k1o8k69Ltt2AFYLUlzkHOTK3nuZCfBAiEAsR4m9CpWMiBaeAamllqCOUlWzSMjQXvxvyjCNWBG0wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi98LBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpISg//Z+5ybTHETYz+YXYelZROY1D+nQOENEqF4XKEq8i9oiKUB5jg\r\nkegL+p6P8DIujFEDoUqnAT46foHBXZxYMG/rAt8hzOaj877Al4J6oVCt8B3i\r\nFOEORXU+4Z0TaOLJL/XB5WtQp6VLFMyR2Nh05sJEqT6Nq3UanlWp8/f6YBEe\r\ntCm1AQ7YkMhZH9txUgouw62wmJfKBG8cOKQCc9mvdGr2pmrBWaYnbAme7Gjn\r\ni//rxHVHcUR5op4NG6be+xT9IV5ihnVBkTvAXcNBHFzzQJIP53jpUFF2XykF\r\nn1FzUTNNCQTlu8Eqo7BY4j0HktTHwgyyW/AZ5wBDr9bEnfXN43ndkmvR7YQX\r\n6WX5VRpfu6RVRZHSLwCFWvJ0GOABR1O26+ca+0ydTTOsuT53vf74JQHxOmBa\r\n56Vtaz24Ee4omxiZBYkUr/Qts2TMkLAxFIM1XW+K2zPKWuPI4lJwEklefdir\r\nkglLOdQ+d3IhPCw+KFdYbBI7Z/mEYw5tfpKosKrScEFSUxVR67gjiJG3osNW\r\nFaBp7iotTAMc7Hqtbjk1RpnkbvHjQ75PXnuAhlXbnB4yIvCIylmazxSt61gt\r\nPWoDpDGrhojniDDPFiDcyPPjb5owpVgMOpHW2l698StXlBaMNPg5CRgx22iR\r\nJwa30MJpNTIYUb8UwT/WDY2xxx2PQqfwuYE=\r\n=0Bnr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "83686d92220cb88d16da579c0bcda88c450683a5", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "8.15.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.30_1660404416861_0.36823673248610245", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.31": {"name": "portfinder", "version": "1.0.31", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.31", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "fa6b33113bbbea2a48acd0a96721556181b93027", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.31.tgz", "fileCount": 5, "integrity": "sha512-1feEm/s8NIDwXNRV6aWfWSprxQvHkXyfYkwrvsV4d0q96AIfuVmv0UUEhH2ilFHMZb0zuDEWgDWPoj9VJ6pHOA==", "signatures": [{"sig": "MEYCIQDCXrYdHNGxm7l7C8kZx/zEjWfWlFmIDmo5+gJn3oCzHgIhAKI1kB9Daoiel2UQyVUGqhy/jj1SuhUnJMJrZSVHaEoz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi98tZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq87RAAjLNssnQSiaOe0IsFJgmsaKEvgLw0Bv+C+i8gfbkUXmPONxyz\r\nNEfCsBafzo76miLVg9Cma/ejkWqkzzMAWiAwBiL1PMDrau4z+4xRiuC2sM2B\r\ne4FPtTzkOr9riiWQYSwY6PhUnwBzIIMEAkzEcogrFsiHJM8V+h6UWL25aOLK\r\n2O0NP/QgJPKrCixTfskW52GT7FIq62Cu5Hn18Qc1hMOwlVfdbHwJCl8Q/gMs\r\nrXNZTW0q4iUb2XPrPV3dfdYcuTgVi6DJD1iVmr7PM+TIDtMnjP3CiqrSfRDx\r\ntelu5MJ84GckcZUAAGMZ3+Mx1KxZq/GipIVjdTgI1QsMQbwVhYxq8XMo0TOo\r\n2kOk4vI1civ7zk1kC3RNRd250NF7d9rJIZxkeG+ZGIbux1Mc7+XY/aSDTPfn\r\nf5Ac33UxofWaNubw3NX1pIHxZw/KwSw3Ao3SmRpr8F7UAnGyK3Omn65zAZD6\r\ntKAIhwXIVaSS+CoCasW2EvWMAvhLE2XHlEmKCl8YPm6gWNffUWk+6EwFuBw+\r\ner01nVwDU/TPokHdgcGkQeQWhEvj1MooGgD/rVjzo4MPc8NIlCm8jWT6B7+Z\r\nb+YmShe2YkO7diWZJ+CncGkkRZhA2WR+vBYy7zcaq7k6Jj0bkdjhumMGvYLc\r\nZ+0yheS0dW3WLZ+AGj6LK1qhdVgBhntMTSY=\r\n=095G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "cd6f5c427baac84672abfbf205ad6db225c24350", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "8.15.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.31_1660406616722_0.7308328163161228", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.32": {"name": "portfinder", "version": "1.0.32", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.32", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "2fe1b9e58389712429dc2bea5beb2146146c7f81", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.32.tgz", "fileCount": 5, "integrity": "sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==", "signatures": [{"sig": "MEUCIDAcXdYrZ8HCv83NBKeTKDM3qW0IPgs2l2YCN45hqArMAiEAt0aGyg2yKvJCLwIpuMqjn5ehCCKOBQ2YitlnHXyac50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi99BUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZsBAAl9ByKo1vY/e1qFlyt6JlBGX+q3+NLH5TF/bYo+ut2hE7D2I/\r\n8BC6Lh3pjYg98J6mNlhs+Io06Twy72gfQbpHJNYUgi+p1S4lgdd4IrFNXEpF\r\nDaB/a11ThwtJiZNTJPlYlPHHNSA5JfQg2hE9ayAeaD/nCoVBEuiWGhyIitFV\r\nIm0gFMFwzWDgd9NAlgp90Bk+skZxNlZdblFzYgfHqzk8JU4kJBFEq2WxtfN/\r\noVnWaP4vNS3XTXAcc5sbN2ljUNv/0CB9tVWGRhIa1Os6oVz0smT0BboXWAIk\r\n92eZaWPlRSfk8A74O1sdlgJHgtLTrhNImpz6u7wNnIIg1ti3ItQy2pf2+hM2\r\n/5UC0aEflb6Ti7mHe+igNllLgpJVDZgexeL3LuNR5LYbLgrIUV+5IWtV+UCT\r\ntyrRrdhgaQgEOe8Wh7JXrfEdF9/qg6EhS5o3jeKsWTpONjBNi4iask4MEuBF\r\nPNl2HJuvlDnOKNnB2StGL2U0DM4aHRIBYsJaDNhfBUFyZ7b233CukLV6LGQn\r\n9A+jU4L1pv3GbSFxdtJK/yF5h22a53GJm4rIDm1ni28W7dnhNz7lE2SVKlS0\r\nsyaOapgQjB52LbF9jasmz3OxbYviWTnXRPtYsmmhCMhNLqPD/CRGvj9IE4dn\r\n04PoI3yjIME8Wz19oiNqzOEO8sgg13t+gQg=\r\n=YX6B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "123a06f8028288a0f196cef4bf1346f818a8cd73", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "8.15.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.32_1660407891900_0.5060899917433515", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.0.33": {"name": "portfinder", "version": "1.0.33", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.33", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "03dbc51455aa8f83ad9fb86af8345e063bb51101", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.33.tgz", "fileCount": 5, "integrity": "sha512-+2jndHT63cL5MdQOwDm9OT2dIe11zVpjV+0GGRXdtO1wpPxv260NfVqoEXtYAi/shanmm3W4+yLduIe55ektTw==", "signatures": [{"sig": "MEUCIDTs8uauGy89pRtazn901ad/YcN1DRiEcbFTVGqozSB9AiEAtfhwhmDJzsbUW6+Bi3D8rmHT51UvSRIdXupDVi9tTCU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21555}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 0.12.0"}, "gitHead": "1d8fb461b909f4109ebe179b0a20db12bb6f4002", "scripts": {"test": "vows test/*-test.js --spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "10.9.1", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.33_1740611660548_0.030654471291004404", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.34": {"name": "portfinder", "version": "1.0.34", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.34", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "65b49b1cfa5f481208a8675ffca761d107d75fa8", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.34.tgz", "fileCount": 5, "integrity": "sha512-aTyliYB9lpGRx0iUzgbLpCz3xiCEBsPOiukSp1X8fOnHalHCKNxxpv2cSc00Cc49mpWUtlyRVFHRSaRJF8ew3g==", "signatures": [{"sig": "MEUCIB6txUDraD9HJPiTJEJozuyJir+lRnGG7s6v9kfnbKJuAiEA1eh3XXYZkpmCEupNYdHepEcuSJwJJ4OMBeCnipfdAcE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20923}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 6.0"}, "gitHead": "16dcb5e9d17f5859930ecc48587e4c7f79366471", "scripts": {"test": "jest --runInBand"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6", "mkdirp": "^0.5.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.34_1741723996036_0.9032842077590957", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.35": {"name": "portfinder", "version": "1.0.35", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.35", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "6ebaf945da4d14c55d996e907b217f73e1dc06c9", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.35.tgz", "fileCount": 5, "integrity": "sha512-73JaFg4NwYNAufDtS5FsFu/PdM49ahJrO1i44aCRsDWju1z5wuGDaqyFUQWR6aJoK2JPDWlaYYAGFNIGTSUHSw==", "signatures": [{"sig": "MEYCIQDigYpWOGE/dfFC93FSgTDPI5530iPiH3xppUY4Xo9JMQIhAMHjVrULxGS/wUmA0lkGJyRy/PKFcuhestl3jWH48hU4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21388}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 10.12"}, "gitHead": "8460965f3cb11f24a7b06b3b04c3595e2b50cc62", "scripts": {"test": "jest --runInBand"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.35_1741945232618_0.6029975473709528", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.36": {"name": "portfinder", "version": "1.0.36", "keywords": ["http", "ports", "utilities"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "portfinder@1.0.36", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-portfinder#readme", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "dist": {"shasum": "4eef523c15e972417a9ee496c3e9c95b8f649d52", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.36.tgz", "fileCount": 5, "integrity": "sha512-gMKUzCoP+feA7t45moaSx7UniU7PgGN3hA8acAB+3Qn7/js0/lJ07fYZlxt9riE9S3myyxDCyAFzSrLlta0c9g==", "signatures": [{"sig": "MEQCIDI5spXcARQA0lNK+tuN/ARKj2BmnAQFtKNGPH5SVhhvAiB2vBTiueC11vRjgwTLx1HJuezm1HjCPMzuYyAIO2O0wA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22503}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "engines": {"node": ">= 10.12"}, "gitHead": "e0081a03220003951e8981fd7e5093f712a1ddf7", "scripts": {"test": "jest --runInBand"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/http-party/node-portfinder.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "A simple tool to find an open port on the current machine", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/portfinder_1.0.36_1744646286143_0.9887359536175", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "1.0.37": {"name": "portfinder", "description": "A simple tool to find an open port on the current machine", "version": "1.0.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/http-party/node-portfinder.git"}, "keywords": ["http", "ports", "utilities"], "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "devDependencies": {"jest": "^29.7.0"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "scripts": {"test": "jest --runInBand"}, "engines": {"node": ">= 10.12"}, "license": "MIT", "_id": "portfinder@1.0.37", "gitHead": "f317e117a458f9bcc40043977133141b1d888f5e", "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "homepage": "https://github.com/http-party/node-portfinder#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==", "shasum": "92b754ef89a11801c8efe4b0e5cd845b0064c212", "tarball": "https://mirrors.cloud.tencent.com/npm/portfinder/-/portfinder-1.0.37.tgz", "fileCount": 5, "unpackedSize": 22489, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIExcs+BRw3YgCgSHeTrtH0r1K2d7o4FwACARejmxyHkAAiBzudvgD701dfpC3cmqhRST/7QFavVDuCtnS5xt88eDMA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/portfinder_1.0.37_1745867208343_0.6892121587876159"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2011-07-08T19:30:31.334Z", "modified": "2025-04-28T19:06:48.771Z", "0.1.0": "2011-07-08T19:30:31.474Z", "0.2.0": "2011-07-11T00:13:17.919Z", "0.2.1": "2011-11-20T19:30:29.396Z", "0.3.0": "2014-12-17T17:27:35.231Z", "0.4.0": "2015-02-06T04:18:37.956Z", "1.0.0": "2016-02-03T06:53:02.521Z", "1.0.1": "2016-02-10T09:08:14.800Z", "1.0.2": "2016-03-02T03:29:08.900Z", "1.0.3": "2016-03-23T19:53:14.197Z", "1.0.4": "2016-07-14T23:40:03.827Z", "1.0.5": "2016-07-16T04:21:12.961Z", "1.0.6": "2016-08-07T18:39:24.865Z", "1.0.7-0": "2016-08-16T07:01:46.083Z", "1.0.7-beta.0": "2016-08-16T07:12:06.047Z", "1.0.7": "2016-08-16T16:40:25.968Z", "1.0.8": "2016-10-17T22:15:07.692Z", "1.0.9": "2016-10-18T23:45:12.351Z", "1.0.10": "2016-11-11T00:32:02.536Z", "1.0.11": "2017-01-17T18:54:46.223Z", "1.0.12": "2017-01-17T20:12:16.928Z", "1.0.13": "2017-01-31T14:43:01.735Z", "1.0.15": "2018-08-06T15:49:01.673Z", "1.0.16": "2018-08-07T16:07:14.646Z", "1.0.17": "2018-08-20T02:19:39.819Z", "1.0.18": "2018-10-17T23:38:42.864Z", "1.0.19": "2018-10-23T02:23:06.047Z", "1.0.20": "2018-11-30T23:06:46.327Z", "1.0.21": "2019-07-12T14:25:40.405Z", "1.0.22": "2019-08-17T10:16:08.974Z", "1.0.23": "2019-08-19T20:20:21.652Z", "1.0.24": "2019-09-05T19:30:51.583Z", "1.0.25": "2019-10-15T21:55:01.612Z", "1.0.26": "2020-04-28T20:58:41.104Z", "1.0.27": "2020-07-20T01:35:31.279Z", "1.0.28": "2020-07-29T06:32:31.366Z", "1.0.29": "2022-08-06T05:35:11.212Z", "1.0.30": "2022-08-13T15:26:57.036Z", "1.0.31": "2022-08-13T16:03:36.971Z", "1.0.32": "2022-08-13T16:24:52.021Z", "1.0.33": "2025-02-26T23:14:20.751Z", "1.0.34": "2025-03-11T20:13:16.221Z", "1.0.35": "2025-03-14T09:40:32.785Z", "1.0.36": "2025-04-14T15:58:06.311Z", "1.0.37": "2025-04-28T19:06:48.576Z"}, "users": {}, "dist-tags": {"latest": "1.0.37"}, "_rev": "3307-c8a8fe4e841d1036", "_id": "portfinder", "readme": "# node-portfinder\n\n[![CI](https://github.com/http-party/node-portfinder/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/http-party/node-portfinder/actions/workflows/ci.yml)\n\n## Installation\n\nYou can install `portfinder` using a package manager like npm, yarn, or bun:\n\n``` bash\nnpm install portfinder\n```\n\n## Usage\nThe `portfinder` module has a simple interface:\n\n``` js\nconst portfinder = require('portfinder');\n\nportfinder.getPort(function (err, port) {\n  //\n  // `port` is guaranteed to be a free port\n  // in this scope.\n  //\n});\n```\n\nOr using promises:\n\n``` js\nconst portfinder = require('portfinder');\n\nportfinder.getPortPromise()\n  .then((port) => {\n    //\n    // `port` is guaranteed to be a free port\n    // in this scope.\n    //\n  })\n  .catch((err) => {\n    //\n    // Could not get a free port, `err` contains the reason.\n    //\n  });\n```\n\n### Ports search scope\n\nBy default `portfinder` will start searching from `8000` and scan until maximum port number (`65535`) is reached.\n\nYou can change this globally by setting:\n\n```js\nportfinder.setBasePort(3000);    // default: 8000\nportfinder.setHighestPort(3333); // default: 65535\n```\n\nor by passing optional options object on each invocation:\n\n```js\nportfinder.getPort({\n  port: 3000,    // minimum port\n  stopPort: 3333 // maximum port\n}, callback);\n```\n\n## Run Tests\n``` bash\nnpm test\n```\n\n#### Author: [Charlie Robbins][0]\n#### Author/Maintainer: [Erik Trom][1]\n#### License: MIT/X11\n[0]: http://nodejitsu.com\n[1]: https://github.com/eriktrom", "_attachments": {}}