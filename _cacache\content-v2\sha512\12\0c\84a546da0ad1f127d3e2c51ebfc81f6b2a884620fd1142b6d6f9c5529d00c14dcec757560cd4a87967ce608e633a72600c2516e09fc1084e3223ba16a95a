{"name": "is-interactive", "versions": {"0.1.0": {"name": "is-interactive", "version": "0.1.0", "description": "Check if stdout or stderr is interactive", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-interactive.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["interactive", "stdout", "stderr", "detect", "is", "terminal", "shell", "tty"], "devDependencies": {"@types/node": "^12.0.12", "ava": "^2.1.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "gitHead": "2db313384f144af046d58ba6e1f475ee5bfb5231", "bugs": {"url": "https://github.com/sindresorhus/is-interactive/issues"}, "homepage": "https://github.com/sindresorhus/is-interactive#readme", "_id": "is-interactive@0.1.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LbJ+XBnFVtLsfN1igC67fKOpfGfZoWdtDSLa0/tqastCoAJPGv6xqWLDuIBNL7Cx35hpItVo2dR8etfsd8rU8A==", "shasum": "5e22ca82df5f16c175d0da6da72efb7bb698e33a", "tarball": "https://mirrors.cloud.tencent.com/npm/is-interactive/-/is-interactive-0.1.0.tgz", "fileCount": 5, "unpackedSize": 4626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIMgyCRA9TVsSAnZWagAArCcP/jvD4FNjYL18mwJMG1zg\nQN43/gvYQNqTqcClkeC1ZYR/T3Edr91Mt4cwidSY0tCpm9hJAJ5afrK4cnVK\naRZWO+Yb0xa7GmpV5P370JqZVC7rLqvLzMNJ+T7V1niQhozG/LFzJHwW69xf\nhv+Yo/OJ+nZN+6y07GuG/5KN4uLn27kJyWopbJtvFwdnPjZCWr/wwW48zgmW\nAdUQtfD7BZuTrDiXc2uCArFYNF/RQS5RXJuzT1JKl9Ox0uZZIW2QvINzs8/L\nRu8+5yxfIK3SKm/rNA5rRpLflDDfyyPrg1dchZaap9j6FY8SShibfRzN0bcH\n7FUrRHp/EWzMFsHZqiZM64I4kbwQsf5MEZ4aqFy34jNT1M8NXdLP+5zINNgl\nBmO/9tp38325H8AOMk/wLIWGOI4oIA2fgW++nr2p4zzMeDkU7kLNfFc3ZOlh\npsZvIb6cAnnsH4dkWxeNnYlTGUsnp41JNSmJGN2mBeldzWfHdMEq0q8hZHBQ\nzk0zzObGrKU4+134WL2gIXTmUTI+ihBM8Be+tBw1MHAYbfJAQZqTyxws3Q7h\ntiCHE+EaYzEO4/nx4FHWGgsc0gSzilEWYEcXYssNOp/ii7MBJHFdu+g82p3l\nacAUgp6+asa+HAY+/c/OK/YGiNYr4W6j4jDRnSD0j1sWHL6j19QQQAfSi3OF\nDrxz\r\n=qoyR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID9o6whYSzzBk+8dtsqyS0mnLhPU5z4Z4wqEtbxZzUP5AiEAuD9P7oMcofk8ifdsxXzJVnR0hYV8H0R0CozjFQ2YXHI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-interactive_0.1.0_1562429490289_0.8728947227792072"}, "_hasShrinkwrap": false, "contributors": []}, "1.0.0": {"name": "is-interactive", "version": "1.0.0", "description": "Check if stdout or stderr is interactive", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-interactive.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["interactive", "stdout", "stderr", "detect", "is", "terminal", "shell", "tty"], "devDependencies": {"@types/node": "^12.0.12", "ava": "^2.1.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "gitHead": "b4b4bda46dc6a5c5b8e347b2561fd3625a927f8d", "bugs": {"url": "https://github.com/sindresorhus/is-interactive/issues"}, "homepage": "https://github.com/sindresorhus/is-interactive#readme", "_id": "is-interactive@1.0.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==", "shasum": "cea6e6ae5c870a7b0a0004070b7b587e0252912e", "tarball": "https://mirrors.cloud.tencent.com/npm/is-interactive/-/is-interactive-1.0.0.tgz", "fileCount": 5, "unpackedSize": 4619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNIVMCRA9TVsSAnZWagAAyMwP/iI9w+rD5KltF3LQOeiW\nIrj64r1JZYvFnDwtMhmmDq5FKZubUXvI0c/WqDAvlzv0qbt9rt583NZeOadb\nuU3M/3S8epBJ2/5VDw2Q9BSLu3zgztPSC/8F9rX/qRW3X2MH0PNDYPmXN7uf\ns1IMqrNT0BG0ITRoYOTYNUHZDG7E1gy7oofppIxk+XFGM9RXVkv6PQs7tgwU\nx7nRjn4j+ofXul4QbvLbrX273IAcXX39TCr/CxVxHB7XtBQkpa1bpu/bsYfH\nzA/gRLKfRessstWe1r8oMh5j9bSvDi7uQZLGXEvP+fq0+XIvHwyC9vDnvor1\n8mSADCCeM6JTkdDln6fFdfLrYEAosXWm6OH6Ilo8I2SUQ9IyGtqp2e4iPbzA\nV2Nqp0WhoKb/dbpL5glp5GelyGmdSUeAbAiNDk2a1VsovRzbDVVGOzHoclWw\nkLQTXqGq1sDCUu8JZ+0KrNHkvSnwpC9RaSPvQLXPoe5HOliffuynTcGX/rBe\ndklMvhsNq08Pp9c+jWMAjsPaCQdyE8+3WZbb6Uu4HuaSr/EjgFye5P29uy8s\nz5usDX9VHkoCHFeLF/lZVQIBUPgUqiff+GTexXYFvqKVYladdE3gLKbM++w6\nM/ixRVErfOEf8WhSm6PZ+4C7LRawOd0hanW0c0IpmD91qIAjmYtYacbG9wF/\nFKki\r\n=bg8c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5AQWi7NPX8twOB2tkrQdtz4T/+ot3lGsWORzlNx+ifAiA8EgpXWIJPpsSU08WhMgSKGN03VDw/BXrO9ZxU1xYf8A=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-interactive_1.0.0_1563723083759_0.9837731888386234"}, "_hasShrinkwrap": false, "contributors": []}, "2.0.0": {"name": "is-interactive", "version": "2.0.0", "description": "Check if stdout or stderr is interactive", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-interactive.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["interactive", "stdout", "stderr", "detect", "is", "terminal", "shell", "tty"], "devDependencies": {"@types/node": "^15.0.1", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "gitHead": "0001ea8ec644168c0f3567f355c608a346aecddd", "bugs": {"url": "https://github.com/sindresorhus/is-interactive/issues"}, "homepage": "https://github.com/sindresorhus/is-interactive#readme", "_id": "is-interactive@2.0.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==", "shasum": "40c57614593826da1100ade6059778d597f16e90", "tarball": "https://mirrors.cloud.tencent.com/npm/is-interactive/-/is-interactive-2.0.0.tgz", "fileCount": 5, "unpackedSize": 4726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkFq+CRA9TVsSAnZWagAAD8EP/0OZAmAUJOfDHY26nj2B\nELNtvd2KQ+8YTu4BNYSGhwp235+DsFqxW+BBdi2K152UIckSxeQmKzGKTcVJ\nBbZzXK2vubZRzWCX41WtIPXuDBZlxlitMN9YE8kJyJTDdEWJe5r7KlAxjSuX\nK9z+kpI6UKxoLXhQPoJjCKwvW1LABs1ZdsFNNhm2us1mn9OhKDEN/eQ/URCQ\nzEA4MR2/UQseFGY8QD0UhwXJOdinXsMa/7FCGsC8ITadWP+iJbOyuwOIDLl4\nTCqUnUOvVXp49vWqbGe08+rjERALIYwmiqTPGXbNK7KnQgJtpbNvdI4kzFMX\ngb3hw/Qtlt6RbTPGXBuXwTrQIo4PpjEX4Bx0VpDBdBwo12aLqTi8wZ+MLD7s\nCiZVc+NYq/hvtc2LOq3dkuqdQs7agpY9XVchiOzyB+z9aSi7YwDmoBgM42KG\nq8ReIGfJezoUH1D1iWUvTLCkq/hMIA82L12bxTZT7zlk531NeOR5U0Oz/XPo\n48mg2lwOIf454TcMhTk4yMyH9Ju+PJZNi7JsJuVOnAYvnXUx/IunYCdbPa+6\nUwUbG7Gg6Fbw/fqME55ENIWbjST+8qBMEyyjUnNvSfRJQvc8usWd8AoOxjw6\nKtfbQIe0r0C8QnNK9asUqG30zRoT+FVuwauu3rQKCe+CtRqCQW35UNa1A6b1\napPx\r\n=N9vL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH8olVgi3eKQlLddO+aZFjgy4A+F4nWMSkrD0JDU2zHkAiB/QdyjISp3lJBFr8ypOH82fhd93Pe7qzt3I7Ff3YD2kw=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-interactive_2.0.0_1620073149866_0.8395475468833107"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2019-07-06T16:11:30.289Z", "0.1.0": "2019-07-06T16:11:30.421Z", "modified": "2023-04-12T02:45:58.620Z", "1.0.0": "2019-07-21T15:31:23.890Z", "2.0.0": "2021-05-03T20:19:09.981Z"}, "users": {}, "dist-tags": {"latest": "2.0.0"}, "_rev": "4520-e6d2d10f32d398a7", "_id": "is-interactive", "readme": "# is-interactive\n\n> Check if stdout or stderr is [interactive](https://unix.stackexchange.com/a/43389/7678)\n\nIt checks that the stream is [TTY](https://jameshfisher.com/2017/12/09/what-is-a-tty/), not a dumb terminal, and not running in a CI.\n\nThis can be useful to decide whether to present interactive UI or animations in the terminal.\n\n## Install\n\n```\n$ npm install is-interactive\n```\n\n## Usage\n\n```js\nimport isInteractive from 'is-interactive';\n\nisInteractive();\n//=> true\n```\n\n## API\n\n### isInteractive(options?)\n\n#### options\n\nType: `object`\n\n##### stream\n\nType: [`stream.Writable`](https://nodejs.org/api/stream.html#stream_class_stream_writable)\\\nDefault: [`process.stdout`](https://nodejs.org/api/process.html#process_process_stdout)\n\nThe stream to check.\n\n## FAQ\n\n#### Why are you not using [`ci-info`](https://github.com/watson/ci-info) for the CI check?\n\nIt's silly to have to detect individual CIs. They should identify themselves with the `CI` environment variable, and most do just that. A manually maintained list of detections will easily get out of date. And if a package using `ci-info` doesn't update to the latest version all the time, they will not support certain CIs. It also creates unpredictability as you might assume a CI is not supported and then suddenly it gets supported and you didn't account for that. In addition, some of the manual detections are loose and might cause false-positives which could create hard-to-debug bugs.\n\n#### Why does this even exist? It's just a few lines.\n\nIt's not about the number of lines, but rather discoverability and documentation. A lot of people wouldn't even know they need this. Feel free to copy-paste the code if you don't want the dependency. You might also want to read [this blog post](https://blog.sindresorhus.com/small-focused-modules-9238d977a92a).\n\n## Related\n\n- [is-unicode-supported](https://github.com/sindresorhus/is-unicode-supported) - Detect whether the terminal supports Unicode\n- [supports-color](https://github.com/chalk/supports-color) - Detect whether a terminal supports color", "_attachments": {}}