"""
共享记忆管理器
为所有Agent提供统一的共享记忆实例，确保用户记忆在所有Agent间共享
"""
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.storage.sqlite import SqliteStorage

class SharedMemoryManager:
    """共享记忆管理器 - 确保所有Agent使用相同的Memory实例"""
    
    def __init__(self, 
                 memory_db_file: str = "tmp/shared_memory.db",
                 storage_db_file: str = "tmp/shared_storage.db"):
        self.memory_db_file = memory_db_file
        self.storage_db_file = storage_db_file
        
        # 创建全局共享的Memory实例
        self._shared_memory = None
        self._agent_storages = {}  # 每个Agent仍有独立的storage
        
    def get_shared_memory(self) -> Memory:
        """获取全局共享的Memory实例"""
        if self._shared_memory is None:
            memory_db = SqliteMemoryDb(
                table_name="shared_user_memories",  # 所有Agent共享同一个记忆表
                db_file=self.memory_db_file
            )
            self._shared_memory = Memory(db=memory_db)
        
        return self._shared_memory
    
    def get_agent_storage(self, agent_id: str) -> SqliteStorage:
        """为指定Agent获取独立的存储实例"""
        if agent_id not in self._agent_storages:
            self._agent_storages[agent_id] = SqliteStorage(
                db_file=self.storage_db_file,
                table_name=f"sessions_{agent_id}"
            )
        
        return self._agent_storages[agent_id]
    
    def clear_shared_memory(self):
        """清除共享记忆（用于测试）"""
        if self._shared_memory:
            self._shared_memory.clear()

# 全局共享记忆管理器实例
shared_memory_manager = SharedMemoryManager()