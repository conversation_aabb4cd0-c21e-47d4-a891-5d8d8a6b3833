{"name": "babel-helper-explode-assignable-expression", "dist-tags": {"latest": "6.24.1", "next": "7.0.0-beta.3"}, "versions": {"6.0.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.0.0", "description": "## Usage", "dist": {"shasum": "b6027ef990e8d709ff3647c57d91bdec7ddb6d4a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.0.0.tgz", "integrity": "sha512-269lABHh2UMC7/Dl7O0+n7X733o0YH6E51Ct0Jbxh6NZyFfv0s8CVeuS2SCZTaXYDUeGZkiE6cUkWMm4j/hNpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaJfQKZ7kZBpuKi76+XrtP9UoD2VKjPMpL6Opt8JdRBAIhAMJtfWpAq0QiXLWZZ0UnSDwKz2ax2ZsIbpgv4Q1socSL"}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.0.0", "babel-runtime": "^6.0.0", "babel-types": "^6.0.0"}, "hasInstallScript": false}, "6.0.2": {"name": "babel-helper-explode-assignable-expression", "version": "6.0.2", "description": "## Usage", "dist": {"shasum": "3cb6c95a37be3f0b7c567f8aecfb62f209e1bfca", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.0.2.tgz", "integrity": "sha512-+x13A7k6qrKrZoEhFpZVOCabbUEvjSDQw6sUXEZYRO3sdyhZhF/AOlNvILeVotPseqB/zAxubOJtvd5TEyzz5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDS2YYZLa+xaUR/zNU+4UCboBzeIt6ZkluJn3awrdfawAiEAxqA8hT0X4KkZs+DUXDPRo9h0XWXqT35/ONK3jY1SL+Y="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.0.2", "babel-runtime": "^6.0.2", "babel-types": "^6.0.2"}, "hasInstallScript": false}, "6.0.14": {"name": "babel-helper-explode-assignable-expression", "version": "6.0.14", "description": "## Usage", "dist": {"shasum": "720f31ff9855a1d013255be4f7cd1c43bd3c95ad", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.0.14.tgz", "integrity": "sha512-6zS5mkHK1rHBNgQzUlkgj/+K52zfWftFFPQQgcTJy2wTrlVS4D0dib0Q3Azccy0zdO5sJc6sVpWuqjfhMRo2nA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKpEXoVbM9mFkTJ7vQbjfe8imJAIuvaiYO6f1lml7eIAIgFp6x5Mq+TpcABnWSkOl8e6FwYvYEwmVEPabn6N+HRXM="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.14"}, "hasInstallScript": false}, "6.0.15": {"name": "babel-helper-explode-assignable-expression", "version": "6.0.15", "description": "## Usage", "dist": {"shasum": "9f046e268d08c264a24a96024947734fcbc1fed7", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.0.15.tgz", "integrity": "sha512-+NNwZaRxuWDN7f9kIDC5UteTjb+15BfHVO4H0RjHAQ4pyqOMURHOJYcX6j88dbJgsdT6B0T38TDk+JWr6S9+tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC41fLRprnkEKxxfg5P3TvKVAiqurePdb64WTsd+A+d+QIgKyo+2GIbqf+ox0s+RoUWt6g7UuuDmhgQ/v2m0avCPc8="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.0.14", "babel-runtime": "^5.0.0", "babel-types": "^6.0.15"}, "hasInstallScript": false}, "6.1.5": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.5", "description": "## Usage", "dist": {"shasum": "0d51891932dcd724b87d5142f785535d533d93bd", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.5.tgz", "integrity": "sha512-PBxaIlaqWo3tH2uuz1rNXgIrc3wUQG40o8s8eFeJUA56w7Md4wzOQ1ASVPClhFwrBuHJdivb1qnNU8Sa9j/scQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7bZ0BJ1XWfVEtIw7uEAdkhw+UmBNEGOx3zDwsBj7U8AIhAJvO2kwNoQLKc9TVbdwnFr4fMx4KWX9Zw701RgmFPSx0"}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.5", "babel-runtime": "^5.0.0", "babel-types": "^6.1.5"}, "hasInstallScript": false}, "6.1.6": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.6", "description": "## Usage", "dist": {"shasum": "b46c08feb33f9b424252a7a23a14c8628649faac", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.6.tgz", "integrity": "sha512-CowVfPhL3wplzKpMEuj2ljgTa5JgsCMX05kmzcNGy2TtpKEIC2oLq6exl/DgS5NDQzDm9JrY8sxHCXhHt6/Law==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBA6DOWmvcfOvhtjmHJcd6e2vioPi7cXNkOh5CIohbs+AiApFnoLysbTTQ7JAHEfJ5dh58/tPOIEAzG0xtgMOACDvQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.6", "babel-runtime": "^5.0.0", "babel-types": "^6.1.6"}, "hasInstallScript": false}, "6.1.7": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.7", "description": "## Usage", "dist": {"shasum": "d33a2f47f3d1c54826d7991daf8127e7cde292e3", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.7.tgz", "integrity": "sha512-F3rYHXQ2ePmWPrGuUlJXFdg/nKsUqfy7rmnNrM7OZ28pMwe5UmhDPT67JRB1wdQRI/w6BMhEgt0c/ROHNXB6FQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH5WzVmV7xbFIdfBq1nIRY+k9YIeBQjtGJhvCpH9x8YWAiBruIdFJaoVJa1oAgbwvkUiTkOFCMMSZdOHYeaBBr4lQQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.7", "babel-runtime": "^5.0.0", "babel-types": "^6.1.7"}, "hasInstallScript": false}, "6.1.8": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.8", "description": "## Usage", "dist": {"shasum": "6bd62178ad4aaf60a83838761c5f1101192d3f6e", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.8.tgz", "integrity": "sha512-8d7T5+fHIWIEtsfFC4mS9E/N51FsV9AUyqE5cF8ldt5BXawhHGiLeoQyspiT3Pm6ErlItXe6Y7EqZaBoe9nJgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCiX6AT18KXc6VDeiz8TdCnMy+ZE8G2IvwSv2FLvGj8wIgH+dLA8uF0O1wvCRljJ4E21pQL5c7bx+790Ng/fRka4I="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.8", "babel-runtime": "^5.0.0", "babel-types": "^6.1.8"}, "hasInstallScript": false}, "6.1.9": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.9", "description": "## Usage", "dist": {"shasum": "9bb4a51bc343d683cff44ce92c0d5b85ea582334", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.9.tgz", "integrity": "sha512-ziMedfojIxcthhFqu0mEFuCyEmF1bEgXWbTBXMjdh+QEpjSXI5AGB/pfR2gNqIkeGS9+MEvY20rkt/SHOUv4GA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgcTuBUlUaBeQS79JuJtiI9/Brlyd1qoZ4gtJOkPvRxgIgVJ/+OD7YfN/FG2XzJnNjSLFnZiPjK4wmUiiKFdeOjTk="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.9", "babel-runtime": "^5.0.0", "babel-types": "^6.1.9"}, "hasInstallScript": false}, "6.1.10": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.10", "description": "## Usage", "dist": {"shasum": "6aa7287028945f9fb7214dd9ed3083cd94144264", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.10.tgz", "integrity": "sha512-/QXnJnHVIgxeTPCNviB6FyWyUAUdB3PNIadZLtdf5e4QfWJCrxGoGT4nmkhs37EF+3lKfv7Rrr0egw4CY5Z3XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNCGXphloUv38GV9gRcICwFk/HymTr7kAgKg/Bos3xEAIgM0dYfEVYk/gA66aaPKa4UA1dQD7NcUypBQCl3I7a7QY="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.10", "babel-runtime": "^5.0.0", "babel-types": "^6.1.10"}, "hasInstallScript": false}, "6.1.11": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.11", "description": "## Usage", "dist": {"shasum": "e0355b3f1e316694cde646210492a73064b325bb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.11.tgz", "integrity": "sha512-3OUImpS7UJHrTBY8W5i/O1b13SGuGGctnOxKluTTRYyc+Dwxx+3WmDv+7uRgfXiHOVAS38yz6KTDzaavjVpZmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2Dixl5MGQ5GWRTfU7KG5A+AveHhzzkUlGqL9Vc+CMnAiAedjH5RSfai49fYEjchTU5jCQRglVeDjBikY35TRN7Rg=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.11", "babel-runtime": "^5.0.0", "babel-types": "^6.1.11"}, "hasInstallScript": false}, "6.1.12": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.12", "description": "## Usage", "dist": {"shasum": "76943d252ba5c6c9249e1d7a7fcad22897dd5ac6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.12.tgz", "integrity": "sha512-ZqORW/zJYyVeIIL1tIb/UJH6SXi3TnSVevBsWrNq5eSJiMDjlHLne6wlVsy58tG8JsS4kSj1tL7VhK0RlBrNEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7JxloCcr7yCimn+7/0CmNdq+xKVnqNfP+qQzGmd/zqAiEAikuE8cnpPm0ShJM0qDe8hHCC3rnJZW3FVDQ8cc8SGjY="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.12", "babel-runtime": "^5.0.0", "babel-types": "^6.1.12"}, "hasInstallScript": false}, "6.1.13": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.13", "description": "## Usage", "dist": {"shasum": "a6e22a94b3e2fccb5d171cb4639bba2235a9a81b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.13.tgz", "integrity": "sha512-1w/w2wspSHFksVBesR2GoD60FeuKfJRzm375hmw9hrtd2ybz23lW+4OqxaONvJBQHMBj7oF/heBfCwPi6PD7vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhnHb0sqUf2baqgjJ7OkMho30poPX2xFapZgj1Yp4GHAiAO4Wt2PGzvKnbrpWryVWQV+++qYpxLHOvHL3WI5J/9gg=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.13", "babel-runtime": "^5.0.0", "babel-types": "^6.1.13"}, "hasInstallScript": false}, "6.1.16": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.16", "description": "## Usage", "dist": {"shasum": "5dc3d530fabd28517dc5869798f081c031f8fbfb", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.16.tgz", "integrity": "sha512-5W3RWLADwM/hSXNZJia5AfhXAKX/3bLXJuyw8LjBPDKhOqbJLVddSpdZ0vgbIf/HFbRLsWc0IEBDWc5fV9ECJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjOvWAgEE6OxC2o5DSFBotYLWIjWYyBBbnhNE1o+y0WAiA+CVkvjiqkVziH37+OFd4U+J1grFLnUj3VueXI1bB+zQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.16", "babel-runtime": "^5.0.0", "babel-types": "^6.1.16"}, "hasInstallScript": false}, "6.1.17": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.17", "description": "## Usage", "dist": {"shasum": "10b3a74cac0e9cc06211ff87db0baff522163295", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.17.tgz", "integrity": "sha512-WRQv+xAJLt20Zdy+K8hy0otfC/Wcx5AqCyT4K5GPRCOOZ0WtIwp3N59wS3VXRI9gC325SYyjb8bXnXl8ADkbgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDrhpe2yHKYg1PrcEE6MkKkga8w9P5hR9s53yoj1B3owAiB0dw5CO271LDd6HY1qoQxlwXQRbcwYC7vKYYgO7AuQrA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.17", "babel-runtime": "^5.0.0", "babel-types": "^6.1.17"}, "hasInstallScript": false}, "6.1.18": {"name": "babel-helper-explode-assignable-expression", "version": "6.1.18", "description": "## Usage", "dist": {"shasum": "a8e20cc23c564edd0649c8657015be468b959d33", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.1.18.tgz", "integrity": "sha512-ah2ZFmFh8zUTHBigFKmVa8WLY7z3Pn75EvHKxIsmEmOarcZpna8IcHz8TLe7IC1CZPSt0A9Q4wd5bro/p7nQPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBmd5FIgkO0r8+mB8uRIQ+1IsQeFSc1HA3D9QDLvoh8hAiEA593/QNgBPk7cpLK86396zjyDt1XTRzj585BJtqyEGMY="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.1.18", "babel-runtime": "^5.0.0", "babel-types": "^6.1.18"}, "hasInstallScript": false}, "6.2.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.2.0", "description": "## Usage", "dist": {"shasum": "e1167e6d61f62cfcfc1a1d8be5797c84426f579f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.2.0.tgz", "integrity": "sha512-FNKSqzLcztPNhTzVsDvPU8jQO8F7voOI02mi6n35XwZlE5uhRLuoxUfKnjtYELhY9HYD8NgSGGP6yDsfH7mhJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCbvQCAtBGdmcbK0cBG2yKgiFoUUkFCYyDX4d3hIqf2AiEA4GfXn9cC0E+b6kdw0veDKKBePM9SjS9MYFynjeUXQRc="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.2.0", "babel-runtime": "^5.0.0", "babel-types": "^6.2.0"}, "hasInstallScript": false}, "6.2.4": {"name": "babel-helper-explode-assignable-expression", "version": "6.2.4", "description": "## Usage", "dist": {"shasum": "21615489294587a254fae41451359867d547e26b", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.2.4.tgz", "integrity": "sha512-luaVUWAFrQmJoJqziqtlMQA8cRJ4QZiNnHG2nsN8o75/Bwlbo6nsCgcj9nJXNdbnP2hRWsXFV8vr6mI2Ggfhjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHg0VEUpoxwn2ZmGYq2s77w92KsQ7kvAYbM6CpmzFPPnAiBsEo5KDEnhUjYQL6gcLzBXVvG0OxE4r36C8DIhZIn5gw=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.2.4", "babel-runtime": "^5.0.0", "babel-types": "^6.2.4"}, "hasInstallScript": false}, "6.3.13": {"name": "babel-helper-explode-assignable-expression", "version": "6.3.13", "description": "## Usage", "dist": {"shasum": "521d50aff320cc4d89ecd22fd48751d9ffd3f9a2", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.3.13.tgz", "integrity": "sha512-W0JblQlXV+XAKbBLbcyhJK2pvOK4JxZKpNVEtg+6wdF0oaryKk6LVWq3NQkIOjOLu73WTxTCQvpAQZHBeeac7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPdyFiL/nqjHeQmZh7A4p9o5W+2jlYe8dkMj6+pDCB3AiA3a1/+pTjfOxae8KXr/PmHFpG2xtX5VEw9ipUeWrMcmA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.5.0", "description": "## Usage", "dist": {"shasum": "d0261b2c22d29f94af2c0a76308412820ffef622", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.5.0.tgz", "integrity": "sha512-4l+me0GUBFnDSrZSLE6TjYlK0PFmRXkjqACAwpLnha4SDgxqpZeD2I4nCG36ZWtJhMeyrcLAHF+KUpBUtggf5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD3r+UyM0oQR9lJxxuekt5U6TjgRe6ZMJHf1MKwX4WvQIgGWTTYuJs2CoBGI4oXv4pa9HOkbyodVSL+rsIo3wAStE="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.3.13", "babel-runtime": "^5.0.0", "babel-types": "^6.3.13"}, "hasInstallScript": false}, "6.5.0-1": {"name": "babel-helper-explode-assignable-expression", "version": "6.5.0-1", "description": "## Usage", "dist": {"shasum": "03a45ff8af1a99472c3a8271d8907652c757ae67", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.5.0-1.tgz", "integrity": "sha512-iygFbaLITbpPvkb1CeFjXPZJDFZWHZzUtykY1BgZLsMcub/hKxHJOyoYcJ5bVfOIOoxZobnLWffqMay2Pm/Pjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICCRXPWO/0yObJhYJx2Yem7TLIKNVJ+gMWe0n2ZtGL/WAiEA2RdvLcyMpMeZ22vUuItyYk8PVbdjVPdcY3sZKN5OPok="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.5.0-1", "babel-runtime": "^5.0.0", "babel-types": "^6.5.0-1"}, "hasInstallScript": false}, "6.6.4": {"name": "babel-helper-explode-assignable-expression", "version": "6.6.4", "description": "## Usage", "dist": {"shasum": "fcc52e3073e03fa2fbab4be7b19cea52bd88fef6", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.6.4.tgz", "integrity": "sha512-zpQf38ULb5waRZAzNS3VKTw4mjIzEJogqMQhgFAfU4IpIirHLqYPF7KwKmKQy5ULEdpwO2xRAqTW6RynTqmwtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpFarzusebyvmvaFUCPBtmhJoqDj/S+yayVfi3ztGUlAiAavTBOzaxMmSBLLneZK+J8VNEOYaZGyQqBG0nQECe4/g=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.6.4", "babel-runtime": "^5.0.0", "babel-types": "^6.6.4"}, "hasInstallScript": false}, "6.6.5": {"name": "babel-helper-explode-assignable-expression", "version": "6.6.5", "description": "## Usage", "dist": {"shasum": "bf84547f5fbd4429120162ef4ab6b781fbfd51a0", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.6.5.tgz", "integrity": "sha512-Df7Y0LVO3Q2sun+0k6NVGliXnoeyFARi77CWnEIGA3XiBQ6F7jby2Dr9kGOEepJUSN6q/hU4Ub+/SsVqNeFp5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDw5Ispw5NKZklTnv1PbKhYdlPsWbBYtAfRj3cjrrqvTwIhAPTbIrRJ/O+mc9v7UT7yd2QGV9gurlLchzW02OGxGjz/"}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.6.5", "babel-runtime": "^5.0.0", "babel-types": "^6.6.5"}, "hasInstallScript": false}, "6.8.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.8.0", "description": "## Usage", "dist": {"shasum": "9b3525e05b761c3b88919d730a28bad1967e6556", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.8.0.tgz", "integrity": "sha512-jHYezIRkrFzSTFOb+njO74NwfV9P79l1ZYvB+pbiKjhd8nqOH1vXCITL/uGRZH3AhpQ6BAiQUsb1c7woeKSwjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMYOZWXYeKGhRkpFJ2bIHgVovwSZiQ+ubvPcwWFlSNEwIhAPovR0L6ykaX5erfCl1UYmYXxDl/GJcSoP7UBjhUkhr7"}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.8.0", "babel-runtime": "^6.0.0", "babel-types": "^6.8.0"}, "hasInstallScript": false}, "6.18.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.18.0", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "14b8e8c2d03ad735d4b20f1840b24cd1f65239fe", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.18.0.tgz", "integrity": "sha512-Ts03TnekHoflo72voJx+epk3rZdFEdkilhRKw1N+gJNF+b/dF72MlItf3BqyjJ9dn3fcygtHG1+YMfmxUBvGtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqd3ildYu+M6RK5ncrqMpTvgtYcgkYhk+6mGTutBDubwIgTtaXcqOfySfTFR5U/0jg/icbKf0S5eDBpTS/ZxsCBuI="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.18.0", "babel-runtime": "^6.0.0", "babel-types": "^6.18.0"}, "hasInstallScript": false}, "6.22.0": {"name": "babel-helper-explode-assignable-expression", "version": "6.22.0", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "c97bf76eed3e0bae4048121f2b9dae1a4e7d0478", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.22.0.tgz", "integrity": "sha512-lVMNHRFaRwEfDacIJDrUcn+7LSK+OZx5jKQTnVqEe1eFII1c3bOEAXmUfwzTrrKIqXLax8Z6Sl4qRVemokBYLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWBiZmfZNW8HozE0hS0avBnIUnVvAGSRv3NeOcspzr8QIgB5k4EOSMwARNQ7sfys6cKuI1DLAYAmSeyNApyG/CZtA="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.22.0", "babel-runtime": "^6.22.0", "babel-types": "^6.22.0"}, "hasInstallScript": false}, "7.0.0-alpha.1": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.1", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "7ee6ed93afdf734ae223f5d17bda505754522849", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.1.tgz", "integrity": "sha512-1HHUDBm3TpwV/Pj3CzkxZZjCZSZIBaiifkGYa88sEGS4fD3jsW/i3XPNtMmJhsb9GDBMtAKti/c51Pxeoq8A7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEo128tDsNjH7FJLjl0DJPTqyLok0QMuxbStYqskxK6JAiEApSQXzN35usEyS/3OM6tj1u8VdMqYeICdvjk2AiPQUpw="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.1", "babel-types": "7.0.0-alpha.1"}, "hasInstallScript": false}, "7.0.0-alpha.3": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.3", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "ab9bfe22c0409a57d1a3b0bce3bb409a9e8d86e9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.3.tgz", "integrity": "sha512-k/QqyYQpNNQE3b9s9f3+HuBP0kdTSUFkgvEKGCDHgQkN+OXvCc2hHz3QGy2zuabaKBHe2CzDCd6eUCf1OetgGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE8IOamcMYwLtYkmt/sRu+IcJkbCF16rVOv6bwrhpe52AiAjyS20rGOel7032hpE1U7AtuHYOf69zAG5ecl2psbq4Q=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.3", "babel-types": "7.0.0-alpha.3"}, "hasInstallScript": false}, "7.0.0-alpha.7": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.7", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "114b676437f4cb694eeb51afcd4cc9b0bf926c5c", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.7.tgz", "integrity": "sha512-baf0QIcG2fkdc5GJTYwr8uCPWLsgFm2s1LZnOnHI/ODbbkVwTC0bXNWnrOygSCuZnCYkKAqHOpy6JuFYbzhOnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTPyPB3fr1rzK+zWclMzUDA0cqQMBnXZAHc9ceUFrbxgIhAJB5KYCtKGKSOo2oNUhAJu07r/t7GfoUeOHFC7nBX9sk"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.7", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "6.24.1": {"name": "babel-helper-explode-assignable-expression", "version": "6.24.1", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "f25b82cf7dc10433c55f70592d5746400ac22caa", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.24.1.tgz", "integrity": "sha512-qe5csbhbvq6ccry9G7tkXbzNtcDiH4r51rrPUbwwoTzZ18AqxWYRZT6AOmxrpxKnQBW0pYlBI/8vh73Z//78nQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEjSkzQoeWP4FpTrSl5Ja0yJU57GY5hT8jbCGkTlMtcdAiARojVQ+/sDLofjLJS3PCG8nDwq/7SqGf4SlXrfxkix6g=="}]}, "directories": {}, "dependencies": {"babel-traverse": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}, "hasInstallScript": false}, "7.0.0-alpha.8": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.8", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "ee2e88e0c44fd68961a8df197ccedc39f6e0df3a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.8.tgz", "integrity": "sha512-1MZDRepMcQ07XDCQeUHKLfVkaiLte+wMwBWM7w29FI+DhdhUHm50fsRvMLgf1rXWALoclwILK/uMsYgdBJc7gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCByP50cHtPfKMFcKszs0s1b5IGxClUcN0A3NIryYPrrgIhAIiJJpOGZxtUBHXNxQI0BJW/+iLhWel4zUp9Q9Exm5OS"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.8", "babel-types": "7.0.0-alpha.7"}, "hasInstallScript": false}, "7.0.0-alpha.9": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.9", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "c2d827afd0686752b2c5eedddb89251d3ccf9e50", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.9.tgz", "integrity": "sha512-9czg19iWMjmRdi97r/wAXSCsov2L5ZGx9TO9o2Fax2u3ER2G9a0do1tnR+ZVObMHdXeoV1a9hiFw5gXXW+EcHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeGo3sPX1p/FSwkqPufKT2m2EMIKPnfz8kE+C12gbYTgIgZSUmVhiUCAInFWks+aL583MGkKlaug267G3looQ9hkY="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.9", "babel-types": "7.0.0-alpha.9"}, "hasInstallScript": false}, "7.0.0-alpha.10": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.10", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "4238e22579999e473633a5fd3c9786f0d1838e19", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.10.tgz", "integrity": "sha512-ElA8NIxMh0zMvStHnY53FgStY5Gu3hXNI+1z3WV6k9xe0rzGlABmn6WWJ4IKU2aKJXJjYUVZmHR+bp/TDKsnJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQ28SzivUuJ/6v9DGCW5xzLQTiRtnvHiHOAGl71M3+GAIhAOL7QKhgAfvj8Z8/XcHvE1hgyhD2ZMdKJX4q5Jjbpnq5"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.10", "babel-types": "7.0.0-alpha.10"}, "hasInstallScript": false}, "7.0.0-alpha.11": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.11", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-uiLj8KCnmRBIS+MSrxMbtrWazu5kZ9R89SO+uI48LS/osFuPMiIyeeYDndZS/WKbYw3119zu+h4tCM02pvwq9Q==", "shasum": "9b4df46b0a794d5fa0478f1710b9c8620f8464ef", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAg2bhUkAdwmXSGDE7AS58gLS85xGDJocXobojlxOmBzAiEAnCXHUITcMsCf7r6LHNmYG5VqES6jhvJZTxvJoVPvcZI="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.11", "babel-types": "7.0.0-alpha.11"}, "hasInstallScript": false}, "7.0.0-alpha.12": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.12", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-G5Gjhfw2Iu4OrQ95h8cCemwEkqMHJCCx70DwDIoa5G0Qs8sxViCtmQ33HdfBTMtbv9GQlQQMPY8UWilNa5ZtSA==", "shasum": "c745b10b516e64c6aeed999d92f0e6ce8fa661e4", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxGabt3qg/1pKxFUFOxb2uEJtrptZtJ9ySjhxIIBCzpAIhAIsY3+oeBbWkY/Tr8VlR5texdJ/oecjmqZLYa8xqeB9K"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.12", "babel-types": "7.0.0-alpha.12"}, "hasInstallScript": false}, "7.0.0-alpha.14": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.14", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "8d6153cb834a33107ef544abaa3a6d2748279715", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.14.tgz", "integrity": "sha512-L3/Uigd/OM7ODgKUKoto54r/xItLvHsAB/eOymX3QWzCEKzzT/oEAcpCjBRU9D+zTvv8DVnt5C2oZjrECOUYrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHE8sgjhf3Dg0RnCtvCvCj30FhD8Lkn1CoauahcS59SQAiAZWBObohN8aaFTZ1OOx9J+P2vqY5pN3JNICb62np2dCQ=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.14", "babel-types": "7.0.0-alpha.14"}, "hasInstallScript": false}, "7.0.0-alpha.15": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.15", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "7bfe1b00a89b9af16e17a021e5ea4ef9bb55e84f", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.15.tgz", "integrity": "sha512-cyog3MPHEUlQDwogNAYwljxP/MwsRzqAk/9Q5Vpz5Cpesr9HVjdkdhVwLKpMuLZK85feQUVxSSsmjmEvR7D8ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDjRTf5EjqAtUD+GtV+52P+r4gpfx/arHlMgaje6a5+FAiEA1WGq5mw+VIcIOjk16lYzXQEZJvPzElqZsP75dfOFINU="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.15", "babel-types": "7.0.0-alpha.15"}, "hasInstallScript": false}, "7.0.0-alpha.16": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.16", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "fc1a31592a25a57773f9a84a75014249d8ca752a", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.16.tgz", "integrity": "sha512-QCggGjnClQf09UBHGWtI3KjWmlmYnAmjS7WGBTLKLDZ8sNC3E8TldGstCyq20q+HryYWzzrp4ea44npbkOrl7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxzLYLay5L0svHBxZzJw4nsEOVvuCXgBjXShUp6mBQfwIgRt4WqtDVkBMdckq7yHXCDVemayUyaM6J6sE8iwAj2l4="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.16", "babel-types": "7.0.0-alpha.16"}, "hasInstallScript": false}, "7.0.0-alpha.17": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.17", "description": "Helper function to explode an assignable expression", "dist": {"shasum": "e7438ca538a767a5676c8b4d93c8dc6051a7c4fc", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.17.tgz", "integrity": "sha512-T3JD+yf/s5n1016Zj0EKzZgdw/2QzAtf/bib6wOd13iAbxg9a6vqdmQidVfps3tvBtW2CiZyspkvDrOvYRCXLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS4qMMrY16Pr/UPEimP/vCvZ3PniOMjYwkVzyGBO3wpwIhAMYFBbyf8cpLB3G0BoMVzDWbGYZd50nyvCecCu8pI2ms"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.17", "babel-types": "7.0.0-alpha.17"}, "hasInstallScript": false}, "7.0.0-alpha.18": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.18", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-DzYIH6fEkvi91G0wEKX2Qo5tvjUNf/RQzz/jAqYjzy7OuBog8Z8ZM2EONmLLmep+0Nx01YVE3C8edhsLEG5wVQ==", "shasum": "7aa684719b9a4d9eb3cb000af810f3b8ce9f4de9", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEtTnpuaswUxQb5IvIzYbMCQ5J6+EeXY7IuYlWEVnafgIhAO68J7gXKZXFCVKMspypMG+TaBI0JiTBuW8elWrpZI/U"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.18", "babel-types": "7.0.0-alpha.18"}, "hasInstallScript": false}, "7.0.0-alpha.19": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.19", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-cZ+QtUivFrjS57zrlnmnA8vYs03l5gYXIhy3NWit1rYKmjJGOsQ3VLEulGRtdicgcEQtU0WWk2uVUnyT1HAVmg==", "shasum": "45b639cd0b05a47a0a4c6b22c8925b7a6b7bfec5", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJkzti9jN5ATUXwQk/4VfNkB3TPTrhIb5DB+D5Css2XAIhAO8xMEupxo8YEQrQVAnlsSMS/aRsSS8a0Csu8CX3PMCw"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.19", "babel-types": "7.0.0-alpha.19"}, "hasInstallScript": false}, "7.0.0-alpha.20": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-alpha.20", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-xik9Jg24rkyQK7MffjJ1irh7HTXtfZXmWmsS0f9Vlv12TnnKm54SCiCst7s2ZD0gDgq7oyaHHnz/O4ggSmQ7Vw==", "shasum": "31826b2d3986f105e691ae538c49322a97754050", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-alpha.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDouKMSaNpOEWqrB+GsiA8vLxxitzx7dbYIOZQXopGw1QIhAPLQAXFcoFhQkyz1HoaUBb90J+UbPcLNtCBnm1AnzvZg"}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-alpha.20", "babel-types": "7.0.0-alpha.20"}, "hasInstallScript": false}, "7.0.0-beta.0": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-beta.0", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-gAc+WIqHVadzKgCDSdx0ovsxWC7JDs2lJwJcI0DI2L0Ofj0K+44I90i8/e8Fdao/8cQBQAGaWp0Hg2g5wY4OCA==", "shasum": "b8f878c7c9818b755fd0869c870189411db28694", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-beta.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6c46RaF2rHEbpMwf8gFjnxQyB/+xRYStzajcwRXTPNAiB3flr7Zghd5O5Z7Z5vmalz7EYP5TvkfSVKgQBdnlMcdA=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}, "hasInstallScript": false}, "7.0.0-beta.1": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-beta.1", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-h2WDlC0po307m9dnltC3ND9fYeC7DuV1pNmNMLrYbrAaNl1Rc1wkdrJ1HST/3sHbw1lqXqNKq2I2INkrq1nELQ==", "shasum": "706159a66a77c866cc1b454a49af7f8f6e9361db", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVgsw4DqVeVCEexvicfngvcB6WmdmjP3LFA7eGVn6/KwIgZN8zKOdcN3DQHXeGZqRopbJ1K3fxjh8OAYa8dzrE+pI="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.1", "babel-types": "7.0.0-beta.1"}, "hasInstallScript": false}, "7.0.0-beta.2": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-beta.2", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-tQR6WruyBQ351wBXwzpI137odL8H6nxweTnlolEEp2dkkEYlzUpiofr/nkaXNACkLEMQGDPosYsYIOJm8BzkBw==", "shasum": "e14263dc5cfeb32aa99bfd60da283722e2955f0d", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTihm6cxpFllCcT7L8SrV84ZJDlba4hw+6stzyDz5eFAiEAmBkWz3hrCTDFbWXSwYMPEc0XJytIKxofssf4Lh7JnDg="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.2", "babel-types": "7.0.0-beta.2"}, "hasInstallScript": false}, "7.0.0-beta.3": {"name": "babel-helper-explode-assignable-expression", "version": "7.0.0-beta.3", "description": "Helper function to explode an assignable expression", "dist": {"integrity": "sha512-MugrfM38GSj7+8I/yFYjQwOEPSGmCLsJFNrWz7KVH4HA5Ntim19cMmdMUWSls30rTeGYQpLECjHNFv+ITbA7wA==", "shasum": "de59f895219de658ef64c85c7b1b0223ca74c1ba", "tarball": "https://mirrors.cloud.tencent.com/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-7.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGZ23e6Vy3HV8OsalgrUFMF1Ti0qcEaI/W7Khwtiy2aQAiByAUsR2EAZGEJUiEWqLGaNFZYTyD/xEWLTnzY3gpyC7g=="}]}, "directories": {}, "dependencies": {"babel-traverse": "7.0.0-beta.3", "babel-types": "7.0.0-beta.3"}, "hasInstallScript": false}}, "modified": "2022-06-13T03:59:59.982Z", "time": {"modified": "2022-06-13T03:59:59.982Z", "created": "2015-10-29T17:54:28.588Z", "6.0.0": "2015-10-29T17:54:28.588Z", "6.0.2": "2015-10-29T18:08:47.855Z", "6.0.14": "2015-10-30T23:32:41.507Z", "6.0.15": "2015-11-01T22:08:38.053Z", "6.1.5": "2015-11-12T06:50:14.961Z", "6.1.6": "2015-11-12T07:33:45.437Z", "6.1.7": "2015-11-12T07:38:08.541Z", "6.1.8": "2015-11-12T07:41:11.291Z", "6.1.9": "2015-11-12T07:46:55.778Z", "6.1.10": "2015-11-12T07:53:36.253Z", "6.1.11": "2015-11-12T07:59:38.289Z", "6.1.12": "2015-11-12T08:48:42.453Z", "6.1.13": "2015-11-12T19:58:22.437Z", "6.1.16": "2015-11-12T21:33:58.193Z", "6.1.17": "2015-11-12T21:40:59.437Z", "6.1.18": "2015-11-12T21:47:11.906Z", "6.2.0": "2015-11-19T04:34:07.455Z", "6.2.4": "2015-11-25T03:12:51.553Z", "6.3.13": "2015-12-04T11:57:15.794Z", "6.5.0": "2016-02-07T00:06:55.612Z", "6.5.0-1": "2016-02-07T02:39:43.077Z", "6.6.4": "2016-03-02T21:29:29.077Z", "6.6.5": "2016-03-04T23:16:33.429Z", "6.8.0": "2016-05-02T23:43:59.939Z", "6.18.0": "2016-10-24T21:18:46.160Z", "6.22.0": "2017-01-20T00:33:31.727Z", "7.0.0-alpha.1": "2017-03-02T21:05:35.872Z", "7.0.0-alpha.3": "2017-03-23T19:49:41.444Z", "7.0.0-alpha.7": "2017-04-05T21:14:08.547Z", "6.24.1": "2017-04-07T15:19:12.789Z", "7.0.0-alpha.8": "2017-04-17T19:13:04.266Z", "7.0.0-alpha.9": "2017-04-18T14:42:11.806Z", "7.0.0-alpha.10": "2017-05-25T19:17:37.788Z", "7.0.0-alpha.11": "2017-05-31T20:43:49.753Z", "7.0.0-alpha.12": "2017-05-31T21:12:04.430Z", "7.0.0-alpha.14": "2017-07-12T02:54:18.061Z", "7.0.0-alpha.15": "2017-07-12T03:36:34.910Z", "7.0.0-alpha.16": "2017-07-25T21:18:27.702Z", "7.0.0-alpha.17": "2017-07-26T12:40:00.168Z", "7.0.0-alpha.18": "2017-08-03T22:21:31.665Z", "7.0.0-alpha.19": "2017-08-07T22:22:14.571Z", "7.0.0-alpha.20": "2017-08-30T19:04:34.630Z", "7.0.0-beta.0": "2017-09-12T03:03:00.894Z", "7.0.0-beta.1": "2017-09-19T20:24:49.244Z", "7.0.0-beta.2": "2017-09-26T15:16:02.774Z", "7.0.0-beta.3": "2017-10-15T13:12:29.071Z"}}