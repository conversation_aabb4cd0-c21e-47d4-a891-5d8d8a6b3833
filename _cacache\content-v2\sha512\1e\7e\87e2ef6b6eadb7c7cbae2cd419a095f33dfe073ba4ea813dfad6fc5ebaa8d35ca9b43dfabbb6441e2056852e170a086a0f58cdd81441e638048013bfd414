{"name": "@jimp/plugin-invert", "dist-tags": {"latest": "0.22.12", "canary": "0.22.11--canary.c55989b.0"}, "versions": {"0.3.6-alpha.5": {"name": "@jimp/plugin-invert", "version": "0.3.6-alpha.5", "description": "invert an image.", "dist": {"shasum": "26ec63abb19fdffa4e08cec3cf6977d97c81f97a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.6-alpha.5.tgz", "integrity": "sha512-HfO3DjSGrIIPn6RoAJZ2FYebUhsqbNvD4snNCSfdjuXcPpK3fTUeI/74dTTU0WVNSnMQfbne2HXEl79DbpMDSA==", "fileCount": 9, "unpackedSize": 5244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgiscCRA9TVsSAnZWagAAlYEP/3HuU1HQ4rnRBcykgbCu\nJ/onKrWSnYZcqu1fGZqjjSVGS6gWCz0CO58PrtDd+mvji/auRcLfAF+Vi97I\ncNnPF81ngEbbzUjrVE+IkCbCMwqAkECJUWbkQ2qKYl8/QDSXgBSQ/mUqOV44\nhBlTV7GT22jF4ixyn/zJuIKdw36YyJVHGG+ZXZFVVF8yP9TqybdcVaS3qQnn\njQWYmv0tH4EG5Vx5F7h+sleHUqmgDbpczjs2t/PtQztFfD3fFQeppg0phbxF\nrEWOf8ZfmljOzceI58Ef/HqNK+c5d5mwR2ogagsVVurF0qW/1tMcPLvEyyj2\nHxQ9O9TVeqqxDJYq5xBxFbRxXTJzpRzkLzvHFJX/HuH5SBZR+53Jo3i6OLWB\nw673bz/llOLHF/RgQ/xt594/69I7c6akMhscebumyt1TXyO3UI/HZaq717ge\nXSUz1/UuhDnJJvk4iyek1kE0yFnUTV/CMx9qFUqIihw6ThoFsQhGTCZQmtHK\ntkTL2V5N8HmjKh6NtDB4SlwzbMow8ngQ7b6K6+qloA2+GmJE090Qmi1l6oZV\nHcCLzakKbwMgK6ckoH5x36pd3gN10k2kgTQ2c5W3U51l5soQzrFnebkyx+9P\nmQkxxDMEb3fL2PZS7hBBMfBuOFStST+he3QNHhwCs8y3vh9MYDMB2rCvKvDl\n1jMn\r\n=IluK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAY7cd0AJ0f81f7xmr6TQItlJOGAULvZc2//DIrsJ2SwAiBjdtVSA9iaXY5n826wRtf+dGCMqkJHrVqyiIgM0w3a9w=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.6-alpha.5"}, "peerDependencies": {"@jimp/custom": "0.3.6-alpha.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.6-alpha.6": {"name": "@jimp/plugin-invert", "version": "0.3.6-alpha.6", "description": "invert an image.", "dist": {"shasum": "f7333bd43be7a2ab56b2044ca6ae9cf3a713e844", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.6-alpha.6.tgz", "integrity": "sha512-hMFB4VvENVHyCfOJdaDDmDEpHihC4BaaEYPnEl41fmbO/hTzo4t4xfQMywEFiB2z/nC9uyXdtrZm/dmM4C38Tw==", "fileCount": 8, "unpackedSize": 7558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgi7yCRA9TVsSAnZWagAAqqYP+gP0S7Sig/QoBr4kXFiX\nj4ikrZ8flW1ke1AMIvLIn87eQd/D2mqdVQ6/89LHhdGsv9lgjwUEcayiTfcQ\njvbhy5K9KN3RwdCmgg6ytT7Ek7qfhCJUSCPwIdkhuh3lZAbxZeRxyCXlkUiB\n6AOuFaM5nNTAHpG/2QxWgw5GwzAVM6FMPvRkbm2SHLLgCGuBs3ECu2gBGvRE\n/9gDbPliusj/QUEj2QgT0Oc4sF+h3HWjsYEHXl0KN2ZWcvjjSH2NvWUJKrH2\n0vs6WdldvexKu+uSJ5foWOqFARUqJ6ttzL+xvRR+ZJirqKQJyMpAk7Rn/ESn\n/SSFh2Yw8JvYe/u/upAvOnVHd7uOLKS0nARJXpqUbqlDiTZv2/iWOMU6gHkW\niMCFvbU40yT7agrhiwkVMfWVk1XEUJYw7ejKGY1xEEGVJ7m3FCt26/4s5f5Y\nzdSwYUywxOgZQFLvwZelemuPqQL+JkPB2st9ZDWCilEFWstg2c6OVu+O3ixw\n7t4FdJ0N7AGHPp3MJpSpzDH7VtWp9PLxJwa7mZBabzOKiXm+XSiWd6rpExBF\nem0TexxsRfj2afphbJrrZ4fsaXEMMQGs0+jZZpt1X2Hl9js5TqCBPy9RLxns\nG4gMLLiX6NdO11+8cjsJyBn7yqmWb8WISb7yaWncVa1nyYE/gRh9WSnHD5lT\nanDV\r\n=CBj/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvbyxSqIoKum7IOb7OlEGPcU0vnNPCS8yGCq9v6PNxFAIgemzuFrqKFTD8vi/pV7zBYCU5vjB8h82AeRhhZh9MRgM="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.6-alpha.6"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.6": {"name": "@jimp/plugin-invert", "version": "0.3.6", "description": "invert an image.", "dist": {"shasum": "8edc4bd606b2639360e1df34de0cbc7268956731", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.6.tgz", "integrity": "sha512-dHr79W+++waersTDUnKpwaqyEwOhzFP7ANeKpBv/zg/EtrzTqVNtd9nXfrqVABNFIn0PaKtN5eiRGBSFM0At7w==", "fileCount": 8, "unpackedSize": 7542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgjz0CRA9TVsSAnZWagAAbWUP/0Ojy57R8RG/cCDVxLp+\nZSK/fXSSvQxb/a6vdKtLydw2Yvl7Y5Z7mFJtZim8tSSizxaR46z2lrhP2uJS\nlrBUjC5EUheSvDsJ1mAnTlhEKx3Xtrt3mtucNFad6iUr9gQu419iChHsha3F\nJUMqOuny/dBRd8pbGchj72duwfn8X9FwXCAd1obY/Jqn+RcJ0uxZgSS6JhYj\nmkCU10JwrSJDVGeCxookAIH/L5Xe0Bi9HOlQaqI1fFOEaab4pZkcrA4+biPI\n37Q+PswJaXXaTuurnrHjceu8I5KltcswFX3UxQV4qZWprbHgoM8OXifF3yf7\nV82TjSpYDOB5w3l0icuDa74tT+BNZyQ7AzCRooOHTdkkHOwD60I8ad8FCU6h\nM3qQ/1oz/TnmnXx3kO3u+tiWJDqghrdhg1ACXRRlzBiCl+Lg13zPRNVG6+z+\nhJAm6KMsUqu8TIg3hlOB2jVMYj/Sj/y0yK9zzPtLif38Oy6uSePZkW4OvEqD\nSbDUhBnukcNUYxfbFvplAYvcDOQtFw1CaJ6QCkpOBkUji4LpyLYdimxUfWXn\nPfuSOIH55Qy2j7UAGL+utpTpf9GA/Z0GVna9aDj90FaQitFdd6JqJXldIxeZ\nfzWGzbtwEQLpy6WbLD6aBgvAtUOgDcKtOoVIogDBdKeBNzPVq9JmlXxeIF7Q\n6WFj\r\n=nSE2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyA2NjI20gNSqK/5/6Q/3Gtj0zcW7A+YASiFXSBZ4VoAIhAJNhz3qzdco0IifAbbx/an7l1pRKj0En3F3ovliVY8Ze"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.6"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.7": {"name": "@jimp/plugin-invert", "version": "0.3.7", "description": "invert an image.", "dist": {"shasum": "597276df75edc37db1051908ccb3e7f2ff34f17c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.7.tgz", "integrity": "sha512-XduO0BQEqg9KQJTNtIdYwn9QbRKatgpPPSN4fS//zVDwPHVHPZzY80ufLo5y9zHByDg2lJA9NaWY2cMHL9Iv4Q==", "fileCount": 12, "unpackedSize": 7485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgj3pCRA9TVsSAnZWagAAeHMP/2C9FILMtV0ay1paHx5E\nYLKOtUZ5EijfhBDC7o/n88gdkgpaalSJ2OF8Dp1EfRKCdI1a+Leu8wpejPbE\ngmk7uZpM7sxORVtaCB7Vrk4uDeb4yyc6wl4cvz+pkbBna22WqlUIejZZjt14\nvOzNGZwQM+6kTTzOATeubEOoqJcVFYpn6IwkJtnbUV/+n2tKCkG+DAD0F3z7\nYfWiQ88v2iC1Vm/C83KuA3o+5NtWQgKbKSUtqOs0h5UljutMulTmAz1aNDum\ntyVrO++uS1pD/Qrx1n3/T2Iu8+F9R8fkd0paWecFBydhySjrC1ITN8+Ck3Qy\nd1fMV8+/n/NaJ2+YrQKWf7uVoCXhLwoQZbatPOyAnHnATUkV93za30U3BnNo\nE1Fv+SGuEXUyzhBMfnJWnIIa1uyQ2N0NvmxBsBIF3Bd7hkJ/2P3wLdiqO4VB\nGuiViaaMN/zrQAcdt535YQUeCAt+sdpIXqCQa3fK1LH3VrAyIjYx7j0R6AH+\nGaryAA3Zpjgdm0QI+F8cl0uz+7yE/wgT2vX34+KP+zJLenUbuMv/3/xencGr\nrMMWInfF+8J4clNZ96ivoxA1PXduDfDwVYab+8nLTcD1rGXfXemOVCkPGFjb\nYljOLy1rYFzpv4Gy3m55qYvuXhywy5GFhzSmtccc7/TbMLaRGr+Ods62Jdvh\nXlkE\r\n=VTb1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSHjpaHP/Qg2cREZBR3S+Hgv/kSBsOiY3zFon3qjd2gAiATFNaIft4JFCCx+whOsptRnYSYxjbRg600ia2+IShuwA=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.8": {"name": "@jimp/plugin-invert", "version": "0.3.8", "description": "invert an image.", "dist": {"shasum": "55b073c08a326dd24ea8885f8b2633d04afaa48a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.8.tgz", "integrity": "sha512-ZNzlTFTHPRw/kDDNeeAYsxZPK/VIJGr58bhJrNkbweEM3xPuyf+LsYMWlws5Tj4yO2Bv3yXhjfsn6oXiTyS7pw==", "fileCount": 8, "unpackedSize": 8033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhDyPCRA9TVsSAnZWagAATT8P+QDTbHeUf5RsodolJ3Q9\njdAcZwK239et+jX74SVcKU87kZGvyywO6EiSHeE0zCysIUvsFso0d8sUGAXd\n1tDAu76w8tOFoZfOmK7ZRXCLDbepyyOnf/sPoAA2iRkj3X0Yp+xNVQvJ1fWR\n3pO+XeEl6bAYP7Uu7rfFvyOu+IaWqJiJvk/AK71UMdPuKkHL0yi3EWm14/wP\nrt4oEg4iMoXp61MGOYlZhPiv/v4XGP9dInzVuGT7tnO5j27WjiomppKpD8Qb\nkzL89ff8BHKP6htoaaUxVRuL3VXuSNIU9JXLeeIn/kFlK9OBDNQ9NPKgsyWw\noLtUTJppb292pV2mw29ybWAzxWSPvhRGgECcBu6uijVA4vuVfFE2BhQZcLRR\nRJNV/jKKGoxeEr+uLLqASPaWkzXnb3O/TVldo7SzNuGHfw0fXB4JfwoNQxX5\nMIMTaxCoYSiIEzm1KiunmVcqUaYuSp+nKI6hxXpIqeT5nh7cd/ldrILRR78t\n44pWCMvhf/zaFYqycBe+vjwLk8kZx8L5Av1zC/PGHJ3eS6rcl+Bb2P8R0ydE\nvZg15yg648FzC8Px33Xxfv18yCtQznRq6XTP4P1tTPfJsUTUhy2Wda8GuFim\nGWlMfte8irG1Jt86YSs4GwzG8mW7sGK1WcgmOzQDb72uDZFmJccQM0Nj8jta\nayHv\r\n=hRPF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE62hv70+N1ipxAZZkNNFEtF6EcTDfIFQyq9gsAeKyQeAiEA2tJQA5J+FpZskEbYUio5FPm6z0j8sz6FkUnAopNzElI="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.8"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.9-alpha.0": {"name": "@jimp/plugin-invert", "version": "0.3.9-alpha.0", "description": "invert an image.", "dist": {"integrity": "sha512-G0NZpcMzkg9wCC/D8HYpUUmlyUoZCJUDMXsWCy3PEDuL1N/BBEKyHp3lQcCTiBKfiJT2GEt7GOuRxvfs5R+4yw==", "shasum": "d0a20a941d2d830a457264467365cb907656f66d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.9-alpha.0.tgz", "fileCount": 8, "unpackedSize": 8074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhJTbCRA9TVsSAnZWagAAr7wQAID3V6fcYYffJ6p832ZU\nkpLr3+iQl5MyYJmJsxvhcHno2wI3YylvL3ebYm7JLpYtLDtC+QTWnhANrD6j\nOIP9RIJnpiYmRhrdfVmtt4+xrvZYlgJ0Q/g1sf76cvKv3RIIuV8X6pgXvrGY\nO8cxrKhqLQjwnGN1JSWgZwbsCl92ewfu+o49Zs3o3MD7v8nLPiS67XdvD/9A\n+zEH/5te9folqmhqPI9rl33uJcMmjW2LhWEIgyyPnS2vCMVfbTSN2jJBrYlZ\ne6Cj0nirUMzsvaMg3f+JtCuUk79AYlvGuN23FrLjC0eyzePftuxskfWkvuPh\nbYwzBBfpeQZUjWAU+TVWmtDg0vcHW7wu40VAPhbOrXXgxMpjE2DF0eY8qmjQ\nfAllAELZpMQDti/j5UP3B+4SPQsnfbvBcorZ/E/FG4doxBYB+nZr00M6+EKZ\niAtHMOa8HbCpZu1C36WNNQg6kaJ42jW/MZG7q/DXUZ7FBx2JreAP8nfTpFWI\nHGGUyXOoVxlZE5lgE1VtJ6tJUSgWeLEaGzqC72KsoQWhAoHI4XAAMgHf3glJ\nWZ4K1UV6uDerGlvt7dqbRQIVasVcEjdpPvFiWBW4dvxNiP86pZMT8/nyYJvt\nDcoK6aOV6g0BuEnFlv8wqAT9Dj0/E2itAzWZvfqrkVm9DXGfuLLz8wX1/DfM\na15/\r\n=gFSk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLemG//mUX9Uy/OVB6W8TOK5WkVbf+FJjGmgyutm21SQIgEufQk6zkk2AKSXbrpGBT4KG3LxWYIIgKScTMLknS1es="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.9-alpha.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.3.9": {"name": "@jimp/plugin-invert", "version": "0.3.9", "description": "invert an image.", "dist": {"integrity": "sha512-csdCm9QpV2W1IdE8jo4gTNC8Q6Eh7hgE7kp/Ikc9W4SNkHA+ppcNOliT2tT+o/Yxza393cWTAIVHHOfKC+gPzw==", "shasum": "2673642f82fa790b29b9e40cc476c0c36cf4cd76", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.3.9.tgz", "fileCount": 8, "unpackedSize": 8058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhJceCRA9TVsSAnZWagAA8TgQAIW9F+ddmd+NJKPcgYmR\nZwmcRsZwdnN9mKNXjDvfrOcYNLtRD3XebLZWxW0arFXQF/vIQAy7xFxSpW8F\nWfj45/6MCH0xjm+DtHwaEogsns7MnALPs1lEYWg/BlvYiKdgs5iune7hsjb5\n5QxagwTathMDDIdTcJErNr3qYLUBUktiPXCEFSfLqHDzShtT4eA6zmz/SFBf\ny8j6TyGzfL0DiE/G1xrfdcwY5LDBNIpJ/YEBeifZpDMxb0UKOLYvQwFhczV0\nMChuECA7dw7Hl0Fp43TWmgS9Qk8V56Dsru1A093z5fxXbjQIzerEJTsCHADG\naPFxba0YpdAZgaiL6NkOoVpM+19ShcvcJkb7kV12ZZHD7DvGGl/VNjMtvfsB\nP86Y+62A1pYv4bYlnaaJR+rErzjqz7Snt6tuTA20YoUdx2TJ7MgKfJYkhtp2\nYVTIMw2rmMl5sSeBQMgJioPhymUZfyZmsWLIKESUehps0H7XNLMnlb/n+/R2\nLmR6BF8GAtMroovHHnyaaedUMPo3AO0NSdOUCWc0oEyus90dFyrYhBklGcOu\n7ppNdDwVHvNgO7Ni6NJLShsiSeK1UDkGSo25rJ6IklvD/L5rLD4OfQNzAiHm\nKc+XleeMeOk1U7CGfSfxSxgiEXjXg06dZZLm7YxrQKgfKC2eDYCAu1rr3han\nZrMB\r\n=km4U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCw/6/17I2SR90ZlC6R/izIxF4x1iq89gnp4fFLFelBFwIhAI5GSw7VbxKxn0ic/TXcdpviCq5ubKR9PRQaZQyW7nhz"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.3.9", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.4.0": {"name": "@jimp/plugin-invert", "version": "0.4.0", "description": "invert an image.", "dist": {"integrity": "sha512-X9Zm+uZP6wEpWrnCkLkgbFTDsQAafTByIy7OR0ooKV92hz84jLx96psgFmZAG7OOA8Z1U0AjlV/YLZF8Ydjj9Q==", "shasum": "b490b6c758394cfd73555837968db2d94e41afa8", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.4.0.tgz", "fileCount": 8, "unpackedSize": 8058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjGy+CRA9TVsSAnZWagAACQoP/RQZRDlXlPiws4mIM46x\nN3SpjhS3fcRZZ3mBpC44Ebf/sni5pXoR3Tv93dt8KC0XwclAEHz9Xa2txLv+\nPHLOBs3k47Exk2t1ZwV0EfhH65Kgp+WMk1IAYEO/G/NkC6TaCIuw4DLCJdS1\np4TwjRW9H04dsV/2sl5/aUV/C8HvNplSgEMYBt3zoi9J9Qsa+iLwxL6805V+\n0ad9kJnBczDrMmFVgjylN5IxhsOdPzO10E87F1tDpsyQiiARW+m5OwPXOuru\nu2ndCa0oZ8kzQ/gepyzb1xT2RNnMic0Y5wVN+NZ+VT3/EMBNjicdIlMf/w8n\nwSOOagvNgQ7G6sNzy9m57pFB6bnQrHvMcoODw21cN8D1frUP+rgd6vrUiJbO\ndFylYjjmzWy+qvWdkCkfm1BFIgw8MMLMFrI5dBSoua0SmEDwqpBKpnmPMczL\nJShaaflCPW4j2NXXjTSbwf+vEfL263coGV10S55pYTgrgKC812N1LZ/4fedN\nFtYcYeCsGKlhBJpU4i5wU0Eyy/hMRUlyZg7KqBiNV3EaeGN5b5LxIxtmL11d\nmFSzB6m3SVWkKtXrR8dTY3AgaSMoR52kLoBlFQbYDlNaDt2EHSavKdYf10kj\nlqeCFCFV4rzFRzxzmNCaK43R5kfR9Ffj16SjqHb6ayPtGyit/nRYyFmKQU72\n4kys\r\n=904W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRHIP5LwCqrJAFa9VlvSxFO25n45SRdlOipf79UNVcdgIhAPAW/ltmY6VXiu+1qLs+vTXYZWF2QboDi4JPcf2h79zg"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.4.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.5.0": {"name": "@jimp/plugin-invert", "version": "0.5.0", "description": "invert an image.", "dist": {"integrity": "sha512-/vyKeIi3T7puf+8ruWovTjzDC585EnTwJ+lGOOUYiNPsdn4JDFe1B3xd+Ayv9aCQbXDIlPElZaM9vd/+wqDiIQ==", "shasum": "4496d2d67ab498c8fa3e89c4b6dd5892e7f14b9b", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.5.0.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbnWuHCRA9TVsSAnZWagAAkFYP/jV6S8sDHPhNfV2lK3Ke\nefVx5j81XQYYcr82JHDfEWUb8P6M2SK4PtjlHBsBjexuR8Vde41kQvo2huMo\nwldKtGpfYn49kVte99fiAmQNdD8mUaYkos1mK8YyGM3EQauxe3IJU471c/FJ\ngOywOEG6RW1nrrfES37zhJh1QRqwgLsPANDJJnl5KSdNj7jeav2M6Ur24nZJ\nNL1IEkBJn9h9zsNP4/okrrF6G0qPKzKYbqSrSYpjmubbFSKh03ZIcWAxrgah\nXHdx1M9kCJWjfxD+6zT90zUKKotbsnbdeSrwwFB1W8I7lwYc/qJIaek7VRHS\nAmdUWe7l3UhOU6GqI+IDzAzZ88D6XTURT7XSFpIFhfLTEJVS/dcmjr8Nr0+5\nhOIenKpPE2etolk+nqTZyNapgdnK7jBqrQ2WiiPHpM0RUodD7pMfZmPpqYd5\nrIuYhm6XmHOAYxqYRHwuFoMf3uhJRaECFTZLWKbJxt5GQUcg1/W7ejCIJDlO\n8c24GRmHQ1ShM2OZDcTOLH1io8wkCUJlQxMlFdlO+ascDya/5ogddOlc//mk\nrEBXVtA8XJSPeIks+HMqTiSAnlHPLjNXL42F2oRBjwfB7FRNTEO8NHkSnQyA\nV82NI+XOS2oF6J8A6q3Y2F4cuWuJW7DVjd0alKzLTU/ALidCsQ1pK4dYPVYC\ntkkK\r\n=xkgI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMi+gpefhoCOOoiXM0a5QA26mDVaNLnRGIwg9vMB6kaAiEAkGM3fAa9tur3Djsns4mqVqDqarFhY9Fk9AMEfaPV0n0="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.5.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.0": {"name": "@jimp/plugin-invert", "version": "0.6.0", "description": "invert an image.", "dist": {"integrity": "sha512-zTCqK8el6eqcNKAxw0y57gHBFgxygI5iM8dQDPyqsvVWO71i8XII7ubnJhEvPPN7vhIKlOSnS9XXglezvJoX4Q==", "shasum": "da4879a41cc0c12110c2bf5e63524b07819c3524", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.0.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/utQCRA9TVsSAnZWagAALG8QAJLmDaoIKVa665U9dytp\nYFUZfQEQNULfDO4fAnx0Lf0/ZPRYf0s/Trx53JR7efut/3Pgvhj5HYnayVNw\nUOj+h2Hgi44ls1YXx9bOyxiJhQwFQHtLVPqur0W8zbCvLmyjCIM0JzIPu8oA\nJEiTWoTtw+9MWiloVLqQnIp3gQR2Oypj9hGMW9UZfoz9TaXR2Aa1rhoz5kuJ\n6UmVFePkQdm3wZmvgMJPsbNntn8c/UG2ee2bXtBAiaadP+ChNf9/xXEjE+MH\n8yaAipMKutzO7NrXlxbgFMjCG9lmeOIMxGfGxvjpdRlUJM6s/OtBO2jiPaYv\nnUo6Q3m/F6oilSCahiwVDB0ldjj0vrJpiNvDIy8nLsElpqhkBt3UVxFmC86f\nA0TeFyuJZS171dyskwrNrvx974n42lpgwyTec07w2ATlZpqC1Eo/NeYIarrw\nocx1TDTDWWvkPBi/IDrH8Vou9BoFVJsmGHubqj5BsAoNvTcpehC/PVCJFE9o\nzeJTbJwGbR7Jho8lWPTpYkWmBuTGs4i0RYBm4AGzpiuo97snAZSYK6Sp/7wd\n4AdjfXsb1QH4pm3FwOHqsmBegp+dwmpOaZl4gCMDUr+ACoE1QXApha3r8xPb\n+mP/yXd3GOsSyKKrXH4HI2MLUPpe+IBMvlG2d2PcGHrlD/W0WmZU02ylT8R2\nAPal\r\n=FJ6K\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo5PhQXD8YxusCDH0CCoJfj9876AjDtcS/CtVgQyfh9gIgQtNklEMf9skWgvJ+yUOOtqx6rcMr2p2PU4Pe44QC5uk="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.4": {"name": "@jimp/plugin-invert", "version": "0.6.4", "description": "invert an image.", "dist": {"integrity": "sha512-sleGz1jXaNEsP/5Ayqw8oez/6KesWcyCqovIuK4Z4kDmMc2ncuhsXIJQXDWtIF4tTQVzNEgrxUDNA4bi9xpCUA==", "shasum": "38c8a6fa5d62f0c2a097041d09595decb10f7d36", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.4.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxL6rCRA9TVsSAnZWagAAtKkP/REDHZPZOQ92he2ZBW99\nUdIfmhW7LLqVrTKPbwHUaB/2IivAMtUJHKj1Be8IF43S5zcVYJRDsTtdl0VN\nzLI6D4zlYznR8UETeNxxp58qnSxt78j/47AclzXEQcN6xnZiyBgixz2slwfG\nMqjvk7/PKEoQM00BBxSQ3NbUpKe2z63hyah9Rmuqgs8sRcMpyDZ1Nxi8IgUQ\nVtDY1M5Dznj40F7E50C2+Jd9RsOpzM9gGStFY4+kqyIGCa5MQJGlLjweUGvH\nZzmishvGDS9McQOp6EffFsPQSAkjuYUG965WqJbs1Z0DlfohFDLlaPBveu4T\nibU+et+QxaerVFGNKVjtZqPlsRMMaW8fYfMWr012tnwIbLYehEgCA/I015Y5\n2eMJRcyVsuH3BMTflD42rrVDwCk2PK4J21Zeh6OymvKH4OicXOezlTKOwjFB\nKOj8VVKcXZS7pl8RG7XKAhG6GGxHsiFo6SGkRzwxaPyuPXCgoqtJho3P2BFt\nTUjQGsKfL+vjkn6t68oqyRkLsPpx01tZTFY+t8srXcJb9+6Hiwaj6uSR6Tyy\n3vn2SQ1+h0fTAATYEVoj3I2pCuXXA75+ZAV3vnMj4M1Uf+C7RO6D3tuCX70F\nJ0Ji+CMwygqD+ll6mn+Bq3//ALBsJOngY37xzgDXVpASEX+t6+upKOTGLtLv\npMYK\r\n=/OL5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm4/XeHlUpIIsZY0jcPknxhsVeDoEsTpccAe1pusHBqAIhANOnaSisSZknzV1LfY/ZpqjGwXa9UTTIxv5/3P2N2bUp"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.4", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.5-canary.4564f3c.0": {"name": "@jimp/plugin-invert", "version": "0.6.5-canary.4564f3c.0", "description": "invert an image.", "dist": {"integrity": "sha512-loJrxdcP5v7ZFdGnXJg4QPvIhBJiOqT7GacH0YdUl8VRLE6fPqT98GN+MJI5CN7Gsw+D9IijHeUCV051aDFCTQ==", "shasum": "b8a4d6f12b1410db1841827a62fc3ca140d968c1", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.5-canary.4564f3c.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbaz1CRA9TVsSAnZWagAAf7QP/2Gt/oqU9bM1Muabo/Ho\nTz676Mf0ugnozGs20pQQcdHr9Ws1+8YLhqRb5oUg1taCIKXSTglDJq26HZ/v\n7O6jStOw1jXC53kMcSUrtVwaA7EHXWa/fz6dHgIimJO+qsaW7vQ7f8em8f4o\n0Qq+p1QHKyKHzpBoK7c0hY1k0EVYV9HP1yS5aazjB8YkygU4hgjpR7FWFHXg\nZdvqPO2ZyjhOL6LTxLulTPXJiA2JO1w7f1ktq/MN5dcwARWJfFm5YXyqSnIc\ndcw1IOJCh69Kmj+LQFVICiGrlyvDrocKOKJRXWp/8HOCNmySXxTr8SZTYSzf\njb7X0i2Rf5CEUG95XR+7XIxQoaeQlQg5HMlJHKMDMJEyBZoZ81H5JFEN/roK\nIPU64psM5yQL4nbYX+ZqAxDmUY7KJKYiAGaoZCICtCEvC+C7qFa7lO9YomX2\nK0JTu2Zv/z7LqlFhBdrFZk6pF841K0Y3pkbWdtzCYqatOt/EScXWYXyNzaVS\nswFaPGSMaggJtvW/2IIFCjIBMLadv1XA/atlUwMSsTqdEJ8pAqSKt4BO6QqD\nhWRuDf+aFHeyr/hlxBbm5zuJ6nV9KfHHYuuq+w1ZdYKw45hGjZFCL8Kn7vS6\nueM7NbSwDay1p5I3VSD2IYo4w37zH0ckuQycwLx7+tmoMtvKDeyqa4ZCki0a\nyT0W\r\n=4U68\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH9LSXPM8gKmRxQUHVTxlBkna2BPOH4PpckUu+z2sedAIgBQMJZamG3jAYi3grk+nyyVkJnrNddz4/QIQRnyDrP28="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.5-canary.4564f3c.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.5-canary.eeb6481.0": {"name": "@jimp/plugin-invert", "version": "0.6.5-canary.eeb6481.0", "description": "invert an image.", "dist": {"integrity": "sha512-uMghT9zl1I7t+BhiH/gJxqkI/QAWkd1WGk/RvPNYMWNNX4SZZTTVCV3PWSL+s4nn/HQyPHBAGLvQvS/JM2x85g==", "shasum": "93ecf9889e7d3d1ce7eaa52c454445c74956d5e9", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.5-canary.eeb6481.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbbD7CRA9TVsSAnZWagAAIb4P/jBvOqc0l80UX72sq+Xx\nALvJ2X7nE9hwinYUoz1ZlnpDKRqcDcJ5MdgPK628O2VfOfIve6QRec7VquKz\n2t43Qcr+ZXefBmB6Cs5kA7/7htZiqInI0jjVpTDdgvQ36rSMHAw63zObU4Lv\nIxjtmjyEcBLrQ1fbWws43noB5hrRyCKMF0ELyUwoS9PVQXlYiilyUCrEYBBO\nVtY8NoXCKFOAoYigllJyRJ1DRQ52q/hHUFj2u79ymN/DzSs1jeadURXH5i5d\nw14FDWU8v5IJq90hoiAYZw6+UZ3KNnUuc3A0Sz9ksnrOmcVGII94FZhCPFzy\nORQ1djlGPWJbo2k+DbbAcMQYC3UH4hxnfVBPmCyWSs7dKT9NBt86XKM1P8wm\nh0yVCfIFQYHgJgXq25xGPUtnIaYfnxQOTY3Kf2cxWYNDwknTb8MISH5egfeE\ncVCP1JhNbrs1B8c9Ta3ctfBAqOnbfCDLeqPb7/jHS60on1IoWaRj9xL+N9J1\nyGHYYiYnhCs5I513xKntbR9bHdDKJfvwPWBudiSgPFG88za8K3iAdb0ryL8f\n7P1p/5zNhwJCQ9hN4V81etZSFRgN4Bxj579qPFLXr8ABsBtSlSOUdiWtPQM0\nnqSo7449hd33MTSiu3axg3XgCsNVjkPHWKHnxeWutgtQmsY5vHuAnNumO40T\nwvKt\r\n=PBzk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC15tbXDusYWjIQvp9PzptFoPETIngYbDSYJCOzaub/lgIge3D62xOB/hNzEEmRwmaOGFfeyfwIGedQ6HktgZljSoc="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.5-canary.eeb6481.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.5-canary.784.118.0": {"name": "@jimp/plugin-invert", "version": "0.6.5-canary.784.118.0", "description": "invert an image.", "dist": {"integrity": "sha512-iKxo9DddDO/qQRVRMBLV1m4Eim+4bOC26p5phEvyBao7EUC7V5MH7g3Ahroo1QI7ZWaY3llEd0Jo6q6fk39JdA==", "shasum": "caa91820468806477ba6808c7d50132228497e08", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.5-canary.784.118.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbbcrCRA9TVsSAnZWagAAI0sP/As8/Ly9sJL4L1pmHiAi\ncD2s7q29OYnY+vQPX2xYbua25D0atD1ZpoNOfBWCKDv6H9nJ4lRSykCwGrYf\nS8aWO8XxLEDmxUyy8MR5/w3t2EthQ79SqOYaI9a2TgNgMbG3ksSozD6srHkR\ns8sQW+uQW+MCVXbVF1bzhu4wUdO+rLNztadwcZoL4Uo9C8FtJxH9Gn9lOm+d\nw36Cmy4Jui72XNlDfhntIz/wNGuKXWNmv4PpY0h7p6J2mfmevylkXHL0nwT2\nh85iEnPljOgv/qf57VUzClM1KablGoYlRaor64WjkE7tzr+yNDW1ytc38XfH\nOUx+t/bKIqQhXjcXRUbZe1f9uuYflUjIAWAJGpbsnFZdQxMA0pTuf1oXEPFf\nWYwMNkcWQYYdshBOXbVGh5/WXc+oD8I3RPHVtGFRbRlUXHjX2owE98btLjvj\n9u6NBFZfGis0YUG3OUt1EoFyuDTUwFf5emS/cjJW4hR4711kY1XNbBBh2i8e\nIOkMN9WQBqKRfigQf/jc4SWTfxOeBDKf+oJnFH7Ena8g0SwThlKeYhJOx+Ov\nnDG1Z/4Y+GXsDois28UdNyzIZQ8hqwUuKWhV4KkaDY7Ai5VwY0HflxoIBlyc\naMvud7HDZ6DhtUx5bDhfgv3jZLk1J5vPxfEgR78A77OiKo9H8DuBgCsZK8ID\nbgZ5\r\n=Pl4U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRaLtIpMEFvwJ3oxFHtYVERUXF8dJ2+cKdtmh9NENZ5wIgTHvOzLDauk66nXtZ2fBWcIOxVnsVS1FHYIUstUE+UnE="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.5-canary.784.118.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.5-canary.784.124.0": {"name": "@jimp/plugin-invert", "version": "0.6.5-canary.784.124.0", "description": "invert an image.", "dist": {"integrity": "sha512-m7ZJw8t7vsanwX2o+yU8ohntkxU0n2kGvky6jbvVJWQfUxKaFxrqID777jw/mHt21jPxJq+KhdJ0mRD6HoaQJA==", "shasum": "13344e7771b5c0dfa4cddc7d9e641ac62e80fba0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.5-canary.784.124.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbbkTCRA9TVsSAnZWagAAEOkP/iQ4uSlFeH63lXYEoS1F\nQiW2jvP7AoU8txt21vLyKyiBALu2rJnALpJDeTAUwsohnReJtSnmSXS85ibP\n3OWvOnkTBFCl16XjaPk+aXekhY2LgNQXRpZ5sNETdsxTJYZpk4RJ6Mp1JnOB\nCC9llrtrUM2S+8F9sZiy9DvVUHZQaArJMtZDMoyvA0U/yg6+/A+qw0URgYSE\neUE+rVOgZGikXTinlysdfpzuHPlHitgPUlKyeEKo9n6qCXGgdZawGrEUNBCR\n2qHK8g0SKYj05SPz1adeflfNX7ZynvL2ZUzCdtApPMst7zbxBMibvlejLf5P\nb+KkXYZb53ZOUoDA75Qv5stsfgGRrc4o/SSfxqp+3I70oyW1jEieaACxeqLk\ngiW0IbN11ToCY427PzXstNrWIL0s/F2HR3xacheSsZFiEiUWUM4Y+rKQjIYf\n8ijiHjukE1MJnlyN5AkqIT05AGtPGS/Em5C5KuKQyEKcEiXxjAo7vCsCM2k/\nzKmFStCpE5pR4KnxRz0gyCXO8vhZq69KTG27aHGTNTpeaSHUTyeh5j2TmKdO\ni1rroyb3iiIFhhUZx01asTpbTNmUh/w/BgoTqa5Ee0Qy+wdhxOJe7SfLTw7y\nkElicQVxU3lZxv5LO5z11+u7eKZ8tCaFPsC89FciSEhGw0O9y6EyCLxMIURQ\nn0Xa\r\n=qCjY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdirX+pT+4QVvx9ymdmmTs6A0hC3mqev6sXYzdalID2gIgU9NX+YtWPXpvYeaAuJ6yDJBNL8a3KlVvvPRLh+aTuco="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.5-canary.784.124.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.5": {"name": "@jimp/plugin-invert", "version": "0.6.5", "description": "invert an image.", "dist": {"integrity": "sha512-Fob6W3p+cm/QEF83X4XAYQASK+IhkKneUjRmiiLn0ZgZbIsGGfPZTrOGPUFhTLFGwZIyOmDsLb3FBtAv3CLtFA==", "shasum": "194e9311b0990be17e72ee435d845d1c410a54bf", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.5.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbb1PCRA9TVsSAnZWagAArNAP/AtUOPgUZ5vCBrG7mAQ7\nxh2kxLEEVvimrBb95a2e5Iycw/cpbK14T0e+jRqJl/RLJ1qu/ssCrqFQ26U+\nqUog1nhW/I9XWAeX5WN9N2AAPOPI6EFO8ix/IasCPr3Vr8pRsQikauOgUMct\ng19/+nSPZ6V3DU2SyQO3FqA7OYwCjfrqwc0hSLQApHs/FlGExYNtGg0+tNGm\ntO5mEj+ImVNAgnaFyZkZkwkbD5LrbQgIeLVQt5Fz4UW6hczfYduYGuDtnl8Y\nmS7A2dd/yi7Al3km8OYLdFM3fkItOHYHdWPAy1FEdOjqF2rCwBJ4BuROw78d\nY5PNDIwJ6LYg56bq1ft9yosGMUGaePoSrmQTtfJRc4ajuCzRvpcmz2uaEvaR\nEGFHeUgmZAE/bgFEo7dodJ1rK5XSXepZmT3XsehUHoPar/+LzGUv/HJ5rge6\nSpqMgWDlNwzdpH6YkRwTzyVNKYZUQ9W7MNTfpw8a6D04uSnZ3rHnXZ5UR1nq\nJpIh2+qXv/DR/1sqp8jzdIc/tlwL3MMfw9Fl6EykMiOXiqD6tRpduVG+QKQk\nTTwHsvDRPbJOtqiAz2eJZKOWVfaUaip8wRkAOaJvNZVm4945RmBV9YPU30qe\nF/Xh0c9c+dhuJRXHuEl1hPriLxf5ZEh/leX4npmCZMlcA285sq1LPh0yBlCE\nMoXR\r\n=vGfJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC288hI4aB3oTG26rNZb7mJ5SekTSYbBVQAobvuIad0gAIhALzfTQUqjAmyi0a/HCDmA85qKd/DFlpChxdjIMiTEbu5"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.5", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.6": {"name": "@jimp/plugin-invert", "version": "0.6.6", "description": "invert an image.", "dist": {"integrity": "sha512-Qr1uAZAPPBb52o56T+Guz9RYKscJvHv4eu+a8nqUEz1jMuGE71/4OYMbtohyKhocBD7N4msS6ILuA2X5WeUGEA==", "shasum": "5675ebfc3fc2c0766b6aa43caa5b25c6a5abf6ac", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.6.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbb9PCRA9TVsSAnZWagAA8doQAJC84ob9a6yIUWHT/NRn\nUXteV09pCGigLcC4Pm0EyyzYmu9YOO998S87ndeB6Hf8Ea3Z9/13xfbJNSCo\n+BJ3Zm5Vh4/qChqoJqtoTAu74XR05kIfsU9mA8oWDDrWhcJ/Ax5LDEj++pnF\n6qXubqQ1c4ldFNI483qc6KgYKm9tOEUBvF7L2e/9YXohZts4xwXbPg5rl/Sw\nfyjEbBXGpX+sSjH3zJKT/x12lJSRRpKSu/ABtRUU/rucjow6l/Cg7S112tdn\nVLPD6k9x/mdrahW/Z5vUbEiHrFCloWbGg6UM9ByOBRJgoUszz8IRF8Qz81KT\nzW4rTE7PvsezvK+mnjW0LZ3ImCzuDEz9eCAf9Iex1ZLm01n5YW4SR9gwXU9H\n0LesKDc/tvqQcW5pt6X4dcL9PAtBWYIjdkdxe4KKZxcsDS69k4HOmFkHRm1F\nmd23q5Sjo0caDNcewAGp9trjihrxDJt2MeTJwOKqt0QVBUnqAc1ACBX9gm1i\n7U+naJNRlDQrxEWvsDhjQbFYLb8tvrnbPNiSXclMvvNZnzngj06A7LGymSMl\nYkeh1Pee6nBT0Ubnie5vKJu4udXnnVt81bE+K2G67/iUWeJMOANtLeBejYde\nfkgQW1Z3u1HMR2e1F1/gPRcULGMi1fK5O3L25Ri8kE0sDymcgMAbiqX5gxjX\ndirj\r\n=FWJ8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFhac+IJZngkXOZoOi9mUaZqInM4SSloJnT7lnw41y1wIgZvgZFwPDZIDo97msgBRVQcvPs/9qdMbjoB77hC2pjRM="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.6", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.7": {"name": "@jimp/plugin-invert", "version": "0.6.7", "description": "invert an image.", "dist": {"integrity": "sha512-R30ai1dg5gkug4yzLSgBFA+65I4WSUREvEWm6Zs69v2TFaM0CD7x5FJ8qdwCgAd7QUwD+sMLWwV+FbjIxeGYfA==", "shasum": "112e4347faa6eaa34b1444da073f6718a5c1c8fa", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.7.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbcEtCRA9TVsSAnZWagAAkkIQAI0vIfArGGylog804aAf\nIEPnDJ+gVZGW1n8uegoaT74LOVgyVhB4u2WQkMr6MJDzwVtfflrWxSAINZul\nY/5LqkVqZTkonpcYdMdZIYqqncmtVXEgKurM5qdtcY8sWcdm8lgOfs/lTn42\nhN6yNrFuw/vZiBRm5k/f4dhw7ow+KZ9C63D0HLp9LpBsCS6G2k/W0xRZbFYa\nv8p1IVroE0LLmVlUNbaOPc3a0hDwX/ju/Elmuhx4QJqxQigmPCP8Jw0FYn2a\nqvRodh1NlGGsdK8Qe6Dz9p8O6tpcgaKFv19QwwZy/jnfO9WS4VHr63XKhGBj\nsOR9bRFUuapUULEI1LfJw5CG2bCbYb0IT2jMJg3pJ0n5+aNR51ygTiCOu+IN\n1lzA68e+zuhVwTc8JEdAg6TwpNffj3JFRSWSV8J7wCv6nLHPQSi/d5QWF6If\nSZs3euRxxwm2fkgGpo7m/sF0oe9nVgm4+KBp3/S8IOxIS5Aw9vn8iZUqP7KD\nIX+zTXB3FVZ6zhdZ7OGMf7OSAxAhQPWLiF8Ljzc0B9eS64GbypqTO08P4DU7\nXwgetVVLhm8lHgHZ+Sft5tvhFIPPTfMYEsuSuhIQ6r3U0vB530ykvpI4n/rT\n91zp5CTYEAbUyb/7W9CKuIMk0v3Lt/Sd1CndJv5ZRyRqlgDkF1mJJe6XGHtM\n5Fe0\r\n=5fWu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsOiWgUyODSu+Qgkku4Mwh+3yIB4Y+Axzf97VVC98QyQIhAIkhiGyMydV7YPhvO920sqIvJxlvlgXh02ca7hEX+la+"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.7", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.7-canary.783.157.0": {"name": "@jimp/plugin-invert", "version": "0.6.7-canary.783.157.0", "description": "invert an image.", "dist": {"integrity": "sha512-t3z2wURDZwepUcTi9k0Ka/Sn8d6TzDJgukTr+7Apsx7+J4o53k3D0rVyU02boiF7TWJiGeSowlGlmDq4mvEuwA==", "shasum": "cc9f1b5c30384bdd3d5273da15b7fc3104d19e0c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.7-canary.783.157.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbcHFCRA9TVsSAnZWagAAY4IP/iCiKfvXzHPhYFPLQymm\nH/QfyaZrtAxU0mKU35UkcUW7SthKY2TMTz1GiOR0ZVpCWdhjTmgEocsfj6Fj\nf8vz4qAuAqLnGwfeM+/yrfHrPydJpJUJyAmLVcTBo8t1qG85T7vNhxvlkJEL\n7ZtBn789LVyl7NJpuOe4hKXCyT9gGEGvH2OdTApsOMcAuApXKeopmUXy9lpw\nyLpCDLXZeoK053vYr3wgTJ+RW+no68N19xkLT29z/QBM27kwJfZOiSeUVDtX\nKMG8iAABeVjXgEUUW6mCTPAjqKtEdGO4n5gwNH3IB6bdh+kexM13AUxEpVMA\n3IUuuktKrBVAWHPmKZ9jCCEznz11fHDTlD/nOSwraQZ45U3VqmwFfI8pERRN\no+FDzsUQcjNNJpaUT+5s9ZI6NU62oDqNwFGE51OG66yJ+ssr8VokWDusG+XW\n+r5V0EmOku+6YvL0wbtkJacNXN4tucwvqDL7MZonRkkjYSjIE70vkbm7qFnN\nOMFLzVgPgobIqxiDrQn4xgItoJtehel2j/lGL/KARwsiD+TfBIjtiO4NkasV\nMc4qS+GGll4wtJk57O03oB+I9rGcgPGOw/srSmy8S865Vvj4f+mYGpVIFjc3\nAj3PUqhRjISeSOGPZU7+iYBrP/ubqloQua1f1ysV8usojuUSzhf6V3UOFGaD\nswWw\r\n=cOaC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJCcxFalsfuH/uj/z3xDRuJuJg7D2uCcn7a2+gpKU+8gIgJTjktbfLmnxGxVE0j8Q/ZdhZ7g7mO5Gffj9xjF0ddSo="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.7-canary.783.157.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.8-canary.783.163.0": {"name": "@jimp/plugin-invert", "version": "0.6.8-canary.783.163.0", "description": "invert an image.", "dist": {"integrity": "sha512-keU5aaS8DHFQLUilZyLlK0HHlfN4BAtAIpxLjOjbVakE7HemMoCGIpfVL8R6fGupP+ESmSdG4q2axIXPLXI+3w==", "shasum": "31482f1fcf7c9dbfcd2021e6d4edb762d0d7d063", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.8-canary.783.163.0.tgz", "fileCount": 8, "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbfL+CRA9TVsSAnZWagAA0SIP/0hhIUcAkmNI7l+cxObX\nZ7a4pGuZ0qYCbziEU43GymgRdb4bSqNHcNmUrg75nb77SuoaEPwwecIJZrXL\nm9HsYxk799I6eOcgWjM1AzUaaV9+CtjkOEZU9WL/o/7Cu1HCSt7Ahjm13JIB\nquPW4Grdwp978YOgVrwgwc00IHUjjcrw+YSs5elhncAKCijICoftWCLAwAm7\n/dw3sp8HoulT5c+6UK+o0llziYaRnLEFVPx33RIUBvMBHJzMkR8AV1M4/nX8\nm8iSk1Kj6xBFc7U3sjK2IfxXDjdE0IYpcdtJxdHdX9guNlsnlEYtXsxWDQqh\nPxU+YSYOyFZT6uPHEN/ZOIYXRRmJgejYaMC18/BJm380HPbDI2F62x0yixtL\nCAj1636ZM0rPlLy3Wh7QrYwgGvDtadz6HqoFZXwTYHMaAwmQRt9ZmeCBn7Xs\nIkfJ06q3SeX28cPYQV/gSVcjWoQnmSPWpg0ZfweTQZVBtRfvczGXBoLzRH9f\n5n9/vw2iNcfYWd9oMGFFLjrtTrOhc38FIYkeUbrI4OXzqmBHFEUMyRvCyqqN\nO/Vjx4+l7Q8Tvvb0Hg3ib0pHPkLZd6/PO08njtxZd1uWm/Zq2YQwMek/2nh/\nIzx7nYfobskMcQdAPMcnej5vfwG9NsIlS1L0tPkXR1iHVMMzo0qGmkmwykzL\nUk9i\r\n=+r/6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCY00hnOsYAhN9mKe2HbXie8F0p5d9NtPUQaLrp395DGgIgYprfXT1JI+mtDOShIwIzJbPruhwmFAV9dMuPc6P1I1I="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.6.8-canary.783.163.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.6.8": {"name": "@jimp/plugin-invert", "version": "0.6.8", "description": "invert an image.", "dist": {"integrity": "sha512-11zuLiXDHr6tFv4U8aieXqNXQEKbDbSBG/h+X62gGTNFpyn8EVPpncHhOqrAFtZUaPibBqMFlNJ15SzwC7ExsQ==", "shasum": "f40bfaa3b592d21ff14ede0e49aabec88048cad0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.6.8.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbosUCRA9TVsSAnZWagAA0qcQAIvXxiQUXGg0TDsIGkUb\nBNNhto4vH0E4zEIQcZqZ/j2pOrpzuw1Q4mUnpFaUlnacVzO0XC/gWkk6MwZY\nj6K0F+x+bsiPI8H45P78uwQaKirFoAIE50HC17mG4qr3S9aX1V/TD8ZJiRuC\nxIi6L6Pehxe8M+9cArl2trSTaIFtgfQ9SEc5s7H3CYver/dJYKPXHQDBkOr+\nRy5pBPvGyLaEK0QdvJwnyshgP0GxyAp1KDVHpMs6o8Qct2s8Cn3WQsUQVuGT\nweOr5uUhfmd84ZgIINzxooDKUMNaR/Fyx6M/OaQctgHXj5+RNByU9o2afTTo\n6cwJEne1CsYOtcHXlePGsnHHXKkkQHAQllfihje5BrfxWMdOWOIxlnhk7nZ8\nqDNKXxIFB4CNFqjI/3nQJe1pI+EVtIAA4Kp68JQ1Us0G12TtUbVVcSLynqCX\nGQg00yy9roJ5pKDkq8shMxfNE75JFdu1aMH6HoNCnXhB+acEdVMpAdXAXCwN\n3InBhOj4jan34XkbP9XGAbGoUUFKBvZhHk4LOBpuvImiyvSlEVsR7LnGHQdM\nuZ420ZbYlR86ltamC4UW37jF4CiThM/ayDOpfbgDvLdEMEGzem1+iZNfKbL/\nm7CkwdfVf0uknel7UapgH0RDe50lKq1O6PggvcC2Tpe8oEFxpvfJ0W//jPcR\nsWlf\r\n=jEX9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVXkt+K0G99ywHmbaevZz0q0X48NkwaiidY7Hu4pRUOAiAzFibQpUzclsxrqUN+N18cTBrsTHLBwVYqcsozs/+1Uw=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.6.8", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.7.0": {"name": "@jimp/plugin-invert", "version": "0.7.0", "description": "invert an image.", "dist": {"integrity": "sha512-FTu92i6SNGvF2cFKbLa3egA2/Fq6t/OY8DVeUoS9enh4/xbqG8gWiVFyx8wVaSYLioeNeGikX2p9dmkT30FKag==", "shasum": "3fc4d6d25d1cebdf8245a8585a863d37747009b3", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.7.0.tgz", "fileCount": 8, "unpackedSize": 7991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcoDoCRA9TVsSAnZWagAAJBcQAJ6eVAdSBSqn8A0uWH+l\nLic7Ztiy/48TVGe/iz7/H6Au3uotQ6j9KMOhHMRoRwUY8NJTLG12WpmztBjM\nAFKE4euJ5qiwqRWBsE+3Vq3adeAiXCmmEvrvCeGhax0ei/O59R3mUVF+oP2K\nv3jqLyv+e5qkxq82/tt2J3ucrFJuEC9O6OZhRSdEMfvDGhXE60lEwM/KCfBL\nn62sOsdggVzPBmZxAc7+WygJH1aAHpm+R3UJN+2jkX2740WEWvPLAEapgv2q\nHRyZ3Bety7aGi+K9BAeMl7qqr6Dodusf/F9pWclOgt7I5ZKBGhHsj6UDgsfl\niv/X3lsIRuXh/7b0vBOinqfPmqvtH3+vGH1vEH5YlQnhVOyD2CL/85RaJIzQ\nKs8CHPXE+1NbDNDZUOn7dTPf83SPsI8+QEHj1TbYb1mw2+5KxQP3PyNsOcSp\n/v09qRhEr/k0StR3lDHkaUv0wBdmCnMWKjpcw7Pqy80YKoBHU2ubii6S4ll4\n9Y6Pn+7gjf821wcV84JAe/XfpTDUTdRTeeZ1wnEywambpXR7dFkQ/yXA2z/k\naVz7APS/uX7rWypM+9lb9/wEuJxeTTov9CkL1waMekaYYa1hhMdtadLgqYSs\nC0z9DJM7yFQzpVEeG3nGZ/ermz3YCFPc/PRDaSRRrD8JKfMumtJFaWU9fHmt\nV1QA\r\n=B+eH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID7i05fbsT5Wg8G8WJ8kU/65HUcthRwu9zxUL9tV4/kIAiAYMQjddBia1HMeIljBNjpPGsqWRDtij4rZZBFrZXVkDQ=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.7.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.7.1-canary.770.193.0": {"name": "@jimp/plugin-invert", "version": "0.7.1-canary.770.193.0", "description": "invert an image.", "dist": {"integrity": "sha512-NDTqSQvRdJ1Gplt5RpPcSgXnN+zpTYPo0I27ZydpeCZSyN4OSFPK91UAnrOL2kMe9zXSIAEF/7OxbsnfJOl++A==", "shasum": "c60cc5f2d95ad4b551535ee3e8af8a482ceaa3a8", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.7.1-canary.770.193.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcxqTCRA9TVsSAnZWagAAZGIQAJQwyl0RrjFMSvRWi9tj\nFKtImrMN/7C/Ht3PpdgbpmW6F57Ke28LI1B6Aq303Wh171EiNe/EjuPti2dP\nBiW6l0qRR69hIemFUMK3ZgsNGuzC827DlMw2mYFE/JbTNJzjyCTX2IuLyRsF\n4gjiPVHugZp+yBsvU73/H5I7Rid1U9h6lGWPHhyegWxOFCKyxt7Ufe4vCdo/\nB4GgeEUBfwX0pdPpslZcZQ9CzzzrjlTK6l+4CKeqV0B9KgFr3a1yY6mbOWHB\nRssbDvDrxhuP49mZopM2n4dSsmQY7eM7Ysr4D4D4PmDy0hMb+mSy0limoFxR\nN0XTh9yh0Ui9jTosbrYf1TNbLL5WVKknuWvwvbsr8jA7OheYGWXumyI34veO\nJLhHUuvMxGq/tdO4HmNFjOfECOoBecCqqCvu7zxoZdmfsaAr68JBcSiQHf+/\nQYS+DgbTcxHnVWetFaNYbJZvzEs5HKwOluzKJRw8L4d8IE3A6qJJLi7/jWzY\na3iX+CD+3LgYcCrRLGkh/RLrtLsjuhgQJSJdpVfbwcz9kjhOEDZ4e6VWYLGe\n/aNTzTMNqVgQeYMwrBTH4ZfFebOCoBwvVC0rCzx3pokAtANW8L04zh8B50wO\nDR+NfZqbYRK+gTwutAWxLI+GVHTo+CAmfAdwHyk3DKojsq5NGGEBt5JqQ+/M\nLYtZ\r\n=IuWn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBtJriUIw5G0orbfn/bRG/gwQnf1vY5auYlFOBBVdI1yAiAjR0XF/SkoD6gpRbX7jGItoaD/04JdESSAiHauRYZkFQ=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.7.1-canary.770.193.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.0": {"name": "@jimp/plugin-invert", "version": "0.8.0", "description": "invert an image.", "dist": {"integrity": "sha512-CiJkxXFkeFcv6n2NTFawDZKbflyt3TpQVJpB29yhagL6MXqt+cQu5FPQuFZJ2vtGGkthy73XAGWe3frbDnR17A==", "shasum": "563668c0c216713f96705d5c75dfac65163024aa", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.0.tgz", "fileCount": 9, "unpackedSize": 8195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdc+bDCRA9TVsSAnZWagAAExgP/jnYwd+tJwB6fOrcoPED\nQqRSKScgIdUii3EMNHJIZeqZMWPB24Lu0ni1Dm6ncqvp07HoCHmbuVceWqrV\nXqr6nrWJN1V4VQjeU8oHgySLtLp848jGF3xME0au0WElZmEPpcTuugf2Fgbu\nAbVUvr9Fm5SEu08I0anKG45rN8SjZtqaA/ZhY57QQmsXpjy7sk2Ay7HDcFJR\n/Uc/M4MXU74beyFitARS8pyp25mPZEE6x9naZQRIRJEJPaBw8d0o+WcrrwXm\nw69aFP0lxSXAhvWcxJuSYdUIgm1dYEwlRqBSO88aEst0wiAzU1AjhBc32neI\n9AOqYMvWF9R3rIydP2kMZf5I4w687QGt+27O39LjCm/h9eLAND9zSKzuKlwj\na6wGfPiU83UuMnpcwNpWnl+OgUe4x+OPzksE4lLGPOOR/TLna8udXKDYWiEo\nEwj/kwg+Ix1veQQ5gW55SsReZ0J2E6yhNwrJd8ql1/lpqQdUCgScAdTuAKFi\n9zFtg1WhlLjGHpE0yx3r/0PdS3+/A6g32ZwAAjwzzV55bDnuMWABNFAW1PN2\nDV11/S2rTgNZgG1iaBqKy0X2Ck8mXuHRLpmBSAQs2GGdnwsjIrtIkKhMkUT8\noYxH3QYX27P/cVuBS5hg1ZWmCQwNBxOcozBlBFJkG03261c1jS7gkMyFFW5W\niYnO\r\n=Svc1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDCQVDH+X/+1kXhvUYEG2jDHV0R69popinRW9rZmYADbAiEAvzhPOjEV7WLGLrfsd5otlZmmi4CWDIx1R6EppN1u1KA="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.211.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.211.0", "description": "invert an image.", "dist": {"integrity": "sha512-imNENLgYWBnlryVCrS3iiULoT3/OmqV0WkOlkXhs/JE/L/LjM5QBwq13lkqbyrC42+UTWNtJxSVEtDi/8FsE8Q==", "shasum": "633ed82d1bbe62334b51bd83b303ba3627b6242a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.211.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddwbXCRA9TVsSAnZWagAAJaMP+wUUrL3bzczOmg/Jjk+t\nGVoem6w1naq+9IB3f8mSeOQZjR1O9rSsvp7UsAVpSxV1alXBhArjc+U3fZlR\n9Xqf2JgvfOprjxqZsnIDE6SB14ObgMkOo5LL1gIwhcD/mx5+YwE/QXLZUd0O\nQTDbom1YyEA0zFEjEAzCDwPLaucAvR818TDlykALhdPmI6H5rOdsY8u+ygwG\nHvEPrdme+RVEwnqfQNthvSjA+GJEkTvnLj+L83p3K0dv+HXQQyqqwSaMdVT1\nEIYt0BAOFHH9iUAHsHr7xdohBjXpOwXPDRu8sBFkugsg01V8THTQWuGCxZ/S\nSjEW5u0Jr0+SazeTsXdPdKLWRu2iGXWQ0dYADf4U6M9rSbEP9yHzE/XaaqAz\nxR3bMlKP1n57XhnfoxaRszd9ZC1LNmHmT+In+OJHFHh7q6ENAMZVMHG59qdg\nSFg2u3TDbqIaf+6JMMYKrhB8AnqGofuYrziw9IlQ3iH2ifqrEH2EzX9+3UxN\nLCyqY/tNgzBrOVZ9tLyaxH/bQTyb1F15jyrjq2Pt7ZdI7r2IfPxxPDBUYTG3\nNfbUrlD54TStKK1Zt6N/0sMYVpK5Vo9ThjXtmuZu5AquGEyD/JWaSRmKWBm1\nX9fN0lyvWNAufjJf+Iw68GxG6yDvro1zzvUvjkphG0mxjh8GQxhgr7Ym8JuD\nI9CG\r\n=ZLDk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmxjJnmP9VMw6M2T5TMaWfwzs5tu3sTPtRgVKKUTEjvAiEAwbJiYOJ8DIMJT6Rrn3C1EX82NUnXIbFopvbnwZUUi48="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.211.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.217.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.217.0", "description": "invert an image.", "dist": {"integrity": "sha512-Tc3jlMWI7c9Dvd8FGbWRgSjFFlokUS6tXs7Yn+jxeHm1xNLvdHBQwKYOMloM6TcwvhuszYUUBqSMWWYo3obwYg==", "shasum": "5f15266660a705974d2d7e5949d9c15dac39d6af", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.217.0.tgz", "fileCount": 9, "unpackedSize": 8186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddxZWCRA9TVsSAnZWagAAN5QP/1p7Ff0/CCO2zHQAS6dX\nKUUXEmEvBtdwO+0UpPZ2OhBAmok5Mpgh90KvyvVZ86awpOykrh3PJR4YCMZw\nhtZ9I8Wv6n1uq+7msKZ4UnJce8ovbrauUfzV2IylxSWUM4E/S660cAO6cNub\ndei8XdWP+ggLkutXIAQHRKYLWyYpiK208LHvFm6zpLIwHQmVfs31TahVz+nT\nDoFRMRyfm9qI9m8Vg/C9aPv1SGURgqmSPRK9McfuTM7qX3vpfzRjyOo0R0OZ\n9/aCkf4gu9UNUdPS0+vX44K4L0NfD7ntDJAYTRI5COoqSzgv9N6PGzAk6VF9\nq5PoMxJhMTDgTxxZ+7Dr4C6TOOS9b7vy7XfsQYqYMRUm/wMORjDLXNovA1JV\nP2axBYnhsemhHNs+u/eIiCivo2Q2KzIa1uthy5F2CL7/0NTRW14K5lUdSGn7\nq0MBqze0+d/Le7GltaGC1Y1Ct9QgdvnhwT7nFW/lkj4nM29PiBrcOUdiElqU\n4yy5DJxrsIEq6Hfm83u+qde0slLabPUmMHwXyxQjCJsTzDN2rpGJ2q4rmhMv\nt4wSRksWP6c79plEYGSY7LkpAB0OhtF3sjMR+uQ6c5JfpI66SrTr9G7laAJ7\nhAHDc8cxQ8JOSe2zDfY+m582ULaksa2ZQj2t7vwcC28dWF1DVkNhhWo3gHJ0\nOoEa\r\n=Yg6s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1JkxULkqry1VEKZQ/n0pOaAlrvQT+kHHW/r0xH0n+awIgeBlNWuIXmVth+PEiofDeTkvT5tCUPa2/do/p7JfCl8o="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.217.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.223.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.223.0", "description": "invert an image.", "dist": {"integrity": "sha512-OxK4CKJvWZ1IiEvGFJhUQ3LtId9wxfjtHv0ZuB6hLWhMAba3Cd9kEUuCoHSRi8nOVO4aAWe8Gs/U+o/HZCYz3A==", "shasum": "6b89d64775ce18a0a4eba0db49faecb2eff3c8cf", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.223.0.tgz", "fileCount": 9, "unpackedSize": 8186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdd1IECRA9TVsSAnZWagAAedQQAI5+cdjEbfFussFoX0cE\nLaLHB/2cZ4u4iv3PTESeycxIwMfcG5VaozdiP/rT84hfPFp2jZ4urZz3z09q\nHkGaIVkaQHl7OLdwZsZUBrNFormxZyWNfXUQyGWNxXcGvzT6oXowQTmfAqIy\n/xvTJJil0Wus5xIkEvy6lzwDtXnzPW/24F5CkbNdsQPuzroyWDzVyw2QYeuf\nh8HlKvHwUMkyrphAYl0tpgsBOIbEfrHxiQDpBF6ZSz7VYhXB3yJYMZfbG0aj\ncIPi5l6zx2ECosCwW9hO6rBGRHE2QBIexhRS1rWLrRWVBjMyjaX+dpspl6eG\nb5e1+ZlrwL1dCnoqvrpy6OWgKdjkxRu4ZGhsUyXidpj/MSBgas5dxarxsN5Z\no6RDft4YYgoKGTFpd8sc375Ick5WlWpV/SeIJ1YIALfC3bdd32FFv3p4GY4r\n4lCwMWP2bcEqM0b0abyJFw3NdjIlXUFQfmWPitb9MDp/ZOSCve8QqF3WnplR\nPG4mk49coMtzx8DgavqE8PZ8Hl0h3b8nkodjZhGFCnk9IgWlPDviZSmUIXrl\nieH+9R5qK9iXs+qlOPQLPmmSur/4r9PAT2tM036xDO0oF9r/RXVkMsGMHSQh\nghHs/p6XL+WcClWzYq5SJOA/QNyzW6tNzYG+wQJOpGsWoRMSAp7Np/BklfWL\n3hmU\r\n=0iKf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCx1sGRM178qgaQ6DSCu3QQqRhHypu9deIXR9eUiathNgIgW5aT/DEtMk3RJqkF6hzJKLYpr6aOPPFh2DtoINnVey8="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.223.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.232.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.232.0", "description": "invert an image.", "dist": {"integrity": "sha512-l5kRaIB5e3S/Nji8HdEGbFeDREAqnwYMNlQdX5OV/Lkl21owWxMs7ADbhXDqrCiqroMNY6kW9K5KplpTKdGZSA==", "shasum": "fd8467d01141091fb96363f9ac331c89e3f22cd5", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.232.0.tgz", "fileCount": 9, "unpackedSize": 8186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdd9dnCRA9TVsSAnZWagAAGykP/3W1S71XOeuuWYZ+1z4l\nfGuUR52pgHsV5P5VzEd2jW+qLk+RP+phriwhBH+7zfA/1UY31c+NTjfeOdQz\noc2CA44gtl3SXPgbKeQ1kVen4dQo9BvDjOAqgRBB2O9Jtbn/AVrj0LlC81m8\nMeIxesn71srgSHDN79baoyPrQ/fqJ4YORuzVBmGcgfX57iB5KF6tydeYEe5g\nZqc8+T/wZcUK8Kp0c7ZgYvg/3f35EcFnT/LESEl3e2mk9rrMuZoBZbcgPrd/\nBUYMB/pWMte8EdbGmk7jOnLd9YEamQL++iHe9X6nWfTwCr7RfgP6qTGfoL7+\nk9kQOrASws2ibk7pTHJJ1LQ4HqMUW27Fp18pZGvoC2/SwgO1Mn18AYu1jgxE\nWxiVDLvwZHmsS0N4+qYpHWUE6KjB+9EiLpAB2AXSxeyNhg0CXNaJaKzQ1aXg\nt6b3R6Af+3+XcNBMS0b/qxx7ewCjCNODoRgv9FeEAc2+5Wg6GTwSERr5XhQe\n3sUjVc5wlAODwKMRSire/xxHT0u4zoK6aUETiQh9lrZw3VfzlAHUbk695EuA\nzKAK1UY8FGFrSG7sVdQsKTRSjPDPIXL4YrYioribLqvR24kgBCONQJaip+B4\nz7tqVgccfUIV/jbFmWA3l/12D3JR7JXbLrQrhHEDW7k9mH+y3NxAKBtsxsmB\njW3+\r\n=3I12\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcC15Xc7kCFwWUNGTnfb8DTgEYKgVyo0PfOppwInAQaAIhAKlyflSxQ5cPi0bETcibnh+NSb11HIgeSkgH7gSIipRQ"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.232.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.242.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.242.0", "description": "invert an image.", "dist": {"integrity": "sha512-NAKNAfh2s4+01IDGXMqbUPb6n9AJFM4wS66RnNMGnY3Rxy7roWugrChT97GWvKYGPd+3jvnlIznd8xSZnWMZkw==", "shasum": "b7a21f47cfe28f84ae89a044fd74faa0a5c71af0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.242.0.tgz", "fileCount": 9, "unpackedSize": 8186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdeD4XCRA9TVsSAnZWagAAMLgP+QHY/srhgd5/uga5aGa4\n6K9zFcIBkcebPT5mEoZtGArUR8nAgqlTnv7uL8u2oK1AMzIFabXNE37gZuOi\ncae1kcQp8KfqcfrZYcomBP2RIAlXz0oWJISqB1BAV/jIGvJN1p+cpnns9O6T\nCjuaQQkPvyU/dONFm5wHRzDfYI1NUGtOd2OlXqTtxq6SblV7wJShFl2ASWe+\nvJip3dhW2+TIW+XBm4fBnOi9/tLbDJLai0Ha7LeqWTFTui20MJ+JKXd6M1ga\nCtUe/5KCzsH4sIy7N3mjZlHn0Q0XY6bhAEzFjZbmDymUa5N6lvvxtxT6W/7/\nS0GK1oBrvtMWsJJ7c51TRIJDHBfgTw6aKpLDAuFwWJ4jxvKLgTL/sX6lgY5Z\nrWNKzlgEKZWh8z3ezKH+9L31qZKFZa1pzSfcbL9HvzYoZFg+QFcpZfwcgZqc\nR8PdnBjwdWkfcAktHoa1zpitvfqDbpsfWryosP0j+DsMlED5cIgCLjou6Urn\n5lD5waDFyZwjCoO0xeaxcTvpwPwYnRIwae6WdV/IMHrZIKoTs0uitgFE7nam\n9nWdh6ucbU+BXN4I//BuF7Jo1HjrzvpnsW7Eu0oh2gGF2z+AYTuo/2Tdx8at\nmtF+yu7bD9nLyDSa6VB+3tXH8YKejiEZeqSKsnEsJnLvnhK9F7k0JacnV+nt\nWojT\r\n=3BR8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExZ51Yg81UdeIosI6XKU2OuV7AaFX3Mp7pn+Xkgy+rHAiEAgNkHkv6gYLhBSOLG8lewlKYHSB1XI3KxVo9oo51IhjA="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.242.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.786.262.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.786.262.0", "description": "invert an image.", "dist": {"integrity": "sha512-97Pq14yfaKDWD1m18baaeoObFKCOEZGJykZIAvTvpbsQW6Il9AYnxZnivUq4bM5Omgp/0/uDQ6cHv0vjJmzTaQ==", "shasum": "d8a7f313d084716192137efd0085687609275731", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.786.262.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdeHkqCRA9TVsSAnZWagAAIPcP/1rDu+CKoxGsWnXHaCwh\nhaIa4tUgKvZuzIeDP7AMoz8hNf4k8hIwyMo32V+Co6ks3eINMQzGfdeQURbV\n77Q8IoDTJP2llv9q2lWAMQUcSh88/cr6e9MdVe1MTCfrHYaTiM1y501AjNEI\n6jtAF23DBbiPGXgQ5cImozVVTKNzXRlZmbhXfLxLEhMazUFQa1g5KCNQ1xin\n7CEeoID1NyTOhj3UxCnaKSKyT+YvrD+Tf0UqBB4O/nXIQ0N6tosNiIusXnmf\nnClNP8QskHHY/gP2WVcHTtfO3XCA9q3ODh2YUGrmXlf5/Xfi3SaA/wm5tDm8\nJm86HakabHsb9xvxuNHuMPknSeN5SG4DmITnk+peAKRExeSZSQB+7Eir3880\n3jaCK4uq399YYN2eQ+E8wr8jFKBZZ+lUEmBUkxw8CMdBCCwUrmXjRckmvluo\nHyba21a2mKvvVLTfk7C+m21mCH875k0dJUpqpLIKx1vVSquzkpgzOXxRhfKs\nrxi+iEDOE/u1jbUj3N01+6KhhTJrm3kFoEJvlKzKm5qmVDo7Pu13DDaDB2qK\nYNRsaGDFxx5elsZh3aOcIfyJ5TxXUDXkoZfsN5QRWnEhkq2YoFSq78fBL8rx\n3TnuoAxJ04iqTb8KZvgDnLqy2YBHrRIdzYUoHIx3Z6dFpjPlicU+SaeAew57\nXNjp\r\n=eap+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDxXdLXAHQ3eEam7KiJYIzfc9/dkRnO+bod8dwrK/nU7AiEAjHxfxp5FNdo5XgI2o1cpm8rlBZugdpwXVcoD2pVAWiI="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.786.262.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1": {"name": "@jimp/plugin-invert", "version": "0.8.1", "description": "invert an image.", "dist": {"integrity": "sha512-zkNuho66C52C0+VLrPNTPRsaoQM9SSk1w/srOECIChabK7h5VxeMbU/hsRYKdNg9Hkzb8JKG5uMHNNXeJXrVCQ==", "shasum": "9744fbe1d3ac1df5681b687d48a0191777f5e910", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1.tgz", "fileCount": 9, "unpackedSize": 8154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdedMMCRA9TVsSAnZWagAA2VUQAJf4PXqLNV3642LIzWFt\ng9EJPIPQVhEc9STQ9knnEr28MsvsseyMpKbKBCqDqbKfPrfWuNWg2gDuqPHD\nQO8GF89TdX+UKCHY8lprcZbRjbhfSmSR/k6Q6Ii5XgZ+cSOgkwUiaNS0R96R\nY3nSApMGd9Q2hMvtAyL593PkXxWUy0s5QPrwkcMyHTral0SklXJXekvyXs/7\nwzkHyPc8deVHBh3u0ujxXx9rutp0qF3R46bvhSDGdV6mM5H0W+aF1/iwEYRN\nS+vDgyViM32BrjidJIRNApXeuTWd3g7/KtfBccI1RH2aNYbAGIvevDTAXi6F\nCQ3m1hRK3fXaOzqewWxnna/894jncGdP0cYLqyawetTS1hu4R1/YHfE9Q2X5\ncJazKoariJbVhOtcWMNXAddtEJuuVtA7GWsXfVOevhuz4EjPBhmKKgWMnk4F\nAJgrqVqMnTuCK4xgcLAtopqm9iBPbo0bXC820U3Psp725ZdqCSbb+PeSb+27\nQSB+WJyeR2RMunEYQuLoCDuimIMU9hqvRI3XGcBm0s70O91VnErqUL++nN3d\n/oiyqY1QQNTH5or5jyhwk+6oT4Fjouz/AwfKkLXTgY4n9qPZY9PnHTtTC9SD\npTHFmREo6wts0Yzzl+vs3UY9eM1WFJLU6hclGJmmymwWBzFpMBQt/L6IQpwV\nL0s6\r\n=mHxy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5ErkgLrgGuTst1GKUCzqk2Y1Es98vZj9dverJ2o/ngQIgC/m6ZbU4iBfKumqdP/lrm1gNT5IwaUdQAGrqYu9WaeQ="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.1", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.791.276.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.791.276.0", "description": "invert an image.", "dist": {"integrity": "sha512-htR87MBOx8Nqnt9uiddG26ygBrIsazfmsubGkXUsBLewsaaD8ESc+wHMGgcWKP3TpP4GtcF8jThbYQA2NzK4kQ==", "shasum": "c90d8ef8a6bb0f2e5d5c7cdded887240a71e75af", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.791.276.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdehZsCRA9TVsSAnZWagAAti0P/2t6DMLAttNd0vgMkzEb\njBnU0M72v1eQj2FQJSIpYa8LpRupbJVM7BJ/BIRo+a6p0qjbXG0TnFWC/cyA\nF5E+kSD0pQWllPtC45EmRBrKj7YlEKWt8XffneQJ9w+5TxWAhEG2F2lQAEQC\nwHPECCiUnas9C1yEDAm23o/ZzH41iCUPFReHJKBNZcm6BZ7oP4Cy75pA9Kmg\n8tI3a29hupveGz4hZZXItlpFHiybXsfDBhiVRF22lj8GuazE6LQjZYZ7a8RD\nugKhGc1Y9kqOk98bGhhC3syrppjGvEO+GsmjS5qgfHa52qVCIziEQ/2fapVD\nVFhkmICOXCDwqfgpOPCBTOqdHqYFGYU8Y2HbAqfOQYLH4SD+K0JjlJmlvj9S\nlgKMXCv7BAEAGIdD6AKp638lbq3VP1U07Zw2K84vQIAIZvtyIDIKwp5pPgos\nijMZHSrgsDicIQNAwgnL9+duyS3evKShwwR8YeThBVj68/5NfrCvpsfVQHyB\nPyHLkkCl6S1w+rikbAvDaHSmy0HtyndarV/PTfFqicChjr5G2MgAYyB6tPJN\n8gEwn9qg+zaFVwtgD2x54UzCmdGdSzRrXaQU35FYdGLjhqrHayAH6KbijlkC\nOBDk70UdDBTNNbzfMeIMHKgNDIFaKWEUjO0h8kOeEoDlEOGlnLb++eTWjST/\n2dvJ\r\n=lVtC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0Iva5bJf3nq6ERQct+Le01gNldVqAAuCVmkhgYDcyygIhAPuWAVc8+pyID4eQB2jGtD/CZ1JdpjdAHBpRxvjD6XK+"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.791.276.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.283.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.283.0", "description": "invert an image.", "dist": {"integrity": "sha512-L+OZI/AID7CSBijokRChQzTm29LTLDSrWVbnnFdt3V2i9rDg6qJNFnPxPf97pGjxx3/U6m7k7mH5DNwEyA4BmA==", "shasum": "3f5f6de4bde83f10a34b575975f81211bc049a96", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.283.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdemJ2CRA9TVsSAnZWagAAB4YP/2sUYW0T3vmIINcwkaS4\nOI4Ag5jt2Bi1/8SAXc8ee6tZjYY7GexLk7GQJGd2Urgx316BO1wOySFM1dMX\nYZ0LCD4lcxulJ6+D0b384Wvvf8fsOFjOgrIQGr3qG58i/qUo+IDr4JiMl7FG\nCWAvhy+4+Oyc6SzJvETPNIrH4LhvaSAPceoKsGChdDu/bCge9d70lhJYspG3\nZ4yHhzybx+lAbr9xWeOYMAeTtBPrqIopgKNQezFrsyMRAIstlDTnOGuLI99L\nfr5sWu50Q6lS75UafjR9VQY7E2LMDXuL0VSHVEgqwI/9s4QPkMretjlHJBE3\ntIvvGs25aPOIfvC5Z73DMqY4uMI7g1Ya1Jpr8tnsFwFd9RTOwWXiyH2z6tiV\nX6Q8set41fsk2wE4etorZJ2UPmxV15clrtseWiLgMJezlcAfjHoE1MvTOz/V\nQ1XDxy7wTv/73bdBnaH3ROIWjTuGtEpul5mVs8mtgUvhBh9tiAznEvs1LygG\nW6Jy/d1o8zosdC1deWyqDyX9K17ancX7YhqiCQHXq7rxZerBS+iboneNcHtq\nRy8I2TbZHFl9fz+6D7s9Sva5yHkhucqoQ+8OeD8y18pt95UhV31cjh93W3ui\n/03a/HrTf+8+4G+yXjnc3/+2SPgx+z/AC2+/9LjhIPXrF3C8Trp38nsYbHmc\np/Lw\r\n=yVkS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8EFC7lIciSnQ4Xobhf8TWM0IBl0nKVjkqNgfXs0CU8AIgEk0dFvICIQ/ggOK1a9d6cx+5osM741nXXXhoYFsxTCw="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.283.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.290.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.290.0", "description": "invert an image.", "dist": {"integrity": "sha512-gWQ/ITDyw4c+zRhoBDSAoTw6sEX8zG9hI+SPwwHVuAdZrIyiqtoLCOLLpevUx6IAQiYUHoPz+0BhHObxHZkDBg==", "shasum": "50b7e13f14c8f20b7a477b13e67851e275d318bb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.290.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJden9sCRA9TVsSAnZWagAAPuEP/2QmyTu3dq+5oV/XVxNN\n4MB0m+TjWIWyKcfoEClWDe4BWe61lga0HNa8Kf0aIidTvdZ8adniCY1Ou9IP\nrTTH9UiEV/N5Ep7rP+kTjWH5j9D3V6MMFHO1LDkw9lzwjNaxQ9XezOqL8WS9\nMIgAL4boeh8Vwg+rF0QRhTR7+Eq80Iarum+ROSW3WtCmTgPBhARB4iWU5yWm\nMsnq/mivHKK5Md2wt1OCRZlMUzsW7AGlJ1/BmKKQSIui1ijjsBmGXKpzyk2T\nG+JPwPd5mvfmBXAzA6tZVIvCpBI4VpOl6ZSgqMiMV5Fd5jrkqyL4UE2LrFQi\n0A2KnB2K32Ji+7X3bss790E+/YVOr5Yq5um+c3fahh/12lU3jHibuTSvUI3w\nB1JAXLgi3gJb2gtbz94weUyDSLKZlbn4qiB3wViin2R10i9Ke/ErFJo0x5/m\nqrr1K6OO5TpUezt/OJm1BkJrButmS/bM5H96uuMehFKF7MjGMRwDASamA51f\nwlVIOKqcFspnL0zA3TEM8jkRY7k3mzXDity5YJDDLEu+U3zYQiN1uCs1+Tcy\neRpJPP2ZlmiCZpvZxO4OSkRrh29qoxSOfP7dFcd058as6/AFLfpomYhY9skb\nDhA7FGxnX23zWoDD9+Hd3v8WO3tifj5GghFuZUzoLdTdQQVCxF8gvspXj26C\nmPrR\r\n=Zyz1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0OVpljjaaOjVTfcCAjenYbIcblgO64fKaKS6LcCUQfQIhAJ36tYkAqJo3AiBjAHHNHJSSywfw9UafbJMNGPxuDlwr"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.290.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.789.301.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.789.301.0", "description": "invert an image.", "dist": {"integrity": "sha512-ysZnG8a4n2xhwtw4nw4hs1SSCdv7vJPNfY4PoCetSoYQ6e3J9PfvCcpgBmGIUxAdD/G18xP0csHVzSqkEOSwNw==", "shasum": "8210f10ba0fefefc62fa7a256ea491b5137cd75d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.789.301.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde0epCRA9TVsSAnZWagAApIgP/3PQ3gkTjVMWpVTzcDGS\nMsUheB67UVZxSJwtNfeDUmfzfF5dGP3mGiTUXWP7yBra3lIWX9RuWjlLwTVZ\ncxAiPDc3LUh616emA4RFblj8xHValRdyGpTtMhMrVnTzQBuz+rzHgihv1KYq\n5pAXvLi8dm9KQwWu9606pkgGu2fWeSQhvoGNDb7t/cg2Ie6z1DQvXitEBlH5\nY7K3OIMveOnqSWZi9VbAb7an0JxRzxYeUdpEEW3af/KbZq3xb77soWEL8Pdb\nJWucsiLiPaBrsneAKGx/6t0ETGkPUMEcWegBZjr4QHTvDvpF7mfwhU4KgGOn\nItKAb8/I9+VmAV0msobX68S6TqRYZkdkp+OtJ0UtPIItCGg49xdAuvS9LKuD\n6VmhGtpz5HGKIkn2lwe40YC/QDoY7Os4k5jbaVyl0wIi10g0BmSSxKNgg1Oe\nHQ6dVnUR3Yhjb8pmYkr7ZiHu7JbnbMTA2Bt7b8bOz6ruQLveVUEUn9xyAAQt\n4/oK/XP3Ubm0Zyvxm/h+DbxCzOLVCbRpiZRlGe3Yz8pXDSmohr1+lmXTNWAv\nvvy9oDHHVttp2wK1q+eL/oqGlzj0bvwCovwPfKjruEZGbh+q3wHCfJuIk4TS\nWCsqMvUo7krEwUWqVXmG9Yce82Lyc0VcSjodznrJ2WHX5uiLdZkGzQan7Uf2\nl6Q2\r\n=9JlJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLAdTsSbwaBR/FtHu/vLkZ5uvLF4fM0lqtNtC1ZIejzAIgFMJs4Z88mT3/vwvxFqRqsdKiGDTCzKu0wNsrxxbrzmY="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.789.301.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.794.308.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.794.308.0", "description": "invert an image.", "dist": {"integrity": "sha512-GE9BnYG96BNCMcDxTFTUriCyhQClOhbKPAo1eABlb+MhxOn34z0hBLKTiKqPnAHmveMjJ304J36KtVoa+DzoEg==", "shasum": "3a5dd46f706d94191681cacbb13fd0f30f76ebab", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.794.308.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde8ygCRA9TVsSAnZWagAAVs8P/imgDse6wfpSL0kLm1g2\nrrmCfClzw4Jt+7G13Gj39C2KrSJ/vGR8+fXV/Gn5NsHnEAnY6OxFr7gOAVXz\nMscf7Sdk5Z0uPYNt62sKbKSENcRtoPD/RRlZZ+T3fB6uVYA/1l0FOjGAMezT\nemfQr0msNOKNX9VvrGVPvK+2tslQfKdYxQ6Btb7cqq88hZuTh0S3RKDlQCyh\nfu+Rh7gS55txlJlQe7t/uO/I/f7wKkktqdwCfl3ImH2qYErcq+o1+hyhSJWl\noI/173TG2BLFVXxpqlaf3/pF8pMGZREfGku9NHJhqNSI8P/lUxbgyVTLzfgt\nfrNCAUcXxqOhma+Zc2qi37Y9f1MO9T6hJTrxORj/Qr5cbaWxMsxw1wdjYa3+\ndCuO6nGvTRsks9AtgkFtBUv6Fc12jYwbpeKJ6SZ8GD5Ree5QkBwKvmZeb/kl\nWnDw4qHDiX3byqv90+CxrEZXzc5mvYbyQvjpR8pzFExmrWrbxlcjG2AryMbN\n8NLYYSbBtdSxr3hxKL0PeogYcMZ1cdMrytHJUprrmfhwkOW4xR+5nBDUog42\ny1ZoAam0Nh1yItdgsAXMpoZxyQR2UCSSZMgA+Z7ZZD829nqjk9pWve1Skb0k\nvmBLKjgAYqsQViTMAbXtzWKMv4bLNoUsWq6ZLgBwc272X1sndJg3/n3128ab\nTV7Y\r\n=/T4b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSU4kH/LbzS8EEbl5fzsudDaqie1+gYDTyIgYjkJ9CmAIhAMZvG0En7pLkgEbDpjnogx0MwD74de2i2MhZi4QwII5E"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.794.308.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2": {"name": "@jimp/plugin-invert", "version": "0.8.2", "description": "invert an image.", "dist": {"integrity": "sha512-JL58rf8K+ZMKOrHR66G6OJ89jvtK7ZLSiiPdm4yfEiHCipPIpSobW2yPQePn6bgBJ1xLM3LfDINsGFT5NwTy0A==", "shasum": "b74512bb5c50fb3a68b0a809e0d4628a4631c626", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2.tgz", "fileCount": 9, "unpackedSize": 8154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde9L6CRA9TVsSAnZWagAAEp8P/3yrr3E32pYJoEr2MGKK\njWjDvJ3Vs9NDkSNcYgcwhUY12SHSKWIG0U/xOhsoUfDGPPUmIk9VkQmurZWp\n1+X7+uMa6C8BI0W4QQ4hc5k2ZYdVCwdWekexi5Iwia9xmERjDTToXy6WHPyY\nw0J0et7kVc413sghg4z5eJQK6ISes5yNyDb/OTT6TPnx4Na6ejiziPUpiZar\nq6+A+wRaaAAnINeoQ8Ub85fYu9vcAytP6vAi7Na7r/9buF/8E1bAMPHGDGqV\nczCbCYh7PtFx9+oJR6KXQ31dXyQPFGguKqMq8UBPL5RH4p0LXQly41KdpnHj\nngDepYE7coqjh23nNETXVw+4d3YFPdVjlbB+jWMFJ7e/T4oKd1eg+OH3kD7N\nqQ9euOEGZkk7vjzwxtHwvUPLLVIOL6gBuwuE0WZHfej6TiUGqTxmhLEr8uET\nhsejULMndMOPjM+oZxNnB+RjJOH1AV50EocIMHV3xsvwwKRccF797R9mj0X5\nU1XcYw/8Q4uLwDiSqnJnKOL3oFoljGeEJ8+UTsDovyhBkXby2D4iiyX6fng0\nIlF3HwqesGHIE69ZCzxV3wVhqVxDhrdUo0ZzOWUCQbGsT7SL6EpTxc7aVekD\niBJUcaznIpZpWdicdWXyxpJW5KD3mDL3ocAI8T0Oxx9MPsplwrc+SLFELyVa\nvurF\r\n=taTQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7lzhlbiSfbN+N9hJ3dEEeB+BHuhUfF8viJKFZJOEmuQIhAPG6LvhycOODbAZLy71k7GFpz8hu+Vh+3w6oY2j47vRs"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.2", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.332.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.332.0", "description": "invert an image.", "dist": {"integrity": "sha512-EBgkeHFh6rgKx9lUyGUvJw51xEutYsS3UWPi8IPYL52RgW+HAGPFHxHSMJM7DwkWq0EMXu+WM6r4+VBfOUzTAg==", "shasum": "41d3399c6ad8bfb5076cef535990e4e07f3979d9", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.332.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgSdiCRA9TVsSAnZWagAA1xgQAIzak4E/5ocrcqCW6vdI\n45G1Ms0+WWhjY50s+QzHF2vpKNxZI6VrskQoz665YSUkXCxzJdXBUDyZyUr6\nt3OAxoGPsKt+wy0g+ZfIRTVEEzEzIc2CJaLWjQMnKjoC8ie84aF1oQ1HrojX\nsn1/MK1RQl+zh7dYmENJ4OpMcliQI2aBvwOG+sULmJV4Vsg6FhxfQAZCnJ2m\noxJC9wkOumX7C9M/8VGeDsAld23pmveeoZBka5e/UbChwkjdpfbysZ8Z6qym\njLKpAMZS/Gi240RBlB3uN0eV0GOsbtxGVYUw/hOFQWltrtrvI0R3YIf+vdie\nrVWa+TLShD0UzSso42+85mEXNey8zqQzPzqSYtf1C0CAN3RrWe667BK8u9uN\nK4NNq0lZwTLaNOQtSxqQ6YnZaKn0aAhB0lRzRwY32fwEd19AW8a1aNkbJbPK\nI9xFShq1H9jubecmKjPmU4CIu3r6dIoE1SospUr43K54yCVf5B5vAdOtM9Wt\nmDRGQSxvzu/hpQBtWKEfw0aEzbbgx+p3oCde4kwitRoUWluuRqHMuSpJJkFe\nJSuO1Umkx0F5mXijPoFAKoy9/gmKEXYUIAxOXBCBe1QjwRVpnVL0eFW2/EVt\nZ9ZABkA9LHOyYQK9AEg4VA866Ei1o0qxxs69Zt9LsguZAZyYJWCd/cOWS5ri\nm9gi\r\n=G00E\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrtVP+0sFmDiuxs+GxIopJCUjAI85rPiEfykRVfvnvkAIgdKv2/BsuxTtwPIIGGUtnGQASwr3QdMlH7p6n7chY/E8="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.332.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.339.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.339.0", "description": "invert an image.", "dist": {"integrity": "sha512-f0UaRypIrrtlxkDhr+pIOWiuLjop9BmvWYS0wrn3EMURakYcSI+NBVqOIr3vgwAbelRE5sIHol0+HQEPIaheJg==", "shasum": "53ddba653650289929f2c7a77fdf09c53136d01d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.339.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgWXwCRA9TVsSAnZWagAA5E0P/0FRXNn9CpMcYMyrrHbi\nin4s//Bgs0S8ZGzFb35X1uornRwLnjeWByWCnKoTUzvhhqCQKyxuv/NtFaSq\nWCar0dpmYDIVa691DI0sS4UCYFrKxRaS0z5jSshr3lVoxYwOByToRZOIiHLn\nqMro3wN1kY7/KlbUzqjMy0JymzYqUIePakfFJvgC6BE54Q+1sjMXZFyy+0Xb\n0oPiGxLEYpUqlDghVC49ZXiN8kURIolVgZwL3GlEEY+1X6k1j8S0owgjNF2x\nXFEN6O+T3jR/o566X4x05UrMBYUY1WoGP0ro2WteXD4ALUk0vNbuFwZ+MLzQ\nrpYZErlFb6cSxf3MEdgm4ljC69GRIw8qBwKWrPrPHV4vPXBxPzfeqTNtwheL\nAhonFrZfvBsJdnfZ4HkNzsIuNrnWBuBA18mg9VUcNiANwLhtHS73vBNqhU5E\nruyAYYqIXWTIRCvA2PV334B8V1xb/5VRabN7Wl7SSFxEFTRHskbu4xyO8KW1\nNDc51zBt7D7iXMieWaVhRl1hUc3fhB35UaRyRFeUWznOUuJNKnzTcYNb56f9\n5IBgJAp+AT6fu9zhsQ+Ya+Kpx8sN4yA+4prYk69EGre7LPdXu2kMmhXlPYdL\nhb6y39bS4QI8aQ7CCnvvwhvf0bcRI5hEQCQmf7sgBxBxtXUizR4OCq2amE1l\n1ynN\r\n=CPoQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZoMVbIRB+GxImcxzZnWXDk4TYXjSQCgPN61KObrGOEAiEAjG+bwhQzb7FT6pKDXvKOXlt8o8XmV7kJo/GusbFQTRw="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.339.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.349.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.349.0", "description": "invert an image.", "dist": {"integrity": "sha512-Hgf6G4zgqLpithvSdmRv/c0ACbW0UCXKEqyB31y+vuvlRvKZxx1jLTzZG4vukOcYLosMW0BuWYAKccM3MHlPzA==", "shasum": "2a6a2afdcbad93f6b129e4ccc596060b40e636fa", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.349.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgWewCRA9TVsSAnZWagAAWMMP/0d2QWsAb9bRArFfUM8v\nhM5fYLtCatF3kxiU/bJQZ75K7ST3AchhT8U116VBE1EhRwyNYmNgiel0mNYU\nbtQ4VRawFxEwOKKUoM5tBk5XbUjhv2TECIFRkjnBA6H2PQmVEnZ6Crxp7on4\n1dAC9WOGwrVxz7FH7c2xWlMdGI+o1bJleoURrAkxth0vEIpu7PcP9ng+TRLW\nxd+N7b5Z66HcBUXSZ4eI7S+HBC/TbPIxcmnU7Ma7Dqp4bO7VLnC2pNNIWNdd\nC/neYFkjZi0xOsgNVqn1oNRMoHU608l0VmMieD0xLTuUph+Mm0/jkWG9/FdQ\ndIGyTXGU0+PxfUPGsPc8e+SyLucO7BJVvej24L1Jb26WuwWWkh/I/hkKwbBI\nRrxa2aWXGNh7xL20vQp0JcCo8wAENaLVbftvzJO5q78GolpimiyBNHpr3YhI\nVU0itAoGs2gr4xBFCi+J2U9cSME+UjQQZnAoRUDyHEVI9F4zIhFSkl0H7X2x\nGDXLuG1lBBHF3q1muwMlQDCj1O15IbCCxKDrXx94c169CAgKY8s8lhgBOoEN\nlmgGW89++vqw0joojNG8DAflPPIUDCLdt6eS/KW/OPBwgh8krkBEmKMD7GkN\nD/uAblctsHnjjsD96T/eH1XcPR8y6ctmSVcHjrXuBmr7G7yWwpxQ0LSVelKp\n5xd8\r\n=ZjmS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8VvRndJIKSRaujkUAZc+XByK8wOoPxBDHK/zKPgciBAIhALsN41m+NYV11JoXiMrTMFqi3zc/oVYEeoGaRMg/CTmV"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.349.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.353.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.353.0", "description": "invert an image.", "dist": {"integrity": "sha512-oKvlPoiGaT3Fs++O63CGNfHaiKTww7tf+Ct9BGLYsm2p55lnzMcj/HVRUvrH6yjeuu2d8akXC0u8mGjAI/0hUw==", "shasum": "379163aa7dd64bd63f60623a3d6dfb4d73f17580", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.353.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgWhYCRA9TVsSAnZWagAA/EkQAISG1mTkPK+6tShEK6gJ\n2+pyRA0Kq6rQYlrU3RQ0BWK3og2r+Ijpp4nbomFoRZuR0f5gu48tulnsAVss\nUKX1aSa9dfoiOYCoUInf/aRljqRTfceMAth4+95S+tYXDkZUhaNXC0eVvmt1\nDh47r3Q+kO1FGoK+IoMCRLs68X7nJenGH7mR5iTCaFrEAfqfVYcIdjYYEn4c\nBERSENf+uMtLgzdSv+g6ywUwi90aCOvJFDFLVCBSyX4mKW7eeYzT3stPyW8l\nHw/l9toi81GkE9+iQ8m2g12Z8zR8qnO449C1Hfw+USVoWYfP47zJMOmBfwIC\n4arzvATST2RyIc83LJCQSQZAm5sixocfmS2dP68uyNXxBDcWRT3KuB6ul29V\nVnWO7ZIrSvG7VNooGb8BoJ78vTXyWYTT3T9Mn8FColFxlu55CpCeOmqj9JQ9\nLrBOCcHyrPsu3QwopIhg8yJ2EZqDvKv2cu0AazD5i6hoegI35G4eKOy/U55F\nb23nXFONR6kfA8vRaSdo77Mwav/EmkuXNHLTOijFHgV7TApqRP3eYJkbcF43\n0WtQ0rPEU/C7AqRFW59C7HvgmmjZbzU7AwU61933R9TaEea3Mnyn3NOi+bpt\n12cJ/XVD3umWBAsWqXU6O/R2nKwjAt2LFa7r68C+red5D8WbsxAe/AjguGGg\nIh9n\r\n=9O8O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSQUpzcGtm9Rfguj5MAqK3QJEdvQQr5mS8Cow5qDKHKgIgOaSOzaQ0Pq91BAO47MZ6ajO3qsrS0UCmqV7mK9FO0jM="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.353.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.2-canary.792.360.0": {"name": "@jimp/plugin-invert", "version": "0.8.2-canary.792.360.0", "description": "invert an image.", "dist": {"integrity": "sha512-+4Up9PQjlk51+Hs0Xpob0J7SeS9s3P6aLN3vb2PnniLNH20uvWkRP6h0xTqA8ckRRseWrc2YUhnvh6My0JQndw==", "shasum": "40d5f8bb6f82ac107d4def02c7ebbc5fb8a8ed57", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.2-canary.792.360.0.tgz", "fileCount": 9, "unpackedSize": 8187, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgpx9CRA9TVsSAnZWagAAuz0P/RWJX7YnHUqvi4zMKzn+\nEsRvOBWJMlV6mMY2Pn0a2icNzG/HvPb+FWFXQA0bQbgDa17vTr7XBsbBWwAd\nXSEpSg4lfeCSE36ScYyiMr/JE5FMyniBd/RM5MzM2oRqG2dypWhgHbZkViKD\nbdy06uSMeVpVrrXRl2RIwiUNidjeXkzTe/uuAQBoefMXkiif7K9GCmqYp5rW\niOd9uHKr9moV5bzrq7VqPd8dxv/x5o2hWBueJYajh4wYGkF9+t0feZ1Z9ZBw\ndVy8yjVjfkMA+7VZPorvI1kg+ExohjwyH109Iyfi73aYsa+PDtXluLqeLEZY\nDUqyHZi8mPTWANMlQ/J4kgaEpMVxlVpSV15bJ+I+F6QgEWUE2QCxs+JKQUIM\nQ1uQwDQ3cvwRYHM+e31VYuQOkcis2yl4O+mgAlpd5oP9XaxaJzaUVPZ5Hz6s\nyDmFaYTGv5EovgoUHTENb9KgNo0OCV3YhA5hbihS/oIBrbtDIiqEYc41NzUE\nx9BmvqTnffnwRT5DvGS4evM5V8AIwczfxcOMA+o/W3zaob9iyhAS8kgwQbzY\nF3EILHLE/niQtMqJPpH+4+aMf07vMoBtJ2vw0wcYJ5QP40UX4HNR+0Vmmmf8\nSKZVjUUEG1bp3h1m8j/cauMGU35vWAjXxVJj4xiuyMpu4WE4KZs9cDCsKpzT\n8I5p\r\n=zjOH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt9uKaWXyTX1pX4u20AfgLAMfe8W3a3eV983a23A0oIAIhAOxbX4581AfuN5BOuSPOSDV+nc64UsgTlvqpTBquAXFL"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.2-canary.792.360.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.3": {"name": "@jimp/plugin-invert", "version": "0.8.3", "description": "invert an image.", "dist": {"integrity": "sha512-gyQBs4j+hHioblD2qYsmRhGyca2i61J1Oqe4aXRytAGvYCTSXIwYMaMKcf82JCuTA59YxP5+0AxOuWIYLfPO/A==", "shasum": "4b92ad6f7839a984c90db67233aaef6b2e655fa3", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.3.tgz", "fileCount": 9, "unpackedSize": 8154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgr0RCRA9TVsSAnZWagAARQIP/0dwKJzczeKb1nGzik+p\nYloAKjR2Mls7D3OMpArEmY7RMrLKtLEQ2NtitN2x7fYSVEOig1vs6V8EfNME\nUujdJNEJ2lf/1N8mg6I5n3kgc2UceZZou94j5iSU18wpgEn3mvDPwvpVudZ8\nGslM8cGfbi7vVPplZaeBSHYVeGj0bjEJ2gEAOax/KOJtL1xLFS8ak6eFDqkp\ns0ZDJesiGOPpCRiEpjyDcGnqq5SqMs08qfTKvSl2w0uRPyqhDo2TCbu8wMLY\nHTclasxUpvRTND0Cy8mPoLZB4oWLN82XC2l4fqqQ6Ngn6QS1chGgn6/BqX96\nlIHxgnKdTv/964v8yyoTZcmrlktZlgM+wAIs3N5YkYOFeN7FU8xdiC4grygS\nnsiCniR8WCFYcOhhlDmgFXSXPbPtGrn8VKYZX99Uiw0MW5w6b0cROQ6/+5sE\nVK06BjkcS15lrl1uepYi77dMIvxHgtUEmHE1dDWOh0d/wNwXYRDhOOQbKmPf\nre2vCwQorYjCLJ4JcxvmuzmSoxtbRvW46e7XHDuaoomKHjGB6UjaGlkqDqvs\nCr2+DtDZB9kfuTNl9tladlqK+oTJFGin6VlArRggHofIOzg9gLUUT7/B7YrT\naI5nmiNg5b6DKrmLWRFd8pGNCeSioqub/HmgjPt+dxCuMBZc/D1gYdQtoCYE\nwMRn\r\n=FZ2t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGMsQhyj99cRGaBC5tSkUZr8K7l8KHTdeXlnb5snHmptAiB4ZUiKuMEyTl5s+a3+kC3H4DYmS/kMuvsQTkbtanIwoA=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.3", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.4-canary.798.377.0": {"name": "@jimp/plugin-invert", "version": "0.8.4-canary.798.377.0", "description": "invert an image.", "dist": {"integrity": "sha512-gFE8FdKOVgqEbUrVXw/XuBH7emmMI2jUu2JcjwES05ET//rdo0LzXMWRot1bQ4+Lk97bm7929d2pUcB7BX1NAg==", "shasum": "180774f1453bf3d6da24e4afdeb11afd1a4d6356", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.4-canary.798.377.0.tgz", "fileCount": 9, "unpackedSize": 8193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgw1RCRA9TVsSAnZWagAA134P/jFF62lwldAroXGPxh7f\nXfH462Dzj9++zsYeVwzDDOnhPDBfKxeImqj/EiqA5ji7/4qap1pvx0vBVXYz\nGkknGvoDxXfw4vaWXwRYLXYvFsCq7Xu8AHz+YBltOJ4HMHIGpa/xwZ1fpwaS\nliCm6AMy5HFzBYJFmchZhjxdC80JE7W91kNDQ2l3S6KkVSgHW6sJkEzepLui\nZr7bW5WLsdyY/zjol0geHcpN+6IcrwoXS8wCZ3uUSlU5lGxwtc2df5ci9jFv\nKIpGXduBj+vWj6VVoi8DzfeZqYMNS+UZPMYfJ6lZhMs9HNS9ofgU8PmiPrL5\nDGH5/tCd733L1KOz/xg2bD9X+vJEZ4AqmdRBqD6KqXyufnUy8QrwjpJGtPYM\noNIjEA/U/1PZyC/qOHcs5Ev4DU8AFR/oUxoIdGBuk3vP6N8BuiBcTyIb6Rw7\nh4ks6l/ZrtnH+pCQSmzcINKUl8uLU0tV4b+MUZ9PIQmPR4Ndfp76tINFFzCb\nH09fDySLMEfRF49XK+lE8szC8eZKMQL+uoSrlri2vEewCL/vD+4vWNed0qlK\n9NWmSqv+V23/BzNzaPiDq8tdUrjPKDrIZUM6JXbYNNtv6mgpui2hpKwCwOoa\nz0zVxNihPNHM+FHROWHlYaK/zRlOzM7feeUwlz4wfbhy+ajf5cy+fg5Ir48L\nNaqq\r\n=0Siz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsL+EC1UxgR8IuvXF7KNIhLimzYIGzZiO7fdnd0DKLyQIhAO4nSgF/fpx0LSZtr2B7C7B/jN+Ji9T6CRetAWzctZGy"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.4-canary.798.377.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.4": {"name": "@jimp/plugin-invert", "version": "0.8.4", "description": "invert an image.", "dist": {"integrity": "sha512-OQ/dFDbBUmEd935Gitl5Pmgz+nLVyszwS0RqL6+G1U9EHYBeiHDrmY2sj7NgDjDEJYlRLxGlBRsTIPHzF3tdNw==", "shasum": "fd4577beba2973f663164f5ee454b2172ca56b34", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.4.tgz", "fileCount": 9, "unpackedSize": 8160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhP0qCRA9TVsSAnZWagAAwYIP/1idQaMR2Io/XJJwUb5T\nXqakep6os5xJPd8OO1+5Z1YC4O1nRMSRkOPzL1KalWRRnWTXmt1ZBnJqYSsD\nUlUDIlSyzdreeg3n0W1bLXEM9nR7onRQJPe0Rz//Y3+nyomppWEFGYVuXyT5\nbKAwwiOmNlElt4y6Bv+ZQunkCluW5KmjXyJ4dPL5OwjHPI30p3Q5MKvYhc31\n2Yy7Bo35ac89Sx1oWKZZXPvyJBU4CXSmU7ihLsvVaJ2Eg+uQusprMa/gbOLX\nuWmPfNbEijy/EP/pPQuXK6UjpvypGO+As8+654aiLnqk/4GNeydRUER/XkRc\nw3+WBK/sZdVCalHOEVX2xsmj1Gb070c2Vn+fzZn+97EGzuoq67HeCQUc4oc6\nolaZbbystqd2Ru6YzbQdrIp/7mlZ1RaLKe+epI0cLp+4jO+VoDLKtTqRWw4O\n7maj9R3v4LRpG4Lqy8ujydPGtFfaDuhOL4SKnEVmo4XIMDRdBTAWtbbMJ+Zl\npnFsWOUMEq7NDVZR4bpQPhGoudjqcO8SJMqHO0N8RF541neyWjWRQQENpieA\nWx37HtpoBQ2Ld3PvWhkcXpmJYfNlsoNiHSWfw7M55MrlWN4P4N1DIKGcWHn/\nck+xCNU5c5vEjiJ5zrhey/I/RJOENPrxxBWG4ziQC/2tXVAbMYK/jxyu4qHc\nXsb3\r\n=nUjK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBI6tveRAkByRwDQQ2EL1bUA+s9+zXQymHYInAAm6rDAiB606kS4bTkZvqJV19jwCUVvBG+lNQPaTlnKtt695BJSA=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.4", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5-canary.800.426.0": {"name": "@jimp/plugin-invert", "version": "0.8.5-canary.800.426.0", "description": "invert an image.", "dist": {"integrity": "sha512-LvVzPgQzPff/VRK6VDA4mtyg+W4Irew+6udyHqHZyg3RJTX7GEgF1v1CriCJ7xGCMcSq+cgCdPgl/X9C4b3foQ==", "shasum": "3a243f5427214bc09e1a7aca3da3e068dbbd4f4f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5-canary.800.426.0.tgz", "fileCount": 9, "unpackedSize": 8193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhXSMCRA9TVsSAnZWagAAImsP/jnTW7tiHaalevej2o4p\nacqCXy7LqC4gGG5TjNMpwnNDbVUutkgsBp2QhgsvdRHulvtwCadY53UU4uXH\nTDIIUCT3ebX7oJsF6THdZeM8dB6EJC869KaelQXoZQ6Wr3QCh1gRqktUgm89\n9pj2NNFnXk6EtAgBDgJO+906zBGZECZoSyilYE3Rl+j90Bx6P4k2t7WqAp58\nP0hgwAa6aih4pgi49o+GZYABmqgLVIAVCbwD+B67jnCXp3Gq7Q+6LdtWt2zT\nmMIDF/yvNIq9Pxc8Uff9H8OXO2b8sY7iPyg7OtWfBHxEUeqI6Fp274yWbLco\nGPEAbQxcSh8t/1KfgdhCYXRMneMJXuQv0LccUQal11VwZf6p8BJr1qwnYlWa\n7dsgnyBd0+8FaH/gWL1+U1usLv12X3IBY5XfijJR5cZeCmG5NYpBpQZlBNfi\n/XNFnd6XxR4R5jWi6Zp49TqcBwVXTGFQdIKJBwIeujqP9TRBosg9gcEu1eYL\nUlHrn8JoFNZwmWv9VThm/Yw4AXP2U8qMLLTreRXu7zZ2SS3gIz/iuDZ5VdpF\npxYIIUt22gf2Ssqg0WkA8DgA9tbnV8fy71tOFHnRYz8/EW4Kwbgo7xdHJDgZ\nQJBbUOvcgCNYJmGz9AbDIa/KQYJ/HhyfsJwGwn9lQ/m47aAfnbet7Mi5ITEj\nTDb/\r\n=iNAH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLF2AEo/ukI9rDSvGuS2Nfsqv+F3Ekphu5w4FXNVQhQwIgQOktkxyTo5/3KJ/IXG5Hmd+Ibzv/StXpn3AbZ9jI8AU="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.5-canary.800.426.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5-canary.753c2c9.0": {"name": "@jimp/plugin-invert", "version": "0.8.5-canary.753c2c9.0", "description": "invert an image.", "dist": {"integrity": "sha512-UtvzUr0ADwS4naeeFPjyKG5ndIFgVLSN7y0s5QTpr2FyPzPNiRlQr1/qDYwfnbphpWdn8wGaOWmDhvlLIqHsjg==", "shasum": "63acd041810f1119c7e83135d530e7059bead8d6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5-canary.753c2c9.0.tgz", "fileCount": 9, "unpackedSize": 8193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdioBpCRA9TVsSAnZWagAAYgYP/i2pnnxQXmCl4qQsMLFB\nNkAsSFOlGtXRFP6PTJOTR1EJfKagLz0dT3th1wJZgh/J9BeUZKyhekvq8Ovk\n44ztvWE7zCqc2KYQF36IqNnIvtkLZgwOEhyRWbrtHeYGK3iZV/GcMpiwTs8j\ntMCXPFGcfhx+KKnmd5tYO1FPKjAgHT6mCuhaebDPJv2AcLE855mHoM6LwbbL\nG3m3QECCTcril35nmWgkh8ENWurmeDGUPA6PRHJhJ75lN6RXylp9HHAN7AgW\n5+zIDVqsAOu0pi6/vM0hi4+vG3PQHEJG/2zKsPRNhMcEoYs7B/EOskYMYSgV\nPVV5asQhkyJNyVY3zQCoWBuWx6qrCbw8sgQGBVQlbkLXKvjRNJGcAHPqbQUS\n47kOYlA1C6GMfzCxLCAHdaLt8oOPHiHCl5aHhj0jft6TjkihgzbeLtzUZGGB\nuTMY14db5wZBq1eNM6KW3WV4y/CRmJ03oXcjIuvl9BDc4jwk8ZHRc8hFQvIA\nqO4ngFiQ49Rajhi8NtIARbYJwTUBxs0s/8H1Q4kptwuhy1J1Z/c9Ik14enW3\nAJtXiA6FBr1WAx+DvUkzZZf7wngCgFzW8mk5L+1oe2aErWqgvhGAetMSzYYa\n3RJJ6k+5J10LcIwsefzFUKW1Vpo57P7W3w/F1NHi2SrsDwT5GFkJacLpA9q9\ncet9\r\n=l5Cu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuPop/lK9vk8/Wy4JhnBz+xR2ujKSLH0sCoFlgI7XfJAiBQjG4ahxpFs1SXmquwNiAGbEBynnN+x05Zfb8cyy3HAw=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.5-canary.753c2c9.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5-canary.b147e81.0": {"name": "@jimp/plugin-invert", "version": "0.8.5-canary.b147e81.0", "description": "invert an image.", "dist": {"integrity": "sha512-fKi8fQQVGmBQqcfMLvwmvp1W3RiDTZzi9LioHPgjGdt0R0JMgBwYRFCzBEa41l1XKzIh8+DbFtjjCT6t2gXc1g==", "shasum": "78c09a3d8de8fdbb43f4ec93ccc7e3b95c02a216", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5-canary.b147e81.0.tgz", "fileCount": 9, "unpackedSize": 8193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdioEtCRA9TVsSAnZWagAAc4QQAJ65ZsDljPXFUDVZ9Szb\nt/VJq9hkDJ7yH2djm1qQDRHO51GSRMk92kkhKM2B7eVr6merDuE3LAmBbXA0\nF6080MptecNolQ2oKwiBkFARWDApsyUhVfV3X6NTbEeEHB2U7eg5qSPRago1\nm94R2YRgV/BhtfKaIPApE/WgeDfAKZ1tFG69IbQY6n5wwF4jyjxcn+sEKUHs\nE6QWBW2YWC90dmCxRa6mlfEhLK3VO96oemJFBBUGMCRJMBLaccwv4W2818rc\no7lCKJuKT/1BmraiyuzCk6VZ2313SgEoURjysPpHAKhL0Nmkakyt5HLXAZ2g\nyaxtthxKvwOIevyS/ygCn+PMFB4Wdbs3Y3Tn+xBRMBNBWjIByyfWdn0o2Dcg\nFPilwOmn1tplpNnMRln3sgLtKkyE1/Bma0sSV5mmtYlI+Whjy9s+ag5MquMG\n3lB/p27hKfvFwnRlLxfz1NXBGVXaOcS4lOgTv4IG/n5hlbiwJmwnSl6WxJIn\nsXujjLYQpmKzU1pM/RGmzc+bZLfQW6UKBk7MrxEBIiTeSXM0caqjRfT7BDaP\nm8nOg79zFNmmRE4+fdfqgxuQ2ho3OqZcGisHlM9grhbKJVIN02Agv6n/nT3v\nyKnwaj6ARF5Lup8mMC+kwv0lUL4HYHQeJ0AHe5pnsuNh92OCk/qT4P4FK4id\n0R1g\r\n=sB/5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAaBacYG2DNRYrIFzO4piPCmY/EmvfnX6g8deu6ZfF5QIgb3FFjMCTuNIB4Y6OelSFHAk7NU4I2c1njusTEXNcs3E="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.5-canary.b147e81.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5-canary.1.b147e81.0": {"name": "@jimp/plugin-invert", "version": "0.8.5-canary.1.b147e81.0", "description": "invert an image.", "dist": {"integrity": "sha512-8hoEe+WFXgPAWIHbEWfPlhCXctX6RpijcjHpc5QJExLLZzuIEOqrJhToqT5OefNqlEqIyXopl6Jj1hLdoHxTBQ==", "shasum": "b34bb7fbf6da2c071dad5d989ec47aaddaec8ebc", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5-canary.1.b147e81.0.tgz", "fileCount": 5, "unpackedSize": 3394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdioOCCRA9TVsSAnZWagAAH/0P/1D6YJbCDQ96sfloCtWE\nxf00azfxVufGol+Hl3vqFBCD8VDvtKPdykr9XUoRd2qG4u6ZdxZq+Djy0Bvl\nANvWFRrrfda/GaSED7aghQKoWrikwizW5j/ac0cz1xoSqH4cXqt3x0E2vYI6\nxzDOCxwin5XG5FdJ6827EeCkRDzOgq5jJYGOmmI/FmsC3y3RKg9BI6uq622O\nmanUjIVQprc3DUqbGAvQUJUDQ5Pk8pAk6YTKepn0WsgnmESVKj2HZ1gdPM6R\nVbfve5PGHOk31vGEQh5t/gzV2F3LGU6h7rwU5xycic5a5EN/6bESdb1mH7lL\n8hcihV/3aKBXHmnAoZZzevz1cKEtFLjG3EEYf33kDsjqrBnMH2TnZ9Hk6lbL\n6gMiiA912I+RnvCKSfRmL+EiH8UVsqKVvhpeiGHkxS0CRJt0jv4wOVA9wrl0\na+AH8PJM+10hfrcDnyxJSH5eyulbywV+2XlN4guSFEFFfxYwXu695CcvjdCb\npL3V58LFOpwsXOtDR6y/ARVzp98tzjH+oX0MPyQdLnWcm9oBbI33ooTmWraS\ndQx23OM3N07wSU/soY0J3SmbovMBLU/FC6JM7dvDmqLcqCrYU1EEpRwf5Mu7\nTwUU27AgAEGB+Jl4xX47DwFfGwVhDYx3IrjYinAc/SK5v4mUG6f6MdRdSs5d\nFFJX\r\n=Lz/b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2D1P6+Jw5Zfs7G+O3gVQfu+zIN8W94aTXiyc1WOzhCAiAjc+q0FpZzdEVQZhXOOmHtyN8oXuoMrs9LF1dKe6AC3Q=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.5-canary.1.b147e81.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5-canary.799.447.0": {"name": "@jimp/plugin-invert", "version": "0.8.5-canary.799.447.0", "description": "invert an image.", "dist": {"integrity": "sha512-/Y4ilKCeO8ZV8vK5PChcYuU7nTLau2a9KVTowKQ4ij35PyQ9tqhkjyq9OgBeietWu1JBr2RmfjhLa4HO8S8Bog==", "shasum": "2956088a8e65aa4fa4351dba73d039be3c5f1d1c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5-canary.799.447.0.tgz", "fileCount": 9, "unpackedSize": 8199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdioYhCRA9TVsSAnZWagAA0pMP/1DFP+WULvGi5ChAUtpG\n3E2buOFCpA+GBue0MqANK87YUUywfBdLZOe2FDENMDsiLVRfAhgFQVu5ZhSh\nbjR9eAdrw72hsGOchQ/M6PySCgZA5bnPZojN98ilWH1+jOgEcHw6tceE0Mmc\nLLWc9Y1mBi81AslRQl6EhqY1AfZ+kS3CgjQ5xk05RmwLZneernq1Ka/XRgo+\n8j4TKKtRurKGYcU3cJXm0cdoHJ9Az28cKDrOIIbqivA3vwWL15V7e072UqVk\nnpD4NC7eLHDzvFyjbJ1Ym+PWyjbYNv2PSUUg/4UvgmcGFmq6xzqBhz/S1bLh\ngsABYQtOJJMT8uSJ1GME4LDN89Nq7DDk1t6rSP+n7sOxjZZLv0RrOomR5A42\n9DzjCxyj8n5F9Ihq6qh7LEdlIt6jf7jp6X8oxPEACsjFtWGcYuGZfQrvUq/j\npCUtrOPn6kP7WRqNtaBuMcyKtXDqj92GY17awcgRq7grjjgZuPVseI0Ln3wI\nfv5oSR3zPZUh2/I8QQdLcfC/lqarSSDdl8j0EJC0kfhbg7mL5jqildKdcHu5\nrFYmjRgqLBpumkNTJpFsOdQpAuBPT8y9594UTSNk7nCdJ8xwJX/zGpqAmPX0\nTroVrtWwYT/xvkQ70eO5oj2JriUkRqiSkmdJiPsk9DMySUjilSeh6386UCWp\nM1d1\r\n=bSuX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICw0QHVqmimUitJ3qQ2NjEJnAU85yfeZiadt05LwXy6aAiADvTw3QR2Rkq4Rt9Lecifpw2YwIj12O/D+GudsxbMsBA=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.5-canary.799.447.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.789.460.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.789.460.0", "description": "invert an image.", "dist": {"integrity": "sha512-FxEKgi/Cy7HKWKwuVQcebE8Fg/BlCsEhAtYxsxnFze0eFTEeF35kYAli7dXZb+qZET8hoe68BTmYVh0BKkWcIw==", "shasum": "d9533a70f4b23f8457d67d4dd1a83259a5c5c72a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.789.460.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjISlCRA9TVsSAnZWagAAuv0P/RgrXI+irsN3mQF/UIjW\nd+GSsY1G0bL5UFsxJEerve/NDwUx12pu2zbUiS5hD6l2EZIL7wQ4FCWQ+jWL\ngDq9axFh6aHrYBtoLZ08AFn/0uWy0WojTH+y0I2HdCPwJ1d8C7Cnb8uXj0Du\nax6LDs0zkbJs/m2g18N9agdf/3R+MrqoOTIxH7VQS3aRvhpQhtHbiQGw15qj\n1h/8NA8Jy9/7CmXysxxg0ZomVIpW11EJjIulA6/IN8wFPR/SuGM3UBs1s4XH\n1ywouP4pa6uURnaAC6oCfbLvJC5BXsdC223PZ9KLeoG7kFR5/PE1ztiaCk+r\n0ATlvK4DaVKa5DnsTd93NQzeqmhJARixv9Kz2aqKpcA68NeQ2z6KS/G/MNZL\naTue0sUYhnGRIo/p284b6YetuaoaI9vQlDH697Y0oSk36yor4h8nubZOXk0C\nWCpNjxm+uNtGRfKaHA/zujJYPCFAEhLTxIJ9pGKp+wh8QFhif7pizVgURMLT\noYqUm6Va65R+2Z+q56hMxvAYUkD85MtGDVt0PaIFCianyZFY1m6CrM3o/ZD2\n1gpmSd61hyGHRnx53XT9FXfKWcSSk8DQcPdEtHWpi4sG2OnzQZ4kBrlGi1vk\nNMZHWzMjpd+z8e28sQEZUdZHg57C1U/f7ehlfEIMlzH8N1sJCn0/Hip/rd7I\nsQNf\r\n=AlJY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfP9fMdvmPwrdtixAat3URMkZ7L94ZKdzgXQ7/xb0VwgIhAJyrO0bvBrj2lAMgGYcim4AmlkXYlwVPIQMAjLUNMbzc"}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.789.460.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.1-canary.791.466.0": {"name": "@jimp/plugin-invert", "version": "0.8.1-canary.791.466.0", "description": "invert an image.", "dist": {"integrity": "sha512-Zc/427QqQrFpKMWj+ZH5Vm4VpBrIYUoimrAmAgIfqwABeQB3T/svjmdHyCpS3kv09AkJGp5ZtUF9AUOzHnZgOA==", "shasum": "6b34c5818c66b03a2b9c64c3acf30b5c249a6cd1", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.1-canary.791.466.0.tgz", "fileCount": 9, "unpackedSize": 8228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmwH+CRA9TVsSAnZWagAA79QP/RZzw+oMZ8M7gdCWJ7zo\nvFBd1YdPwN3p6X+JbRwPDiUQv2yS+BWLmcb3QjXA7if/p/D1bi7KoXLtpa0o\nD74cKUo/gKPrlns1Sz7OarsImf9Ci/u99iOHsgxKhAfELAKQbRfyl708qIDl\npmb4uArOEuAGYBwv93wW6qlJz2jwOn+DxXGXGHL8jvrfDLZ9cHnN91lSOwXM\nX/HeJdY8Up1YZ7czxcINrb/5gCxpi92BBiNn33L/O95JtoFaOzOyu2hUyKaR\nnuSm3E0o58bf/BpaIjOG7mE9bxtL/kcds8b9ufHUmYwIhNLulxYxHceMfeiK\n7a197VM4SiYZu32Al9W6wHyAlSjyHh1aTRO6nP6kh6s7Pky7659h8JRGh57t\n1Dqs3eVMqpz0lTtBCwjw5dJqWIc/oRI2oSeVKNcVeBf0pxDKR6XC3zzrUJCo\npho+CeLPzncevP3sovUD9lyems3zhtyyjNoOhcqOvQxlumI2oUY+HH3VuySx\n0q0ob+QY29sss5aGQB+K8TgmNasy6TBNRrWrucx6Wq/lhcMO8CFxA0R2C95b\nRMMzbcgL8446G2pZWMTyZELbvWxnuT3RvUCvccxN1vVMZ+vZQz8kWYEiflFX\n2HBQ8XSCKL9YDY+0tn+cJ4/bcpPkTjw/9XagVU85VGwSJDtgCgNFRaQGS5TG\nnfUl\r\n=hciv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExFawz+DxixuErLXLpskbELs3a5sPdxTaW8QcigGTqLAiEAjMiE1q5+hhDOujMjMSkjnSbkT25kaJrIVAXMLmZP5aE="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.1-canary.791.466.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.5": {"name": "@jimp/plugin-invert", "version": "0.8.5", "description": "invert an image.", "dist": {"integrity": "sha512-GyMXPGheHdS14xfDceuZ9hrGm6gE9UG3PfTEjQbJmHMWippLC6yf8kombSudJlUf8q72YYSSXsSFKGgkHa67vA==", "shasum": "91044275df2101beecabd6d12416539724840f6a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.5.tgz", "fileCount": 9, "unpackedSize": 8166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqiX7CRA9TVsSAnZWagAAJ/gP/1+GHJ/v5Bp6qSfri9Te\nTyM5/+sbueuLkcH+FOEKmy8jcKbFgnXJB+UKMDM8yeXnKnHJ6Uwgt5J+JPW2\nJUyWckQFsK/cV0cheRBjMyyNAXLmCvqI5J8IYmPkFonN2EVBeZlVrhaBCC7x\ne5x1HCpQCdEFSU4uJeR3Ya/BgiOcNdgzZ6pQG9OxnEAoehTH8+yug5z4XFeq\nuckdHKfaDsYY9wQAhxgX8q00lv/9uQzvCuQ6/ziWbJLDG6kDNTKh81mJg62v\nxzOFZPabD/e/0AJZFCXxontIWskTy+oJNMueV1Aron6+aA6NAwKNE/7ZiXP3\nc5PBc2uhdJsGc6VRWU1XEQsK4MXIVm4hnKt/PPOU1nx5trOUCQi7eqnBCFeb\nEVS6lQLLwVtWfikXxQTFPxoV9N5z3jc0GAax1A4BzqF6bqp1oVFHoKVHER5U\ntq6Khvrs4hv4xwoFqcBfVPoe61tfHtNSeuRaTDqebr+HZwERtwp1uld7i5UK\ngHDpzKbF/N4wbu2MzW9hvM24IyAWnRYiiYwq1Rcve7lyqvSHR9jAxrGbDXnQ\nA6/EqPF7yoO7I9O7nu6TqXFp+kdT0w+RFreD56MDPxEcalev4ITtO1Eztko7\nD0DWtaTvDGAOpp9AH8ISoBIiaZw5HyBBMG8P3oartbD1+hn3ivKplmfLIwMv\n0zpr\r\n=QyRl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFTGmy/a93TnzTawJZnpteMpB9xEJIb+tc28aJMULdUwIgPwRTEXheQIFBYr4fz1HwA/GI/tN+oWByW9Qr09P1QOY="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.8.5", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.6-canary.810.481.0": {"name": "@jimp/plugin-invert", "version": "0.8.6-canary.810.481.0", "description": "invert an image.", "dist": {"integrity": "sha512-ZM0MLlnyIsVsMUih0jY4giH2+TA7LHJqjaZMQpNbdfyiqr/njFoQY4UHIFToLH8+rV4NFWAaqN3BnBbQ1kVM+Q==", "shasum": "96f96f3769e50db10bc51c07d93c1c6d35892350", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.6-canary.810.481.0.tgz", "fileCount": 9, "unpackedSize": 8199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJducVDCRA9TVsSAnZWagAAOYAP/igICwXpjWpOoxyjsb8f\ncYUbSZpFUr9Dxo5YQ0+Xvl98WLctEp5NrKbkaoTPHJsOKMCfUTE7dY+x+r6O\n3KEyJJyijeP4V3dKjCjQtJRa6SOjzSztg+hJ3F6wZN0ZOERhPSBUIaW+J7J+\n+txnpKoMQj6c3Bnq4nbuQMbaBK0FASyz/LAyo8IPenl0MyJdEba+9yzcr/pz\nCPQSZNkKS8DQwdMFq3XQvZJgPa0qffg989lprjqtLljbIXYEMn+Rl02ldFfO\nVRaDyt7xguQ1Poxi9g37SJaXV4zNpCktMf4Kv0Qvjdb9XrSjPdvcQrEMtyIF\npli45lfBNDj8NkdPZCW4x6kO3cKpngBGLkVFLdMhU3vrNcNtYwWY3P6aqY2Z\ncIcz1fg7rkm6Mh4ox7oeD9RmqHwQDxaxj4cIptiWkmWSw1FyOKgHfLFoBUIh\nNyUilGnDZ5kVDiDa6GOgrqIURHHWG6r/RfP65J9TsyDbPmMqNf09e/a8M+KK\nJVPprBsU9F6PIrV8DaKldzC68dIv+thirNdHcdXqFBqfvfVnxMgEVO1cwCqP\n6R7iGdLCqyeGJRaUXXQAnO03eTH3iRg4nXUUHCugo2p3ONaM6lbtoo3xHeT1\nVg4XK0UdMo5GKQ/on2n6xU582moA1SBvoUA610hJhq2ziBKzoGgYe42eO+AW\nKqLj\r\n=Egl5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIELo15IRuSsjMiiBe0LI9WzofkfQbE7S1Z67j+/JVS+pAiEA/Q7aZoNppVMqMNRUEIZUnQn6pRh98mn2hsRXmo8/bu8="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.6-canary.810.481.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.6-canary.815.488.0": {"name": "@jimp/plugin-invert", "version": "0.8.6-canary.815.488.0", "description": "invert an image.", "dist": {"integrity": "sha512-mgjXeQAgDUIhC9lIkcwAP/GLYCm1Hfos6WMuHSOwtuLBI5dQhoAGOZFMTXGqP6y9tHV2NLJk/pTaGWNV82ONxA==", "shasum": "****************************************", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.6-canary.815.488.0.tgz", "fileCount": 9, "unpackedSize": 8173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0GleCRA9TVsSAnZWagAAnJ8QAIuzJVJ4vgaR3UkBH+E0\nFEbudxlNO+/4NBJ/0pve+MoU5TmCTWhM1Og2zdRJZqg0wUWnYLp8lUKP3ukH\nNUVm0Em68lbVLLJytHaHjFxxOluBQ04EAQEUdy2SVBU1oyENTTDJT5FIXFmW\nIlnqiH1n7H0aixe0gNGbn8nRVbvwXbxplW7fDP0tyS8xwUn+jUZe0WlWRbo3\nMx/QD4kb2mr9oSR05Lh5RHbme0Bh6xu2ps9+7sy4NWvReMMfUXudZ03h8OG2\nEZcwaR1JqB9awwAknoSgniccvKO+gT0LZCZv0Rg8AGxisN0hxYOcl5W/z6UE\nLuMo1gt4feKdubHFPsGqxlpt+qi3/XdyBkgHOOqu2MLdOh+M30iM6LhD88LL\n6M8hJeZODr/WKEosn3G2xKH9lZGHPd0saFHI6tPUC9vQblC8ZRxt/rw9DNu3\nx1zM5HiOO45sZf4FcwXE7vI0A0EloRguUsDFqI9u2xwK92ikjtNMxLxsooKa\nxQguiJN+47T7mjweg6feSBGW9nhhyFPhw59JmJgNNYwNw7d8uAEd23PlzcoM\n56pOBK10Y8SEQ3RtldVgmFUbaIIbHvdeAkUsRlgeKo3qRtSiHv6tcJVDjnsj\nnjOlfHGsXgJ5PcFM3F8e0Z4EyxY8F0l1G44JpzuxfL1XgtSO9r+nMqoZhCCd\n7+jH\r\n=ZU8/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDzDTf/sGTeA6D122TulavtH6WGYwst/HYlvTz4V6C2AiBH5BSzdVxQ4S6BvnE07RdU73zTGPARN0GC/lmVz4BF4Q=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.8.6-canary.815.488.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.6-canary.818.495.0": {"name": "@jimp/plugin-invert", "version": "0.8.6-canary.818.495.0", "description": "invert an image.", "dist": {"integrity": "sha512-E+BJOVb2FlQRu7U3+zpTp68Nsug8zlWx/JArMbBSdGOXN2Gc3QAYq6dpsEQQT4Hg7q3sqqrTqc0pO8+4gR/D7Q==", "shasum": "87ed192647aaff0f88fae7505991779b1ec07baf", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.6-canary.818.495.0.tgz", "fileCount": 9, "unpackedSize": 8198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1FyHCRA9TVsSAnZWagAAyR8P/RlSdf03LRH3NL3Lz0Ja\nOhsc6emvqcSQchl80n991birEmjIfmCq23x/VnfagVEEL3HTAJLSfap1T0Jk\n7b7iVyOMPjbMcRfa5czE0/qUNPtdUg5+UujUMfAkCXfwl5HRti8PlrdBSk9j\n6kF2dqRYQJy206MuS5zKHg20AvD2sT7C1w1Y9bncgRSm7m5YmXAZrkgToXXj\nT/m0KyJiAOLInKIDTpw/xTd9RtIxELzE6HDUSA4VQTbs4qD+nDHV+jFsAwWb\n+SLXsuFmXupd3YrjZhiY8gijSzUDmbueRdx6BytkuiETCeRlIIohBSjWqsjm\n45VV2T1QzlYcI9FZFm+c44qW8BMXjNdgP3Cy6znx/e+gtqCrUE8YLOcPm9zd\nFGMYR0w1TxuEwEZQwHRw3G8jWJXJWxBCJV62TYKm+mHBBalnTXv4zn7MQyga\nnmNV6vHj/mqxYVaplkIA986rzFTDztwQCtR4m0L4L4bP0wFl3rbKrOWCwhge\no2zmJy4OwyoYrew8t+2NoelDgIFTj4bwS8Q8bVGPOrC4oAszl72YQTg47BtH\ng01u0YxPurGdabwTvIFdpDDDfvbzr/DLdvece7Pdy1roMifys7N9eLhsK5RD\n6PTi+/QVT3MTGrscJfwQfuTUF02T5QrSXhxwfRZiWE2CohugYRDZNunQGDeQ\nduqQ\r\n=PdBp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTj1MxwsuhVem2plG5ymaxwyer9m+NTsXB8KG/PpG2DAiAOmUyyGGIcZGmzbWvLdViVJRYgZlSFqfFHPyLj9LBZRQ=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.8.6-canary.818.495.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.8.6-canary.815.502.0": {"name": "@jimp/plugin-invert", "version": "0.8.6-canary.815.502.0", "description": "invert an image.", "dist": {"integrity": "sha512-kYIYNyumA5sLmDxgQCDxZimJQlnhae7+djPgdXQY8o3IVtS/Utv5DBq2v8yFYP68dYfRPlRzj6IoVjghAt6FRQ==", "shasum": "10a2ce6f20e6356de6c9309bd0c27fe8462b93e7", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.8.6-canary.815.502.0.tgz", "fileCount": 9, "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1GkdCRA9TVsSAnZWagAACgsP/Raq3Ipx6V5+fdXIaPUb\nxNx9MAlVnkMnC/ajf9afJURg+3zzs0lF5MnPZKVrpNLu8fa5pCndvTv57k8/\njArY3/5C9frek9X1bG44GY+uHqhD6hqL+48r/RblKxojOXRMQCHdTkXrC1A7\njzTWiTmH3W44PfqMY9W96rQRDjGPPGXU+hhOtO35No0pe9SgLM7nCpHFUI4S\nn+s2P3fLuKtts6Rdie41aiVoBcFhKnSBaDcnETH/xnb3FJRCrn0AHTmN2/Z3\nOw6jCnRiGJl7LjGa39UZWk2G+ZZ9GC3MTniJc2NXm6fdwu4SqPrKo9Db822k\nfbSR3vfVtcyDwKuOgSQu0TABUssjjo3ERU50FxMh/wrdUXusNevJKHKsq9U5\nZu4cG/dShd1JFt9jJclQr1s0xfbMzD0dVASTAA790Jg7FaXpBb9GLKnIc5Rh\nKGMXb0esyaHo21sbHXjnURkpvwAgCmbgO2Ppel+poTVP0mFhm4xVfcChEuJT\nkrTYgyI7VjJbG+Y04LpoyREsy4NgsjtHTorWYK9j2kYlWkiyZ6YTcZJ5mu/9\nGs3PWStqdsKYYnHlPs3nO6EA4uHhDZrrJkAOkDXRdfe+jtoosKAiuqr7u9Wb\neOaSHjo50wDd+7TNIBMRSwS4C0mEXpdVWKjQuM/xcelonU8SKbyZgJn33zB0\nTsG2\r\n=suXb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCki82DYMpmcMYHIC3KRxJS3o0QPkBKZTAucHGhLrFS9wIhAMnCzq8QSxExfWgW/6u8E0Wqeg8I3P/igU8hgIap4RQS"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.8.6-canary.815.502.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.0": {"name": "@jimp/plugin-invert", "version": "0.9.0", "description": "invert an image.", "dist": {"integrity": "sha512-KTkrgjn6TrdVB0prTpg33Pejtff1hEOERHpWz1w6zw7nKoPWg+sWy2U/H/UxDF1oT0X11W2cSaVrtXnTIGQ5Tw==", "shasum": "84461f11609a651bf9df3edec47185d06ce15bf7", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.0.tgz", "fileCount": 9, "unpackedSize": 8166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3VvhCRA9TVsSAnZWagAAmdAP/jKKkaNAvg77FuNk7bK7\nGLL7Ja6qFlghosouf/idA/6n8U8tJGTsPABlQ5eSqxEbi6cYn1vPVtL2fZyL\n5LwSynuuzDAN0txUfG3LPmImFjm3AHcibDn1ij9E8jgOvnLVxDGSBRf8A8Dj\nJk4oa8IxAEHze75g0IiDHzFoY1Fk8rP5K5jdBEzE3QZ4jinpyWIOGLerqOkH\nusX698v4O2orxbpX7W2PjIvSoLg9krXgXxEIh8w7EdSo9z/6ZsfHkr9y7Hl3\ntyf3IN6RiAFEwstPWNLK/vhv+T2OPKiSRG/m6LRardPaxTZ4XPHTTbpo6R4b\n6crIwFNuyd5sIdt0rSTLllo0XZpGB73G00gACnTh3R1FNbFH3SE6IZiY6UgJ\n2qMN8Zs59NH6GCzEu8Sad0h2xjgyciZ2VrvhXQ/SRDR7ToiXDVOIGwV/bWne\nbNFylPZd6cLR7bu5I4Lwvad+UJ4q3J75sU2KNuAcdE86tVMOHt5RnaOSv18O\n1rgBUUmrihSu0r8eULPNjBnCXahXjDgxIQXKET83IVOWMRMzcf+BGrX/Na2J\nLfCqnGCWTCh8+9wVJlKImdZWlauwYAbVeZKvl6GK9pm1XhrtsfQ3Q9Ud1yjy\nJrDQqJV74ehtDMM3/H0imE3JqSmyvQ4DJ/q/p+bRfHpgrrtaoCGuOuR+Mn3v\nmtBa\r\n=YguG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPEevkUrCiU5O6XAGNeECAP6DMVqMJoq9XtCcvsN/0cwIgPMUybBulEina3i/gY2JrmUpdTc7CkpIfHe7MMi/q7hg="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.9.0", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.1": {"name": "@jimp/plugin-invert", "version": "0.9.1", "description": "invert an image.", "dist": {"integrity": "sha512-qnuRUYYehtUkZoyCps3gsmxqjJG8kIyYFcXaczRAIzDf6K+9kzMHXB1lOcuqDvnWQFSJa/AdwKBM1Z0UEzKWVw==", "shasum": "7809f4e8b8533892dd03b78c49282f8311304d3d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.1.tgz", "fileCount": 9, "unpackedSize": 8166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3WOCCRA9TVsSAnZWagAAlLoP/iJQTQ02/Ft6DAFS7ymD\nOWiP9XyyY8zuOC3XDEQaS72zi8Uy/7tfMQJm3xCixRAj51C8I5ZjkUxiE1WE\nI4HbGGVWeuNMZ7iQnxO5VjzjXf4CuLrt9Ak+O0PYlvscpLvo+GcRXTlipXSw\n2GMg+X6o9R9vdG0qbmFFBQVwTHcg3onQ3G0eOLeWohJASC3LXP1XOHOR/5zs\nNcRJdN+YW8NaUvAOBeef27d1WU5l7F2mQGj4rcBpwYvFFwMvSjOCOfqDslxU\nrTnLR7CEq8DsOoLr6hgoZmiu3ME7oImP4DMiE8Dtfi3dRzUmqVXimvJ7Zbok\nXPL4ca14z0QWWKhw5Zde51wkFQA8AppABFm7ekynOtLFvgIV7bFQo2PtU396\nL/EcDXE05kl7JI7sQNqa3klRYePrD8Mf1nDvk89wslB7+8vsvFFF8vaz73Ty\njUEB/upp2AIP4hP729c685lw+Lrw2LsQ/zpDEJojj338KEfsjmWveCzBYVKi\n1AsNYv9QvwhDVM1YwBKiSyYe9VFfFskIoLqg6Y9gV0EzrsX8oMByIew/ueMH\n7enUQpHGpIXMx3CaFmYWQOc7Z0q0MD6yWQZOSuKBqYLxLq/D126+Z/xN0b0f\nQKppHaDlx9Sr0MuiqL5E3UTV9Z1xP3inhRyyy5k5/YY/3tHrhBpnF1HtNAin\nfMqL\r\n=yUAP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGcNbj/i944y8f/13OwhEFkfxkAsFWEQW+jIMiQ8I4KAiA3BhBszh2H5A7GBBWCzTFavEKaJBHIhwz+hMeJsm9z2w=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.9.1", "core-js": "^2.5.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.2-canary.815.538.0": {"name": "@jimp/plugin-invert", "version": "0.9.2-canary.815.538.0", "description": "invert an image.", "dist": {"integrity": "sha512-9z+rSfjoWyPf8foZewvNOEeHBzf8FAi10kNIsXWjLrMHd+8268eZBsQJVbAIxAQReZHiaaaCtUwBPe7iw8aSJw==", "shasum": "9187ae70aee9c7824e0cfbac05f22c453fc72fc9", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.2-canary.815.538.0.tgz", "fileCount": 9, "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3YdxCRA9TVsSAnZWagAAF7oP/igN/MKq14dzz0MmKhAE\nyhuIYIzhipmLs19pdaVeIbJ7A5MAuiX5EMZ7RPxeNMbH5iSpugfZ014OgZdK\nU8MO2r9AYzrifEEFLMkrUCKX84piDUn/GphPxRtlm8HS7uMyFLIAjDpJrl/s\nwky4QzWP7vtNEG+vsd6IAyKQ0p61lop/U3mTwNJ9ggMEV2+Sb8Q+lg+cnoS7\nlkUqsD5ocXb8kZ4bfnOnTPPPSqK4eitt+95sNrsNj4yt1OaR/B65gD2AubZl\ndbVK3RDE3LIYB3PPrMglADEGlfkPY1wC7mNGJGZ2fcjb5geZ/S3dRfnd+8V4\nUTen0MZ0khftk9Cv2XemA6kJCgr6yl5pJv5VMgwNyliJVYhQsV0X40ZG0Yyr\nYjCfXb55EprFxgnuuoeF50qgd00ybTilyntZPP+blR6yVYlh7fv9+XmNrjWc\nTNd3/ZzrGFRkBEARL3WPcF2OYThkvocGRygzCr1u1OOr5xDKCnb+P+dir3nH\n4/7PZN3jn6f4CyLNcrp7qnLPI8+gPzRodsblH29R5P3DMHRORUUPy2FUmpEN\nmPAZukX1uwt1HqdqV02rrqqaA/y/4i/roJaNweFV6Ho7O4flJW3ejAsBfY6l\nORVKjigqBklF5t70JIeHJU3DDrYFawtxjT7bsD5WlqtjQ0yPf3qWEhOTKM69\nCZ10\r\n=LUGT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3+Evx8+9m6a2gmVCQ339G6xm2WCTgKezq0ni3cxmztQIhAJtbYBcMj7kCpU+H+4v8uUdaAPy3QPEQd0bLE8+tPrVL"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.2-canary.815.538.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.3-canary.825.584.0": {"name": "@jimp/plugin-invert", "version": "0.9.3-canary.825.584.0", "description": "invert an image.", "dist": {"integrity": "sha512-bPnxIC8nbMRDOigSX1AN7kPJPtiOyo1LZjOSUCKe+Zv3uweDFcTby/B0vE23p+cqTo10Ft4XMLrzpd1/t5EXzg==", "shasum": "536d9f2d82d22ff2d1719a46c17cc4c22de7237a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.3-canary.825.584.0.tgz", "fileCount": 9, "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3bBxCRA9TVsSAnZWagAAxuAP/0aKlMMpUluLmy2mXB2b\nYzJ2DQ7Cdpj28UKnijbdvuz4f+Y1202rd02WotduFZlhl02lUfwrROaVY2Kh\nf12byxlDQkeJJ5Y+ED7SXqkP7IYJb9LFWXo/DzJgq6s78YKfEtCaiGr/hxo0\nzVDZJcJ1oag92ngDRvbzeFe0prBXyYikQFUKXmozDvS6Ba+3qLmZ4Nv7mVE6\nmkWLxEILnNc0Z6j34g5tHp6ELVN7p7YhGZvczuPXdm9RKR5akJ8Udii3U94U\n2U0W+fPirc+VtQDbvR4ZudHgaAbRAEzM6EuPb62hv8Ts59RVAqr6kyufrUZo\n05tDKvhFNrOkb02xUX6RLDsT92u4RCTd+1KBQs1WjmaJh9c3d4XKNxAaAhDV\n6q7FxKlKNEwnI+0LpYssFenS5FNendxM5vIy2XskCCJyAauN1SkiqVuEQJcT\n9Fgg6IiCQ47hmm9bHDfw49MCRRUB48O4TSqSeddNl5iTZGm/mcU9umk5ff9s\nhFNc3SYCFaRbW1jkrRQQfrjw1s0uceCtDF4UEg0c5APr+0T2VDow8TubAtsm\nR15HOXCNxzdvw+7/OAM1/nN4AuB4q0x8tMQ2TkOlrf7QlTmd5xn0jnBzPlYH\na9U5s+qfgCm3UykYCsbhAMRcyNyEUwZ9RoSvhrbRwPARM94wMgZLsAgS0u9R\nZCPu\r\n=8mUU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB96WGgRZf6hkbvvnMMW6RM52kvjl2M0UiXZt3CIjh5tAiAMaJOzh4+LowMKVSdjNtxLhb0mL4xxpgAb7PKOhDkpeQ=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.3-canary.825.584.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.3": {"name": "@jimp/plugin-invert", "version": "0.9.3", "description": "invert an image.", "dist": {"integrity": "sha512-0lRsh7IPkzyYqExrZDT50h38xdlB/+KrdiDcuxWwWyIlKauLMR0kInjwf8sPeb3elPLeETmze7uwPAxrIAtsGQ==", "shasum": "723a873133a1d62f9b93e023991f262c85917c78", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.3.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3bLUCRA9TVsSAnZWagAAenQQAI9zS3XS6SZlSgK0Folo\ntLmzoQNrdCOLrKYdcJgIF8PKmIYD0FsQnZP+GGMgj1r4I8h6Ep2MI60LeiCW\nq3xPO11/liXRMMYZebE+WnHmq7AjF8KU2c/ZjKFlyz+GtsSTettg+VRWXNmY\nlz+10mFxlJER96wAucyL+5ThcRyyjM6MnoPGtHMfSXhA4H0k/Sp15DotRwZF\nMKDzdNYDKGDEyKZiTPR6BomPsOPNFbO/1gvBf/9HLVHUe2kK2jwHYCg+gCOP\noU0Rc6lG+5PhZSdQ2L6c2aKrRGL4UUL0m7v/9e3Hua4d+6fxI84AueMG7KmE\nWgz28hyN8TCWxu6WujFYGExP87BddV440cPJPKOGb3AkFr63UGFzVrv8xI6a\nbQr64Oxf4mc/Lr1d9MeK4/t9mQt8os1OmUl6TG57srALyV/nlSU8kcfpJhA/\nmnDAbQL/eNq2y6vwEd43yEbkRmasgne6KNvEFfY20CI0+C47yEh4JuYqsIWa\nmbzEDXaplvT1c+ZtewdjFPnXBZpA2iLxKHL0DXQEUNK1XDdkn34spjsLz8Wk\nkmwY3Si8M/pgQLhBuDR974qi3P1f52OJ81uGDulSp8Iu934MfKPflaJmqj91\n+nruY0GvWE2qkNDn/Jcf+KtKClZ9H2ot05s82DptLurqJ1v+qnz3Qg3PSfu5\nSiJq\r\n=qkS/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAjF6RgMz8l6iW/HDWitBlcmCUk8k3qWO1QhfT33R9DDAiEAkvp7LsA4nireT/PRjOBb/DcfGtW76xUP4hgg/zTp97U="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.3", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.3-canary.825.599.0": {"name": "@jimp/plugin-invert", "version": "0.9.3-canary.825.599.0", "description": "invert an image.", "dist": {"integrity": "sha512-Q4UNZaGISOMfnQbRJY4sDnlfc0/jqw7CuaRci5NLR6Ar4+PK/megouolfjCUVuPQ0+a242/qSt4V3Ynyv9WIKg==", "shasum": "ec92c3a23f1b1b435d2808fa2e33652241da4c76", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.3-canary.825.599.0.tgz", "fileCount": 9, "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3bNQCRA9TVsSAnZWagAAjZcP/1+e7CMCNK7yPKdgLoJv\n/3Ne8qpt9FfwCdiopo+dZrpXMfpZgvI+TIOkJnsJTInDHEFprTzup/w/j0AY\nQ9Ce6evNBX1gYMTbR1VUHi0+Eudl04gaDzw5+9pZ2T0A2fqxaqwB8irFBJ1/\nPEuEvsWoZCSQ/stkSYVgKxhuDdYCe0riGzoZXyAi17jRvRhchq9oqvEEqh70\nzW+Q3aQhDu2y2vVvemLiEgpexTpLikhc7LjwetBqw1SpDs7tBRZa5ZvK7Stz\nosUV/r/Ub7CntrLg4MaBCYT0g/BwODlzfw3lCutHfdbLZDBT7PrE3iOytitm\nBGa1UdlkBDpu4bjAhYw6i93STzV7GI1JkRUVg9BiD4dRui9snr6kC8o2fSkd\n2ivOoapbNHEWUgEnMkln6gImXCqvYL1FdsuZWc45Q7f4fkB4kARzTeqN1rEn\nDqWMqGL5HfjT6M3nFqfrRzJExQ26c2GxfxqTcFTq8tO4+5Tv/EBLrX1FKGQS\nfoOajiPmEbOPXRAd7e+mdXJU9/0IqXQW4uZRA2u2ISGj49hq1DaWDSn5h0yd\noPujyvjYPvqHLuB5yp7i5dcl9aMJxs26r6KxcwbBt2Q/0ZldcVvEVg78wBof\ncbo/fE2vwSXrURiynOkqCLGcOpcowb3991/PzRD5qNrFiNKOQ1AImVgvsusx\nM4xc\r\n=hzGF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDn6pRE9tf1KNDERuVoF789DvpnUGrKsI0M9nnJD51MnAiEAqGgpF64ZJpVi0d5V1c/2+WVuXi6UP9XvxlijXNl8WRA="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.3-canary.825.599.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.4-canary.832.606.0": {"name": "@jimp/plugin-invert", "version": "0.9.4-canary.832.606.0", "description": "invert an image.", "dist": {"integrity": "sha512-0iySZRtuvX+hskk2h70KxNw79SXS0CiBLQps2qY+JmHNxDk+434a6neDawBpxBFd70NwTJw+xx49g0Z2MMoRRA==", "shasum": "bc3ba9f087e26f40dcdab0aaec13b1b4aaad7de4", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.4-canary.832.606.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeBqEmCRA9TVsSAnZWagAAvXEP/186Ttqx3LZiWFDTNuol\nj3jJO63VWL0Xi0di0neScqI2FuoMrL3FgN0+0z4R5ge7SSH1vPsklOGja2iC\nPW832XUdjps0GXT0aZrvEN/4gPdUeEEW720qNVj3LL1pSDaD5EO44G01uSLx\n01/HCAIqVBcvFfXGuf8vrQSbxMksD5EjrpqI4DneUY1WUEasPx2PJdISHt2D\nPKyG0D3ocBZZe8zO81eoGujGFcz2AwwLsFRpbL1pX/Cn8Lcm7zvMAOUIs3BY\npBeOiqPT/LQqXL4+jtkDCpnDQJpUYAvP3u/eNtKEMi+9yQdhWtFzOtOLwKlE\npcJD9rgJ/OZs6D0ZrbRdPZzZKkQ3lcDD1sfJDgsDXxqamS6hl9C+tIatSOh9\nn0zaSKC9t/pzZ2TJWpFOS/A4Y+aPJxAr9P4dRoxrI0cal/u13ONJdxFp+C18\nWP9dajFsCsYjlHfFF3CsHhbyMkX64byjRw0UqZOwUj3Sl5lJqBUCw3c+GT3A\nRBw9TSeFOwKgixZI/wfXtLzVzBfieJ6MYurOD0hfSZM11vdml2P+JTalRg/v\nhQnD+Xrr0Ws8mg5BzIuj+LTNyRGo9fTqZ3aZqhSieqcVHiI0nSyD18J6bTam\npDnMac/K8bCkoBFR800rdOvf+sz9it61A4lf9FPw2OyWAaxaOMjDx9LT0whd\ncIj5\r\n=3ToY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDlW/MpI81P6qDbvP6E8OnIJaHocq6aAcU6MEi7Zip1AiEA+rR8XUMbDWACBzPkDvdvtNLQZRzx4xPQRBPrNKbyvGU="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.4-canary.832.606.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.4-canary.838.613.0": {"name": "@jimp/plugin-invert", "version": "0.9.4-canary.838.613.0", "description": "invert an image.", "dist": {"integrity": "sha512-Y83HuxtT7QUf7q4U6Kd49L5r122eaF2sfpXycy7h2saOk8swpBVks+JoM21lt3aOYwL+J42DBYBmYl/7EEGjbg==", "shasum": "a0571d1fe07cdb709f66d0e4becd8ea2f8fbb4d2", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.4-canary.838.613.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeF0eFCRA9TVsSAnZWagAA23gP/2HipxJ5i0FXQ9c7Ev7K\n6d1bbY5LspOF9HsXTyplVpsQDPox0jeurL8rxlLM3d8SfGJgTkN9ikMfRBNu\nFYH2DyhBvtnDs7aKdI8O+VTRriNu+6s7S9K4j4rkublpX9ygMKWAo1NnFwgX\n06CZaVVHNZ9j7i4Kj0VJlTUiAO9Vcn0MrNoWB+foGCiUvdWXogj9Loy9elVg\n3xmxnOKWA0fDwkxdtjp/tRA21vj/JXyvLBkQQE710gnGAtLs4AdZ7Zw9ejZ3\n+Aroh6k074wCaEfYkuVC+Mf5Fsy7g8UJJxARxvZ2RDRRUuNmvFZbA2pYH0u4\niwzdVC/Ch8VOsYB3ZfDS2rWf3vA3zHiM7cltpcLqEFhkiVshXRldY+7naV6Q\n2wacoFTR1q47sanc/ZInovfWMUDNkWAR24sSztiwMru2XSetavwJl1KPjPc9\nfzMUjEt2Agofd8/PmIT2s1gH2ljhviwXt39pN9ATwTRtiw+tmC7N52TCZXML\nFOdi0KmJmefC7hqn4JkhsbG2ndvhkk72HfO4WxjwAfnK4VngIYYp7nZR0atS\nxzVhOgMXtrODFQk9gG0cTrNNLVzWyI5X+UIRBqYsybd6ynutNz66RUAOe8Ke\nZn5zEsiM/LOWm0AKSJ4l3uOg6D+MhTWSmEtmxvVfkVquLDXaUkTaCw95q75o\nrNMs\r\n=K966\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlDgibM2L8/Kt/k/eHlzNKTMvKIXs+aSXPI/XMTx718AiBUcQs47IzJ2yOWDDTjRvgE4UF3iCO5cf/xyJVNpdrkDg=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.4-canary.838.613.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.4-canary.841.620.0": {"name": "@jimp/plugin-invert", "version": "0.9.4-canary.841.620.0", "description": "invert an image.", "dist": {"integrity": "sha512-fBCpWNdQ/cRu7DNs9jg1vZLv7bgnbN9HtkZhvrCX419G6SPwES3wwyZfIPbvye6cVO1C+qvFtdLm83mYq+v2Mg==", "shasum": "13c26683b6b035a4775f016173efc3641abf9bea", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.4-canary.841.620.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLM8TCRA9TVsSAnZWagAAUY0P/jND8O1t+wBviUAmU+8v\nTYWRAejmTx6KPp073eRmFxUo+PDeykC1OrPNfR2shE54WuIfD9gjdIsHKgln\n82wQexhw+S2HXInfhceo6n257+Df5FioK88xPh3l0rcNTvQaIRcXBWDopamp\n13glVXbC/gd7RtXQswy+ZGIPHYBt96vuO9541rLEvQlKVjICJH+rgEq+eVGK\nVqN1n3jf/k2+QOaWOsoMSR3POk53yfZNppdEhBWVorr6eZhBzzBhFKfgvBPy\nSBJpGNAuDg6+kYVbuDyXIONMliOg4YB6uil8dpFsb02EehwJnWVCfoxWRS1L\nwwaFozzeUte4u4kSb223c1xA1XqXognRAWJlwR+/DtBUzaGx4wNxa/cpQBgS\nT6nePNMQ9NHbYZ3XCZbDXVJEvF4gePYJDVtX39sZOwIgvQ0OrtofpXRs09uM\nYgMy9zSpiTqWT/AXBHAZrf9ufhZbhqeESRoBQjGDx35l7xg6mDCyrHblAcdP\nlGD9s79kxq9Km3s6/MaGW1hwKpmZtl4dXBvAF2ex/hT+mynHgaCt41AGQ1Fq\nZ1DOjfF4QTn7z9W+owFZMyyTt/gF8DaCaBRK1BnxTapQnDbUw2Dw01lc90d0\ncMzhmiB+Hkq/W47ZFNBw95vxlsRBwqGJOCL4/Q2TxXLdzNwV1tfLSvvhXHnw\n+31B\r\n=8atA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGKCZ606cdQvvceuObxj4rEokChQhNbij5LPYDkI2vJAIhALCkSXOT4AZEMpwJHzZZon0dLocKWqT3Q8WiCjuHA1f0"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.4-canary.841.620.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.4-canary.845.627.0": {"name": "@jimp/plugin-invert", "version": "0.9.4-canary.845.627.0", "description": "invert an image.", "dist": {"integrity": "sha512-nqdAMGu5g6YLXyRr09lX9iM8JnRGSdXKKWN3IyESNCOH9/W7GOjZThX5rTn+AG1cE7qeAk8EBpcB5EUok1/ZMA==", "shasum": "113ce529eedf3ab82f4eca00d2a3379d5c6e9044", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.4-canary.845.627.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQwWICRA9TVsSAnZWagAAnMUP/3QMMwtvODi9kZBizb2i\n1T9Z4MynGsb2XbUPDYNVdDr+iNNFn/okme3LViNaxCs37GwQd6S4+HLlm1Cn\nKDk7Z7CCYXAoQeqTyanIO+zOe+hdElg6T2Do+Q+JpDDU3TWKYccYWszY5Hgr\nZAjBrX+q86d+YdPgP/ujjp4XDdDkgVjVV+45Rut8yUe6/6/62zT2ZaOX+SZi\nW+lYSzEHvRRvl9ouxJpUz/StYTOhW/3en1DaIJbuKR6P3yKM0WoGQMif6XnJ\ng4mQ55pWU5Vt+DzwJib53znJ5t8OpSSOHOWqYRiNx2fzLsq1AtQyfHFX2/mW\nW3QzzPdXFUAdN+gJCr91FC5uI6BJZ6HkaidPdOO+olSek7sVGSYu2CPz8HSU\nG4vclDurraLr75mM9m4YCaDhwnrlMHo2UjWtx94EEF2Hnsy4azs+3cHs1tJy\n6Z6n3I0ysCBNujxDuK+hZYmGjTcM4H++ikNI9EI2NMyFM+Sh9fd2m9VfT1mG\n9DMIIBaeIUGKpJsV2ddljupxoGEG/aDxYilpzAHSvWslFeZAVTR74Zog98RO\nOEHaG3pK7vdwXFZkn67n1+y2Kimp8jw3MADDosVzjUOc/3o519yMFd6iRpbs\n6NH/3VersOX3YGO94Y2VRVkEZ0du40gprHX63+eO860WPRN/irZBW3bxh9DI\nkc+E\r\n=2asS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHW5+XpCxY5mPoyblY+evzh0wAAUOPajZEoD34jkU9QgAiEAy7gUtUGOrdQ8wkpk9c45e/nOthdPbRJW4IobN0L3Ycg="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.4-canary.845.627.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.4": {"name": "@jimp/plugin-invert", "version": "0.9.4", "description": "invert an image.", "dist": {"integrity": "sha512-q0F8nYdcnuGEYjfj7CCFNG2CRyCaKShsmW6VETPMt0jQG3d4MWavha1Mg2ogHebJDWY4BCgcLLNjyRbUG7IBKw==", "shasum": "2fd082437511ff08c5099fe6fcd6755b16712eb2", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.4.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXtz1CRA9TVsSAnZWagAAVkYQAI03gVwqF3IAjlZ0uNJL\nebU/v+iPILVtmYkHV0jgqG5RokDXTPFRCTv3KgnFtMIDGJMcUqBlq3u2mZqf\nsVyEknU29Jnl2PZj39s0iQ5VuVEdTdEbk6x3oGyQjigoolr+IvjxMnlSdebJ\n6c/n6rqHLj3bvJ5xpw2Ti0aRWShGzhpU44BUHmEOEhKaHTC7X6oDWOwlBIH1\nTGcBaMifvSN5uIGsKGIT45MmJxisxf7Zb+oK7ocPnArcGqqcRgLWpJ3uXRCY\nbI5gUGdq5BbexVf+F2lBTpRIvHt3CfAik61IoXC2ly2LJCc1OW9sgnRGTD+q\neBXEoM5ExmqjYW8fmnw8Lp1QiSLMH4pXSMw2ZDBOzuR1fE0B2b6jK6EjufIc\n3TdUAftRgDGSl3EotfqHnBbt9nm4Z06lyW/xxEJpp6yxnbkeRA5O1Up3jokq\nR8eVvhVjJgijf7UU6fR8QFnwAh7n/l1azqCjg2e+SVp+yV91AsA1a53JolxS\nt61ZChzSa/3av7nzRx2+C3k4vI41wIX710qeFIHfx+hHGWxkTfnYBuhKg5UC\nfhjwoD6ifyo+M06KvdbHUaqQUM3YlUpvMj6qs87KxAlQVZ+rYaxcsnUU++0v\ncWiOBoBpc8T4fXAQGtiYoMZBZSwYDZiIjRhTnC5z5nGgiVy5zQdEOgWiPT+o\nyrLK\r\n=02WX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMVj4jsc5/lmQlRYl9ARlSMTIkfJ+uBijCz4Gfk+wDsgIhAKzGk/3f7S2Q9A3dUs+ioLhPyzkWYUtHhAaY26Uk+780"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.4", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.5": {"name": "@jimp/plugin-invert", "version": "0.9.5", "description": "invert an image.", "dist": {"integrity": "sha512-tqfMqQqsU4ulaif0Kk/BydqmG5UbjT67dmMjwnDL7rke+ypJ8tzq7j9QeZ9SDFB+PxUQcy/kPEw/R2Ys7HHi8A==", "shasum": "46b7db7a988373c9c43d14adda3b8f001ef5899a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.5.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXuFACRA9TVsSAnZWagAA1cUP/jYEJm104AiLCpRs/n9a\nsw+PCmO8RcMrobFJRwBgPyhtMbkYilmLXNr9Rc+Hg6s8Ggc3GKRzuQ+RBINw\nmRWmJRCJtmtXJVrQj7VIvmbnfyjZY6C5vbq1htlUXY8zu0YcXiNe7qrz3XGG\nvtDAEzt7jBcu0P4LUfkHLyNH8vctn6jYtygdG35pt8BFp5PeyUL0tsBSA7oh\nohpbyMRa5yG+KN577CJWeLt25Ph/darq8BVrtRci/wMpfO9JVuTs/VnyZL3L\nY5auUMlnA2nJHG46gmNHKKywGXGW+cyVtrN0qUAeqUpCPo783Lxm5YXxhCQ1\neO90vtEOHZw1Mq0vw6sPE6kxAYSTGWCCMcahPdJE2wFtjzFUCFwoFvDY+MYn\ncGpvrlny4LFp5ZQrHVjl3D6TaXGNcvWKeWYF/nUHqkzwiQplk1fUgWIvNa0J\nnJ/aX8PHjPrl44+Dn0rrLHg5hBjOAwMirTtNsW4v31kB0JNu0fmJ5wdZPgmr\nhMKMjRV0iP8H6rR/cRztwh5C0IKkjofWycFlxqNj2mdGc0/A+B5cSkkF0Lr5\n6hn84aB3/FcNjymlLt5dml2bAgea4mJZxAVo9VQ92aB1AquOr3rF4AuLhYeZ\n7ykZB6cLh/DVutkf3dUqjkeI5inoq4GDoQtEL8EV5ldVv/rKc1CHBWUsQogh\nLIFe\r\n=jHcG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEo+P8IexGAvBCdwQOYPfN0h2ZcFATRBcWb2zDnX0dvdAiARUdO2LlWMIe46I8lKGBJL44GEBoq4RImPOtS9LLVNGg=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.5", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.6-canary.858.667.0": {"name": "@jimp/plugin-invert", "version": "0.9.6-canary.858.667.0", "description": "invert an image.", "dist": {"integrity": "sha512-hIJ891mKtvKZww4axwpwV1k47g/VcH+nEuYtDkOiYpc3/08j3l4YlQMed6jM4+rxIH8+deDqhjc//ZUuopS/Zw==", "shasum": "6a9a0d87acc4bdcd815536474b79da71c0a9734e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.6-canary.858.667.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeck4kCRA9TVsSAnZWagAAtTMQAJVL+11tUke2hzcra2Yu\nVqC+vEE6brPdq0DzFEISv1jd2OE9eBe1wHJkU5OOXTRt2oADhP1dUYAb+xQx\nvwEJ9CvBJGmgUmTAeLkQ01cWNqe64V9M5wwOdXmF7Tde0VOMDym2Apdy7Ut9\nb8WYHVxO7g+F+mVua3rPKvTQo9Z+SoXaS+GQeIo67zbz/sFguCC/km6phSVx\nqFAP40vfDi0cyCfgs/1FQnOythmgZbRTcFMCikP1Xfh+WxyXQRflR98Dpo84\nY9xdVbA5eRD13hW5ed9Bc0xvPScuOgjBlZqSYjh4bSy5Z7ioZhbNNVQEcFO7\nTOUispEOEWZpnVZJVye9C0+GTbgf0ObtaHYNZHtJKll2vR1L/T7ijfUTfiro\ns9pP1pjXGzgmi3T5VZS6leshiaV9yAb3eGP8jRFFPm2GxUn5C3fkbJKA+IwC\nwJ3v8naKh+59ZQnmJJTlcpcBiYcDAJbPYoPs5YcA0DXvOnX875awjxgjixvC\nUYqG77E93CsBijNxbaiDoec/rzO2nbBBYxky5W88PsomOD8apwrBw0D5Qu6d\na82ucxmW0OHMSJVZn8Kyd/DN2GSxkeEbSe2ZbnNFGb9D1BaLcHuhtdM2iWVC\nblouWWh2+gWAJAoppbqTfGSKQ4pnalwhCQdy//d6qFzCDmnenRAmMeL67eHs\n5OQ4\r\n=T0TS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmgpfrY6gaAUbD8h9nTYEKku+n3Ya3aznYqZvmBGiK6wIgX2r9r6b35tLKkN9eZEdCQoVz7JEEbS4IWlS3JF6P5ow="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.6-canary.858.667.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.6-canary.857.681.0": {"name": "@jimp/plugin-invert", "version": "0.9.6-canary.857.681.0", "description": "invert an image.", "dist": {"integrity": "sha512-cUnJW9MsuuxjYxgn5N05sJ85t5jhhWS4jev77iLf0LhFMVGqxX3ZJ6/6pDErNYIfoZJmrLQX+vPCnG2eJ1CFnQ==", "shasum": "1157edc7fe57c6fc113e3e9d665d672cdd75a0c4", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.6-canary.857.681.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecltECRA9TVsSAnZWagAAXhoP/3J0WNqLdIWi3DenmeN0\npGk42iQ+3j0iYu4d9lJkHsXJn47t2wHK/TquteQCm2OyjoFvzXxI+hiNsYv2\nSyLOX555YXcaOn3qBAYU8ZBnH9td6hnNMoohMm/reGH1wuqjsGDLgqzQ6mVX\nZqNoLINrKPxcoUC5RJt8wvkRECreVk9p/GvGolV3KQ8sakYd/rWYgPi6EN1q\nN8ybTJdEZqX/kzW2+3OxK11vSbGH098EHp+2ZoSnP6meMwPfeAf5uEP8GwEx\n75bOFbkNwVYOFUuK2SKXNAoqFi9/8KtF135RRo3g0EiH6FhzkdMdj9QkW9xz\nuNiYzbiL6q132btJhcjGZhozxv8fX+rg7xFNCcifhxd+omPjeziwzy/1Sv2x\n3qVFN2yroJvw9eosTCcM4+9v0pYmQVHc2EaoAiWk+WaMp9b2dGP+w7282l6R\nU/Vik2Riq7CvRW1dpNgj1uRGoc7znnhdE8N8QT+c56P0oiQvDYjKNQMV3DWH\nPzLtoxzU65JrsHLZTpaCFLszSXhSi+Ahc4ikkbrdL71pDxAINrGlF9w8SYKB\nih7Tfw+jExcsyuF1hQ5K/ypw7QaTuDJ73kTMlmjOLTq9rjKB6eDe3uq4jtyX\npRs6wVeCZOgd3WGzCAfKtkSW+mMKf21TPbHJ8qUDIMofOeBraU91GiVwKhWn\nxTGa\r\n=IuAk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDMwKPCi2DqvatBmS6Ad6OevqCRD+xsWlSDiwRasHfYOAiEAvyoYIt4KrtMQs8hR7fPqezWhPdWLM40Pou/tinlTjjg="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.6-canary.857.681.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.6-canary.860.700.0": {"name": "@jimp/plugin-invert", "version": "0.9.6-canary.860.700.0", "description": "invert an image.", "dist": {"integrity": "sha512-a72nJ5l3W4x3xqADukMq1BDu4jVMCjo60urhZcd6D5UL7uYKlEsFfyfX1P0lFl88WS3hqMyP+InoehD68RmQFw==", "shasum": "d5c69ce608da1df9a82557427601cd805b9a6aaf", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.6-canary.860.700.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecmLYCRA9TVsSAnZWagAA94AP/30HZ/wkvL+vRgZ4BCtP\neItRq2/ihnSU7qQEAOOVS708hfcol1iwN1DYXIRuTSYGWLjtuMekQAGJ7mbV\nhU/TbhdEpr12jHYD6N1IR+ygyE2o7g4O1W0duICB5ghynYhRPL2VYMHXD0w+\n2OAYH1fmjJ6d8LZ3/141763qt8Kp8bDwrByH9pyrkdEwGKg1NdWJJ3IWRJym\nx+1Uq4Fgnct+bnJD5dasgT0zMviqQWhsVG3BWoc5IfaafEraFduqCtc/bUoK\npXvegkRbcRm1T80eS9bC0h1fX3ICxEI8np5A9UD2ZkbtZlMz0zNYZ6L9xpUL\n/6nG7dKnxoCG7Q9pT6q3MqeQFP3tJOQRwEu5TljeX6kNVaAeW2uqrM8iUfdH\na1ezcFqqJNru4k8VW2r7gTFY6G4PAxkOUgoR1caW1rqboED53T+eiOFjiHUu\nzGMaLI4H0LFLIXA0c0d6+WF324q9nobrP0LTI2ndHVhKeJ9ThvLhu572yZHD\n4zY0Lj3sYXRu4kq9bjn85eanTlxDt3QiF/6pnAMvUTVkPR6I3NxGMcM9jhSP\nyf14Ld1EYxGAe9guwYCg5FfZDCgdeCZFOZQCka2N02F7Ciolm31U+8JPWjbl\nb29v0PjfJ3KwkYJoWtnhqW/EAzJGkBnODqLZXyH8GrhMuW+GW1N6AIHSinnB\n6qIt\r\n=703S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhceuIeePEe/O3Xn0vRcX2D4kTm6L7yZo7tZJiLKAGbAiEAoUUpKB1z1KwpP2ZeoQsDkPIikto5qzJt3BfmwV5lwJY="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.6-canary.860.700.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.6": {"name": "@jimp/plugin-invert", "version": "0.9.6", "description": "invert an image.", "dist": {"integrity": "sha512-Pab/cupZrYxeRp07N4L5a4C/3ksTN9k6Knm/o2G5C789OF0rYsGGLcnBR/6h69nPizRZHBYdXCEyXYgujlIFiw==", "shasum": "4b3fa7b81ea976b09b82b3db59ee00ac3093d2fd", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.6.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecmPnCRA9TVsSAnZWagAATvsP/2VUnjIQmLnpb6TnB24d\nlk81f2TR9SwZxeYg/N2GBaMcU4PHaZPhFujZ2VusBH+HKu3krflBIycKYyRl\nvuFC3AXviJ12sqmtMs+AVJ2zcWOkhs2T4KLCM2lbEMTQNGIcJ5MJUIH+JDdF\n6P9Ez2GZ4ha/rzkUxJ95OdoxGCzcNY57Yskp8YfNBBOpzFnnCWA473JGHLxL\nIwxR6YSG30XkuTCQ7xl2MculNcLPz7D+OsA6qWlFlI/Gz1EgF1oqoJT6joCT\nBspk0rPyPq082Gks6wJfsN2evdtapyZUT7wOjIgXIOKxC/kdq397sZeV8+A0\ndj2KbY1w/HJGLdqqtTZUu4/YM5Ktv4yNwVlbC3v7mI5EFkckpCCAaND8jFbq\n96aZ3ltfamMTd0uMvlf/MJRJM5FfFoTQ2gLjP/uZIPux02FQbL+V9K6lHhMA\n/ZVP7MboYFGalUTr7ADp60QbOeceR1g3VNp+6xqoVbc9QchszPe4Z6SEonj9\nqHGVROwN8DNex1hX2TBXpt0WoSDSV45UQQ1hja5XT3v846eUnAcQOq0SfK6s\nxLKGNuS4KkYH8cTR+tjD6xeWzws6OWre7qaLitQTQhGH97VL6HcUOiS+ix18\nmyXH46b35knrjRDcLbVvXIi8vhKh/EXCjarTr82cUgjHUbUOnyKCXyZovozY\ndgIq\r\n=XX1y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH7lU7ytaaiSDA1k+wWF0PV1wUOiY0tKmi44L/rTABtdAiEAlKuj/DV9wRi+fbljZLnZ4aQjOIAx00xnDD8Fkrt/xOo="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.6", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.7-canary.854.725.0": {"name": "@jimp/plugin-invert", "version": "0.9.7-canary.854.725.0", "description": "invert an image.", "dist": {"integrity": "sha512-tb2gBfmCrgjl1cxuBXfzV9p/DB7dgwjUKBe3yv0CZFKaUKfiw8BI7KgPx7jiDGN8aF9zgma0Wejnpb4ZuuvXFQ==", "shasum": "15b871b59d4647de26364579cebd513a4082536f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.7-canary.854.725.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecmZoCRA9TVsSAnZWagAAY6YP+gMbaDUM/hAp76xi8w85\nBo6RbIBmLg0QZIPgSKcZWmGm5GNpmRB+Az5Pet3gHwrEpQEYYYXIBM/tJ/7V\nHOuNql/os/M1xLUl0Z4HLWDuP+jxjhZOKafQ8PK6/eUqxaXwEseFnyDUZFFJ\ndo/2QsBGe1ad8ysU/QdMLTQ+lXEVKK8dCnCUiREdHl+Y6RCe3jtftv9+0ZWZ\nQa666crcClYptodT+XtGZWrO5XSgs7T54pxA3RNGirE9SNRHXX3/ZTs7dAQZ\nA0jW1kHJjv+k+eDFWNBXrk8Eni/W9ag0mubD6PyVhNnX93Xj3iKieiCiXmaA\nISWT71TAZx2CemZoQu5SVbEPtXRBGCMtnAO2cAl/7h8mwl9eVnyX4BRRuFVZ\nvOenBPMvgfIkFVeXLD5eeGThjcaXhgfkSEQU/5Me4Is4dtz45aOPPsjNZqwA\nKJXGz+Otknh2ErpWibjT3pB6BGKczY3Od43+AlVhE4uE3DVIQeLo6/hRcpaQ\nZXhzL6M4UvuWIrCQAR7n28AnoeAzeD2qW1DzvFCaFuKG4kkSOGi3pPNbFuYt\nQ/SCGH0pvJtE4QgRk28+JfuVp6tZy4MMjrgJKmQKfYUozk8t9MuAv/jWpqCj\nxySKE8zwnO3NlIGHygDeLkFGwWDJ9tPR0X3hGNDjmsIY7Qyu38Iu1r8H4fhV\npLnk\r\n=sOkD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4ovsilCyqOvXiYrO3sYDxGxuxO75wm3vsiAuupgucRgIhAINc9QDksRURQbQ8VUEwatJLiOMN4X/SpyvO+apZbJLT"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.7-canary.854.725.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.7": {"name": "@jimp/plugin-invert", "version": "0.9.7", "description": "invert an image.", "dist": {"integrity": "sha512-SjmZ1ftxctGrkqOsxsfw4ENf3bloeMSzfnaw6WZNro9mPRy0gd14kmPIm/45BvnXQFrW8elxbAGCoFkdrtcIKQ==", "shasum": "4cdf3a186a68d696a32fd7e0c9796884250156e5", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.7.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefjrBCRA9TVsSAnZWagAAnA8P/R9m/4LaSlPnLdzo8sLw\n4U2kYzjnzPLhfaGn/jZg0yL2jlzgQfY6F4jkSQL/aY1cy9GhFTzGjS+6hvAl\nKTfYzsF6dHR/Nct8c4yTXw+pLLGiw3CgijABUWVg6us27k8LULtcrGvLmnKd\nstokVhKhO1kwjddNmp7XWqftY9Z22wgWm/6TowDVeikOexkR2sp+flnfMoHi\nQeocmedZX0bZSXbIDdJwYndH/14F6I7Dhtr6bXgnX8iaD5rGIBaxbzuHyrfW\n0LhtTl68vW7GdtIH2bSqZIvfx/a65nfqX93klJYlDVeFhiCJjKb41SqdOkxl\nzAdtQTD/+HB5h17i/rXPCTSS5B3QujU6thi00iK+7IzfCst6EvozapJm1LJZ\nbAqOtsEBdxDUWJDR1iBSsbjz3thAGw0LQQX6rBuoe98cHqmL6W0S45hr8NbR\noVKllxSic6MJ+fHDgAMKdtrHXz9359qlyY0z+QcPmyZZKoJGB3172bUlvhSp\n07e2QJ1tuEU+gVegkl+xFMUbYf7D93i0B9GafTK8qHBgpGJTZNwnaUwolmNt\n/3fjQ+GGwi6n8HMJjsW8zEHW/h6mNRWP86nhMUPJwI1p3It1XVHvaTuruPFq\nDhmGcibsutQdhyoE0HZmc9q7mQEj5r2pNJzoNj/OMeLW3DWdlPDHItXs0TiJ\ni04D\r\n=hrEu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhjfS7oCDDrdEgr2jNfA8xiy33Ums9lL64N28qypNwRAiEA3xILWmtm77y1RFrbaoyUE/e4jRC8xaj3c80hockY7eA="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.7", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.8-canary.866.767.0": {"name": "@jimp/plugin-invert", "version": "0.9.8-canary.866.767.0", "description": "invert an image.", "dist": {"integrity": "sha512-IijyXbfTC4xq3VxX7VAZgdjnG61D++g724VFxlfd5up8Y10e5M0Ul3IQLr/WlKuRnYNLzl24yuL0Kq0f67d8aA==", "shasum": "59d5e697e11f32840bba2e463dd6530be829f855", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.8-canary.866.767.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefoGsCRA9TVsSAnZWagAArbEP/j+BUrGnnXrrrK5Uv5zb\ngM2oDKjEm/EPYYaH6HbYB7RwTGrCRBwwYH16eoK1KiTpVgVPdO6P3a8nfed9\nZGbNo66S1Fp0HOFHU9Gcf/HYIIqvBx+dShRDvunC1JhqRNdsIgTcMHurOAD6\nM1D9QiF4ejkWq3lgpneLutMjZQW/TFZjtXWr1P2AiHdCUWec6Eyf2/SeEVXR\n+HVIshCbh9V+EX6d/DBoBYxIpOcKS/F+vXOXgUZHlN35KCfj2hcXsNRucHqa\nK1CmpvpBUsGR9AX2NoK1fod8q2/rX6zH1XSMdZIp1KyyqyGXB+auzw28wa9/\nnqC6qFA+DLH452rbAuWN6dvrtMvSfBneYCb0QH1FDxvhC3rdskVNjdJb+rTH\ndg4SHFBwwaI1W+27cNJMH245q2mAFpKn1P35GcbHAAMkeShQR73du8IYQ8kx\nDP7fqSC8QIcyiWjUOdELAefu6qM1AgPxFCxwh9X8uKYNCfZb99PPGlfdmxnv\n0+Jl3c+6Pfu1QNxfZy/g8vzj5wtedP24nfiMn5B8fNJiXsHetTz6PBsyyob7\nOCwO5H/WHzVvV1kjIWktBJUlrGJUVLLiEfvF34bUzWbb4bPsT8jvFjATznCb\nEzw/9XI3FvNnUoQWEGLi/Wy3ZTTPPFAW2rKwWEw0bf8TgMsZ5Hnjx1hLhUsb\nWqZ2\r\n=FjGr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNBd85RoISts3NsW+amUDtMOMsLGtN4jtDeP1apGCnlAIgChpMeYFZV/gQC6qi05XB9MHjkCV4EEfPsPLP8IAiNzU="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.8-canary.866.767.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.8": {"name": "@jimp/plugin-invert", "version": "0.9.8", "description": "invert an image.", "dist": {"integrity": "sha512-ESploqCoF6qUv5IWhVLaO5fEcrYZEsAWPFflh6ROiD2mmFKQxfeK+vHnk3IDLHtUwWTkAZQNbk89BVq7xvaNpQ==", "shasum": "41d6e87faf01a5d8fe7554e322d2aad25f596ab1", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.8.tgz", "fileCount": 10, "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefpbECRA9TVsSAnZWagAA+dIP/21nbj/Q1/rk+YYjz62F\ntKsdxLPzpxYxaMqGDMWGV5RW+FtIvnPJOXqsngCE7D0uCfPwMmoEHRUh9U+j\nO9GeYKdSTrVNjDkj3j8E30MCRUjiX1/bxqA300ubVPtxYb+3VE+nVOdst63H\nAhhiKMCDF+IZ+sDAwd/CvdEdfneM00OLSap1uI3ceICgq8q4VPrrOk7gVrFr\nJFuBtvrIkZUO+UFftA/uizZaShPyXIZpq1S+96ah8uhCgNJxTI7XsBz8CTAI\n9HbdBKZUGQ5In9K+KX2kLE/BhPO4KYE6RVdnJUuQzHYvdLmy8XOFq+CZuO0e\nIB0IboECzlHWjZayhSVktG8i9bSjQLkZ1O3Svv/buFBL9wWwGLwINFZ7KpWD\nKXd+foV1jtbLJO92joH9H/9xoHGBoP5Chhss74fW9rgGDwHqdSyxq94qqjlQ\nOnmHMhQzY0Q4nFx0VPGnfKbMMsTehu76dFzpWdyy5pHwwW5rZninHrNqsyRN\nrBu9vVAQCodd3/1t3P+f1K3dHe9AjEESldOiRN+tt0idixwjimrv0JGA/G5t\n6ICEuy3fMz1IPdYWJCA3kLtlxToo/hhV57dNuK9AseDDfulOdnCPhSKOJE7i\nIw7fHwCooJ58LXR77mCYftiIfHrUl7onKl5UPbJQvcwfuNu4CwGk0cRFZFgJ\nN3pH\r\n=x2vc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTeRE48DDR1+rmFpoK3ijecJzwbXVdd5zsMqYGDsNHGAIgT1Whm26bFvLA/YJEH2uRO+sN8Bhc4mAFehdI5YmTtB8="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.9.8", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.9-canary.867.792.0": {"name": "@jimp/plugin-invert", "version": "0.9.9-canary.867.792.0", "description": "invert an image.", "dist": {"integrity": "sha512-b111CTX8nu6mSDCFJ3YgNCVqXeONcfwpEbp1emz/Rsv3Rhu+QZ9ANTRfwZlhVhF6VbJnN/xSzafdqE+2ZdUO0g==", "shasum": "865dd7d7d29ed833d74c14c87025cf39e746d507", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.9-canary.867.792.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegSU+CRA9TVsSAnZWagAAXvMP/1VDWaHKbffPTvylF4Xx\ngwUnMNFuBRtNEvCoNnrur7cxpVUrPV82dUvWp1WyBKQLqV17viSRUAwU+EXE\nw2v//CD8h4t1/dL3aEh2uM4vnoXxMT4r8Vtvwp41BXc5FmtPpymSuLg0/BW7\nhj41/z+Uo/l4io8uQiOOVrbVPt1XzI9fvyCoEOOIBmXo19/Rp/JL7EK5ri8O\n+F5WnUltSInU0MDq9I7LEj4CVXnnTZIGaQPA9elFQZeWNGMnxIXVI4tCS0NP\nUfB8yqP7aixjYiM9JxbxDtJ3tjTb//r3DyuY8y3U6IfJkJniJ9DyAJxZSwrs\nz8IWdfBGjKVc1d53bUH5FjPrt/rUtcIxNswavgeXdEzyLiwWDA6sxCJSCYsP\n8mTp0zWlV3YC7P12cit2N7a3hnJkvjwanHIuQlmisMtLE2phtiIAWmkZauT5\neeGkKhrYGFaETY1cRPDRTSL5HIc/FDPOBHiGBF9KCN/f4+9NXPZhvpDyvVxO\nd22oqOA2P0kA2qlXBV6PBp8QCextI/ueGjBo9yPejLhk4h7xHDInO/zl7yMN\n2E4pEAaR4V15QzTu9Z4HqbBV4MOzTnq2uCxmwR7g6FTK2qTUU9kZDX263xnq\nlcpDX6la88bMmfAEN6REZmZErDoSak6we4IyIrpXw4ZFfCYmdRecZC4uMF1U\nptB8\r\n=YbCr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICq2gKdR84XJ/7RhnQrLC2I4CqdesS7eYfdceGvnz1lUAiEArA5uX9O/Inzg3llNzEVxaaw6mD3ENI50ku9BYW2Qmo0="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.9-canary.867.792.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.9.9-canary.868.799.0": {"name": "@jimp/plugin-invert", "version": "0.9.9-canary.868.799.0", "description": "invert an image.", "dist": {"integrity": "sha512-bCNFDOdrFXS/zszsNqxS23WS8M/iBJ2rlCnBkuODCDyPRh38KJQTBORpuK2fjNAzf2vahmm2ucweVAPUOQzYHw==", "shasum": "622b79a04ef74ed455d47327f4f34e47e92132cb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.9.9-canary.868.799.0.tgz", "fileCount": 10, "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegUhpCRA9TVsSAnZWagAA2C4P/2tvKBVsCK8Seu0alFX2\nOSmHfV6Er/3p7i6ImXyffLDsWz6kzOc4W+hsTd5qqxDXnrH4RyOqnKywRoNy\nmUrL4ki7JJIuramnyJfYt1EtGkrdtOQac8zC0ARD54BafhDS2G3MHFmBZETV\ngIoEirQi+XyzZ5JkPYffBnljS7FfnGuhU6G3LyurHOnQutHsRg6n6uSBStRs\nOO9oqn5C4FydNTL5WlnaIpMizd02dH+1fi1BMi1BA4i1rJmtsoUSYBi7tfVs\ng8pJVYGpDQlquF8hjI602+yA2NrZ8Gsdae/gptWbCjz6wxyLEDZSHCZIwDD6\nIWVzrkvdbcFd/q//zmyqIFNvJLKbbbhZy+TCLqCvZA8SjIJGH2W0nn+WhWHn\n+aKBFxCjmLZJyh/9i8XDnvhwp2b+KLtznAGduwqLnOu+R8fm7qYzvGPTayci\nCFQBEGyiCWfzD9vV+uX/E+v/bq1hTgPblJLYeNwebRHp1iNkDrtuoQTYhYO6\nZAZOMJvWmvGOQ2FS6UqNvHDwkJdwNeCbYC8rB4IWLeEFx2SPMYJurZRhxvq5\nZ32k4fPy7OMRfNSG9ABune8gLoaBfkwtE6KBvooqIDXJmH9GqEaGn0n0RbJ0\n/hX7oLfAJEJs8WpAGV5vW0tP/chbAggvx8FsSkotTpGyTUPNv7kx/ydS+e/e\nA1+Y\r\n=p8h8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDt5pOCwJiGClYJQIbRUGHlWsaQLtstTgwH+FADjY4StAIhAL4XrYuLi/uis+xPA9VtpuesFz/z4fEfEjl6B2STpGpy"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.9.9-canary.868.799.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.0": {"name": "@jimp/plugin-invert", "version": "0.10.0", "description": "invert an image.", "dist": {"integrity": "sha512-QpvABEakobueEoVuHRPOTH+Habf8yzjE5zLYPDApt0zG/u/dEsZuyWcUdzstDEn4paH49X4DED4odCbExX/mdw==", "shasum": "c71894fc7bcc09320e0b63aa0e722ab6db8853c3", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.0.tgz", "fileCount": 10, "unpackedSize": 9310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegVtaCRA9TVsSAnZWagAAdqkQAIpKMHeaDtKLaloSX3Un\nDqTyOQME45LFBm/HXnzoVbryPgrxRW81+UaBxaq9J4YspurRnJFcSnzT/SrW\njKxNw59qkJfZGLQuESomDPJDmTzYWyGRUxS1zWmLK+z6vFGk06FLoLMPuwFE\nURuHfRmYT1RwTJaiAlIxIeFVKlhbXjXUXOV1o9lvkwsbDUHhO0lw2LAABfLr\nltvuEHv1pRHoTt6z10cQFNKk9bTdajGCWIdzSkN/dgSWpomWPbbIiFq/KvVJ\nyx6V403p9qYbZlfY84jHjAJWYwxcfrDf/M0+ZchMW1H2fas9fA+hgFGRulIa\nQJ+7ZLCoxTTNDtNhcwyjxTP9Asj2b1FqtTt7LBe4FtuLD5kCrexYb6WO1JAb\nFW3ugUXtlpPK0/6V6xhEfqwnZ7hgYYZ0qSUjL9ZEcIKCUFg8Kj8E5v6J8hA0\n5315s+IFy/UG3VT0VZuVp43YP0xpfqq74Cnq06ggVY5xnX/Ergm+XZM0dCMp\nLJ0xz+0OAAjaizJ16PGXSWyorH4voCNW3cdf6tNQmAwLc7c0b0Au5kFrfNr9\nknAuRs4DJDCNHCS6gq67UVNux3o0VsZjI37BE/hSwzo0MM5C35uxCNDyBWQ+\nNDXR8ObciHbJS/DxTd5Ki/BP8u+nXmlDcDRY+YN19tx8lsZgE327lEb/Fau1\nx+be\r\n=UL/3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPYooBvYM6m1SWsslzvVlcxSZ2gWP0dGzx6QWLZCxGMgIhAI0UwJN99wkfIBOwBpU0pffJP24x1TQFC7WNuBTpIb2v"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.10.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.1-canary.870.821.0": {"name": "@jimp/plugin-invert", "version": "0.10.1-canary.870.821.0", "description": "invert an image.", "dist": {"integrity": "sha512-lNnTxMVdIHJZI/GnaR+JAACAvDCiC0gS8npX0in0dtxDUsBkF8Fq+thTB+4sP3H2LFHqFVV6a2O975OUnnd5UA==", "shasum": "12ec6427d3a87762f056077526bcef2090475457", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.1-canary.870.821.0.tgz", "fileCount": 10, "unpackedSize": 9343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeibiOCRA9TVsSAnZWagAA/AMP/RE1PkpRFADmiVwkdQIB\nS3H5kr4CruCGdh4Ik8nhtxABA6FFIjzTz26u1pWbSrwNXlnpcpiyfg78ZMYT\nrBaGpuPXRJbcIzsoqkscP1Fd/SmKFRtzfVemTBYWiPcOjkLqZ6EdBgypA8R3\nueNe1PTvVSAWpD1u0ScpN6FOXnIy1qBFfz8/1+N4S/Pnmn9gDvp5iRfsvXKx\nYB8rmsKFLso2GE6ayBZHMPEUcSisV8IeuomUNW50X69mYCTPFPNlz8z3NABp\ngmECkglFOr7paGe4M3HPsgLzyDmGAA+j9OZo3hEPBXjwjRhaOLJUOiu2PMEu\nt493GwGHES/a4OxqnYxaZ2EMY1J1NnxQMkTlywLjIEXQefiU9sv0hweo9wfL\nDTR4aW7jTjCjuCWCdlS1NyIJjDZ3xE5MP+YgupcA7ofQMH/W2pDnx5mOoxO+\nouvVQI0oYTLuD2KmBD3GLSshzZq+gh8OD3mKOacHjYBP6IOpQFVCk8gjmHa7\nejwww44zpq/LPDFl1vEUpUt5IRBPfDYaUbXFqJX37HmfXCYK3dw9Pf63IyZR\na5/Y50gQitqh1g9wrWo6FLi0dJ5jFT1qWJIxSMTyE6TvO6F4BkYwsZcr7B8R\nkwruXwO+C+bQn4MmCt17IyBS7ctXdLJA1PjHGK7PhQbdHf+0M/Jcr8OJa4cq\n9VZY\r\n=Q8U5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpOIwZH8gH7DIubI7uFVxMouOrkp6Muz7tw9J+RIO+YQIgWuVCCWCpZforkO4wsWdrx/HaEGoDEsJK1vToLaAb0Vw="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.10.1-canary.870.821.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.1": {"name": "@jimp/plugin-invert", "version": "0.10.1", "description": "invert an image.", "dist": {"integrity": "sha512-PpTUbnjsAkw0nZnbZWrKdsEW46MARhzzabBXy/XCjvutG3jzoO8EL19VeEtcrxBml9duJbaOzdzYmbFkQsNINQ==", "shasum": "cf5e711c1cd864bb4fc46bd00a8b2a40d5058a61", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.1.tgz", "fileCount": 10, "unpackedSize": 9310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiiAWCRA9TVsSAnZWagAA8YsP/AwHB+CN7AhRHVs9FxAW\nzWRBXl8p1KMiayGobURRpKqGE3T8aMNhs7r940P/4YZqujAlW9DA3xj2PAEv\n5qvH0ZsUIl9bxSojC4NkBEV/1Zq+7m1KMI0G8NR7IB6tYFYPizMaMi+6pYYZ\n0C1ZcMsE+rzM8TxYjUfje1+qTdU7bxbhQRf8BcRI+qv1ktdcM+48NBuHXRbM\np2Q9w7mPDr0RTMVMGOMsQ5/7YmX0cH2o5sdH2yRDICQQwTROByMAhBH4EiS+\nn3jnh+muZ2ZuqHWGXhC3VJ0EhoyNkgfuvVl0AwqpqMIkdPArdZzgsTCy3I7p\ne3K4MS9MYSDtd74TF6EfIeW8/TTiLtejNR2jz1UyfaiA4cz0lDVxGIbLXuzd\nOjxiYT6p0xoaqDziU+Vbm/i4Szz8uX9Lcx/shwscqI0ARAAhwR4O2h7jxV/l\na/kt1yNyxMpWyal2ecDQ+5yWWeSFTgOgl45TwC7ruO6OFbyL9/QyotN4BFOq\n67vX7FY0SrJKuQCmkvE+vEGNtT2WG2oChYKqniUgAG8i9E6Gb42BMNDix6X6\nLTZndLYdfe+U88rvutYi/XOjLgXUY5S78ie+v+hti/Y8ikLt8A5LaY/suSlK\nQBCQp1FYS+ZgT0IsJYKzl+JurVfAymZwNxWDM0NvhXIZKamXVzVkU8qxzBJZ\nNJzO\r\n=Kzgj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxKQ2tuHJaIXpSdzTl2zhzj8Y1XLlosslazA3ILr0KSwIgU7KxrXDqLO5Vw3Qb/deZPnQTdDKQnhCVkYGwEh8WTP4="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.10.1", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.2-canary.875.842.0": {"name": "@jimp/plugin-invert", "version": "0.10.2-canary.875.842.0", "description": "invert an image.", "dist": {"integrity": "sha512-F3AgVLiGuqxeY30ZxSzLwdOIKkxtSwfmvxVx3xtEKwYn494VvTZ2CIF08VFchKyXGUe31tOCTi00ZYf3ndMvNQ==", "shasum": "30441d22d1c6c7975086e0e4824e84789d96a147", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.2-canary.875.842.0.tgz", "fileCount": 10, "unpackedSize": 9343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelbmoCRA9TVsSAnZWagAAKaIQAITeZiF75Abe5aDksm9A\nSNiHsnabiXYQjUGm7qhoXIwzNXdM07F1RlKFQI4oPsf0T1tND9cYORjy/ljl\nP1Z4pgAGhwtHmQ0i7LvMqp+YsvooR8AeHvIfm3dO6Vv+yPyTC8kv29x87Y72\nayw50E3+iiqibVij+8T3oKe8xyFxOqt5bo7Kblu6bfKakXClwIPmb2behr8R\nFNtmtAJhSNPUZSYPn/0BIxXKgcnpWkuFQDOmesGsaC1EAJm3/A3wQh4nphKH\n6SqpZu3s5rHxQvB1oC3WglztQ44a2Fgb5Jx4BhngkLR72ltMrL+GGC/1Tu2E\n663gIFC+cHt/xBFPke0Av4xx3jxLu/zWitIz/N71Qz4YK0DAq5B2WREKhrfs\nDuax7VnuUWccsBpRohMHjomSENqeXkGrQIhCv1hYoj4V/1wtwOrOKCTmIRTC\n5mBmhro0beDEhZwdq107Dob8eAare4xndXWgEGGdvfcq6OFSwXuWjHINj+CD\n3NKJxDrY2OMTjZBz+++VbsTcttXHuX0zhRtANnO9Vx1Ga3B+TJm8NkBEc1Aa\n9Oy2+mzBITfZkl1KIi8Q+Z/I7nqms/EvlI2Lm58aVHeOox4MpRqYhlsVM2Ha\nrfhVl9kChShTEiI3EKA2a7Ury9lVl+/7AKwzMF15VEIhK/bf97gevdt5Jx9f\n637k\r\n=ysg3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsWcWj7ekmYfRz0U6wfNn1Ear83WvIJV25FAtqss12gAiBzptLrPvVecfMF+ixdWk52FTTei8Rt5cS9oeVOp9dgwA=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.10.2-canary.875.842.0", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.2": {"name": "@jimp/plugin-invert", "version": "0.10.2", "description": "invert an image.", "dist": {"integrity": "sha512-zm1NB+AS0fTKW0gmFs1Tjgkj892gtnDicyxzmYeCLoQzPTr/1iPVf2EGidCS88+aw04sA5DOu0UX7637ib7TkA==", "shasum": "5bb2c08dbe813d198d9bd085f0c763a9ec8eb977", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.2.tgz", "fileCount": 10, "unpackedSize": 9310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeldrlCRA9TVsSAnZWagAAHsYP+weOH9jzyo0/VhIh07tS\nRHifoXYAVFPqREj34HyBWfwnWoIw35ncUiQxLsw8JYJf7Nc4J6wyZsWOUGHJ\nftCtt+tBqZT8OKmWVsyMBAZPrtWJ7WnxU8KP+CVgJ5i8bQpM56kuZIB1b3+y\nUZFZLHRjmGw8Z9Y3SMo0WkZTjTQMeaIWEwHFxs3WPQJA+clnFfnKb7emW3Q3\nXij1l6rOI1PZmOp100dbxI04HHrJuArwej+THfiPFU9GXKAG9m2XBIvZziN9\npGUzM/sBFCzqU6rFo9WiuYdmXPIQuBLYLk+yLZJvdHpc17A1YLMMzM3xPzdf\nh0cOvPyJasS/s8TtkY9WQIbmPm+eutY+cCX/klBCiPXtZEr+OqPiLo5H3iwG\ndaXVq6rrlrG1RVuVT/PyPt26afQx0z1Uty//QGRqbWTyhdxdqAg0AZLamkDE\nWZkakMvYD9I/PE00P1s0t/qh74W9g97lEB0EjwTFmkZHDQCx9PEfo9WwOa97\nnjX9VhYALzsr3kc3cbBJPAgJqbp88HrignlKEvTjOv7/TNJTTEyCSF28xeoU\nebGhY0ceOVV10Pq6KkeyRDAYSZJbC0dzaf2q44wR7vB+QPWKnGxJouKGWH/h\nZSj24FbhnHrgNH2vVkPF9GA5Z40pJ8IJzzaTGG8fBbxtH5akZkZ8f8zv/BAL\nMxwr\r\n=LFi7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFmFKcIbUeEsnt3174tMYMt8IyNaxoHCt7cRukLFgRSNAiBEB/8/I/xAFD3qkbULsidLo9sM9Mhts98KyDEdlNRH+A=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.10.2", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.3": {"name": "@jimp/plugin-invert", "version": "0.10.3", "description": "invert an image.", "dist": {"integrity": "sha512-effYSApWY/FbtlzqsKXlTLkgloKUiHBKjkQnqh5RL4oQxh/33j6aX+HFdDyQKtsXb8CMd4xd7wyiD2YYabTa0g==", "shasum": "6b7beacbe507fa03eec87b1d6343feba80e342eb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.3.tgz", "fileCount": 10, "unpackedSize": 9310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenf99CRA9TVsSAnZWagAAXYYP/2yrD1lzhoIlLC9EljL0\n0AJpRY+fLf+WHcaRyOP61vEiZ9jUeoayODk+fbJuMzcUxW5bdBAS2BBJQik8\n5yEt7FUUy0sdJdNj1UZ+c2tP5TK7VpdELZqEmXJkpW9tqaEUtvDZb9ouk+Cl\nO+ljb1ttBk/VAudNnYiVFxU3RLs7EsDCSMUIW9MJ9mNpj/IIV5cSkMc7luWg\n7G4/vtd7GaSHPNiy+a0P/2EYEKdh5xm6/MKR9SyqnuC3Ji+FH5haLrjhdrrX\n06GAVWJg4tlBqsqtPtPC5eZ1xQL0ybxqqtYu8uwHzh70dsjEgL7UZDOy1cj3\nspW/G1CaXcnLnhCpoIs1LnP/iyQnnBSZLLN8fJ1oj32XIajF7eRdQFLPDqoy\nARHWlFyxgEaSrnio/sf8ju3YwtPVbZ7lKrkPCsrMtj+dFjz0IF6H3gXkzwJW\nj/S9rn/8r1qyjVcEIdEwqmpOsCj+Lx+Gk3wdvEt2RT4lUHDjhRXZ4RGBTLmk\nwqSzoInthR6LxFnn05QA2BIPc1LQ4uF42S2Do453emJJDuJgBOrA8ZZ3Fzcy\n2k/zqX9BwBw019uIL7jbrp8chX9OqkjiZDDAbHw/W3aqir6WdY9oeuKWob9Y\neoa9j39BBHb8DKiIHKegm9XbqrViIIz6hSVP/uYE1a26z1OA68c9NcETucv8\nsfA9\r\n=wCwb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdGeY7H383ojciqwwNeOHsvkmJPHI03w35dQUNvj94GQIhAMRY36vc77KnHIbDSgDl4QAysmBG1dXwhzaAsXd9WLAP"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.10.3", "core-js": "^3.4.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.4-canary.882.885.0": {"name": "@jimp/plugin-invert", "version": "0.10.4-canary.882.885.0", "description": "invert an image.", "dist": {"integrity": "sha512-neTnNWT2463TsO7wNp1guu0iSmJlQeqmzdCHceRWnLnBC3EwX8n95M5ZPiSGXoxbmIXFRhhsBlW/DWafkOxKKA==", "shasum": "73fff8c5f8fc72369cf99c592f784185fe33e2b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.4-canary.882.885.0.tgz", "fileCount": 10, "unpackedSize": 9318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerOqrCRA9TVsSAnZWagAA/icQAJpRv3R53a8aCrbu6H8l\nhmpPfWOVQcsaAEz2tCSdRTWBGQfjyXo7fThJdGg2SeLejCLg1P7J91X3O9IB\npfXg7aQD/hx8ZVZIjZtBXbxnja2XhJoSTLp5+wfJfuxR08u1t7SMpNXYMeh5\nfS9jjGjq24qjZcZXch5PEB6PhuhAgC2/G4j0IamLVX9ELBkmyqSMLwj7wayg\nsuKdshuQQkd/CvwAwN3uxQUk8u+dV2H8fto4JcvVTx2KM2o4RDBkuBjJrfca\nlPB22iarFyGlsmS23mLXG2+EL3Br6fz7e6kseH+KTO/AW+t3HQ3koAxk/UVa\nHO5qAMTYyNqzlYYmfGZEhc0dMUZTGSnSh9JbqJA1SFSWre7TPNJ8Hq028xwT\nyAQ9BUVWShFTDS0j3+1io1tMxcTppJxcjVsCgcxRUcG4Aogd76xv8//hjCgs\nccwbfAjyYcX3QxW15SRWsdvl4GSlkf1lDM+ZsSBiBarRqo7l/cortbj4gpKj\nLeD0Ii3PP1H5R9MCPRQ5ADjMRLiFLwPpzs4ciz0tPORP668vfODSWOHt3eL+\n2X3UmmMKeZLgOQPpTNEcK4hJA7V+2t2jQkw9zt2ervbAaqzIbyNu08a1ashQ\nxfuCYpOz9niMdo4oMkYEzaPJXfmnVSZnnjP2r1uGfBQcF/oyhbslnpslDHbz\naQqt\r\n=RkIy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGKZlWZyqv7DDKNst0UVllcCZKgBPluQ5Ty9luzJxn+FAiAxMHKtHiJusXmx2Hm6Mi4jQ5TX2XTCNFmEnsVkjuaQnw=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.10.4-canary.882.885.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.4-canary.882.884.0": {"name": "@jimp/plugin-invert", "version": "0.10.4-canary.882.884.0", "description": "invert an image.", "dist": {"integrity": "sha512-FbvorpALprWtT8kTe2SfeGDDGnYNKI4EBL2fGFLjnCIAYq4yvYQQkbQVy3eLbectfXTnvuSUXLpPFHK2KM5FKg==", "shasum": "d257fc6b0d56630a0d9825b857fede3381939c7d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.4-canary.882.884.0.tgz", "fileCount": 10, "unpackedSize": 9318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerOq+CRA9TVsSAnZWagAAOAoP/iAy41JGxcmhS5CfBOpv\ntJJprTj1Z8jJeJHbcOUsUB52NFXKSVzk31/WndsHXxSlkydY0E8n88nth7mt\nieB/XsHF/gIjKCJa4dV6E7mcPbWcPZZAz7zOYw3htVqhmCFR8ImT245TctHz\nA/2wqBtRGLBZSTYFcI5zramoavf4OGSRsaAvc5bcKmhJPLwABgTKBnL5S9rn\nGPmhHhUrK7IAdaajpb6+A44ZIwUheFrtm86azAkCb8iJdEs3T2RJfdOzDOlS\nXJVlhABxKS81pXfBedl8l0U8TwP6nKDgNtDouBdAoa8ruPkMQjsHDhiJ/yHH\nkwkI4vzhFdRCSmrh3IOHDnNu6kkr4Hcug5drKNob56hAI/+DTg/H5MMuM2wH\nbxekPhQ8PhEVvFyYivevEuCZLsOhqMaJ2JoNw9QpPCEC7LgWv3081j7U6qa3\nDC1cA6rtMsRbW1gTNofUf/oG1fizUlbhM0iS349ckdqxoAad8XNBXGbRtYtF\nJthPD/V9Uy06Xp8hPHve0mC0U6MFugmmkbtA1H3LxNQdM/JmS/ilNiPCZARy\nuNXFy2lFm7Jv0OT7wfpGY3AKmb4dszKSzh1k9161DveZOYKRguRJloElc3R/\naNQGYjMphrKC8O72ph/bjqiNgUzcOP+yBWA3XrMkwDdE8UMMJRvvroFSKgVk\nzXuM\r\n=MGPz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdKsvHZmN6A7ooMrhVH2maCbqVaBBvLQXx2EvwPNpJsQIhAMKOyShpr98enrVu4uH3fG9QeytSpxmUJuyUVlVnjcaB"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.10.4-canary.882.884.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.10.5-canary.882.886.0": {"name": "@jimp/plugin-invert", "version": "0.10.5-canary.882.886.0", "description": "invert an image.", "dist": {"integrity": "sha512-ubhJLZBDCa/er9VIcMXzi1BRx60u9l/eoAgaQg0j1mNLU9/Pm7zwtJch7UAphIBp5RMY7vOSq9x38bRh6yg/JA==", "shasum": "1b1d246cbe4a75c4da8188dcbf93c5c805d93536", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.10.5-canary.882.886.0.tgz", "fileCount": 10, "unpackedSize": 9318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerOs1CRA9TVsSAnZWagAARVEP/3RK8VViBj7RKRCfTAS0\nK2sm4vZunrzXwb7mLYh/T+56ROhOqZsuQJvb2p42xWFGQep0rJLPIPgsYo/0\n+uwdkhQHkT6mUbTPMfm8J4HqNdYnVYYbVZirw4UjVcTssO7LwLpj5d5R/kCh\nEhZ4iZUOeoGHzhFd1o9J09LsWFaJ/8sLq0WSpfCA2pW8mU69DO89AGagUTuR\nGuZ9vZKztATDUzzBkbwG+KtJUmDIFGSTTwOR4oGO84rFpBV0/R2fPR7MvIoL\nkyIsW+9aXXa8nD00Ozq8FE4ucrZDkh6ZYtwcjFFZCl/ywzCM5w5yH0f89WjT\nvgvTCacriSdKgBYXz2kv70OoZ9DdBN3FnyU0Q2UdxexCx9HBCUnf1d7AEole\n2NnH4YGXRRMJoLziAJI4FBysY2uhf8uuZtv4bwdWP9eRVAeJDxH1aJ1L+4aE\n6i8JQxcbVYOBCLEmlqfGZ6gS1ck+ig7K5wcyrMNl65e61XSOSX4o41a7Sa77\nF8cDWJwyFbezRb/iY81EPf0zPSuAmVBmo18+3UxCqAgNRNS+QjEGWV4n1hqK\noAyxuLyWe0vbYSWarDhDIasBFoiqFyunIREDsm56B1HSLq0v/VnE3YfQq//Z\n9WowFvFJWWTe3jq58HeNeLSFmjwmLgdV0N3/s+HWGDC4Nk9QoyBNF6tnZoWd\nXmyn\r\n=WRjI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDDdu1fyeUkSPwDA12qfUJdvqG+Cc2gZPi5GhyY78MdAIhAO+HDNYEXCD7zfwgk6zTJ6TQAu/VoYMGCKdqQIyimTjF"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.10.5-canary.882.886.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.11.0": {"name": "@jimp/plugin-invert", "version": "0.11.0", "description": "invert an image.", "dist": {"integrity": "sha512-6USMsP1xx6+wBdr2rWwuw3sVoGiGcfh6hrL9AA9Qm/FJ5mRZx82TwE9/CfdOeyqSSl6OdELLI1tsNHIeip+9kA==", "shasum": "61ddce3e5c2776814e04a3023af6a54b57a0ef57", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.11.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevwfaCRA9TVsSAnZWagAAB7cP/20zaCUm1Ldeuczy+fCT\n2MFyqoNI5uZf5i6KmxqLqcKRTm45nmuxAZ5l9urmtC0H1ZqptuuoHHQKompu\ncxENmR547hMs+xhzYeHjLmtn6yaPQ2gLw1bpuWOrASFgpnxFrrbWDZ7SjerB\njcS4xkxXHsUyeZSjC3yx22p1S1Cf5BjDJd09DTaun93VNm1nVZzJK/VYD/+c\nPzQSrTXDWq6RHgcDcsKsBIh8UzjmgotCmMUGDv1bqeGsBu9mCxczG5sYInqA\ngjpV+VBdDc6fFhTqV85B87zG+VfldW27q9HZqDcX5I/HGzOvqTbHuv6njfbE\nlw8h81HXgy7eqqORl9oypr2LaLVG4p/jxUHnhvxJa1tQXev5etMi52TT7U49\n0bB6eRPwK7rS29cIfMLroBMdNeynY9Dy/VjYXEWvDt3flkATKwCAVtISUhLi\n8ANTeiCZKerRk1L6ScK8QGFTRHAdcG64KNEutjlACDezhrie0aSus0vt0cSi\nrcNqytqQzs+rvQ+47+EYW8W3j4MNfK++KKEx9lQ7LrlPTwevPiB3Y1G3O/7/\nGKAuvVNG/oCRKk+4o3WxZbYGHyP7b+Tn3DgnTsHgClR7/w3F4r5g8O0INvTJ\n94KtTFPmsDXXVJJ86oWWPWkOD6YGrwqZ0Dald9rGuTanQwrnRupu6JWOq6Ll\nYlHc\r\n=TaOm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3l5sC3N2Vj63QS2Enpz2rOZM/W//hG4B1uUZlqSqqYAiBZJYVOKeKZwl2mX0mjJrtR/en3XGH33FQzNx0SzFDirw=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.11.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.11.1-canary.891.908.0": {"name": "@jimp/plugin-invert", "version": "0.11.1-canary.891.908.0", "description": "invert an image.", "dist": {"integrity": "sha512-bu58BgG33Gsc9gcHrgBzfWmLb5Mc3EUMWZxyVGSNEIYcGcKWLf3L48suWgFVxSLaBjJK1PffrZTQu1qBbY4hBA==", "shasum": "e2835fe4b24d137ce5ac5c76ffd97273bd028b51", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.11.1-canary.891.908.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewC+VCRA9TVsSAnZWagAAj30P/jNal6rk/OuzNisi/zgx\naMet/RfU3KXAEabJwaG6+QdQx0V/szno2lGk21Bty8kEL6dHfvEtSNAv5LT8\nztsWzKIUTSY3WDbNNNdwXvYe5XyprE33UR9t1H1V8HIoU3b/T/ncGncmLV2W\nN78/QwXt7WDHm0m7BbGUIWOyRPvcFx8M+kS6WHsakCndm+wnrjcKOMM9qXgu\nfO9Q2qR3x/NXB1Rj8VWE0I5WA2knV1WA9tto6kSuoNNeSEjzkBoDvgAV8EQO\nvXDSSC7Xu/Ryy4wD/DIYgj72eBqieFKvwxIEoaNZ8xl+atEdQ8ihXwBNp+vw\nFPJnoQTFz5DR2AdsE9lInb2uQNKrtYgs4C5J3o05IuZy8211aD/cs30kHcvA\nrC4SrXOebGoCQvzMbq7TltTWk5P50H4tqXi+TzxmoeLmXxuxf97mgXHGewKI\nhxd353u1otNz2YL/ICxfrSWvzgedPg2LUomBeK0CNv2rtjVYSoO3r/DifLvr\nzdICJ+UPG/fB7jJtJ+1BZgWjDy3c6cOQW4p9j5Lh5Afu7Y+a+667hmyWA2Sn\niP+47zCyRqMdpRsiMWS14hb1Imk3IweZrPGfEhxRo5kT4eoUZy3xq6l1wqG8\noznbHGnQ5OQOodeeTtlksL1FS3C8YqKVVwSV2p7X0axR08qLutUXhUQhKIR1\nf2qX\r\n=+i1F\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmtfBvwoQONvSZ7OnAAf5yzrggiD28wo7FjDqMSA23fwIhAN+Dt/OcK1jciYQOkHjsWsVVxfUL4CAKbct4btoxzTII"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.11.1-canary.891.908.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.12.0": {"name": "@jimp/plugin-invert", "version": "0.12.0", "description": "invert an image.", "dist": {"integrity": "sha512-fkOBCFg9P3Nkc0aFgWt5WgRP41KOs9m8OOnIi4jLnvCamv/Fv8GJLMeDS3gIXuzb/XkS0W/WpMQJmvI1+Zj2xg==", "shasum": "f1226318b7590c254e5887b3f70701388f620f6a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.12.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewIaGCRA9TVsSAnZWagAAJUUQAJYKdtUYoZggl5n4w+40\nxsefIjfq7bJfqPO8GVkDTkDJxjfuY9Ti6AkKAvDaOPUHw3C4kQTDqxzwMICH\n3MAPV7MIM+HwyhQXWLIz5LkjVZkf9Nom1/KIK6FDbLJJHGTZfMEW/kbZfl9s\n32a+LsgnPvVoYr9wKwFr28jQ0AeZdyYMBHNwYmKLqa9w5smAy02YCsL4xbag\n3Bt4RARWvMWlZNil+q8U2DB1pgGfQVoGwV4b4mkHWjhor0Fnhhk1ObapUMdT\nlY8m7F9aw8EtzHkmFWtU6w+FM+/0GR36fDBuxgl9hcXSEb6zSsiDiA2V/pZ3\noZ2M1Qi8ZOk+rkohtY9FEZLQ5eqU2+MU9+TYWxivDUidvw/q3xasxSP6tskd\n6J7wa/70XvOcOQmQ5hHnwBtvZQC19iYkGN/ce499qfJ934U2Gd2Gm/SthAJ1\nKZ/D/pvlwbtBz7sT1psIXPOLQ3xctlvkBAv9HgaOAG9i4sypGqkroVhUb45C\nufWA+LZ0kVmZVK4oOPZNiq5Tc1uj/CDA0DAQthaylaUQCHPMxf3GaDg/BFDC\nqeaysYbz61WFFrjX1s3CCwSvOmwRO5IhP2eUOiWKhTHVntB8tDu998FxjQ6j\nvZUA/ypoff9oLeUvSx0eCvtwRsqH3lQGweLEeXw+UI9Vr443A7TCOidVlCXj\nrIHT\r\n=tsNm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEf4HhuFJHsIXc9hWX7pxIM/Nr0W/OxlxMyrjrAi7g5gIhAMxec4h1HgD+WgKlqt2GNYvDu7VNxRQSjYttDRrpj3n3"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.12.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.12.1-canary.892.924.0": {"name": "@jimp/plugin-invert", "version": "0.12.1-canary.892.924.0", "description": "invert an image.", "dist": {"integrity": "sha512-YxbpAs/8XQ8W/TYYyJco6yO8BRUdB51Gcfn4Td3MX5Wf3QUzlYn9zBEwcMg7W8fM7WXCVl/XvZgy2mHSZlFNzw==", "shasum": "336a534f68cc2b92c94488410f239fb4045e237f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.12.1-canary.892.924.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJew3qNCRA9TVsSAnZWagAAkMAQAJIBlnEpko9reOO6n6TA\n8Z14KW3Eaf0sho1I3dc5I9o/VSyTHe6SefjbPSp3FO802s7BbGUZJvgEBOhD\nixqU9cETB4c31aMjEk34Re46edA1zgFGc5edyOYp8Q28QVgyd9w6uXextFr9\nqCE19TZY1cJK90eH16wFtXMSh49TGdcTRTQJPZm+aaHkA6v9CdUSxXjNgw3F\ngh1fj3InHisXA6YCYOt46vRK9szmzlUsVN21r6DuEJAXrWj3r8w0IQ4jz/n2\nmincgRSVeZjNF7TaWmT+2P891RK9jKfE+dWTbCQwNrQ6QBSthPDd2lp4s+ah\nZ8aycUofOM8iC5N9nhIJ+5iE0tM4augO7Z7NmR6Jx7nxBBIjLOlwnuivMCrH\nOHPKhNa73oS66xsbN8UzsGjcaGXX/9+cdtkr1+hB2pKzNcJe+UdAx5tjLbzu\nes3+XSV2uVX/Qu68Zo2gGP0GmEAaz/hQ9DT2gsLsmBDOPyvqVENiw4Yjk0/o\n6pK5HmJkR27LlBo73rinYL10uKXi1eGbiHEdhYEbLENqhnowX+4ZlTV+gpHC\nr1HWKoD/tFLYxjJX6gQ/bBpb1BC59O7oX90YFMhtl39les8qy1lfTZ92encw\n0/bX3vwKgtGxJ/oK4UrrLEyJ4mn58j7f5pqI8uSdUkr/L6NQ8LJZWLtdQjdM\npO/7\r\n=0EA7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH/E56Ey/wF6n5nucZ7zstfIgRT87OtUDQN9OM2eWPiQIhAOenNH0qWYim8GYmbVBOb7yGcvELzsgHW0TQzhKUR7Pa"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.12.1-canary.892.924.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.12.1": {"name": "@jimp/plugin-invert", "version": "0.12.1", "description": "invert an image.", "dist": {"integrity": "sha512-JTAs7A1Erbxwl+7ph7tgcb2PZ4WzB+3nb2WbfiWU8iCrKj17mMDSc5soaCCycn8wfwqvgB1vhRfGpseOLWxsuQ==", "shasum": "9403089d9f740d54be72270faa28f392bf3dff9c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.12.1.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJew3z/CRA9TVsSAnZWagAAS88QAJ8DUaAu2mAZjJdhwKPf\nunzG3kTDi8JnAr3ovlDbwFrVVEqqmG2p1wfRFYZF4KjedT4Unn4+DugxNEpK\nVGZVVwdAem4Ky3Gq/mVHRD31oQR/w6MOTjJ5o8G2KZ+eELifbQDl2yoX5h9c\nFfaT1766Rurq7axvKtg4NIXPpjv38fnGQ6v48gQOahXoKXdn4iH8HU8xtzlV\nmV4l4kNsiiOb0VXkRjCFUpD0Z5ysQBv2ZhsY/L6V2ReJ69jeu8X7DIg1Xj9A\nRCzyMGtNSFbqiQF/AF1QeKRkv+6pP6uG0lARue8lk2iZ9Dnft7xrdhUxJvKB\nvPhWW4PLj9vAe+gY5BlCXMQZUeYD2Csf+cXsbsx+ziekZkchHsuLlD6nCyS0\ngkPFOIbCAfcvV5a7zEhbZ5JaU7tvco7orJiVlR+sDazyv0w+Kc75KSKUi3Aj\nkWMtdnbtycYqMZBGi2jIDAFLXaBJtkXS6Ywt0G/qkHqf8Ler5iniqtvwSllb\nSoRmDq9sSnWwC5GFv22sAhS8iPOe39Dikp1TGN+cvi5Jp+if5l/eLSLG6Z2n\nWwnc9Us6Iq5N75Fwk5ch5dNjJIqJsIeu7zxJnrqWM5zuNcyjWuFKIBrPqnfB\nCuEve8sXC1uPtP1OpameGALXFW8lAA452/1DicxR1YdZ+Q4HK7CQqfXXgONO\nRl5T\r\n=UkOX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF/6z6cOIgQcu/2MmYYkMecI5XlpOxXmrYSBDOtrZs7hAiEAoeVCYYyXuuP1fRnkHaj55U0p5i4PGooTlbV/6dy7e/o="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.12.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.12.2-canary.899.952.0": {"name": "@jimp/plugin-invert", "version": "0.12.2-canary.899.952.0", "description": "invert an image.", "dist": {"integrity": "sha512-FPQVqoLwASDVyuwP0Q7v5dD4x9ol7x4J7cnBX68XXjrtMVp2TwwAeOD0qauOr6wwfFy5UERTFBOqOUiNHmagTQ==", "shasum": "f0fc4c7ab3e4862a091e021b5f354dd3db360165", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.12.2-canary.899.952.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2dToCRA9TVsSAnZWagAAHF4P/0NFh1myz5mUF4RBsHH8\n8trWDRJyi5cgqDdQC1yPHT08AQTVY0pn6JBU5XRAwYZKHsyLiKN5ahhZtiZL\nXFmmj6ttqly1Y6ZaYPLN9y+gFs4t+avH+I93zW7ZkiPkz9HL6W8P28SJBvnm\nsQvwkn4g6X+20fwWvN0hm9P5lJ9jOyBS4hisdPLpU/Stpma/aRi9qF4DopBD\nyezUw4Q4u4Fy2GLsSpuoUpxHi3yBTq+n+j04aKJS/CsDiZql0skJzQdw4msR\nh2aR6MA0h2crl5VEZM26QpukRd8ndCXoiZXxRUDAFaCWrFbz3JLBCn/y4/ZO\noQL7lShw+KaCuwzbpBiIYRv/G8T9RXcEqxya98AA2y4cb5alRfR5KbNAtR6h\nvkXLyaGb+Ny6FmSUBs2i9wcGuFl/HxRUCuA1e6WG7cS+BIXh9efs2PwnJvIw\nOFU0p9NWrmgppeHxsgV81I6CTNLuRGtvt0e11wUZ8bToM8ldWYcV+joOMbGs\nGcaVcRKBKb4TRU5W+nAFd0U4w3AH5PUKWUkir7XparW1LxZ3LPH8y1tBhuX5\n0a4yvNeoc7hadtnAjj1ycCeFBZzNmPY5HqAE71Atl12ul7YEvYf8hwdJMwn+\n6271yxleForhXR/nPtyRAedq/2p134jju+wFPLAfEWaUqOM0fy6Tzm0PFzDg\n01m3\r\n=UaOM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaclgkDPty1KmTa0biMAN4Y5ycH6wIhd8sQ62gUXX1yQIhAK+ObgmFtMmaZ2e49Us+HnncBEGherJ2aP6ZvHk1vnz9"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.12.2-canary.899.952.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.0": {"name": "@jimp/plugin-invert", "version": "0.13.0", "description": "invert an image.", "dist": {"integrity": "sha512-k7TWx/la0MrTcT1dMtncV6I9IuGToRm9Q0ekzfb3k8bHzWRYX4SUtt/WrZ/I+/znD/fGorLtFI057v7mcLJc5A==", "shasum": "f06ba0eed19225f45d1b87fcc84ed8a233e32d1e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.13.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2eBTCRA9TVsSAnZWagAApEUQAKT3Rb7mG0xE4GiMDyWS\n6oF076Gpl1gAxnISTSC3fNFO3xImtSW7Qzkmc0uVJpA9pU/cwEKK/TkH35KT\nWikN9RHH1oPmMB5L0plgsI4Q67VIqFg27Zj0Yu74GbkOMEtPHHJfLxRwccVs\nZPvY5ac3ltzz32WJJIQCSstC0eXm75DWtQ7zaiil850ZbwJX04RGRMV2gV4v\nn8NtsHGv6i0nQoWx0MWO4l+wYBng+FLsPUmGjevkSKHz/oqGyhaFINmhLAzO\nCgaPPPro2pX3bvgek5RXS07yVeD0KjEvDknjzCg6j7e755p0J8sJ4lh+8i4s\nYDxDKTx1YS3bXbmAZ1qp6VTxO8Ve92pA3Rs2jweQnlnrBy+6INUZPkY9O++h\nCrugfAI/Jj6CiwkFO9AXywa/NRIQbxEPC+Sbgcs3UywlJcAwMhbRkfbq1m24\nvC9oenim3n3kLz2F+9x9cewY9qdKgyl+aHs1fWoBgoaXUtOff4bOBcZGxsT3\nm+adAc56nCDy5nYrK16OS7+VWMfzlbv2NDSs0Ll+eRVLmtXHNR2HJ5El9KMX\neJRvoL9OUyc4Q51edr4gJsmCd+wgmDkIwnkIgTyf6+Omj80D01QfOLZo34QS\n+W6XKNYzqettmRL954sEPxdq6MD/5kEA9GDKYbyHhfigWpOhL3eA+5a2YJE8\no3B3\r\n=6Asa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0yUsUzoZUR68r0Yk7vuTvawZ/f67TWBaqc2NXac60cgIgcnWZL1YzxrsvJGfASQodMUjqJJTyr4yr/CucgR1qu8o="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.13.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.13.0-canary.899.964.0": {"name": "@jimp/plugin-invert", "version": "0.13.0-canary.899.964.0", "description": "invert an image.", "dist": {"integrity": "sha512-PI/r/xIohOai6xsAOV2sAFiFJ8ZK3iBmnZG7Rp/YG/QIZPWdXd+ilRj4rBrTv6Nbs3LEYL2b9ULM4yMoNSu64w==", "shasum": "c7d47dce7354109f1c98e09b20f131b06b1e551e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.13.0-canary.899.964.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2eBjCRA9TVsSAnZWagAAXj4P/iMq0N24IYlVQLqiI5mV\nRNpeouNcgVQLD2QJzw6f5FuGtFCmBOMIbgHxvCA4uy6K3mCEhyo3UJf10ORI\njtAsiKmMh4thG1lfoBKOGSIPGIOSf7MXTWi1cyrwikv4vfR67AYgREHkjus6\nfCuyv+V2ZBw3ra/3P6qulEKbg4DjseZp6vLdYgPIpSWutwedg6qisFy4Q11Y\nIIsjGALNkK/eKultpNtitrLr8XoPpKawKNKGxGNE+U0s+4V2k4WOCrbG1xwr\nYVIT/EOo/Ki7CnzA5FeHLg2WnQgMXzR/o65GLH4Wu7NLhK5AzuyasNRbr+31\nCUD5uwG93VcBtIilcpbQLEIHumS0K3vFbFfaaR6R/0zWNXRQXrbq486/BOw/\ncXHcuKzLR7wAosVtW7wuyqirB2xgk5HwStnGfTCK1VDmuklLZeRzoMZ3dDi0\nQykxAkfr7lztOaUjN3fwFCEpZFlXkWdt54vRy6y2lj21wsX+ZoXbSYBKiZ+X\nsiLt1FAzE4DmHRk/aiMqABWH7b1LOIpeP24pV/ptELqd8/6bVnVU1r+OkV+0\n8DRq8l/IFgOhWFh7TUChUR0cZuw7oJVpk+bYSaIElqAPKqYXUHB81VS/Cjey\nFppE2boZ3p1uhMCh0RzLQpjURrZfmfH8bkL1yEGjX3Txc0P0O+DTUhmNC/Ns\n8Uth\r\n=6xVD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDu/gpIUWMBYlpSZwtAOaDNcp6LH7gUKDMERxBidjBinAIgLnBh3kzqKZasypJU2jWuT/Jgu2nCmvY0YrvAt0i7c8k="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.13.0-canary.899.964.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.0-canary.904.976.0": {"name": "@jimp/plugin-invert", "version": "0.14.0-canary.904.976.0", "description": "invert an image.", "dist": {"integrity": "sha512-sYJlgaakawUYiAl16DIlwZL5Cc1O2VVW8MuvAAhrb3FuwmORbHjgrO0DJSNiOnprfclH7LTii9ACW/Lgpfl1PA==", "shasum": "769dcb16a9ab8c2d6a45763956fb4cce61d606f1", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.14.0-canary.904.976.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+hUKCRA9TVsSAnZWagAApXsP/RYhOHDU3pWBHN6RosbJ\n9fENEboKeSBWjRA49OwuBlqKk/sS7PYoK0mjzvQBlztnv016k2W9wsy7/orA\nM2c/lo0PvAQsyyQ+KVtIpaprot3B8m6aCI0yq45Fq1by7h4wlLV1jVZ9vhPo\nOQKmebi5y74/4+RBKuLfsZQoxmyuBeqVN5yVavOPMXbMk4tbP/14hFNqGV3M\nhmEyhdj1eQLjRVjjp5jY7hG/UnE2GnbmibAuRkdUMKXRvh3a+nkSqNA8imUy\n037FgJyFzakljIAD+RAPnUtJRIMjzub+7kneYKOaFdYTh92AlF7b+n2hDMNF\nI8k4D4sbJcNpYcihU6JROIBxSISpMpWR+8oHMdB+3a+LDX5vIJ+i/+Jf5Rxo\n/bq7wskhYeg0IANJhVvJMbDEf8nvSCY0VOy5UqDkpnTy517f0NYunQLKXX2R\nhiB5Gm93SLFBZ/bDpEzzmhsdJGCV6lllb5Mtk17JqCv8ZMC3YmE5n4yWvCh4\nW/bP3W16ZjhC1eCWvgy3VlZhoPcy3sQV/oAClx6Uae/z3LD80ZG7gOfuyEpt\nlbFU8++Zg8H0MsB2AL1h2ZATtEn4KxzjRzKfDT2LWUOWBlle04KMQVDXtE/4\nam4nh6NtS5ppihDqOhSxozw0yJgCHY6eLt6kMOAfbrft08HwWhnHJOkEkq+/\nXq2W\r\n=hwxi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBf2NdbfAEYKockMv2t+RaYlfOd1QfaywzitKrlfIN7BAiEAx7RrXAFnXbWDMaDL++SprEKQvkjsnfgl2hZPzwQ8CGI="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.14.0-canary.904.976.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.0": {"name": "@jimp/plugin-invert", "version": "0.14.0", "description": "invert an image.", "dist": {"integrity": "sha512-UaQW9X9vx8orQXYSjT5VcITkJPwDaHwrBbxxPoDG+F/Zgv4oV9fP+udDD6qmkgI9taU+44Fy+zm/J/gGcMWrdg==", "shasum": "cd31a555860e9f821394936d15af161c09c42921", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.14.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+i3RCRA9TVsSAnZWagAApOcP/1fgNd7jsm525x0oswIx\n7IEX+fynR10fB2tgleRLOq4onb3/6c4pmONSJDKiSUreCEkwoyl1J6INXc7O\nuJ/TQxGVBt0vu5pooYRNdz0RKyJP3sCoJrHpkPHmj5g1oEpRePY43ruriGXl\n1yvXl5+5EpAFvsVHJF0aqBx7KhpgYstLzQPTsR2HQgBlZdkLWCUkoy9mz8Ae\nQWVyGiB7Ej7nTTanSX8h4CHXLjeA1fy08w3pvHgI27mgBRbOSIMXSZyO1/je\nRE/8Kq15nOdpV++Or15iI4apQXN71h6vImGk/E8pDZZdB5qxpa9RujGVMX9o\na7BBd2Fp5Xedo9z5ZXTVlArlawOjqVGMfC/7zjkCe/DOgS86XerO+tPLaRyG\nPDzzThFznqcUtp9sY9XuFxm2e/UrvurM2OlvRZuMtM0d4Ktd8DYISSdXM7YO\nYgq4891WqOjR9d79C3tEaZ3Q+CJ5ub6yHE0YdGSc7sWyhL2l12bz02HwZmaZ\n66F2zl0Ikm9/0EWT8KT9qknWSdao+TN1f2Qa2MIhEyxiXIBXTLEBmBiXzY5q\nEzEecfBEm4K1WC37C/Lv+2LQ5bSzARgaBlECuRtWfRbItuqbpgoUXtHcqYbU\ni3O72v9JlVzMo59sFOCntLKgDpMVIwCiPLeQHZo0GMRawSiTdt0HrwVDuZyE\nl/AZ\r\n=vli3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHjyT4nBMN70lqqYBNmqAJ7ntA85ugE7QV1nwP6/zUOmAiEAw3lri5FTwK1fHXuwyCMRrHoIMgPIVm1qXVsT1BdUuPE="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.14.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.1-canary.911.993.0": {"name": "@jimp/plugin-invert", "version": "0.14.1-canary.911.993.0", "description": "invert an image.", "dist": {"integrity": "sha512-PmTVcuU3YPePx+qVxMKDLq8zpBNHWl7jvZAOvBB4att9mTpv9G+gbPiz0gtokGFdZJ0maU20JpdrPqbGbfqZ7Q==", "shasum": "6ca566aa810bd4714025d39489a7149b4d49c764", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.14.1-canary.911.993.0.tgz", "fileCount": 10, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEJ1GCRA9TVsSAnZWagAAPXQQAJx8yFiR5Qf5nj+dGSVf\nVntDWOv5lIW54AZtNqPH2JVSdiZ8H1RzJtL7mW6qqdEF/ZluHiG78ED4zIu6\nHfDgPUt9MXR6JZpRvN8szGS2YZee7noiJO86EMNqd7Lx3gfZou49zgqneG4a\nb4wdlxklX6XGRVkPO79aisTyOIl/7CDGrGL8zhKobiUFqBuGTh/m2RBXWDUK\nhiAqjTS5ijmxJkNegnwZGrsnTUipS2Cgs5Hd8YuosBxDyNIfp2lu3CwbXB1b\nmfaZHa7qYU6crl90wrKhdTnHcxoxyQLJXfdz+rFlApN04pnwCDaOlPTcCxgA\nEfplQlNJF1jSvZzwZzDw8BkrzPuu1LceZCg0usRbRvTP4mp9KOu+LA6Q6zhY\n3vQGAJJth8Tq0CqTmArV5sHFvkxwcVpNIq8qZ+FxW9vAnr+Zwu07ramMmuVm\nl4lv2RhsDcWsnhFzAN9cAsoBLPkO8RghrGI8vXWcaNZ8AGJ3rYZmRzIy8TOI\nx6OROeVNub9RqhVAQwKzawd6+HTSpVOksR7osM4TOYwwV7keCt2cb7WnKVzO\nWWHEIbU9oSYlt/npOIMY+0q0XbJdAETDrZd0tzCzApT1VV65+IF3ir/Uo4Jh\n6PygF7leFHpSZLf16eLrwSdUhE44uRsG6U4sW9Kw/d6XhNwQHR1+LY2h45nm\nd/Aj\r\n=phol\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7noMbKhyvGO7lAq/KEQBF3sp5WyfjIk6krxR5nEsY8AiEAq3WmWa4PLFqYQoGeO8mVKR4cosbqf4GUGF3A7B652c4="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.14.1-canary.911.993.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.14.1-canary.919.1005.0": {"name": "@jimp/plugin-invert", "version": "0.14.1-canary.919.1005.0", "description": "invert an image.", "dist": {"integrity": "sha512-JWX1KG16I9PxfYQ92a8+N+ghhPIbjoxR7mWzWXg9qEtIY5oaAkGnFZuIX+8WKcJ9w7KuHbhtqd0dXanOYCbDwg==", "shasum": "b8dd8ef4ffe528199b9fd68a4b2324dcf8d021cb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.14.1-canary.919.1005.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIrRICRA9TVsSAnZWagAAhgoP/Ru0GkoKOeT1tBaJ71tF\nIS29rA6gdiUO2/pxnDseMVq9e81CGLaT17KmwVDjd4v1mSbINVnTdEwyA5wX\nnnDSRkc6srQci5qE/D1N8QMqznSe1aq/nnA0SH9U1KLaAuSlvfeQ8ArK02gH\nxZhCbiKKD5BktCFBn7cxb8VElrOBSKgDgLPRPjD3y43IyKYYANF61UnntpO/\n++kK1oKqOsh3c23hyhhJgOk5xPfwiK6YJ9bVykH5Pe+wR2VvaxfQJfSAM55S\nn5ZTbcVkIDc6l69yq6WoWrjmxCHq6R/nvK7Ypkin7oeA72UNKbk8+3lPKX9M\nUGnwU3HW9XqbtQz0DL8BLw1dIud8CS5WW1ZijpVIYF+ZyX3X9iYYi4sjxHET\nJ7sh46fk1wV9hU8jMdPWTMs8a1aTxP6GT15hpInKqrzaONdthXAWVtvHOasj\nT5bGxB8e3Fy0HwxQRrWbQKHypiVuc1S/FRGVL89qoqBkEPCjjcQiiNcsdtve\n4t3EQlBKZkpzrvBsZ1EF8eadIH5DtJQ3sJ7gjGn+yY/1bkvtETT03AE+fJYu\nm5fdqeb0Gh+7F4sOLILvZM6UKT2gDrhCHtMWR6LMQXDpqvKSy3UUi2NFKZjM\n1CB3K2lDo8WVj77WOIms1+zcrG2Ds1mHITQIyXmYxIfEftAZFAhKLuKYbD+1\n48mP\r\n=bhZX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE9V7acLB0lLVGH3u86VT2F3cAJlI7LG1hmtGgCEUdkDAiEAyWnkyHHqGPwX9qrqvbrxylexdXTgc+zQVIUe3CoV5kA="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.14.1-canary.919.1005.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.15.0": {"name": "@jimp/plugin-invert", "version": "0.15.0", "description": "invert an image.", "dist": {"integrity": "sha512-pSaESvyo8pOSphqPFzdOWpSEqDx5n2Sln9PJiwEQk54KxfEawRV5worr8x2DkAwyVFSSI1BoSNa+KinL63XDGQ==", "shasum": "808ac34b74cf970195da9abdd89cddb0250156b0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.15.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLbirCRA9TVsSAnZWagAA4z0P/3JT8dSHEga7VRg+yJWm\n6MhqZ7uwfC0nm48QycH0Rcy1wGoSqA1GgDhX/JL/F55Z318WmuqVrupTO0wV\nDP30cul9C0L2LdFGyEPRFgk/EFdUP/h3Tlco+wQOJlJw+cwC2xWO7KOL+wAv\nj7L0teVHodNC+aZgsu1rVUjaqOaagtNLZqW9PzX5KEdzxXi9URvYLSzcG4Sd\niIcck8zJT9zZlmm/Kx5zZSezOtSDEaMqMluM43qZkA1ZogYAr0K00hGYSGkQ\nBZ8bbA7P54//q9M7lH3aG9F0mBOp1/zQVGvranWmbROGHJSREqAvqdaTQACr\n4b4sipNAxlndMAy/Fg8w2y/+9+bWMzz4h7qQmtNeXe2KsObqJueYCS6MRW6Y\nkkYryJjmpMF09vvKKogJ2bRAizh2NQ84nSB+D8FG7AcLGAIVE7x7f/utkaHE\nYnR2GORbUsaFKhOxFMPgEsdjGFv3rIHC8gYDXqHxEIj5dkJEQ0YHql7hnTZQ\n5UzDPnMS51H6clWD+zhs79HDtkLWUUKV5Gh0xdTt+oMhqTqhMbB43XQzDU55\nLCkj53FEyGxq5Okk+jUj1CvahL6KKv5txwmtPSV2rHOdFLxRHWsvNarwUGGb\nbE1KeOO1RTBcKEBUo839M7OKCS1vJmpGn4kIob9HDmiv60bEWQZDhjS6Db8n\nn12F\r\n=yWH8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNBtZI0Ik2phqFt1ke0CV+TgN1hu6tBElvDH4pl4xJlAIhAKvqH94FeDGH6WD9DIo+4mALDrZ57ryicZ9kmArJVpKo"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.15.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.15.1-canary.924.1021.0": {"name": "@jimp/plugin-invert", "version": "0.15.1-canary.924.1021.0", "description": "invert an image.", "dist": {"integrity": "sha512-fLDscFmzPKZRZ1r/vxUbpDof7mYrsWB/wzLkzry85zcJ2WLke9nidC7GPtyD1nfBJycD9thP35nO8hDeUFqk6Q==", "shasum": "36b11e4866baa83037c91f1d880d55c46b005919", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.15.1-canary.924.1021.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLdxvCRA9TVsSAnZWagAAhCMP/RixMD4awBrDoGGdNzvt\nIRUq605f0jLMJdJC9ve1HkAiJfVTBwYG4FMEGwYZGxGli9dpwWiXER987jML\n21fuxLkC2lOz5T+x8Tb4dFsMhTtaKaz59Nc4sMbbCZuEQNz/zKo8c/fy7eLO\nedM+5tK01IuqNptbYPZgQruxLoipDTuseD/xabOvJFUFMehkaZrJg0OwJcOv\nurXYC0D4to8SfErQsxo9llOWXlvhZzopq2DqI5/ABm5oqOYAFhBAt4IeTuFQ\nI63IyBPg/y6pflvD2KMhRXrGJ6JWoNX+1//8a5hI3TmDxNd1LbdqMWgHTgO0\n/7BLx1gBgJJqlTXYuZ8AUn6n4PLxv2xEt44oWp3+WaOyJrk7xmbAupvO4Wq4\njg18tlnuh2FNM7FHHX2ruQOxCDioke+evpiaih6S2Bb8xbE+Zz1g8NZKQ7oN\no1fsKkMOXUSnXNn8vBKWKv6acn4PuCFALTuevf5g1ewzjVYWTuJpVm25U3wf\nj3rnWpW5tenoxQML46Zx919QPAFwdVouwri9QNWNDn60GlO71P8Ux8T34r3L\ngJF+009ATi7twYqt3aI++z9qakHubWe3zdlcVAJRIxOesxf8S0liIguqbGPc\nAPKv8b9KY+NgeWKsI+2WVZ8zPF6ni0rrO+Jy8DOb0UncDbcvwAzaIk6avwMJ\ndjW9\r\n=4ph2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEfbLQnRkm/tyuP2PPYlbmjN5orUlR6sXFKNmtEwRygSAiBsViKhgVCo3pP35nNe0cPtwCCBwd/34mmwsQ/CpKQ06Q=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.15.1-canary.924.1021.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.0": {"name": "@jimp/plugin-invert", "version": "0.16.0", "description": "invert an image.", "dist": {"integrity": "sha512-RFsES55G7po6rv35A/3jz7IA1BOr8krX4H0zmtKUprcrhFl+El9iZojVCpJQRJ5QKQVS3iXi5H6EGN7AT1HykQ==", "shasum": "a8a342e463b22ae44205550785e1c4e1a0acc246", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.0.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLfFtCRA9TVsSAnZWagAAI4AP+wVUs2kSTqQUHcsZc8XQ\n5kHfcO5LKeXk+MyG7P96SxKD+6yhvQi2Hz8yAnfkuS1h/4x4k0HiXNQlJdbH\nybCMp0Mn+iTXYI6UxDKKtjLRNqMj5LrzR83Dqz7nKr2JWJQdcs3yBVVN8Jxo\ncjLrN7lZ0gajaT+hXOKRVoLpp86Jq01viJUjYTK0YY/mAxWAq+hQuerwNiQ0\nBARhypD0V++IWmjxzI5dTUou7tYoI6MCBT8ylBVKLMAmsmAO4SeG6E2DKDSt\nY+yAYKhLVw+AMZzQ25F7wWHCwRheDGghu09rheXnNPreYG3inkjBidPqeFk4\n4Oy8NzM0zhkv3oCcn+zZmK+UC60nLaLdURVOys9uxDe12U+xn5XULJTYhqgs\nhnLAE33U/Zz8kwGEvsmJn8ASh1yG4KDMMyaIERwm8mm+xiH4wRdNFzmUdlUh\nuYIFtOKaEey0vqn76Px5sRtqTATu6jtv2sU0vnNWtatSKAaJNWxfyyPKxzTK\niftpxYXLGWJPyGG/02xkUZBj0eCHkeKB1RmDuejbc2mSlwun/QQRofWhSGST\nWgPCxqKWjcweznZGoqe8Dpy8eyR8e9iW49Fufdli9z1rRwS0nR6kiRij3Hml\n1hmpXeYsiF/4i9XL+WCxylDeRkbGLc42i56jFK4KU626Aloe6xBO9zA3GrDE\nCGVE\r\n=3X83\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcxc8+LbG4si6BnMxqBt3lKt/L0vcUJOj5C/5LigtbbAiEA8i9CfyU7zhdF51swq8DGx7u0kDfRjlyFrrlbe3Ezqxw="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.1": {"name": "@jimp/plugin-invert", "version": "0.16.1", "description": "invert an image.", "dist": {"integrity": "sha512-2DKuyVXANH8WDpW9NG+PYFbehzJfweZszFYyxcaewaPLN0GxvxVLOGOPP1NuUTcHkOdMFbE0nHDuB7f+sYF/2w==", "shasum": "7e6f5a15707256f3778d06921675bbcf18545c97", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.1.tgz", "fileCount": 10, "unpackedSize": 9553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2MxrCRA9TVsSAnZWagAADiQP/2NnHsM9/reNBgYh39P7\nwW6jNyfED1NeLtMwyHa42Y6CCtN2YpxzwRgtT0407Z5z2E7l7FnjoXoALH0n\nwdS90F7NtPGsPFWa2idKD5ROdMrYB969rROzvAM6RcWoadrb9zWVAud5NKTX\naMOPkCy42afM7lVBiOzYDDdai4XIkBO/jKsJa8cbJTmVrF+qJOQTZRr5Z3Df\ndQ3HpiLcCZLEdc16aOMIiyAJCBSEpCUNwDMhF0wkoe5nNVuG24yxFFWaj6Lg\n/bemyffjSePsZIxiW0UvyejWAYl1g/ql7zNWog6hWH0SN46Do1wNwakjR2Ag\n+scGqqjfsHRIsPXb5/jxLHCVWRlXyjKhnmq6TzVveOa/e8vZ6f/VFwLS9i+k\nUaO8q64b6IF9Ox7ghSfRLTdy/Zuql/ZnFEzNOnL/KvvLSMMThhuh9Ja90+Uz\nx3kSI1s0gQfcotoNuU8CjwGcWwTzdTMBh//X/rqrw5MR0/BcvpqsgqW2MFF0\nj1Lrz2hpeFj4JRstB5fToqmpF/KSLJ0LaggdxmEmN/qKsHWf34zdqCUZwVir\nbOepmtEZoIIF18QvVLbTGs9w1vSoGE04w8WPF4SOMk4LrgtZvwMES3Og8CQz\nkfkGmeLysvwcJv7V/BTf7DvAWd4h6yxp81D4HdyQxcUByXpA4PtDroWe8tBM\nefcn\r\n=NIPH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDhXA+1ecJ2X0VWUC188r+ReCL75EY/AjIPtgeDKK14yAiAn7+6Kha3r4+UxieD/7nw9b9DlA1h6Q7Ucp6JwF0jl0Q=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.919.1052.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.919.1052.0", "description": "invert an image.", "dist": {"integrity": "sha512-tK8Z2BqRTvTfSOGMKtOdhRr+SaXhBWtBH/9CUxKGcC+Mt0uhicLiSgpsR62DvrU8DfQOy12vhqouMused2IpcA==", "shasum": "bffd8c4fc569a80de3bc617621eef2c4e4867950", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.919.1052.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfST2FCRA9TVsSAnZWagAAvJoP/01GseJ9lclidhiqkN9x\nW+otZu9ojpYjY+fVbadd4uDOnDqCYDl255k7vONsxVLnvk7Bt1arqDJvpvIY\nI1qUv2cEYNYH6lhqigoBeid41lYXvTJEhmUtaIjtKiEDglA33DToo74meQWW\nLPZDnyljpPzx+UG/G9STU8IzxusbDZ3uFVw0YPGAEYjik3a1UV25crkh1NPI\ng275ZRUiTcBU0RLBav/7IaM1ZEEBsl32pMVgRzJO2W7snEQUXl5blglw7TKD\nLhrlcepjf8cdXpUX9qTx5omg6yRy6sze0SspDSwdJNBOi2JhCOIzLXjciPUu\nMb2bGr3KgS3FPVuREN3iAhk8YT/I/L9CwTY1E79bxq5Q0OxbRZNl/hcbsGOq\nTbq7BICpcxRvaL5JGh+vrgEoaJzS48Hv5AEbAkE5A7AebyEHp/jm77JKYjhP\nNRJ5zeMUsB7inyqWmyggfuxsIE4LR35E7+jBWGPCTd+NItQEy4nUdP7dt05V\ni/bRUTwGKOyXoIJPaHz9Exmkm+wNYG/PLeEOMaP9RzK9N0qsWjnoUp3Rz4Ez\nb2zFM4nCp2Oj2mDTpB/FBSmadnxlG3b8Ak7tt/S4FWb/PJhe0u/Pfq9YP70S\nh4spPcMVHPkawLNBTuP8MD8k/XRYoHidlN1mvApmKqXzUzBzmljkJWgqpbOw\nEjLE\r\n=G63r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHkWA4Xwnk3c9fV8NHDAaaEOBHA6ihYRhc6U//xZ6bzAiAwWSADDP+wZnEiP+fbkGA/jx3UpfX1NhQeIjweoIjhRw=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.919.1052.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.934.1053.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.934.1053.0", "description": "invert an image.", "dist": {"integrity": "sha512-zzTS/1I8EMWpZslnploumuJgapMfOXZroj0AdTS81iebJCQLtyn/uBcJ/Wtd7vf3l+SbIeqs8bl+RG/tqFcYUg==", "shasum": "7ac6555c580b66119cca9d2fa8c975e53207bc8e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.934.1053.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfST2+CRA9TVsSAnZWagAAlh4P/A2lQC/PCTmWWwfilMX7\nn/EP6KeYiVXI89/Choebm+1bjLhWVKNr2pF4lGjhB1eaM0LkCFIhtMxVxGpb\nbfPYPjT6+QNoSLhUMISTx8NbVUcYiYYiU7x8IYnpmdzfyWuvWD+sdVaqkduF\nY5PCBS6k7il8QDK7OnRrh+SUrcUuuPcibV5ZxQ8DTBq0D6nj7WCIAAnqH4WG\n1emsZvUyoMtJP30cErmj68NvGAxWkULQO4Ninb+ELi7O1HCIElGLvlkwP1dM\nhCfZcked0nKvBQzpL9Gv9MZc5lwbGqpSQHBDCXn6p8gVScvrwC4HoiAXn78l\nSs7EkezAklzjinXER0Q29e8fkzMCa7zb33WfBdqoPup1XlHt5tot9ATAuWaQ\nQQFvI1mDU2nhiRW2TaGA80r3cCDo7yPA4cRs9Oy/ekOVUFq3K+WPWfWggcYP\nnU3cCQnKBOkE/OpDaTfybnvXI/czJNXVc5iCMOX5iOjiiuz3sVA0yObqvONR\n0RswjnxkqEykJXVMOVc9/CzArs7Zd5tGiVlgmIgMYqgsgVMfaQXbrcWy4SnU\n6x43K6cfLM898yhYYnecUOLRp299pao38td7jmecCx0wo7eBSlLTIjnhko8m\nnzrEPwGYmo/WOutdHwAiIYw8UtMc5yf7+BasfhDZOxWTcnqzNo035VKaNuky\nuBgs\r\n=cgbo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtwHU6SmIlWVIOC7AvXUQlzaMZ2dsTbVKR8ueD3++FkwIhAMDKrUBkXTT9nw0AzzyANdC+mr5C0V4CvtWkJMtTjakW"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.934.1053.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.938.1059.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.938.1059.0", "description": "invert an image.", "dist": {"integrity": "sha512-eRNT7u73n1PoDsb8MAJB8A/O2NQavN/h4CqLerkKIqi3ny5Fv1Hp5uB5NhX3zmD4uEmJU49h7DdeX2+TxcCPPA==", "shasum": "5fdada3fd3355f757b4d26fe8b783df7108dde80", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.938.1059.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVi5ZCRA9TVsSAnZWagAARTIP/jXKu/AgFE0dt8tLq+4C\nmklgjdgILOair8c6JWTVHmdBVjO4P2WzqmaSI6cgQuxlez+8JIGQNWGAXW+q\nFDtLX6WO56ntlJI1Fd4oqeBNHdY311uoM5uvy6M7dm4gFP/ouxb+3kqOZj87\nbnYJo3p4OQ5q2EQUkndVmDuZncghsc1ipWodTJDkDquPHsYgNSreCzlcW6tD\n9fXDDbjfOE1TL15Hqo4h79Sl5D4MeW3AhWdf6lhbizU1EVd8a8REyn/bZ0Ld\nVIXpBbkPyLzEzRGapwtpt7ygxrvEJoEvVE3wAgkQ2jn6b86Yd8JUCKj7LTSu\n3tbbuUYrtGZagKlEeUkq6gPB/WKQVsKBAqg424u4Az4qjINAbpXjqXICtppZ\nP1iQapdLCR3Vr0zOrtmyvSeRvwqnU0rU/16ebkv5p0fFq9zu9DRKy14dhqCF\nSG8eN41lZU43Qls9T1L8QuoqE8b8PqM2R7A/jABPGCmvQuud+OahhMvGo2YB\nq5GYUT97BhAb6h7d4BT13qHLcRtBfVlQtCdMKm4Jfai+NjasV6ZzwmkgutXJ\nF5zE2naldRrN3Hw23gtmK5F5QvTWLi2E0Ggxvdf22X+oeCvKS05eQYQDy6tY\no67J64lUjvA6pl8Ghqc84NRp9kqD7ULjGTwu1UmADXn68qe9MRKV7iQw2EjW\np6Wj\r\n=rWZL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAdod6EjXgqnix/eVlveuzZsIIS3R6201tKlsg2L4Hu6AiB+d2yJ6OJW4yQcgqKU1fQMOvPvXufowZYRnbrFttPx3g=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.938.1059.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.947.1077.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.947.1077.0", "description": "invert an image.", "dist": {"integrity": "sha512-kqREj6TfhpG9qBtupicV56xTgNPY7+IzF50f8dKrhPKm2GHDAx5YQMcnWBUKejqrqq0R1RfO3kiwOCheKQlNGA==", "shasum": "f20c1c290b4e533d68b78f03fbaddef277374d65", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.947.1077.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeAuzCRA9TVsSAnZWagAAZfEP/1ExVnRUx10Y6wQ8IUJa\nLpRJuYc07yd/a3GdnXz5Pp8Ehr57O42QCIoqNWv7CaBHAqTiWd1phY//1bLu\nwUR9hpKhPf57UWHS8OXDwHUxgwse/aeHNUTK5TiU1Qpo9IQnkzbqfazp1vvR\nP1EE4CRcm8xEEEJjw7Q5MktmvlYjJJ4CnKavYyMQCDkEU/xmcb/2kf892O2h\nKs0FKGDBmg835fMoeQ7YxnxK7cmIWhnJkfv/k60wQRI00p7Q6QOXOp3n0Lu0\nESuvS42oTq9fErk3NodzySKam/s97XZuTpuO2RaTu45D4lcXhJ8wWZ/DGC/7\njBolz+2NSIFYpMgCKQAHdiSuKi0DGgoo6p3SmLHRSaF8zqqBQlC0vhV9O/Jw\nCZUpR5IksdTKCiJHT8XpSsWcBg5FCrwXtz5dD8NnM1xAXaPGPdJZ3zgrAoZS\nIAPt0cbYLnSM2cHz/dA0nr3yMcnATW7ezaJdt+s1ml0BZupCl4BxA4QRXvB9\nZw4Jm1S6x5P+9sCHSisWhjr+Hs7wKMCsz55V4moHxWMCL88OhYTXJt5qp0Hh\nIRAULhoSmWgw6M40xXxeag9Hw9gMC9n4+6MsIbT+dBD1LFEOt5rKaKNOWTeE\n2cFE6iYHrrjKC1Y9nPZ6UGJI5QFKa6aUfo/aKHLhPoYXLBE6sesJ4mxMCTV6\nOS9v\r\n=o/A3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcFy3r+D6/QLo7uPNsKQxYQjGBVYJa4g7svTUekf1SSAiAmt8QFNIUFApMxG+3NW2vr2w/RWQ5BLQqJvCrUtbCl1g=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.947.1077.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.956.1095.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.956.1095.0", "description": "invert an image.", "dist": {"integrity": "sha512-v5AYmOCuew2Bu5cIHK/9njkdlu5C54qPaqNjPo75mjXZDpIqzIyyoBkEDwrCg799dTRMhcAPxQFhwnPdi5H8ew==", "shasum": "bdb23261f2c9f82afa4e4f3edaa2e2b79be4d32f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.956.1095.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkz/aCRA9TVsSAnZWagAAzt0P/2q0MS5kzm3o4rqa2V8J\nTiIEXyCUsu6AVjPuCBQB5fbjbfOQurIFfmNYCoj2tJ5sTTt90OKC6zwZeVW+\nrS6ilqyZpKuC4EHa08ARZvD6oPGXlT19xHqywX8V3FSxpYVJjO2XjYfq1dDV\n9A3I2quq6M9hSR5uSK8pw7N8DFsi67j0tqp1UKROp9nl1nDpjg0NkzcxFQlv\nqeqzwwiLWEzG5adiI4+TFdRBokhJK9EYyPC+2Bh4nY5Kve8VXpMhDNQFjVFZ\nFNOVY5NbeP+keXWwQ188YlxG7HSzX3f9uLqSzH6BH+zqamhVzwaWCTWtxppo\nhqEbg6T14ZNA5od159QoT93MikoBAq0LXQSUAhis+vhoONwX9Oc+OK0jRpdA\nGNosm8Snq3Xv6Wg9aXn5T4gXepdYMEGTuGi/zeIX7gE3MdUSZ5uU0D/ROIxf\n2QLfLHgxcjkX2j6smbfTl0ULnPokbTNZK72WcOHHsslzVBKFObEHEKMOSYD/\nGf8+hykKDnVM2nFNrJClhG3y0c5ET93rXm0L+FDV+2Lp4OGrOIXV+7jdm+NI\nTeXYGHdkQaUKnvrv7HtkIWgtVWxJpBXPrmctMGPvVDQcx6FaMerV7NkysMzo\nYfIo/H8b7rOOKix0GjCqv6Zf74uj16MsFoJtRqSJLN0DbhD7/xsZxogI0uc2\ncW/F\r\n=t278\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQTmtIcjdngTgb/EO2vQ1e3huwD8JAG67y2st5v/vqhAIgRbzb3Xd13/9SVzFzeoyiuIc5TrBBJBHEKNBzCwboIxM="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.956.1095.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.964.1101.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.964.1101.0", "description": "invert an image.", "dist": {"integrity": "sha512-LVRp5mfRPqm0r93mO0izNXrIZjHTDEP3L4eOaf0c6JO0AwVI69jM7Q5hkHRY2/Mjb4IdeHfWFMgM8M+w6DDJAQ==", "shasum": "1c92e4a216235fd7dcaa5738611aea2738082f91", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.964.1101.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvZyICRA9TVsSAnZWagAAUEEP/i/gxIek9ZKtpGCEK+7d\nFMT7yUOGfler0IzhhDnzkp+H8kgFLdlKlht4zHPXHWUeRzEW5qMw6QblxFaM\nEcre0iK2dje2zpHIZutMacffhavXVpZSTXGQM3q0rVHngs2znzQ4vxH725Zz\nI0tXP4EYBNqPJKYS9g1BIy9rom4C8d3pG/cPNRcKsQK39eARXpKUQySD/s/P\nGQReocCNYnBo7iUryMDs9S7373C4tqseDlPz5MpL5cRdf2e+5IXvs533mW6J\n93MPlfjuPma+lmZCjyXTuEY4SZEE5ZVJCvRM1Qn5nNa98zW40P3pTKru2dzW\nekrKDUdBUhV4zSeQy7egKqt2uNCaF8rmd7wsRLNU/b23SqOVj66GRe6/cIj6\ne04IACe7KiC9OG1bzuIqmPrbvLN+5fnth5iiGWh4qx+vKmuvUyw9lrHc8dY6\nm1kdcnSEam2TPmQ08hDtC3fHdVbID5qNa0u4Wz3Xfhn4VKSzlgb/p1eJxSnP\noFsx4ZpB8LLB9+7wx9UGX5zXZjMEm2ryXvUqHwyB80jyYvbqGORiwf3T4SI2\nmXMrCunzJKEzJNiEbW6f8e2Jpwz92oQ9l0z4SWCYiPFibLiOQNMDWOr9AgQm\nKrASAl7jCeZet2qVBjgE08CSwDgBRoaAWecgOPrd7yZZr6nPByCM1IngEmTn\nCO6w\r\n=V1RD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHBGKenhkAqGoXJzz+YQR3B06RrXB2a6/SITzClTUYxFAiAiMdHdfgOrRMDt334uoA34IcmVyYDZlNamF8LwiXlEOg=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.964.1101.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.969.1115.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.969.1115.0", "description": "invert an image.", "dist": {"integrity": "sha512-Wl6qn934H5LBnHh7ylWW00jgqrO9iEBk58XPwl2CAlEZhXqKf7CLfIktoiM3gmz9mgD5OK5wwm6N9putD9RPsA==", "shasum": "747554bfc71fc877cbebf50257ded757b97090b4", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.969.1115.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0tnCCRA9TVsSAnZWagAAVDIP/2dz55IkuhmQPrIHFfyB\n2j3oQgoMx+E6hLQ65D8H3s9bXuRhl8wCzHWrYpiI+mctDFOw6IO6jmfyXN40\nPqpUN/T+55eYn58FRO/QH71gIZqx6pIO1Lo4gNFjk+LV320TlcsE9iXhNj2T\n7IhvehWoMT1RtGIJlymFLnUGJv+KcdR1iW+lE0FfJd9+7hAwJ5UdF0PqMRmh\ne/Dy1V2ZskqJwedbNvdPwWRlFobHZddV3sPg1p7r9uTDCINLjuTy345ROLky\nvzcUD9ezFTorkwQ8uxWnDQyLTnUY0swyUbYO+I6RDFcL3RRQy+N4Rd7lj7qv\nqleTnG+M3oGPW+7Bj+/3k/eypWpgvUrX4oou8VN+WxOdKUucGqbJ0kkAlK0s\nXWW+ZTU/yI3A/QLTQ/gYWytGTN1iKPObJxXcWNHq/IFkDolpdthVizdkEqCC\ng3OtEamffhEE0gKGL6xK6hsjzIHWA0aJh3G3PGwgr+wxtgmbn9Ukmor/dT9L\niRHfD5DsvmjFEXF9xVbIjLwDn+2chbr8H2Ezkz0P8AqI/xPiQLC9Mn1KYNvG\nUydpRlTzmG9kWuZ3cGIRKL3y41OaMTo4huy6wJFtzri6Q3cirzmZ7Zq/3GB5\n+S/HStf0N8a3faln5z6PznrBgXGtj4vi2M/aL6jLBxkuyoeUkdxBiQ+SFnWb\n5DMU\r\n=wftp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDex+s1/TSdNVrFHn6a+cyUTfspgBdMtD+0xwIgZJpM/wIhAMUCZbz+gYBaTmeG0UgtW90KRv+tT9ZGZ7kf0FyR+xlp"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.969.1115.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.984.1126.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.984.1126.0", "description": "invert an image.", "dist": {"integrity": "sha512-D1pZDXv4o/nZjRzMdUfTv2VIXpYMCnGt+iC/SCU9BRPjo1j3s34Z/LY99igPXUTqLR1Q6hNaYX+YBVduiBY1gQ==", "shasum": "88c2960d6684c43d50099843be9697b30ca465b3", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.984.1126.0.tgz", "fileCount": 10, "unpackedSize": 9588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDwl/CRA9TVsSAnZWagAAFg0P/RO1Oj++RzxTpJHPsPTH\n7VBe1smfq2KCB4RxulNcIrqdvgQEsyILRdX3rRN/a1lwtNcvZOniPm5YlIYX\nFlT60zFwzrCBV7deOyp5fiIQQ+fRWhuTjEX+NsZwOVDDm9kgK0W7kk0aoDNO\nCreynWcvvaEI66KlvkTgXwioPTvELMC9JIyAnUQ+MGsMN4N9Vxbm9unEz2qP\nJIWHc7lVyBXk3qBbFO1RGJ+gTgHTyr51FDKTR5mpl25kqeCq0QnVyjLAU8mv\ndey6UMJjW0iulnm/cz3QknSDGrFaQ3T9Rlq3EKjbXIyWXwIrTZnJon/rh3O7\nXQLb8JcYRCNLpE0PNlCWPrLstb0ggc7KSwKgv+HNKiJXtxctbHlPlskUbYYr\nIEzBNfDWL7zT+XRxf0Sz8hH7KH/61HylJ7k2DUgshDljow1vqqM6/pcTJhcg\no/NhRsgzENET/PQ2w4FVwJOIhP1IjlQSd4X0ax/yLA6yVwk1gAq0zlF/w58K\nHNt6NVDVMWHlPUVN8eeurCXl5ba756Vcr+oNSjB1No2mFjNXnTM0BMwhKz3m\nNelAfsMmXP/5nU5hBU2vuJY2y1FwCrdU7Esz0GC2BtvBLJ596djht4fuSyTD\n4CB7FouWDAbQYTcXpjWc3HT0+UrNND8ik2Ew4+RZWPunWkfWFC66YfneSKiI\nl9Yw\r\n=Xg5J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZgWSyDKjsJlaSu+zlasXyk1Ak9G8TSXi5mh3y85fUyAiEAkjy0OL1hychE1vbWHl7ryizXMUPeMfK1i9b8L6yanu4="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.984.1126.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1008.1164.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1008.1164.0", "description": "invert an image.", "dist": {"integrity": "sha512-ftwWqq99KB7F0XiBF48jNfnhybAVAGr1NVGbS9XR8kQhe2PzJFrzRysl3R7tU2IoIpDqRvTc5V+nUqeYHjcCHw==", "shasum": "fd49dc0d5dc4048c9b377a189761ed35eca3a395", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1008.1164.0.tgz", "fileCount": 10, "unpackedSize": 9590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgioj3CRA9TVsSAnZWagAAe0oP+wYQIf7OZlCOkq4Uwz2y\nca8NqOdHrzeHm9+sVjtRM4hrsUux4sUBpLv0aiqsHOm0pyZ8nI9Qo/BZ0CmI\ncDgmyfU7Rbz6MO+g062ImUKEDKCDC2OxsbZfUqA5ESY3DuvTVs3hu90dDYc4\nyMJnh26dNb+VWk/9QFjy9ebXjwuh0impeHX4wq+v4CDg5eXGF1cCt+Pmd8rd\nmNtwZbCNsnBCpnl3sWoDwaNrkTbzStZr+Pz9oqTzlnMcZGylZ186LJQH0tWw\n6zTyiq2lOq6sJUgQdR4VhEHsHCdbhGmikpy5evCO09/cMXbqBlZrJukQ4yeG\n3PYJljc8L6WOYirsMW+gLwr1mYd6GTsUI6S2uIAh51+fQFjg9wxXhbkEERr6\nljiW8PEZpu8dgR3HC886ecLKK3ZVIeAX8U5G/UrV2mkXG+NBlC2/58soJxP4\nKYxuNGUFehbRWy7wdP02ykC+3B6UgWmE32yQ6rLZvzd16eJSlHO7B6zT/WNC\niTCkKz/rIhnjEx27lOQ18ZbFynuEB8favHI3sqiHpvXLGxbG42Q67PN4aJLq\niIw8y3ANI82h/6tXm7wV0Bs25nRIVEgMoiwdvo6tVp+I8uqhwPaF9Yho1YmM\n2KkSX4l8saKpfphJ9Pd0H/3dTaqq8wyV+hHGfdSrI6+zbhzmdXB06X7b6cbZ\nyEkp\r\n=M9gx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDE7ijABw8cPLZ2ijWtG2K2JWKwcL8/3HrS/kznCEoJJAIhALQYPTRu8ApzNpKwsXR08zckJF7ChNd8LWr+R2eIc1vX"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1008.1164.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1016.1185.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1016.1185.0", "description": "invert an image.", "dist": {"integrity": "sha512-kjgucYS45bBzmqaEzGC1Pnp4Bkepg82yW6GKevMgh0LHPMUpTr2gAaflNHeBp8Pe6q6lSFHeeuKFaD6gyY4zxA==", "shasum": "616e729291c63d5787459ff5e0fa081cc311ad88", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1016.1185.0.tgz", "fileCount": 10, "unpackedSize": 9590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrEkWCRA9TVsSAnZWagAAunoP/2EI13/TbUS2W9pvlBFi\nFuGKAc1aDx6DnUovpxYY4PnmKoTkwN+h0kWihCU4uWoaJF5hs31bKoGOVwA1\nCwzFPuRAJLisOdj/jrwNA0h1aL2Cg5uHIIBK64lSIVLUBsl7EGQnA0M/jTma\nMf9Oqy3kabO7iXoZ/teebuuDbXzwPiOikj8a5TqI9xdBGVt135I76WQOcmkR\n2MPDN1BYLjoE7O7bJPN5GIt/1m1mfEUei853qakDjkjKt5WIa6oC0Dp6IB1c\n9ByuneNV0w3a9IFx3B5ElIPg3aUfPL9zyqG6nKRMkTbvImFb2EwVQvrZI8NQ\nTvhFeRg6M7715k70bJlRcdjWc3FGZHUz7zhDWJcbA8MxescCyKrbEtoWDtXW\nsV03wo4ECXj/Mr4PCbat9LWO1vgRUYPaQIM1en8I04Ui6XwM3TCAzWYCfavz\nT85P0vIM686BJhc8FNb9qw4KwwhZDh/BDILZZICKnoILVjft0sqfet+/ZQj1\n3OYxQYbgZ1Cf9aIGMsoZVfLlHDcrDvPcbPJ3eUptSPTb4awF1INA4rxUPfMy\nzeGJaZYJN5Sos0rPobEZ10UWQrlDEl0mgN2ijNyADxsOAAWuCCe/MfNaWL8Y\nGb6Yei29J0YFE2atNXIUJKgMp2ee3pfBoc7Dnt68P3Y/ZZShyEBgXB1TmYQd\n/y5L\r\n=zksO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFu17MtK0D6qXch0JDVJ8zwPFTh7o6WPVL8h4lFyT5hMAiBkcvqvYBCpe3AdiN8iyFepOIgvkCBbt1boNmC5Aq5+yQ=="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1016.1185.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1045.1221.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1045.1221.0", "description": "invert an image.", "dist": {"integrity": "sha512-s/EPWg+ueao1/pwR+HVIZm8yfqJWjjUJnXOdcrM9PAMxl+vbed6SYysu1hAWfr9tw1lx4rwATGcBVcekx1PNPw==", "shasum": "ab63b5a0beec471e8f66bcf6ede6fd9416c2197e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1045.1221.0.tgz", "fileCount": 10, "unpackedSize": 9590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLtI/CRA9TVsSAnZWagAALM0P/1z8jcFCAd74lq1utC8f\ny/nwxfyuBf6emlZ3lfMmETimjb60jyK7DqrdKrSX/SKxRqHLeCus/1QJuMEd\nkuZh+0xfqM6n48pzz6uqk5i3tGc4//HTwKf5pOb5ImiE/3aimnSxhXRVkKp7\nJkXVzsQV5wAvnE38dILrMmBjza+xMjL3RLFbfD8ovR7l/tJDRuCIhoxq3rE1\ns8OfCQiJcfTgsOea8snntV/PwrlF4p1Wnie4h69+yC1eYVf/v1cQhr/SQHT2\nrrrx3BIK253hHDPLUqCIj9hMueklPoDcn3H3Qf2vd2VgijEih/+Ug0JFbDPu\nVMbJLX0ybr7vaxAaTGhNv7U9+X20u1EVd0wmDEjm/GRTnAwziIYHBai23E/6\nCvAUQZXrgYA70YPxCyhyHv1hjCTP3h6/RjYPA97NMJgXhWivI9ztaaobf9nk\n4zFcBODEY4dTcPd373Xm/TjxP++dCLPd5ftJeCG2qUS4DOHm0HXWdn1qFXPU\ncOznsurevbqBMcWx07sMzzW09fAZkqmR/u6pIXJ4utvx4c4wISJvwaY+jeBo\nihRMI8QearmTRFV+eewSp8xeZjaUN4N9BWU/rI5p8ZpT3A+e8UECDamXThkY\nqIn/cn4Lxc+ipEVcZhBqfVBShyfg58ANc8vktOeXVltrBNSlhn21gtzeyCMB\nfQrP\r\n=iYRh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIALQ1cwEnuk1rtCuhGEcgPUPnb91mQwlPU/9flcJNwvJAiEAuCZc6u7ySfDFfvZ6KEwd1BH8kF79X3RYJbBLbvIbbOk="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1045.1221.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1051.1228.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1051.1228.0", "description": "invert an image.", "dist": {"integrity": "sha512-dPMkxGy9YObeYfDOC/rA/IS+4VL2pSBuWKJ+u7bI2jWG5gDVAQhOEnPgkuqwcGQUIhzlMRYaGhzTDGW/Woec9Q==", "shasum": "ff27a131f34a066d485786e1ca5a29df94b3f600", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1051.1228.0.tgz", "fileCount": 10, "unpackedSize": 9729, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBlDcejEsw//kDr5XRXjRK2bO8mnKzrNBkplcp+QHJ5jAiEA8DeM73H0BoosSDRqQEMsXXQ3t7x2QW8L7N/OrJS24mg="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1051.1228.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1052.1235.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1052.1235.0", "description": "invert an image.", "dist": {"integrity": "sha512-GbEJrdhah0mV+v87Wuf+0pM5hCtVM61/q3rBZFM5TVGv3jMX8prs8WXQuzUVuJzHB91SIyFGhfV4YO6l7rTXbA==", "shasum": "5a4e500ac57d18ae4221cb765b6741714db5c1df", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1052.1235.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYTjJ81gIrJxRUT+WuQyihRnR+t/SNeMnVvYUXvzEtGAiEA582oivfWrd05ZMQ03PloStHeBHHyT3+RGB1RWJb72iQ="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1052.1235.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1070.1265.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1070.1265.0", "description": "invert an image.", "dist": {"integrity": "sha512-FtDQYmsXJM/Nt/semcR1IEJkoSzoj+fjKuR1lLlp79x+h1aR8l3qrUex0oZIJC0RWTaUUTvJUJcMDGJqzknn3A==", "shasum": "2783066e488f82d9a2fb22394c00b30b9d315bc6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1070.1265.0.tgz", "fileCount": 10, "unpackedSize": 9590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBpGiCRA9TVsSAnZWagAARbYP/jUCxJinTwwaBlmyhGlH\nTFjYiskbjLT3PhI0RaCemJ5WoIdKWK3t7Qh8yfa6rzb1OPp9tjPLa3z6NYO1\nBy8FC+L10vG4whaF1up/C8ety+yBCidaI/HGlD4lZslfYIlS4b8dIQmzeRXy\nFzM0ncGt1935DjPNYFT9V7SmZRnSSBQbmzruWLypklHSiRqM0ocJPWL7OpvW\nI5J/GI7d8aIAn+ahcj3JmGS2MDwCf9/dzrSVNjDIQJuSDdcB0AbYcGyAZtl7\nvljsDsHCVAHQSbpcClYt5mUzENL4+4UTQ4K2xtPwc4kYptoxLmZoUS18gGcK\nqxnSwtle7/E+nPo3M2j4XCfgr34VDd/yBcxmevNt7CcEW31BW6YKQJfQdPEr\nt2zVJfUn/bxcv2f2FYDDWNIvPEfwjoa2kbR9ukas6nw4sGBh+B0u0EBJ+veg\n2Xx8cUFDNDrP2rImQLe+lSSq1aiV8yNVTX/wBmVDLOd6jy9pW2gxUxVS+/Tw\n+yjYZ4dOALTdmBN56iGKJXGVwtU0XRP9j1I2K8XQKtUIJ7X7flpyDvxULt9k\njzdfyNLSJLfWXInBtxS1SGYAkomGl4yZsx+ggvpeSY3lnf8UCbFWR6YSyr76\nSplEIzNsxyfFzUAonF+oMxKCyZDxxK3O9BtlLVhwylWYUbHmFcAGVecjDdpn\nZWpa\r\n=Dtv0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2N3r3PG6VVMah7IbaNH2Jx/IGyyDA2wL0A9rpiyoRtQIgN4ABhfGaVn1RB76ftaG45IRZhhDBjgsD/82CZjPQ5ac="}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1070.1265.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1073.1276.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1073.1276.0", "description": "invert an image.", "dist": {"integrity": "sha512-o6i2t8pu5nL4nLW81IbWdufSNlKw3cKHlP1bm3NSRNzuL9RUsQAl3G/RoE0hBVvjL4FCFoHFVf7se1Biq67RDQ==", "shasum": "c064f277cc9819909d89b130762a0940c9e42900", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1073.1276.0.tgz", "fileCount": 10, "unpackedSize": 9590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE3OVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoN+g/7BYxEQLgBDMlL56aMFFgYEUGFN/CRE88zXWdEu5A86wrfDM/b\r\njpkLljOr/Q99XQGXBmdfQVX4DG2u2rdc0bx1FeAVdfA7JKoqpywQNW4CQVys\r\ni4b/Jx1nfPWMNgszLUOTI6Znj4AEZvunlHHkeRpYfqpthalvLTtKBYLsS5PH\r\niOX4uY59WNYvgCpK7PQ4SGTDX+r4Yn1AZeMO9GM36GzD/ote9w4JE+jnupbN\r\n9JpgHRUvsOkyuiaS9+Z8xXS+FYVv6/8edyWClQXvKv1rLxa2Osp9bXDzjbx0\r\nFXpDffypeXbDwQFBrAuRTLXpNN71Gnjy+G1LYMqum+qhcv5iwKJWEnSi/cLZ\r\nC5K3HB2DdjlR+jnAz/aE2QYDvlD9mkJ2XCRXKiosoehfKIZb3j1tuaCgwVgo\r\n/fj4U8ecfwbpEBucaXyzWKHQA22qwRzVw/S7kJbNo8NKxHrIgn5NX+13SGPn\r\n9TKCnE37plwq6nokbwqat2hPmTI3JhgA6cBGlPTO02qpnP/6fTb8aGmElW7i\r\ny6PR2v2yL2pz/YNX+fBHot4XraPkq+PMD+7lKRb0wnkxItKkjY68Fl4g9Mfz\r\n46+glJnJ3Fj+ZaGK+S3DcLuqVc3QFLVvIustn/48rIEiFQc+L1bWcr0I0/E9\r\nvYKfE7/Ib67Cmjp9r/IseSOgogPncnYsb8I=\r\n=ovb/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuBpbj3veGnt/9BAiTjY6rChBP/9XHmQfLScR1t5V0mAIhANcT0QP4U9ozZNri84rLou5t4r+Ld8jC5W7YdAAPRtsY"}]}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1073.1276.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1080.1288.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1080.1288.0", "description": "invert an image.", "dist": {"integrity": "sha512-jjqWgxfm+ny9t2BGLECe+HgOUp4RuibVpW5Rch+aVnVwqGQJRSlkWQSyx5JQsylkathRrs8/ybFuGb9KFSB8vw==", "shasum": "1d474eb72b3d86fb49778b30970f3d834d7561aa", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1080.1288.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDK0RO3x8gT0hG14d8cqfq6B5g8Ya2D22QEWOlCoK3EOAiAOT9vrLiHKAqnaxXvR/yb6InMLLDxGZ7HPgzVWTGA7EA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiS1iSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHpw//eBzYbg6jRbEYfOmi/i3up+OVNB0/kBVkpgNZdqroFABpPo7v\r\nAen4WyMZLE3iA2iLpIS3he3f53ZLdY7ADm+vVBtO64H6jEPXosow9x8pdVsn\r\nNkFylvjS3tLIblUa3oNmjA0JKdSbR/xixHz96wW/qhI8wi3r3vd2+HQtfhSx\r\nLuNJpJgTIh1nremhSbrwYd3xVlNY2b/rDao8OY2xmb0+hUQPFeZUxyO9VxNU\r\nR7MopHzpT/yiHroJUTF8td0hDJFMIpqvAnsIFGEYDWw8YeMC0OatwqT+5fAK\r\nKtQsEMcbmgMZm4R/jIk46httjj9fuIWucinGoFhU58/42dgZdj85Cnpyk/tr\r\n+Zh4EQW10qLGI6p1ZE7F7zxlWdkJVwrTj2b0UEJUoUsMcuIDYj4g5d3rbcNw\r\nEKCtVBd0LN5nLUkO+zfZeusytfAFLwo1KEVYco0TQ/v4Oe72PVPZVOUTa8UM\r\nRoIiM5uYKTcOdfA1UfDcTlKCWDz9wot8Ff7PfnlYCvxeCm1hdX37xxLIrfOZ\r\nToDSYFmB+Yol6mIkyy4Rynsk4PTgUDBz1bFlUROvWkIY/9Bz0ziVXmBR7UxJ\r\nS0ch0uli8WYXCxR5XQuzaeJ/rvT1P8l5OQR7DwpEH9CQIJnwJxBYFPAGVLel\r\nLGAemGAlZR0OSu42ZJ3FOPiSkD6I/05ef1o=\r\n=VTIL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1080.1288.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1082.1294.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1082.1294.0", "description": "invert an image.", "dist": {"integrity": "sha512-jZ8xD8wxGMNsI79/yn2+Lq5afHQkFNyc2jo92n0SAlTeVcxsrSxsXXTqoBrVVB0e6Gikxw8EpKmNYiRpzPpZrA==", "shasum": "2acd9246f01bd9903d5145111a3bd0133ba143dd", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1082.1294.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEzTLs/moj6VOMJhrR5TXhhzn+mAaBTEX635qSWmN6TVAiEAm0UwHlgovvYA27gPPolwkiR8aYTV3v6vh60gt4lQflg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiauhEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg5hAAmRf2QYbZ32r95Uw6/yhSlpzxbcOyzeQ0rU8IBWdBFMkcH/le\r\nV4OR7Tz/JArZ39qd4EBM63rd5wKc1PjvkqDH7ifHsznHe25iWBv/W1VcHW99\r\nPgQFMaY0Xo2fgS4fKaUtYknYquaPwNPZLTVRerUJmj00iBLF43tfs0PB/vfV\r\nKaiI+cF3XxE4MyUPlYGBzXJbQS/L76bU3bLYAyO64w6OO+bejaxMVuVbf636\r\n67o/+WMHgxdB0lV7CAgZa+rmUi+zVVTwFE9afuuUwx/mhNjsCCWYxbiAqVez\r\n9Me2hC+mhBHo5STC/SA3UKGXwwDBIq93ytXQBITboYnfO+UwDvg50TXUktqF\r\nwkwgFxEndRbIf4duUqSUlLCZ6VT+xQJsu1vCw01rWMXgjdIDhEiVG3lQAuVK\r\nt3TGdHAp57o1Unlc+oql8/VHvFBU5pt3i+HDWFrGl48RPvT+iQkGs6Sn0VH0\r\n3fFLer66WT1UdJUQMgx3SXVmb/YXT/eyDIrRz5gY4lYQy9EhclgH6mWCW37r\r\nSan8hz4i3e/Wsu22SFSiJVaIR+MSzxvr27sz0C7RZx88DKhlD2IB+VLp/IVE\r\nyXX8LHR2BpRHVu7mtGOuZutcKvy6TQUaBHqrnmyV0Jv0lgPWtmsWtrwa/nNM\r\n9hDa13/uFQhDE5AvBWq59tjjOqPjqCw7Ylg=\r\n=EVW5\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1082.1294.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1084.1305.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1084.1305.0", "description": "invert an image.", "dist": {"integrity": "sha512-o+eChZB/TLsGN4o1/BFTcD5+Shqk6zymUWukKaaQb6OIfKADK728654YG6SZsjrU2fWYUGg8ostxRjDJMaysHg==", "shasum": "03117e0294835f8ac0c2f136bdd2612f06a9cd6a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1084.1305.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBD4e3YW15t3gFcAiw0ItRmzRSWVMS3PiPMcvQmBb4USAiAGbB81b5vXmLaendNqqSGNr4u08yLJ1QKDs6Nd/sTI8g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigoK5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooAw//SLBILGol3UrFk+WFioAmTk6w2NuPmQ1OKJtB93tpaWU1cNGh\r\nsZ8411lWzocLaHYNQpQRM8joCF0ApiLFSn+WNypykKV2AKAS9zQ3hg3CvDlm\r\nyg7BjSft9pAsBLF8exWL8Q/V4h9sUTCs4r5LlrXgdIyR9mnKQWysw6n76hbH\r\nPnN9IIgz1CTQKdiaLSDdnduxOyJIIsjxE6Q3URgWrC/hquVcYWMi15a5Trwg\r\nEoewIn3FFXwkJgDIbtCNF6BrCsFWgV7hAcSiP+G9lMXKZ2E4CzAlVtKyRwXM\r\n+FabTxBrYA42BXAhvD1aXjBfJdAUhdTHe9/ovut0CqYKX1vGzKMa6oW5ryTU\r\nSFGNso07+3qDnqk9iHHkoHY8d9+5oG12Tb+8N9dw3ljYag96I88iFdAo1OC5\r\nYpzTRhZqLwU7TVKyqFGzj8cWVLF/raPX+bUtuhi+/kt3p4jEUUHTQgt8ZBNQ\r\nArsPV9VtvIJVvdHo2HMuHs6giloK8TbGOkVGhRbAHnEP2GZHgt2X10WwIl5p\r\nufUFFYW0kWRDKzhP+CT0YrVrT7GQeGJxBl94o1VoJPtDnd9hjvHLGlrt9Ixg\r\nX3rOfIMBMmrmlbhh0/fmEjG1XAovi2I9YzmxQAJDgJGRno6b3Euinu2u7flK\r\nrG/43+tAulPwQHYZZkKHbx0JjXDeyuBvY5k=\r\n=0bgz\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1084.1305.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1086.1311.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1086.1311.0", "description": "invert an image.", "dist": {"integrity": "sha512-ilF1lwuDo4y2N45izU7wx7YXinPJAHFEsBjIDRgM5UuO53S0fsOgNG+h0FowhlD5bND6cINxOJ0RuxGFovHP3A==", "shasum": "c5dd4c50e6438ffdfb0b527800565a79a51d6d1d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1086.1311.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3LUuzHvWkaYQjCbA4hqFYS9VnYzZxsXPpHQLbSGWzvQIhAPoX/mEekrJrK3ZxWp/csjUPQ0z+Fij4+VbmMovpy0xo"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiH+5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3fxAAlpggovYdz904yKNXQpSiohDqQQ2tF8imJfJ5S/zRQok3R+4u\r\nt7nZUN8pecixQqhiEcApi1tO5z0K504H5XrJQhBSwuoQtXhvXm9SZzWnR1q2\r\n6oXy+Gm2wx3p9sWDCEpvCarKm8UoQVCAeJl+Hc2ezCodEYE58SRHUnARNR8i\r\nyY9t61ZvnTP4JXyCxpTbsv34DL9CyN5HqV1sprw1fyyp1Y6nFVsumBfaMFbP\r\n1aA2xTQmnPFT0av3ooDiyC9RyIQ2WtVvnXVAzsqFd0Bn5VyGykWc2zs9PCRW\r\nfLVKZE5TF2kvhiaXnXHeEuc8EE590lA/SsyP7qsOXigMyZiT2qmykSkHG72i\r\nPVJyfogxerSbtDLant8J/t+8psqxNFG/jQEfU7JSk+OrfhqpvM9tr2r4Ilst\r\n9tYNm+HSAYC8WsNQKEkPnnh1wcmZ+4uTTbNfjRO1WDmgIelBKxUypWS6i8D0\r\nKmuJg0X5IJDVIl8f+nkaKMMnxjb1hNjlLL/ARVdZTZtf5BPpWvGuzTS7Ukl0\r\nLuxDO8wp1su1z0DIUwE+xpAT6Im8hpfyH/R25N4//gt9KFN+guECkU9dEQiG\r\nHYaydmm1KOevUaeMyneCKTwDHa6mDEre2rwgRlXQ6vYLbvgDNmDd7qQApiN2\r\nB2sxY2gT0OF+60yVEkN6Oo9XsxC9EuAki60=\r\n=d+ED\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1086.1311.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1093.1332.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1093.1332.0", "description": "invert an image.", "dist": {"integrity": "sha512-Cj4slQw+A6Na7rMwfKrYN9LB08ftcRu9RUs+HC4nKFLLABVtyOjZ/qvliRRyKBwMU4krgil2PCGXvzguFWsScA==", "shasum": "2993952ab39f7994c599585d213703c004358119", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1093.1332.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDC2CD9bjAu/weyk7PVuJkbmMnZF/65pwkVtOmtd03/AiEA+2SlQMuwvWX2k7/DMw6NDdxs45i9TO/uNGwh8dN9D3o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3rW9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTxA//ecJaWc7wf77FHjcS3x991YoCJ43CLGSS8Webms0K8aaH8j+s\r\n8rKAobYPu0q2EY5nsj9kXgWAgYQk2CoW4f3QV8m3qIHDUaffAIy7dZKc1igG\r\ndNfTtPGk1vYEGnW4bb2+zqkonr1vIN8wRWc1jI/yI14KwrFq67VekzR3bQYU\r\nkxz6OBcercWb7A1qOrax/Lsx6uhpRNBW02e/Pc8hHhV9KQ0kPj3w3cJ1S0o6\r\n5HgIznwU0eR7P6SpgxWQmSOzTq4abGKC7YWqs1cD6B7x5V/FstmBzuNxMXyd\r\nGyLUDgNYATcxrP70pLWqiYRV671/stJwUrm0wP8mhuuQF+J3etH8XGIPTnOq\r\nwMGU4zHkVGPJcINtOcaWOrXNeXRGv3YpcT6an37gzBHRQhtNmn1Hcep2yPGl\r\nlzTmmA1SMWKmAgIU0ywnSyvmbqvyZj+1oPzUZRx9cAhb0q40Sla5DSTSGF8N\r\nBt8cGPfJ6B7pRty2GManrUNDRO0yutGrurRRJFnbO5cCOOzaerBjW59lig6v\r\nF+9L38/MK1R17/xtJW4S+ebLh8tZaxusn7tPLsc1nfLWJ+HLDBspElgp9bJb\r\nAKuCwrUJIsgaCpFVUxQucm0tYNbyKrxNwUPH47Pxx7GtFEfY8gKv1uGE1Hnp\r\nYvlOPGgnEaEMfIyA8seVy4/7RHwSavdPP14=\r\n=4Mt0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1093.1332.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1094.1345.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1094.1345.0", "description": "invert an image.", "dist": {"integrity": "sha512-*******************************+ensTUn+GKrScJUrDRM5nDUSKibLBzSWDjt8zkj7rKDyk6yKsObPtnA==", "shasum": "15e912c0e00bc3ae186c0a65b4d0d4a9703b8a59", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1094.1345.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgd6TEfghewbKh7qg8c7qKKpLeXa14s3iFmhSTCxnh8AIgO7qvBvHIaI7wzJIG8WNEZHmv1qkbYkFxuMdlfsyUnNM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6H60ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0Ww/9HRoFRlQECewfflPHm7S0S4TXlByPI9NHOkJuGys9KxTSpazp\r\nA3WiacZi6YfmO7UGQwTFUqdj09h0s5YFkbvUnhWcYmrABoIeSBLPQLgzZDe3\r\naMAMR+W2QdDNzvD7Zspyet5rhPfxmGqeBw8j/HW1Yvp375g/kSymJN91Q90w\r\npZpmZ0X/4PQHlVle4UK8fqh78zPrEaHyuyB2c24QddcL5URGZ7X7EFuLqZvM\r\nbScLQoEvxbPgdIKYMA6t91EOtarbdX76qS1u+Hw+ETek4gaINUqGy1T286Om\r\nNDeRm7SxgQPQYvet+v10ULIf6e1wkDPrv7JFp1dPcgbzXgh79UL8UbzhU0ih\r\npglGEhDxZ1FcU+zAHP8Tjqle7rHh/VTtREq7yKuKhT5ybXvhyt6fyVkxzgbP\r\nOl4QvqYM4XCSospRbX9PmNyr6RIaYZ8oShtztYZdN6myYqc9WgdjeF2+F9ch\r\nHMBJKBmgcBVV1Nn4HGtrrQ1N3NnzdlseZz6xnAi7pRB0KXytZhM71JtukiFZ\r\ngc/7zBy2L/HwD3VzKrVwUBqT0py8Iw724Qt1nlvTBR7/IXvdb8dt5bhOWYOc\r\nGCL67aV3kQPVpz6SxGqOOb39G+P/9pZ85apAUTy+p+70/YRZJdQwU5lArFGN\r\nCy0iALxeLvVAKhP0RqAL9NbKp7UDLyRwilc=\r\n=//2j\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1094.1345.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2-canary.1087.1357.0": {"name": "@jimp/plugin-invert", "version": "0.16.2-canary.1087.1357.0", "description": "invert an image.", "dist": {"integrity": "sha512-WxUZ31jbIVJ0jW+umdX3TKUUeGvKErOGvSIbHpQUtLEM7jzyoQj/0uuqOcaWDUKpGDhMWVc03u2MOVEQCHF8yA==", "shasum": "f3ab8f339ee63a9cf141e909b95c4ece72f61257", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2-canary.1087.1357.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGlBuVDyxV7zN7I058pEiT0lDY0eFRjqy1dXOrDOhyFGAiEAhXfTZu3fBzeSoEnuBSwC/qG3IgG5CyQqXgAwc1G2NUQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI1ATACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi1w/+JUC+FFnK5yalm8vFiyaLA+ttXsaP7V9mc7YkF+KYN7fXGp6W\r\nxijkvNwFJtYHhaCuq4fogelJvrETDdFrKxKAea0YapK8/hIS75DVsKzS7PRi\r\nUu0uvmeYrInONzsq9ibuMY9C5CTq5IKl9sSXhBCFPCAAL3pPZdtsna6n+6JH\r\nfx3hxlIWdCfhcGrolZmbPRd+9aWUNpVnXLvpfyMpQVt8IBhQfTstWd+0VQPC\r\nndnJYCPSItxAkmD8wsbML77s6dds0x4GgaRo0NA/UwlSZwpDXXFCGOULPxIc\r\nkP0THyuB+XK16jC9Wk3Tb59ZXp4940VXt45SCLroL96/NBnoOtcy4eAVRPp4\r\nDeXbx5CW/wf4ptsx7g9frJwL/g7ZE/F1NJ2U5KJ4GzZYFyanfzuaPP2IsaOt\r\n4goL+ta3ek0i78N5xovhgxaGlvUWxulNENGmUJBKqbfrd7RqC/OndzSuoy2O\r\n4jLAx9Stirx+x2WfXH+eaGIznTB7HMNU6oP/trVsrgy5V8lDaALAek82yJ6h\r\n86C3Q5L2xAIixkpn+AY24MYO6xMacq76Br6FKs4d5iN2lNe255unU8oZVDtK\r\nbQgONN+r5fsuK3Og1IxcsiAU5JMyRdOEI0DPmjFMq5WDmDb27oX88Ll9V+a9\r\nfyk5ducA5DJ30XE4fGB1XQ19nTnXbtEID/8=\r\n=e5mt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.2-canary.1087.1357.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.2": {"name": "@jimp/plugin-invert", "version": "0.16.2", "description": "invert an image.", "dist": {"integrity": "sha512-xFvHbVepTY/nus+6yXiYN1iq+UBRkT0MdnObbiQPstUrAsz0Imn6MWISsnAyMvcNxHGrxaxjuU777JT/esM0gg==", "shasum": "6ca4f7b204c5d674d093d9aa4c32bf20a924a0ee", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.2.tgz", "fileCount": 10, "unpackedSize": 9553, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxH5lPpfgGOr1zrW6um9HZ/74++FHJ8rW2yflQ7JkIIwIgQH5XzlXaYLYFqFyp1BUDz6Y0GUmCXsh8obFHVW3Dj8M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI1SEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt/g//QR71BvhDh0V5/+oDxmVqSIsFHhmY2Tyos91rNSA/k7xo3ZVU\r\nVim3FQf/cyERozS/aqWwxgm6k+I5+eVK8jjrcykhTwAm0WtWYcervyKLBhVm\r\n4dotehHlusB8Ft4+R0Rh3jTlagHl1wm3RSTv+Ee8+WHlcwckgj0Hgq+iYovC\r\n7Z69DoF+3x0OpgnV1guWLqj8XzIdT777OheRSdIYI4iCWwhlaF9fqTMgCzVy\r\nc/xp+ulZ2eE1pCJ7eG5h3spwEn3wwz2aaM/tt6xLqGTcvb3z3gUKQY67lbV5\r\nPT1HpMRICqpstvKCdrEOK4s74eFTqlTw7yku/9xghbgKgcyJLZr6/QeqLyae\r\n25k4X7vw+b1Y8EN8sdfDCEMZ+A30eNzKhYejh0uvewSYrYIBAVfEIxCmXXZp\r\nTrqOwtmw5nrk5kHLUWd8tVKZdzZGDfY4LLPSiN1UVMv3LA5DpOeYyJmYIprk\r\nEz/KfNC7kvVKodE1Ds5SvNeB4VQrWohRSYq2f63Eft/rWIa8vlH4E5u8Kz6j\r\n7/VwPYGgcCmUe5BpqVTbd6c1PCxVe1GXniNZTK/3osl7cvL5a7PRT7Kccu9z\r\nlDf0joujJPkmE97UQwEbYkHUTmRmTKwGtmUlATE/0N6OgpNOZHyYhEqirZB3\r\ndjifOgvIBeY/xFWIkm1dXp9HwKCpT7idF6o=\r\n=vnja\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.2"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1100.1371.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1100.1371.0", "description": "invert an image.", "dist": {"integrity": "sha512-Z7f81aVvQpBJS+vOGZ1P81CDn3Nun8+uYSKiRrhQ9jY9N+Yqa2bynHbFAjRwMMPbe5Fpih16OZzhnxXJWVqlqw==", "shasum": "69d9e5325b4142af98f24c1c7ee82e6bb2a9158a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1100.1371.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbhGAjHjOe7autEywIKE8SodjkYpzK6lF/8vL89l3nJAIhAOxB722l0yrFzLO1Q+cV5hcvNKagoNBWiOBa+5pEw2V6"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQzRpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop9xAAnoUIcS6e8nB3n1GbfF5TFjcZ4ydVYX4Z2WJtjlM2ZCYg1pPf\r\nx/8xAeMc0nHLsfqwlrq2ZJvvQ7qQp7gW6L0muiruqimOrimGyH3p8+Vvy+bo\r\nQ7lAHanvg4ElJPFwTmZTjI5P6THb0eZRufEGbJ41YCEZhYZXZxvzcSx8B3oD\r\nw4ZBFdvQgNeUWr2q2o3kjACzcFT4JHIJ1K+Qc1VGor4W9G6f3mdFWv8ZcIxw\r\nFpS5Zaqqxjex9RKii+fJYR+EU4Iu/waULceOT37ziEmMxm77JCt5NZeYe4or\r\niBVzyE0A0F/ekoXOLsPTyrSxsZmM+Qp0XhuHbVKl6qSju9Npbz9rPCgxz/Lh\r\nItNwRdSt8XEwUERd6cp099ybCGoyWD1ICZ8h2Sed84V54dAGxBjox5eVpy4m\r\nWRq0QIQ+E9suq84IhrM8R1SzofU8tpCiEQBDNH3z+QtQ+DxKzmzjzph7wrKQ\r\nfqYcQZuK/EiR4YsGxdhZ8znNAV1G9EuKZ1QLtmURaCVJrcBOQ2Y2F2LbU/gv\r\n9RI7Xh7zjuFu7kK8g9itlSUPt1aDsC40uZzf1vY51QR8gDbAAIPGpds4BiE5\r\nTZumqoRaTImna4Us8kOab4U8y1+ZlSp9BFGl6pxsZibGsPB953/XkbHyUMbg\r\ncGOhw2ZQVLEkAQ/3U8WIHxa8+mPRwG9JMjw=\r\n=yHAI\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1100.1371.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1108.1382.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1108.1382.0", "description": "invert an image.", "dist": {"integrity": "sha512-zhplTCT5e8wwdPufW4Ci/veCUEvjjv2/iQLosYLKycpZAWvLRI/8IOshgN6Yrxswx6Fmre7tuTRj1v9zs6gsnw==", "shasum": "2d686792a171a3e3c200458221a0667da6c81e37", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1108.1382.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBAdgIOxr/3BPrYF2+FII0s6wP7ph1z1iW04w+v7Ukx0AiEA+ME9ZegAAmb1oADEQouxel//vNagPeDOXAf3qqV4Su4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdDUhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcVA//VHDZfDgedzHQY92rxOCLWmeSc9TdeHlWsWpGmhEodMBqohON\r\nyDxOsVLQlRUPB6X2yMRBV+CwsoyGE+8oN+lnyrTUn1LbNHEooRrRkeE1cPYH\r\nSEodqWbBOYBcQJkyHiqT4rn1Ov8AYoClaNgRZ2QR2JWOq244hd5Tw5v47z2Z\r\nGuhhs45kskLC6yHI26j5BystOwY09kv7RZCMmi8ZVXIqXtvxtmv3p0TuuEuC\r\nd5Woe+sk9lx2galpVcVMt59fYtzPkU8E3iVNa/0a+3XiI9FxU65d78mZ+/LQ\r\nNjLMAu8nfvcnGo9ee/dPNzhytfHFV5IBhKKAhfA1Cbv2Dc5vfZV3DFBh1sUi\r\nsM2Ha9xw2igN2Okqmum9sVr8JsFiWUN8k4UyVLqnG32QyXl3dM9C76CtaXNM\r\nuJ6Aq/sgVoMH48VhUMSXHr8a/n8FzJndU6LSEbFkjLxuRD49Dc5+s+SqPHc3\r\nnwk73KcQ4Le4A6mZvUBv2s1imSvBsTKhS3vL/JAoZs7Wwi3NUOK3LT3Nf7gg\r\nHu3NKVApNeipk+sVkLbov5UxSnDorab/l/+Uz3hIjQG49Jg7mjw2SFyJExvq\r\nves1puc1oLvWmXpOseLRsU/b5I3XNTyl8QoI/jUi0ywvq4sALQZKihJf1Fmp\r\nd64lw6TbOiCaJMmvIqyRiiJN9RCqiYALkog=\r\n=nzyP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1108.1382.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1109.1388.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1109.1388.0", "description": "invert an image.", "dist": {"integrity": "sha512-igvoOgIZg1ZvFXXE/hgy2jDA3VcNkke9BuTbhfuxH+m70pluzBPOTc3uQwIf5p8PkvHpuJBnRgzU60RW8RV0Nw==", "shasum": "c62a69446bd166a6954d4352da40e9388d32e992", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1109.1388.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBFAvDEg1y697EM2w97G3rsQGHbL7q7I2DNUYHhE7x4MAiEAnierYh+w5sBz+GWcriAb5Ixvx2qchxBJZnOl/ymwL7k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje9DjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouUg/9Gd2Y0S5muf+eoLfYhcg95y313Jv+o9xpCuDICxgnRIsMu34c\r\nuK9vL64eOHRDHz5KlqLcFd5tiD0ozIEPFeXLCpAnRjchkj4q/Pf8DKKCWpkq\r\nG3n3i3ei1XbVjj6wkdyLMyXK1n72VizkiAuxdonFlAnrvXSyhcruHQ/k+oIP\r\nVSxZAV4MjaXHP4IRPEXmHF1j600ReDHCRw/SZ6JxddmVcVsi94aNTEd1MWu+\r\ngMEmTeBK3uDJBq5J5FJ2AW2LM6EYDaQyXRlvUkOVeLw+Bdb6O7tOE7ztjuEi\r\n4oKKatxpLd9kByHn65RLWaYgj7YRSh6I4X9PalLsrGTbS4HlgNUb4+cuacBC\r\n6yqQOEDlfXYGTAvvZHIQ75gMZwaKkxfuHenmrO8CjrgriFTjwSh4tO1wfrpO\r\nnWm7ziO/+gOlTI8r1Gqn8vpdc2ClUCnXjkUDLhgZqcj/U9sWzA4qV3EyIpeu\r\nKL8gvmw+IA1q3ZBezFa9epsqs0OMkH7HKS3yPEWcOd89upmkq+8fNJTGP54s\r\nbGwQZd1HKIAPLhd1z1Ra+PgcEKgaa6DOcleKgvGisUgvT/NAl7plGFBr4Jwk\r\nCpXXy5wlYzmIAPSk25pmSvEju+MDSMWZQatQQNeWKet7UUHxvNFHSia+Y08E\r\nsQY1NiZGyjvayETPOldpKnttYEslcGXNlyA=\r\n=VhV8\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1109.1388.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1113.1404.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1113.1404.0", "description": "invert an image.", "dist": {"integrity": "sha512-eALALCd5r5Ve8cD8OZGtFQsqxQQdiOFdYkqXXwvD8C+LaYmvIJt3CqGAfE6FfF11c1CJLd5vJl0GIag27mjYRg==", "shasum": "d4f077f7e64f2df515651ab17d8570d2fcd9b0ed", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1113.1404.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsp2AYrtkeFSkcPxdENnIbAukcyQsfmEI9V/CLDznH+gIgYbR66jEG5MwOWRb0rNgB2uKy0wpEgC0O7CJRy/LGiTo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlLoqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrruw//Ykz8JbNKL6MvvAmZ8wOTru2z16HZkrIKeqmhVA3v3CfEhUiD\r\nxaENRRHfmUA5nsgQ3GhUlfkVbJ8c4LxT5kXhmpeU3Hl/4Jv4F8ElM7PHiIPC\r\nj6nWvRrglEwufXMNQ5IGfUIit50pGUwphUNrU8nMzBtvDozY+koBHDwi2LI+\r\nmuAh5gXdeiAoEc5YjsexF/IN2+dxqhMKv2+6uEZ1RFsEjH9Jt8ZN1FCV1dhI\r\nhHjId22enZULiT/8aj+S1zLbKdxF/k1/6XLGaBYMuZj19ohKYJIA9z7ecChO\r\nCIAj4LEQ5TPwo9cNqgUMx12OO2K9lEgy2j3Jg5coR762i4ET9QNGiY3DJ3jl\r\nUUY6vQGvyOu3jZkqhk55Er1dWfwCx2w+ivpuC/5yZG7qgRrg6QBG284odHCu\r\noaAt5zfv1XAF/5AQZ20vQV45OyExQGZN0nIcFY8M7K7qg5d4lddz4n4yUv7X\r\nvnwHoWAJHPSzWEAIPiGXU+1xERdJ00EEp2kWGaGcFe0dSKvdXeQ9FsOIMSKC\r\nw4rzkwy0kljy1VhmxZXdPtjK/nQaQtZtJEH6L7VKhltsuX78ZYRwBchaluC2\r\nnLRs3+xKoCffBZE/ZyhN2CQgtYAIf0nkM+4bdTy3DlZe0lYK/KbOSukq0b0g\r\nnrH9P31Y7N9XFnmQOr+qR1b9nFGAauZ6ggg=\r\n=Z+bR\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1113.1404.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1125.1580.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1125.1580.0", "description": "invert an image.", "dist": {"integrity": "sha512-PEFiHDX8f5huVBBcOMx1dzui5Ur85yzg+h+lR0u1GeFJ3m965I9300qd5UfKBXKUBO/AUQwsHzTDgUS+lLLhfQ==", "shasum": "3fabfa239f1dd5c5f0c2f7344456587984b7f99f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1125.1580.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDOxfavLV7h4/k4OvpxRTTm8wOwrM3L9x9N97EeZmb1qAiBsHvmG2E3k3oULWh20P0S4KYcKCXuST4yE1OrqQX9U4Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JmcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqQxAAncaGPgatu6rkdjniUjyZ03JunLYL9fu/8p7MGj5owhr++9qz\r\nvEvF9p+fKy6pJmlPR5MervOkycBa5SHc0LoUXQwJQduo/+NMOFJvLkid6el5\r\nR+8+wYM+66ZpC6LU7fN5FvjRz34nDoSMg7bxJCJDywzmSkbg5IvzqF6CL61a\r\nPaYUjlgFeCIUi1JDhnUBaIR/gxZxF+AMS91PyYbr5I4Xf5uuHX6EGZdOJBGk\r\nU4HrXEA22yoCK6VRVqP6dxvwjFFZ3oEoX/nCo9mhWW/KinyVTi8e05sEQdwV\r\nTCBNttThRIkoqdeqE2Y+zqOkqonIzxLBjdhu8Q9xXzkb9pNPUY2VdqDY1N3H\r\nXwRkelOJH3J33wA2RqB1kPIeASv9j2RW9aXiyE9W6XAUDJtevGt8zMWRYe/x\r\ncSjkzNaETMqsbLqYz4r9aW4G5a50dZYqSUJCeWRGCO7saKEx5aZX2pN+yZlS\r\nlaDE4Iwctny5DNBy6hbxN/Xd5CFbB9S10bIJKh8IKl1pwWudSFdi7Rt3j5kb\r\n7OeRmH8EH91AvVyGsCdanKqYowbBD9qIMZuIFmBx6weVE14Gl8FCPlHtXPXZ\r\nyzZUYON7J0SgDdgzVlDiGiAjOG/S6Q6/9eWoEhER+WYjp8V5wPqjCMj2DoQR\r\nilK7q/nSI4LT+i/ZP2GEa2QyA0d2JRW/X0I=\r\n=qrpY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1125.1580.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1014.1579.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1014.1579.0", "description": "invert an image.", "dist": {"integrity": "sha512-gnLfY4LINFKPKAVwWHLTiLOFUPG3uQze5JRcLE2JxngqJFeKF6pn8dTDT+UyIiBUWtqEE69kO8ST24aHbrF55A==", "shasum": "0abb06c4d9efe0e2ce01ad05a121676057ba2ef5", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1014.1579.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBII2RE5OqkbqXvaUuO/68XKqDMikRvyV25+YsiL5WeMAiEAhB0IQqMomiWIJs9b2nuoOHBauNyFtrDhAutKwE0dfBw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JmhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9rg//V3DGyVce5XEvFRpNUrFtqnKgqzX0usUgKe8Dp5K83nct2di7\r\nvVZyXCfxaKoed5KVG60aVuDBA3JpSo7SNyHQYQUq3vDB/XH8vT9qkXM7NHGp\r\nRvO2KCCFoUIDDgq7q53rgogbEzRk+pzTHCnrUt8geBy8hg1c+KAi4+sBBZde\r\n1AxrdgAh/FFny5iTxSfBobXZgiwlxz924Kjcc6B3KU/liMlrg29jFEjuu9UW\r\nxCgzVKWPsNwZUngdK8AhCU2SbdQWOKz6CBQypNsVqztK4pItnfvItcP6myPh\r\nhafOVmfF3b1j7EYL+xwZLcmus0CSL8hC6rqpMFit3yPv8Mjym6c3ZRA2aC0k\r\nOmmr1jKOtXXYO8X48qkai0wq3j1U0DnsBaFPlYO8s55SI+gZeOqnKO+m3xxw\r\nPJ3DF9kqPNtRw518YOYD28JcpBOyWf61/ertIro+mP2pOoUSMmCAI7eS3PQg\r\noizDVP+hPfOS93ZZeeRB/xa9H5Bcdv+SabIgUks9RFeR3ZlJvPpowqcqQ6Pr\r\ntH99bArIPdI5L9j8CrVtNPVJpT+TV1bgIy/yJvYwT+gjprP4iuy0ZZf5fsb7\r\nOGeJOzIyYOndXWNkusH+XWV0WEXmLU08PFZq1w/sQpk6axQeiSegw9R2C86i\r\n0yzI/BAjXn0gYvBQsKcprQWAJqLdfvarvqE=\r\n=3bux\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1014.1579.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1085.1581.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1085.1581.0", "description": "invert an image.", "dist": {"integrity": "sha512-XYBS15rDytPk1xviQ/8ZBJKSRlJ2CZdvA7H4uPHsieUCNOM9X397clkPkG3uaTOHqmHyVdE7M6ulaBQ68ktt6Q==", "shasum": "b3f343ba6e38b85ea85df5b11fd642a8e94e833e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1085.1581.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcnkNHgfhdl+zlDXbd2xNzCI2ULepPrQAytVhtQcRWowIhANNEzseql6cC8mRU64exSJkMwLw2Qa1i1Rtt1Sj8abJ8"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JmqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFzw//f/YXedbLSb5nUJRf5qBJpK8vb0CEEZSvMxjnf6foTM0gIpd1\r\njMVFAmNDcH/U0sNmHiqU3qnuToOJxVAPMNMCgEIjphr+nYIzSESGu4zrvYPk\r\nJqJoQypUJN/il3WYFQSLqiCx16plr/QL78//Fc02CrDRcuSDnZJKnzgpGYeg\r\nhiN8B804XHX71tMoh/Nsc2mlFbEdxBTS5+mkATINEfD69wIf8ZRLW/IDyF7h\r\nYTRfoBvuyZhkJ5lIKenrt/K1dzttEwEJkX4rBMJhJfXjBoaFQORjbeGv9EhD\r\nrfQuGxqSWGgmnS14zqVIkjuIp1ZJQEYXEogS0dXaAXK7+cxqqT3SL7a+Xi4X\r\nwxTu24MWEY+98q/mOUaKooOw0uGainL2wwExo0/PS9NL0A2QSvyN0ULfXfIV\r\nVn+IX/x+Wfp84YzP2dprFwRsiFRz7fn7Vkuux0b8yZTv2rYiIKsC/SD9ZbiP\r\n8IhAohKv7Ni4YfKVX7CiA2B9zc5btFrWWaaDWEDkTZwR9FtkIY5akeG+mc/R\r\nT2SR2Qj39s2+wqNmQZIcsDQ5cDDy7CeEXINCnWwPc3P47J/qY23f/yK3K/3Y\r\nd9ORbBSA/KFZ/y0L+FeesOR5LLHXFMeICbhR1DTkVk8p9iz17c48mQQSDPbZ\r\nVGsOrKxneNU3KXg5LI319G8VNyvnWeljRpQ=\r\n=+mde\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1085.1581.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.999.1588.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.999.1588.0", "description": "invert an image.", "dist": {"integrity": "sha512-Y7l3LHv2MODfUOOLvO2858wBCMYGQSKA4M5wCm3kzxNXpADfsDj7jRh/cZXG+KvKYdCorvUyNXa+ujnihXUvmA==", "shasum": "b288292eb890df6090ac4525701a3c439e75af64", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.999.1588.0.tgz", "fileCount": 10, "unpackedSize": 9588, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDekUc2CcL/NVo5WZ/jxc/8kr2229JKDLUlZ/I9gMfr1AiEA7fJvCLGgs3eti9NOpm/UDrnqF5ahhZRlp1SARdpD8So="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JncACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrduw//UCf7VP7cvgtbaGFfF4DSg0E7Wcq8O4QKISuZRkfoyJg4T9/r\r\nW4oyJopchygwLx/vf3USocSViS1bOXS4R7ZZGZbtiOQQYc5BHZRnHZ4jb80x\r\********************************+7v0BaY5OpwzvOfndm74MT49qXFw1\r\nj1baeb4UjtVYCZqOJHsEKnvYbavJnJr4Va1i+2/hlMTg26XsihE1keaZ4OqC\r\nSkcyp7YqFtREXa2WXaYQMPU+VY8y3AziPy9JICdWPu+So6XSDuvtDwF7VQND\r\n4R4MoMOnUeaNAq5WLnAARY/kkAh6m6sPJwiG7RzJHdnikf8BeEUfoc0hUik5\r\noGPmiLIqdVnRjPaU4VBRCUAY05+NBJIxrOivYA9a7AaID3EPUDE9TrQcj/O5\r\n9fuLXh8qEYZDFq5gd/sEzB+PbUX49HHNgYr3e27VdPco1DwCV2mtcqDrKAoe\r\nTewMSwhKxB/mFEBzgqFviOAsa6FqnMtkYICSTnfUhUGE/5cj51T4yLe9zWzl\r\n/UZ1y4O5bUrfLJVeU3C7r7MkzNfn+IjmVI+PneFgh0F+woXByRwEe15RQBUL\r\nH33kS+WvPvQsKfxu4kfDlGCdlskXy7X7pmBDekyF8R2i/ZE8pIDZDkra5wsT\r\nsI9LfLlmbyqvQnr/9PRD1/MhuiXAiBWMEuk=\r\n=dAMs\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.999.1588.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1040.1589.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1040.1589.0", "description": "invert an image.", "dist": {"integrity": "sha512-KMRX8mDo9a8G0HJ3vmbvgl22lYoJeUOE7IvPTJdeF6meWir7e9LCyJllOiMIxIXNVMNtBJtYVxsG8sXlPdA4ag==", "shasum": "86f9183227808a98156492adb0a08675ffcdf894", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1040.1589.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDcVFcgsLIFgZF/yLj99FjfKqkHyBB+d0bVCt8uCkaBwIhAO1GCrNo8ufg8/3WbVTRdDclbT3tr1eXLuDzGCKVyYrI"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3Jo8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcVw/+NqjNETb8UeSrM8ZzSBd/CQJxVsSw+wr1EnYx99mLI4svzmZJ\r\nt3L3BNlbF6AGrC4G0sB0WUaqucXfT9D1C2CzCOXKEu4tADSpYi2Rw4XofIwX\r\nkV0vNQioN4vll4GdMYldKXVnt/BgjVP0h20QDNA2tR9/GyLCn7Ntqo4MA/Im\r\nQrpWC5r5N8pnGnvGHk6Qy8iTh5MbLeVP94xl0137GC9hfrY5YivVsUw3MEvf\r\neJusfGjJqs4V+qmMvI4XnFO0d+/v0kDMV8VVRItlECuc4xH/zEImNHtwCjo5\r\nUYEljGgnt4R3+c8cgXuPSfEX1r+36BFOJ06FgnfMcKbsa0Ahd6sxoB90cL9O\r\nXUcsmGiWepGnmdt7XOsK2BKadb96l5or/wej/WmwFF3Idfwo+Tc5elXP6Wg6\r\nfwT1GdpHzlLttyar3fRMzyFeOySP7uJAO0TA2vBp8bHrpC5RUjj4Cp9dRnln\r\numidfN/ysdVYeugUIyxLrb0myI7JyVspd7Gy+lqK2k2XyNB4Nwea24+0he47\r\neNSizcobCwoTp4T3vEd0NGPn2FaykhuPeV2il0la6juLgTXwLBj2hvgtg0tq\r\npVTe9LOv15b6sVPeNCb5nZwWr061ynERjbBkomdBGiQfO8zofXSPS2bXwTZT\r\nzBLoUmJMyqjd7CoC6iTFR3ZosqxfKr3SiOY=\r\n=SCQq\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1040.1589.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1082.1591.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1082.1591.0", "description": "invert an image.", "dist": {"integrity": "sha512-8yAOE6fXgnwHBg9vURRSXM+J+nUjpRf9r7enzZrKG3C8i4q44rr5KxbGRNWi60mN2dwnyEqahGl9YNromWSpbQ==", "shasum": "44a465d1edbf33d24f47b923864b277cd56d0203", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1082.1591.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICKeoCT6NXxo8kpaWCMaAWdX3FARbl7XUc0AbNuW4thgAiA8T+PuVkXQ6yDfaGnB4b9x84jKE3/HIP6a/yshjkHoZA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JqKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPug/+J9nuXJYrhuHG94bjYjYcXbX337T7CI4pwK1025yyfcV+n4m2\r\nUT9mI4MTQpYtV3yQdLWb5jJdZ7/vMVpHucNWg7OUsnWyEEJeo2D3QZBTHeGA\r\nXUD3s8rHj0uUtEuY52NCCN40NtBqWPmlgO8XMq9iGXqf2uKjKhXcaFXMisbg\r\n/MIiyKnlrTkIrRSyFyp+HVKsMYcA9b3RLIXzXOijs8PkASgUUnnXpuLykXv/\r\nkuqCnnvypA/Kbe8IcyFchR2Xc7dW7SmqZ+KRvslYupP9bMqaGRM1yFGNRexZ\r\ne2K6NW0qeAUa7GvbG4brNarXtnup0eM07ReO//DE6/28Fez0rPj6fnWurFas\r\nrf6NZQzL5+JLIm2fKNhTXNdpijV+LWr4J+0EptAdd+XSVYzDWIlXZssDjnhp\r\n/E24be6rigO+BgoSjXNMNPBeXU1/wYItB/c8ylORhx8bYFT6KOK4IhgLtMx3\r\n0tsB0kGElYarnshYwMw9WNTJCDxhmbcT7MQFOtOlFBoMZsgBZE0Ad6tH7vRH\r\nHRQkrr5DTV9rajxOovw83znk1F6k/8nsERAoqs8gt4nwngoomqZp1bdfEBOP\r\nuliFVz+98XDUR776so1r9ihnJEIZMAKP2xmiK73yG+ZKEK9Fj5xLL5AO70V6\r\nRxbzog4VidpAwEiAHAxt2dwPmAtA9Q0176c=\r\n=eL7a\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1082.1591.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.1045.1590.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.1045.1590.0", "description": "invert an image.", "dist": {"integrity": "sha512-aYy9Lf0uYXUr3HJwN0ydqv1uTkDCp39uTSPUEzuU4t9O5SiK/duXTp/XdRg16cwBwwtVkNqAoqAH33y9IfXR4w==", "shasum": "8deb109d1ee876ac2ae79da02430687f0250f134", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.1045.1590.0.tgz", "fileCount": 10, "unpackedSize": 9590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/g0N2wiYe8bkY2E5l1yap1Y03auYzVjWlJS1wTYxWNAiA2SMv5DxgtQmFc1siAmVSk6sZXDFnjCvNffOFiTgjt2w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDWg//c9k46g4GlIh8WGCbHJRb82DcF8fz1VZjgOxs5SLRPlmijWcU\r\n2INHJiecutzlaMXc8TGjoOyNggm5ERkNBmRgbqFosqVWyaFlFQN2LFnKTrYK\r\nYr9oGDDQ7RC0a2OxNkgX0WqN+63y3Hk1SdQaNjz/zDvHrmkTW2U7srjkmlfh\r\ntsaSa3c3QFVBGURMed0FS4Rth0PUFUkxBv7JOQdhoK/3cYVDYpwD92LYH3h4\r\nxvYdz50h4E1LYxtts92oxvLU2v20KhXzulAsnDOEO8oHgWZENdGgnv/2EJw4\r\ndKOOSMIaNY5W9wVYMpKAsn90ueW0WSK43n5QhBeaeKFDpnwKmPY2iR/7NerL\r\n0lqmo47jFh+O5xAZWvYWXYtKQVXIwqZ46ik8y/kKf4/QgA6q4a3Q2mdCH6jl\r\najqSqlhtxI+01JTHV0o2abZ1ohMVKFjThVqUr/BVEDe2AkIFQIPzOA4P1TpW\r\n/NKbw3y7EQs5BNZTKRwwF9pi7uBF0pVaQgJiSxSOkvYiYc48E9i6a2Uoqvb6\r\ntKxro6aMczi7qpw6hwCTAP95cbVBDsc+7ikGCO2OoulMBiI8fVv5GSkw7YQI\r\nYDc0djhY5mlGOnv666HQsTx04qOkb9o6ZWJmKNIvhyDGYtbQ91WSoEF3DOpa\r\nDSHtFIzEBvLNnlaIlrLfKs+EWn1G0Deco8Y=\r\n=J1Ys\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.1045.1590.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3-canary.993.1592.0": {"name": "@jimp/plugin-invert", "version": "0.16.3-canary.993.1592.0", "description": "invert an image.", "dist": {"integrity": "sha512-8MoW1zYx0re+qBV32YkS01rBUbCzqitDJBdOSP0yIh6uOvm0oaLiu39c7NPgyqA+ert0uX1WMdW2PZY73m2dQg==", "shasum": "4ed30e6a87716b4db451367e4239d779ce1203c6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3-canary.993.1592.0.tgz", "fileCount": 10, "unpackedSize": 9588, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh0Rc4SOKiiJl0LMtGrdzTAJRWLIv7SM7ZP1K04+O86AIgL+vLamvOBHbEcN+eZgfNTRZEQ2+aY/2qHIam8m7qlgE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3JqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobWA/9EJRsBcpN8I59jyBxlFIuLY6mkyeR+uMNESCwh+NSZxSW7gWS\r\nkdS2U3z7xImK3RW2FTYua3FmPHxC6fG9UGeol/OVyeV9R2a9MNLeRadeGdUG\r\nX+TxcKW8Kd71zPtxfia18MObgPVoHqEn11D6cSEbNsLMXxDjJxFwbXqdfQte\r\nLI9wtPt3CR8fG+PGDB1yJlf5l6trqfYGGp9UmwcEL6tAhihzld7TdRpbFNlN\r\nIdn+XKau/p1cvwvmHu2YzVEViFai9hlgiL/nljFqxv/9JUtuEFw/XCCZzT0l\r\nWz/6nTauN0HQVw9bKvHWSMoJ5h2rgyGSIBOhR8MoVJSOR8Lnr5A3Y+LBRE7f\r\nUX0IzcinmMz+4kAkl340ysKLtmHR/AKmnPpSXy70k8dyKipohUEsWUy91OrQ\r\nBqnmzj/NU0OzUZahnrfynEOWE7MbuF4n2JDiJX7neQAS/fqnM/K67VhjejN4\r\ncmzOeR1saKbUgoflzdRKkep8xtCJ3QGhgX/lgAVWs9IIiHPEQS/SRN7pcOcF\r\nlVjUh5k7E07VjP4nRn6SpUB2l4PqUzuhIjGr5963HE3QtG1y+oK38M/ol1NE\r\naVT2BfdXnbzcK1VUZoUjTDlFmNRoX1YKKKjF+ZE05DXKvAfce/3f9wKBmE33\r\n8a8iTafESIi90tgeBXUH8RnTUdJ8RZMqgu0=\r\n=6DIP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.16.3-canary.993.1592.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.3": {"name": "@jimp/plugin-invert", "version": "0.16.3", "description": "invert an image.", "dist": {"integrity": "sha512-pbES7GduCGjTTGHk1ig2VU+Fwdz1XhY8xW1PnJqf9JJ6F1R4OZ3x3SWHbgpx9Y2xJJHyfOmzav2JXY6KGqTPqg==", "shasum": "295991694e3970a48bf657ca11dfe024267dca06", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.3.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuAidVrTbHXNlO4PrbnUDK5nqUeNVRcXKwVl9aaxPkswIgGnz0ubmMlZdqU7zFpwTm+WW/RVimTx3lCOb4MLUSerA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3aGwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozAQ/9EFHRDLNajrsqiRPfa0YjuMjDKWv/6IePLlgGOaLir0TTOxLl\r\nQym7/9QI7P0C2iChoijTqfj/PvojIMv/qvAuMj3fE1Dvj4Q0GfDw7kLiHI25\r\nCl2Jrrt98jMuPiHsTAY9i96SXzUsBF5uvHr+AONhs8uk+HztEyCJVYSldC73\r\ncrC9FoIWcqEiWHK+IZYwcFUVs2LG4MqAGpudDKscxggJaHfgLhOGzDvMSoqj\r\n3h5pMtYUHEnUlHurJGpd9HkefVUep6olEeRbCQzChIsJUeAvxEjRjlVUbIRH\r\nTW58kil/Ic7Z59wDh1y8dWhKttJ1hmgDOipt3nwGXmLFiKuotb0MB5q/xUXP\r\nT2euV6bgjkq8Ew+h3dbWn0zrv0XAVQk/536gDKEarluTrsn2awvIr0q5h8wF\r\n4nR3MoV10PCoaUCyeSwsJJIHdbR0YX8IVoL7duvtrQJ2Rgh3CN7qwnet7XrX\r\nXCN9CcjKsGrlKPQtJLFhunHgoV2idyrhL0TNKyTBPhK/AVw/epqX28RT1Hk0\r\nLm1hkOaix6q/QJbzXvmTmCP0DykaOepNg9lxGXZTzzlw6DzqEdyfGWPEQ3HB\r\nXAIiwKQNw1CNsn367BOZulNClMBVGHOy25i81cakY8QbSKcXXqXss82sKmDp\r\npJEyIr72uT+lV95ZuAerDN/++FUMyPI20dg=\r\n=iMJ0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.3"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.4": {"name": "@jimp/plugin-invert", "version": "0.16.4", "description": "invert an image.", "dist": {"integrity": "sha512-F2RQSIk5AX24xdwtbC6/nSgJPhd1yeZcGRst2Mw3Kd+cmbHewqaIAJKHM/Wu+vfXdqTe1HdEoEJgBchAyC5PXQ==", "shasum": "4fcfd6ab5ed65ee783e2fea601ba8abffb89e2b5", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.4.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICQXw+ETRqOrKIuXWNNG80ujEvF7BKeaO7cWeBimky10AiAGpemxf7+z+QsXfZ3xw0IauiYO7oAHuywRe10BuRZJnA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3aPLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXug/+Jbs92duO+iIYvf/w/36Cw5RnoyHnrW1HBjgv8OL8bawWWFmr\r\nx+OlLtp+XJf2xmVn4gCepUNRZL3XxXCL5/C6zWCRTMUoU+2bHETKPprDr1RL\r\n1q2nUa9rdSh5tCiPIsJ8GUHPZ26h9lsWidA/WZrOGOiVyTQG9AQgpEVdQw/h\r\nPKdX7XDk9pmnDPC5nUkjOTBtZQ+wxqvPDpwXie65M+Q/C/XIoaV25s8XeNqO\r\nnWwVUc90Dh8bnGfhinxDV36RJZyjMv0tDdFRu3/aj6rRBnMT2u9rEUkmPv0I\r\nuTLwc1ahrCgPNIRHFBSq8CHbD6Tt0NO2u/wyXFquzBrhN2po0ft5x/MhFIWu\r\nrAGjHuP8e0YeCxkoiatU6jgh1IJsTAamkH64fallM7SxcFEOcm7anCIaJPvD\r\nGJDDfm7Rztq/+9bcGq/Pp72uBmjj8pQ49CRiF5EyYNNemQoE3HxBqrhWPcqY\r\n8o7NimqJ8ZK7DcTLJMPy8jZFSN1be2a6Zg4+l676H+tBhQTG7JsK1vI6SN0U\r\nV/6zJhuB1UcxWq2KYs/vyqNA6ORvzb4Jak/BuAm0AoZPEM6QbDsOoNP0WLrG\r\n4sPB1QzoUdmmcnuNeZTRIi5OcayRc7v4tvlAcPoiFQ9PEkhitsmpI23vkxoa\r\nOzedzLYemhuPcShXk7MO0tEa4mjT3aMUAD4=\r\n=YVO7\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.4"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.5": {"name": "@jimp/plugin-invert", "version": "0.16.5", "description": "invert an image.", "dist": {"integrity": "sha512-gEVIXG+AMDtrUyTOkbLPo+jXe4FLf4c4YTL3+LQFx9O0YBnYBZgY71hUqS1WT6E/gNHr7iYmF7RNY77xPjIb/g==", "shasum": "4bda870e9e45d7b58f3295e26ee5435ee7116738", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.5.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCv1gMQFAad/RGYZjC4oFHqgyYVfNgF/pii7w6XXSTxawIgLxqDkgWEbaTUfVoDB0S+tv5X8HKKYqtUrnKBjr82j6c="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3bN/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYphAAh7MqwZfISlJ2xg6DR8xpFRW+1kTugAvyIgE4mhHY1pzEIREq\r\npeWXuGUb7n1qOhVzMasJM1vozCb4sNe8uRIVAYex4LKh/b1+BeZ17g0txnwE\r\nY/3rF6E1Klbo3cXkcVa4s4e4BqeFKDn9/fNWylnc/JVF1KdoUnZoqRhPcVLm\r\np1sBw71Lgppl1a52F/jnw3P9EFm8fpw4lb8v0GgPtqbMYDdgqsPSBa472b7Q\r\n9nK0esFNnK36jKXnsLf9CvPKw6oCDBI7PJ6T4zwqnpYY//dS+9AICv40xd6j\r\ngOmsSpofyZXA9lHwn3MfTCB66wie49m9WFGH4qFIl3lb2T6R8au3BapEgj3X\r\nOJMVgTnSLJHvJT8QodbkB2Jt1ogeqTgnKtXE+ZYFsgZl4Zvg2L2dvrc0ylst\r\nuVfuGM4jqOgKSbRxGmAAj4UwN2LPEEnddH6Vkt0ScJ21imwShXrvGiJtspym\r\nhnf5AIx12X5KO69553fMBBKLjN40LzORlLDJHb0/WotYPr10ct0FAN603w2H\r\nih2sAYJJIoapYrC8GQDtATBw84kIgu1EKJFWWuDygSRT6ifv/UMnO/K6+2an\r\nSbKTVi99r6fQ2dfI5sQ7wKtY1V2DGAEscMp5hGq2JFbRz9E6TyxieiXxL4Jn\r\nzsKFQdeWjMLcMgi8kbfje0aKCIpeF3Fu+qo=\r\n=gVRY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.5"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.6": {"name": "@jimp/plugin-invert", "version": "0.16.6", "description": "invert an image.", "dist": {"integrity": "sha512-Qux4t3dQEjZo/yoYQ0574dvn765IKWyjI42nz6QXXFl0/9j7m+eWnVD9YV52oJCNCO7vwwP8r7ETke42WmWQjA==", "shasum": "61178730977fc259e16b99456d55427c9ee384ca", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.6.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFsThrel+hLG2p5u9uyP1zRa3D+73e6tSUYT1XME/IniAiAbgFNNpxKGiC7Rgtoa8RUSFA/afLS6/Rf+nwBQy9Ju8g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3bRCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtAw//RIj0oMAZRV+X1sRrmontGzjehUWio2gPBnusoVPOOaVSzcHp\r\nNj4my9C9pepf+iu5opbdImii2K/yg21IN+wS6MP6R4jJeKgWeBEfdug8X9x6\r\nnCAPC+8ia0wbx0UpOR9nEx4GI4hkged2+B3+OEv5z7XM+OcmF9BhnpWYJp26\r\ntrY4aTRqot7r6IHsTO9jLKy49UrcqtGlzq+mdKTyRCA/nWFYtOkhqto7qvS1\r\nS0wA9KTnmI3+DIVo+kosz0yX/k+LoteMmDk9q2OzdfUwbmmXFyzf9JcWG9qs\r\n0hPq0wH23m/y6xcuol2RmqwDiKjO2l7OEm6mWCM5/RvvRZRWCqKUH8ohaSTk\r\n1zfvj08W47RMS29+KNaw27wpwDIjnN6Nfx7gMEkdfSrCwbwQdyo/q3oV5nlY\r\niYVd2k2wWSjrSuM3+0JSK5tKn1NMFsq/wqYiGJc0jRv4U5EUKNVgHcYFOfMc\r\n/lnpmqQfjpFirwS2wladHABNMPc+j14Cmj/P/APX86ftCAJTb3ZICA4iXs28\r\nXffjv+ECDAObqiF1tFdPJOVWV4Rp+fD1hCSRHcfoQ8VOGFoR4uOI0WlVpuM2\r\nfwErin/2pCinsMC+Q0v4jlxtFGF7PIuzOwJ6Hk5imQn5ZxlulxSP8j5QEqv6\r\nxfw9LN9twAThD8AHyEdK0t7RSzAfOfuC7jI=\r\n=g0Yy\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.6"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.7": {"name": "@jimp/plugin-invert", "version": "0.16.7", "description": "invert an image.", "dist": {"integrity": "sha512-v174gFMXAfeycthn49iTcpyL3FoqxUBYo5w2ossiWZ7YwdRJ9uS+GDNS88HbwhkLloKAW9bg/+t25hSHL/SRng==", "shasum": "33de1b9bb6a84a18436392c338ba21b37ebadb1e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.7.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDnnvZBr8wU+hDWfIWze/BhNugj4Njy+zf8CndHdnygSAiByjQ1YDjlhcwSmTWvhM9HUKqqoR65vOuibxDIuNW4/sQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3bbKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoscw//camxwlWS3S0VnzGvu2/D2yR+aJcdBXPfiGZVuBvRnnGFmGQs\r\n2zx5SMh3Jh9FIqYodnnkmYVOzY7JS9lZRfHS6/eOaOaMS3uduMEtT75MQg4b\r\nEaOMoMMO40O8jrssFeP08F+OE2+Skmihal9gP0+49ytHnQB0QpQ7znPPbU2h\r\ncryaDIgHqIm+f/vvqOTH7df9n9e5mHNm+BM7Qu8VEZqN1Ej9rD5MaDf7+iG8\r\nshttz9krsK6lp/7KYAsNMoJd98LCxbwtKyuUcwyHkBI9FDNrg07kkvGyMk6j\r\ntfCjyAJaHmvwkJLx1Utak3CLhIJBkpCYNQU0IPINiRLxSXb6GMoRGWX8By4z\r\n43G8VwQ8OLCxPDc+ZmOjumXjFDXv5FtNFUL2g0oXi+breZhx7+z5v2RKJoZM\r\nRpCYGhLzcZ98D7TbvvgaBlh1hKE4jznPwCc/uDN7CbSm/9e+dfE4h5L4zJyo\r\nY8S5VqD8XKFOBsv8UzFcHf/cq6ldQ3jRDUpjnTkGooM0A0OrhvhF6a5tjp1a\r\nV5x4eXdwIK8IdYbwCIJagQtQghNqGKZJP23T0RQZmjznlMLcAuO/c+r/GKEu\r\nsIMgvPZGyhQ3PQ9IUytUO+VcfhvJapfLlCedhNkIXm/3h3Fo/rtWwkw3RJ+z\r\njrhl4xpWwCaiodRbcy2KZjPpz4/fxe+xSeQ=\r\n=9U+3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.8": {"name": "@jimp/plugin-invert", "version": "0.16.8", "description": "invert an image.", "dist": {"integrity": "sha512-shAU1eSWOnBddPlQtiqgiIf90Pye0UAPfJnTSFkdIOd1y73uARK8nQLIhM9k0nK+Ix4jqWDLc+ricQbX9U94Dw==", "shasum": "7b6df77b8543f711b69c018baa2ea89da150382d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.8.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC51YexB/5hRtFV3fog0CohQ7ybbmAm0uKUoiu06aclnAIgDBtMVo42B70cnAkg8nMP5oIuxW3MRbZlrQ/sQxzx5TU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3bjBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJmw//Z2kkXw5LCSP1oiPKD2JRQVyTBkfthqfBttPESkpY0MoFy/mm\r\nCf1ZdWoqU2mb3a+pWL54Tuvdk7c+Kvn1TwUA0eKrcilgK+/6PgPDSXrVq0+Z\r\nb3HXnIVmwVVK+9uIqPsvJWKPxo00+KV45wziXjqJbwMKwVlD+Ju8YRw9cwi7\r\nttfvcEGK1+RNC0lrnjoA4H//d5FU1w3xPhdbQmeOQIE6cQoCvSAQN9K4V4Tj\r\nQrlrSLY3IP/ax+O0lbu00OP6NkDNERx1Bk3jZq7aAP9JVxzLTiclMvi4HnUg\r\nqkDQ0hHXeuXXf9IsMmUy2P9plgMdkg6QHzKkYkFlmIyLy/X+Zg6LFgAfWm4A\r\nV1vdu7w0MZEndviPT/8A5B8BnOto8bC/bAmzVMWZDyHgFpw2pd8ku22xd6r7\r\nGj8ciKHkmELk7uQwZCtDlI8+FQYrECmFDvgkljO1B13YXJinIetWqPWaq9iN\r\njRX4QM7yhLUq6urMCQfHfoMMXX4eAaltPVqkx1QxVVQrcNevRkLSqTeV7Cxa\r\n8m9zZ/281tCOMquzhwX2ai4t6yuSXmAra8pbcWTH6RnS8SV18AzwMDWkhbG6\r\ntJXca6fDx7DwivWGqMbALLXeUdMo5glECh9N/RFN5X5/4mrbrVTzoWAp6ZZn\r\naujmZ2JGmbQq/Kly34UR7LRkGzevrbt84VI=\r\n=em0u\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.8"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.9": {"name": "@jimp/plugin-invert", "version": "0.16.9", "description": "invert an image.", "dist": {"integrity": "sha512-h90sM8ypmi3Y6as5oj8rwvMdJCf8WbjwLuSEIZeNPrH2TqAQDWf9ig/MJqeL2/FZCNn5u0EMkoWW5t7YBHeOFg==", "shasum": "f0f361debc8a8d248c9d61375cf171ea6cf52ef6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.9.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCblZrf9oh73HjzjwHaa1ELhaXqnpI7OK4cLlN6hRkiEQIhAL/BpEb44wjxo73nZ1MoB5XSbvdrEIBxb6FQWVF8oafG"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3cAEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpI5g//ZpEahZwyJHvqsnyAYi/hGEoGdxWo1Nhldfu1tW4qqlXzGrHq\r\ntLGoAhrHpqH7vutRrtxHN2vu7p+8+4lVDXVVBMMf+YnO4y56Ky5qG1Y1yXam\r\nH0N3XT0FpJ21+PiU+rLbWMqTT5moyW8G8xcD9/ffAQThtw3PjfitX5zJUhT6\r\ncbhBF2AvU4/zcsc653ar0o0kyoB0gadl3v5yIs0KXu/kJBC2AxDgVM5oFBgr\r\nFa52IBwe/xDpdAgM0UQMcvGsn9k/5m4TAwFUZVzgjhXfGfkfbvhNQ0Ly3FYN\r\nlp2opkUN/5h1J0+jgO295rCBKS9jFW6cT1gyjbckLHWeWh80zhB0Tkmv4qll\r\no5DKx85lRhpJuJ4SYe+DMYkKicuCx/p/H9D4sHenPGrGASRRQ8G++KHEGpXf\r\nmbNRtTcqbqYAIx3SXRf714/HEXPQgaUp0znbMgp2ohWhbwOWW8w1LjJq+2Lf\r\npfhjCm1e/rv21g+DUUi1UEkNoK+7b4PlHoOwezCGGOLglHX8Pw7xoJqICw8l\r\n7xA7C2vFy554xZQgKuFWlTMPoG+powp4SyIkl7vV695uUhWWJb08E3uMh9Uf\r\n7e1Ln/5Bz976uUD2RcDg1jCkCx4LGB8CAuravIrdTlJYecsSInnOP3v586R1\r\ndvte3wey3sNKrRH0bxpo8gP87cqsja4EADE=\r\n=0y2+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.9"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.10": {"name": "@jimp/plugin-invert", "version": "0.16.10", "description": "invert an image.", "dist": {"integrity": "sha512-ZXOOU2U1JsPH/q9kgyydSHhG30d14YW6M/3XzCTI3sI9HZgl2+da/PNEIRCIiOiB23ap+fNhH6LStnqtmwhtOA==", "shasum": "abaa71e8b1f02cf8e561414d6f66a6ee22955116", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.10.tgz", "fileCount": 8, "unpackedSize": 7419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTHIlJd3MR7ICnzTawB6yUQdx2zfrqUMcVD5tg1+RM0wIgbOJt/sHaLe0NpcvbmXc0aeNQAeg2FaYdxZkAWQsRuEc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3cGWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyZw//VC6tIlkaM+TZUU7DEaD3yVebMer8CogZkWXp3QGrBm+qZF+5\r\nXmzWobKZ+T8PuAJusnqpiW0thpCQ2qjpH1r07laZM+RDJvwpoWo8p1JKpW67\r\nbZkx5i0hQ4p9kD/Yy39ctz8eiOQhYdag1cXlisq00zmsmkx5CBSNsJHrWdeT\r\nv6NT2ZkJJldnPrqUO2tcveejbvBYCVenVyTsz2XjPeezdV4tM9qh2mZLiWWq\r\nkBjpSH5kTkV71YixIGUXaKC2OppcOSjKdZ6bn76FhASMyFmgplBztH+mv7At\r\nA5vBGwHVmkkn80kWsjEvRMMchX3gSiYmuvSr0Agpot5msHLmkoNiTeio01T1\r\n040WECKh0hMWi+3urzEPUtjz0zxVl1MfCJyv5JK6PgZcXGJH/bkgE8/OkIXZ\r\naNBL5Il362BmCg5Zo9r8//A3mnxLaaalCN9WjaFJi3Xsd7UU4B8BPzgimoFS\r\n3ix7hMlc6jBUidM93S+HwIWp8dyMgFVRq+H0eD48iCCb4FPYnySwnghG073M\r\nR5VA6IeOqbnKf1LQ0fIE1pw2yN3z8OWt16pLmZlDmvKQY5iimzWIM9mdJoBU\r\n8ra1HqxB1YQGIz48j6dVSNcq1uaSuiTYleG9thq4D+VW8c5BuAk3jJu0OHd5\r\n1YOpPyjOXqgFF0GlopRVTUj4l5YDgZofp7U=\r\n=YIl3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.10"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.11": {"name": "@jimp/plugin-invert", "version": "0.16.11", "description": "invert an image.", "dist": {"integrity": "sha512-pY+H2HCG7zXcLnzLV2IhDbIFE8jAd4RSmVHZel4XXmbRMFId/NN8U9gguE89P0MTfsaNXu5ujtQwKMxwONzKLQ==", "shasum": "d2073c85573c5a37ee9d0b55902cdeb32f48dc7b", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.11.tgz", "fileCount": 8, "unpackedSize": 7419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDpnzTiXGhdCTDEjDm7lr20GzrOOMTmzd74a+7fEfGPRAiEAqWwkLuRnqPNldo71hDmRdIFoHpQokIjCeDHLhLBxSS4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3cTbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV4A/8Cw9PrGr3l6xpjVT3ROYUTmlk2bXclpEDkbeAKVjGo4+VVXcw\r\nJUVS6NwPQL8A3OzOsgfTAwC95q68/NVAyk4LaiaYsgewSXgItHvy5Ypchgac\r\nVDprmpEpR+1ElnVtqHj6bJVsodI8ueFe7LWnliDwjs41XgsSFAmMjUVTTeV0\r\njzqjTGBEQUnwYi6/LGaxK+o/GJngEhQLgZ4rsFk8oXeiZqoB1ALNO/TbIXVP\r\nbQDy5lwokYjkAr5/pNB5qK+2R9I7J1sQ4P17SkMYMC9kcVrszY9cktrSoFVf\r\n8alQBuzRSJM1y4HzZ5WOmvIBCjfdYGbZHELuAV7uyx4+OwwGdgKwEn+5tufK\r\ncX2BvWWgzEqIfQNSbjXXmZZycypzN2lVdFKQ5sjKLhwL5j55NucStR5FQqEI\r\nujHddPjySg3HiTARkEydNGUpZBba4OmgMiNa/mYy6x2O6LIJnX8IHWSEXqqv\r\nAJj0ysmxxg4+zQZT7KsUSqpD9/fDly9cemFpe81eYkYWiDqoeq2cOwzy/mxt\r\nLD+XO2VUyuuGNeTtafaFcpJPvUKnovGSkTfDmsaUhTMhvVj24O6X03IyoNi1\r\nIKuL+asJocOEA74ypHry/cQLWM2lc/mIU5yzXEAMhqYj3UtoiZKxA7jvE9L1\r\nymdKSJj+MSyLJsEGYcdnvroAV7kg+yk6aOc=\r\n=IpKw\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.11"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.12": {"name": "@jimp/plugin-invert", "version": "0.16.12", "description": "invert an image.", "dist": {"integrity": "sha512-K8wlsDLiDTIMV6LH6ZGDzYTlP3xUGQgnKL2uqWOFH31bbggO/jVm7+K7IpRTdnJp6cbFeDEO+9XhVdAxIGtWRg==", "shasum": "45ca69fcb4037b37e0870d1e275509c7a8df47fe", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.12.tgz", "fileCount": 8, "unpackedSize": 7419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGRkED5lb97qHmZUCqj8R+2/64MFueZne3JRtGfQOykWAiA5RhKgxrHVjdc1ZPKKwk59MSKRf1caCJxqT14Dfmrl3Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3ccEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp00Q/+M79L11X2K4HrWhDj3oImapXx24t11Cmyi079/DfUBLkLnK/C\r\nasWp460PgSmxF/Wh739EK0xUs4J0NBwsnSJWZaO8hhi2KKRzOFd2dfM4UUQV\r\nEUTikd4nXsOP3lrI7guqM32OSbuDTmiTbI0Eu4EcPrzX0D6e1OrfUpGt+j8Y\r\n1Y70TBBInCI3nSpV5VF42ndMVTZW3tzFhrwy+bQhZJFSLPCYH3JKsNbe56Pw\r\nsJUoCWUpzPp5yRcJd2fbDjFJND5S5sO7Wrg6/eLvlN/+Fnzut8TcJeGm3Tf8\r\nRpJssphkvVKCeXNm2Rme+o+2nnP8+T3dNrFoX+j1K29mNrMZh03N0l/RyTUW\r\nyQJVZGRuOp6pKsW0pyeFFRBaXeYQ2zv6QYr+ghlrx8TSnp8Xjoz/2vM8FVfO\r\n/SIa/K3MBHutF5DKO5yOuK+hH0VpP7hzUp+CBcXez38OTUe3Y2aAQ0uD9m2n\r\nzHWA8+IjuYkkQv+byiPReunQqa4nvrxE4++Th7CQPkSg9MQZDbyvX/8JWXe0\r\n+7Vv2XlQ+ENAu6HhQLoCTGLDd/OksRXOUdWLs2E7ZZS3mC0kfz1DkISz6pur\r\nQLQjyXQZwThcYyJ4lTHk9M+Vlb5s7l/l+qAT4LYHGjsqujVge75hImoJ0pWU\r\naaHcIJOFUxRtyLRzt9Dzn8t0kpOqOAbUNXk=\r\n=mpJI\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.12"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.16.13": {"name": "@jimp/plugin-invert", "version": "0.16.13", "description": "invert an image.", "dist": {"integrity": "sha512-xFMrIn7czEZbdbMzZWuaZFnlLGJDVJ82y5vlsKsXRTG2kcxRsMPXvZRWHV57nSs1YFsNqXSbrC8B98n0E32njQ==", "shasum": "7449283d5b0f405ce2cd1b93a6d79169c970e431", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.16.13.tgz", "fileCount": 8, "unpackedSize": 7419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2ZSiJ0dFOk2+DSER+wHDpjM/1jqgjw/bro4fUND0b+AiAltBm5n15930hXfPbLMqe74uXaqn+c2xdam7EStXWYnA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3clcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+zw/+NJJiDn9av+xj6n+3Y6ajiVRhKu5/GfVAzny5q21tku2cfPKB\r\nG08cQqLcHV1fsorxorHfJ9BQpDahSJx/LBPq4w9p/Q8vAZJtSk3aZxnfmABk\r\nHJrBfktarrcAx1YY0TrjNwXs6XvkkvgatSPGcVPdPqiOYyUspvRe5Q3OmAlN\r\ne1pHV0rUVKWvKjTkosrds5Dox3tG8EHik79G/D0abwqZIsGLmwYMp8Pzchv4\r\nbgLlqeivPjsslgqv9La3Lj6BfxaDATdSDRX4O7KQdbT3kDj0G0syVwBVvEih\r\nktizS7EwCImgRis41O321AY6eLArfrlGeQYfKjlYsyhkDvqcap7+LMtxOl5j\r\n2j9nux8nNtzd+mjIdXL0qnUWWyLsEm6+s/BNbb1zh7AlPiNzsRgM4kQiljZg\r\nilqOw9cJg6vm6SHKztfE8q5KHYFJRx5/QaExqP2AtTfusNQ+TlXM8h27klzd\r\ndY9ARCgERda31rqNkdq6chlKQUl96XKMMf9NwlVn4kO048jvANdEcRH2dSmy\r\nrNq/rzWe6vX5mWMrHxHKe1WQLgpH2U06KQlwEb6+lsNWd9x3ZjUx5D1K6S/M\r\n3r7OqFl0AIOQg/DK1PDzlyLm62jAZmSyi6RgbpAzxOl6G3Mhzomdl5w1fVE6\r\nPdjcT/tls42Pe93tUpTWlv2Xo9CcCGRP1U8=\r\n=5E8Q\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.13"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.0--canary.1131.af3cb94.0": {"name": "@jimp/plugin-invert", "version": "0.17.0--canary.1131.af3cb94.0", "description": "invert an image.", "dist": {"integrity": "sha512-WCpafSgw1O5vJNFl6m1Gm0AQEoVuKLsFf/K0m9Mc+Lw0JUK7ZfwreY3kHzgFePJXW3Ma1JyCe02qfy5PU8jU5g==", "shasum": "f3ebbe5083bd51e90becd84883f14e40224edc90", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.0--canary.1131.af3cb94.0.tgz", "fileCount": 8, "unpackedSize": 7226, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4GKgQ9h2h/EZIXHsKK8kfS73H5AImhzsSf9zSXkErzQIgdZJIamG5Ss2+4JdicTHkxsCT08QQY/Kfq+78yyxaoos="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3cqrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRLRAAiigVWODT6XPd1vkeLut4UMsNaVQTvwgOcDB040RVHZUzjxHx\r\nCEc7o1pwuP4qbRvjn4j7iwnvT1WdRpJkojx1v1uw/pO0PzVnZiOAdX8kTAdn\r\nSbQ6qxmcXOXqr7wkgkjf5kvm8fhJ9NJ/klMAuB8Uf9g/4SRXBFaUFfgm80WF\r\nlCpCVc8nJXscXYnCL/0zzX+HahaSbx36GGnuM5vOvWsvxfApW8eK+qNJqYik\r\nYoLghpAf6TAhieVZxi5BAMv7URzNmVx5v0X+VoUhr6AWaMbchCF69KveSNOD\r\nHezxJmEJRUeKV+OneLCXyfWrY6tX3pNQk4PTbYtDBJAx8dzCITOc/h9IL5H8\r\nOAV5uqrrzxRJ273buLq219gLHYHIzzhJwHAqXZpcXhua4x0fltvp8LSQOD6Q\r\n7XPy6p44Zyx0lZ0UmovQCVVAeICp9bU7jj/cqRBjqikusA6YfANYrE3vQYNo\r\nw54S/BcJoOfkPfnUIqf8Pf4iNtin9TYFK6npqrcj9sr9+NdNArjkrhi7bCJp\r\nzM4H4teattagBjVxHxjN9J67eFYO8/bkqeow2DpQlt7yby6pxnHoQQtwjb2D\r\nijocwIYzSD8kgGEML8I3nosmd2u/qbSp3Cb4Llad/2/EzXUC12qnsW6tct/l\r\n3M7BjTdqH6ibXEWqGUQlSZjl506pyo62Kdw=\r\n=Vul1\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.0--canary.1131.af3cb94.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.0": {"name": "@jimp/plugin-invert", "version": "0.17.0", "description": "invert an image.", "dist": {"integrity": "sha512-HHEBkUYX4PS2fzKa1Z/dZm+/cRrm1t4IKLZxhqK6C9S3pGky8WYmH2uLb/cBmzZQwbcTAWbh6kVF6ijI+eykIw==", "shasum": "8a41609628235ecc3ba393fecc758b1ca6c030f2", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.0.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1RPBg51Rp6jfIgZ6TfY/DMhDpQSQx3xJNXp/UuSOA7AIhAOjk4r3T5WCbTYkljl/iqvug0DK2TELW4d/Umyf3QX7R"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3cwPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpakw//cdhy5XHiHF2FCMLCnETpOzxmpEuAEYMIbz8Ye5cdHhZlEpef\r\ncr168t7+1lKmHtyrktQaj1MMX5hviV0SV7avyARgfSEI33X0bUV2HcgEfI+U\r\nj0Hm23IvGV+YOjgAVxTQeIg1fSaKWvDrKq/Ve5s5XlMUcyABKDF8mEWlPvzo\r\nZ/zNVxcVciTWQDKsLgwolwFhI+9PwrdmTQJj3TEhm/wKTLQBDUQc+IKs6kNl\r\neCh/hbIwUR8nPQtbq93iZB4gdj20yYVKY7mT4U8OSN66MhiaagO9GhOqutgi\r\nW/aownbK/83/qjERuJ6icSGM+QyfmCsntNHGvwag7YcfjLfemCYdslTAxb6e\r\nrDexTxbZ8mRIkWEmh/XsXG3WLeK91Fsm+VEyzc6wDjr1NijnKOJ7Tqf4Axn9\r\nk6mn2Ecd0K0CAYxy3umA63Z/7dKZKadwx7WCOjno2aB34btFig2W0UyO0nle\r\nwAXrSATMdZzbKCX//YIKGxDoGrVdAp6DkY6RjepQCwoPYtWzHpXVI1rSDb2d\r\nfCmaGAo2EIu0nIc9A7vMOlQz6GN6V2qg3E/3zuFcR9DtIKh8qYw1EOHPqZvm\r\nvSQwSfk0q9ezBkVWV8pzhqv/PWVfhDOqj8/LyAnlPW3l1M487z9ocUHksNhM\r\n92jjExcsNge0ZQcxQSt12x2xVm04Y/2bVFA=\r\n=D8TB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.18.0--canary.1133.54bf269.0": {"name": "@jimp/plugin-invert", "version": "0.18.0--canary.1133.54bf269.0", "description": "invert an image.", "dist": {"integrity": "sha512-/rqQaULJbCxy1QIF/FKa0j4E0K9StLURGwBMCsDVi5bHFS1OYcFlgbgAs7tJYXqaz/Sfk73fDu2ePURSKAloOg==", "shasum": "9e26d53f10a8249a70c2ef0ed59b2e734c86747c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.18.0--canary.1133.54bf269.0.tgz", "fileCount": 8, "unpackedSize": 7462, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnJZp9ZNRetOesTuWz1klEaWJ3f32BfBu2rXG9oE2UMAIhAIBKNGDxszf0C9MyRapfuEqggy1DVfDs7/hInrz07DGt"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eCyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjcxAAguQKnfjJ/AtppqE2HTNWANLJtEHrRFe+acXzU/lo4yjVUiCO\r\n98DA2IFNGGd2QCfJroP1zleRWokcGS2K372KYMOxN9+a2wb7KRi26+sSNcfA\r\nwJGYr5lH0n+nIU9vLAZcJhSRDVLE3vSyrw71v8XcAmV/P6qwAMjPU8+bMRtm\r\nEkh2pgfoi2xcbxi+gbEdrQkKChUig7hqPvmsrACkqmc0JOF5Yaac435Abkbs\r\nUvb2hmeCJAeJ0zYiMVOy6e84oBSVKqK0hEB74vYeVklZ0D0rSDzZRI8bzFGB\r\nMTY6VjKfOs4G4JqUMobgilkXysuwvLBm9hil9de2yJyEglMPdu6aBa0pEabQ\r\nbOxuwj7hJgwRt+ZzBWAgNrddGRKaYW4ADJTz2t4TW+TKajMvFfMUdlym25pn\r\n0g9NP31BG3NB4Eg1kT3/p4YfaAA097fXzEiK/s5H7j+DsZHiNH8uekSrT1j5\r\nf8ZkGcgsusOPnxixMOz7No+kYsgW3SsV5K5B4DtL9qCut6ke2uNnZyflXGK2\r\n35VhUL0DaPVPNwl+fyeVhKHsRf0dvZCmOAJ4OVoiN8SOD7AVudI7yPIwHFwP\r\n72ZKv2Z72fJEM/39fV9Nh9yMpWyRkprITfaIZ9MjloKHCXVNWGpMVgZzpoZX\r\nxjkqOX9+8g4X11wdzv8DL0Qud1hTUSHjT9Y=\r\n=PPR+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.18.0--canary.1133.54bf269.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.1--canary.e22c14a.0": {"name": "@jimp/plugin-invert", "version": "0.17.1--canary.e22c14a.0", "description": "invert an image.", "dist": {"integrity": "sha512-ZnhjH3Fa2ZvfkJx8dqG1oG2zWDP/6wRv3OwTG1NcuH9O7RtfgN/0WGdtSPBRLHU0qCCTtbBb9LjEM1F4qF9+gQ==", "shasum": "f9814a30221c60f9e5801947eaa2ddc02e30fd6b", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.1--canary.e22c14a.0.tgz", "fileCount": 8, "unpackedSize": 7452, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7hbjVqtj43hcLELLsUBknXhUrbLKpCIVrYt3vB8cpbAiEAgoGt8TsKqgYY+2Y4bp5NNtsoXBoLuaMuuNINL84RZc4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eFbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpq+g/+NQTfDn7NnjxYUVAB90a4AyK7GFW5+GbXqzDoXyJiaz85H2PN\r\n3GSRaWeshdSwT0Ty73LW3GpWHHLvW+N7G6VOa0tJtvZ7UJAFTOx19XBWCcDK\r\nTDcO7Cn7kRpZ+jQbENWOLilHuwX9Jahko3K2ADcEVJHRz4uAfS3j5j79//Oa\r\n7yQlNsSRxJx490mN44G0XoHNcDztnrKNpX3N+jq2PcFUOAPl6sULTzvAqEeE\r\nKVP8nXf9uOONUpibHD6CD3LYcS4d4QVtTCXNW4W/X3D5+o5/9rzghfPzjnNn\r\nirfJQQ432qpwLRI/ATti6JcfouWiX2iZjlbghKsFDB9LWax01+V18JEMK8My\r\noZXkvUebHZ7aYVxggmRpaiXEFBBm+f6zdx+Svr/xpzmXHoUtWnTm3neiE9Ua\r\nZVZSQ4jzwdjcc1VxMYjsVlLZIdnMgTritEnqCdpURZmBnL2D+Cz2eXMBIaPk\r\nLokR/FaSxNxpWKf2/qeDHDw07DZ736XPrimWiT64MJkMi1ON6jpX4T9+YBA1\r\nVHVOnf/cDvsoN45zGy18pT+yTv2731XcGf8D8XH/S0lNSbXTKFtNmlFQ7wr9\r\n4y8gk0Q2atDx+mH0gUFIEdHUh/zs3tSHONjD27R1Pyy1j6A4nJJe9OzjZiuO\r\nf+rqjVgNkLBm03KlZkXBsGFstvkl3Cy2UUo=\r\n=L3rN\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.1--canary.e22c14a.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.1--canary.1134.e007a48.0": {"name": "@jimp/plugin-invert", "version": "0.17.1--canary.1134.e007a48.0", "description": "invert an image.", "dist": {"integrity": "sha512-p+sz/BO6jIls+E6tX3yEfj6eXSTADWtt4D4t6RX0tvchoreIc3nkPdJOJ9481EMx1BL0LYeTnkAga1cDSp6vZQ==", "shasum": "84ff538c937ad426b2f898ec25e78d217fb81b3d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.1--canary.1134.e007a48.0.tgz", "fileCount": 8, "unpackedSize": 7462, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXDYiIiyGNnfTdtvwt/E3F6KyRXalA7FqLCpShASF1wAIgbnaPRiOG+RtTW87RKSqZGzGWL0eo/Z7nFQQ1mtSoiIs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eGqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3/hAAj7B4MV+IYx5TXzfWWfYxP85MCR8Ol3YptnRzta9+Fz7kHljA\r\n5hLLF2FKnyLLSCCKbu+w4+u+TGzgLRIpP+6nmvC0lEzLnjYjjmnG7i1LIq33\r\nN/0+zcuSF6Y3UQC9arV6MewCZJhBteQehj8fP7BX28x5hre6Evg1IkICwMk8\r\n9rZ7cqvyk970ZAeiZnIp3RGuAlMU4TITIOWO9oHLEl2Bn0b7tkr+Ir7wOaLD\r\noCWnGDkxqqrvIcAP5q+w5j5gwG+Yx2Q54hksXxg36QbFeqFArbwQ3A9Ll9zr\r\nw9J3HeHgl5YgIo7LvJrSdTwjlKjyDKHxdZQSUEk3S2oNEb0esmPYthVKFoin\r\nrmUSV8/9mkBdRlP3WqhbYJcPCHNwhfDa4LyaPQQU97vMQtDtJAmCQVmoRBdn\r\nnR2C6Nm4mMg0bXFiAtB6Ru8VA8j96VG1zf6+AInZ2tP0T2WAnezPWahTzDc2\r\nbAqp4Of8OCCf56w6lECHCbgGRfBiC/IwUmi+fVP8ciUbEGpPbuOlCMcbosG7\r\nd9Nm+wkmw3OD7HGMX7y1disI3VOXnS0YX1AKzKuQtS5OQ8Z5MXymuWipMXPx\r\nZOq7pAiSxxYjEwAdPASs0adT0t1ndSaLE8Uf1y0dzBry5DO8kZLwUzszmucy\r\nrQ8qnAqZJcaVK0j1CfaSwRA0gcLSnm59bV0=\r\n=uF1e\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.1--canary.1134.e007a48.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.1": {"name": "@jimp/plugin-invert", "version": "0.17.1", "description": "invert an image.", "dist": {"integrity": "sha512-2rCTi/naS4QBWsFfUk0/QAwOMWINOVXd/yoVx9c2UOXQ84tm1nMraGfVuPnps0dVBQGr89kXH8Zewyu+Pc6NIQ==", "shasum": "c73f44ce88f4f8091b4fb9c56cc5d83215be1284", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.1.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgAh9lHPhvXU4sAISh0WYP5XFL5OfFIpMN9GR/qaRD4wIhAPwviAGOdVeZeEeojD1Q0VaRYxbiv+3u9z6jLzCvas5s"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eG9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPcw//U7oU1PuicfQNIzaIz9tTNA/0U2oQS1vYWCxX480PWOSnjFeX\r\nSK6gA0tJ/wwqQQKywkaqjNN+XNcYkPtBoypuidI0D8kPniWzU8VPZlnpnl5g\r\nllZZwxiHcCNT+sXHvGH0z7QElrnXp29YjdAsS83o5hJQnlcLSpTHUprN0eUD\r\nV6NPtistxOePST3MdDns0ZYdOlZkmSMmir2EMpEcOFbi+IVvVeEXbSxv3TSO\r\nYGchUai7x7qafHNLPfulN3DaTvQ9G7V3wkF9dae8Bh5lWieaWEhX+u3MS6v7\r\nCcuNqCt0Bs5joWmuVR5neAjYfZytOkqPJZ3vYrq74fivKjTjkl4YqjqnjTMt\r\n8dhR7h7smbkSP/BDF+JvrhyC4gxP1rSL8GCaeTvK2tfgOdYlldYXrAB799+S\r\nxFN6RNA/+UYURGzDGmgXzEXkP3SmfCQ/loT+PO3+hFLEmkLz9biKWW6hHcor\r\nyw4X/2S0RcN2OUyf1z5BPBtIq0ZAn4m1Ufenai+/cSB8tTEzk6K+vvKC2oX0\r\nHngEK11Mv4LM2+LBdD3L9I5MomPN8EzYjnlYUGykDBJarmvyEEO14ectsGvZ\r\nqyjS3+cuL3G3ahMweSXmiJ03hgXi7dHtJxLHTPBQa0BzJIl675C4kIcvZZpv\r\nowsLeVU45TRWTWOXVy+qZIuaXPrOjZDjFx0=\r\n=EO0m\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.18.0--canary.1135.911ed04.0": {"name": "@jimp/plugin-invert", "version": "0.18.0--canary.1135.911ed04.0", "description": "invert an image.", "dist": {"integrity": "sha512-YLoHMJIG3w3asF+c0vnhFlpMw3p47w2xnk/v4ay8jUxYBUHJDakq9D8WpCjwpPyM/mX9Jej7Bc5HmGBvG+obKw==", "shasum": "5aeead44345f3e77769b758e932fdda1716524f2", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.18.0--canary.1135.911ed04.0.tgz", "fileCount": 8, "unpackedSize": 7462, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIACeioyuLONxZVga/dlJQ5mb1Q9JRZUDx6jBX6/uMj+7AiBHKry6pqfQIBc0r9EZD/SiQMzl7RTt4KYJq33khx8jXg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eMvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmZQ//TG9VUDbp0vZoY37MSWJz5rCk278/MU6uw+bXF6DlsVRp2wb3\r\nSvo+Ohf7Qu5JjeFQFZmn1EERYAUosL0ycbHs+EGmm8MqOnNwQllB7PPV3N3d\r\ntVWwDU0QYbS+vlwr4jA+YfGJZws59MLXTrnHjkmdANaj/2Jf+r4TYA9wd8mm\r\nhnrB4Fwikxsu8ZXb16yfuEqihtkwAnCpnfThOWFwBHeyopxD853qtpdl9l4N\r\nw42gMn0tlbGebslpTVaqoIaLWrcajMcY653mR+eMl/FIMuSG9oiqsNsPEDFw\r\nsLbvq8JNN5FYxl6Hb9oP+hzYK0EBaJhSZ3sgJzo44/2jZSSrEWds7JuAMr8K\r\nSUGk5/B1IqT19lHT/pAA6LQxqPQnNjqYQ5QHIeCqj5gAER2B4IVMjWGwLB4+\r\nGYylcuCAxh+aLuUjcmEAVOPOkhMyigQoWupuSNMsVSCHV/fMgF8vNSvoDRo+\r\ntibUw2t0qnUxikaXv0tEFHio3fE7/GW6nYmjXL+EoaFHm7UemQvGBp12R5dg\r\n1LG8atKXbG3mp1qeYjAxXvw8F7Zjr4s3npvbwWmw798TjENX7VOsHZ1Sv+yU\r\nq3hNXmhuR5sb8pwBnodP7qK1LumYFgogGNYyvR7nVQ9SRlKrWUFt/Ng/eBM6\r\nZVqQl98tg9Lys71hN5l4scgnmLnY22eWIIk=\r\n=IWyW\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.18.0--canary.1135.911ed04.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.2": {"name": "@jimp/plugin-invert", "version": "0.17.2", "description": "invert an image.", "dist": {"integrity": "sha512-74fFzMvfPWES+sMBJgyWhaKNg7fO/ruPP3xzmakK7tduh50fGiR3sabd05rcGDK3d4u0c8mm1xFXWzjK/vuk9g==", "shasum": "241b18e917c2fbab31f1db3fdcd9493529250906", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.2.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2WMNpHB7DIoFKdNki4QXtHCmvpF99oC3CV5IrRXs9iwIgOqHJG+t/rXe5rNquLTHySq4BVQKa0SgvwUBCGOz6Vv4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eN/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtzQ//VS6Q0pqFAS7872dc/93/U67DGz1Em0Mxi8K1/Rte0RRjEV6e\r\nfbVSQ+jRQ1VH3ZOvDvQordvC0UWlBhoHMtbMvWUWPKgawvF2Og1ZeowlfU91\r\n8wyMZg1EoEGf8HX/gasV9uEJ+QuLHg5LAyw7mBmrivIjB45tm8KICQBVoHPr\r\nAgFJHbLgMZEO+UB2ZtGT4nM7YbL2exXXNscGU3tcfKczAIK0GFZ+tg5c+AqQ\r\ngS0oApLWXkTMd5Bw+9+1WE/d4lEJQYpTMwZkp62QgBcMDDx8xVGUMWZKBVv1\r\nRm0xh9l+bIBiM0/uHkMLlP0LUcq4BMciAdfOKILjXfnMr35IleJdQ3ixs5Nu\r\nxZDNsxc7w9dwFI7XLjWmyGkdeasA+TWUBtrsjPj21ILsHOaRmmUd78cJyBKJ\r\nXsWcsoYNlXFmNyxO3RTHa3aQBdX0JxQoUi/YsT+qS2UDZ4Y0LaOlnCdT1GTy\r\n5z9wp94UzF5MXR+a1SAygoT3/Cm7JXqtQDDrBaRvQ1HmwIIDmiP2YwijQQjP\r\n44zKZFO5XqhHMLQIom6e16bKgiD7JZ5KJAxz4jdJE1gkF/vWJ3zIKZUZxZTu\r\nTmaGtgKgDoy+SE8q8QDV2r13YNMj5Sc25LOWOjhvKtBPiixYip9HBkjZ+QUg\r\n25XUvd1a2eKfZg4Dld9yT8XGi3yz8pqsdtc=\r\n=7Bid\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.2"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.3--canary.1136.7f5f5d8.0": {"name": "@jimp/plugin-invert", "version": "0.17.3--canary.1136.7f5f5d8.0", "description": "invert an image.", "dist": {"integrity": "sha512-a6SKPYsG7ANmNnrskgLQoySETb3q7i7NdTL5FJH7QGjOjuqGemNd7RIXzy0YU18hfcImbe80uFjWeed35K9/Hg==", "shasum": "d996eebbe94e836735c2eacb5fc3f4af5e3540b8", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.3--canary.1136.7f5f5d8.0.tgz", "fileCount": 8, "unpackedSize": 7462, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICMtQJF821wyCZQhkONHTq8l9OVzHBqUWWg+tGPh4IJoAiEA+IOlXuU7h2P4tY0JL7WWoRvkn/kjuPaxwUHs6O5/AaM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eVTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV2g//Ue/BRaMiKPDqxhRcGsZM0JPaV5v9r2cEQ37xwxTnmK21YN/W\r\nCaWUU+jNQ1m+whYjaNsXHNukH7/zxME3z+M6IfmMIGusZqy06GOPAjfyqoFb\r\nRKKOw0NwIMGjQkzWF3c10tYw31I8nQLsIDRW4uxGq0VSS8NWfC4Igm3J9OrV\r\nacKPFRtDldyGKtZW/I+rUtFjZp8eAybmMMUAB2iakGzYTIFj08JbH9VR334c\r\nNK5mKtlxUtDeJEdrVXVU935qDH/iO3T9uQEl2ePZwci7ozW6tF3TmRFEKY6g\r\ns335IupwqA2/Hz4B0lUPvnXXxREYxLxEpI+JgOm02fVAtGBOJa4U0WQLkRHE\r\nm0/KLpATOWHucDgULcYZfmsD510B7EV36erQv0KMdG3PKkNXEe0d19gzvTMd\r\n/CdzrhFFmavfgPazrikOwgOze/0lqgzF1eH4j0a2vPZ+6dFVp3v+LqCWicIa\r\n8MswNLuFYoq+aXfIa/Vw/5Ji0IjGOFntxv2Yqbq1NnTKkRrL2AiSCJ/mmmyh\r\nFmBYLvsskfc9ZWvq8/vtK7QbXnXl+k6Btabu0z9aSHKLVJ9kWQ026ijJso1b\r\nXdy1w42xxmlYRvsRQkYLBsNmi+YdaWz+xwEKHbmUUMhBoy1LmXS9orvEc0Q+\r\nFH2P4ooiwANNLAVxTEgmAXI60lOAHqcsZbM=\r\n=uuZN\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.3--canary.1136.7f5f5d8.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.3": {"name": "@jimp/plugin-invert", "version": "0.17.3", "description": "invert an image.", "dist": {"integrity": "sha512-4Z57fU0Gx1mQ7py+MAsH7UBs0hpCt94+2ivUlQ3E+7GgqEcCowiL0bEcVA0Vxy8G/KEv2XQqQ36GOe+aegrfmQ==", "shasum": "431dc7cc62fe7b0b4e23730541c48b5b11bb0323", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.3.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRk2ueI+5Cv5GfAmd+3LcTQTw5jNBMG7buXGOhdkpxEwIhAPmERE0exdYidVhE11utJWuHK8X9r/NkRvlOQ1TZXIPm"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eXNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPyA/8CdzbnEY0fSF9fzLzsdE4zkT9DW/yqT7wsJHqK7ltcNCyP2BE\r\nf44qsEoQBipUOlV7sg29iMgaM9RaKpLXuqap3Z/v9rEGfgNUqDp0Mh1Pe2Mz\r\nxvYzeBlEWYh810B+y/G34sMfGkGkDbkWzl6+YWsg3SJhQZEVsBM1pLu6h0+L\r\n2aCgTqlaA1c3GNePu2Er0K7aZkQfnpwI1/dKNxFqU7pUIifRvCpnotgIZ7P5\r\nh2J3F5ITM7hn46s7xd1tin9FOy+wObsg6TyzZm16Zte6u7UAU7CBhqCbrlFG\r\nw4V9JqKHK6VDxDgXXarC1Slllzrt2Iz04YYUo4A58BMMRtQZjHR9Gdv7LCvX\r\n35bLm7wFvns1oHQGPjXRRqiwc5g0njJncfFs2OJT1NnKsjlXOZSgeasgulzq\r\n3zuiiKTYwqtf+1SUVkRdH1kG9nVwGMUo+dQ3uwUO8QXCpRe75PObIPIpjSar\r\no+ExKSQxGDMhuVyBOF+4saNVuUzfOy9Q1cIXOTqm4xpPl/s8mbL3GbLgEr1W\r\na63Nsp8tguuErLKD3HGbwGUZnb9iCdFPFywcMs4Em30odrtlUDCTKDkkerGX\r\nlyNkCS69uDeGx4av82w4vn8rpc4/+ifkvwaL76lqEXpz0J9JxFDMJtIx0HX2\r\nG7595k2d0N83GaOt93TdMdJWEnZVCqsSdtQ=\r\n=Xdpq\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.3"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.4": {"name": "@jimp/plugin-invert", "version": "0.17.4", "description": "invert an image.", "dist": {"integrity": "sha512-ag6FnSD+uOk1CQ9uAYQHlKi6fAZUAvOsCpaFVJN9y02ZDN6rOPtFvf+yaZvJMbVOPtJTzMdrhDc0EfxIgak0Kg==", "shasum": "4980a1eab56a9f8713f84b7269f412c5bc400867", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.4.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBSacYacN4GtCUv7UpJdNMOsBs0iV4qEmsslzHlz0GQ9AiEAm+fZCeYgeqgmwQROxfV0rH0w44DFPj8o1EpQy/wrgh8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3ebpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgyhAAmFKEnheg7nNvDGhb9cN4aKqQ767sYASuKxPykHo9PtBl0VXS\r\nEZS9Mx2KsO0LtjL87nxUjiWa/Nzb2090yVMAK335gHSW3xqSRqj8gsklB9LM\r\noZJaV+cmNSe426Dxe+4qZNMAnNBp7Y0wvHijYgbaY46m35dbr7ghgEzr0yd8\r\n9r8dkxowD/yXboqC+VM3y15FilfKF/0HzkrCiOepfk0G4bqUR2NUBKL6Ui4z\r\nMwyio3AXKCQZ1XR9VsWNxCxBVY1exfbBgw4dH6rGzYQwNKnoTf5ZsA9g8QKW\r\ncnRuv8noXABcBH58co2CEcDFZRjvyGQ/SnFCpZg3Ds03Yd9j7jqAwh77J64T\r\n35LCJHhsWFQlB57/pSNFPkgxNTGPoOD9PsKg+f3fl3ArjMxvwqBFux6V2uR2\r\n/Vg2B/hV/GFNl44NN8WeB7t0ZCBtX9PZyVM+4kevBsT2Tz0HAlws5eeny/M6\r\nE+5fM/RorcUqoyLW71wF4Ajv2LUTb8u/A9N8Med7f50rUwHyFy91yCgh8vJn\r\nfn17fxbNFQNp6Fj7mJnblvnrnFkTicXBikThaXl8dV+7/MsiePPBYtRg9BtM\r\nsYbjO/lI4jVJwV/N6wWcXI7iiAaKjx3QGlAFf50b6bK6sH6kKTxN6lhugdpn\r\nRCtUIiXcwxZFOlhjfieY02dLYGyCw3oH3dU=\r\n=nXTi\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.4"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.5": {"name": "@jimp/plugin-invert", "version": "0.17.5", "description": "invert an image.", "dist": {"integrity": "sha512-n/Ij2YZk46a6txY2B6p4GwRSdiajuhjPnUwi3FJVa4xS8/pKTfHiGHiZRaVzVe9cwx6OHPjjKce2nrYbMwtyUA==", "shasum": "d9043e059c8c2e2e0d0c4313f62f02f112d287dd", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.5.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDb5O+iQoKffU/dfaIyq5x9lWN+17kMSNe0RnC0ZVI5nQIhAMr9bQ8uT63QA7Unxd12fKuwbBCg9+oXy5bKW7WH2+Zh"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eqzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr92g/+MUCHHYIpHicgw5rqbjMCZ/mr9/5nCqqpIAZQPvdAdoxE/AMp\r\noOF/CpbOyhQOUGV9LSqccYgJxjuYPh+AiUr0kJeeGcWm5xU/yuSNownd4fvP\r\nPzbBU9RXTwhnIZYs8WNo4x0siEd8eLowvSjGL8QESH464E+YUJ/q8ujLr/j9\r\nC4fnkdmfCqT7kCaY3/6j0CJXgUrO8oxWMla2TjcdzDzQZfui2zuMjZ649+Th\r\n6+Kq2+C9D3f4a1W4VK5yVAtkcbn9eqngkNL8q7rFUrAXbeKfLi7GOsgwJ/Fy\r\nkV2PYTQ9GUGPdQ1GJx3EElbKlS5ejoCu8KbUTUPYmmA4vImrl0Gfz1vrBMCs\r\ng7VeDWG1KI8zqkuDXjJWRsPSwfDYxY7gCEcEICmUHL6nDRaH93N6lkBfTyMx\r\noMeUPxhIgauuRRSny/PEwN5WzC4MsjUWQVBh7vAQkNRqke334a7Z+cJeVa+R\r\ntUO4+P0t8FM6sG3tfkknrQN5WvVbwKdlv1Da1w8+DuygIk58l2qBGZyuRhjt\r\n1x5zpzDUIPxLuKXhgZXEzsgXZXj+Ld1EhvVSkqGrO1bGDB8aluZmv+lQJqqW\r\nzOoVOyCuqW8+ZKNSGxBVglBBKw0VBqxQJs9770thB9xeBpnItPe8QeYnvmtT\r\nsUIunITtuixTqHWD4s/X3GfS1z6ohVruxmE=\r\n=jGJW\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.5"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.6--canary.1cb89cf.0": {"name": "@jimp/plugin-invert", "version": "0.17.6--canary.1cb89cf.0", "description": "invert an image.", "dist": {"integrity": "sha512-ylVaeF3CfHTnaX2l7hXLa265Fc9h6Z/iUC5CMpSFJ/TQaU5FwHHrHncjMscEFQ5KlG4+OvK+ciJKtO02q9Zc2A==", "shasum": "585b85bd9f7563ab779da0c57bb3a26503f6d5c4", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.6--canary.1cb89cf.0.tgz", "fileCount": 8, "unpackedSize": 7485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID6YI8iU6TFm9L97G5BB6bu7XLzy903N8NbUgm9Z51ifAiEAqKXqEBTMT2Fp6KPZr19FfwtXKuRut0HFF93H7mARafU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3ewKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos1Q/+IRbriS27iDtRubyJMp+vygPjjo+lSoxcREHPeGRZVd9g9Gs+\r\ntiNqedmuQDsWMB5fqFsnqIqcHrY845MpkFWmgmL+af4JPg3IbWQOhyIQg/PK\r\ntw2KYDERxhiEn2KatVeyZH6QkPqMPIsDnVKuNnw2s1qQ7+ziuph5XRPJlsOp\r\nbvLEK2TrymJ7bo4xuWcn88SmIpxNlH9euIek4frevj2G9XgD93faXws1+ual\r\n8cRGPta4ZhoXaQybwdHp7hmEM8K1DLLVuunxaT/0RZuAwBpuuoL4F2+atUqf\r\nlMfOrRVAqvoGKKPK+KALnmN5/A7Ur2hsCcJVQDYwTot/wPv68YYtPCjq7uLw\r\nDvPXdrE6i50YC1CSX8CWWeqxC+IabyECwKxrfxlzoxLU7KyEURdXDLmX64+P\r\nclgh/talh0jAmQctdz99kfy31MPhv++tfEEXQ+eZDtIbXrivWkmtFxbSJjUq\r\ne9/yJ3J5M2JAuG5dWdmYAsbtCr94Ig0Qyk6EyqWQ3EBffR+ikMrWpy+OMqCE\r\ni4yBVPayoE86rkxdi/b58WVgfLyS1D8Zu+TrpLd0N8yMHmaFTlNMHtaonf9H\r\nPjGebKn0HiI7cJucVx6u4hNHoTjXLUFSuUGerov1B4Y60ICkZwIVaC3FVjNL\r\nc5RyLM0rOY6PmyDf1D2qD9b/qeIqzW7vn70=\r\n=HbtY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.6--canary.1cb89cf.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.6--canary.cd893f3.0": {"name": "@jimp/plugin-invert", "version": "0.17.6--canary.cd893f3.0", "description": "invert an image.", "dist": {"integrity": "sha512-XhBxyAR8HqNDYRFDOaLiCVvksph8ed4h66pR/ii7QbvYKDuDKSi32HdEao4oYS2u+3g4/R/DOOkM6ayTuSs2/A==", "shasum": "96be99d1d9c46a17410248a14cba71263a61fde0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.6--canary.cd893f3.0.tgz", "fileCount": 8, "unpackedSize": 7485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtI5H9eYPSIvorclXBA+dEqEt/RGTcor7UYioaYnJypwIhAMZ62WfXilTJtE9zu5/kEU8SNVg96ZJnNgyuTHJQHd3J"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3eyHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmot8g/5AZ6FeNd6yi8DGpuRiSpOjgyp2elVxR0TWcaAJ6HgKWbCZ1mg\r\nmDz4F+JRjgfNiHAAxSEHl2LR4KRYW86FWeRFMqdDBT715PR3w9D9foH9AA9w\r\nef4FJ295V1DM1RN0diVacbFtghoazQBUm2Gasy78BZqM4qecDK0xKxDzHNBX\r\nv4bNYlpayiRiRkfBqvaR7mpGxWIEgeYCfKL9nkIf3kQP81EdjAHcCkJsSsWU\r\nQBKUdlklu1c+thzIfqAQt9qIhkfg5SAoZHqFcRVQvwq50yVRxnDrwQBl4Ycg\r\nLB88wAVuHQ7W7+ktvxpLzngpw5KAV5cq3rI7qaTciewSXNQ1wcP8v05Q7IPM\r\nKzm3bmcvYkzkOyIen15VZXxdHna6HG1zaCz+TvKz/IRBRM6FfaGb4zpX8qS0\r\nj/+HMmwBwKaSRUh4UfA4+fjU0lzdpoz22TRh9iMVp2292iUBvKj858kdMT5s\r\nvuMLxbCpbvX/BqzIuTFyDbbVt69RcUCeImwGzN4RthMstb5mPAhQMKwwujyn\r\nJWzvo87EynrpmWC4dflFiRTtceIFZOD3Rqpf+BbA/kJHkVY8og4CRpWFEOKE\r\nPKGbBOdLzFQnjmfEPSWvOB9t/fWekw+p62Ot3DFCvsouVbPUduy04RpIzhNW\r\nC8w29n5DFYyyKXz5gNeW0T/WRpmg9IMhZZQ=\r\n=2v/3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.6--canary.cd893f3.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.6--canary.1137.5e459dc.0": {"name": "@jimp/plugin-invert", "version": "0.17.6--canary.1137.5e459dc.0", "description": "invert an image.", "dist": {"integrity": "sha512-<PERSON>rLcZZopEJC4Z+FLgmTPRVLll5iZRstjdvWRBqi7yAhB4j1b5RNPF1EEI6jQXHSHcWdAFXfSo/rlCzsBg5qwRw==", "shasum": "ee5d03f5ae2106fd97345ec13861e2c3edbffe48", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.6--canary.1137.5e459dc.0.tgz", "fileCount": 8, "unpackedSize": 7495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDTmlPbSXBLvPw+wXy/UOIc8HgwQl6gPVh5iSYAhmIRnAiEAixYi1eQthArs7XikNYix6e1ZKheXLYdaOqMQ1xPralU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3ezVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCHA//em9WUWwA2NGnf5sO6UQin51uMn18o3ikE6tzPTaXgSO3PqkO\r\nzw96HJVz7kT3vugkKPzeMnnNbLwEtO7v6yZPcXYHTVPfqHJTedD6Tb++eCRx\r\nzipM0/rgPGZ7taxQGGe85Be2sje05w17/Z1lXRPWMk/ryvvfTIejNNyHzdK3\r\nJpevwWAjqBFM2ydE2EkFLAi0bARF0wDZ8sah0pMrDMfCoLCTaD1amTdbCUpW\r\nKWH++ti1Z/7y7aB8ebAFYk/99/o/o6/Aymd4nUA28EChAUNpYplRLQEaZurv\r\nhf2FV4Ild0fYf25hq2GLutmdOV8bu3mgzscB4NK393/GfNKJCHmoIOpHLF7R\r\nMqDSfKHeFWvQk5FiOKy0uODQmk9zB3EXJ+PcCCIviTbn0RS+h6A7jqPTy2hP\r\nwVeEAVNMRDnHaSLZgb7joB5o+U1evYWl2sAPlRKF6xd/uZBWXv7qo24f+VB3\r\nB8I+nNHoxkzL711z1Y7BtF4QkhatXofaSV6rPrybekeZC3oCvTCli/udnlii\r\n4guANlqv5h4anhWYzX1qMHWhHXw6K3NSRpY849VhvsGicVr3zNtkxWan3H3o\r\nGY8V4cEogaJJZpsaIpUVEjQmWb1/SCNIHdS0TpoTP4aJnU/feJDoyBTZYuns\r\nKW1bBpEXZmz723sOGFeXJDuUp8S0CqqJNMw=\r\n=FaOG\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.6--canary.1137.5e459dc.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.6": {"name": "@jimp/plugin-invert", "version": "0.17.6", "description": "invert an image.", "dist": {"integrity": "sha512-UqfDBcFNAz0LK76uvMTc+xucNzCie7ueh+mNEvdHp6KzmbKrBFIeeb2M6nFumDm2wJnBjkKdN4D45nvq3D6pfw==", "shasum": "3a4a612c0808c08a328b3f492a88e565edbd8a62", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.6.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF8jh6D/IXVpvZ7VgBETFIzzsSN+QWRYuN4Fph0oWIuzAiAZzv5DgcESJtHA0TJyV9Enq2eLLL4ateVjIAs2NSmCKg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3e4kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKjQ/9E8P1qzL+bUw5ZBWo5KUEh6/Ybb1SkTH+AmkaCcG/uxQNExsH\r\nvF3mF6VTC5ivJoZ46/gsQGSXz5843Eyka/2RM5ytqWYkposXY8qR0RbJHM9q\r\nv0MkZcsIImjlXMFlL66mfw7qa0CpCf0UZELPJNpqx9Tijc+2E+Z5PTawni88\r\nZdI/iKKIkdtzJGmLiZHAoYHTKYN94c/r6cPaU8G3BeBpJjDMXTe2Dul5VNDV\r\nTsa8rIaDgzUWt2EAf6bYrgMKcuT1m2wQeaFm8rbs+tQweKJnv8S4hgBjgcqt\r\n9Xuhd9SsZDCwMiDjFOLy7Zki8vDsiagUwuSaDIOPd4vA4VLODkcNkMT1OQj3\r\nW7tMojmZjXsYJOa5R2lBTSHjrdiCS2YpHKQIK0XlmIaSmdFyWaSptouEG7sm\r\nidzK+LuIR1OwCQdQwsiu4nt0B/NJofFHPRrEU5Z3FesiKL7jUrKumVl2F44Z\r\n0XJKNse6GmPcIs1gTq+kYpwiIIzWS84zZZqR4Ng3fQypaNouQTBqmFbYXx6M\r\nGb7GBN5yHagQAs7sHVcBYevuXJSk2NTSBLcB7eXA7MjF8FlR3er4cpz1/WZS\r\nEeMadF4u+SC4dqFwbu6mZ8W15m6RhgdM79LQgn2OLnPA63FXNr6pRe97h87U\r\nP2SwEfNwNoKCROZnSTryw7psq7uXFHHm/As=\r\n=vjqY\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.6"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.7": {"name": "@jimp/plugin-invert", "version": "0.17.7", "description": "invert an image.", "dist": {"integrity": "sha512-b03DLDSHOKVDpVGHUc3oS4Nxk/DU0wu10WnvDe6SNGX7YE6XZs+By+d2CB47CFNDhWI5EUKawNjwbak1xDXilw==", "shasum": "802febb07133504fc28fd2a84da4bfba6304a04f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.7.tgz", "fileCount": 8, "unpackedSize": 7417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHhjUANDRwyHLAeSSvK2cojS4xue3nQJzWGdYpOVoy5AiAp0isppkqprKwWeFGoMFxkYLAPEeS0aEkqKLXZDr2+bg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3e+/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/Dw//fcztCZwjbYK/MejHUKtNC+GaTWM2EvjX7ZNhCuzLPCQDqMv0\r\n2d8RJnAZDBjknx9h3/C0d2xQVK2uzMZ9gRpbyYfqpwfv36ZXTY4ALuS8Foo9\r\nW02z+td4LN7QoguVMw4PHtwcuhMxRx8gw8JdVY88UR4to4YRj6J7SPMbkq88\r\n5gmtznr88DvVkvFozhxds7qjqvfLEo/BDDcYhUnbrxfxt9rg/YiZ4QRvw5bG\r\nPaGO9Bk45s7vlKFbcNVfDiUizruyZ/ppGwUSt0/xOetCu5hEe+7kE5MNeX3S\r\nvHrCVVcdtfpx1smyvVjHMN+LjomV3OU3uhb9kleiLrM4Br5cvIphQFeW69EP\r\nIhwOCVY5rLSqy4pohtQRD7GZZ6pdWExBy9Wgjsy0889FOOcm8l732rcsneDV\r\nhDwsfMf9ZE0/Epz9GV02qc0LD7YVEsmcVjtozm7A9b95qYgdJZbTV/thb1Ye\r\nokx0b19fqmTw7zYTHAQ+/lsWEbcLA9cW+gKpF3ccFpFtvjjGiMrWfzXn4KCi\r\nV4IIXY6b3uqn5Rrnq8ioAJWftA9VYs7Z0IE2dDM1uu7w/5uveYP0WMzB6uKz\r\n6BHXYtqsgGfTYPzUsR57rrUmdGWjJd67QdljVXZZ4meWW56/soVA8bt+MMAH\r\n2pxwnhGOLyi13PKrrgLx1H0B75+BlUW7v24=\r\n=u6Q1\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.8--canary.1137.476d7bd.0": {"name": "@jimp/plugin-invert", "version": "0.17.8--canary.1137.476d7bd.0", "description": "invert an image.", "dist": {"integrity": "sha512-tP7Qy8Uu0//3PJKdtfNGMmewu/lEIWA0jieGz3KFnhqbk8lmJZX4unGCSUXS1lyNtZitCS/Y1Q5a69rVQzPxEQ==", "shasum": "e4fe50f12ef7d6d6a0033beeec2dc8bee080791c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.8--canary.1137.476d7bd.0.tgz", "fileCount": 8, "unpackedSize": 7495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkYsaWbJsFH7WwRBCVVWyplRIsC+9h0fIbBHje/BLrYwIgUCUNd8fNobrfF1KEcT01+HIeFcB2ua81jrljC26RYRo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3fAhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+4Q//bkiqLkkdbUEt9QCEVJwQ2jVC1JKyhm3dOE+QVyb9ZaMn6xhZ\r\nQz/S/1NQukcAhR0FYLZgLeDw6t8DeK1gQzbmySGAmEGDJsnJLxV9GY+s3foH\r\nmbBkXSQOKNpngBzAio4rpdxeqxlVquLF/f/3oB0SZsoHlrdT5njru/wFIk7E\r\nG4OmAj6uBtSxHbMGijF1r6EfxwQT4ybJhmaDNlL/EdUUUoe1yuq9EvIaWjKq\r\neBzWjzBMTQM3h+e1BhXVV043+m8TJfsBn/zBEbxE4YU19X9eq8jlAauanhc1\r\n9TB/bJRzTwLaUcbdIUVvX+WzRuVpbQJMUC9j/lxFw6CKWv0QPuANT7chlomb\r\nP10ofoehtEFSQcgem/2d1EA2ecMvjXnUHKPaYj2ZDGIyU6SQqYnTb/0Cwxnx\r\noFccB2XALz4I9OPQzEL0cBvOld56rRMcB5n9i0Q1msERiSp8W35nF3IdWuoW\r\nZFmOHa70kyypuYLQ7y1eZP6yRRoTQjdNpxT8nEjs+SaEnD/8fV2cnirmrc53\r\nRJd1g1eiYcs5kZ7n9JXO5YU8XD/nJAIIm2SF2iTg8vG048YT2EDjO/g8GRml\r\n9SZUMQSHV7DNJfIK0U2IZA9Yw3A7S8g3k54cIZzHk0DPy8pwk2KenkdBQVmw\r\ngdKWQCyMq+Y6pmQBLSrKmBt7sWts45HSwTk=\r\n=9F4M\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.8--canary.1137.476d7bd.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.8": {"name": "@jimp/plugin-invert", "version": "0.17.8", "description": "invert an image.", "dist": {"integrity": "sha512-OdxofIIJdW1MIo6pInO6tBhWsZWrDu5bKhyopiJ+41+mO1PnujBnEnx5ahIKPKzr2fMVH37v4B1Jz1HQNxTWAg==", "shasum": "a2fb9257d987374d2d59aa28c66c549f53abbfc7", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.8.tgz", "fileCount": 8, "unpackedSize": 7450, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHX6O9qEikfxuo77rdtELAdL3Epw3D4MYBQ5PWCq2vFLAiEA6JGZz+qxTsEWIfLRM0d0/m6MVqePLLIqeUydFMOGs1c="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3fEAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqgBAAgPnuFjcknF4/1uzO8QFZbZXYEoqbgUomXCoYTthVpY6SBfhe\r\nj223L7S12D3f9D+gMkyDRCglFaV53VA+H0IEtrZ+YRi7QsVJumKUBDDWCuW7\r\nqavXKpthIQhUqT5UPW16SJffd5rd1J5Hn6vpQqMAszHeizsu0aw9SMOM0JE0\r\nm4AOBsSZc+4wlW2ns/xRGLXmUhYg16+vJCTxtH+CfaAowhado3JkjIwNO/nL\r\ne7/1psgD0MasQzZW2YQUqhKNlDOgA9h4Be6UKJNeEzaoU0cxWDzPp4PovgPM\r\nLcZzXutTp9pvP5xQBkSkCk60okqJ/ccANEVTqPnzwBWCb30iPbPCbxTrUWWN\r\nh5UG7Li1DXT/Rj3dTsIoIYndUrZmxcbG1OtC6ZRsslAAvuJknal3xcEe2rUJ\r\nr2LE1xY74J2CC+3jVUFv2uMdQmIM9rw07L5aI1SjTjT1pksUfEkrz5UYqXev\r\ntMri5XBxejD5Dg4rPqIDKMw4xrh0M6QPmctwm4wAIreK23e+CzBXPd/4HVTi\r\nUWo3iuIAKOt4t6Em9uCmg9IOaAycDDIjLXUp3rc564XfercNW7AMR3d3Vf++\r\n3MjBuFQw4ExLoN1/EUT/JMru1e+3miZbx2TU2yMHCLKSXhCd4ZS5AbBxEzKk\r\nrkkOKr/so7VPdi5wmqeBIM4jo5boYPgb/vQ=\r\n=XGKo\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.8"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9": {"name": "@jimp/plugin-invert", "version": "0.17.9", "description": "invert an image.", "dist": {"integrity": "sha512-emv+Sb/WWh76Yr9/8jyZcVzuH1CRf+XlRi1i4INKsw7Qn3fdjNrBSRd9MEDtZseKV7lLra4sHIz+gR4PZijKTA==", "shasum": "108f643f67cc849bfa7c553afbf694472647f0c2", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9.tgz", "fileCount": 8, "unpackedSize": 7450, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXNjibLVZqlb4bSz4GPQC2xF5gVk4Qeg9VEJKjhgpboQIhAILzO4citi2orCuZ4LVEMPanVD0+Q+DP69ZMLDhuq8hs"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3hW3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0Yw/+MDLmyH6E1cCcQyLkOJXZPVwUfW5dUSYG5eSAfvXbm1EIbLSZ\r\nhpx2pHKL11cxf/Aw3dxe/LfulbyrL4JkDabmoz+26Ye74BCM872/JJPJYgI3\r\nR6llSo9l2/gWCgeCGugEUYkfV3xhQlyddDR0qS8s3w2ZUtSfUOMmA9UHcAlE\r\nk2vs0E1TNmIkn5Syp3T/qXHXAOFI79rTbjzWtlLKrn76uEv6KDjHo+L8/fdX\r\nw8K2PxIZUN1oR4hoGWW7ftIGxbyaHExS2GQ2S0wL7CT4MlCY6W6mSCGm9pfu\r\nLjXsQNCr0EDLIpjWd+D1H1561gKwGFZ1L2iPxNjomAFSG9iolUyuB1McD1UP\r\n3z0nNvwZbDJcMKFf4yLj3SuJWae+vApD3DZK/4a6OaqJjo0Hom5R6kIpfPZq\r\nn/CF2tPfe3V5cgZTsCU5dcQAw8OcduNXgAAWXnHyX/dsyH0DKoLCqRQIfp3P\r\nrBJL1wvqtqsACxqnXq0iYwHbAczCdUykoZ3WXd3EZpNa8FpY9vPuJH6slOR0\r\nRW+oBJ7YFRij9WCOjXsbSzxmlzdvG5B6HXnD4S8aNHoo+Ud2gf9JaNfxrUo+\r\nnJFgvz95wku5g36WUzImhXPZ1SGOI9CaGNnDCCVaKhBB0G1R4BXoUm89UkP0\r\nToYX9wEFlpKx93+ofua5PxyDW/cZweCE3w4=\r\n=23Jh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.9"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1140.831bc3c.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1140.831bc3c.0", "description": "invert an image.", "dist": {"integrity": "sha512-+I10hPOSdfc8MU0hWGUpPsNwYGbCaQnSnA1MOQv/lfbeDAm3T4ya/6THuavHxooApg39U0SLUinEYkssypUWcQ==", "shasum": "ab069431dea99a33f9c1910fc75c2a4b26184188", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1140.831bc3c.0.tgz", "fileCount": 8, "unpackedSize": 7495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZQ0+MJN2Z3M/M+IyDWU0FoanY3N/mhvF2SgdJOMutPAiEAn/LeINkNI0G3GimHmVEl3wfpLdtXJ/JK4+xPPOTJjIM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3p5qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwgRAAoGGds1sw2MeVoSxExVrfpdbQ8Rf7oNROPb1n4IMm89nyUhBP\r\n+Kp12veo2xM66Clck4lBm7RLVdHPAe79oGosYvoKeSTiteH697K8HwomoAal\r\nQJxVNaCL8LyxI4/RDRPbbtPIVziRI0YkBuM2y70waV6AhR63TMJDb5C1YGvC\r\nuAths5iDRLMv8L6YnYR+dSi7jR+poe2OPa/W51smXpmpecs3Q1D4wleSxHDz\r\n/20V0LRYtUUjEbq+4qMN0PqIFbFBBHoe4SzSB5EMSKW7uXZWk41ZA9i2Wfya\r\n3OCP86vg3C404vZzNn2JEZrIkFsv5qwzYhkcbZZKZ14HkjXm/GeOJ7NqK+52\r\nyRXtpqNqa66Qzs7pUX0MV8LuLNBebiLnXUyTgsVc3imGI5fbMUZeevCBVrYC\r\nF7c5+yP6xAPmvdFhT1mf0NsIWWfckgBqcTmP5DF44kilKCmUwl9KbI8+orJd\r\n05kYucnb/ioG9eiR5kjnB4mBON1QIbfsoeOBX6Jfh2CUkEQO6SyuhXkgjvUm\r\n7r86gveufw3S6IEZzxdbVsafXUe+srbnQvRwdR6SEVn+WCeczm3zMeA+QWLk\r\njVaqAWj19UzlWsyWRR2tHyAayjN1Ifcnb9IA7362u/juy9Wc1jYQ4Hbl0fAB\r\n97tWGhfB/GWpcRpAxuOXywHixtQCgtV0f94=\r\n=/u1d\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1140.831bc3c.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1140.4042b43.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1140.4042b43.0", "description": "invert an image.", "dist": {"integrity": "sha512-VRwDoDbqy797uWRXlR7fHmgj8/h8/p4FNYWMmdnctE2By9Iw6QqQEW5wKvXY5+UfWmqeWnI+ITSgPmWlroAMSQ==", "shasum": "0703de9d786a81f3667173a0719b8e5859164e2f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1140.4042b43.0.tgz", "fileCount": 10, "unpackedSize": 9922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1NkgNvGvCMsiEwUGewmipE/rzj0BiD0tsL2fHOP2MZwIgAjkNEB3Dr9CxDPCHJpJB/z4W/EXYQO1cUBG3UF34aZo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3qItACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqLRAAonF/mtpsPh+4hlW5vZN4fU8m7bwoXuQopZxtL70Go+OT4cMb\r\negymRkuFIt/WsfeusZwYGLeFUv+bvV1lOik4fkR7exn0bL5vAhKHZkRCkRUv\r\nkDxHsqluHvKtkA0ha01mt+x1/LruSgUiDbf9eqxjgXkHUSrfYaCr+WNZbOjW\r\nMW44gpHz7KGgPJFEatrGAuvLy9llrezkNoZW9oPvWBrv4B1dBLaC5jtQxK1T\r\n1e7wFZ9wXp9iRvw6AiZPunc5JPBifFvp+EZvpUhJu1s7iZapkB+Vw5pIX0oG\r\njkerxfadMZOISV9TJvwz1tXh4cTkfIRsYevQ+8OhBdBcXzI0KPjQwVN2K9rj\r\n0+3HHvpPdj7wJK0R8l/TbmwkNpewFnvq9nbdZEOhKFZbKl2gQ99pfNzctLIH\r\nA4MGCyPdAVlHaj26VcUICXO5BRc/qIwrcNYvc+hxw8Lo8qw8kqZ5HOrN4SZN\r\nXAuRQj78pKz0sdDG/ykuxV7ttYrYslaTlRNMEjLZKzluwTkHyRayVKLpI90u\r\nBePDvxQzpY7edCEv+erLvvxnZMxdoRsi0zeTBJmB5s/nT1TLUBAlEV7gzUVY\r\nKbnBJhMUWovqmvfnnQGbHULZvbzBjy0KiYUC/vkZ9rQdNVG3Y5WGwSNEbSC1\r\nRGYmZ12uoe5IKIPPqTdHeDJtWTfYz8byKbk=\r\n=llUD\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1140.4042b43.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1140.e3ff49d.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1140.e3ff49d.0", "description": "invert an image.", "dist": {"integrity": "sha512-A4tMlQkd8GV5Z8z3IO3kmcH2CHCpA5Y6x79jxQp+EDUfHf2/B1wRjlUgN7G09frx/Abr0BYDZbkdjqgkF8p64g==", "shasum": "76edc656fd6e51b0f704faac634720ca5fac6c1c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1140.e3ff49d.0.tgz", "fileCount": 10, "unpackedSize": 9922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUN1WxSbkYZ1GdrgtREexZjGmHOPCN9ZF/n0fYr/kUoAiEA2Rcm3AVNgWKertxYCz/gViWkXUZ0TrpB/toQ37uPJCY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3qeWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmri7g/7BBmFz9xgANDRexPzcgXrAfTrN9GxCLVmi5hqITD9A3uQyzmQ\r\nsjOfsWHsAZlBbV1VI1ExH7ZJZqOWoJcurs6D3GHfPNUfQ7+CFnlCyEvsWl0u\r\njXvGFy+Ph+FV7MItfnOzp5fxxXpKYPy0aeLT4PPNoucPCt6sNRlTqzxosFde\r\n2dDG1W8fMmPf9Q08YGoEkQb8yZJP6/PM5p7AoXcAxo4GObCHD4xry03Wxv4r\r\nqDP2k2l5TF1Ts+jxfjAwC4c4FVpxcUW4s3RlKmHCMnaYzEvHAgptEIbt/PZW\r\nQN//GT5DdyCHIXmecjYOtxVgF6tyu+uFp29AXZmE9e/aLQhlU3Zose8KZgYC\r\nEYttv8t2W56FXf5uDpQ3DnfzxR1LvmrnXkL2NCfDHr8aX1VhAAsZMB/o55LL\r\nG/5+1F30VfnctxbaW2uiRawDh2rDqNfarQ0cmlJC4Yy6eoc+tbD4DDYXj0Jj\r\nywZ3aaE6DvRF2NiV+K4uZBxN3/3Y8Yzxh3m6gIT01rvm7vChwAk1Hn2hHB5/\r\na0C6ccXiP3rQorZhpMhPBtquIRhRB5LASoyYl78CS31WUWgiQUs6LqVAYzN7\r\nyqde1SSvMHM0V1NraTaWLprIG8heAfpkGXD9KtMr9v8ZTvB4zMhhPzr34z5i\r\nxk+2TfIQ3XSc5hlKpUwJUCtNEMvpHFgYCn4=\r\n=1YaJ\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1140.e3ff49d.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.10": {"name": "@jimp/plugin-invert", "version": "0.17.10", "description": "invert an image.", "dist": {"integrity": "sha512-9c9B6+C/vjpjt1ce5Q3hhSrgHvJsdSgxeK+KxpJKGNdpARNDSzhzRv0SFsbruyHnTDIcUuk0c3Zdy9IgYLpioQ==", "shasum": "e62904723aa4cca5cb6648ea65a8fbc0c1ce07ba", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.10.tgz", "fileCount": 10, "unpackedSize": 9879, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICWxWs+nXxvCRRIzfEUvDfxLYW/45WcLD8sJGOQWdKBEAiBeB3A2xEqK/t5A5kvOYqV3gdf7I/hTIU8SpYHR3F86Xg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3qk4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp20Q/9G45pZ9dsbj3VEFkF47Mg4x3x97snSsyMfA1qhB0HwINNMEfC\r\nQd+k32NHjlSqbLkjhGdt1Arfru0RMN5JSbkU9xOtmTuy7y8Ki8yRbGJhn9CA\r\nNNERCp80D/uTk4+Iz7iZFh32i0JbIx/jbMxZZq6xBideb3nZVkDARo8JXIJ9\r\njlO90OKjt0Bohgjm4Cj47uXM+x/9O5QFTpOsLGl3NeCJVs1o/Om4qnvntvga\r\nPMzXf0NdvhDoFK6YXngBPccFF7MzoiAdzOLPAzommYhL2vZ7OVEAh3nr5jaV\r\no8TEksgomK8ptU8NQlO9iVzfCFhXSdfz8khX0fz91u6IE4o2cpXIp8iWQp4o\r\nLdhYPVyaQoN2fi2o0qL/uHA7bkRj3WpFgzYYzJyBrYZRC34RlwlzZsmEsPAv\r\nws48uuzaDC70M2Vpbl3SciTziexbUNm5/wtm1d7TiTPOtHx/re7P3F+BZdLd\r\ngCwuBnrpX4H54ZwMpd6eISmYspOB2OwOaXTdAbvoL0xx8G/tcbbfNjBc7fFa\r\n/avKFeSpMNsmFUUMmtdAf1max56OjBfVFy2oJoKUVU4uyP1EBVF43KZGYVlw\r\nvAplev1zTQj3BH+PhPh7Nacr1tIYNoPr9EFD1RvURsApyqdFWvZrpZbPLSDG\r\nQXTOWwGaGM/rOqOlmwsR4vLi7GSibn0HOmk=\r\n=ux6P\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.17.10"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1141.cd4d455.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1141.cd4d455.0", "description": "invert an image.", "dist": {"integrity": "sha512-6vTJKwYp0b/LkBUEcaKR8LiiXA2qWd6el0TCLax1HNJ0ETigWPfr3NerykyIdnTQBMDwhZzwUOrXDvvfrIWxMA==", "shasum": "48c1fc265de9531b6d9822f503807b249693c01d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1141.cd4d455.0.tgz", "fileCount": 10, "unpackedSize": 9922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID9LCVPmXsGW7+HW7n+vVL7/tjdRa3h2IsgxiRhA5WROAiEA6fAY/d8uxGMEVAzY0ZBuoQW0JDpdEfsNtEN7WyjmbGM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vNQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj1RAAgufZdVSPD5+zLnz16c5I4OQC5eoWO+8CcbP4ovjskBbjdugs\r\nmdWjbBp9X+vavi2+dpyJftErs8IoMT2S4MacDbYWpZf9DT9J+2/10F9zYSBN\r\np6c2bBhwW50PzXBTPL1A31GI+eDEnbg8hxSlyFt5O4D4ma8N8hr3xlRsHFS7\r\nvahw0KXDzMmx7WPVtvRh4WQGFMqZH2Jw/RG8P5HIq23bMSggO8TAMV3wjqK2\r\nOG5X4KEc4vS9DpqKH5sFpOIRkOuPYLCEWeM3j6GbpgLTSYx554qkTk/eFkK9\r\nvlUe0zZezHngeidWdKaqzUq82GlzHkebzYyjg32VX3jWIkvTY9M1mMbtN+Tr\r\nWFQneqWmDX3W1pbGXnPWNMMxA6np3+EnORVCcuEwaikspuMkIhMeJdwRMsF7\r\nPRBuHG+vDcafKZbauamIaJmSlf6o+FjeBRTyTziMS+t5HmmylM34w1FrO1/G\r\nhQIO1qS3lMysuuAL8WmWUKtIU1ZmgvL+eTgHhMBglWD/5GK6E+UPjW5u9IQC\r\nFgxshzJrg2kTp6xp64Y7/0WXw8vrTgPLc8EpyKc+7bk4E6w469EFNsUPei5p\r\nOFeN13mj+lmPTmOcS5j1VkgqTjzAVP3WS/8hqA57bmPoUc9d1fxiWuGPmnW7\r\nGAWc5lnPmkZ30hw8IdAh+7AeuP/fJ5Unty0=\r\n=QjHh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1141.cd4d455.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.18.0": {"name": "@jimp/plugin-invert", "version": "0.18.0", "description": "invert an image.", "dist": {"integrity": "sha512-aQPul5Y2vpA5mrpSzABTfulIJdAyN5LvPkrHDBT7Z52CwSZJgDrEU2gu+gQk0RX3SMHx5xHQJTuk9ktswpBTfA==", "shasum": "4d72f6de1b59d27b726335c0a0ffa4dde470a1e8", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.18.0.tgz", "fileCount": 10, "unpackedSize": 9877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG25w9jIwD3gvJtpPvU9BdbUvOObVCxjZ+fiSrtA6pjPAiEApegv918vl/jTa8SGdtrl0oNiU4F/nrQK9pl7/vRghQE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vWNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKBg//fZ9S2eUqaU0Y5Bo7eP8Fv+Xi/6OZV6u6fLUthCjrCuu04TvH\r\nSKW2wFm4dcjwGz75qD4lV0T4QiaHVSPdQTVPuRR8HrNDyG/yDLj6XSKu22L7\r\nfvbwnt45tB4xSOrMJlEwrZ0/8+7JwMX066osNKfxyH/dJZeC/Qq6snk6hllC\r\nNL2gM1A6PgVlLFnWdP3lN905gm12toC+fmOjuCZb6TDrNb6d3xEwOtfW2Glh\r\nNPNQ2exovzVD6PRjcq8WlK+zx5qR231w30Xg3Sn+uYb4Nk4Fk3JCLhvlY2un\r\nWTSUPNwuD9TdTzlf+4XwMPnWHOYeAGJCMDOcIJ84X6nEGrN5FWAIo2MjJMK5\r\naGOYE2271uWkLGfGzgUNAlajbwoZ6mNy4CREB0/6ycY5oIyor0ZJDMK7e9jl\r\nyBlnsmGm77DT4Uaeyhunyv07yAAAeRhHFywsVzV3NA2y5vwA9V2BNJmsFpgK\r\nx9bJT4dx95GCvq7+8mZYA6ADeUueDXm4XEE/SehiAfnUu7hDAaIoHH0M1UCY\r\nOYF5Az7v+pyGeLRRLj0V6huRxIC/vx2t8tyOtlZy5TDFqTC44KArrZXcnKQH\r\nKPClvop8VTe4bYCcs1m1wU54irSsEQ6Wbpz3EwPXOxj7SJz6sZ1qSaQxHLXK\r\nDylk1ooml1H4v3giHNbHbznQPsZezYDRwhU=\r\n=ZwVa\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.18.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.fc042e5.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.fc042e5.0", "description": "invert an image.", "dist": {"integrity": "sha512-Q0AyCeOJbxR5QKOziivdzvX8KIdph1lgpx5J/3JbAwyVq1meKvzCX4vorZ3ZAZXKdwSVKRNViFAKisd702TK/g==", "shasum": "7d184845efdd7c43bb1eb9e7beb4b9ebd32dd9bb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.fc042e5.0.tgz", "fileCount": 10, "unpackedSize": 9912, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQo2su8pPsbW49RSDkWGiK37ki6WF6BmxMYjUaxdqHagIgeL8wI+KFZSK0Zi/U3v/sjEPJJjSRwEl0xQTpENlajc0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vZkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+Qw//VNslhvVVly85k0Ma4kXJ3da6BEBCEha4LXa+w+47HXFdS7uq\r\nNFMDdirXlwWy/x9TgLbAe+BC38OXCmmvc6iGyJbESf+vdA4aB8nEtoR7HeOI\r\niGThIuC2TxU5hzU4tvtZnyK6rnqE6hwnPBJotZWR+BWBBH5VwlCelHeW6obc\r\nF/Ye4FX+kFZPeLsCRjZMERsqeFB3SBc3iEsZtHscNw0tyXsUEwtWxgkJ5Jfx\r\nU4B6xifOdwSJY6E9V+7ZVyGCdcVS2ynw9gFHGsOd568v3qpGAgTNdzGdOLo9\r\nP0h/pYkPZNvrHSoTMJ1N4PgELgsAkKUa2V2Z6bMN21sRx58KWpGKbLoLRfB8\r\ntuFmTbLCTikdVlwip0yxZbcYoRSwOpUFjW1walMb8hhDtUTd/5SlDcEq8evB\r\nyqjk12ii28TvlxaCvIJ2DT9qX6/O5BKbRlA3Bopcoa9n614Pd/wrdf40x+e3\r\nF+FVbP49uSlGwh1imkLp93wGlv64D4IF7p8IZqjJMgAd/wT/b37hMuVQpL1q\r\nPx6qx75nBJ+D53Ti4fPw0HJBLNxt/whzf2NE/66jiybdlQLW6D+6HGRGU4F0\r\nzvzmMvASpiHfmqnDCe4zWO51A+8/u1J3eSdp9NiYp9KZbBoVdl6KVTIioXZH\r\n7/ZjhRDykrfGsHnuvjr0w8h3ts+g83U062I=\r\n=qbZk\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.fc042e5.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1143.90575e6.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1143.90575e6.0", "description": "invert an image.", "dist": {"integrity": "sha512-Jjbw4D0JBCbOwmTmJvDI0V4qYFPcf2+ROGYs4NqgbLSD4cN54E5Jmlof1sMzT1qeOJ/xtX6Dq/5kdhsSLLnPVg==", "shasum": "631e92ec664a6491fc4ac4f4246b81181eedd280", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1143.90575e6.0.tgz", "fileCount": 10, "unpackedSize": 9922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0Zj6aVAYTSH82Is0YZm2tlE5EdkUqs0RXfCqa6ViaKAIhAON1CC51y5dECZY5yW6pb8IcI71wxyApetQc1x9Opqhb"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vbFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLaQ//YpG4btrYoDhuyTzrlPlUVbi5HigHtwAHhcGNUW4C4Zh0ipMA\r\nuNyWMbf4IJNwgn7rezRo4Y4vIadB0hnaKADrn4Bb8DOgLnawGkWHQvBRBqMW\r\n9yUPh629RYkGE9OnoSfJ1vBNSNDlIW993tUESGS3zNE4htwGavmb9pi/LFuM\r\ngSFZxi3FFVcWpDgSd4xxIggQEVRK9CCrJRsADDioNgMHNM+8x4Dusgcl6aX+\r\nPCkZKzRhqpI1LrpjZqXbI5B7EXj9V7ppovJji+uNgKTIdw7O4WSm8eHAZNfh\r\nvHgCQ4dIpOwbCFPRmmQZZA4txyr1QiEsTI1YkvMdIlEjG/6eg6KA2ZNhkbzP\r\ni38iROtHHveogFCQhvUUczzBU3A8CGBawHJw1colNd6S0/EZ4TjKw4kuUeTa\r\nWQRa8NuwpUJbPVAWP9XGSeee4v2SXMwK+HWMtz5jsbPA9jiXzMRylXynjHPC\r\n4axdswxjJcC7S16yTot30nXN02yzWen7g8LpFGVWpBT/BpAvDwWhyz79uS9E\r\nhgwRdmoLAC8Xzp3OkNaiuv+RLTMmY5qS8D+58U1iGUUPUscFck2PFfQvtcJb\r\nzPkYQefEoKk0ud+yg2JbRixZbR5FFScZdHp7jH8L8h/3NotSABzHE/GTT4cq\r\nEZm2MhaXtavFjGXlJZ0yfzK2CugfkSBCrZc=\r\n=O9Bb\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1143.90575e6.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.17.9--canary.1144.3455afd.0": {"name": "@jimp/plugin-invert", "version": "0.17.9--canary.1144.3455afd.0", "description": "invert an image.", "dist": {"integrity": "sha512-NguXo0YfuRb0iNx3up1xFFgz4SrJjU0DSRuPy6MIlbIm+nyhib92jYALm/mp2LpVaxhMMGZQDJCaXR7loVsBvw==", "shasum": "f9f754698f789c562cbf2bb05ecb18d750565e84", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.17.9--canary.1144.3455afd.0.tgz", "fileCount": 10, "unpackedSize": 9922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC5tLBuTTIupeCvGK5XHo2HPaOCUNshBpFNEmJplBRuTAiA4fAYdI2AOhG3YpKwB//IM5zwWPzvWA0GMB4CiQtp8Ew=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vfxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaSw/7Bj+syivh35ondp+UIGOJJd7ZPxnrpRrZF8kWhK5LQS0D62sU\r\nDonSyx5vFD+cMnt93zFXhqVvsikFCCjkFAtBMVN3Xyf1ZzXIFArsb2TnuECh\r\nl05yulSOsLLDdcgdJ4IX9u5RI9J2i2dMXfAlQmrf6VoOjtaYTvIAnIQndXTr\r\n9lKh9IByrcTjGRsa3uFhEVXhBOTqjLaT9csYu6N9ofYs4E8InNuq/vidURES\r\nh+UHGs7ypmT4BEjjnlizD/1dwK0rz9FCavDtqCSwWr1kudTB8sPB0NXnxbFi\r\npbyLBBB0tD9sudbEno+RSAkm7N89uYUzbne7KcpxwF2yDMRMYY8k8rtfKKHa\r\nyoTcQmN5Siu+PRAqHmq/jcDdCKv3KFzZrfv1Nu6ZlkFg91PpH02Rd83Owvoe\r\nEg09EIwFyk07nCgl73cD3cs1dPWukr4NX2Q4o3zqyap4k3YdfO5qpdN8EPlx\r\nldE6vsJRVTJV1sBJUPW0KRZjkiCV3lG/jM1ZFU1q2monb/l1za2YLXrn+s3P\r\ngwsLd+JfGDMQ9+/k6mwXErfDZfIpaGyBH+ln3aSAcuCgv9vlZQ4NJsuWGau5\r\ncJXkVXTCwTbT4OFA5+jIvsYmfGqT6mzcVcZA2YT+Qp5VM6lcXND46zTsDbmF\r\nODop0K/4gYm2JdfQzO6K0lEuvWNes4/0mtE=\r\n=KISc\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "0.17.9--canary.1144.3455afd.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.19.0": {"name": "@jimp/plugin-invert", "version": "0.19.0", "description": "invert an image.", "dist": {"integrity": "sha512-OUMtN6Vfzxqo0yulg79/japepXpZbC/CoptgftuEnPVC4l+fP61V8Euf6jGbniqJkD0YbYtOw19+sHeZqfDmtw==", "shasum": "98610f168bdb557ba4efb890fa98a52a63ab89c9", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.19.0.tgz", "fileCount": 10, "unpackedSize": 9877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICvT0e4MPJ74Tm/J+vld8FG8ovgUlr7O3h/fUgoB8BZ8AiB5icCPucHkELLVNyPyBCdPdTUbixKEjEpU00CtC2O26A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vhDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ0xAAk/1M8PeL1TsMg59Hnvu4IyglLsM5vBaJCYGh4IUpKvUnHWYc\r\nMSCtDz0JyhlVKQQz8FZ+Ulql4MhbxVFYVNo/PATf+Xco+/ikCpGRvI344UlT\r\njO7Cg9NcX8M5LdgrJcNny22IpjAFSpuLUizcfZx7Zrji93XdquxFS18qGUhG\r\nZB2jJ5wb2B1mj2qgNOHPUuenTPZVUH1xV+82HMQG7+DAZsaCI7bcwRSTW2u4\r\n+QE4X5NJ9B2KpR+PGqdaHXudCqaohDRd8LsX5xYXciQV+wq9QGT5zP7aU+cJ\r\nCZ8mY5MsX/mMl+HvK21rtxq4+DdJADwrHOptHql/J6YIeHbCZDc1pZHoBKCJ\r\n521R554vDuFn1Kp9/uhqndoajLq1AmcO1qAjXJ15FPuQUzDB/PWg5PpKMTns\r\nOh+ByrfXnBFNSdZsmgolNugtNbrQPaNGsfvXjNHrjEObJcclLjprDRxX92nL\r\nbp+ghx/w8sIQY31N1dHzcFUY0npWaJyAfOHPkUQK9NGcAo/qKJR1QTqj3caE\r\nSCQKe1MA/wzZBur/awhaL2Z2pP2MPjZm3qyR9bufyuEx36IoZE/CAcQvP/BE\r\nr3+0yopq9lIYx4xGvOFURJlY3eaVYsfTsf6geiknNi0HBMyt5EB9VdxKuyic\r\n4QzNvt5LruRjD4sQ0NbaKtix4vlnsCEDqcs=\r\n=Ueek\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.19.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.20.0": {"name": "@jimp/plugin-invert", "version": "0.20.0", "description": "invert an image.", "dist": {"integrity": "sha512-6/av6eSa/54D4Wk6cVSlhaV3jJQp59mjia26UfvyF/qT2dxdzs/kY/9IUfKRW7GUe12P9B+Ml6RN366AyHC/XA==", "shasum": "bb497a3845fe0671b0e856d4565b3dadfc859654", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.20.0.tgz", "fileCount": 10, "unpackedSize": 9877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/JNk3wafbQ8PDuTqqGGEVVcTvz3MfsPG2phCx3+PUSAiA7oLDZUrTu1YHE2AF2OTldCcWvkoUXIm3zTO4vfjxq3g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3vvvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsAA//fTiTktFi1ys03mIDy60gydBXJyavlGDfSyF6/wO7u4/U/eX8\r\ndu1SJ64o5t7+ooFPXIFl29R2X7coJcvNWVCvDIecGlOV/j3AKD/6yMZA1bVR\r\nS/TZbUjFE2Pi9o9WBMBkbE6KLDg2s+tqHFlXjYBWijMfOYniHVsbn9AupotD\r\ndtFn37twmB1O3afj9TAe4ticjsobRtTvPq7yTTk3z+kDXxWaHhdU42URMTWD\r\nlK/icDzVFC3iCTZk1dYNZAsItwMnZULgobESU23EMXBFdO0qZbUPE93TIAeu\r\nctwfeP1XizjRjQYDPaJ8HNd4MiDKmzrKug+lpYgarb03ZXdoUhM9wlBvIE1P\r\n9AOo6yiAiJPLsfnfX/sd11Dq5LbrpIM6Ezmo5jGzKnoPw+G0KGyu05AbAAZg\r\nsMx9+1SKWTmYx0tuXXZBbL/SWBkHey3EBQIqsvRHq50Ld0bixens6s+lXUBe\r\nHpnesP6ei1R2+8tOn1c/xCh3wPO6p13yALLB484xA8kEWMGrRLb/Wb0hEz37\r\nQteQTLKrCjSPf3vc5LRP5D7MvryWEj8eXMFNlm+Dw6qbTM6zo+ep4gg+EHXd\r\nojssQl7E3B3JLTJfcllt4mPZ82dddan1lsYfY4XB05WLDGhViUrkuxT7thSb\r\n2n+KnVn4viXwhCMS44JZYaf4f5aoIIdkELw=\r\n=7d5+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.20.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.20.1": {"name": "@jimp/plugin-invert", "version": "0.20.1", "description": "invert an image.", "dist": {"integrity": "sha512-L7yqRILHQ+qUmhe5GLK7pw0z1i4gJtmkVlGoFy3oPrO+OW9mZ/UGBLipJrwPAqWdHYDkajxFkPjWGOJEM9U6oA==", "shasum": "c1a632298c6b2b2e9ba1d3995976db98380e7b90", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.20.1.tgz", "fileCount": 10, "unpackedSize": 9877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGolZ3Fx2CErPFodRbpoX3mMa/tWZ4K9KqHmHitE6MIEAiEAhdXxR6fAReER9kh88tm75c0z8wlOKAxa/Uam3IUvoAA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3v5NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDRw/+NnHu6iA+YdZPokpyVsFkuANDjhyYKc7G1Qc4zkKlYtMK+xQx\r\nrhPyqIUPdZigfRjvdk/OrXOFhbMfIQEh1dOoMAUsWzBuIX8X3HBBDz1M87fN\r\ny8mHLsO/GQQsuY5ZAGGhpJTK86gOgvGveIRIQ1lAgdYIQq+g1F8jwgK+Vcw7\r\nMzl6INPHaC+fgtPOv/5r7ItjBm77LYk71dotLsWr6lHFi9Vhr+wPoiohbovV\r\nRPU6/N/6zcglTOQRo+HzEhXy3Og/ul6D+jH83XxTXVmiMw9LwMLM4Tx2jsOe\r\nIoUPYo8DBoPBpRaWoyr2FpLDyg05r3CShGaMEo/4gS5jkNSDX1sQRqvhKbTg\r\ngSSxYtvbIX7yiuCS9L/XS2zzRSbL5TdtP9/t1lPgsXf35tK+ssPrTCFfFOZo\r\nF8gIhLnVC3kpBzNY+IbAgj2tQop+deXKQhOoTgZLMWATfmAAJb9eYu8QJr65\r\ns4pFQYKh7KlbEVOqq7d778j393FSvztQEDy4jC2oMykXkFd57ussQBcFxhul\r\nNGb1xnrDu3O31vnuzZc+qRc+XHNVrPqrlkX2kKqiAjKc+VAxV6KAfWEgYxWh\r\nApmxTT0DVdl7HGJoXMvF3ekuLRl40b7iM74KgJnEx1+J8imYiv7U0912GTa9\r\nN6SG+vnejnZWz3JyUwa+EwnjedQ6xfUEnKU=\r\n=doFp\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.20.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.20.2": {"name": "@jimp/plugin-invert", "version": "0.20.2", "description": "invert an image.", "dist": {"integrity": "sha512-TrpCqhizM8ZhUXtOHvu/EIpvGUROE6FXEebNtBUhtz0xYI7HBzm5hG3FkuQOAAZcswkXFUeQRn8xGb1qmb6FuA==", "shasum": "94d56f461232fda2935e1b478a60ecaa2e773259", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.20.2.tgz", "fileCount": 10, "unpackedSize": 9877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN3SNY6W1XMMHHH5ypRdyLJ8WDsEfuz+/31v8D+Qlb9wIgf9aHBHW8Z+0RFNXqPLiWtNcta+xstsvFM0Pl7AiQXKM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3wHMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvVhAAiqXWO/1Sfe9rQvRdgKraH4jekc6QqKH1D/SKO2bpjom1JK0b\r\nwZwQYeb3g7AUFH6lQGDSnk17BgZeiuMPGZmAUFDMxy6eEstIDvQAsUQDzqkR\r\n0TTTGb8mU6LuCd+Ys5ovRFvx5C/sDeelgtd9mOqvEctsaB69lnu9GeyjgnU2\r\n0J7u05z5Gt4wVuWnWIUddE/30mvqUAf2A0HeBdmMYBBLDE+UswSTDDILunKm\r\n12GHp7+LdJhTlJSbllaRiAmgJOmWw/kkF5u83USWHXjLEh+f/N6jed5bQRMi\r\nNJUx703izkkRAA2cIxPPEzbRNrVaDl+zU0bZxwRdM6ruoMhBRrZwXWUrN/+B\r\ngLL7fiIFYEtia/2fqPP8Xk5TdCWC6frvcumWWO0w/YYE7BWlvdmc44ZMguza\r\nwTXBKgfgS4YYogkBNx9PEbJ5WrF5RJN3HwsR6NJ0u7hTXGVmu3WKdk91ZvjL\r\nddNaWl+92mWE0djm/dzAy+FvN1ibjj8TWKd0AEKam7B57T4u7HhfMC1kjgIQ\r\n7d2RZdokMlx35xDZNueIMQXobe5kHkekDrLa5BHVjxEMTJor6Q214A0egH9Y\r\nolJ7rIQZ7hCbtopqiseT2IqVRTUu7+ctEnzcI1kz4jA5KgRMtf4ZQAc1epy+\r\n/inwDqh+FZoWSek/Ja+tL53iyd2fV85qCIo=\r\n=LxYt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.20.2"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.0--canary.1149.3239903.0": {"name": "@jimp/plugin-invert", "version": "0.21.0--canary.1149.3239903.0", "description": "invert an image.", "dist": {"integrity": "sha512-3d265I05mf9IAhlZfLPXtllwbwVCYxmSzfqYf43QYWHGc5hvC2nyXurSeZnLb70Z/vv9SnyL4+Wcd92+qCdtrQ==", "shasum": "d3128d33bec8ac3900aa2fcf8e4cc5c297ab056f", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.0--canary.1149.3239903.0.tgz", "fileCount": 10, "unpackedSize": 9797, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFTS5w5Y/7dZljFSX7PA36+iswLgFeYogqcEk2RAeExTAiBBKLddIsxtwI/ivYJQfvASIXgnixMYyAfvnj5Mnaoe5w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3xKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6cw//RSzh6KJgEnJGclK6r8Xw8Hn5e6lxTSgN87gkt4cC8/lddwCW\r\nSIuRd5urf+ZwKBlZ3A+Dbe3uukzxy1dyP0vpI6+MsUg72P6m7CGKUpUXKkoO\r\n+AlwOf00I8NOi47q1gmninLh6FAsCz4RQu8HP6OvbW+FttXFP9xbvUuXIv8B\r\nqKeGXEmPabZrqGv5+vOCfJl8IhspyNrr+DpAUIj1EWaZsGPi7j7+oNQ2uYlU\r\n5pnyBY5gncQSVioEuxLc2Rg1cvtUNWZuNrwalLw5tUmBwSsQ9osYJmAcIyZw\r\n/x+ZEnPFL3/64jmBAFJYEiE13m2mw0KZ1UY7m36BNTygZIXZGu7t8oBEKqxk\r\nMScc06IfHUBqTMEAFAw9B0MkaTINegMc/gS6wJmflJFnnyi3BOSqc3RDT2fB\r\nZVF93F975DeEgpFaLAA5/TiWHrNGEfFdY1Z7trFBtVw+BVMNepjhCRI/zt/j\r\nyeqb1X0zIhsPSLJob3+2iXvKkB3lc6GlfNPkgAhxy/ZGaQWOEaw893gGE1Ll\r\nQKSD3IFKpEdCcSlnEZDZqTIbzzUF07zXBZfx+QNwBjAEbEXrB2AwIep+ylU3\r\n4DNznmrwvehFbBrV0VqaNQIGjdULSHUWH6grJz5zExGGWhVX3QdNeUI8FflY\r\nzAteb27RImGVuRCEzjG2fTEhTRv/mz9OIHI=\r\n=AAR0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.0--canary.1149.3239903.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.0--canary.1149.a81f653.0": {"name": "@jimp/plugin-invert", "version": "0.21.0--canary.1149.a81f653.0", "description": "invert an image.", "dist": {"integrity": "sha512-jAyNjNyxfKtg0OnPXKgtMURlRovWs9DQ/YmCP9qpfXiagbtoLqIUEl7MkmdUikFQGQEg5EDKemD57XsKdmSsVA==", "shasum": "35466fa56534ac1d3064df1604bc406cd95b38b6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.0--canary.1149.a81f653.0.tgz", "fileCount": 10, "unpackedSize": 9797, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJX7bBda23/tNnlRv/ZZbPVGVip7v3RA919iYeswo4bAiEAmwtbPyE4N7c2eayANtXUly5z9B1ozJSiW2d9Yq2fF7s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3xT+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkoxAAgujYHpujAztgY0gCQnqidXgXriOlJ5ArdyMeVnp/ozNwBLhz\r\n5tw0iCDJKUFBLJNPalHLQgsgxy7RbNrmiTSkOgyMS8o6uV0LlYuRvBAiXHzQ\r\n/H4BKzrZMuZw/DHiUgIi2JzLQsgKQw4IyYd1q8IqeWBmgC7zUyeT14ZhJRz/\r\nEFjXmHrlUjHTlICTnSVVM9xTKZJA7nzZV0zbcLkN48D0EA3NfoHcK3C3PtBe\r\nnjyBPj0/+d6H+vP1pCLJn0eCV32lrBrR8xBHjdTYV9YNGk/gVj0sIGtfQ3/Q\r\nLjyn3UWEQLz+ulnPdTQXPcLW3h2KDn+gb1degaPdTVnx/5gJUl+VoY+evWus\r\njvqkqM3mpD2rJ6VJB+uXd4Sq+qWXIxU0PZzG3HCY84PcwAQM748mW31Hezwx\r\nOoKpqZP5yCVuGdlBup3o6YgjdB8qV4sQ/7xOWg/hgVhaX5B1dYFLYbsROgt7\r\n6JdyQ8zmW0XOxVunK0fy7Xol2qclft+57yQxDbn5aEvWb0atoZxVmcYiVZL7\r\n8jEvV+35oPzRlYuNUzQtTCy8I+6YNJXE66+nodsVjV9U8v2Q2hR4+JWj1GRk\r\ni0pn/VDyEIw4w3HXB+flrUGwE4QjvmGcK1t4StrCvzHR4yRSNWIL/E+pLLsP\r\n3/4zs3XMUUSw8ts9xeqBtG0GvMF3nK3uh1g=\r\n=rM0G\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.0--canary.1149.a81f653.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.0": {"name": "@jimp/plugin-invert", "version": "0.21.0", "description": "invert an image.", "dist": {"integrity": "sha512-M9dD8sQRPC4MsBHotL3sPRaOnmmee5y9rRP5RKs1D4eBVhDGY154VaxGgPjB7hcpeUl7lQb3hDsfpRLeCRn0jQ==", "shasum": "c48498f4db88d1bf9f97cfe874eb83777604757e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.0.tgz", "fileCount": 10, "unpackedSize": 10419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkQpusPFCtwC3yMQWXghU3hY18fM6jx+2z7t9lEzE5BAIhAOEXez7Piq3zXQCZ/+57SHfJPwWtVQCemYE8iFvzb5aI"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3xajACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmolrw//bw//Ls1YsEhfB32pqjzv+aMlD+qqkJHJEeMzg7WhTSv1dVym\r\n9s+Ar/j5c2uF16P1BPIn20CU0tXTR4XE1Y39fhvgf/jKnw5PXK0JcxvdZNQX\r\nkhzX79o2KxzfysheQ5fSHWD77M+IBwMal0LHdBujzJjDNeygH284MYTQnSPj\r\nJf1G0TwwcmwXEZhDGTgNOw5ttx80ovlNKWqVThz+fdivPspV8gN9govlrQ1V\r\nHZsCVXuwh1bJv88Pw+G2PxngeI1ax8uyixesJ99JMkLFFzblKlxtx6ZYwrDj\r\nJ3J43kA64SM9mu2HlEHDs8yYdM9B8QsKTWLn1oramqt+SsSyda2E7YS+ig9T\r\nbox8JuOy4s/3Lmb2ZWJ29e1DN4Eybd1r/l83nbNizyVlLtBjq8VcmuzYfT9O\r\nCoq10ImOd4JijwxKauNAH5R/v4L3aqoiefOrJFMSnlGVD1ToGLiBOwzQBp1h\r\nr57mcpjwZp2HAibay9aZKhQuQH7bwMV3YsHkfOCoVjqYzncKJb5cuX8N1kBf\r\nAB3xYoafe3Am+Z1dq9SNLQYZI4AXpTTtUhwo1eJiOYO/RZ3vB8XrMdsKwrLC\r\nC2FS2Fqz7Coh3ZLeF/Q6E8OeIdByKOQJZiqST7MbeO/fGQW4gn/Xh7DUcDz/\r\nWhZGEAEsu3jnsKwOXuiHmaZhybLvDYrzE2E=\r\n=PYi/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.21.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.1--canary.1153.3cc6d7b.0": {"name": "@jimp/plugin-invert", "version": "0.21.1--canary.1153.3cc6d7b.0", "description": "invert an image.", "dist": {"integrity": "sha512-d2oqq28+f/pLph+ExoU5J715INk8y81Nooc8AcPd7LvqXPFsrMsPQZ157EQp+AQKZE6nqmXLml6wEO2CGLr6lw==", "shasum": "0eba3ad8a1e21c397d4b6e90a7e43f5c5d5ee64d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.1--canary.1153.3cc6d7b.0.tgz", "fileCount": 10, "unpackedSize": 10464, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICt56Qiqw0vyUP6VZIOPpTp7BEWwCRvPjWJ/gJt9oPiWAiEA14CyMHqybpJ+BCSSQdzM3goM3Ses2k67qsbqpRH0cdk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4CkFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrirg/8DNJrId4HtQ4EXuMsn8qW0nOJy/Oaghj2OCqM1N1YEH9dOyLz\r\nj1j7zBzuhRY5L8Ax2ssABg3pGimFgJx8pko7oPuTam8kdinVmwC8vigd9f6/\r\ngMZQxnFxy8ulWTk37DiSr5hlGcB9XRBzqQktf32UT02RgyGXjZ1yG3Dp7YQt\r\nadRZ5bpge8a9Tl/uEKDgGUKmQcc4EcL3uU+KY0hG7F1kfB7Q3njyNTCE0xVQ\r\nE7Pn9g/mpWpYLna/7P/3hsEu7znBOvgQyUmfF3bQbHSiIl+Uw4lmHznC0xnR\r\n9fO65KkiRCSjkl/3Hw9hGbd1ntIHjKHGLlQTLxryI7XHd3X37H9eghMyNmd/\r\n+mzuljvx3QTzH9uhaNUm3muvCgTdO0nkKkTQYWxoLzS1/5QUoHSGlBF6a5qO\r\ndOh3g/5GrjvQ81g7Dpxh7Gj5EYaNxZzaBLDSS8S417G8ekE5n5tis2bBEFSu\r\ngnkueJX8pthPc8Edo501Tv1g233olgPZzMu1v/y/xSK7VhdMOZUOSa3JNLyM\r\nQXC2MBjdrsEZG3tqNZFM6nNAsWW4gLQodaPwoyPsNTEL0huKrZ4+BumjUWsL\r\nixuA8SH7hFcc0Mr7AS69VIbkT66/ajRjqhx0fBtq5I5TDJN5VOnmDJ5hguK+\r\ng7dYr8VvqykXtQCqLlEPsBgc28a1QDFL/kw=\r\n=3+vd\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.1--canary.1153.3cc6d7b.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.1": {"name": "@jimp/plugin-invert", "version": "0.21.1", "description": "invert an image.", "dist": {"integrity": "sha512-RlafVt1zdF9BnflgWDNbGQvlTBhD/HkhY/R9eWIUZrSi1j+TD63CVXi6DirlnPlwtIl4uIeuixHLfMY2UpSxMg==", "shasum": "338c438d5c6eb760a595ea8c4a896cdf7911bc8c", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.1.tgz", "fileCount": 10, "unpackedSize": 10419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdMfq75+XuB1PwKwgr24jpixKvPl0xjuY99n3stHcuuwIgGeA23MekOdV/4Ni7fcIp5zq9wmJHe8NAH7+ow0VSx5Y="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4C7bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIUg//V//X1w0ZSRzYo5hk0wc6gDPXshJrRPj5xXJMkQV3fazw920Q\r\nc/asRDJE2jh1/Sfyf5vfgFngjdq19eIi0Vcp/5qIwbRGt9OTrN+nrHJxJrY2\r\nK8SCYHaU6vPyDnlj21Dg65Y6JCGmFFYcIVTen7ecxrXH5EZ/+cJTzYZTZN8R\r\n7THFBKpaypnToMdgNVzjONT9YM+hfVhtNl4Zm8M6Rh25n78els2RvDlFeCHq\r\nycsoCRj4Kzqk9bPPRtr0Tnm6By/H9YNLEl99kwMVwy/d3ZMNzis2r41P9lJk\r\nYTLh98MkRWNayrn2ZsAT0qU4ymhJ3of426r3JaBWpqKY/eKqdMHb+4nXwjqs\r\noE9Xat5FZCIx4WFItwlZYZFsP6TqGoyC5yTL9NtW0jBOdJOMTtPw69O3UhlM\r\nC8bbuXt6eoA4frOMx7yZ49lJcdOsqKxpZdyqWpBez/hKjYbo2k3LUN8/o/6F\r\nc/1qCZpDy1xURoexRxMMXB6JhrtqWtpRQgB7bXcTgJDaID6Xt71LSyRLiJFl\r\n+un6pr0YH2daA/+ra56IImkfBgmKOsyVVhcIjmA8fkkEDKRUE29dTInjChBb\r\nyCUHVB9b25cSyH675TxKheOwFjtyqF/olEt+7xwcmHT9+SJ1i5J2StFBEKZg\r\nY5OjNOzeU+W0xBtSUY8ht3vTwjLKYsoev+4=\r\n=s2Et\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.21.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.2--canary.1156.8b2cc45.0": {"name": "@jimp/plugin-invert", "version": "0.21.2--canary.1156.8b2cc45.0", "description": "invert an image.", "dist": {"integrity": "sha512-jM65+vyZh1xkiTBM3g6by2yUa1xsqz3xSxYXRamxXO9+EwT2WSlh1U2OW5FxuLqxgQgaCa6cXnCQuHYGIRIBzA==", "shasum": "b88e58740709e10ca3901fd7ca79719adde3ae38", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.2--canary.1156.8b2cc45.0.tgz", "fileCount": 10, "unpackedSize": 10464, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGT1iIGZ0aBJUeUj0M78Eve0MdNeV+t9TZ+11QvYLvd9AiEAw77HQZb5cRCgRLKsViIe1kMfmFDp5+S+C/4zeNDfMRk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DGuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrxRAAmnfjMMG/sxLkXeQcE5KjM7/ZiaXKMv+KVmWIW0oCIoPhHnJj\r\nRqN39QyyksuM33Z6UKS2DbMpaerEwxud79ZqXR4F7KtgycagdtgFABp3JcwQ\r\ndGuy9wYKafUEXdeVzxrPBS3p7PXQEg7xQHLWvl7mXsW+s9+Xld/ZRYPu4pls\r\nni5RVbYZUS+wxBEUrtywuxNKs7QNuZwuepZmq64AaSnVWlZQaXeXuOfvKr2P\r\nSFhr7shi+NjpjCbsiJhHEbqxNAOjgqePDh/nnGuBylrPB/LH/3aLg0IsIw1t\r\nZU1TOqfNg4zgQvDZvtHJz7tx7l4ejLQ0+1l+jEGf3Xz5d5hHj7xY5eU8oO7K\r\n89qvjDPS8dpeNnPwxOJEmWRPk26r/TS6/A6v4P1hy7+SMpPOz+1c8PHbSFFn\r\nmaWZfSu/QABS91qwHPLGDDEznKxiUbY6fEApXbnm19VbURd8VGiva/z+wo7M\r\ntO/qxOtjHO/Wd2mVGV/HhfCcq9gVMWxuT+9w/AKYsjtzE0a70gT+yw3y29ht\r\nUUxWHWmZwzw0eFofgdsxBWUioIneBdUA+QsSKis31IdMMbD68OTJH+r/E89l\r\nN+VhWSmJwLHo6vZFighXXhqX1/Ccq0PlsZDDHozoUW+FOGNViaRhZ8MgIkPu\r\nt0EsAIjaIfGasQVBUKLf11RislHMH2kW5zc=\r\n=gxPd\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.2--canary.1156.8b2cc45.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.2--canary.1156.ba9fec9d0c9109924b4a5acab31bcaef8159a610.0": {"name": "@jimp/plugin-invert", "version": "0.21.2--canary.1156.ba9fec9d0c9109924b4a5acab31bcaef8159a610.0", "description": "invert an image.", "dist": {"integrity": "sha512-6Pn2vmiHbffFXcwVBBqENR8NoQ/nhT8KgDELi/hVQjBjnHQ1Wv49Mtxbm4RoA4gHfTtj3y0SjBSfle/lPGbnow==", "shasum": "27c8252c493399b95851bd967c9c659828796884", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.2--canary.1156.ba9fec9d0c9109924b4a5acab31bcaef8159a610.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgQowXXImzAnWARvnbudrn3ZmHyIy4dzWTkJ3IInuMGAiEAn6mpB3DOWAYDHHwFt9xalY7SAqJOR1BF6YY0a7Ixh2E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DHbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIWhAAj9LYW7m7aTIc1lHR4Lp2r5sl6m1LG9RRwk/vw5zuZklMoW38\r\npKuRrX9X+l9s7RZXkyJJKwDiQZKk7uFp4jIjM9+F6xM1ru9gf42QElpEbEjG\r\n0CmPYNamoTgYp+uzmkH442BvrXSjO7Ivv1wyXLJnzh9x85/GYeHcg+nLl6Sp\r\nMnNSm2DqX4qpmIiYmXrFrtiW0H3JgtS4yHXQNRMD+1lk62q/vLm+Wm<PERSON><PERSON><PERSON>\r\nzjL/dxUUY/B7//9kKBHsGVjmlDAmtU6fM/vdXycZUcMa2er8iMYf9uC0zxLq\r\n3ZBDlnP4U8yJI1gO3dbKwRXkfEDe2oy27q4gwOXrmdyns2cJovNL4eqdyx2j\r\nZVOogT6kIbJc+/kzHmvdtpYorS5sNflO4XsH+iuD3n9vsASpfKCWNpSvQRqu\r\nT0SdCDtnCyOkizbbdWn2eOMAXCwCDxwNr/t/2ICevnzuMwyqIdXKfoPUTrhC\r\nqOIiZbTw1e2TJ8ZSgC6Pp2EP7dCXLNCFyYGM7B25b4usZAMLHNO9uJb5gfSU\r\ngm+nnu5XBVDomjpBNk0bI6/gLAeHRFUGx1WH7QgSAj6HuVu7EFvRN6GdVWy2\r\n4hwZANwiuEAKmmzplrMfUgwSovCUYs7lTe1B8XRKSSze6ig81vUZDzLxuPDy\r\ntWzd8tfYDCTaBYLKB1j2ZH4QB01yD1bIkBo=\r\n=E6WP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.2--canary.1156.ba9fec9d0c9109924b4a5acab31bcaef8159a610.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.2--canary.1156.1f8a92bfc1cbc6b5f3ad2490d0ec86710c81f635.0": {"name": "@jimp/plugin-invert", "version": "0.21.2--canary.1156.1f8a92bfc1cbc6b5f3ad2490d0ec86710c81f635.0", "description": "invert an image.", "dist": {"integrity": "sha512-4siGBhgHHvDXGNrv+uy/JAghdJ87xub2HkHf4Egpe2IPWF/MzdLgNVfNsZzIL/qPX7VhZUA9I9Wx7N5HP5wfBA==", "shasum": "38e65d1f613765842f2797621aeae2c4aca4b920", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.2--canary.1156.1f8a92bfc1cbc6b5f3ad2490d0ec86710c81f635.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTvcNVm1ufzThf0f1TLbCZoU61fU02CjEkCsF8yBXOqwIhAO/c7Q168IOG9/FYvxFDkraejeJ6uQ2YmFfWhasPEuWJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DK5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLOQ/+OP8txjIAY9DuAkhPIK13zW5rhY9QPYX7dcakjR0UsOERMAi1\r\ngQyK2t2ehYEHUXA+v+26ciycecpqpGPeM8uoY7+qBrFe0TgcsyFeMiXXTY/6\r\n3V8Nx/jqnaJS5B1lvwPJvrZAUNUGAKaktC5ZPUO7FmUXT/FI66JjB6yJ6jG7\r\nOaqqwA2iijfFywvlwvzhoys7k5krhehsnHXXC5kXjU3sHZKQ61p8Okv1Dml1\r\nG0GHtbcEJ2NcvtDPMD8BCp5R/e4rOp1OPgLVIkKMEI1iYQTwsyoXpstxzABY\r\nk1AG4TKJQVNY4yliXsadx5JJCqgBO/4pMveM5FhP+CIfvmo47+NLxmZCa2ss\r\nN837UiZuaDw70yz67LEcyLyV7qDJPiEjXhQEVgViWXkm9IqWzqJ4rILmN5Qk\r\nBHo7l1Rh+5gd72SLbGD2jzMaOSnRIEx85NtYZcg/d4G8YgFAC5BjwFl/D5kw\r\nZvbQtILLQ1aN5D9rQKUOJbskb1J6i+o0veaJgLRyL/y2cVPRe1MuKh389wTa\r\nvf6H8YAulwYuMpp8pxqB8uAnbgu61jYvqB+GrXPBzTXw1LsMsuTj6dNFEuhS\r\nQOjLsb//Z0ZhQw08HV1Jmp6q8TU9vb1TFrbAjmJjoQBR+16dbxWzlBACdtXS\r\nErpVHKBkKyUJhWKQgVo0bL19EBM+UpK5vII=\r\n=W3zh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.2--canary.1156.1f8a92bfc1cbc6b5f3ad2490d0ec86710c81f635.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.2": {"name": "@jimp/plugin-invert", "version": "0.21.2", "description": "invert an image.", "dist": {"integrity": "sha512-Z/xqbvUE3x7WVXT1yRWKK0MpFI1ik4l6W8u3O4PXrAiSC3DSQVugBGUo8kOkUh4w/Oh2TSpjC2bJJsq8V68zpA==", "shasum": "5e65c788d0c0f6e579b29f8d76604b7d41ef5b7d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.2.tgz", "fileCount": 10, "unpackedSize": 10419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFDBK1MzAV5YSc6fqlcOJ/DRA/qlkkGsxaOCbizr9PT+AiBOd+qfOZi05GcYWzXEO1eawbRZIwlG7u4RLtUp4eogpQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DQ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMBRAAnWLOMB8EjyXP0dduxToBBDlTrio6ETfFEST1xVz0SU9nkb4m\r\nxN7D7id+b3hKAA8WGrEqO3nxPoFgp8LrBYtmEaNoy0gk0D3abfeUqmHqDNpU\r\njogZXKLjoqr7jvK56LvQvK9RdOekzVBrYP+VT6uYO4JmcPKMBh+1zEppCZWL\r\n9ojwFO4QqI/SCZOF6vMqyjaBX2r92SrOe1ZUptJ2H6XwHl3BOvtyK60wZl+2\r\nzBE+XDJnJM28/fF4uRm1sTAcmjOOg2AbOksxHIJOo4mSd9YmJTDaYZzjoWq8\r\nAWUlleTtsgOW5sId+rQgtErf4JBlvcZmHjQiGN3XcajhVs8c+RTZV5ETjZTt\r\n3qDURczKPPAjO1rQtDDkGuqCrbKUK1cbmfEIjfCrTHEHTLOjXRTY1bPvJ8bY\r\nNllT+P92sQ+dZV1BNx1zxCPedaPmwXTZhkvD3pSkf3hOogpbOGfeab3RtNNf\r\n8flhFfjtUYX7yv2DOxYuwmEXe5D1PJzMHZQSPXdFT46HZy7GOKKv4dCD6XSb\r\nOZZbXLFTPAajHColBV86xJrr8SfaHosEWoo2ecNjIt/lKrTq4OhoEBBQ5k+j\r\nmp+MVsS5YFm+y9UCm2OF8QV+Ovp0ZQB6K2PP9j+OVIBHbA2T5CBv7DWUrh2M\r\ninaWcX6kflpwf1aIUp19LGUk9oz1+LwtUXg=\r\n=FqIN\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.21.2"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.3": {"name": "@jimp/plugin-invert", "version": "0.21.3", "description": "invert an image.", "dist": {"integrity": "sha512-PMUsua7hsyBMZhDkwARJp6MiDcAeqmqMk7W8GqBPkOZNKpbfrfs4FRwUrRXEH2OSGZGVdjxadYNLeIRurHpWqA==", "shasum": "32b5707406fa59fb8d77ecaef918db1c873fd728", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.3.tgz", "fileCount": 10, "unpackedSize": 10419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC44KcWAiNv2vNCjB1gr+tSi09bhaADfDOM0y0JIXJb1AIhAI8Lt6sJPXpFtoJeV/mk0EL87N2nXQyi9fBkQHkFXHdJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4DZMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp66hAAi9pgCO7uoj6D8M60s8LfryJ/shkonGSHh2XPtfSg+9f57Vm3\r\nUwoYosLDDjq9oINR2db6xMTGKngG+zrXrlh0/SVvQPKa7SVaaQ9ic1HMb0lf\r\nbxAxCDOEHRb+eu/M4hSpQHP5mzZ6EUaQoCBFzM6uxtkuFbd4opQDONqwK4fs\r\nujNlA/WPjN7JY4+mcRf5nkvzzPnFu5V+cpNpSCt9XdWOmR7q/6G0n4FrD482\r\n8rpfZsAwdfUNisLa2NDdhP6SGN7LnBnlw94ZVdDnB1dPZgZ3Jh7uwrbvdpr8\r\nVxf0pmXk0GzIAM3EK2yytpyl7ydj4IRNNBe1nivm0Gsq+OK4HYPUJs1L1OBc\r\nvczB5m1Rb7XGjOULzo4ybDkLQIvaL7uI5kZYA4lrfbw6KqO3q3SgPbOAkWUP\r\nQ+py/37me5IwtH9Eb1XY+KgrBZBKr7ojELDY4XwEJSJn1KHFG27dv4GSAiDb\r\nqbUuj972SoxFx23xfWFqa1VqIZLgblzRh1764A7T0p8tbjw1FeQnRG1cFCL/\r\nSM/KJbmgzuza0+Acz2bgjeCwLr1vme1yRRXUBpNIIxBYVlJ8HgaNtYzIaIJZ\r\njvgE12pdH3R53EcfxXeW1KKRnMePkCKdInKcx8FNIaITDSSIMsnW2ys/S+EA\r\nV1EqTgJjQztKX7LV2cjXdamN8lFDoCXpt0c=\r\n=UGel\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.21.3"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.4--canary.1163.a26f5b300ee88c2fadb9909dd390cb4380b32a42.0": {"name": "@jimp/plugin-invert", "version": "0.21.4--canary.1163.a26f5b300ee88c2fadb9909dd390cb4380b32a42.0", "description": "invert an image.", "dist": {"integrity": "sha512-kZnjTGT4rkNJfdQ1DLbf7rKDvKhW610zDRRX71q0+R3M4n5FjE17HDIMsXt90HwVM91sffR9oCBxtTZR88cA4Q==", "shasum": "be475dae1115e9d86b797299d1396b64242ac773", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.4--canary.1163.a26f5b300ee88c2fadb9909dd390cb4380b32a42.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH8TX1X2yw50teGwQWOaXYykxmectoIKJ5ke1YDWBevQIgJ/zcK0Sq77Q3dW9I+Mzh4DG89VyoC4Iav68PBt9DfXU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4E56ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOGxAAn3ZVa4QcpcH3d8hrWIT9ot/RLaMMldHk1u9OT9i6kg/0kwrj\r\nzUZX2OxnZzCOWXsSOkStqvNI8YJ0OE4hglP3u7cshz92ou0G0BssfAR8I+nh\r\n0y5a5MvNi6tyLJLU/fPtg6y5BLGu4FAReC2FPrFKOVPdN93CRXT2rcu6Qrke\r\nlwWI+cjhN7eQQhQYSqYbN+9dTche36lMPTpxaAFzY6oEB6SdlC6tN4gTGFBw\r\nzWmR+RQWw9znVLwon2MsOQhnlh6GsBWOQo2sc7yNp9YaPHwsfrX8EZ1M5jt2\r\ngzhXs/Dh1mpYu+sfYJaLUrvHXv1d+PUoioDln8yUk0Oqw1jk/cviPWFSFUm7\r\nFCihw8p4JUf3T7fYWmO26m/2pu8CV0HQR4Tn1uQ11dZIIYveHnMQ1b/CxnuX\r\n4tEhQK2rRz4aT1Q7gjDXHToFO3OCs7sRACqELPHkF/mbg7oWkU+VK2vAQfsz\r\n6tyEOD22Lr3/qsVIXxWpSosHbg1ZLFfq9PYtKO3GNofgL8vdAcT77ECTE6d8\r\nnzhg/wUwBwgXFVva8BAQtESqcV2JLP5iFYQxoEZIQh0dKe+TEgN7Nwi6Ib4B\r\nb8NYj4tSVHmu2CwqbEcGMC9a5R//iDNR8B87JQCr+AV6k0r0qgvy1npqDZsP\r\nYxW9XLbDQNVuaggVOWRWPiAalY15NE2FpOM=\r\n=FUN1\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.4--canary.1163.a26f5b300ee88c2fadb9909dd390cb4380b32a42.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.4--canary.1163.6a6ae39e83bd731ccd25120a160a75b32273f9bd.0": {"name": "@jimp/plugin-invert", "version": "0.21.4--canary.1163.6a6ae39e83bd731ccd25120a160a75b32273f9bd.0", "description": "invert an image.", "dist": {"integrity": "sha512-owPOV8+XXU5KQA2CnKCnDVbEomqNpj+6hJgjRrTUYJsyv2/5Abljl/7oDUJnnWyPfFPwE7RyekalWof/DhaX9g==", "shasum": "24a2f1a8aa73b690b04c598bc7bb1f75815523bc", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.4--canary.1163.6a6ae39e83bd731ccd25120a160a75b32273f9bd.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6zQzi73Jk5Fp2io//Lb33IpFmkoT0hFlmHmPzsRcbhQIgKAVa7S0wkMFXnv0Le9A+2xrUNFuJxT4Ck4gWzESGwEo="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4FAAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRnA/9E8B0AfsRGcQiV+r0uaMrDg5SiR2VJtkqb4b5LOGPIW3RYBlO\r\nAdwMZLaK4wTsh0TkikhJ9qATgOZEn6wyNLKdk4aQbSdmCBMlIjwPakaH+Fcp\r\ntFkscyTJ+2HtCObeh8UpGaAKE3Ty3lxuUSJUmDLS2hnkBauy3YeWrkCpo3Kj\r\niyJPITUTSZWCyS1Q3+Um6FZYp8Or5bQK7H9uhrONfi6yxYDBrqLvsZe/26uf\r\n8smu+4k74YSpZJn5tGrzucS4EmsKfVRxW2qdgfm6rU6h7uYDqa8Nh6z27R30\r\nZOrqt1fOoUBBaeFe7v5eTxgOJ3zw2YeCB06scMdlGVrcJ/0bHV5Nf8c5JyOI\r\n4kTXS0IRUxvkC+HLoRVRMZO95Wn1lW7kkyhZNRyky0R6UHdpnDWJKw9yDVNO\r\nkWlt+o81CMu1Rp1ROQBq9FEP/V/M/HNxAKPB14KybjITTHJOo54lYRWT/Qq2\r\ncrVyMngLWxy7BHByvEXeXLzfU5VgzKhwmRwLRCAYqqnhdnccwzYbHBosnPW/\r\nnaahoa+9s3khUGD9iaHEi8DzWzvRTgnetmdSaqRlrNjChZsvzFyxwcydjzQC\r\njaJIydOhD7eIjrhcV7gUPeS2md9idM1QAmeoNAFl7WY+I53ktIVKrkih0EY2\r\n2xmxnN9neZw+N20AK88k2dMh/RGQ5UcFqO4=\r\n=herM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.4--canary.1163.6a6ae39e83bd731ccd25120a160a75b32273f9bd.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.4--canary.1163.7b9287fff1036ae67190d5ea3a8dec9926373a63.0": {"name": "@jimp/plugin-invert", "version": "0.21.4--canary.1163.7b9287fff1036ae67190d5ea3a8dec9926373a63.0", "description": "invert an image.", "dist": {"integrity": "sha512-VVKERhlpDmwtapIK+n5GmQKxCaTUmUVyGTfJCDZA7gQRvi8S4opwkyIPpFaagm0CWdXJihvJMKshTFsH/7pu5w==", "shasum": "50d76debf49080d2cec73016b7f1ed8b4ba9cf4d", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.4--canary.1163.7b9287fff1036ae67190d5ea3a8dec9926373a63.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE5G8jwizq7XftdfCgIYzjl0KOXn/34xXGrw+RUiRqkRAiAZRKQImuKUyDZxEi+3xejRhsB2lGqsYyaoAPDh4qurKQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4FIAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrISBAAjgVNLc5PTBXxLyEEVPVABAdzz8enq5blgGOEfGUve2YS/2Bh\r\neRAGbA0ZDZPmGJXv0IELesy4YOd3Vky4T7Sdd4OUW9lfG93HCZCoy2EbFs0h\r\n6klIGrlNOxgpobJp579mya7+P/g2xvxf1EUHYBibf1ExScWISSiMZD9ms1Po\r\naPnv0+tv+TTf0/EBIShJe8qorLO9oD4XcFoAzjhFkDk33fAd6+w71PcVpye2\r\nePP3C4P28Wql0fJ+UQ7kfc+4S5SwBpYKx5RkLycmE26bSE3sQFy5tnPaBCMY\r\nSjkL9j3/yOOlBP1k2io7fChNe0l2nLd0ARJ5KLJuhq+jXM0SQnF1I/ZA2uvx\r\nTxr0Op7Pi/2F2biXoHQUQs4/8xSbhCMzKG7FvWzQ0BGcShqoqGT6mQLEGvkV\r\nLBJZGjYuk67hB7GuHfZsNUmeLdWxpMb0bYBrjE3GmHMs+E81YimV+tey+eFE\r\n9JFzoyhEQio/tVRg4zCmbUl2bm4NSxg1UncEvnFGOI3O44hGHfgG8sn/1sCy\r\nfbavcblvYEEnPYdxQMIIvIMvnOeQRSE5QidXopiQUczlUgHB2udyYBC++OAL\r\noWLZDvbyKovJuYRPJKLwiOENxCNwgJ/qTeTXz/Po5J045Mpi1mtsRfLY98xX\r\n62iJDNFUijciv4jcoVk300kvYtxg7pDbldY=\r\n=3GKu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.4--canary.1163.7b9287fff1036ae67190d5ea3a8dec9926373a63.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.4--canary.1163.d07ed6254d130e2995d24101e93427ec091016e6.0": {"name": "@jimp/plugin-invert", "version": "0.21.4--canary.1163.d07ed6254d130e2995d24101e93427ec091016e6.0", "description": "invert an image.", "dist": {"integrity": "sha512-a45IxPXjQHbXKIMgpojFUhHL5d6f1gNVUuLbxRLZ9KTZk2pj43XYgDOM64j964tAtoOEyAhxgxDwd45N9+eg0A==", "shasum": "0f3acc320335ab875ece042689816f4b1736e9cb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.21.4--canary.1163.d07ed6254d130e2995d24101e93427ec091016e6.0.tgz", "fileCount": 10, "unpackedSize": 10530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICc4vejySwSdR5OnAQJK/OuTMpWyy2ZQmhV7dVlWjjIXAiEApkPb9ox8Lm3seGa0n/1BJoctrGD4n5iTHa/i/yJfI5U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4FOoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9DhAAg39aHFq+pHaI68T+h4v5521O60pni/U29wZiBri9/oHrtjxM\r\nyOOVduR5Vx3hyxCewjOAyGyu0S3OobKnHLmfXITDLpTUSC6yYR76SIofG8ds\r\n8qugW5j9CjrO0+bxAIndx4zJ/lP8O3g85xys/lNQNhm9IKwkpXhARHJyoW0j\r\nWUZAa5RQBotD8d98pS9Krm178jNdwNEqV2jqmz1HxptULzJroSKDwD8vwcaW\r\n4G2tn6ZVpJGHjaninmZ1hXG/iEPUJuLisTUm3bb8DYHKnjNkAk1qIEZ9gi31\r\nTPBTz9GaHcxKr5q3AA3jf43mWIg9TcF5Z2RDF3nMu537zAdrOLKYaUCJhrPT\r\nLjuVlBtq/x5AJ3oef/KsJI1K17dotBk0xcTwcujG4UCnmPJRMYsBkCp+M0fT\r\nqEOv+4zynASu0QKL9WESjkDQAJMe7yXXkYgP/GnA/aCjbI1Vxh4bdeoHAEtt\r\nH0Gggjy1q4Oj+vKJegTZELSRGDhkzhb7APiTtxBMCaa+elsItgHr2+L38zeT\r\n/JqNRJqDva20SSNYE0x0tcOmcswegfTnaZwosRRhxXaepSAOChIZ0FGXWSdi\r\nM6IjqIEe0Pzu/li9G/0hurvW0qZ8f71PSLnORbhFg8QCdX6AVMp4cqGcLTuM\r\ns1Ge68eHc9/d7Qgkf+fQWDwYrgX9EKAQtIk=\r\n=xOAu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.21.4--canary.1163.d07ed6254d130e2995d24101e93427ec091016e6.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.0": {"name": "@jimp/plugin-invert", "version": "0.22.0", "description": "invert an image.", "dist": {"integrity": "sha512-+uuO1us8XVhXL6J9iWUnzivRxZxzoyUprzZkH856Mw6VMvrW3HS2Su3o/AA4Do27KLp/oVEhLI7hI/lUC6kXqg==", "shasum": "8ceb5d0b383610190c95812a1d918ef938bc29a7", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.0.tgz", "fileCount": 10, "unpackedSize": 10419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0GIOY+MZ4iUmxIud+THs2KBBCzsqdsAGa+iEQSt8iTwIhALkCKOwy9dcOsEPEhgwiAiqchKGIYNvDm1s+XY7B3eFG"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4ICmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEhxAAgB1NzkotpLGCV3BFPIOWTYLtzQW903reDQnaVQu2G+xr/Xph\r\n8Nz+CUsYevf5S0WGrZP6ShoKKCucBrnZSjE3CYl/cw8LHJVGOgPE3WIoAjxy\r\nIphxOLwff6AVzQc//NESEHV4xxKWC/eKacXV6r/J9BjNJS1tQqDcdLhBocEF\r\nk/qQMlI8RIaGUfdSROIejoSvfMGV3pBIZziJm7CY/yeIa8Qf4QGVr9zsszda\r\nyq0PBNhAgRMonle1FKQkAj4wgBuiN0+LQTc30MwDvC2plfBdECAEAYTPZXsd\r\nbRepbyeIh+5OMXVWEs2FYiF7SORMP3CstLRlNxhF/JjVfERE8LWwR0fBvvGp\r\n6pxMAOVm1SuKnSAJXnXY7olFH59Sb5TFuNjVxD8uwk4Mc3d1YrSEqe2v11CZ\r\ngAoJ9X4n8sJYD60rSObYlqXXL/hwS95TvPkfKRIw8CE5F9txGRDSOq8cnWJW\r\nlB/naAi2w7PpL+qGVn007TLCaqBbfHI1co8FtD0QOD9nFKgWuqx3lElP1bwx\r\n7gFP+yMoeykF3MdO16o5zv8g9zo5I+0o5+fRsWxzp7j4trZ/YaOSd7h1thuP\r\nhXQwzD4X6GVfC1JVduQIUL1my8EhBX6KeJsGKQx8wCFiDr2LyrPZ7h874As0\r\na5km0/ghqMB4rpDJI89TFqQmSqD2PjDOQQ8=\r\n=gayu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.1--canary.1169.6db477d088d28aea43b31d9ca8b0eaf74dc511d1.0": {"name": "@jimp/plugin-invert", "version": "0.22.1--canary.1169.6db477d088d28aea43b31d9ca8b0eaf74dc511d1.0", "description": "invert an image.", "dist": {"integrity": "sha512-iADRjFdqpEN1KUoOQ2vRjP8brkDX5Z46cjsKaMPBTrAphlMoqVCaCGXO7Yd9SK7sdVHFcsUYTF8fZJjSqKJ41Q==", "shasum": "fb15ca3f73dc7acb4ac77a78c268d873364cb094", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.1--canary.1169.6db477d088d28aea43b31d9ca8b0eaf74dc511d1.0.tgz", "fileCount": 10, "unpackedSize": 10527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCePwTDuPbLcVVOSJfZA+k0Q/PBBppmqnEZoG53M8Sm8wIhAJTKYPg2AkN7yUPO0dGc1SmCyo5FKquOwUYS/IATx3QJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4JRPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBVA//VM4V2K9PA7C6QopCgDsYjDCBH0atpgFrIk6dlh6mvj/X3OjW\r\nOxLlzoGXR9LCZsHhnRyzVF85boPFtNF611j0x9+FV+Kyolyjt2+2NMo5jfXa\r\n1crIyLMYeU1g+WsNcw4cFOexZW2AQX3X43ZHYsSdtu4kD8vr9+/BawmdQgw9\r\nNV+p/frVYDScrJpurYn1Duf1KfWMIncfqVJVaPgbc7BYdyfvEphq7zU4TgC2\r\nTfz8e9DwjrrbBdmEIFlG14Kc5vun6Fcq0Z0mHweBYCfpEAsNRqqlRosm7HyB\r\nCbmTcVnLphM1SOJ7RS5OwWowg7MR1rqQpLeKoqAkcFoe23qkDKZk7nBm7Y43\r\nuJumjwO2t+aNL90f1nmliOcMYsz9LoJRAHWoOf6WTVym4B3BkOEXmO0J4Ryj\r\nh7RyRU8cbZHY0vbiNSEa0iVJgtOwDab1fc0dK6cMT06ORzgJWFeWqcZirgev\r\nTkB/FR7dgAbGDoTMmFld3nDg+wS5/dmHGyB+LS1vkPonqYby7tLW1qVjPCf4\r\ndR4S02aTNWaCfJ74ep3HUqSycc21uwK7XQgVkAlK351f7BVdNS2z7Nxq8eWu\r\nQQnoArLPRe3rRHIfkmddumlHMkRHiMO8jcMeOL8jdGHKtZj0oWbqnMvg7Yp9\r\nvUHpoNRWK63Cjj8VPlvgqtpNiZzgR9dYtdM=\r\n=k1x/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.1--canary.1169.6db477d088d28aea43b31d9ca8b0eaf74dc511d1.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.1--canary.1173.30a931db5f6552c59e4c99f41fdb9e2b9ccf8291.0": {"name": "@jimp/plugin-invert", "version": "0.22.1--canary.1173.30a931db5f6552c59e4c99f41fdb9e2b9ccf8291.0", "description": "invert an image.", "dist": {"integrity": "sha512-X8gtAVzk4Axl5+fQGZupRGOCSUAN14uU5jk1hVQILk4VLgGrQeeqMimgLr2eMJo++tTqdSRAuZSP0R11q2hXvg==", "shasum": "01a30f14dae0997b3814c907a618ca9273c2710b", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.1--canary.1173.30a931db5f6552c59e4c99f41fdb9e2b9ccf8291.0.tgz", "fileCount": 10, "unpackedSize": 10527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBQOtumAC+ZdxEO0FlFAcyYGiHcapO7KZZDm4IiUCJO6AiEA6l9xYaykgZGj90eoq6HxY7obz3PjF/ZYiXDmWK6w324="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4MS6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfZw//dw9t1jKHgQTroiwxNKvIF+XQ4nBZPlmOD6X93pAqAYW/7Zdn\r\nr15yOHBEkw2fB4FgfREIB5wTaXJv7GOcPcim5nc9vuf6Q+xirwJwq8ZjXLX4\r\nBJPFMYguOFbdtVwU+9A+T0doqMBZPRGGobogs39SArFLq+IE/+9BxljWsUWn\r\nHwZ7NPizAQavkpImXyJXwahileCRX2m1rAYJfn9aCyFnNuyq/PupU3enFST4\r\nBi7U8X8dzbBwadg6BN4096H6m9wy1wEyl/2cJrOKkGb7MpV+g6qzJnKRvMkP\r\nJ5hVqUguDUen4kJHD9BKvY7MqDxafEJUlM/mrv4e2quvqpiWfYX6btBTVha9\r\nWIhNwlKuxAL4AinP1mG67+vPjcUIZDzrKPcou1k7MJxQmDX6c7ikhnVbJx8c\r\nWVAuDw5c23GHvgik8AbBFro7nOe+KiHNZsVpeMCDOBLHgizSmWEGc1kIjUZl\r\nd9QyJBVelxymyCuUgaDw0BTFqee4tPI9E7XKaZcemBXyfv8zAny0toPkGBNo\r\npAOwKcQb//Bn1xL2Ab3k+pW4xztExKZGph7PZzqqmTG9JdOJPR1fSOWd6WzF\r\nne7YbtVtUP3L97VKsFa8X2xQj0psvxQKdwullmQqv5Guoo1S5blem0ULL0cV\r\nGsJbMG4sad6D0FxuMS+3HBQhsu8LJTmw/Wk=\r\n=eEFf\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.1--canary.1173.30a931db5f6552c59e4c99f41fdb9e2b9ccf8291.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.1": {"name": "@jimp/plugin-invert", "version": "0.22.1", "description": "invert an image.", "dist": {"integrity": "sha512-zN7ZrjD87KBS3h6NVhULkKwLEHSG98D5YCAeCJMSkHZmPR4izBH+MuhVfcwz8yQ8oODo9gs42qXrDDNukGgWdw==", "shasum": "f07d9beb853f390dba27b69abcb929244c754648", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.1.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdXVXwdS/iurFzaunCWH/72JJ89Q5HJlL0UQIWpqIWQAiAKYBrm+N78jnVIqTWH0JNErj7GNSio/Zm3eVqA7EXvgA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4MZTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfCQ/8CUgq1tLKLob8Fy5dfI3682JlV0RZ8dl402//DDKYAk75+1Q2\r\nmjAraEzGXd+S4cLVZs4MAAvCPDpHgF1UGlbEsyqsaGAV56TMH2roUEwUsKqQ\r\nBR9W7teO4mxtFk+pnHVJAJv6zel9Zj1G93LMFcxO3QT7VJvho33yt8D6nlhv\r\nMDNoy36Ah7dN8TtuTes2qk34wPijB5XF07racqeIsCB8alM5NhgC8de5BTNR\r\n7T019nAEN+uh2jFIfXyvE/DvscOTR5wseh8865Vbid6R1rN3iKp66HT4V6Wx\r\nTQz03G3iLj8kEUlqhWakq/qbIqY/Q2/O6U5TtVfvryKX4OrdIylQQ6JGEJwM\r\ntUg+6/tpEopG2AM+/xm44R9OpzLZi7DGkolDPWbsG3eP5yYpgkYC+Bfkjsvr\r\nZFPJNLYuzLz+yRJImOrDzVmD9zPs+sqzKh8uoptCQfkLCBRg5e7SKdzvo0aY\r\nBjNbTAn8AH78DMoup1smf9r2AWp+01GPZe39GaTmumKWKb2N0DslPfneJr9e\r\nlVoVKiZGN1PzG3D0HqBDfQ09hqxBlAItG5QTrCYz20jGvSzMq1i7kGiwBSww\r\n1ykpDqXbmYzp0wQ1/ZSg9vfRHGUZg3MfPpmlxSE3JNb2m+TF8+xz7vPFfRfh\r\n4pdhEFB8FcVuMwMy0Jp7v0HPb8ClJrHFo08=\r\n=yzRL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.1"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.2": {"name": "@jimp/plugin-invert", "version": "0.22.2", "description": "invert an image.", "dist": {"integrity": "sha512-SsYoFghhrnFGr3oKBr9P1bLVAfiCYhEu/M/p/tTLXVMKOMiqcMcL7hixItblMa7NNF4n1gzzx17Mjrp6P2qcJA==", "shasum": "ecb2838b17ebde87ad51eb3812bc03e4917183d3", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.2.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBApFCwDx/YhFa5T6UZqM4kvsW90YxBgubdp5TwoV2/VAiBzJmbmiICHUJAo7C0Uqs+i1RT7GIEp3tCXR8+hfEgHlw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4WTHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw0w//eWkb3vnd0PCYUSXgbR0wvDhLI9U1d/Dcq9L9TZvIvp7Ox+Ui\r\nKmc1psrkvhmV01RO89ZDLQF3cQC4mFlhTYMOxg7AMcRCRXjVns7lcq7CWDpk\r\nwVu9xn5lHrDXYVbAIC/78hXVESjKWJIjPI09X7ZgWhpgelZBjFEdsJggXO8S\r\nFnyMK3yav0gih8hhAi5cs61ma4gBBTAVonZyMaPlIEUooxQ2YnrHVqgmbv4M\r\niVAZKAHbjnms0PsDOqCc37/5wjDnfZafxLr1QucK9yTnX2KunMcAGIRonjZX\r\nVoh/w2PjR/fO2nBDGj3SuuK0XP62VgTqpjHouLjxq7rFjQ/H6vGTQVNTDNMi\r\nQZhyiYFDyGC5+oS1AeHZC2JZZvc3nuJpU3t4kU5IeuEbYU2ZwRDT9cW+CeV1\r\n7NqTVthB9nASeO5vKlUJvfQW9+mcaCToprqi09692e7TGMip4Ohu82k1vsNB\r\nipm4+6tP0NmqV6QY9wHLx+Tb83mXNYf4MlUkUkxsFNYg9j1A3ulpT9/jhPTP\r\nHKEaMjNIXARqVjpZKhLNO+EDycQ0Q8x9QqQ3QvaFRyXsLeDMdlFbXsUuv40D\r\n7RfL4QC4QmklVt1uVrcYW4hmbU8NlD4MzgtVxbY9VUGKum0ZslA9zFR0ochA\r\n9Xxc7TNCMw2cf0sa4aw2ep5dD+cRelRAK+g=\r\n=lq23\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.2"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.3--canary.1184.ff6583af74ba85d9555c36db2844886e269feb16.0": {"name": "@jimp/plugin-invert", "version": "0.22.3--canary.1184.ff6583af74ba85d9555c36db2844886e269feb16.0", "description": "invert an image.", "dist": {"integrity": "sha512-zYmXaqhEd0mR9K05JNOKrxsCvvGIzftEqIbybvFHF0DRBA98p/VWCjUBvREpQqHXWWO/wr14iuDv2QLXY2S2Eg==", "shasum": "cf74e26da1a674a535b480042e1989b5938d4301", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.3--canary.1184.ff6583af74ba85d9555c36db2844886e269feb16.0.tgz", "fileCount": 10, "unpackedSize": 10808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTjBXgTCT/tl2FAW4DJdNmcAbvB5sj2gTVl5fjnpcuWgIgSx7XynCtsbGbg1MfP60WzP2ujnlz8TmvOVjJoKqe+uQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4YVaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEbA//VJuVGkg8/9goalh/2lGOe6Zbo1TEXHNtGfznWUvww/VcRN3v\r\ndv3Q6BLAkgUXqaGhLnyvOs2jq7ycl2JNnfqqHWBJ6LEeYqbNO2bA9/YRd+26\r\nJtY6LTnqnXGhUxYO3oeUHj8fITqRLTAMplFRyjaAT0ELPTeOfnrPZG2quzDu\r\nNogL/RRlNoKXb3mUU2Twcks8Js0eMoEDhQyg0KL/BRW365CgPs2sNSH6I3KA\r\nG3IWoOVBRjIN4wI4uajQmbKGSuZ6QytUHrc8rrbfAjSscHcNfTGc6EDWBJbx\r\nmu16btvDMjWr4vxmeWYijQ8qgfszUY0PSdWIw70w+LdpQKniMYuzbvRCrxbm\r\nOVq5qctf/zdQKItCGLQyKXxE45nN+LCvjQsxDWmj8S7Gn0i14kRcQ7CEs0MI\r\nLGLwwc859Il389VFjemORBpBIDtlQ8iZkyvrlERb8h3VGvlZMPWa9mHtd0Uh\r\nCbImkZ+KcgBFU89pnQCgMCu8+kgbhfBFRKNidhTAUoO9wuPRHCsqmvdJC2Qt\r\nAQNsRk4MK6Zy9n5llZBJgc6SOUB9S46Sy9/EOS5svGL5Xoq2WaoydgrH6kli\r\naoOFTK2KecAcJq14snrLgAUPF0epRtFIjbRSnzYqQottb8dxo586v6XJx0KH\r\n6q5eo8MVswNa+Xo9uLc51piEISdiwgR16vw=\r\n=fJJ9\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.3--canary.1184.ff6583af74ba85d9555c36db2844886e269feb16.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.3": {"name": "@jimp/plugin-invert", "version": "0.22.3", "description": "invert an image.", "dist": {"integrity": "sha512-phWvWHPvqBDJIY7gLqyCDhqUGdLXXpMa2tsR6Pj9+O5K+WK03bIglLrRfQL4SpHK/1X3mgq+/Zu6amqmws1tJg==", "shasum": "6e7b48d774bf408fc33e32a2de139c8216d31545", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.3.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAC0mXZ7LqwJQFhduIMXbs/zXBPcVtpy7+Dj7d4NlUnfAiEA3c0Zwhx6lTXfGksLsIfXHvz/fiyDD/07dHwznay6/YE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4YgzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqq5hAAj57PY9o63cKA1o55iFhxkJ0nFP1QAkDOcLRM8B/Cd1qV24HU\r\n2ZZ/jD6k/XYrX7oBYR2sthZbmUU8S7jxbQtMOQNLeg8IvkZ+FNEjq7jt7l2Q\r\nYTqwTLPgpHtpzqW+2I+JJ1Jbx2m8zqoWmRHHhTYfIIdAJCO69+exaIbo8mi0\r\nLem/nBM03lSflD5kbE/BULuWulQkEMGcT3JiYhZJsauZmDMnbCVHKVHFyKme\r\nqXs6n0ZJClfxT08T2IvvJBwL3dcFdWzrodT45S4pJIsn61F/U6pAbXdMkgq9\r\nk+QObpNvpZaaRzgkASrE065peogNMlre+sFjCgXt2dXrUVcGjY4z+U9xoAcp\r\ngelFIsMHPnMoQoZR6fPV5TbZvKuruf6uvh8nz0GpnDTPMebOrID9xe6oMOpA\r\nZ1a4GmNYNY8nujrcHHP53SsfctD94STGzOCYCZg/7/DpqbxLLC8v3TlyyBeI\r\nkjBM1f5jbpQ73FgE2RpTymX1jMpj5VAj7sIcZBrgZYhosEn3D9Dqk1y81ab1\r\nti/o052rraEwY6fwZ8yb0ZebkUGg7DvRvkZk5s6pWsKE7SHcWjapX4SJXQBj\r\nv/KrdeEW+71I3p57J5sHLnFRkrXC4PeoYGR9cHTZ+HZinyGGZdlv349FsGb6\r\n4rtJSmHnXIvoUWJvLyd878gZ1ui+GLnX8yA=\r\n=0fiT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.3"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.4--canary.1187.30d10f964404705d383d7163b7d3f85baa2201b7.0": {"name": "@jimp/plugin-invert", "version": "0.22.4--canary.1187.30d10f964404705d383d7163b7d3f85baa2201b7.0", "description": "invert an image.", "dist": {"integrity": "sha512-A6HAT4B1EUXMTU6DPoZU+tF21GYklXZI16Zq7sKCtaSxBwRUiBAaLac08BOjL3y9AQ4l46AX1eQKAKO3MU2V3g==", "shasum": "6bb4f00a12cc45b1f5d2bb568ceccc4b90d500d0", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.4--canary.1187.30d10f964404705d383d7163b7d3f85baa2201b7.0.tgz", "fileCount": 10, "unpackedSize": 10808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjeZh8YuvE2vkJIoawOFxkqGM24mkHhOvMcUcnEAMQZAiBtsh5+R0qCvRUKTk3a84/3A7OHj7HjC1D1Gri3TjmQwA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4bTJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqbbg/9ExSWpAcC+1KdRiiDEfTHLsdgo4pi9P7FlW+7QruPc4Gp9ibr\r\nQy4KlRqZWyPiQSNWv7Pnl52h4x9lT6IPtPvGv+fBLKaiycyrWt25WWYoORRy\r\nqxVlnj8foBMjrJiL0MFIuyNKc8RZih0HDwMayf9F3LPR0CbofrV9zBRfNENV\r\n2zSXuY4a/U18eYDSSS+tixzuDspzEt0i6CaRB/hdOTsn6S8DeW5fvuadsSGT\r\n+Q2slsXRqHS2vxchZAErxVgHzLrVgjEPRS9ubA68ua6CIsDF5QIAvY9JOnWN\r\nYqZTOPTiApd0SlH+ipE8C6zv7v33WH1HsR+VLWChxZCdzfX654GqHXvfdsYV\r\n3rjGRujzxtZrXZbGZ4tqc7rqn9bQkENFGfVoupW/RPbkTwbkOUd0NWKkiBaY\r\nNvsfZFhQEDHqgQiwE1L/mZCkz8RdYQDlVPD5nslIlxoxWKKZL743pDpBT5oA\r\nskZgt7gJLhc3Et8qmjGn+CEPSb1eVk3uAafJMpU65dxDLlw+EcICILZpIa6p\r\npSWa3UYrTcZ3OlMlSfvYb8YReU8P0Z9r8VoxhZ6X1JEDWjxJlp0iSLZzR20/\r\n2c8by6Jcb6EyYTYAIh49LDIvVQVR2egyt5tq8zUPDBtLnqrRZZQlxHbcDizZ\r\nAIYbmzXzLPJHsCfIRi5Mz6oWFa9tjhIbyJY=\r\n=W4Ev\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.4--canary.1187.30d10f964404705d383d7163b7d3f85baa2201b7.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.4": {"name": "@jimp/plugin-invert", "version": "0.22.4", "description": "invert an image.", "dist": {"integrity": "sha512-/WtZeLrF+H+mzbjqudeGvvSxudlHy1kyiP1gVWDxhYNQOuZJI57Vn20kSTYvHBNjvy31LV4/uestyX8j8tE2Qg==", "shasum": "4a345474b006cec5a553303ff8e339b587a5e35a", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.4.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDyxhUSgOcB0ZnsuIzeqNGdwq7HF4WIP3pBAb1aEY+YtAiAL0Gszck1XjdJ/mIjhJDbEQmqpoQ6KXHCzf3MtkvTrhg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4bYgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrp2g//SeMp79Sd/P+hEPIP5OgRwZhq0IbKDU/90rC2oLiq4p4/FycH\r\nHVV+Sdn5hlJWqh4M9+OyNResdnGfyaviat3EOddl38HUUVlRpJZuqktFziEl\r\njLiB9ETk9/rYm8hQruwiiDDex5mZfSnOXUM6XE9lD6LpBNg+XFni18g2crvf\r\ngRnOiiVQFXDlYOm7wBeeTRmCecE2dXs9OLFdSnBniHVO6xU4WGz9UG0THPSa\r\nsfR5yBW+76wrQYtwYnWJ/VNMPpb+LOwK9IAhRl5rKbPCddzfHTo1O+z1tOxz\r\nbyHiJpNUNDNN0/IE12mpVLwCz20b0wfGuZhbIvmQVlpf8aLMmQtnBguH4pUh\r\nFsqjS/yaYU5j9wupQjruN5vibE1hkJVrgAKq76b/YOY7ZkAklOD7BBuJ52R1\r\nUDWNH2PHI1n8zrcmhdTuSYYA0gCAGcZc0g4Dr6VbxkgCrQDi3Qdp5CqNh0Ms\r\nr1b0vwF4DiDDDmpe+c6ICGm8ErUITYE3KWSfcxmHdjEr6Olt//ixEiSn0aFr\r\nEvTZCoXhQVwSaS3yhCuCQYcBrOtVIZUVUUpdr2UOP38wJAe5SXYwNEC/IYsk\r\nUX8GXq7UNTDIUUMWhtcX0fPq7QphavO/XiutBvG2ZaUJ5izgtTYcJCHnpIKd\r\nP4ZAgrrG1WZ5JMxS6rd01UwyeuM3DC8vi/c=\r\n=KQe0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.4"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.5": {"name": "@jimp/plugin-invert", "version": "0.22.5", "description": "invert an image.", "dist": {"integrity": "sha512-Z75QuKwraStsvlPJqAD20VabCTALqDHY7IhvRrGmKtuSMg46IXXYkaMhoAhG/nutDI89lKKSD9glvKRuaduHHA==", "shasum": "7d7f2a1117a5e16c2d184f39024b8d0961f03b80", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.5.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2Hj40zLLaBJGQ1dvSP0fr2sdT1rpvSNdFlJ3KTJtjPAiEA0GVIGo4vYtDSRePsotujjt6aOxHwvFj8T/pUzSeVt2U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9QBxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY2RAAhYaZM5ycCHpM6au9qXOF/j8CTSRL/iXQaXAn9p8PACdThYFX\r\nksz81cXRIet2yFAeXAp0dMW5HPtAyS2f/HNsORCsVGrS9qc9iop66S0WXgir\r\nyEHc8qtWqcDSQC+pgBeJ6bzYlP82sFN6WtwP6Nx3JMif+9XGwO3TI/Y5AJSz\r\nE8fzLLT9KatvIZ9OAhUb9zraaaBf4yXB1hn7tVDyu5bGpIYSt6lHSpYyTCQK\r\nBJ8mJ4jYUixjZZZ/kIRkmi45Cv4SP+BAjG4k1q6H/htn5lQ3oPBUUJdykGy5\r\nfhK2m1vQWwWwa7vwMz89v7VjntRteikbTsX5+tp6NK0r5dT/Dl+8Vn2amgh8\r\nMHPKaOoJb0x5lrHOAMeOgOq/YQIMMvID+A1dwac00ZIviLIF9OIGoR7Wsydo\r\nWIrYBJjlQ6D83F3Jg1a64OhoasULDq3vGlMoGNPC7R9fnlC8KBTY8VVYzcgj\r\nyow7+lnoCrQyxUUzz8lSqMjnSm1Nzpo1Rmke04/i0w2V13psLYWvcuaGKxnX\r\nKOdnBx4OY/4RhMRi60g2Cz41tEuK5kCnKLrXUJ1an+kcHW2bj6A1JCCUnmB5\r\nOuY1ZRgBMwC00hwvtmsXf/LGW8nHK4EC+ut1OALY+w1q3AEduxVnx6CRgCyg\r\n1I2x3IqtrGpvLGV2FEBAHTeVZYTc9Ukp5hI=\r\n=RU17\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.5"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.6--canary.1198.9324a88b87a1fc38b528af47ca707d97593dddaa.0": {"name": "@jimp/plugin-invert", "version": "0.22.6--canary.1198.9324a88b87a1fc38b528af47ca707d97593dddaa.0", "description": "invert an image.", "dist": {"integrity": "sha512-7QdRHUN0P3aWOzUnWckFWJYoszmhbTzLXdk0tlnyxVW8pecNQgH5395j4swYVnUebMPWGw/oCF5slOAX6xQS2g==", "shasum": "3da1eb8e4d1a530d7ea9e7f42c3b6580444542fe", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.6--canary.1198.9324a88b87a1fc38b528af47ca707d97593dddaa.0.tgz", "fileCount": 10, "unpackedSize": 10808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPlpWTeM06NGV1nmKABXAy7HRs2SPTb5WWMT9tznyfAAIgQDs0imfc4eRBDP2qgj7vWXOu0iG6fry/xtVd7T9UARc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9b0XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZGg//UOGkVmlqPtj2mKs0fgIyjPht0QfEVr9ocHqyAQqlPK0iAsKe\r\n+rHHKR3QVv1yKm8HPJF79xxl4mOw+eVJw88fsBHsokA5/yD/XCjCoKhdYY9c\r\nTaT+AzFv71YYC6jn/zCNQu7kRnjRUS1Dx237M6JSqmMkWeZj78rGyu/RPIfB\r\nP49a7HxXVX8ee/4IS59ChuB5QWfMCsu18c66aKfCT80cZjAjbyIoQuKYixTh\r\nDYAjX9HBmO4JkPJOYS9130OH1kYtUVoFRFdmAIKBTj5ts6eyq9uGB67RTVkS\r\nXnVFnLH/98rudEH2yiCdtG2+HCdd8pxpFzx1SuB33WraWWtkUnJhqMKP9LZP\r\nrr+HB3nwz1HWaOZTU7qRYjFx7gUD/6jU6NcGdFD8XYpPrCkE2IYDz6tNPkCB\r\nKf+u921kRp760awPFshYPWnoLBz3todB8mf11v6smIiMMmhr/OsrlYoYYkem\r\nCguAIvVQRIw6W0wdoCvrNQ2dMxnBFBsE/8OaFXPeUBDUlqiUGXI3gw5q44s+\r\nS6KTXY9p5Pm+fMiDWm53ZEd+YkA/gHmhDS70nCgnpk+HC35lGVhdEE2kWzUp\r\nK+flN5IWCHNpeKfkZWHqlsvjEY2UONl9WivgrPTNbPSOMPEI4Zd8S1AFO5gC\r\nJcqCzWkNRP6My33S6YVhkvHSOTK0pKisIMI=\r\n=dKIh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.6--canary.1198.9324a88b87a1fc38b528af47ca707d97593dddaa.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.6": {"name": "@jimp/plugin-invert", "version": "0.22.6", "description": "invert an image.", "dist": {"integrity": "sha512-RUYlCb3jnXn5HodIaVf7JxedzaB+Uvn1/dvioAW+DzU1yW6nPY4J5nZGy2Oyv9a2cx3zPjuLmw9CLevpB6c9SQ==", "shasum": "351c5df63103ff5f2d66464744030211bb186584", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.6.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgCKSJCouSYRbWu37U30M/QT16Qxb0hcGxswuZOvNN1AiAS+uQlglDnF21PPknztat7/ZddNnLEpn6Yr5JgSUe1bw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+RBiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIIA//T4duuMEU1PLkwwXvO3cVMm3JvF0mgTXNPlJobzD2fY4N97is\r\npQe+WsUKFRT7yQ9HEPG87GMcRcsecEbWUi3C/nVi+LOfMLj5Hks3q8xV6qE7\r\nBtCJY1Z+5WqAyIYb640bw/wjbYUl39m35Wn9zsq1s3eMpNkwpOnj96xy4KxO\r\nfow6z1cmOw4peEiXQLUNayndHEc4AEiUeyhJlUkaSv0MAyzwrbI9pWilBhlN\r\nVEIW5OeWlZzUqAyHgxbRQh+8uvlf/aI6/maVsgSn6AI2LCl7Z2cpnVDgWCe3\r\nSKsO6iI8uXN9uFcVrGTAhMrs7jpfgtc0BeJHLErdDyfwHMaYo5Hg3zjrxJyj\r\nYlv+uyhz0qdEXSCtoVsWZ1sy7aeKm46HdnGWZbdy0qn4/Ahi5m4HY4gh1nac\r\n3mut8FE5sPYGIQTxLWcv+1OMhJby7xoUzWdu9umZvtfxV/cXFBPSq6VaS5Hh\r\nyEdycSQi6/KRg15yFjLPFXbCoyobKuu5MDCf9R/kx7lCjZ9GiDTNUcJxuV5f\r\nyGMIoQXGKOd2HsuqhstO92droQiqOIY4++vrmBclHi3GGMshY35TNDidJIg0\r\nualGMdyEz4SakMyag2GzDC1wHLMhgeOpTPrR5m2ORoRQNFkb38EqN7O/A+kQ\r\nMhh6K7UwY8Z0sN7dhnkPOdLL4Pv8NaKMln0=\r\n=VR4X\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.6"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.7": {"name": "@jimp/plugin-invert", "version": "0.22.7", "description": "invert an image.", "dist": {"integrity": "sha512-dX/TqACJ/M5uXDIEJlVPPwietMD6EWUeA/CV4uvhLz9EMjTgHociJ3TWqGCY/70phhIBLbhLcHUVBL/q65ynfQ==", "shasum": "96bfd07dbb68ad9558b5c6d19b6476aa072523f8", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.7.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyHNLGKBgYQAm0Ofx91qEbU+XbANwCDrNCcUTb5kIpnAiBjYdLTeY/SeBk1T3Sgb32H1QLvk46fbPHgpPysLDU03A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+VrUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpnaw/+PLulyOwhTgWUZZF7TfVATR4s4Iwd7abSxKiAKgzfc6DNBSDk\r\niwTXIPXW+dh14MCcziBgWEx3rBmhKCS9RLWTxhC5cmSrM1E8QsaqY/PnqGVi\r\n+wn6ETcVUTBvB7IYmXjxH1MUij2OGAP52CElnCs/WQ9vppSTOq0eeULlUFH0\r\n2r6FWjwl20x0Lb0A6HAMhwnEJqhRi0wYrL+Wrv7j7R6I4TTfO76YdbcF7YOF\r\nljND0qIpBpwKk2Qfr+xh4gGuMYt83Pks5GYSvHyN9mJ6oHvXWe0+ZKKGGqfP\r\nHCAYnhxeTHCA5U5ydOgiQuowZoOdHi8cKber6/TqWHsyboD5uNbNKPcppxMZ\r\nd+bQNd4PutEAb9WkJBf3/BqkVN5YUyolF73vkSoQBbDF5Rtpk/yry95PI3Z4\r\n9YTTKgfQ90ZUlDxfDENqO9G+c8PBxd4mJLLSV77PnCfrPX4cWcy+aJJLy5nV\r\ntIOevrK1QmgjI1cCDLa494mlxAqaWRA3k4CDfelojTMAEkyHxU7zTrhSEcsB\r\nd7mHQXNGBsIqcCB/cK2REyMZKefJchCZALVY5OZALkSEzjewJWe7Ml6qPFBG\r\nOnO49h4KHqRsj3UiNj9gKm6dmCjt/5Nsdg/4VZMbHlWfyguG2MKy0+4Co4yT\r\nURr/LyDOXe8yS0tFRMfKTQmygqBERNUk3AE=\r\n=PzMb\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.7"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.8": {"name": "@jimp/plugin-invert", "version": "0.22.8", "description": "invert an image.", "dist": {"integrity": "sha512-UauP39FF2cwbA5VU+Tz9VlNa9rtULPSHZb0Huwcjqjm9/G/xVN69VJ8+RKiFC4zM1/kYAUp/6IRwPa6qdKJpSw==", "shasum": "d52c4c735720359d942a600827693b2e097c61a7", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.8.tgz", "fileCount": 10, "unpackedSize": 10697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzWmcxxUk3aCyUTPjinTb4RI4UOlaFivFcM8OBqwSESAIhAKYbiLtzVSLMuA2TEOGGTtoWUxpHtMQMhgWXXzERTiei"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.8"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.9": {"name": "@jimp/plugin-invert", "version": "0.22.9", "description": "invert an image.", "dist": {"integrity": "sha512-nDC+rHGHpI/YZaevWkMPRbmX40/N/wURUUe6d5NakqjliLOlUPS+PsbtfT14JffCzXBeGqxoiBHmr4L+4mfuvQ==", "shasum": "2a741c271d87bf91560be882372cf14b7b2c9c98", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.9.tgz", "fileCount": 10, "unpackedSize": 10744, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGklhdNtEnt4QaXwvRBL0Iryzfd9/tzdUmMDCaYtWQArAiEA5aFSsiVr3CAtxd3zPVQwO5+72MScFYVmUVOnuNdpLJI="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.9"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.10": {"name": "@jimp/plugin-invert", "version": "0.22.10", "description": "invert an image.", "dist": {"integrity": "sha512-d8j9BlUJYs/c994t4azUWSWmQq4LLPG4ecm8m6SSNqap+S/HlVQGqjYhJEBbY9EXkOTYB9vBL9bqwSM1Rr6paA==", "shasum": "370b4ce4634b3e2c5fbbe88270a74f5673c765bb", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.10.tgz", "fileCount": 10, "unpackedSize": 10746, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpzbK1bHHhBOBg8YVyWTsrbWwPQ56sZuHFcCBeAgFkHAiBDGFyAsz7VOUzzKCuKmpEc5bdLQfiQGXrZpRA4Gr6vPw=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.10"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.11--canary.c55989b.0": {"name": "@jimp/plugin-invert", "version": "0.22.11--canary.c55989b.0", "description": "invert an image.", "dist": {"integrity": "sha512-XIIQC3GT4CTpn1nd5qj3djffwtlXt7c9GfQ+6XcI7v9wynu8mqVei6PKK9zuCXpO4xGAemCGpoTdNP3xhyF0xQ==", "shasum": "d93cbaa1d736e619e3c1cf98840a22e0d4a716c1", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.11--canary.c55989b.0.tgz", "fileCount": 6, "unpackedSize": 5939, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7YSORcjkF9XAYCUKp+9t1dnp1hOG2zHF3JJwt7gL30AIgBKvZ9qVq2N0Or5kNRfa6Ul6FXgEBCthMnCoy7t0AILw="}]}, "directories": {}, "dependencies": {"@jimp/utils": "0.22.11--canary.c55989b.0"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.11": {"name": "@jimp/plugin-invert", "version": "0.22.11", "description": "invert an image.", "dist": {"integrity": "sha512-9/LJ2tgo8+O2ulDfANyiyWA29DTDPsIn9+84zSULpwqofYgd5ffU5YGNkXS5eMNWil13lEwB5E0XFGzmrj9Rmg==", "shasum": "22fe973117e8d6ed371ba2eee86eab87c9356be6", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.11.tgz", "fileCount": 10, "unpackedSize": 10746, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMHgUxx/QSoTw2CT3X6uswRInN8yleBBxxrf7MxUy9IAIhAP9Hq+8C4uu7UpHjQk9w5K2aHoRMc1uWUu84HxXhn9Gp"}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.11"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.12": {"name": "@jimp/plugin-invert", "version": "0.22.12", "description": "invert an image.", "dist": {"integrity": "sha512-N+6rwxdB+7OCR6PYijaA/iizXXodpxOGvT/smd/lxeXsZ/empHmFFFJ/FaXcYh19Tm04dGDaXcNF/dN5nm6+xQ==", "shasum": "c569e85c1f59911a9a33ef36a51c9cf26065078e", "tarball": "https://mirrors.cloud.tencent.com/npm/@jimp/plugin-invert/-/plugin-invert-0.22.12.tgz", "fileCount": 10, "unpackedSize": 10746, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoV2oqiFqAqUBxFEP0O1H/z2vwBfMkzpXlUKQ2xsr2cAiBd60sKm8eMnNn74uk9E6OJoUYqcu0FEP5ydbDcWvzLTw=="}]}, "directories": {}, "dependencies": {"@jimp/utils": "^0.22.12"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2024-02-23T03:30:15.957Z", "time": {"created": "2018-08-26T04:22:52.151Z", "0.3.6-alpha.5": "2018-08-26T04:22:52.306Z", "modified": "2024-02-23T03:30:15.957Z", "0.3.6-alpha.6": "2018-08-26T04:39:14.461Z", "0.3.6": "2018-08-26T05:39:00.262Z", "0.3.7": "2018-08-26T05:43:05.095Z", "0.3.8": "2018-08-27T18:01:51.267Z", "0.3.9-alpha.0": "2018-08-28T00:18:35.068Z", "0.3.9": "2018-08-28T00:28:13.894Z", "0.4.0": "2018-09-02T23:05:33.858Z", "0.5.0": "2018-09-15T20:28:54.521Z", "0.6.0": "2018-11-28T19:23:59.611Z", "0.6.4": "2019-04-27T20:42:18.978Z", "0.6.5-canary.4564f3c.0": "2019-09-02T23:59:48.778Z", "0.6.5-canary.eeb6481.0": "2019-09-03T00:16:59.104Z", "0.6.5-canary.784.118.0": "2019-09-03T00:43:23.507Z", "0.6.5-canary.784.124.0": "2019-09-03T00:51:23.763Z", "0.6.5": "2019-09-03T01:09:35.512Z", "0.6.6": "2019-09-03T01:18:00.272Z", "0.6.7": "2019-09-03T01:26:05.211Z", "0.6.7-canary.783.157.0": "2019-09-03T01:28:30.351Z", "0.6.8-canary.783.163.0": "2019-09-03T04:58:38.458Z", "0.6.8": "2019-09-03T15:47:25.246Z", "0.7.0": "2019-09-06T15:53:12.444Z", "0.7.1-canary.770.193.0": "2019-09-07T02:48:51.213Z", "0.8.0": "2019-09-07T17:20:02.913Z", "0.8.1-canary.786.211.0": "2019-09-10T02:13:36.346Z", "0.8.1-canary.786.217.0": "2019-09-10T03:19:49.698Z", "0.8.1-canary.786.223.0": "2019-09-10T07:34:28.406Z", "0.8.1-canary.786.232.0": "2019-09-10T17:03:28.099Z", "0.8.1-canary.786.242.0": "2019-09-11T00:21:43.222Z", "0.8.1-canary.786.262.0": "2019-09-11T04:33:45.647Z", "0.8.1": "2019-09-12T05:09:25.343Z", "0.8.1-canary.791.276.0": "2019-09-12T09:57:00.262Z", "0.8.2-canary.792.283.0": "2019-09-12T15:21:25.510Z", "0.8.2-canary.792.290.0": "2019-09-12T17:24:52.499Z", "0.8.1-canary.789.301.0": "2019-09-13T07:39:21.042Z", "0.8.2-canary.794.308.0": "2019-09-13T17:06:33.049Z", "0.8.2": "2019-09-13T17:33:46.156Z", "0.8.2-canary.792.332.0": "2019-09-17T18:35:13.553Z", "0.8.2-canary.792.339.0": "2019-09-17T23:02:01.046Z", "0.8.2-canary.792.349.0": "2019-09-17T23:09:35.543Z", "0.8.2-canary.792.353.0": "2019-09-17T23:12:24.065Z", "0.8.2-canary.792.360.0": "2019-09-18T21:07:09.261Z", "0.8.3": "2019-09-18T23:26:02.218Z", "0.8.4-canary.798.377.0": "2019-09-19T05:08:32.749Z", "0.8.4": "2019-09-20T16:24:10.222Z", "0.8.5-canary.800.426.0": "2019-09-21T00:53:32.322Z", "0.8.5-canary.753c2c9.0": "2019-09-24T20:45:28.783Z", "0.8.5-canary.b147e81.0": "2019-09-24T20:48:44.611Z", "0.8.5-canary.1.b147e81.0": "2019-09-24T20:58:42.176Z", "0.8.5-canary.799.447.0": "2019-09-24T21:09:52.924Z", "0.8.1-canary.789.460.0": "2019-09-26T09:27:57.616Z", "0.8.1-canary.791.466.0": "2019-10-07T09:14:38.269Z", "0.8.5": "2019-10-18T20:52:11.134Z", "0.8.6-canary.810.481.0": "2019-10-30T17:15:46.974Z", "0.8.6-canary.815.488.0": "2019-11-16T21:25:50.219Z", "0.8.6-canary.818.495.0": "2019-11-19T21:20:07.024Z", "0.8.6-canary.815.502.0": "2019-11-19T22:13:49.551Z", "0.9.0": "2019-11-26T17:07:37.314Z", "0.9.1": "2019-11-26T17:40:18.299Z", "0.9.2-canary.815.538.0": "2019-11-26T20:13:37.034Z", "0.9.3-canary.825.584.0": "2019-11-26T23:08:33.684Z", "0.9.3": "2019-11-26T23:18:43.973Z", "0.9.3-canary.825.599.0": "2019-11-26T23:20:48.128Z", "0.9.4-canary.832.606.0": "2019-12-28T00:26:14.190Z", "0.9.4-canary.838.613.0": "2020-01-09T15:32:13.926Z", "0.9.4-canary.841.620.0": "2020-01-25T23:28:19.297Z", "0.9.4-canary.845.627.0": "2020-02-11T19:50:32.034Z", "0.9.4": "2020-03-03T22:40:46.215Z", "0.9.5": "2020-03-03T22:59:12.570Z", "0.9.6-canary.858.667.0": "2020-03-18T16:36:52.091Z", "0.9.6-canary.857.681.0": "2020-03-18T17:32:52.546Z", "0.9.6-canary.860.700.0": "2020-03-18T18:05:11.921Z", "0.9.6": "2020-03-18T18:09:43.559Z", "0.9.7-canary.854.725.0": "2020-03-18T18:20:23.736Z", "0.9.7": "2020-03-27T17:41:21.586Z", "0.9.8-canary.866.767.0": "2020-03-27T22:43:56.482Z", "0.9.8": "2020-03-28T00:13:56.430Z", "0.9.9-canary.867.792.0": "2020-03-29T22:46:15.266Z", "0.9.9-canary.868.799.0": "2020-03-30T01:16:25.006Z", "0.10.0": "2020-03-30T02:37:14.193Z", "0.10.1-canary.870.821.0": "2020-04-05T10:53:02.373Z", "0.10.1": "2020-04-05T18:14:45.818Z", "0.10.2-canary.875.842.0": "2020-04-14T13:24:55.984Z", "0.10.2": "2020-04-14T15:46:38.000Z", "0.10.3": "2020-04-20T20:01:01.493Z", "0.10.4-canary.882.885.0": "2020-05-02T03:36:11.105Z", "0.10.4-canary.882.884.0": "2020-05-02T03:36:30.399Z", "0.10.5-canary.882.886.0": "2020-05-02T03:38:29.465Z", "0.11.0": "2020-05-15T21:21:23.150Z", "0.11.1-canary.891.908.0": "2020-05-16T18:23:17.586Z", "0.12.0": "2020-05-17T00:34:14.357Z", "0.12.1-canary.892.924.0": "2020-05-19T06:19:57.259Z", "0.12.1": "2020-05-19T06:30:23.485Z", "0.12.2-canary.899.952.0": "2020-06-05T05:15:19.770Z", "0.13.0": "2020-06-05T06:03:56.032Z", "0.13.0-canary.899.964.0": "2020-06-05T06:04:19.058Z", "0.14.0-canary.904.976.0": "2020-06-29T16:21:30.116Z", "0.14.0": "2020-06-29T18:07:12.821Z", "0.14.1-canary.911.993.0": "2020-07-16T18:32:31.181Z", "0.14.1-canary.919.1005.0": "2020-07-30T11:51:35.763Z", "0.15.0": "2020-08-07T20:25:07.826Z", "0.15.1-canary.924.1021.0": "2020-08-07T22:57:51.460Z", "0.16.0": "2020-08-08T00:27:25.351Z", "0.16.1": "2020-08-28T17:20:49.247Z", "0.16.2-canary.919.1052.0": "2020-08-28T17:23:10.030Z", "0.16.2-canary.934.1053.0": "2020-08-28T17:24:14.247Z", "0.16.2-canary.938.1059.0": "2020-09-07T12:58:01.419Z", "0.16.2-canary.947.1077.0": "2020-10-03T05:27:15.285Z", "0.16.2-canary.956.1095.0": "2020-10-23T20:40:50.859Z", "0.16.2-canary.964.1101.0": "2020-11-24T23:51:36.280Z", "0.16.2-canary.969.1115.0": "2020-12-11T02:30:26.078Z", "0.16.2-canary.984.1126.0": "2021-01-25T18:10:07.212Z", "0.16.2-canary.1008.1164.0": "2021-04-29T10:22:47.706Z", "0.16.2-canary.1016.1185.0": "2021-05-25T00:47:18.289Z", "0.16.2-canary.1045.1221.0": "2021-09-01T01:07:11.366Z", "0.16.2-canary.1051.1228.0": "2021-10-22T03:31:17.528Z", "0.16.2-canary.1052.1235.0": "2021-10-27T19:58:05.392Z", "0.16.2-canary.1070.1265.0": "2022-02-11T16:41:06.830Z", "0.16.2-canary.1073.1276.0": "2022-02-21T11:12:21.369Z", "0.16.2-canary.1080.1288.0": "2022-04-04T20:44:02.944Z", "0.16.2-canary.1082.1294.0": "2022-04-28T19:17:24.068Z", "0.16.2-canary.1084.1305.0": "2022-05-16T16:58:33.015Z", "0.16.2-canary.1086.1311.0": "2022-05-21T05:59:21.951Z", "0.16.2-canary.1093.1332.0": "2022-07-25T15:24:45.748Z", "0.16.2-canary.1094.1345.0": "2022-08-02T01:32:36.036Z", "0.16.2-canary.1087.1357.0": "2022-09-15T16:17:23.616Z", "0.16.2": "2022-09-15T16:36:20.304Z", "0.16.3-canary.1100.1371.0": "2022-10-09T20:51:53.126Z", "0.16.3-canary.1108.1382.0": "2022-11-16T00:56:01.177Z", "0.16.3-canary.1109.1388.0": "2022-11-21T19:26:27.018Z", "0.16.3-canary.1113.1404.0": "2022-12-10T16:56:10.940Z", "0.16.3-canary.1125.1580.0": "2023-02-03T05:20:28.371Z", "0.16.3-canary.1014.1579.0": "2023-02-03T05:20:33.689Z", "0.16.3-canary.1085.1581.0": "2023-02-03T05:20:42.003Z", "0.16.3-canary.999.1588.0": "2023-02-03T05:21:32.752Z", "0.16.3-canary.1040.1589.0": "2023-02-03T05:23:08.194Z", "0.16.3-canary.1082.1591.0": "2023-02-03T05:24:26.131Z", "0.16.3-canary.1045.1590.0": "2023-02-03T05:24:29.080Z", "0.16.3-canary.993.1592.0": "2023-02-03T05:24:51.332Z", "0.16.3": "2023-02-04T00:07:12.918Z", "0.16.4": "2023-02-04T00:16:11.086Z", "0.16.5": "2023-02-04T01:23:11.486Z", "0.16.6": "2023-02-04T01:26:26.877Z", "0.16.7": "2023-02-04T01:37:14.527Z", "0.16.8": "2023-02-04T01:45:37.767Z", "0.16.9": "2023-02-04T02:16:36.202Z", "0.16.10": "2023-02-04T02:23:18.643Z", "0.16.11": "2023-02-04T02:37:15.082Z", "0.16.12": "2023-02-04T02:46:28.545Z", "0.16.13": "2023-02-04T02:56:28.618Z", "0.17.0--canary.1131.af3cb94.0": "2023-02-04T03:02:03.904Z", "0.17.0": "2023-02-04T03:07:58.976Z", "0.18.0--canary.1133.54bf269.0": "2023-02-04T04:36:02.944Z", "0.17.1--canary.e22c14a.0": "2023-02-04T04:38:51.711Z", "0.17.1--canary.1134.e007a48.0": "2023-02-04T04:40:10.534Z", "0.17.1": "2023-02-04T04:40:29.456Z", "0.18.0--canary.1135.911ed04.0": "2023-02-04T04:46:39.084Z", "0.17.2": "2023-02-04T04:47:59.505Z", "0.17.3--canary.1136.7f5f5d8.0": "2023-02-04T04:55:47.693Z", "0.17.3": "2023-02-04T04:57:49.253Z", "0.17.4": "2023-02-04T05:02:33.498Z", "0.17.5": "2023-02-04T05:18:43.615Z", "0.17.6--canary.1cb89cf.0": "2023-02-04T05:24:26.275Z", "0.17.6--canary.cd893f3.0": "2023-02-04T05:26:31.514Z", "0.17.6--canary.1137.5e459dc.0": "2023-02-04T05:27:49.369Z", "0.17.6": "2023-02-04T05:33:24.037Z", "0.17.7": "2023-02-04T05:40:15.773Z", "0.17.8--canary.1137.476d7bd.0": "2023-02-04T05:41:53.718Z", "0.17.8": "2023-02-04T05:45:36.352Z", "0.17.9": "2023-02-04T08:22:15.532Z", "0.17.9--canary.1140.831bc3c.0": "2023-02-04T18:05:30.760Z", "0.17.9--canary.1140.4042b43.0": "2023-02-04T18:21:33.793Z", "0.17.9--canary.1140.e3ff49d.0": "2023-02-04T18:44:38.786Z", "0.17.10": "2023-02-04T18:51:36.168Z", "0.17.9--canary.1141.cd4d455.0": "2023-02-05T00:07:44.602Z", "0.18.0": "2023-02-05T00:17:17.212Z", "0.17.9--canary.fc042e5.0": "2023-02-05T00:20:52.858Z", "0.17.9--canary.1143.90575e6.0": "2023-02-05T00:22:29.756Z", "0.17.9--canary.1144.3455afd.0": "2023-02-05T00:27:29.584Z", "0.19.0": "2023-02-05T00:28:51.236Z", "0.20.0": "2023-02-05T00:44:31.625Z", "0.20.1": "2023-02-05T00:54:37.912Z", "0.20.2": "2023-02-05T01:09:32.522Z", "0.21.0--canary.1149.3239903.0": "2023-02-05T02:20:52.626Z", "0.21.0--canary.1149.a81f653.0": "2023-02-05T02:31:26.393Z", "0.21.0": "2023-02-05T02:38:27.941Z", "0.21.1--canary.1153.3cc6d7b.0": "2023-02-05T22:09:09.657Z", "0.21.1": "2023-02-05T22:34:03.503Z", "0.21.2--canary.1156.8b2cc45.0": "2023-02-05T22:46:06.802Z", "0.21.2--canary.1156.ba9fec9d0c9109924b4a5acab31bcaef8159a610.0": "2023-02-05T22:46:51.696Z", "0.21.2--canary.1156.1f8a92bfc1cbc6b5f3ad2490d0ec86710c81f635.0": "2023-02-05T22:50:33.701Z", "0.21.2": "2023-02-05T22:56:58.963Z", "0.21.3": "2023-02-05T23:05:48.331Z", "0.21.4--canary.1163.a26f5b300ee88c2fadb9909dd390cb4380b32a42.0": "2023-02-06T00:48:58.410Z", "0.21.4--canary.1163.6a6ae39e83bd731ccd25120a160a75b32273f9bd.0": "2023-02-06T00:55:28.395Z", "0.21.4--canary.1163.7b9287fff1036ae67190d5ea3a8dec9926373a63.0": "2023-02-06T01:04:00.560Z", "0.21.4--canary.1163.d07ed6254d130e2995d24101e93427ec091016e6.0": "2023-02-06T01:11:04.811Z", "0.22.0": "2023-02-06T04:23:02.954Z", "0.22.1--canary.1169.6db477d088d28aea43b31d9ca8b0eaf74dc511d1.0": "2023-02-06T05:46:55.152Z", "0.22.1--canary.1173.30a931db5f6552c59e4c99f41fdb9e2b9ccf8291.0": "2023-02-06T09:13:30.867Z", "0.22.1": "2023-02-06T09:20:19.503Z", "0.22.2": "2023-02-06T20:36:23.647Z", "0.22.3--canary.1184.ff6583af74ba85d9555c36db2844886e269feb16.0": "2023-02-06T22:55:22.741Z", "0.22.3": "2023-02-06T23:07:31.839Z", "0.22.4--canary.1187.30d10f964404705d383d7163b7d3f85baa2201b7.0": "2023-02-07T02:17:45.729Z", "0.22.4": "2023-02-07T02:23:28.929Z", "0.22.5": "2023-02-21T17:33:37.112Z", "0.22.6--canary.1198.9324a88b87a1fc38b528af47ca707d97593dddaa.0": "2023-02-22T06:58:31.465Z", "0.22.6": "2023-02-24T19:30:42.693Z", "0.22.7": "2023-02-25T00:48:20.013Z", "0.22.8": "2023-05-11T01:52:11.876Z", "0.22.9": "2023-07-26T18:52:16.598Z", "0.22.10": "2023-07-26T19:05:36.311Z", "0.22.11--canary.c55989b.0": "2024-02-23T03:10:42.684Z", "0.22.11": "2024-02-23T03:19:41.896Z", "0.22.12": "2024-02-23T03:30:15.504Z"}}