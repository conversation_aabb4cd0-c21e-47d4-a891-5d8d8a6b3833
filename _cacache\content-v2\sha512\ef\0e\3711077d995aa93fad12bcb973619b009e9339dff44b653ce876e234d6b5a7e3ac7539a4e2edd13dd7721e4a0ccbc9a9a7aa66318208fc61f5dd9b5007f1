{"name": "is-docker", "versions": {"1.0.0": {"name": "is-docker", "version": "1.0.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-docker"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["docker", "dockerized", "container", "inside", "is", "detect", "env", "environment", "process"], "devDependencies": {"ava": "0.0.4", "sinon": "^1.14.1"}, "gitHead": "43550f2cb491fc42a273f13065cfad81be0569d3", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker", "_id": "is-docker@1.0.0", "_shasum": "31cbc0ae28b187c012986203254deb09f0ceb867", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.10.36", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "31cbc0ae28b187c012986203254deb09f0ceb867", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-1.0.0.tgz", "integrity": "sha512-jg/svotujBsC1pubyrIqtewPOURHcduBYtRTa2HAtMvFBT18/HKNejron43Z9BOaTYNM7F2+xVdryl04KFLowA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA6Wbt7z6PqJxoM/EYlTFEAxKIPM9/HJWRXigKUBTkLCAiEAjHbDcEXYScs+PGX8efeKnybjwQ5Jb/WVILrz4+ot+1Y="}]}, "directories": {}, "contributors": []}, "1.0.1": {"name": "is-docker", "version": "1.0.1", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "*", "sinon": "^1.14.1", "xo": "*"}, "gitHead": "1ebfeb09bccdb93996164b47415d6aad16ac7f66", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@1.0.1", "_shasum": "c02e215fc3d1d2ffe35a3b70d19f9d984693a4d8", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c02e215fc3d1d2ffe35a3b70d19f9d984693a4d8", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-1.0.1.tgz", "integrity": "sha512-O8kCt+CC/lXilFCCvgMdQeJkg4JTxaVm8O1eWz68Eozgn5FtzoMvf1RObdXtM726NR/q17ggF3G0wO+Zh9w+zw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMr39AmxSWUQms9iUP9aSdyo4ac2edEg546mP7W6mefwIhAIKqeCbgyLUGQxiydqVE4O4+sLnQ3iU37mRgheWm0NQ5"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-docker-1.0.1.tgz_1463555679110_0.1724248994141817"}, "directories": {}, "contributors": []}, "1.1.0": {"name": "is-docker", "version": "1.1.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "*", "sinon": "^1.14.1", "xo": "^0.16.0"}, "gitHead": "018db88bebd72308336fc90979550a3842b23176", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@1.1.0", "_shasum": "f04374d4eee5310e9a8e113bf1495411e46176a1", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f04374d4eee5310e9a8e113bf1495411e46176a1", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-1.1.0.tgz", "integrity": "sha512-ZEpopPu+bLIb/x3IF9wXxRdAW74e/ity1XGRxpznAaABKhc8mmtRamRB2l71CSs1YMS8FQxDK/vPK10XlhzG2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICBjgCShc/gyXsJcyM5awJQGpSbXMVNuId+h46TGM/BaAiBpEcM5fkunNvEzmgEaRq7zAehudUkTO1monhbVLGe7CA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/is-docker-1.1.0.tgz_1477557310875_0.7883002860471606"}, "directories": {}, "contributors": []}, "2.0.0": {"name": "is-docker", "version": "2.0.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "7461d85a54fe45f3688a3938c640d461019e3536", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@2.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pJEdRugimx4fBMra5z2/5iRdZ63OhYV0vr0Dwm5+xtW4D1FvRkB8hamMIhnWfyJeDdyr/aa7BDyNbtG38VxgoQ==", "shasum": "2cb0df0e75e2d064fe1864c37cdeacb7b2dcf25b", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-2.0.0.tgz", "fileCount": 5, "unpackedSize": 2908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcucibCRA9TVsSAnZWagAAn2cP/AoYJ49l8JcVNifeHbpT\n32xm/Mw+mO85RhyH/VEnmYGL9RQpl9FTknumhbGOWTiAxrjvcZcubIF2SdNY\neTwf4pOb+fBggg8LibSvoUvTR2cEXJmN8MdUDNh0G7YRoZC25Se7tDRn3i+W\nAkuoDs+7BeQmGeS/ZhcYqyTHfApp9vuqMzAi4BwuV8lSvS1t3oHOgPMDVg8A\nbaHxNWJsEACj6sNCvupgn3VzK8y384G0LUCIGyvmPNZ7eBBfyE+JO//JHAnI\nu/ftqGtPT0ViORKC7MjK4dik2Ry85WqgyoPN3Bkkqj0BF5VhYKfFiGGvrstx\n+K2uALUoedYx8iIiavkCxoX8k5yGhHzIOMyvPZAMn1ePxzllvVlrEu/S4Kl7\ndYA62R901iIH76nj0NPJhXeO4Y66V+KCQ7bVt5kpnskygFUasLF0WlOtRf/d\nhHDDW2rotTg+XE+th9UseOTYBzjAMgXC7qpEwuCG516OhZ53mOtYWIacrpJF\n9aOTcA/ftvojPmp2zycueVQc9x8/oE+0YaBsojcH2fMjP7VTvXZ1CvjEjfkH\nJCZhMvkLUpPl+nBEelOrOTrT/ucFlcf2Mqy0bmAOowZtNP9qEoYVod1y+nWk\nr1K0UrR4iE7oaCj06xWbshpUKLFYffj9pj7tzWIIofXNP+rbkyP0uLsOblxs\nMFyV\r\n=aI3e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDs+SdJzOqNFealMbtEefaEqZy0j4i4VGo69dBWNKpyHwIgC+XddjTJyS6Bo9sOnMXzgW2SvsNi6QCyhai0avupiFY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_2.0.0_1555679387350_0.8466747373366723"}, "_hasShrinkwrap": false, "contributors": []}, "2.1.0": {"name": "is-docker", "version": "2.1.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "39a955b483b7f3483037b11bbd0c7e04a6fe511a", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@2.1.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mB2WygGsSeoXtLKpSYzP6sa0Z9DyU9ZyKlnvuZWxCociaI0qsF8u12sR72DFTX236g1u6oWSWYFuUk09nGQEjg==", "shasum": "25dc043e4fdc3cf969d622735e05a86ba9952e2b", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-2.1.0.tgz", "fileCount": 6, "unpackedSize": 3146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJzzJCRA9TVsSAnZWagAATjcQAI4LozsAdZHVE1o2RVHC\n0f4bJHLId+iynnrQXzQ48J8aWTcsGpQdZRMLgloa0EoeoPJJnWcZwg2qRlpf\nEWSNGMpYEW1v4bItTjJrGvp0X3vkAorBB5owRBRVoNiUC/7BjSsmWs6pPPQa\n9TLaNmMIicfZZURlhHBneCXSR9yemenbePn1SjI/6vka2fLl1EZ0rurmzSlu\ndBHC+JroQf7NNRhqRpAebNHKIDUY4ZuVlAeVGUM9zD7taGeMt6MD17WbYJod\noMvhUHy5z5lHqO5IIcrSx7Bwp4bqRe6cZsbBkRUHHCEIWPvnkA/GQXjoQKDZ\nVLgla1i7JRUjbciQuyM1/MYLgqrJFKsvD9ks+AYo66V4+/k/etHeed2Y1MYf\nHBulJE92ZJVR0mMpdm+lOGMv0WNfPeNx509g1Jy0wO0Znzgawikf+lnyH21u\nKvNx0cKJs1fjl5rM8nLhyljG1pkJXrDY4iybBDprF7ct1JwboLlSvfngglTQ\nzZEaUpVBxgq6sN0J/M8APlRWdoUy+ELUeWUPl2ipbsbQ8US0FWXgT9oZ6mMj\njQ4duhdBK2IsA9sB6c0Sn22AfVkbFA5R5hvZ594RUiD1xKOiPaW7Ue0J1gkj\nTLA/1fPTzX3F1yTZS1yFLsUp4Pq8R/UGqULQye2/X+JLNqKRrH18oAPEfUvP\nM6RA\r\n=6vaF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICBcZYeJV1xDFNI3bV1l54iZjSjYn88Uu0xMFhEcS0ANAiEAspBs7RvM2vCKSCQenQkLHgbUTkyoOp0DItFpzANeQwA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_2.1.0_1596406985176_0.986967546057193"}, "_hasShrinkwrap": false, "contributors": []}, "2.1.1": {"name": "is-docker", "version": "2.1.1", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "e9df7a56a07125061981b7a5ca1c9d8c4fc8765e", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@2.1.1", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZOoqiXfEwtGknTiuDEy8pN2CfE3TxMHprvNer1mXiqwkOT77Rw3YVrUQ52EqAOU3QAWDQ+bQdx7HJzrv7LS2Hw==", "shasum": "4125a88e44e450d384e09047ede71adc2d144156", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-2.1.1.tgz", "fileCount": 6, "unpackedSize": 3148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKY/fCRA9TVsSAnZWagAAeJ8P/3/zEA28C/hthf3rNxEA\nt2M94QlJvNE5mOk9BKOT+4a/k2jg67DyoTQaAPFXNggHbf09sSYOF29WRIwN\nNvgx5PkF7s8OSMLQ5Rs47mSPPByT4C0hYXwDk5dDqjo/7uPur1lTy7mOhAGH\n9VgyURUPX8nnkuvwR9uhxrJh2ssD/l+6UU6FzLUvUkUD+rGy+ksSwkyeX/3N\nwAApcS9OXgxeJ2/mi3MBPQKJktTO3PUNNZ2JqB3KrWOhgcqbT827XfSuVlnl\nf1cbTeTBUMfMqmYpfpBEHDTamflBtr8DwlxLHdZMp2A4ZopOYH6ywgKV9DUV\nYPZfL+LDu/oVMiKSwhKmMSJ+DQDL0vyr7g1v1bqbJlEjTLLS7PgSlHIukuwN\nxyfZ/YfPBlqWtppGuC63Q4L4pQFfH1r+h+Kz1BySQc9Guz1paLeEpn+1rmnL\nn5f+VyBpLi3OYRWDm7gCpEzBLt7+oJA4l/ePRLt8TSD+HWvmbEVWBMfpqqHX\nKn63xJOgdlmjTFpFFWBOMzCO/rNwMFxSM38/HcTBP8VSvP5gNtc8HDlGIhg9\nHRChsju2kWtxWbflQep607zTwRvODqSPmIFw1pYvrfwwq0hyGZ8XCEnQDs/h\nHVgOn1q0VHGGokbm+94MeZVRh3nRGdEN8Iu4+SCQcD5ZpIMJRdfIljtC0hlI\nFi7G\r\n=indA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPubNS8EVLbq22/n3fJGiYTC8rMnVkUOQ818pNntAHpAIhAKd33rS/E7bC6UZ8BWNyhmdtFhA8ivQutUMkOl6ikV1D"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_2.1.1_1596559327162_0.06418938597862911"}, "_hasShrinkwrap": false, "contributors": []}, "2.2.0": {"name": "is-docker", "version": "2.2.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "96002f95a403482e87f616de400667ef259ae872", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@2.2.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-K4GwB4i/HzhAzwP/XSlspzRdFTI9N8OxJOyOU7Y5Rz+p+WBokXWVWblaJeBkggthmoSV0OoGTH5thJNvplpkvQ==", "shasum": "b037c8815281edaad6c2562648a5f5f18839d5f7", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-2.2.0.tgz", "fileCount": 6, "unpackedSize": 3149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgawwOCRA9TVsSAnZWagAAytYP/itVc8toNNngCYlCF7I+\nMvVWxFtAzlZRSaRm6PhUNe2c87HFm23Xvxo7+KacGkqCS8Rab/zzwNBAr0Dv\n6ASjVFR7pNSEevn4R1gS95+JMUpMt5xMJsVrDQ+HGV4PhaMrrta532QOpLOr\nTM5ZtFv3DA0bwyxZGz9Xk7GauF6l6OrrOP/Hqp5vhjFrVygp+1BmeCt4HLwu\n3Orcqu+7W37h/KWlSz5DKSx9Kt+9lYxEcKgUI6CE/c/+N34yEUqhMX7k/gLb\n7AGr2gxxiGXXLWrhSGXW1Yqx7IdsDSSni9CsZwnCfsNoJSOfz1TYgXvcGxo+\nBT7rmzKOSYPJl8S2NYJ1+Aor9/N5zXxSkO3QiflBHVDiNKCWUn950mQEx6gY\nCPSecH2Shc4A6orAXAD+Yj/5kwHx4ASsgaZsDhqX65rRoOYBndKX6BJTvDNO\nTieuZLKIntTNNMXmdS8uKtoaTCg43kq4H/ob0duKjU7dIAgMGYhllsL97mKT\nI5aVnrJIOw3W0mWcM0daYz6PJziqKj7kZ3JAdq471RBsy6su5P2DfYMvkIra\nF+EaZl1TE+ztKZ1mwaMJ6rYuRyBqlzL5CjYinlBpQFo4NJ/8/YswuuJNhM8T\nJYX23wgBKQ6+s5kOmEK4WeVP1Bh2ux+76PQQZ0B0AuVw7C3y6PrGvYEj6bh3\nJqVS\r\n=zcRF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICkkF7Z5vSpry/udnxWTO/m8bt3NAbTbzl3MLr0rKh2fAiEA+Zwt8XF8AVx5kMsekaX4oBesipHNTGnt3XmP+NS6nXM="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_2.2.0_1617628174162_0.8823876396595611"}, "_hasShrinkwrap": false, "contributors": []}, "2.2.1": {"name": "is-docker", "version": "2.2.1", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "43b4ec12e5b4337901782a7b0335f43f93669443", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@2.2.1", "_nodeVersion": "15.12.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==", "shasum": "33eeabe23cfe86f14bde4408a02c0cfb853acdaa", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-2.2.1.tgz", "fileCount": 6, "unpackedSize": 3013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcBdfCRA9TVsSAnZWagAAY6IP/0cEs+av4p3y47Oh46Jo\nRXxjzN97iL+tVNAbVoUbZedJRFB/G6trv+1yJxjRLcwoK86jjD3SygUViOXK\nHsx5/FUaJq64PRjjDG+lkOESdaR/VE0/UJJ8BqTfHF5PXft/TcPVJ++AXHbV\nksM5Kpz6/kS25GcrUkcX2h7/53i09wg3leeo+87WAmtbsKjnfcbYoUiHKJyy\nPJXhuTneyGRLLaKMxMH74Kr75NtpCYIoGiUetWb3/1EpYJS5S0QFud77je5r\nn8g05dxRNNG9ntyPAG5U2lOycc50sWoZ2DQcV9M8nJLTMuqFHdBl8y8GorYQ\noJ3l2yvdnd0AsETq7KujqeGgj41blC5UFtC1B2xP1ir6SLRa40pB1gt49eHH\nklxpINDGrlNGwaSYridcNsFvK/9C0fy3h9hLbRBDGjzzkL99p02Nblktftq8\nT6js5Cq/aBRbsvNNXIXd3mvOM/eiiHTp+BOnrM4sf23NEDKUNR9YkLJ8fD/b\nH3PNq9ViSt2MfHDTr5HJX79rKO+CmS/s0MwTC9gPBM7sxdTPM8cM0XOHhN5t\nNmKA12OGkm3zebgNFJWGpt2sqCsfsz0weqSr8JEqRU9SR46K8rDer7+t5QNR\nWwoygRjulbCgnKPncmLyovrgTr/wFirpu6VwuDOaRAmYutytVIqhkkO6Ad1p\n+Suf\r\n=ghBD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYgWdS/dbRrYplq56JEKflIHfakhy6E3rtIKwedF6CkgIhALO+uUkgl8D7o6hDEo2O4hceX1C0MwyRQg7FLmL+hGVp"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_2.2.1_1617958751231_0.5669495693109765"}, "_hasShrinkwrap": false, "contributors": []}, "3.0.0": {"name": "is-docker", "version": "3.0.0", "description": "Check if the process is running inside a Docker container", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "devDependencies": {"ava": "^3.15.0", "sinon": "^11.1.2", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "8686c66ce485dfe45593dd77c5e9aadbc1491d38", "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "homepage": "https://github.com/sindresorhus/is-docker#readme", "_id": "is-docker@3.0.0", "_nodeVersion": "14.17.5", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==", "shasum": "90093aa3106277d8a77a5910dbae71747e15a200", "tarball": "https://mirrors.cloud.tencent.com/npm/is-docker/-/is-docker-3.0.0.tgz", "fileCount": 6, "unpackedSize": 3154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLrULCRA9TVsSAnZWagAAGDoP/jdfcDOKNgxY9ZakZgtt\nbNU+KWRYIgmtRpc1Rf3BT/I4LBAxUzNT0tLxb58Pakg7AwfHrsKeKYbNyXaz\nJ4IOeZ5wG4dtNPUkKmcBY2cWF0oZqRahKc6yabzQZJ1eMBKUNTR1d9rzNQq4\nSQPZUzNorDWRV5isiHCadMWH00LZ1QoP8DU1BRX4eCsiihjaDvBPZ0p6VeJU\nWY3iv82LTk701WcAdG3cYxK8NST0/9crMcPER39AYdu1LKmzPqBLJTVNq26r\nIjMMeYq6OD93SkyobT5OZLW1+Qqek4jwZeio72kTMS64IOzcFeKTeKQACXa7\ntQNzVoJ+i8WGS12l0rzuOMSCVWyOlZPbIWiPQnA7L4CT1T3hZnj/OPwJcDm4\nk4FDeKHrVzDZJwJMIs4TSsB31cp92ng14NDn9u1HwNAbh9Vv7EgaWG3qkbU2\nLX1RIp15bzA45zmqWwzzp9WvFfukhJUrWW71Ict+kdu0xmzaqUxHwpRj/wI7\nfzMuNtUyZNiGcATe8w8Mim+bRST0ddkeF8lWojrtrJ3t26QGGJyEAMgiDwI9\nbputpp2JFXOmB8s2Y/DqLpkiy7vd68pSoHt8cYJ6KP9yDr5sPX5lLxKDoX/q\n21Orv2c7Xv7FWgoKdi0j1a3Gtk4MdjlnJ/IXiqQqz69ntPG+1/7WSs42GHR9\n2ZQZ\r\n=Zqx9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEB3ZzNqVuVnO35hHu8qWjDXJJqDIFWfzaeCifh08hSeAiEArqJJySGGG52kI7AC/CZlU4jsJNAGHYzZUN/GjKAhd3o="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-docker_3.0.0_1630450954935_0.5441646887681955"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"modified": "2023-06-01T22:06:54.812Z", "created": "2015-04-08T07:09:24.685Z", "1.0.0": "2015-04-08T07:09:24.685Z", "1.0.1": "2016-05-18T07:14:39.690Z", "1.1.0": "2016-10-27T08:35:14.265Z", "2.0.0": "2019-04-19T13:09:47.492Z", "2.1.0": "2020-08-02T22:23:05.278Z", "2.1.1": "2020-08-04T16:42:07.327Z", "2.2.0": "2021-04-05T13:09:34.342Z", "2.2.1": "2021-04-09T08:59:11.367Z", "3.0.0": "2021-08-31T23:02:35.148Z"}, "users": {}, "dist-tags": {"latest": "3.0.0"}, "_rev": "19564-ab68a2338ebf8744", "_id": "is-docker", "readme": "# is-docker\n\n> Check if the process is running inside a Docker container\n\n## Install\n\n```\n$ npm install is-docker\n```\n\n## Usage\n\n```js\nimport isDocker from 'is-docker';\n\nif (isDocker()) {\n\tconsole.log('Running inside a Docker container');\n}\n```\n\n## CLI\n\n```\n$ is-docker\n```\n\nExits with code 0 if inside a Docker container and 2 if not.", "_attachments": {}}