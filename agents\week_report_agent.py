from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb

AGENT_ID = "week_report_agent"

# Agent元数据配置
AGENT_METADATA = {
    "display_name": "周报专家",
    "brief": "企业周报撰写和工作总结专家",
    "category": "office",
    "avatar": "touxiang/887.png",
    "tags": ["周报", "工作总结", "进展", "汇报"]
}

def create_agent():
    """创建周报专家Agent实例"""
    from .shared_memory import get_shared_memory, get_agent_storage
    
    # 使用全局共享的Memory实例
    shared_memory = get_shared_memory()
    
    # 获取Agent独立的存储实例
    agent_storage = get_agent_storage(AGENT_ID)
    
    return Agent(
        name="周报",
        model=OpenAILike(
            id="deepseek-v3-250324",
            api_key="69692567-4a0d-4aab-bd5f-439f47e0baac",
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        ),
        description="你是企业文档优化专家，擅长总结一周工作，突出关键任务和成果。",
        instructions="\n".join([
            "你擅长信息筛选和总结，能将一周工作合理分类，突出任务和个人贡献。",
            "目标: 提供简洁的工作日报模板，突出核心任务与成果，避免冗余。",
            "要求：周报内容详细但简洁，避免冗长，突出关键任务，符合企业文档规范。",
            "输出格式：包含日期范围、姓名、部门、工作内容、问题及解决方案、下周计划等。",
            "流程：提取本周核心任务，描述任务完成情况，列出问题及解决方案。"
        ]),
        memory=shared_memory,  # 使用共享记忆
        enable_user_memories=True,
        enable_session_summaries=True,
        storage=agent_storage,  # 使用独立存储
        add_history_to_messages=True,
        num_history_responses=3,
        stream=True,
        markdown=True,
        show_tool_calls=True
    )