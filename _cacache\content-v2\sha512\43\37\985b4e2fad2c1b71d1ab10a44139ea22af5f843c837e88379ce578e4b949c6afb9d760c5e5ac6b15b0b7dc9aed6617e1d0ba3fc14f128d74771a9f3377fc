{"name": "terminal-link", "versions": {"1.0.0": {"name": "terminal-link", "version": "1.0.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "f067ee2b2d58ba2f31b077705af1c3557f9bc336", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-wUzJtaArDxef8CDC3yTZVu8/hFpJZPgS8rNYFAw5oQx7BYQktHFUYfsFNWUCStOv0iBi5KVxOylaw58JiIr1MQ==", "signatures": [{"sig": "MEQCIAHGPgn7jr41vgNd8rjsZ/+q2810Y+idh5eJL84SYhd9AiB1vCiwtVQIqsANioiaVXO3k6gwJkhFwKANSBDz4/LIQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3422}, "files": ["index.js"], "engines": {"node": ">=6"}, "gitHead": "59bf01d54c968967341fa80683a6dab6cd9e8c23", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"ansi-escapes": "^3.1.0", "supports-hyperlinks": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "clear-module": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_1.0.0_1522863334415_0.8974874446848509", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.1.0": {"name": "terminal-link", "version": "1.1.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "8573e830db810baa62ce67859c1f102e88fa4318", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-1.1.0.tgz", "fileCount": 4, "integrity": "sha512-sOZb3eUbMEcBeuA+TePxEiyueKHNoFOdU8gJtw6vXBKQEgj2ZeyQfWT0aXqjSDI1a/xEZfjzTZMApcSgV70KGg==", "signatures": [{"sig": "MEQCIHtixr6yV2KA2You2IHXw1LLk5rX9UtSl1O4u1qze2A7AiBPzanP3uN1YjWQ4CYQv1XEwbQHsrAfGOeHv7AV6bn6UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2bKTCRA9TVsSAnZWagAAjgcP/1Bdpy8Ge5xfd3jGdTTh\n7K18wR1CvVvPCFIL0rUuCU4tuGkMHhXgD5h5/CvXKf7dHWrgeIj+sjhkBMPm\n0tu4zhvihb6hM3V24QaqnMZzUfj7OU9Do1QogeeFPcqLw75kc38v2k5NPitT\nM1Ud/U61MYs90H9bcKHmHGMTSaYg2Nn9NsK7flD8FXeItzXZKh3VK3raBRum\napP+qD8Oilg4gwkLkGn/pbdV7J8+7wNJwDzOWUZfgQXbN162JXxNCXHYChbv\nd7odZK8raJl0yivrQodQYkvaSZKswwOSvl7qmXzoBX+7bjIHh+yx32P910j4\nJJFQ69JdY6qt6mtFFi3/ssN2anWEeQuRINNWUcMRUnyAMzsEpGnUUVvaK8/+\nItzURBsxvejPyKRYo4ZDEDxnCnjpZF6EaF6AbcBI0VTyZJ41pU/zg2/n+qRT\nCVaT67hPYJp4Y094fxTXk0twi7ORmrieaDUi5kDp0LBWpY9KAPTAF8r/p1uB\n4MmXubfc9z1Mr1LREjxyd8pP7YgfPb0/yH1KFgKO3I0u9nSg6jjVaVXzVupN\nSXDiQs/KKsi8a9lD3kL8JCyB8GWjGBPahFnlNQcSCi5h9+1e2FQmZXsjOcpZ\n4nEpAYGir1HKBZBWOIemEd0AS1tgan+Ydf5e4xn8EvFc1x54MMLCBOUbfPaE\neUaK\r\n=OHV1\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=6"}, "gitHead": "a67125cef86d5fe60a5e6a8bf7a43c03795884a4", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"ansi-escapes": "^3.1.0", "supports-hyperlinks": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "clear-module": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_1.1.0_1524216467232_0.07764757997577831", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.2.0": {"name": "terminal-link", "version": "1.2.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "ed1ac495da75a8c3eadc8a5db985226e451edffd", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-nkM6NjuohxfZGA/jkAnM1zl2qjhdm8vZTG0Q36t+5O6msGwZ/ieJW+XxbIlLpUBQEUeGswg4XiB/QhcEVI23Rg==", "signatures": [{"sig": "MEYCIQDLbVQXPQmStsVhBlYI9/a4Ms3rGkdyh3d8FDBdBLKcwwIhAOzAEclbs+uNHZ2gWGSl0kDrP/3QznIX07q8Vbn7MvlJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHsvfCRA9TVsSAnZWagAAnvEP/ihv4dWPPRuL8wtOQsyQ\nFykOqK2YhQT2pO0+voyHCYIHQ8pd/ADnsSt5Yha+fc5WTjTCpECGIml1UKpD\nn+KJ9aTjBtwy4qXPmXMusIWkXudBtNZC93WWQwRs1tLXXQNSaq8VrvsZDoLG\nLym9ugmNMObCLUURWnWqMJZ/LNvx/3rVm1TQOMjKjkno/CKeJZ0irSyVTfdC\nnBkG+FM+PFjluoUQYLKFr9LgFlJjnhC15ZYclJGzOUoTMMnSl5hFK4G3OBbB\nU30N7cO6MlUEH0eKvGg+a6RgDjYFm+9yTSpE83agwUkxN7gmSJlMCqhnNt9J\nE/NgOJMdoFeqH0UDhoTdqRBPwhjPedIpVCUKm/cMHLEOpvavSeBhGcjuRxUj\nMk2Izv4w50BptfhhJNzeZEAfACTYtBTSfGtL+SRlw75reJzFzdQ6CMubDf9j\nn+o/L7mZkZm3GAapQjatc5pcbJceFmmqKTpdnWrvAXqDRDLRmLZDtD1vcc7T\n/5HjJqAFJdU0FaJ+at0dTpB/Jp9Pg7T0AGCu+cXOfLao8QqPdUJojI7WtpPx\nuGZO/DI2w6QwkLd2NxH5SoFzz3nqsE1zuUJQEQXUUvgo5FxYqxZIdYIPj+PM\nFAPWu2Gq8aLir3C9k33O8PqdPFdxFrmdSugK3uTcw4YGTgVW9ai/7pUUHcnS\nFqgj\r\n=xkil\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "543f19078365d2c1c5f4af8e831247f72a8c9cb0", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"ansi-escapes": "^3.1.0", "supports-hyperlinks": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd-check": "^0.2.1", "clear-module": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_1.2.0_1545522142607_0.7064849736088123", "host": "s3://npm-registry-packages"}, "contributors": []}, "1.3.0": {"name": "terminal-link", "version": "1.3.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "3e9a308289e13340053aaf40e8f1a06d1335646e", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-1.3.0.tgz", "fileCount": 5, "integrity": "sha512-nFaWG/gs3brGi3opgWU2+dyFGbQ7tueSRYOBOD8URdDXCbAGqDEZzuskCc+okCClYcJFDPwn8e2mbv4FqAnWFA==", "signatures": [{"sig": "MEUCIQCqQb1YlhuIt60akaCs9Iug7BygbOBw5ZJXp54AGInevgIgU/kxiyx4KoS9dhiU4KXGA0iL1C9vtfTwvxJbBxiAyxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqIvdCRA9TVsSAnZWagAAKxEP/jB3PI1y+5G3THoRIkB6\nQ/orzGbQVJW9RJrIx4T+kmqQcFGMe2LKJc3jtINxGSFBRu5/kf4wXsUKU7OK\nBDN2q/uEZMlB/kvUOmigmtHeIeCjqzMGvclcwIsN9RTu+QKR8AufOiynnAyk\ntp3vhTsNpNPSz7LgV8gLBDVc4UvAOmFilsQ8W/BlxE+JeMrC+J4b/SX6aPzx\nsBAUOfQNpxPga/TvvSS8wiATm76WU39MGyhDUmF6X7MyigEzS17AOylRF1u4\ny7Eh1AFLgZzdIQ13+fv3YxYUw0vt6nSx3lsJL2oSNhEVXEF/mPskfw1ed3fJ\nloUWXJpKGu92YBXTGX2exzQ84KUIGms0uzD7LfTLdwPHyJS7iL4Ipk0RGfBr\nLHMW3scgZJ/WdtyJKAW8HcfWdMKCmBXoSKqKgvMVaLzYnbqMjrlJVOiHyWyp\nUtj9/Q8n/3vfhmFtueoG6KC5wlHNybMtgs4W34qRmCi7cJxyOcmLkqmB3++U\n26Yc046zqGUt4D++9RuCSjGxyRna4rW6i7o5cidWK6QEqwWjcldYFDmnZ3ze\nhYTheFIwCFO28i1wLeFKvVWI8V3hCbloKdDc0KmsvGYVn1P/SICS+o9V4mAJ\nXPfio65nDVogvZ5HWC5pOmbQesXyQNZFW5Hdejut2QpkNMFi3X2rbdJrnmB+\n1GhZ\r\n=x167\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "b4d0553c060ee329fa82caf5984bdc6e672b0f48", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"ansi-escapes": "^3.2.0", "supports-hyperlinks": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "clear-module": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_1.3.0_1554549724800_0.610908596384627", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.0.0": {"name": "terminal-link", "version": "2.0.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "daa5d9893d57d3a09f981e1a45be37daba3f0ce6", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-rdBAY35jUvVapqCuhehjenLbYY73cVgRQ6podD6u9EDBomBBHjCOtmq2InPgPpTysOIOsQ5PdBzwSC/sKjv6ew==", "signatures": [{"sig": "MEYCIQDzAoP2O58ztcLcGw6j8NNRQWIOh/j9T/e8B84varkL4AIhAJ2h4q22F4ydJ22Jn9+bTMtCAAA1VCtfe8tt4Q9K+gcQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJda1H4CRA9TVsSAnZWagAAyjwP+wV0nGUTcwKMy0IYLDb+\nJgWDFBGowa6UVhnR7ozdz6BWA/hMpswKGeu+bs/anLYE4b9LRuNuC9Ma28/k\niUZVvdxcV+GKwQx2EJuGl8hTOz+0S+th+n1uOa8qU8DDyFaiqpiXxHYVAiNW\nzvZtvXQJ0wvm5UisjQYbOHeYVKDQTVu9a+ZW2UKQlVlC3Ru3Kf+3gCbGmm9p\nrtfTpL/7UzKnXqQsLeGsOkqHq8RejyRLZ0Cidu5ygqnA+3O/DI2Z1VIkP5+E\ndyKv/YiY2U6uRBzia68H3b5prd2qHrTIR0t2FybQyT3WUGwjNdIxFphdQ82Y\nQsy6Rf7P6+QPu53OR3OD0kp/zKCK8uUMXWei8mxdWGTpJn7uqWWQv4RgC6Pf\neOR4QHWikkbGsKIJX8Z8dIg+LbAh+EZDc21jT2huZEhww+luWEWaUZjlyOfP\nK6roq7UqRJWf0KCkdJaOuL/1d/Fh4tZSOVSGxb0/Q3m2maeWHITORzYszYJ5\nxW0VjnEwU4kXd6+VH57FCHB2wMT+DfMasmkQsfynkr8ldeDLmNGuS8OhdRzk\n7FXf2QPs63dVZDJVmkEX444/zBqMlW5FWKBi2Lnu/Ei+Lpb0fb3Qdcrp7tQ1\ne+/l9ynD7Z3zIXeMZ2SBo6yjL5sLSuw5BirUPOauKKMAvFieVfPc9GRlflJj\n3T/R\r\n=8Ojw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "10072b83148a3168257a8f5a880d10b876a7b87d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.3.0", "tsd": "^0.7.4", "clear-module": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_2.0.0_1567314423651_0.5049925957040202", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.0": {"name": "terminal-link", "version": "2.1.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "19eb4742599d667d3c00f77bde6a74a25bebdc5e", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-UAX1wqFgzEBwdM6wph9hsi5w1v7fJy9LSKVB2nSFEIGv0XJMlf4oNSzIqnrlIOJkB+5zIFGoV17//akYqDuxHw==", "signatures": [{"sig": "MEUCIQD3zPNcT4MBmtFpvua6cTcwp27vj1At5szKbpOsm88khgIgQsqmcMH7j2+hFM7O3XzhMLqL56CBVbX4V9gQNqTNF4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+lk2CRA9TVsSAnZWagAAw38P/0fJz0/7QmOhKy8A0JIz\nVZmwW36IrQ16BAG+j3VIWhXl2P41jdFbUsKBmy/UFwGhyQ2VS/wy/FFe0tLe\nmmd6dELKrETM3pkT178ulCozscm+ee6+201gromoqxTqmByKapG6ntBKMCyb\n9pU1yv237VDqREvt5PY7mPcm2k61kDiYkfFp8tnWRBDIUP4tlisl7eo5ZVp1\neYVEYFIVqQDYPsL8gPEWHFhsIto/1DnV4bH+L1H4T5yw1dil+Wsig5xK+L4f\nUhMqIgMj8oZolnP8yKrWVcA+fGu2qRlxyDxD1H4kCizNsODmYXAoVrf5Jkhg\nnmH258o4taSwRDiQuKKc+eKjKfkIJCNkED5J4Ns5SE1seqD3U+Y4UI8044X8\ncOEpdDvIc4kFtD1noGyn8c2dFG2WY2P9R8vm1UMFui1neuzk2qwbrpjlLUU5\ntNMJvmLfBZJgGXdJmP2eGd2ExfzyAwfyqxTXh4DuaXC1bchYJib1JCUNAeN+\n0A+kBHlHU7MTc/8T9VB7pNPAAEBm/Z09uWFSwGEbW2cMDgnYTcfmee9X2GKb\nevRpYTrcTtjmu5+wFvKyoujf8Rb0ai/DdHiZpE7E7zapBXfd6wxmWzha3TK5\nm7p5W5+VMNXks4gUPl5GLarZYEtj3QDOG4eHRIb1mUnh1281+UV7W9EAQG1o\nB2mr\r\n=/hOA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0ae83a20f62a9661b4a8be2e4ad8c8e66c3e602f", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "clear-module": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_2.1.0_1576687925304_0.5159822599894412", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.1.1": {"name": "terminal-link", "version": "2.1.1", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@2.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "dist": {"shasum": "14a64a27ab3c0df933ea546fba55f2d078edc994", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==", "signatures": [{"sig": "MEYCIQD6lIa+9UMa1e5+iwQC9UiAZcUJH8mAU+kfwgau7JqCzAIhAPdqOWZuRg3kALWIlozAYNSDNTti/W6nXgNXOxpSydhl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+pyOCRA9TVsSAnZWagAAUKQP/iQ/mtCc+TJPa4Sh7TPm\nDuKr293pNo0m7hE5Qt2mwVkorEoSqANEGDRdr0DSwKkt1O7c/23gyZNB30/a\nhKrdpLL4nWV9pj8rj5a+aJVXC2iRPxgdDjSIgta420XByPHvjWXpioEsGezK\nBDvUzpKtFW/rMOm6mgq0QbscQH3BuoTzz3ar7haNInJuN4YZls1e3QsV+jB+\nz1geE3jo+0GF5Pl4dGARwUkRE9E8qCZ+rbg3Cfj14bqedl0DOQqYuwWMqCIB\nb7CJrRgHLGqFUK26D+r2mKkd30tSjVRXnTrq+qylgYOtOf1ivHw7Q8BIgzxN\nlMKYe4Gg/oiF8oId/HyqRYBjo2qwvbGRf7GlhTO9YT4iXAC27PZMv9lRqsX+\nRYqZqcCJvhn/F4TfBg/SA21brcUJasmaStP11LWp6t/v17UcqkJUWguAnATc\nlW5W2t1suI3YDUeO9mgIciBOaKj0OaTiWBqyuog7fIl83bIa2D7wp2+Sxerl\n6LbZVBBiukRMEBbubO0/gGbXjm2aSiMR/Q4RlIfVf4K26GjWI2BIoLLzhbW6\nH/E8eTE5B6ESN2xwGRbCZxyduMhpyP7HDwKtnVNXxQBKjrw6CY/d4CMbP91P\nX06udtOs/SepC4JnaoYZZ+8vA4P10TKV869a5crX12f6lqWnkLNvyn6PDr+P\nCahQ\r\n=T+fG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "647308d3dc4a67f13eb9b1546d82a508fffa5902", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "clear-module": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_2.1.1_1576705166069_0.11704365550308271", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "terminal-link", "version": "3.0.0", "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "terminal-link@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/terminal-link#readme", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "ava": {"serial": true}, "dist": {"shasum": "91c82a66b52fc1684123297ce384429faf72ac5c", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-flFL3m4wuixmf6IfhFJd1YPiLiMuxEc8uHRM1buzIeZPm22Au2pDqBJQgdo7n1WfPU1ONFGv7YDwpFBmHGF6lg==", "signatures": [{"sig": "MEUCIQDZkQVwPI2vrSRHvVSMdaTniiVBVBl0neCin0gEKT+MAgIgdNNQB3exLSnKE5SkMy63qBPFxi9rtOsJkrXZMVL/PPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge8VRCRA9TVsSAnZWagAAMCMP/3r4ncjXxvwTdfid2PSe\nR7wW2/wxDlJQA0rnGwm4PQkQ1edGs2bq9yMCeb7ArfFDHkRpUr9K/ZMvsiz/\nnmHeL1+K8h8Qd0l3OT4jWGfN448GgARLKWQlMpbSqHkvMzDrGP7eyVq42ePr\nvHSuqC9W0+4r587ZqiUPfM5igUFbIgTB6lZRw9AUGjhrQOogTpulhqIRJ0T+\neVc9A0f5q1VQhcNEXrqyvHdFJCp6WtSidzdReSA2cJcpgJK0UDcqO0sUf8sa\nw7P/fBqabLxTJgsqb/wHX/f0B4Gt05o0wXKH6E1VMbThOFziUgUxhb8q4/it\nn10TghZEA2Is9fxG/0mYQVs6dIm6s2uN16r7PF38WRZSzso79GMZOCIFyGCe\ndLoKPRyDDANQh9MSX7IQ/7q7SkV+oEjYpFH4xjeyiOPZl/WoRNZX35CCqO5g\nvQYVY2AW268xGgXs8U//1VQHCZ7LzL/kfBuiMEVHafYAqvwT13qNzGqYuCTU\nW/fnYAvsA1XRF2xmxWzaW8uYTYcZ/gA+5jXcvsAt+a042PF3ct6Kf1racv3s\nJN0mUx9vpxcNW9+aXMF2/sj3xtXS+GdW9ByYzxOOL7bRfFkrI+zmFYFKJRKz\nm0aq5nJFS88MLNrpT8pQI/Z+1q49/35NOEv+11NoTL0+WyBK90jwBlNFHOuG\n6TuC\r\n=qbXd\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a925ecd9f2e6975505d801b5e352a05a9ff5921a", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/terminal-link.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Create clickable links in the terminal", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"ansi-escapes": "^5.0.0", "supports-hyperlinks": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/terminal-link_3.0.0_1618724176904_0.2766692629835277", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "terminal-link", "version": "4.0.0", "description": "Create clickable links in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/terminal-link.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"//test": "xo && ava && tsd", "test": "xo && tsd"}, "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "dependencies": {"ansi-escapes": "^7.0.0", "supports-hyperlinks": "^3.2.0"}, "devDependencies": {"ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "ava": {"serial": true}, "_id": "terminal-link@4.0.0", "gitHead": "1fa2892d27f388ea1cf9a2c934470fc94dda2115", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/terminal-link/issues"}, "homepage": "https://github.com/sindresorhus/terminal-link#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-lk+vH+MccxNqgVqSnkMVKx4VLJfnLjDBGzH16JVZjKE2DoxP57s6/vt6JmXV5I3jBcfGrxNrYtC+mPtU7WJztA==", "shasum": "5f3e50329420fad97d07d624f7df1851d82963f1", "tarball": "https://mirrors.cloud.tencent.com/npm/terminal-link/-/terminal-link-4.0.0.tgz", "fileCount": 5, "unpackedSize": 6377, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHVAXSmvyuByQCCn1qYdd9T7Qmi0THjooP23NF+U2E4gIgCc7MvCE51XmHkCN46/nJU7gcQdQjdP1Ura1q8FtxZS4="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/terminal-link_4.0.0_1742929901949_0.011846698217919283"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2018-04-04T17:35:34.414Z", "modified": "2025-03-25T19:11:42.347Z", "1.0.0": "2018-04-04T17:35:34.482Z", "1.1.0": "2018-04-20T09:27:47.364Z", "1.2.0": "2018-12-22T23:42:22.711Z", "1.3.0": "2019-04-06T11:22:04.902Z", "2.0.0": "2019-09-01T05:07:03.772Z", "2.1.0": "2019-12-18T16:52:05.428Z", "2.1.1": "2019-12-18T21:39:26.256Z", "3.0.0": "2021-04-18T05:36:17.063Z", "4.0.0": "2025-03-25T19:11:42.128Z"}, "users": {}, "dist-tags": {"latest": "4.0.0"}, "_rev": "2461-002d80d00c2a88f1", "_id": "terminal-link", "readme": "# terminal-link\n\n> Create clickable links in the terminal\n\n<img src=\"screenshot.gif\" width=\"301\" height=\"148\">\n\n## Install\n\n```sh\nnpm install terminal-link\n```\n\n## Usage\n\n```js\nimport terminalLink from 'terminal-link';\n\nconst link = terminalLink('My Website', 'https://sindresorhus.com');\nconsole.log(link);\n```\n\n## API\n\n### terminalLink(text, url, options?)\n\nCreate a link for use in stdout.\n\n[Supported terminals.](https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda)\n\nFor unsupported terminals, the link will be printed in parens after the text: `My website (https://sindresorhus.com)`.\n\n#### text\n\nType: `string`\n\nText to linkify.\n\n#### url\n\nType: `string`\n\nURL to link to.\n\n#### options\n\nType: `object`\n\n##### fallback\n\nType: `Function | boolean`\n\nOverride the default fallback. The function receives the `text` and `url` as parameters and is expected to return a string.\n\nIf set to `false`, the fallback will be disabled when a terminal is unsupported.\n\n### terminalLink.isSupported\n\nType: `boolean`\n\nCheck whether the terminal's stdout supports links.\n\nPrefer just using the default fallback or the `fallback` option whenever possible.\n\n### terminalLink.stderr(text, url, options?)\n\nCreate a link for use in stderr.\n\nSame arguments as `terminalLink()`.\n\n### terminalLink.stderr.isSupported\n\nType: `boolean`\n\nCheck whether the terminal's stderr supports links.\n\nPrefer just using the default fallback or the `fallback` option whenever possible.\n\n## Related\n\n- [terminal-link-cli](https://github.com/sindresorhus/terminal-link-cli) - CLI for this module\n- [ink-link](https://github.com/sindresorhus/ink-link) - Link component for Ink\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right", "_attachments": {}}