{"name": "cli-cursor", "versions": {"1.0.0": {"name": "cli-cursor", "version": "1.0.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "3d9f4365bbc3144b249dd7103b65f3ab0135b528", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-1.0.0.tgz", "integrity": "sha512-kVeP8VmJU76ptLqEW2VqiB5YHjKNqBIaoqfSrW+3fN60bd33W+admJ2np507BDYDCA93Dlp7fKWtMo+KQ4YP/w==", "signatures": [{"sig": "MEQCIGqN+m350nJE7cKnYuTY+mXQzgCui2pWnlfLvpLeMKXVAiAKzCjvPDeQNUlaV338JYiteBvHXNCH6ixs9CPvM5KVKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "3d9f4365bbc3144b249dd7103b65f3ab0135b528", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/cli-cursor", "type": "git"}, "_npmVersion": "1.4.9", "description": "Toggle the CLI cursor", "directories": {}, "dependencies": {"restore-cursor": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "contributors": []}, "1.0.1": {"name": "cli-cursor", "version": "1.0.1", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "282b7a20ff03061b7c720e08e4659f47eab34efe", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-1.0.1.tgz", "integrity": "sha512-3nJG1gDsJ+c2gr9vcfB8XOEOGnqS1mvtWXZvigYbId/seCk68dVXQOcFxDVw+KhoF2FDc7IXa/NjPxibjDAOgw==", "signatures": [{"sig": "MEUCIQDKv5lYT/plxXaKLKyz9ehuRHLqwkXORq73sXin1FKjJwIgSIPzKg25T5liDl7jGNA/igDD7jZqiLZjd1M1VGpF9uQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "282b7a20ff03061b7c720e08e4659f47eab34efe", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/cli-cursor", "type": "git"}, "_npmVersion": "1.4.9", "description": "Toggle the CLI cursor", "directories": {}, "dependencies": {"restore-cursor": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "contributors": []}, "1.0.2": {"name": "cli-cursor", "version": "1.0.2", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "64da3f7d56a54412e59794bd62dc35295e8f2987", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-1.0.2.tgz", "integrity": "sha512-25tABq090YNKkF6JH7lcwO0zFJTRke4Jcq9iX2nr/Sz0Cjjv4gckmwlW6Ty/aoyFd6z3ysR2hMGC2GFugmBo6A==", "signatures": [{"sig": "MEUCIHBlKI9NdFZOd4V2J6BD4lQSSQHQy06wpd2uFBHeknoOAiEAmR3ZdjkxSdKCYuAT1Tq3jcjrDAv/G2VQ/ItgNo3Nj7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "64da3f7d56a54412e59794bd62dc35295e8f2987", "engines": {"node": ">=0.10.0"}, "gitHead": "6be5a384d90278c66aa30db5ecdec8dc68f17d4f", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"restore-cursor": "^1.0.1"}, "devDependencies": {"xo": "*", "ava": "*"}, "contributors": []}, "2.0.0": {"name": "cli-cursor", "version": "2.0.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "f4c77840ecb2bed38c2672df39e264d54a0c1148", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-2.0.0.tgz", "integrity": "sha512-ywUurX98mpNoTE8LvXv6Tntx9uUuMiQ9LyoiVL7XFcb4hyYbxW80xQ5iF/k61SeTLBdVslOPgoLCL5tlvgXpsw==", "signatures": [{"sig": "MEQCIFUo3nhLbyoLcJgBRaWm0AL5TPj38ah9MJpdUVyBWpUDAiAS6Uxgxdor4ZmARYER9paclcVulujHAEKaTdRx2bASgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f4c77840ecb2bed38c2672df39e264d54a0c1148", "engines": {"node": ">=4"}, "gitHead": "eb49635ade57270d824961a6bffe42610fc3bee7", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"restore-cursor": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cli-cursor-2.0.0.tgz_1483989942414_0.22937274118885398", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.1.0": {"name": "cli-cursor", "version": "2.1.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "b35dac376479facc3e94747d41d0d0f5238ffcb5", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==", "signatures": [{"sig": "MEQCIHB22z6KxkngHUNOVD3MSgd4aHujjNGbHyWIMykCMOWlAiAwyVTSm1cYqXwcpooHcp/eQu4NtaMmWhZffdNU984r/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b35dac376479facc3e94747d41d0d0f5238ffcb5", "engines": {"node": ">=4"}, "gitHead": "5a403335e6b3980a1235b71f8afe1d63ee8c3ce1", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"restore-cursor": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cli-cursor-2.1.0.tgz_1483990808692_0.16963833128102124", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "3.0.0": {"name": "cli-cursor", "version": "3.0.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "f6cc68b8ae372a18894e59fbf2ae3bd659731c15", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-m7avcYLWHHQVU+cFxu301q3kKZJlcZcKXQSL9kffYnIvRNtqX+a7gJKXqOKusHoKXr4oquSgiMlAo1R0dDkSZA==", "signatures": [{"sig": "MEYCIQD+gl1oHslgP6Yb1H2WRlxU5idn+dR5ZkkVS1PLvl4nAAIhAJd6FwBJFbISYLGEvlYqQlB132qauXJN9CuFBJJglg4R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrCWfCRA9TVsSAnZWagAAQg8P/iqax0Sh5+WqxIR23Gh/\nuMHRC/fMBGwohqF8jCjqDyiR4HFuSqi8i4s9VLD8bpGgUY+9HtB1YFXjgT5p\ntDLo7zfnVrrlq/0UPJJgUaJwDgU5sM6rFIRjjWitnmP/bppzt4w56QT1ZE3w\nCTdTUva5Yu2dWGFv7plN5WNy3lq5V1av4nY8vc16K2e2Dh7gXPH7pTeAXq3Y\nkPWF8sFondZbJEjDk82LuYLbbx4qch1lWjsTk4wiLyn64Bcg7s98LI75xb2a\n3l/21aogVXe9dEB7x+eD97xvV0U46LT5dneXnyHpUJhMhbJLQcET3DEI5BLh\nHGgzhth1KTE4NZcAP9+mqVr9Wk5q0Cp/5nLKWFjspOpnAJ/JX/V/fkVkHulz\nE36P5ZdKqnYGpTirVxRoE3tPBr5lVO20HNq3pxmfLw7K9RY2RwkN8vxOGJhU\nYJ3CgERcygbc2/t4I791OIsr+NFMjPoWZfiaW/8iajlpUvOSnjO2w3+TQWe9\ntPX6IRC/PHFu2alXf9j3rT9GmKRpntQn8yeBQmGR4HVCFph8Y8/1Q6Gf19wt\nniMV9TS8qxx2xaqUMefIkMZYEzXnwNtsJcR9xYruJutKVBe1AJbgNoK8/+X7\n9zZ97xOVQFDVbjItUYODkjHLeLq86MsnW9IkoVrukwWvUbc22tZT84Mx3xa6\nuUsA\r\n=s6Zb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "2a72de1ce73c99f44509be7219ebc4eb90bdfa02", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"restore-cursor": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "@types/node": "^11.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/cli-cursor_3.0.0_1554785694351_0.7813029275132335", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "cli-cursor", "version": "3.1.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "264305a7ae490d1d03bf0c9ba7c925d1753af307", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "signatures": [{"sig": "MEQCIHdnC57vmyz+DeZwcb4LqlwAKhAy3qHAJJqXaEtuqtHYAiBVhXHcx/ekkzFeQTQ75RDAHPYSeH6VI/2zZYZDIXRJfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/92BCRA9TVsSAnZWagAAdZgP/0sTf3N0eXnmmQd3QcUJ\n2cJjxEDw+TcubH3nPCES+Mr/m15ONMCCaKxC9Kkirb7SwGFe33Svcu5wOM9f\nshjVlh+3h9A55Xl02ZnBjhM9fOaVg3/InD8ku25HvPwdUvl1AmccqIVyuoQg\npYvIVmf5CjrOFQoMh0KRjaBxR7sXtO4gmP+AA23UzLYOPeNlmgglZ8YuSsR0\nGnR+40I+E+a0ghj+J6eCh/C3e5OKGMM46501ew/g3cZMSm57keNTup0KLVBW\nZfwfgVgnLTU0+976Wu32G/+JglkQEWqeKGGYhAOMLCL9eM8Bkji4okwJe+iF\n2JVKbB310AzSUgYSvuCPq/V1yKKIkK0LNgE0GJ6cpgYqRcvK0ECS6+qyETXQ\nHf3nnoh+x322P43iXjjPcBRNxB8N5n/PKP6/7D3vTtHbrFXNQsX/vODOGxkx\nUDm2ysVmYi1x5CPTjE0t83BK9VcJ8S3m/dl2rjSeE+Z1KvFhdaZPIfMxQSWF\nFW+9Qa9Oq8R1sOy+W6cKa4JWEsTZsQls068G0nPyuHhGd3Kjo76+wYE7OAie\n6h6PEPrDeiwikrsXgrxDsdrO3YZX8tsQ716OzC/tNrNKSYC21giIiNLryYaX\n2u9GJhGS1gAp/W0v7Rpvnyy4HOB0md0+OcSO/bG+BzYl28BNvBCtuS4KU2Re\na5MJ\r\n=Ezm1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "49edacfb841a9dac691972c2aa5d40dba8e0b56b", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"restore-cursor": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "tsd": "^0.7.2", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/cli-cursor_3.1.0_1560272255624_0.6903118486191766", "host": "s3://npm-registry-packages"}, "contributors": []}, "4.0.0": {"name": "cli-cursor", "version": "4.0.0", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cli-cursor@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dist": {"shasum": "3cecfe3734bf4fe02a8361cbdc0f6fe28c6a57ea", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==", "signatures": [{"sig": "MEUCIAJlBWPa5eV6HyMxCLPeFlpTbZkJyPEwwI15hVzx9hzwAiEA9ioZywu4DizCvY450NZi+2YO3qoQYddW2hJdx0/Xq9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhI/h3CRA9TVsSAnZWagAAOQwP/RzfdENraYG1wf1NPC/5\nhDyYTCLmoN1gsbEFUBEQOwHhrZrLzaIJFQZtaMp8mb3PrUromEwu9pmI2mCL\nigoj4RDWO+psVY/75huZZKaWs4PyQJsHoGICnOuC2sGjiEZkJ6hFKynz/U2V\ncp8by0KCRki4Hw2cjBG9Sv/2zolibHhNgZEDmJtz6T8IHls4FiCTXyl3uHAZ\nxmLRMIm3jEXA3IBdRVQjsSZwN7D9m0Avk/Yo90ors8FMLcd1F9jB7cqy+6KZ\nOg2lE3buDto/oJPB8b2sX1Fl2oTp3Ud1R74qun8knCoks72pE9HRhFI3+0kI\nMUQxQuGkOhxw+4Dy5LiXkWkGz1ScHWNtVDtDUYwuj7n4T+sEu5udJYQCLeNQ\n4NxvrSdECdnzbI8Qf+zRrKdtS96Fd7yFtfKCv8sN1XWD80PmyfQ3lBPSE5cQ\n1eizhmuK+i9VFKrZLFIT7+qdU6oiMlZk99kzPveTHvjK6Z+dNtHiUBVcu9n7\nIH765jJWlo60ssRcPkawu4OdfBBkksRuAvcLOSfon8nSEvjo0iAYLE24aGlv\n4GyBoYFPH0TxzI3yMrqg1FLybjOZXqY4jRi/vy0ZxXEDSmP8alYyf6U2YMrh\nhmjWyfAUCW+ocBr+0bd85zxUUorXXzIpFwn2R9hmpVZ1nTJMt/u0Quua870q\n3y6I\r\n=MZoJ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "e5722c2b3967dd43f12cc3c4a102f8089e3b7a34", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/cli-cursor.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Toggle the CLI cursor", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"restore-cursor": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "@types/node": "^16.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/cli-cursor_4.0.0_1629747319834_0.4336124733288802", "host": "s3://npm-registry-packages"}, "contributors": []}, "5.0.0": {"name": "cli-cursor", "version": "5.0.0", "description": "Toggle the CLI cursor", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/cli-cursor.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "dependencies": {"restore-cursor": "^5.0.0"}, "devDependencies": {"@types/node": "^20.14.12", "ava": "^6.1.3", "typescript": "^5.5.4", "xo": "^0.59.2"}, "ava": {"workerThreads": false}, "_id": "cli-cursor@5.0.0", "gitHead": "b97e13ef5de9b735fe7cb84eb5550a47f2296664", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==", "shasum": "24a4831ecf5a6b01ddeb32fb71a4b2088b0dce38", "tarball": "https://mirrors.cloud.tencent.com/npm/cli-cursor/-/cli-cursor-5.0.0.tgz", "fileCount": 5, "unpackedSize": 4109, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3AsDT1rcu8IQCx/SeFii49MlCYLTddTvvA1qX46+X3AiEAuHmnXSx+cy/IQRF79xCx7nJt0kbjI2Q+jjCEow2r7XM="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-cursor_5.0.0_1722002546043_0.7046480980288738"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2014-08-31T11:48:41.699Z", "modified": "2024-07-26T14:02:26.439Z", "1.0.0": "2014-08-31T11:48:41.699Z", "1.0.1": "2014-08-31T23:31:41.380Z", "1.0.2": "2015-09-18T13:47:26.541Z", "2.0.0": "2017-01-09T19:25:42.647Z", "2.1.0": "2017-01-09T19:40:10.885Z", "3.0.0": "2019-04-09T04:54:54.592Z", "3.1.0": "2019-06-11T16:57:35.876Z", "4.0.0": "2021-08-23T19:35:19.940Z", "5.0.0": "2024-07-26T14:02:26.233Z"}, "users": {}, "dist-tags": {"latest": "5.0.0"}, "_rev": "6385-fd879c869d07372a", "_id": "cli-cursor", "readme": "# cli-cursor\n\n> Toggle the CLI cursor\n\nThe cursor is [gracefully restored](https://github.com/sindresorhus/restore-cursor) if the process exits.\n\n## Install\n\n```sh\nnpm install cli-cursor\n```\n\n## Usage\n\n```js\nimport cliCursor from 'cli-cursor';\n\ncliCursor.hide();\n\nconst unicornsAreAwesome = true;\ncliCursor.toggle(unicornsAreAwesome);\n```\n\n## API\n\n### .show(stream?)\n\n### .hide(stream?)\n\n### .toggle(force?, stream?)\n\n#### force\n\nUseful for showing or hiding the cursor based on a boolean.\n\n#### stream\n\nType: `stream.Writable`\\\nDefault: `process.stderr`", "_attachments": {}}