"""
执行监控器
实时监控和协调Agent执行过程
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from .task_analyzer import TaskPlan, TaskStep
from .agent_selector import AgentAssignment

class ExecutionStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ExecutionEvent:
    event_type: str
    execution_id: str
    step_id: Optional[str] = None
    agent_id: Optional[str] = None
    data: Dict[str, Any] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class StepExecution:
    step_id: str
    agent_id: str
    status: ExecutionStatus
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    result: Any = None
    error: Optional[str] = None

@dataclass
class ExecutionPlan:
    id: str
    task_plan: TaskPlan
    assignments: List[AgentAssignment]
    execution_mode: str = "sequential"  # sequential, parallel, hybrid
    created_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

@dataclass
class ExecutionResults:
    execution_id: str
    plan_id: str
    status: ExecutionStatus
    results: Dict[str, Any]
    execution_time: int  # 总执行时间(秒)
    success_rate: float
    created_at: str
    completed_at: Optional[str] = None

class ExecutionMonitor:
    """执行监控器 - 实时监控和协调Agent执行"""
    
    def __init__(self):
        self.active_executions: Dict[str, ExecutionPlan] = {}
        self.execution_history: Dict[str, ExecutionResults] = {}
        self.step_executions: Dict[str, Dict[str, StepExecution]] = {}  # execution_id -> step_id -> StepExecution
        self.event_listeners: List[callable] = []
        
    def add_event_listener(self, listener: callable):
        """添加事件监听器"""
        self.event_listeners.append(listener)
    
    def remove_event_listener(self, listener: callable):
        """移除事件监听器"""
        if listener in self.event_listeners:
            self.event_listeners.remove(listener)
    
    async def _emit_event(self, event: ExecutionEvent):
        """发送事件给所有监听器"""
        for listener in self.event_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(event)
                else:
                    listener(event)
            except Exception as e:
                print(f"事件监听器错误: {e}")
    
    async def execute_plan(self, execution_plan: ExecutionPlan) -> ExecutionResults:
        """执行任务计划"""
        execution_id = execution_plan.id
        
        # 初始化执行状态
        self.active_executions[execution_id] = execution_plan
        self.step_executions[execution_id] = {}
        
        # 发送执行开始事件
        await self._emit_event(ExecutionEvent(
            event_type="execution_started",
            execution_id=execution_id,
            data={
                "plan": execution_plan.task_plan,
                "total_steps": len(execution_plan.assignments),
                "execution_mode": execution_plan.execution_mode
            }
        ))
        
        start_time = datetime.now()
        results = {}
        successful_steps = 0
        
        try:
            if execution_plan.execution_mode == "sequential":
                results = await self._execute_sequential(execution_plan)
            elif execution_plan.execution_mode == "parallel":
                results = await self._execute_parallel(execution_plan)
            else:  # hybrid
                results = await self._execute_hybrid(execution_plan)
            
            # 计算成功率
            successful_steps = sum(1 for step_exec in self.step_executions[execution_id].values() 
                                 if step_exec.status == ExecutionStatus.COMPLETED)
            
            status = ExecutionStatus.COMPLETED
            
        except Exception as e:
            status = ExecutionStatus.FAILED
            await self._emit_event(ExecutionEvent(
                event_type="execution_failed",
                execution_id=execution_id,
                data={"error": str(e)}
            ))
        
        end_time = datetime.now()
        execution_time = int((end_time - start_time).total_seconds())
        success_rate = successful_steps / len(execution_plan.assignments) if execution_plan.assignments else 0.0
        
        # 创建执行结果
        execution_results = ExecutionResults(
            execution_id=execution_id,
            plan_id=execution_plan.task_plan.id,
            status=status,
            results=results,
            execution_time=execution_time,
            success_rate=success_rate,
            created_at=start_time.isoformat(),
            completed_at=end_time.isoformat()
        )
        
        # 保存到历史记录
        self.execution_history[execution_id] = execution_results
        
        # 清理活跃执行
        if execution_id in self.active_executions:
            del self.active_executions[execution_id]
        
        # 发送执行完成事件
        await self._emit_event(ExecutionEvent(
            event_type="execution_completed",
            execution_id=execution_id,
            data={
                "results": results,
                "success_rate": success_rate,
                "execution_time": execution_time
            }
        ))
        
        return execution_results
    
    async def _execute_sequential(self, execution_plan: ExecutionPlan) -> Dict[str, Any]:
        """顺序执行任务步骤"""
        results = {}
        
        for assignment in execution_plan.assignments:
            result = await self._execute_step(execution_plan.id, assignment)
            results[assignment.step_id] = result
            
            # 如果步骤失败且不允许继续，则停止执行
            step_exec = self.step_executions[execution_plan.id][assignment.step_id]
            if step_exec.status == ExecutionStatus.FAILED:
                if not self._should_continue_on_error(assignment):
                    break
        
        return results
    
    async def _execute_parallel(self, execution_plan: ExecutionPlan) -> Dict[str, Any]:
        """并行执行任务步骤"""
        # 创建所有任务的协程
        tasks = []
        for assignment in execution_plan.assignments:
            task = self._execute_step(execution_plan.id, assignment)
            tasks.append((assignment.step_id, task))
        
        # 并行执行所有任务
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        for i, (step_id, result) in enumerate(zip([step_id for step_id, _ in tasks], completed_tasks)):
            if isinstance(result, Exception):
                results[step_id] = f"执行失败: {str(result)}"
            else:
                results[step_id] = result
        
        return results
    
    async def _execute_hybrid(self, execution_plan: ExecutionPlan) -> Dict[str, Any]:
        """混合执行模式 - 根据依赖关系智能调度"""
        results = {}
        completed_steps = set()
        remaining_assignments = execution_plan.assignments.copy()
        
        while remaining_assignments:
            # 找到可以执行的步骤（依赖已完成）
            ready_assignments = []
            for assignment in remaining_assignments:
                step = self._find_step_by_id(execution_plan.task_plan, assignment.step_id)
                if step and all(dep in completed_steps for dep in step.dependencies):
                    ready_assignments.append(assignment)
            
            if not ready_assignments:
                # 如果没有可执行的步骤，可能存在循环依赖
                break
            
            # 并行执行所有准备好的步骤
            tasks = []
            for assignment in ready_assignments:
                task = self._execute_step(execution_plan.id, assignment)
                tasks.append((assignment.step_id, task))
                remaining_assignments.remove(assignment)
            
            # 等待这批任务完成
            completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
            
            for i, (step_id, result) in enumerate(zip([step_id for step_id, _ in tasks], completed_tasks)):
                if isinstance(result, Exception):
                    results[step_id] = f"执行失败: {str(result)}"
                else:
                    results[step_id] = result
                    completed_steps.add(step_id)
        
        return results
    
    async def _execute_step(self, execution_id: str, assignment: AgentAssignment) -> Any:
        """执行单个任务步骤"""
        step_id = assignment.step_id
        agent_id = assignment.agent_id
        
        # 初始化步骤执行状态
        step_execution = StepExecution(
            step_id=step_id,
            agent_id=agent_id,
            status=ExecutionStatus.PENDING,
            start_time=datetime.now().isoformat()
        )
        self.step_executions[execution_id][step_id] = step_execution
        
        # 发送步骤开始事件
        await self._emit_event(ExecutionEvent(
            event_type="step_started",
            execution_id=execution_id,
            step_id=step_id,
            agent_id=agent_id,
            data={"assignment": assignment}
        ))
        
        try:
            # 更新状态为运行中
            step_execution.status = ExecutionStatus.RUNNING
            
            # 执行Agent任务
            result = await self._call_agent(agent_id, assignment)
            
            # 更新执行结果
            step_execution.status = ExecutionStatus.COMPLETED
            step_execution.result = result
            step_execution.end_time = datetime.now().isoformat()
            
            # 发送步骤完成事件
            await self._emit_event(ExecutionEvent(
                event_type="step_completed",
                execution_id=execution_id,
                step_id=step_id,
                agent_id=agent_id,
                data={"result": result}
            ))
            
            return result
            
        except Exception as e:
            # 更新错误状态
            step_execution.status = ExecutionStatus.FAILED
            step_execution.error = str(e)
            step_execution.end_time = datetime.now().isoformat()
            
            # 发送步骤失败事件
            await self._emit_event(ExecutionEvent(
                event_type="step_failed",
                execution_id=execution_id,
                step_id=step_id,
                agent_id=agent_id,
                data={"error": str(e)}
            ))
            
            raise e
    
    async def _call_agent(self, agent_id: str, assignment: AgentAssignment) -> Any:
        """调用Agent执行任务"""
        # 这里需要实际调用Agent
        # 暂时返回模拟结果
        await asyncio.sleep(0.1)  # 模拟执行时间
        
        return f"Agent {agent_id} 完成了步骤 {assignment.step_id}"
    
    def _find_step_by_id(self, task_plan: TaskPlan, step_id: str) -> Optional[TaskStep]:
        """根据ID查找任务步骤"""
        for step in task_plan.steps:
            if step.id == step_id:
                return step
        return None
    
    def _should_continue_on_error(self, assignment: AgentAssignment) -> bool:
        """判断在错误时是否应该继续执行"""
        # 这里可以实现更复杂的错误处理策略
        return False  # 默认在错误时停止执行
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        if execution_id in self.active_executions:
            plan = self.active_executions[execution_id]
            step_statuses = {}
            
            if execution_id in self.step_executions:
                for step_id, step_exec in self.step_executions[execution_id].items():
                    step_statuses[step_id] = {
                        "status": step_exec.status.value,
                        "agent_id": step_exec.agent_id,
                        "start_time": step_exec.start_time,
                        "end_time": step_exec.end_time,
                        "error": step_exec.error
                    }
            
            return {
                "execution_id": execution_id,
                "plan_id": plan.task_plan.id,
                "status": "running",
                "total_steps": len(plan.assignments),
                "step_statuses": step_statuses
            }
        
        elif execution_id in self.execution_history:
            result = self.execution_history[execution_id]
            return {
                "execution_id": execution_id,
                "plan_id": result.plan_id,
                "status": result.status.value,
                "success_rate": result.success_rate,
                "execution_time": result.execution_time,
                "completed_at": result.completed_at
            }
        
        return None
    
    def cancel_execution(self, execution_id: str) -> bool:
        """取消执行"""
        if execution_id in self.active_executions:
            # 更新所有未完成步骤的状态
            if execution_id in self.step_executions:
                for step_exec in self.step_executions[execution_id].values():
                    if step_exec.status in [ExecutionStatus.PENDING, ExecutionStatus.RUNNING]:
                        step_exec.status = ExecutionStatus.CANCELLED
                        step_exec.end_time = datetime.now().isoformat()
            
            # 移除活跃执行
            del self.active_executions[execution_id]
            return True
        
        return False