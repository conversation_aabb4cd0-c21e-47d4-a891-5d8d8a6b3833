{"name": "open", "versions": {"0.0.0": {"name": "open", "version": "0.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "open@0.0.0", "maintainers": [{"name": "jjordan", "email": "<EMAIL>"}], "dist": {"shasum": "7b5f1e7bc4f68effd4d6ba17de7ac852723b5186", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-0.0.0.tgz", "integrity": "sha512-tx0BlMx63kdQfgZiYpAxhNLMRo7xe4gJzRCqeqgd6/4eHa3vkGqldXVnCwAX4aIzHTl+FXb+a9NxS0c1bTI/GA==", "signatures": [{"sig": "MEQCIC7Pixo5L6PnYXt8HApROjUkOPM7SUYCNzKFzFYuD30oAiAf1zeBtp8FH4ySULarp1zwfER1tu1DZxXcLoFBVXZUlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "_npmUser": {"name": "jjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/fixedset/open.js.git", "type": "git"}, "_npmVersion": "1.1.12", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.0.2": {"name": "open", "version": "0.0.2", "keywords": ["start", "open", "browser", "editor", "default"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "open@0.0.2", "maintainers": [{"name": "jjrdn", "email": "<EMAIL>"}], "dist": {"shasum": "0a620ba2574464742f51e69f8ba8eccfd97b5dfc", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-0.0.2.tgz", "integrity": "sha512-gnt725gcNKTaMSul17WNEz4I3rvgVotCq30TkU9thlEZaRJ7ivOV0vEoRupkGU/NJ2+qxqAmVbSK94rwuOWXnw==", "signatures": [{"sig": "MEUCIGAFds/VsCHxkoD+71N99T1Rw9IPS8Eq3hMfjzWSU4Q8AiEAxHtKEZ8/9HDWr94ezt2RbXc9ooGkod8KsI67z/SlMnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/open.js", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "jjrdn", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jjrdn/node-open.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "open a file or uri with the users preferred application", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.0.3": {"name": "open", "version": "0.0.3", "keywords": ["start", "open", "browser", "editor", "default"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@0.0.3", "maintainers": [{"name": "jjrdn", "email": "<EMAIL>"}, {"name": "pwnall", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.costan.us", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jjrdn/node-open", "bugs": {"url": "https://github.com/jjrdn/node-open/issues"}, "dist": {"shasum": "fa377f4ff308212d92a9b8e6395240854646a713", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-0.0.3.tgz", "integrity": "sha512-Vm6nsJHcKV8kdOtHESAFAymOOf1VlKrttpwTqtLxvxRDIh8PSw4lWq5qk88QkAvNwnkv/1dNi2lvedhmam/ggw==", "signatures": [{"sig": "MEUCICvPH9Hnm9K+mBOqD7RKjFIPBoPlWV3Yraf29Ay1pdJvAiEAqJLVqEsn2Uc08FldQdURU59n5EKoruuE+KbOr8cywUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/open.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "pwnall", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jjrdn/node-open.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "open a file or url in the user's preferred application", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "optionalDependencies": {}}, "0.0.4": {"name": "open", "version": "0.0.4", "keywords": ["start", "open", "browser", "editor", "default"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@0.0.4", "maintainers": [{"name": "jjrdn", "email": "<EMAIL>"}, {"name": "pwnall", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.costan.us", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jjrdn/node-open", "bugs": {"url": "https://github.com/pwnall/node-open/issues"}, "dist": {"shasum": "5de46a0858b9f49f9f211aa8f26628550657f262", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-0.0.4.tgz", "integrity": "sha512-89TW6JswxDIlkXZ6gBscNCE7x+A3oN/J0OqGhiLNhFnWiphTVnXOMsi5ggo72DqpB5PzinTu9ZU508z7Af2TnA==", "signatures": [{"sig": "MEQCIBGemKJHDH1CFzUJH2EFeNUTyMJFYtv0GrKl4ADBmVe+AiAKxwXP0quK2AyryVbMqBgM1oou4ztsO0tST9UNGCRTAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/open.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "pwnall", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pwnall/node-open.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "open a file or url in the user's preferred application", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "optionalDependencies": {}}, "0.0.5": {"name": "open", "version": "0.0.5", "keywords": ["start", "open", "browser", "editor", "default"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@0.0.5", "maintainers": [{"name": "jjrdn", "email": "<EMAIL>"}, {"name": "pwnall", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.costan.us", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jjrdn/node-open", "bugs": {"url": "https://github.com/pwnall/node-open/issues"}, "dist": {"shasum": "42c3e18ec95466b6bf0dc42f3a2945c3f0cad8fc", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-0.0.5.tgz", "integrity": "sha512-+X/dJYLapVO1VbC620DhtNZK9U4/kQVaTQp/Gh7cb6UTLYfGZzzU2ZXkWrOA/wBrf4UqAFwtLqXYTxe4tSnWQQ==", "signatures": [{"sig": "MEUCIH7pMGtPTQQuIiyj2wMBodYAm5M+/7/XxrjXsnq3sNdkAiEAxXDmyhAKDA5tPRvCIhiSqly8kSne/G+PWwx+llDqOjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/open.js", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "pwnall", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pwnall/node-open.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "open a file or url in the user's preferred application", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "optionalDependencies": {}}, "6.0.0": {"name": "open", "version": "6.0.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "cae5e2c1a3a1bfaee0d0acc8c4b7609374750346", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-/yb5mVZBz7mHLySMiSj2DcLtMBbFPJk5JBKEkHVZFxZAPzeg3L026O0T+lbdz1B2nyDnkClRSwRQJdeVUIF7zw==", "signatures": [{"sig": "MEQCIEigHFdujZl9Ha5IiN3OnsZDUydkHjpn6Ux/R5cys6f+AiAeiFtE2PX1/DehqoJba3EkcqwFvg/l5chE8HGdybPxwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmmdjCRA9TVsSAnZWagAAltQQAJzXBQHWAieqaqgcstbe\nXP6ezB4wBSK/Ptmo1cGXPskIzwrX5gGECWA1KunF03VLvt39oNOSGei2T+ij\nwBa5LpkdVHmdvyNP7ZwZNneQSpv7SEQYpAdjD64plmKj1hiGUKbzyN78LR2p\nvOqUA/TAMlYIweeqapb2JYpr6BW8ySvz6cLH2QmDNl10nmvevTP6hoT8zf1g\nTIk5Lpe2uW8M7IfRZ2FGRRWzVZMkby79MQ0vXfycPaAdYE8KNI44vxUA7JIx\nei0XzcRjzGw6uY/vQCcDOyDozOo7BKinZP6JFQgbzNK4+Auh7wcjyXpxaxtJ\nX6uvkSGlQQ6PyVZb+JcyFzw7MW5mFVMBrvetoUJ2m91M8x7AjAUqkbf/6Ixu\nPZW8Rtlq32QQVillm3zgSiyHRkg8GEr8uo1FyMZgLh4kXNuoi9h4dkaxkPfz\nVkV6N6O995MrtCtWu6hm3EG+FwTC+Hre/j4PqLJaHDaQm+SIB4uNzyl6fMBD\nxqmH38v9wsqxJ5jO0KafwWvRG1Bl55BFzXQBpZ/1zvjMgXETWizvdGG7TKUC\ngMHIXTk3bt5o5aSvNaP5h7V/Ah1PDSSs76brIBXAt5dJTkzWuAmWVUukkKjS\n+Mj1GCdRz1HzvysJvhJ0KrMJcDvIan0UiOZGuoJwfuR0sLZ6+/C3e66Nj9kI\nuUGV\r\n=BMwl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "c3569171f477765d16349421fce1d3b2517a7b81", "scripts": {"test": "xo"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"is-wsl": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_6.0.0_1553622883011_0.17607051667538376", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.1.0": {"name": "open", "version": "6.1.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "0e7e671b883976a4e5251b5d1ca905ab6f4be78f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-6.1.0.tgz", "fileCount": 5, "integrity": "sha512-Vqch7NFb/WsMujhqfq+B3u0xkssRjZlxh+NSsBSphpcgaFD7gfB0SUBfR91E9ygBlyNGNogXR2cUB8rRfoo2kQ==", "signatures": [{"sig": "MEUCIFDY9RGPH0x/4ckI9kPWjvZRHyk8QWO4karXDQXODHLNAiEAjVZALVtUmxz+aOItGlQLKu1HhWLZKYjDU3TOM00i+OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqcW9CRA9TVsSAnZWagAAai4P/0NCJDIDSL5rfpN3kga6\nU2OwyXdr0uioxuvP80PT4JY4wbgfaT0oMIcThZgBwnZakkUStQuSAzpqzKU3\n+30suXgdeZ2kSaLqUl4O8ZUkZhLZr1o5zghafNTu0J/okW+tM0Fxinet8NPm\nRebV7TB9Fwd8UQHQ6KRkjkJ9HYaHjgpsWxQMwi10kEIeHL/8jqG5MMP7XZPw\nTmg3MeK0eUQTNYFFwSgIH9dFvt8ZEhoxyz4CD3SJvZ2JRcNsyxbff/Zi3Tmu\n+ftNmD+2aPFiSsC9yDnsRAgdKlA/LXHghWuv+x00EC2utjUuYQckwL4fKDyf\nJ9Ohae6IcHIAH9rmlu1relAY/Tjs8efywL7Ue7kKiJudQMl6YOSR/1Dg+gJ2\nIFkLo+PbZs4E7rM7G8eJ8OuKHiSi860o3HM/HEdTnEvNhozAGnuyKRbZRg+F\nP3iseqPHskSPYqaZrq+tZzBzYlWUga8isKqCTj5Azn6X4AMfc8eQ6X6JBi+i\n4FeqvsJNmq9O0SqaV81XX4eRFPOegBtf/XKsz7uOCb/Bvcfgyok7NE2X7a3W\n61Wz3M16SA0a2DITMIPghcDCRX73Nl+vUhA2kBXkeAXRSU88zs8IqOq1kg3a\neuYquTPTTrsQG0f6zOkef/LgoG7MAd1zDgU0CffKY6iL+x1w1OuqFX4ix885\nJeMD\r\n=oxdq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "48dfe3d209554584dfa448c1c19d919cfb3270f3", "scripts": {"test": "xo"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"is-wsl": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_6.1.0_1554630076079_0.11965164174216758", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.2.0": {"name": "open", "version": "6.2.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@6.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "7cf92cb961b5d8498b071e64098bf5e27f57230c", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-6.2.0.tgz", "fileCount": 6, "integrity": "sha512-Vxf6HJkwrqmvh9UAID3MnMYXntbTxKLOSfOnO7LJdzPf3NE3KQYFNV0/Lcz2VAndbRFil58XVCyh8tiX11fiYw==", "signatures": [{"sig": "MEUCID1bNuG27/24fMOkJsFeLBQBwrcSr6n5hgWyTfgkDPKUAiEAiuaBr+pgg4hMXD8XOTWLgdzxprU10kqLcIVd0qtRKyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvsj5CRA9TVsSAnZWagAAxS8QAIdaeq6BJkCOWgAw04ER\nCtBY9mRicTwyKPBdv9dzikijG/21K3qP4/5XcGIkFF0sF5JESM0jmicMpVJZ\npDFps6NWu8aNJFrJ2X3mSNKR/5hZB9sxA8JZxF64Nah6XSvOfFuvb/LGppb9\n2ePuo/RBBgLT2VZUTGNtWCWbLXpEKg0LRyzCbzOh+bwdcaVYRB1dDLUEwMHY\n0DU8uRVrI0dF3Fi++J6jD64CbaA+8MWuZ1CdHMa7v1ewiMuKCRMk8v0ynRF0\nzpOfRNiyN1GKul8CtY2kxOBxUoIopJYtd5vnYMAj9WjTWRT/QGy3cPLWB5ny\nCkj6/6fU/HXtegEG22Jk7BfzPiOH3QHCjxGIlv1WdiECi2taTvnv9KJfE8yt\n988HS/bOPm06qOagqPiP7VmgcNe6g9J9ry7PlMBD2G/kPNXZaB+9MXiQbqvq\nvGIYnHNPXPbUHkdN5a7+amne1pCOtKxdA6Jr6t3bOKf0+ImAnDR0RpcvahQJ\nOCH6Y281bxqf2i06O+4JgkVoF/H3Ry/yJ7LglPRdPgbWf47b5Tfd1axmkIUL\nFnxKZ3Wn4D9eYva6F1rYIF+eNowsbpH7V9RR1paux+WcDjDK9NF3NTH6Co1/\no95Fa3IhGRIoyQfPYTcWmbOgzxVsa/iStr/AsS3tZs/SpcDEZOxt/eEG8Hq8\nKcJS\r\n=m8Aw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "27ac3c2bfda8aba058c753e9b0b7ed9b9eeba76c", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"is-wsl": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.0", "tsd": "^0.7.2", "@types/node": "^11.13.6"}, "_npmOperationalInternal": {"tmp": "tmp/open_6.2.0_1556007160345_0.13004161557012583", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.3.0": {"name": "open", "version": "6.3.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@6.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "60d0b845ee38fae0631f5d739a21bd40e3d2a527", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-6.3.0.tgz", "fileCount": 6, "integrity": "sha512-6AHdrJxPvAXIowO/aIaeHZ8CeMdDf7qCyRNq8NwJpinmCdXhz+NZR7ie1Too94lpciCDsG+qHGO9Mt0svA4OqA==", "signatures": [{"sig": "MEUCIBBA1RufwNIxDI4urKzYKuDdBttW74CL0z8w1frF/l2FAiEAkIPcQn3CGXRxHrbXvanh/TitZyLyOz79wejxHiS1hFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc195gCRA9TVsSAnZWagAA4ZAP/2EOpTFsj8UeqlU/X8l8\nNkVrfXm7IiVfn43UkjZJNg+iHcJbeBeYwe5ZgQ/EArlIet/yHHV0iiB1hSBA\nNG+Nq9fn2LIl4nS3godtsyz6KMNZJ9gZS6pvfQgJZRU/FzpwHLu3Eau6NKZA\n4On0PbEPcUZgpbmDRrWbywn/rIc+iKkOcEBZkLtc6RPKBB4NLSbl8KZNu3bc\nCgHGVoKNTzU5O1YrmZ2G4973Xt12mJ5FKK1NtviSfneGln3Mi6IuITqJ0SsH\nXNKb7UcwbvXyffi5cOtrT1/8UABLBjZh16uMB/JvT0Ds5ht8zKsF762WOY6M\nmC3wUfnZQaJT7J/XZPmwcnfLDfN5XkAowKhnkJVBWygkmRM9hNoSEqlE4Rim\nCJZHUzZTtq71Y8yOMjgpv/vZgbck2DFXpSRGg0En5PgITV9Q+asHIguqA1d/\nVUPje8oVUeldiZanzhzTs4DHP+WgdMBsYDqGKJEh1wvO5mGC6HYYWP3hGmGs\nD9x4EocbG1voiO000IBdFYfOlGTxIiPM6sN/hTqNMc6iXygi/bwApoh6ui1s\nOGDnvGprY9eJE99u5HG8uRYkF5ZSVsJu7Vw+YTba++WHF5jWepu8nv2FgvBb\npuyOwODUTxJsemNybP/SXrmrEIU1WUp24QBkDN9/y3Or1hXY6LQap5fiyw0o\ncN4f\r\n=R4Mv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "b73529feb8543f91381b246843a3d15ab9238e95", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"is-wsl": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.0", "tsd": "^0.7.2", "@types/node": "^11.13.6"}, "_npmOperationalInternal": {"tmp": "tmp/open_6.3.0_1557651039851_0.7746317779507104", "host": "s3://npm-registry-packages"}, "contributors": []}, "6.4.0": {"name": "open", "version": "6.4.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@6.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "5c13e96d0dc894686164f18965ecfe889ecfc8a9", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-6.4.0.tgz", "fileCount": 6, "integrity": "sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg==", "signatures": [{"sig": "MEYCIQDfZ1s6G1HK9FVKAaoOAIcMrDHqT01w8+s1bZsLY6PlEQIhAONuWg452oYqOQ15va8Km8OJf3dOk5tTMiEz0/I19gqT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdF3fiCRA9TVsSAnZWagAAc5wP/iFSUApwqBVuigd64wkm\nD+9kPwBqcmGr44Tpo3tA//W7mKBBaTGA1UjHOqokkfqklgQwaqQu9bXn9Ujw\nCzF4yt6ZrpPTOwpz0OLQo+iw41VAvuhGoe9qIZrmwY7dHcj77dIupqZy9l93\nqryGQOMRVu4tiFfPVJakjekXzDxmKj48DPdn2vj0unmMLDNe/SJQmDgntXsh\n3q/S2lygTOPeG9tF6NfGS6PxtCQ5H/tnW0FLqxTrYlYmP8SoUdbCAHS6kCJb\nuHYPdreZnEbOMu3t9p/VtjRDHc7nAmDgspCaP0zeIivjTHWI+1DO2VRC1Ugg\nK2pfcC6fU61oqQyKGSYFa1Z7pB1LvhlhiEosnCX6etzIKeWtrF1sFmNLEb/f\nrtvQCn4HoVYjMgfetCeTftinoclEi76I+Bh3SH3eOc21DhJ/9ZsruJrezELC\nGWSzRZ01Sb5oo0LYTzZnp624zu4W+QXuLZnEcCgaXXQLRYK1x6hDSIHUcZfB\ndkK9djPRPXANZW7ZCasz/aUPy2EnAnhR1sInUG8Pol/5UssqRyU4YzcqTQnG\n6KoFyLg7fm87aldLKrVjRE3D+ep2lscTNSRXpbJZsKRa7j6jnXFOQB6YzAKy\nki5uVnRcoIfQGfJDwcnmURHjraOXnt2kKPag/Cn2oRfOqBqt9Ps6rKkRoUt8\nmk+L\r\n=OpRz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "37a769d6073862ab4a46c00b1a21b2ba814e68cf", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"is-wsl": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.0", "tsd": "^0.7.2", "@types/node": "^11.13.6"}, "_npmOperationalInternal": {"tmp": "tmp/open_6.4.0_1561819105278_0.010991781535895528", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.0": {"name": "open", "version": "7.0.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "7e52999b14eb73f90f0f0807fe93897c4ae73ec9", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-K6EKzYqnwQzk+/dzJAQSBORub3xlBTxMz+ntpZpH/LyCa1o6KjXhuN+2npAaI9jaSmU3R1Q8NWf4KUWcyytGsQ==", "signatures": [{"sig": "MEYCIQCkGJupIjw9w19B95t88hA9U83Fx3Rw/xcJj0hkhqhbmgIhAIJII79XJJ+vyTnR6vsCT8mLhxcLphMwORRuFF/rW7Hs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdphMNCRA9TVsSAnZWagAAulgP/1S1XCRmO0K1XQLt5Ga+\nhTqF+LzZ9NTiRDbcZjsDYBJa8oSnmB7Mo5Zh8agd/k+kSmHG0btPiMaqNSdi\nlliPm3SuvhQkC61B+kQH8fA5ie4EdlCaCm+E7SaGQXBi1zatE+slbvjuKxtr\no/cgUjdfvtodpOsLycVM7Ak+fPcLh9G2NZqyBt6oGvjvZgSVecvJqjNTqK0Q\npEoovoJ/2YrHj9UwB0qHHxIllSa7kb+GZ/Vj0GCHF2da1aDPGBFwUeO8dxvy\n1DX2uj/JzVHXSmRwY6QC/T+iNZ5iPy5OtS46Or3ciZka1KdVOYae9+TcVIft\nzIJGfeLdRxUkTm0ws7SSPjfqMtjGyxiBT2l4MmfLgREfXepginU2fCqTn6c4\nq+62Ce7YSzPfn8tFKxybICjchCLHyB9opf1IiKXsS3AZsjX6m2KZhCSOzWBR\nif5oAr58XxjzKq63rVmj+z92oSMef1+E4PDiKxCsEmlWU8FiKq0yjJROK2rV\nxUTjLuvAjbo20CERI3dppDusn4iPZM/pzbRoBAqROqGZ6hXmxTnsvqTVLdnf\nZVILArpow4GIU8sK9dL/2JyI4GBiJ9M8sKuTds+2gFXyV2bEbfndPFr6Gto6\nsZgKUALugVR7iVu2/QKnntJGrp7BMNK1+SskNTFSs2J6NThAPRdcLzv29e3p\nLl7C\r\n=AZ6a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "ed757758dd556ae561b58b80ec7dee5e7c6ffddc", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"is-wsl": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.9.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.0.0_1571164940433_0.04787258939201888", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.1": {"name": "open", "version": "7.0.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "259a6c2097da6b95c596a2734f2ed05643c432b8", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.0.1.tgz", "fileCount": 6, "integrity": "sha512-/fVm742AZt6bZ3NpbmBzGpZksDiGbo+xz8RylegKSAnTCgT5u5tvJe0cre3QxICphqHhJHc0OFtFyvU7rNx8+Q==", "signatures": [{"sig": "MEUCIDu+sZw2AEaS3ebQSBwgeKS6vb/czr8ted7TfDDAVl63AiEAm2NPiYJGQH1XnOHXkldgA94blugBpVUcAJI+JV49+LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMZkPCRA9TVsSAnZWagAA0c4P/iTGd8BgFJLCEFnMSDx0\n+9e72+R3gH44Jf0RW20gBOgeeH+Ey3n4iIliqLhZmzJtX6WHXEdXpHoZNOc6\nN+jWO7EmDTfr/gUF01UuDHtmZ3WP0Q+SpqFcWi64uY2J2L+BFR3MWO4N8G8r\nVBTibVBeO4rNFE8Y28D8k3x2t3gmKalyd+y4o8ONKG9gPm20kJJlIJUU2PSw\ncytx33Rp34kpdN9XAh6/D+Ozu5VZxFvpC4Ee9yMlhb7weuW6DHO7gLZecRbB\ntHgN/bUq8jIAYc47rnS69taSdBFYE+GPWKOY2ybQOsXkgif1wTIBGbcMNgBD\nlf8Ush+OHUvO3+K8W242NrZ4h6EZGm4sevz7cMyFoeV9VsnCGkdaJxOKQdgG\n7rc4aLJ1L0QaljdsGPTcpMrfqxKgkMQTuISt/AC1ikF+b5YVPFuxxRfFzf4r\nnPJrsET6FYdkQmiqtgJovrAweb3A7bVY+MpNDyJ+7WAmS8NKX8ED9FNmkJ+S\n2mQLB8ytCJ3aL7sdBzWZoaj9k/JowsDqJLZqzfW70EVvpbTmDbXDtB1FWovk\nz++yJSHFr5Au4GEP0ToJwpQgJvU1jNInQm1OCdoc4T3pj6W4IjzAZvVhd0SM\n1rSiEUE/mfZF5NyNg/hhoP9O/sujQXXbP6dW6atiaEXElUBpJpc/XaR1kuG3\nowcf\r\n=OnCI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "79a90bfb2cf0ff6e22eedce3e6c8bfa7c4ea7021", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.0.1_1580308750649_0.5905894351050964", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.2": {"name": "open", "version": "7.0.2", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "fb3681f11f157f2361d2392307548ca1792960e8", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.0.2.tgz", "fileCount": 6, "integrity": "sha512-70E/pFTPr7nZ9nLDPNTcj3IVqnNvKuP4VsBmoKV9YGTnChe0mlS3C4qM7qKarhZ8rGaHKLfo+vBTHXDp6ZSyLQ==", "signatures": [{"sig": "MEUCIF+69qK9RaC2SVrb0bEe1YPb0iOKKCQaJkfkVAL3wYTdAiEAgWZPzEu8Zc9cbWj/9gDgipknfcVBqDcbFWTbWIJPh5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNTMeCRA9TVsSAnZWagAAIfQQAI2qAbvE3ivY6HHTwmov\n7v/iswNohyrtrNPP52JJLUbWXYe1UZcjVYHzCskTlnE/zZb+4Bl8qjc/0Wwm\nHTzCB0XEQQL4NqdZs7jqnbxvmcUtLIV9vebXrnYYWe/ubU5HVScnytA21ZJC\nkqIj17btx0mQrlfoyQvKwmq83WVcmOXvLCrWBeo2dZ9YxbGqJ1m1cXrHatH8\n1tP/nh6ain3wsGF0noBElppPoxGzxbVjSS97tcA0c/ygSTsIqxdi5DqghxFZ\n0c/+xGE2BPAb4fHF+M+uY62T+I6dGTDl9d8We8veoC5hyUq5Jq7wBDvbTe64\nTBYP2VSe4gn20T2j8h6xgzBprpBSzEq1h7AN9SBPuXWZme4KsSMQC5p6e8Er\n17hjGRkbt/bPLkIVxInDgeeFCVuRgnS8Hz3yvj//bd9nr8G2APRzK90pernH\n1s6LXwx3Rq4e+JyPLw/sqQ22gTAc2jofXwcJ0lp0IPaNAIQyzmJF3s5GLl4V\nivWdeiQcBELh/946ODs8dW+bcC+BkOREJDah+3TsXRbuvjEkcPIo9vDRns8y\nYSost02VY8XHxl6bGz0bDPQ0CZ/OybFI8mj++mrY/FO3kcdeX8Mx8eCTdr+z\neRi0uQfIox6Dy400O9T6n7zVV+NsVKLO2iH8f/NLgRqMtoVDLMvlrp4keqKQ\nOJ0w\r\n=PgpK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d8bf43e8435c00952bbe41cf5d40d52cf0b45242", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.0.2_1580544797415_0.3206222834581336", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.3": {"name": "open", "version": "7.0.3", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "db551a1af9c7ab4c7af664139930826138531c48", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.0.3.tgz", "fileCount": 6, "integrity": "sha512-sP2ru2v0P290WFfv49Ap8MF6PkzGNnGlAwHweB4WR4mr5d2d0woiCluUeJ218w7/+PmoBy9JmYgD5A4mLcWOFA==", "signatures": [{"sig": "MEUCIQCrRFomUho5+kpzYkIlp5A8mBKD1uIoZOnuEjrzMJ6ebQIgIfB06tp87igv9dKOEJhgkitcCnw4FYmz/j4CPI5s8Jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZd9CCRA9TVsSAnZWagAAfUkP/jKFUlf1PxnBB5CHY2li\nAPQ3CAQKr0JO3deFStUq5FeJg+81zYwnFEPerrIOitZX1+umyiDc6Wvh2DQl\nI7tryS9eYdSAWplhvQfkIcSRSD0ApMozG/CvzaCdGqtMpRU0+qFwk+4WS1y+\nlVVRPL7Ies17jOcWxyfeDVuCtrm4jRINrFL9+XHH2hE1X0J5ryfFRRZWkShn\n55dkDo8xXVfXEdBPWQLC7pGvRmc3KHanmh97t5ZRjoKND1s/DMwoffmZVjUU\npK+A9dmAb9xgYWj4WuxO6kZPwmNFauCVr+L2PB81/iwgxqpUZQ6qC1G/Omtv\n7dHdyfYA6aOgrEPV0K7hLdcQxq7WpyiIz7Qgw56lk7xt54b1rBMeVipgf/ZG\nxXtiWGfdNUs2DksnNgBpaBPXF8pcpZkAuZV8kI9KyNjZphrTiOT0gv3P7WeL\nSjvBwufxPuUBVS8JfYZ/wCZsr3znYMPF6qJqDFun16qRXvmBAgqW/PJ4Tq72\nU2tmquxK2s52VgxlLx2rWHb8FKYTE0W6ErnBB9IN8qsmtV9JEZiayebHDiEL\nntYh6rP1rNo37aByY2LbEXEDqKPrKhKu1WvM03Fk95/2ATf7RS4MBgOCK1bV\nqDNN0oS5L/l3kh3XABFFgjBj8moTDTTG9XGku1GfM0QfXr5ekNjHOGmMnOzf\nrGem\r\n=1T2+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "cc7b781edbcee122836140a260a254aa5c64fb6a", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.0.3_1583734594407_0.6643809701528305", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.0.4": {"name": "open", "version": "7.0.4", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.0.4", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "c28a9d315e5c98340bf979fdcb2e58664aa10d83", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.0.4.tgz", "fileCount": 6, "integrity": "sha512-brSA+/yq+b08Hsr4c8fsEW2CRzk1BmfN3SAK/5VCHQ9bdoZJ4qa/+AfR0xHjlbbZUyPkUHs1b8x1RqdyZdkVqQ==", "signatures": [{"sig": "MEUCIQD/tR0yVans39JIte/GkdDzHQdaE/lpcDe8wYUtWGpb1AIgGCZ+pyim7vEQ4LgeM6Bs3fMekgW21vBCGRw33h4Ji6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJev6wrCRA9TVsSAnZWagAAnowP/0kgT7o27tAwFK+AYa4c\nDiypmCu4dLRJyhX7y2P3+g3bwUSYeshXyZYyY0LkVysqvqXkmqTh0UBFDpFU\nRj1fxcwt0Z4Kuqm1/dyWySZFsxk55ZE8FO3ebgffpIMZH14OL5NYcJ9yDx38\ne2SlDhha7LTrrMsN5zZCG0t3WEOkgD4xkfoZkWc5ptW36E8qDRP+RTnqP387\nUVQ+YH8XU6yrvnKo9bvpXTmrZlFvQrlrIKTGrFJBQHQnLxXtbqAxHzhSONRo\n6JwVxeY9ifBc3aEIx7LJrjBjKqLfugwEacGx2V4BxkQPL/db0CVrdkIEn9R4\nIaQbOFCRcwofJ+v5AhzTfRafYrcd/J7Pknnfa8fs2+gqtl2sny49hz622pDN\nh8crhk5AMgFLekJvQ1WmkseaFUusth/2wKLYMUKXAwaMWZkeoJ1sxsQ6x1Rm\nx86FCCG2IVCWuCemD5JkRiT47qam7ITXJE6VEHGDI3P+DQdoQ5Ni/TO6Z4Cb\ns8KkqlEmD/6fx4c1n6mi0E1uFu3qt1sxXLd7iXH5J84De6wUUrwe2Hozzixo\ncV+fjiXCYEFl0nnI31AMLGH2OZChaG7qELynTlMa7fux9mzWedOUDx0STjKt\nYEWpnPuWzbMTBrI9bqdLeEjBIqxBtrT7Eb1MfUrHOP5LX5ExoUd+jOI525ad\njaCq\r\n=pxEE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "72d9ddbb5d34f26ae38dafc70b54e908d25ab976", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.0.4_1589619754905_0.45386272374711845", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.1.0": {"name": "open", "version": "7.1.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "68865f7d3cb238520fa1225a63cf28bcf8368a1c", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-l<PERSON>I5KgOwEYCDKXf4np7y1PBEkj7HYIyP2DY8mVDRnx0VIIu6bNrRB0R66TuO7Mack6EnTNLm4uvcl1UoklTpA==", "signatures": [{"sig": "MEUCIQDlBtdht6Z/hSWTLgTihRCu7gBFq20SbQvP2czOHZepZAIgHKB6if9BpsmiUH8ohKDLOtHnAJEUEip8mEM5qAULL1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFN+ECRA9TVsSAnZWagAAgkcP/15t+1ebJOfUub6diuHT\nv4H3zRxwlckcXu43scxDWpEYuunPFVPyZVdk4h/UeDBJF8I1hMLecZHVyto0\nB7MNKqr9/7+WHsNleUJPwbyUzEBxyvhg7+UOu401VDJidg8UH/Xjtas5Jovl\nbfMNdV4MQYaTl0G7Yl2zkpYZbYkUmcX3OOzmbm2AWQvlzeszdXxi13lZBP5q\nNtAOGTLfmGpPOSIOUvh2pZ8ZHdniFwHX/mmHHIQUi1fopvuB6UKvaOdmnwRF\nKGMIG/tP1aMgLYQ7T03iQ3hz0pVNCP+nZytpMjTGrq7gw75bYHgqYuqIWk3h\nif2j/UbPcd+dnuMNGvwpvSe9K3hqae41R2uvUgFnOu/Tn0lNuBGbzIhWB2Gc\nh2qwQon9fcZ63B3FwnygOJSVvOD93lBFl2l2759RBbvCTPeqYinA/1pAGWqQ\nzKenzu/NK6hp3mkaL1Sq9Ey9UAa7WeUUiNFydmMnhjeQUrkzITbDZB7ay0pF\nDjV9tTfXzx4dj4/cb2CUrpNAuTSWaTIovd3W0zy2odTbg5/13oFyoemc5SVt\nC8zrdysrKAyfdpwfqghGes9CV8lB6JPkiWpgJVFdIFHYnTnv1qfOBkzIvhsH\nzpEmhPSvcVLKhcWt6udPmYIl7u0Suqm6YT7Vjk41ERQK4VfoIXKT+p/MNrjx\n/yz0\r\n=Pgln\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ed14bbfb7350c3a54400d0bcbc4eefb6c8db2a7f", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.1.0_1595203459663_0.595130035286243", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.2.0": {"name": "open", "version": "7.2.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "212959bd7b0ce2e8e3676adc76e3cf2f0a2498b4", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-4HeyhxCvBTI5uBePsAdi55C5fmqnWZ2e2MlmvWi5KW5tdH5rxoiv/aMtbeVxKZc3eWkT1GymMnLG8XC4Rq4TDQ==", "signatures": [{"sig": "MEUCIBK1uqhMofosTsYiBfrNcnZhr9Wzqy9Uhx9yPbfvyosqAiEAuuYp1fBr9IDalWAUB8T+JfSbXhioiM4ibLiiyaL2qPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQEo4CRA9TVsSAnZWagAAfrwP/0pVfXLGjI9FiQ512jvj\nTUtuNeRbkgS4T08XDBK16ikVjcYq78/32vvP8jS6xQx0olzvzM6hbS9fGsq8\n3CFtjuBUPNrTzurklooolK3IjbC6VPzoguIN2a9xLZe9Fpdn3tKmr8CEV7hR\n1Zg+b6Df+GXVHGf+boqpL5kdoiDNV2lccqfoDz4lYTvIIzzJBZm7pd29U7EM\nTBZny/uUIrxqqHv+Mh2v57cqvecigKv2EEtD8ddanIOz2jjcoN0UvyBtiHBk\nbrkxFcZoO41rCiKbeg06a3lK7DI5HOHk4Y6LwDNmDsNyhpI8eTI8YCUnUyx/\no+HsWtFVtPvZE5ZK7A4ztCe+R8vxPmoXgFRVsUTxll9pH+aKK9g0hE9Cy9f0\nC8hX2DlOIekSrggWew4/igjVW2HZg0vrMN7LCQBEqSescBMC8VS3jfeP3oA6\ntjg46XMRxkLFL9UGHkGGOxcZ78YNwsJ/7wMGznrrfUV2+zJ3lvdXvs4s977P\nahQyuHSNzfOWKKYeVpsNRx/4mbqPqKXnaR8GP14c3Xzy4Fh63ql9bscmdwy2\nYXvUWLu7xH2wIQ9DABAmDgB869xDAr4/RcVHizfOoh6XmzgFTDYBthoz4IK1\n3jRoZ1ABjAa+nUrNxtVH36ApRUzSVIXRdFqusCjm/Kl7cFWghowSeEOuE288\npk2E\r\n=Tofw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "1022f420261ecca34eeff2ba7d02c41b4a708f51", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.2.0_1598048823414_0.7169592904019657", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.2.1": {"name": "open", "version": "7.2.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "07b0ade11a43f2a8ce718480bdf3d7563a095195", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.2.1.tgz", "fileCount": 6, "integrity": "sha512-xbYCJib4spUdmcs0g/2mK1nKo/jO2T7INClWd/beL7PFkXRWgr8B23ssDHX/USPn2M2IjDR5UdpYs6I67SnTSA==", "signatures": [{"sig": "MEYCIQDYOskF4WJak4/EcDU0uA+5nO8/TXHYyDa4a8T6MhkT7AIhAKSdjsEcDwV6kLXeyE4QeqQWxm9U4B+8IdFrT/c9ivLn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSOeRCRA9TVsSAnZWagAAGD0P+QExxePsjy5VMD0Aevxr\nrJ3ZOlduNdSRAKSmBRszz5WqrghH8hXSmK0br9uO/mmeddaYTgDS2cMiOVzX\nF1MjU9af/epY5W0tgfem4VqgtZs2uMDxIfRY8h3hXHtsISdX5C560gooMXgN\nwFcN13K6sGUypOmfQ97mJwGyTY5j1u/UgO2pAPG2oyo6vCf3iP6sHw3cPuZE\nJBmBXo15TIV3Cxq0Ji+z6pFKKnHIW1BMLqPKBV47M4RL85LrW3XOkctjnLU6\nEqSiE1G+PHeYmDDIqOyVpE5WDtD2lPI5I6MU5ZDkuXwxWyLewAbFyes0njEZ\ns6ewr8CFeYlU7zfCB/1qIci/3ckdL4con2Fcfs6C5adWTiSzb+iqNEziUB84\nRAqdl1FMKE+d54CzDAMkmFLIScOYvsnyOBw4Asgqdk9AOW+8Nx5Mos1Dw2+V\njQ8CRiqVmi+yDlCm1ZW4APBnzZXw87Mmi1wQZink2TYRK8rFlbyaEsqA7acK\nsLsd/7up2BXSYdVKykQWiWx2RdhVeTvXDMPlGbKT9e/bsaYQGM1Ygkskletx\nTAzVLAtWq7e+E8QLjDIVg8x6fXC1Jst5xPP4N9aK3Q4JsIqEOXsTNwQX3JEI\nKAcK0Zgq2iB+R2YptiFkNe2umB4/NaiwftZ5DM4R27TV0bbz91aHTMcttjoK\nKTih\r\n=m5ZT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "45e50caaae4af8f78588bdef017fce3ba818f8f6", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.2.1_1598613392939_0.5546220483764521", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.3.0": {"name": "open", "version": "7.3.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "45461fdee46444f3645b6e14eb3ca94b82e1be69", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.3.0.tgz", "fileCount": 6, "integrity": "sha512-mgLwQIx2F/ye9SmbrUkurZCnkoXyXyu9EbHtJZrICjVAJfyMArdHp3KkixGdZx1ZHFPNIwl0DDM1dFFqXbTLZw==", "signatures": [{"sig": "MEUCIQDsnWyeY2pypzHcHRP8S4boPkHXgqjCRu/asPfKLDDYBAIgKPCpAU9FRPGemcQFw6dT/XYhjUak9uIbcGqNtReYaSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcw+qCRA9TVsSAnZWagAA0xsP/RxBrpEZpbuzEHEihOvu\nxh2e3dkYoFz9yhQTZgepVCHvcK4GrZOH8zqu+FqtjITA36a660HbGaO8sfGE\ngrlxtlrqZmfD5AN4k9cpNEG4s1XyIYMmVEJNroIl0UgALVEplkz+VBk1jnFH\nlOv2/QcKJXwPqSsfZ/GH64APVVMDi94dFzNK3V0pSwkwMu2eGjvZLKDxxfdF\nTpNHPSEWHNl7iIucFshRpGWFkA0liZKz6ouQffTd8kbaKsht8F6qlNqIrpyA\niGm3jxDsGpl2rKsKT0bQfchK4TIP+NVbYbapHa9W0EVuzJRD+s41sUgHywJ6\n0wuAb5HLonS74GLT/6q+HFLcsYOo7VjPGk1JXZ1AlXWnRrlQrr5XndHMCP3b\nomnCv6JkhGS3dVYr4Ub1tMRbkqWqhzoFqCGyVAv2Bxe9EihjCxcguxBSwWIa\nDzXyeBEOtadIT/muLpSAtcgbEnrkyjNQ32q1JU+L2h35PEcssRTTNH03+ajd\naKqT3zQnar1ZESbj5wHmAldp2QP7Y7lUBJz/P5wFqGcx2+XbpZZsLmT1HhAp\n8uKUn6E3dwkKMDUvg1FqTEFtuzJeK1SddS9IQnlX1vAvGLwl8G4ofiK848SQ\nOxmO6aJKPoXsDn0BgkhdaJYPcy1khmhEsV6PRRq64589+7AZ/ihOqrGUU+qM\nHJJ0\r\n=nViP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f3748e4f5a04a41e27034aff1eb08e8b86d01c1c", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.11.0", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.3.0_1601376169678_0.11620673383692104", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.3.1": {"name": "open", "version": "7.3.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "111119cb919ca1acd988f49685c4fdd0f4755356", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.3.1.tgz", "fileCount": 6, "integrity": "sha512-f2wt9DCBKKjlFbjzGb8MOAW8LH8F0mrs1zc7KTjAJ9PZNQbfenzWbNP1VZJvw6ICMG9r14Ah6yfwPn7T7i646A==", "signatures": [{"sig": "MEUCIG3OOPZJe8DIeOa8Vt1torXDq9HgqQtBI0G/EwLf8qAWAiEAgcZOjKNn4BXDkK27rk3Fg1xXjhohiwxYEBbSnhgkGLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9roOCRA9TVsSAnZWagAAVpkP/2RvU+V4OCrEeRo+L9OP\n++DAuae/MEdkEFYV+RMZwkWfayeVOWmDqjF/0yhn7BTeQddnJ/V9KezXzpSa\nhrSZ9NCIV9wpCX3pD08DrqVJ7cYv3cbUcr0130MrdyGfrpD9fLEHtI98/x+H\n2C39TJX0c/kKNHjUuJ9P7gtOP+6vjAqN9rcyXzZMOt19kTrxlN+zNAH6fX/z\nZTikfDVMkWT+x/9bi4iGEHlGUwrTABivwF/sNBn5DjRFp36rW1fW1yAipkVE\nsDPW2x6cNe/9m/VfzKVV1z5L3s4+rnubNfZJUIxuQA+nZ2rVHK3g4mHPmTRw\nRt9z0xdP6R7V938lcWtkmX/UECR401EUI5mUqXrvFXsCxhu00W8KWJ2lT+gp\nC07crVg/vIkh/7TibXm5j/tn62i4wavehUuBcRjjsqjXh/8psB4TeJ586dSf\nD0ecTyGQrgoIXI6JI4zicu/KDgUGvgMK9Ookok0ro/x93jjIAeCGA3CM+EM5\nblvWKR2oz0vVGjQFD4v3yQgkGBl7uFy4+yHkcIvx6llKZacdj5LGnDI6aoFg\n++pwT3dHWlTrzBJrQq/57dKrHEYxR20exNJYPKEnIYFB+4FK19+b9mEPP1wx\n8/phnmm59vOUjY0hphSXWPcOyf6K//ebKcOmyQjqDB3hJKc6r9S9PVcD/M8F\n5Sw2\r\n=SKrn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "3182c380bd16bc70c77537af7a86bbe6d3f38a90", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.3.1_1610005006370_0.9763334441490346", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.4.0": {"name": "open", "version": "7.4.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "ad95b98f871d9acb0ec8fecc557082cc9986626b", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.4.0.tgz", "fileCount": 6, "integrity": "sha512-PGoBCX/lclIWlpS/R2PQuIR4NJoXh6X5AwVzE7WXnWRGvHg7+4TBCgsujUgiPpm0K1y4qvQeWnCWVTpTKZBtvA==", "signatures": [{"sig": "MEYCIQDpG8b/D7sfd7t8oYXP7KqOACQCrRB/oPuXs0OZ7Iuu0wIhAJbcdLlTVgi7nbzCV0fmfj082aTUUtp9TXtAS7bzr/NF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgF9E2CRA9TVsSAnZWagAA7xgP+QBFNpH/P/IV3J5DmVTc\nmEV0d9nyW7+dYnqNFebxDP4Szu0eygHtlqpR/uehqzfrOPG75LJT46ReWY1A\nXKfNOeyLbnKVEaPmnRcC4AuhxrAIJVMQ+qYXO+JPtGV2J0+F2+ECrmto5fMc\noZPvUMemCyCdaWXVPSJffddYQer6RrwhSxFb2v0LhsoZVRky77siChciVX0N\ne0/99tb6STw9n0Urf3K4NYQA6FC+UzBD6PI3Uep0IOALUdmyZQBQXRUKQXhS\nUeWBDMof4SPQRP4SuHMRJYy6na6FiYbwnx2kKkxt+i8kNSaxX1BCpt+aAvHG\ntykrLQFkaTs/8E4B3mHv1/80KV0fZd4/sYOylJ6755QVPnyPXuTXrTS1aota\nArJmmZPr0S3zlOSlZIay4IDE1SFMR0RKX6ef2JVBGInDj+xm+LGbRQtSPlBZ\nN6cMNj6ZPbfil+X8RU/FCyp5QNSyQxRQB575R/Dp/N/JwGaLqZsDVIb/Oibe\n2CBVJ1UVyZfh3oSoJYMgCcovsWC6tsI3Q3/b8kSykCF+oJ3Dt4Cu4vK1ng82\npEyph7yA9zCshb1fRpmPE2KZ04fy2fBKfMW/sI661DH774xkDWV+2qlsVAm2\nSMe3ZETU06wwUkayEX28X8VYkSpDWJa6j/lTB98Rhnq1GdASSSqzNLceJlKo\nvJSU\r\n=jOqT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "5ce319c8482a2c6425e0514e0da1630dae85a986", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.4.0_1612173622029_0.07790807933990274", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.4.1": {"name": "open", "version": "7.4.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "4ccedc11ca348d398378ffb39c71357df55fe6f7", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.4.1.tgz", "fileCount": 6, "integrity": "sha512-Pxv+fKRsd/Ozflgn2Gjev1HZveJJeKR6hKKmdaImJMuEZ6htAvCTbcMABJo+qevlAelTLCrEK3YTKZ9fVTcSPw==", "signatures": [{"sig": "MEUCIQD7K3SIwxQ2bbKRBE8WtxvOldI1dUYiteTPbGPuZpPnmgIgHdjENEmD4eVZJcofGy0heSUKKT4z8GYodsy0lRmVEvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKNSPCRA9TVsSAnZWagAA6/YP/jtvLfTR9hksWT3p6th9\nOQzSkzABk/3mdnG0fDtXQG6ZBlMBbZJ+sROPXVHTe0HiCErd/afgc9Xa5zSj\nNZdnM4bojOrptwxwu7GcAXsiR5mKLM9pXMRJt1qxNoAEyHB3lC86vXI3ELmy\nAq797GcR0qrSTQVCLM8UJzJPtNTM33z0TnfWYZ89w0WyEP9I24c7Td/Ynziw\nNgSOHkwbhx5miprLYKlNFcjdswi08QzRYIK7d1pFakv/NbxsBRCXPp+R9VeT\nxGt/VGDXKtU9YH+qe6h+6w2LjfU9qeGbLVDQqEnVeuSC75ouQXcWVroM86ua\neG8ZXjneBpX5RN59F5X/FSGS3fNdOq9vSIOfyMPebnJesfAMvzpLO+Q8rP9O\n24lWdMRD++hkbUtn8Ik8ilnUwBV/EBOeyv4psIRrbCnuf5kSDwokZOFYpJv7\nOFtfwFiuKEhXvqAhE2MHfmiPJbxWF5dE5jgVsElcVQ3fw1LfWcq5bIiYs7Aa\n5VOUPKeLq+lpDEd+PtYtN9ZXj83dHOwTvO8ieAHswXTyE59K/N0j2hBahUc5\n8LWUopr25qG2sd7uSNHwsQCAO7/v7P3Uls4zRASXTQR63d6d/2lcF4Ajlcka\nclEkqJoGgmAy1TtPAEGPCOJ1SeSUelB9bRdZyVUZ0DlSV4Ir6u6GaeI+E8Vz\npjNT\r\n=zGHW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f0533c0219976bf4cbe80afa4b9a1012ed1773bd", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.4.1_1613288590310_0.18087497580428202", "host": "s3://npm-registry-packages"}, "contributors": []}, "7.4.2": {"name": "open", "version": "7.4.2", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@7.4.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "b8147e26dcf3e426316c730089fd71edd29c2321", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-7.4.2.tgz", "fileCount": 6, "integrity": "sha512-MVHddDVweXZF3awtlAS+6pgKLlm/JgxZ90+/NBurBoQctVOOB/zDdVjcyPzQ+0laDGbsWgrRkflI65sQeOgT9Q==", "signatures": [{"sig": "MEQCICN3AgcN3iu5YEaCdAbPFrcKrITPx7sic69ztNHCgwJPAiA8FXO/Y84JUeSJCsfCPXxieO5pYbr81M4FMCKfQ+4HYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgK+5+CRA9TVsSAnZWagAA3S4QAIyM2FVD31/RRDtVEuX4\nByb2kUtBJjfO+9tt+jofX9URBLQwutwl2xKMhv8VdkA//1OtOaVlwdZDgE1x\nTEWYf6evBj/Q4Vde7nAsGd1UcpHZfjPKFpBjMAmRFQeS4yaLbD7g2/DQx959\nWYMLvu/G3lJBiTW9fd40DL7n3ejWErIS8HyAckTV82K78b8J2YMIeEn87SdU\naPa/0UIEZMRZnkt+GD9N0yw2OrR5ULfvYsKhjxA2KDxOwcdfrB3+qXKwj906\nAxjYPBgceEn6u6RsQk2aYJEzpoKhe962qRVBov5p6G2EhljUYKyWUknhjjh1\nG4Oc1mewGv1MwQq3hDRrk8VeSkLKZxoUte4bXM4EiKmC+OIZlf8Vretw1SET\nvhm2/DC6T9vFZjR8TmQuETwUH/Yx4w+CiMDS/D7Xx4sOHIuxNqreAm8MlWMl\nopWUc3P3NyvQzwwIpLcGrGkRYx7ZcAmceonHUiChXEIkMgeOLt4M6zI7lkoj\nLTrRKql8OkZPqaZQ+Cg0lHORp2aYgzxvfkZoCEy8g1lrPpSRL2Yl+Ho5US9o\n31KpTr0Nbqn98hKTWSAB/VpyEGALSjsvA2edkU5v3W5KbCTqQ/SLpUjEmSXW\nu5xSQW3LOvoTAm+PI0a7lHyKVMzIbyQpiZqFzz6Lp04S5uqbTD5zI/phbmf2\nLD8Q\r\n=UgqN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a9babe05b371ca414ad1c4fe3ffe83bbf40b1c4a", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-wsl": "^2.1.1", "is-docker": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.3.0", "tsd": "^0.11.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_7.4.2_1613491837742_0.4274411654639172", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.0": {"name": "open", "version": "8.0.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "ac5ef9ac7d2f937b29947f6e831393a59f40a723", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.0.tgz", "fileCount": 6, "integrity": "sha512-Q+ucoe0HGwPFrzTDFuhCLG/Cqp9CtdOsKhLZxV8rujKkOqO6wyP+dM08bQVZPQFONVUxa6NLKaciEsY3JgyzBQ==", "signatures": [{"sig": "MEUCIHfy3Xpo6o70B4Q4mYBVxWkmkBtwH9wWUMHxCZpYqCm7AiEAggevIi+KPfrY7L14wwqms1pxY1v+n717JPwqk+EsnpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPhhACRA9TVsSAnZWagAAfcUP/3V5PrXqUx1fWkSb0UEO\n1zR6yHdfL+Tfl4Feubadd8jhPRmj6J1pxHcfgpafHv+sQZqiNO+8OAneedDV\nz+LF8JRyWfSUV7vAhqZlK4Aaa7SnDt8nJUk8vptaHcgayJujREgt3EBl26Gk\n+jRPh25TBYrgXvuvKKxLzCrFKwxtB/zp+LOE/pCoKvvy6/ykREPALtjcFyDF\nzlk60TI05BWgLMxSl2eFrN/36EHd/PmQPSOB+n5T5ZRqiixcXJzZe4mCwmKk\niiGTEzI1cxJTLqnQDDBMJQox7Aq+kFF3l6EDLak4bjmWP1PCeslcKQKB7x67\nSsPV5j9tnPILN7HyXgow9BZOp3iukNz09xuBgVVk84e0utmX4akBVg+5dYqL\nrW+BVvi+fz/oqWn6tNKaLJsGwHCkgFhL0EIKwfWhxrfGAJ+SmGE40ApOoM5w\nbs8LqjmNCbH4TJdC5mnZ+OWgzl5uA1wf8pzZsA6kaYYxcMTyfAVTSnopjHAp\nuFRHV2wDSgy5tEpiq1jpV18qSJD99BiR55PrONrEZDvj0WJwP8LWYkwWyNTg\ntQ4ilJeALixFae2un7aKcw1uwMtqXH95vo+MejjvX9AvAHlbvv1FEvHTV991\nlhsJIrSiz60xc+Z9fLijVZR6oZutkGgykHBgxxj4GtsMxuGUPDmItzfoqv0q\nwgXd\r\n=ZUvV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "48ea7036bf3768a1191bf15e80bfe7ed15f6ea25", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.0_1614682176020_0.3850304090701093", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.1": {"name": "open", "version": "8.0.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "81f88a8d57b8ce0ccdb4880dfaaee6481c8c146c", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.1.tgz", "fileCount": 6, "integrity": "sha512-VfWbDBAay2Zbw2QrMMIxuo4H0SUe+TdHHT8qLkbBRF+TxdEhnbDxf7jWMV5Fbk5U4HX3abq2lvYH5v/xoo6CNg==", "signatures": [{"sig": "MEUCIQCCJPyxM/lfsGD3xB5O/zsQKWuUML4XzyJ4J5NRWwmitQIgfpbBvjxHv3bSGa4Br31KIjC8gs7pPB+COe8oET6zmsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPkZBCRA9TVsSAnZWagAAdVoQAIserBsOWG92gvcgJavt\n9DVGCBYSkzVYAf+ATalj3w4Vn23kvhVLdcyE6XQY9s+41d+9spvRy4mjG0Yq\nmgEiZTOqJeoi12UfUqSoyX3I+GFAudiT50S1ZfgGlHx/S+0ALXlA3nBizbpy\ntaddCDJazvpxVel/O2Xl1uz6qRq9yn/jN9nCaEPTn7HMDS2zhgYPEQvWfm0g\n3UQljmZBdqIHLL7gpvmT2Krr+mFOORDWhHbunWWYU4wTPhPMrjRj6ZeB9YN8\nPpM9l8G7P+in+M4YAXrsHYI9vi7bWWImyIAH1U3Cbp/5BhW1erozCR1UZOEs\n5Q7V/t6eBC3XnwzhpKljx86E6DafJs1945AM/Upp2geDtrlS9LToL81jrebi\n8PwLsIB6aVPLcLWoiO5fVZuTy6KKfp+3UPKsI/K/6ZrViXOUHW0+Db/T+ap2\nFJrg8r2ljDAz7NVfqFFOmuX9mVo3QY3QmmNxQmuqXEYxghiuS+5cX7KcZs/T\nvIwdq3W19FJ1vzAMHO2QG0OtpdMl2nHhHqmnXIfpRNG1pasvUY70dZ9xWB6i\nG3KZddzpAQSz4tCecuViKXv5mo7CfCcAhMsIPXmkXQ/i4/EelvHlh6UGbdxI\nojGqZaP89uq39znqmgHExwFy9Gpa7VJsA+nrBg0UB6WN/guHONStOlz/rqXt\nm3Re\r\n=EK94\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "180c2ab6c570e8b40ec2a972ecf8691334ff5507", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.1_1614693952768_0.4588848365059226", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.2": {"name": "open", "version": "8.0.2", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "8c3e95cce93ba2fc8d99968ee8bfefecdb50b84f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.2.tgz", "fileCount": 6, "integrity": "sha512-NV5QmWJrTaNBLHABJyrb+nd5dXI5zfea/suWawBhkHzAbVhLLiJdrqMgxMypGK9Eznp2Ltoh7SAVkQ3XAucX7Q==", "signatures": [{"sig": "MEUCIQDhaI216iysWjPVJ1gHC8f1in2poRKDO54R30cOQh2kAAIgcpWuQsc0GacYJjYCsqJEW8vmsejei/b/CiHdsCXNqxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP0TaCRA9TVsSAnZWagAAfk4P/3a/AhDEG25+nb+BmsSN\nB0bt9EkyCyvqGyza71UYJCQLV5PTIiVTfRDWu1VSuupHSqPX9GA1EAZR2DOz\nyL4Y98+KenMI9UNMf1Trw3SyEGKQicAmyf/Q5AvUFGPYnEfKpQzJ32B5qCSF\nJYIBGf8xE61g9U7qBhRVrqg/ulqdYMVroDMIZ8/JYaj+94SKeOeziJ/6MaVw\ng15KqXdBcDXyE8SzkQu6WBCoBBkj43fSXm5BZpVYY2QdjUSwkLd0Zd9B54ks\npwsC77PfMc80kRUvivZF3OaBvxL+PAwrinlOaYF1JEIdoDo9X2has69d54Lm\nd9+f7HVwpfddQn+KucNvL04gm9B8ixDq8Fzs8+eCq3dK8BkREh/kV/NdQ0Nf\nou5mAr81/vDx6Dsq5qVDAYG/YvSoPAv/l225oqE2F9XUSmDDt/4rPNsvXXhT\nffDGhd+FVkUDTrXVWkn49ApMIZo3kr5er5IJylqBW3IFhUFniMLhyxCjTs1P\nHH2qjDgMr4AdjSv0jPyrimPEtCdFLoTmB9oxQsRw2W97s+NTULupmKKNMnku\nsAYMwvsm2O2gQRZiiTAtRjPNU99+0wIZ21+4eYBKxyd5uPxet7kUE3YAwjeb\n/Km1KV9PepIVP0jnVcLFwVrLcHhcVllg5MHcXnC72pRo4HInD6P4852Hgq9w\nwYh2\r\n=VGsj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b7a5729ff0c8b4077a3a42899ee4bcd214fa1490", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.2_1614759129876_0.1941393805750813", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.3": {"name": "open", "version": "8.0.3", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "04f4406c950666c35041aad8a621700022116afd", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.3.tgz", "fileCount": 6, "integrity": "sha512-7nsHNw3rOIPTwhF5iYkgE+LVM/oUHWC3cgrWNxPqa+W+Wl5Ekvo32qayB5PYX8zNjXzUkrTaJsWpaGmuw8Aspg==", "signatures": [{"sig": "MEYCIQCsKeEO0T0peWcrB6taGT3pguJ0VeNQbVBZuWL8dqD4+AIhALyj0h9vLSi0paHBpUubxXnmvcDeLJ4zy435fOttHiii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU0TfCRA9TVsSAnZWagAA1tAP+wYqazn0WjBrY5sdaZGt\nNu4FxstEaKH/54BKjhCZYcU3K1nFKNih9uid5i/oC0gEynuyD5fKSLyuZU8X\nz9B19rcYTKJ/snZXq1IH69W+Ys9JoRv4T7Mb/ocfxPnFzo/tDUZ0KBYW9wGP\n3WKYpeMt8pDjqf7oaoY0ioLWG1XotAEAHMU6dXZFmiClq7Gf9HmMTYMj0YRt\nOmnCkrysP9fh1VXPT+ecp4ukVznNwZM5n7xhhYHXWzHTA6rZMlF+mzwDax66\nZO/G9bIlZQEkUskTYkpLUbLDMq2YDI6HthzodWgUSR5raMcqdA5LNbewLwsz\nASlEJPjyIscS4BKxzyBrNjhqgsMN82AFndfzEz1x3XvYCBO3F9d1wX0GQOiZ\nyAj1vtnVMVroiuzaqMG00ntnZ8WqdSCFUqvLnzzQXwQQfbZutOEsGP37hX31\ns9JXj0eSj3t+tHUBfDW9y678VJz1VNrrM2K3TPH55B+tnGM5Sj1LUKNKq6kG\nS4slFUo/LozjEPOQJ1suUkdvhDN00FHGYK0uhqrznNB/qrr5LxhUrWtmqC1L\ndqJcUytxzCFa3ZvaXHnFEuAVsM9+hz2/yjUmz9fLuPYTmAO0tWqtXPAyGVha\nvuHtwHlXY6XC9eTTlJ1K0AqJX5EL4KhT5h5ADDaX6L79mb4T+0LQX5RSN3B2\nutbT\r\n=kZS6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "14daa34927403dddc003b2f456bf4c8dcbb90f77", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.3_1616069855090_0.6850987391391479", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.4": {"name": "open", "version": "8.0.4", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.4", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "2fb90debffcf20f4d7be537502ed3e3ee9e5dcbc", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.4.tgz", "fileCount": 6, "integrity": "sha512-Txc9FOcvjrr5Kv+Zb3w89uKMKiP7wH8mLdYj1xJa+YnhhntEYhbB6cQHjS4O6P+jFwMEzEQVVcpfnu9WkKNuLQ==", "signatures": [{"sig": "MEQCIBgqN6dV/4iSxBP/IrD/f7L1+EN2gUx6zwkJGSFCO+DhAiBziPEFyNXcL2nPUkbPZrfO9V9FPpP7D3uLUD00NuzsXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV3P2CRA9TVsSAnZWagAAAt4P/jof+EMme09nr0aiI87x\nyOY0EKBoXf2RcTpky8I3KItc3Eiqfm4w2RZkty7IM5mxWOAfEaqQWT1JBel2\n0R/S5ziuapBI4WuqfHbhAKU3EOrp+fivY96eVV1XsjSokJqfc7ir0kgn3Bbe\naOH4/tslQouzZulee4eJ+k/IzxIidYsn2IxygVjT6ug/CXQKaUJ3JVKLitRY\ndU6EGuGPJzV7Djz0LwtRIED+Sl3Fp1NqAMaqT0UVyfFAPXZJkWKgOIiYjf89\nUww2ToM5sonTW6AyeRKxuEW3Xia4MB29Iwgw8wPAfZIHZSduyxE53rCRI8pC\ncfVem9B0VRc1vfme6BhjmJ0bBdcM3i1OzqHTzEv9/USbswSnpvHDGdteZI+G\nWRic3sT+NLnTf7J1eJ3B7IZgWNRYkbJA23cJ46F4QCmU8MRPqJKN67+oTDp8\n9a+Ndt1RLxVoMB1s923S17PxxUklt2xTDmFsbxM5Z7EXnDvcppd653/Lyvk+\nEJ93JAwqEQhEyVIqwHJvFxf+61wQF0LWOsrfiGXAZ9uXOYMLT9vey6n2+l/P\ngf/eEcRDhBdkYsuFHf1cpfAoJ8Z9TTC5vd5n/hMzxmt3PDlyeFeL8w4s9Sjy\n6J8pNOWjg1jF4DrITFI0Iov21pGEbVE9kx25Y8jj/sj1W7i5dv+ehnw7E3Jv\nO/0A\r\n=9NV2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "8881dab330e44cd0d62ed4fbb17f0c12bb17699e", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.4_1616344053934_0.8925485503249042", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.5": {"name": "open", "version": "8.0.5", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.5", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "92ee3faafef4ddbe78006f7881572f3e81430b8f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.5.tgz", "fileCount": 6, "integrity": "sha512-hkPXCz7gijWp2GoWqsQ4O/5p7F6d5pIQ/+9NyeWG1nABJ4zvLi9kJRv1a44kVf5p13wK0WMoiRA+Xey68yOytA==", "signatures": [{"sig": "MEUCIQCkN2OsYpVjs4+qgAPd6L+4poO21JlFjfRFsJg85w/L7AIgd3S77ww6pkwH5XQwM8ku7q2uecTqTp0xm8oHw3Sh0/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgaBUwCRA9TVsSAnZWagAA41QP/0fv/45SaXAhTh2GH5vD\nKT6is3eVvxRROGji/CYTM1JcU9dhq5HI0M8MsVr2FBmwAEDWhxeCrAs5cMXJ\nOm2MGqGRVhtrOdMJUCmvWEPg1rtZg7qMVXSbkDAdaee35Ejuqvz+6fdU0Z6u\nYDPJC0wjrE4RBfPYCYh+L54PW/aBUUd/yueYjQzytJ5VPdSMw/FJYQtX7F+q\nDkx6CYygEZwws9S7KGSOsRSijfOvDalafI4y3MquYayV2IL2NEknFUqYGCvk\nqrsF8A7byYwniY1eZWBDvBohcMnyCp4ScHrCfuVBWhGiJs9WHT0Jc7/nga+y\nqZ/kCuQa9d5T454hd4K7Y5bd/tjYez9Fbw8WH7orFG0TRJc8jKHUnui9x6k1\nKvwOfmA2mhyroYnttNoH793MUYBeaHjNj3XE7rfrkuQ6lGwy4dSuaQDI3bnD\n5tihzIoBD/KGonmUVUPTMztsGKdAJXSRl0mpko5mAWke/AWm8guXJD245t7e\nl5Q408xfgWpmJ9pPXbtJtnmO5QuW0YkQhhP9U8bU7VV1vc+Gz48atWFsRsal\nytyOOgCgH6oJD6nL6NpMKSKqSgoLvzdIFpiAiaNT9RwqQvwKjvSfbI9TzmU5\nRDbEWRTSj7Hdce1AQlP9iOG0q0iynBHCMS+DGJQ4vPIcPjfurn1BrQUJJjTi\nC/QD\r\n=fxc1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "45a3551a3264763b3df14dd975b6577254025aed", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.5_1617433903535_0.20095509490091734", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.6": {"name": "open", "version": "8.0.6", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.6", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "bdf94a80b4ef5685d8c7b58fb0fbbe5729b37204", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.6.tgz", "fileCount": 6, "integrity": "sha512-vDOC0KwGabMPFtIpCO2QOnQeOz0N2rEkbuCuxICwLMUCrpv+A7NHrrzJ2dQReJmVluHhO4pYRh/Pn6s8t7Op6Q==", "signatures": [{"sig": "MEUCIQDdpwFwRf71YJesnvQCmJRvBLqHZoJVxjDQwhZLv3eJJgIgUVrGW00OjQ0AyDIAWsDhRtOK5uCxNpiml88HidCozNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeRnVCRA9TVsSAnZWagAACksP/jOzwsod+2CFmibutCKM\nVg8hDN60/IjDNVhKnFgxoprTjG1DOb31U90ATUP6zt0YMS6VPXI0jQs2O8xB\n9QC1BrA6VD8dC+XVfwKHFl0jh5Ac7PoiflB7EQrF/ZCAiYOtxL6XJSauEg3l\nwMc2qYPMhU931BidYcVf+Zs3pZ2RzkWGvnITeVUyiPFwOX8ErOdNEOI1rWPM\nXmGm4EDkb0q96mXeyTKNAR1tE0oyg08O96xhrgD5Iu80TL0LrSvibdUc9e6g\nPHHBsvFDviWIZZP99yyLJr7Zz5ljOKPQDxF9tEFNtEBGArjpzd1GkMRHTlrA\nyrUzOfO9WVI6qsdTDxoi2NWT7gRekDr4krhdmfTGa00Ngd1aXKn5Rpa8kY14\nun+nl3mRL1zwq09/prjHUrqt0GZpjIoFaNiz5/+vBiJUnA1IyRdISlH80k3S\njnHkStzVLi4SZ/YjJv42qGjCMWe/fHRLzzfujxVHTVpUzjKaUwi9WDJwuNEk\nrPavIQ7QJHW8zklHUIwJ42l14HgECkIUgka9fmwLzVwMhqTGSRG7twwoafaM\nqhQ8CMfEaJW8GxWmxIO98htfq16ZVZK8bPtoz1b1I+0WKRw9YBVIM955octc\n0i384qZwjPB6u23ZzpAI5KJRP5KC561eRDCPHfHQP7g9AM8QnTH70vNcpxV3\nvlsw\r\n=EzH1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "01887ed979ded27a8c6f641485fea8c57e2d0972", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^14.14.27"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.6_1618549204902_0.3225928422898683", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.7": {"name": "open", "version": "8.0.7", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.7", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "5597eeff14b440f6ff78fb7ced9ede9f69b2122d", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.7.tgz", "fileCount": 6, "integrity": "sha512-qoyG0kpdaWVoL5MiwTRQWujSdivwBOgfLadVEdpsZNHOK1+kBvmVtLYdgWr8G4cgBpG9zaxezn6jz6PPdQW5xg==", "signatures": [{"sig": "MEUCIDkkmkzHd4mBruJQ01ptdNr4X+21qs4KEipNfNOMEVBjAiEA/yc7EgwCcTWemECebn08ps+8JZDL+t7Q0VpDs12jWs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiBrwCRA9TVsSAnZWagAATZUP/2VDhnGTKRR9rZNerIvB\nKCH7qjbGoI7ZabEVN1HJiDvNEvz53/waNK+j2Mf2SaBpniLRltc5R2P0hmZH\nlNSML5K+2AuFMnywcqCE1fgzyTq4o3JZ5Uu5bwuy9XopMxIouzy92aPOm8hf\nLyCO/5MxZAbdH+6459Z8RK7/gfIwRyi0pfVF9BFWBRurUIz9aguyD4kOOjx6\nPhAQSKjDcF+sdHCxBs0E7cMVwEOEq7wffHCtF/KEqSc5L7LOf3HsZ0GA+oeU\nuJUZ0fgTHIivaQ65VpO0vUaPrDTCO1q90ppc8uvXxRXvBsv9Oxe4p3F/BH4F\nC8Bj8c0KIudmiRQi2y2tjOlVqbstLebe7Vrwa0HfpJ8cjRPPukdt9/rNP11O\nragQSXlGNDF0ZU8L2kT4k7JTGX3bVkuDR2e4cEoxh+1fJHDEty5Sb0P/OR5a\nX3SVD7WEmv0LbKItXnZSYo8PvoPgk1xhFr3RxCh+1VhysD7V5APBTKHidUgE\nK682Tv+DAA4Ws6aKXIx+FyMRdH7MqnBIr9qU9J2Yz9vASrB/mDN7VpC47XwT\n44fXO1EAmzDK+MFuajPD8MsJ1izgxOGlARgMBw6DvAOS7a8GA8juIdZfdial\nU1aBFYJ7r//oU7uAqeZf9Haum50ngPAcO+xSRoLEB80EUNpXDtgjspQy7l5L\n4p5c\r\n=4Qll\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ae885adf8915c2bf16cd08c1752eb8d51b5ac863", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.7_1619532527929_0.23049240705241614", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.8": {"name": "open", "version": "8.0.8", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.8", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "0e286bb2df3c72e00cb2a0203d604abee002dbdc", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.8.tgz", "fileCount": 6, "integrity": "sha512-3XmKIU8+H/TVr8wB8C4vj0z748+yBydSvtpzZVS6vQ1dKNHB6AiPbhaoG+89zb80717GPk9y/7OvK0R6FXkNmQ==", "signatures": [{"sig": "MEYCIQChETCe+XdYLpQcinfDBtuOYQGzzAhnFeryJnp8Cvy1GwIhAIQf+YNZkvHY7IEDms04nXNOHvArc3IFFdU7EStMjdGs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglWW5CRA9TVsSAnZWagAA5YkP/02OBM3nQ8k8RoDlR/c/\nT5GqiN8IZ+zjpOpMHWu9/rYco6QwbL5H7YSZ9cYAjPFNkO5hhqwqMFQdMdcr\nO5SkqL/VnQE9WPxQXjjfi01Gqoa7pYB7ftf5bWS9akuhLIo1b6n2CHY8zUXD\n1X+eapGIPc0L11Nu+acQgCrgIZI+KQmuMP1tqhQ/CZ2cHmB3W6RcxHtypwrw\nPGarKLZEitNHwf7WLkav0Jq54c3Sb/U8IwLykZmPShPAu0znk2wL+8+BOD20\nZtdMKUNjAfcKeyn6/x+Wx0RdmL9rTy8hIRLplK3GI1wdV+4uhI3gfJltPqjY\nxnKH3BMrK+Q2AWMOp+mzTTw05HtO3LJtVbXzfZohf2JCeNTCwsoMi8QhnFno\nhclpcC5BGfJ/3loSwn1bkHuhxgialT3Y1FtzZXScNPZHPhgTZHzzQhfGGusO\n2dAuyv6xuzuegkUIRyM3dd3w4J7bb9Cn7+vzGUnFCl2ihqwxntnWQ2kMFZ1A\n/OAg+LxQAxoRTqiQBPJn9QoWph6Q5LOMP6j+mzE5okMAkENexkwrzL8n3u2L\nhqely6t8Yx8iyZHjASAHh071IvXzNHtiXN50AvEp14PfwSFriOlLroOp4QR6\n02B9sE4sva0flVxHAEPAqsfIIEASdX6zjW/bbF5BMaLzDfSUmw/4L5KTqtEF\nBSEU\r\n=LPcl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "8ba52b4dc6a2b74077e781866dd8bd31de1a8772", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.8_1620403640938_0.45290670186628157", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.0.9": {"name": "open", "version": "8.0.9", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.0.9", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "a7a739fed91dfa3734094255badbeabd71116a12", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.0.9.tgz", "fileCount": 6, "integrity": "sha512-vbCrqMav3K8mCCy8NdK4teUky0tpDrBbuiDLduCdVhc5oA9toJMip9rBkuwdwSI9E7NOkz4VkLWPi8DD2MP1gQ==", "signatures": [{"sig": "MEUCIQD/LzEt2mFs29mQAqklhCe/miSw024S6Jz94gQTLd2rNwIgC2Oxtomb1ztave8s+/fmPe99AHnI3YDIZUMHHxbdb64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgogusCRA9TVsSAnZWagAA9HYP+gLbhhSYidoBKcDYK6jb\nbKGRx4CTrwYIhfgB0IQMhm2icit0YmJc67JRvqT5wm3ifMtQbujTNI3GSouN\nYwhLcHY9lmpqTFLoAvz5G990Cc8Y+WNabheAyCJRpNMTZPu197HGjyWMmlxk\n35TbEdqBJi/v1wH4ujp5qwgbbxBDo2Hjcy4S7LQHyVSKrZ6laM3r1sTbpDT8\njcC1ideQAyFviUFzopK9gM898kEnTZrm2WQrOvDZXamQB64XLbIkMznj5tJO\nOUyNRPPPGhPKS+4QW2OqTkCRfJVPtMFYlCaNVd/TLXVpEHJ4SvFjyZT4du5T\n/rceSqaL4ozxkxh44ruyBT1DpJ3QSD+ICbE6p3S67H4X8T5m/s7wWZc7rZNi\nW0ZFwnol3TKfYCkcbB//2HuTXvM2l2JTd4aKM80WYED1PUNG7crWPO1aOJXU\n5y8/YM13M+S1GIR4sW4hmmvyNBNc8UMNGxv7/7PxIcO5xzjzkrNuG969Y/50\ncNx+2Wb24e3M1l9Ng+oigtU4fh/dHykJxnOQzkb2s834MsFsrqv2QceYZsKW\nNvaQJfzZoW/JNi8iyT+4RF962056FeV0N/8f2epQFxv3BRMJYeo7M9E9Fx8g\neMQsTITtzyaVZKCfpEx1u1rGn86TFHwvhzljmvFQ6CEliQ/827PU7uScagGz\n3lh1\r\n=ELWx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "18b9665afbdd5d49eafb1c0ef4e2444b0b8624a1", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.0.9_1621232556275_0.7737760785798702", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.1.0": {"name": "open", "version": "8.1.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "d77e9d8edab7672d03633674eb182e1c376ee25f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.1.0.tgz", "fileCount": 6, "integrity": "sha512-jB5hAtsDOhCy/FNQJwQJOrGlxLUat482Yr14rbA5l2Zb1eOeoS+ccQPO036C1+z9VDBTmOZqzh1tBbI4myzIYw==", "signatures": [{"sig": "MEYCIQDUSN2OE/N/p9J74pnapfk4sBdiSaYihp3J72hWVg/sGwIhAMoIGi55ITzhbqmx1MqUdqhN3ovNRqYVGxlPHPcMpskS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqMOZCRA9TVsSAnZWagAAPRQP/A1n2b3qTrq7G8F1TOIG\nlwk1ghe5YKWfQ8mxp40T7pYC8IgJIbwwFOZ+TQPW+FDDA/OocfzwsQltCpsB\nydhrIN0Ge2xWR8Ypu/XRnMKLjNN+3kNkQPulo+b+oe4HXWipPZsYI7I5YTvt\nqGu13LDWJ8U3VRGd3od9keIGIIXER/B9BUAro1exCNgOq9BbnByHa8CiFX+H\ny5jPnTCZbvz+z6HT4kUFwI8TZPnKXS94rL+c2PqDGXR0D8RirDc6Phgb8twb\nDZVcvhV04ofpa/wdATBTDhFUzg9BiBzJe5uL/QaGGHWImjyS4zTH8xvZsxZS\nCx7cUBHyIPVNIcXuosCqNPZcBzdLYTvXt5qRqj0dgO2NLCqzM3XzlJ1avD+l\nqzUqWIlRIHUi8OYC3c9CoIeCD03jTel/JwPlzZu6bNCn7qg6lU6QxnKF5A5n\nKijZGr2untAXtpSd0OrLWYcve+5Fvclw7WGozX/rRPl9Yuibe/MJEOYBy57x\nTD/iM5X6WG/nTW8k6fj3bNWMKF82ySxvhuJUpb6jH//VIc1BMF7YCLBSjTNr\nCccYnLbnr8Kh4tD6ndSeNBl1MT+g3qBEHFHE0fDu3B89LHgZdZn+DYVe4LnW\nz2rlGlZb2Vzt5AEMW5zGcFhKaOD+3Zqwu0Bv8Hty3/D/iplukY3P6tycqL5M\nC2p5\r\n=WhPr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0347d3bfd88566bc92a40b9c852d4c71e6e8a2e2", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.1.0_1621672856730_0.7224356452461764", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.2.0": {"name": "open", "version": "8.2.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "d6a4788b00009a9d60df471ecb89842a15fdcfc1", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.2.0.tgz", "fileCount": 6, "integrity": "sha512-O8uInONB4asyY3qUcEytpgwxQG3O0fJ/hlssoUHsBboOIRVZzT6Wq+Rwj5nffbeUhOdMjpXeISpDDzHCMRDuOQ==", "signatures": [{"sig": "MEQCICKprEOoUFx1gSCKMhu+q/GHaAqF2KJihKMV89WhBW3TAiAHePeDK/MuA4wRzJH/OKEcbM2wvZ+XDI8E9pUxpxLQGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgq79KCRA9TVsSAnZWagAA9YYP/R/OOkqs2ttkr2G7ftAA\nMf46c+fvulZC8T3PKFm0UlNWwqZfFxMhoWPOy8f9c9q6chOu7Rh9WVnrjxVj\n6/4SW794hzDfFWvB/jZyaLFBG7aKzo0R1GucyfsQcPuFY3WxGP0hiR+jc23P\n47649bg5CXqFDIoOaLqi0IxgVyp9TcO3Iww5E2rn8H+XVw2CKeAbB9diibcZ\n+FE6+Epp4xHAsFw7VoDJaBCfoEs3TtaiQ8i3kS7sANXugz+MYlpg9gA1dvzu\nRlcHtbEMVmvJr7/kRjCF9H6QUTUMB54ZqHwUp5zZXV95CNDCtsU1kG8qwO19\nf2h8YhGnRqiIrAcDuFnXZdSpR1KB+0SOZTTO0ppNofaRPR/vjBThlD0Tpex5\nuUzrtvrFn6mLYx4HcrxWzNN5n3QIcPtcrGyyEeCzvbX0/VbDF3dPJUDg/Am9\nOqpFzr9cmF/v2A2suUia24MA76fzRsu5VmCwY4oiR1bmKE/I9u/KT8UQi64l\noR4vOIRcqPjZe5Lk9zQhkVxVkRowpb9bIghton1K+8kR/xUz8qYkmxPy5ibC\nmbkA/YPJAkOQKI8f2dixtvU9PTTj4DatYw3PCrUVcTamCGCRFedWLwnv//+7\nmhuex3RzoBjUmmnQuJgCtH+3ZDNBcuY1a2NUUSXkAIopJsMcmVEmsYrYPiFm\nfwRS\r\n=yaDb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "c209060c23d55e804b6780e47bca5a86bd309a01", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.2.0_1621868362411_0.6804200750373266", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.2.1": {"name": "open", "version": "8.2.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "82de42da0ccbf429bc12d099dad2e0975e14e8af", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.2.1.tgz", "fileCount": 6, "integrity": "sha512-rXILpcQlkF/QuFez2BJDf3GsqpjGKbkUUToAIGo9A0Q6ZkoSGogZJulrUdwRkrAsoQvoZsrjCYt8+zblOk7JQQ==", "signatures": [{"sig": "MEQCIATReCDTMO2E1J8LLNRfv+SL1W/S5rdEnTXyiZSbkC03AiBDpOiPHDVk7SzxHnDqR3U4sZgCGoD90EBtv4+/TnCSOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz3WjCRA9TVsSAnZWagAAyEMP/1XMaALDRWSa3DtEbS11\nVb896gKtWmLPMa112eR3Lxqsx6CcimsxmZt9kN+Ox7q2l/RtQiYle0jcJUoG\n0cLbweliwMKc8bvyjUj9whVUJzGzbngvUi/YOpItH8Tdmm0gy46FKHHKDZtr\nC6cPzzzfIBgmwFQTxeKeyy7Nz079MyxViNsHDQxotavf1ORbgRNNTYMP6QRo\nxvISC2NO+SxixGYgIAJAIkDKharY5o36235L98GspeNpqYOY5a3R9UsFkeir\nuXHD6/l9g6p1jo3KjItoIJd7EURnEvIolOSHZptfBUTl/2lGhjUF2Zxlte2J\nKAarudjNV5uEa50/vXPZ3SnHFkSd2I7vfHK6ISmUhRXkEBB+03i6Yqxgk6A2\nwwrkGWugQzqHpFYmAqfvtft9UtTBla4W2sQDxevFZwRWhnVL/kT6YVaToogE\n5PqqmrsWM9s+E5t6bLliLC5UhNqRP8q6zVLPT1XI2oSg+WfEQSOnZIYHsKTJ\nrdH9Vu3439Qt2OXYsDumKXLYfn5QSYsWHoX6PYktJ7aSUERq8i9yhfNADz2v\nDqTgA926Q1Z9hravDlIy1aNtbp+z+xk7iIghlqDM6fKCPyi27S4W5kSdG9zZ\nuTmCT5plxVIsN8t6QyTutsMghF5T8LhTCOBlC0USN1UK0N5DlgF14bwNgKc4\n0+7Z\r\n=fea7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ce40e85de3f77515985f5971d293659ea4b30b83", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.2.1_1624208802861_0.546695768233771", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.3.0": {"name": "open", "version": "8.3.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "fdef1cdfe405e60dec8ebd18889e7e812f39c59f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.3.0.tgz", "fileCount": 6, "integrity": "sha512-7INcPWb1UcOwSQxAXTnBJ+FxVV4MPs/X++FWWBtgY69/J5lc+tCteMt/oFK1MnkyHC4VILLa9ntmwKTwDR4Q9w==", "signatures": [{"sig": "MEUCIQDdYtrrYdRaWQmKS7CuRdunW/Vnru3IlIKAa2hV0Lr1QgIgZATFBCFJIurVB0M7KFEQ30inCNMrYXitqIJClyFvgOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46366}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7908a585fd0d85d7ad32d8f34d95a77f13d595d6", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.3.0_1633622286846_0.9087879499303659", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.4.0": {"name": "open", "version": "8.4.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "345321ae18f8138f82565a910fdc6b39e8c244f8", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.4.0.tgz", "fileCount": 6, "integrity": "sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==", "signatures": [{"sig": "MEYCIQDk1rIqeLnZMcS6PCBHx7/8H+nqludEIOSUITrj738OOwIhAKXicmcZF5v3CFMLqn++OQZOZIUl1smD/qxP34xKmQbO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh213yCRA9TVsSAnZWagAA160P/Ah8biSalkGEZHtWLWcW\ndkMPC+U6rlflGu8mB585QepuNGID/D1tepAcse7okaYO5KAvkXxiN0+f0ZLx\niUU8P5TeNxeMWRQ4u+C83TdILjQaA2981/pkQujYQPHkth6xi8RHOEoKJw34\nccEDF6sxBc5XIzEGYdhksJPwG2vuhwaIcCoHz1vGQqhS8a/70lQ58NIjfkKb\niReX1Vl+paLhfAs+9JmApf+yEe6Lp3kjyQe9BIg1jl/+rok3lnX9VCR0nXjn\nZtZFGhcTgfOK4GQptFpGQp8gOs30dq0xiC8SAC9PinHCOXgKjPOe9BzqaU38\nmjIX0I3ZQkeU/pmba1zCUg3z5URdnn9TuiDpAvrSMY6F30MlaZ1qSCCqOj7n\n2+AgMKYdrwOt5hD7qIGi/SXxe/t3kW/IN3l3OCJ9Oi8C3+XFYIKMJ7ng/Q3K\nouz3ijljVLPUw8lI+5TWToRrgU+YrK/KSAMqBxKLNGJI+rX0qzuyLcwan8Hl\ngj1A33U4P1VzhxH9g3JRKXFkKkH8EzJXOlGLt09tlLC91B+5ASHGEKnM9tA9\n8XNsmWwqXLWIBnrd3MjO967d2ymTLxhKJqPIniU5Y5eNZEGpItyya+PbxtGY\nNwaZhehsLjAewEjqlsV7g84798UpvorB7nqq8DCVQXhzs+nyujoo01oDt39Z\nHwET\r\n=eJ53\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7579417bbd3b3fc155b10f9b9b2eb71381e13e9a", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.4.0_1635046386127_0.6259313853552249", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.4.1": {"name": "open", "version": "8.4.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "2ab3754c07f5d1f99a7a8d6a82737c95e3101cff", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.4.1.tgz", "fileCount": 6, "integrity": "sha512-/4b7qZNhv6Uhd7jjnREh1NjnPxlTq+XNWPG88Ydkj5AILcA5m3ajvcg57pB24EQjKv0dK62XnDqk9c/hkIG5Kg==", "signatures": [{"sig": "MEUCIQCAje/FH0+2VqCcSXMohgch4f9BKmqsb9NXQ5d8kabWTwIgU1SdPPulWnI4iyym0e/RUUIXB3u2L62ExnWEYHFmrLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj45P6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo//w/+PH+Ka5oa0+Be2kIHjrbucQRTdECVUtPF7Xir5LSvLAUpc6Yf\r\n4yv4a3B9DmnwS53CSl+UF6Dk3MIkc+a00jWCeMvyH1+eNm1JQE7TYaj+0OuK\r\nxt/+i9DCg/jPqXh870NLuMt/099HEXUaFV2wIg/SCdxdvnVv+ZkHgjDuZ/9c\r\nDG/XrBo1EO5bQquBkpd4sRQIyjhb6XPf8Vv+V3MHgBAo1APxJ2b6rnRZ/rj1\r\nz+kmUF3S3ZhfpPwI+m9x5PDwFpo8Xqe0IIxl/yyMEWw3vV7pzl4o4m7SEOlk\r\nGRrU3a4Rx3Y8uRJwiwwVK1n7hwiduPM5mVnj2ql5yCxpJ6AajABDqZOB7w73\r\nhBO7M/2ax9yezhTB6+6Wg4LepA7767PeoCf8FdTKoiGTzKc+m2vRMvzrvTe4\r\nwRoceXcFjmROeQ2CVX/8mh1lOI0/M3RI5Q+ronWSlhB+OLzmwBY50sXctYE2\r\nEeSD3W8BQf5jeKSEGFPyCeFUKIdTbn5Qt6K7hcUtJbFsnUfUbEPaolYovpbL\r\nBtVvSNuyihUJiBb9qLHVaLP3NqcWICZp8Yg6cjHJ+RN2i5RAOXgQadYFZQH5\r\nwLLRknHrb8qapPXimWXIhP3+lPx543Pj5koB13wGRV/lSpo2q62vl/VT8OVQ\r\nyAqRbtImWS3hcNNd2fLeCzt6yfLvk1YytO4=\r\n=jVH8\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "27e4e3a193928fe0cfb43cee746292df339bb332", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.21.1", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.4.1_1675858937925_0.19636978897891577", "host": "s3://npm-registry-packages"}, "contributors": []}, "8.4.2": {"name": "open", "version": "8.4.2", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@8.4.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "5b5ffe2a8f793dcd2aad73e550cb87b59cb084f9", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-8.4.2.tgz", "fileCount": 6, "integrity": "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==", "signatures": [{"sig": "MEQCIDcVoaKT8+8FHo7pUQ121zzffO+CQgvapaPRG/cLOFfxAiAsQDOqZWTDTpk+M3M47m/gYvcBmC/Bq1BmQP2f7j94Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj82r1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDgw//YHKpnqxFvIFfRwslxaRL0+t8IAfJcZysqle/oR7Q1wJ0GgH6\r\nZKsplCvbp8bwNQI1FAUM+2vwZC0PUd3Y04J0kO2WMG//C65Nll/9Dz5cdSO7\r\nMEY7D3qAH/C1rF8twpySPDWbiaoENSpYb5Dt+BfJui7IbJAb6NSORk6fTo0D\r\nF8QJlq4ZdYJnHza3TSK79QjiFq8M6oTSt71RXJzuHRngJzXyfuhsoPV1yifw\r\ncXmEmbECAPO9h8D60zWlqVSk+JU0ezVtewZwO+A2HcqtoOGqGLlR7MuQdnqt\r\nJMLy/0tdoeuK3UqFYV5WvO63TooJl/k5cg4ShmviNowUTmRR5/PEO3UeDHpu\r\nuBwvCL1XURhCE8Ul3zxAauIsqV+99z+gu4PC8aW/53pBwWoo2FV5y/0T2t0W\r\nVYbBa6tWGIFtsIInzH+uLMgt/qzolb4zxxOY0NXhJCT7x581DzVtqpU7QSe7\r\n5VWy0PkHYVAPMquNg7KyGrV51qzAivDhfVlNKzmAp22QWELN5l7fjJmB1iXo\r\nupenV9ibsmcMl5k7zzn6M5BaGoAXJ9hoyzPDfcr0J2VmP1944DZ/V+gzosOK\r\n3LaV3aICGiZskK5/EAwQswSmD03VGbqVvAgaTnNwH+EbTHfgb6gcij8DyDJt\r\nSJTzVftkBdi3iYn2CbkqBynZu5grL7ry0oo=\r\n=Rzyz\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "cbc008bab21f657475b54e33a823b2941737da6f", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"is-wsl": "^2.2.0", "is-docker": "^2.1.1", "define-lazy-prop": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.1", "ava": "^3.15.0", "tsd": "^0.14.0", "@types/node": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/open_8.4.2_1676897013073_0.44287481541481144", "host": "s3://npm-registry-packages"}, "contributors": []}, "9.0.0": {"name": "open", "version": "9.0.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@9.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "2cedd60ee6eb55659b20f9e7a5bd03eb7709ff00", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-9.0.0.tgz", "fileCount": 6, "integrity": "sha512-yerrN5WPzgwuE3T6rxAkT1UuMLDzs4Szpug7hy9s4gru3iOTnaU0yKc1AYOVYrBzvykce5gUdr9RPNB4R+Zc/A==", "signatures": [{"sig": "MEUCIEjeKnMY55dBvRhpRcHyEIysBjZLCHKVrPS+3v4YH3sgAiEAtPL9ddL0U35c+Ez/CNUzD2xY61HN+MWhZmAlHamtXKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGCt0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos9Q//VY6Ew/dv239+D48bY3PZGoFwdSmyIWJxlOJ2yNqiBfAm3MB/\r\nOpvaTxRizqfv/g+BSQlSHYwLnR7o0Yj99AeILippU4jId07UDzfIKAlQgscc\r\n7yu5tufeY/Y1PJJZNQ6PbJyBJiTccQx0clWWZNQKfFf0bMz3Csw6qqAzpl8w\r\nV97XA1RrSIlo0ColhHbK27z5H2hX6N/9MrKivWY7ND2Z6xSqC0sCib1ucCNx\r\nGXniBu2wQm4P4Ny8IuMfNaRXenYUDYzX/X3ADMAaJjO1TKjPHLjGtohMMgM9\r\nX4oQyEr67o5e5EOwY24R/q6DSxtA7bJ50IvoxDieqdfPChPkloNE2D+1HM9t\r\nkbo2npuBtM2zEMwcG/aMQDXkGKdNwnR9ew5u6c3OpCSP81EEH3NyUOIUJVkA\r\n3qmxjddA8UsSV2bAQoFX9KVhIxShDq6uRK3QFXArwBXMxj54yaKRbHAUo/9T\r\nfxYrt1VO1QiwGNaQpCn4PCw4uSfuFHBSnjpgLz3T3uomzokSmyef7U1LSLt1\r\n+d7CmPj/2yCSNCr7RGp4fobpbu8Eq81M0uk2JLD9lTtFqup6vkqnDbFhcNtP\r\n1B6a7zadMUV8O++xOGwfQ2gMfcWBL4q7dMc7ZH6RAokxgHe72Py7NnLIxEk4\r\n5kLGAMl2+/HpeM6QxRsR292khLhf0qwURDY=\r\n=gdYi\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "36c61af1e696ef4365fd12dcc733586877106f19", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"is-wsl": "^2.2.0", "default-browser": "^3.1.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.28.0", "@types/node": "^18.15.3"}, "_npmOperationalInternal": {"tmp": "tmp/open_9.0.0_1679305588631_0.5884917315127183", "host": "s3://npm-registry-packages"}, "contributors": []}, "9.1.0": {"name": "open", "version": "9.1.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@9.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "684934359c90ad25742f5a26151970ff8c6c80b6", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-9.1.0.tgz", "fileCount": 6, "integrity": "sha512-OS+QTnw1/4vrf+9hh1jc1jnYjzSG4ttTBB8UxOwAnInG3Uo4ssetzC1ihqaIHjLJnA5GGlRl6QlZXOTQhRBUvg==", "signatures": [{"sig": "MEUCIFaOQHRzgaieohv6RfGRgiCvG+DpFqH0t+w1P8wt39YdAiEA37xwDFLfH579/vOf2TygxFSKv8kPsILpPQnPdqsR6QA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH5zGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc5RAAgzoTWcRn3eqRsWesx3v38PmJCwD19Npf+NykH1g+74MtJ12J\r\nd60v7Bs72dN+9RjnjpaXBLbqIPq4Tg60951S//RCl56pgjgNY7uxgsZXXnnb\r\nrlf9BL/JxS7QFM01EbihZcDuOw0Wh3RLFRmIwjuQ33TiGiZ7j5ToQLunS/H9\r\nBNfXkp2htWmPrMm0ub+AWM87X3ilpqKEA2iQtz06CJnMxOIgDWcWvvUgvqTH\r\nIqiucpcD+u3mxNwurTq3IFCb13Nx/u7BN9kqx9g4dxSpfSlAbHNJ/Ugvf4Bv\r\n8etz/p5yKI3xvZRr7NBN9Di1C1xMUP1j2RsaLuf4hvFw0cJJZICTXa6YKjhm\r\npx3UbfD4+eVuaoRDaN7TxaU14jyq1WJGyKkb1+W1M40fvVbl2PqtWW+czZvq\r\np/i+ZI2rccv+ELY88QQPqvzL0Fc5IknBfTgHUxHlsmdJQ+pKHUIvPDUz3ZIg\r\nI1zXsZuz3sjP60aMXaxTof3oM6O6SN5mRfsQk4o4Iym8BWSb8aQhzyja0Mvo\r\nW3wAdhar9vGGCH72x9vEPlk/1+vIGbWjvN9kshF1Pz4mQ2IfJLhyj56QlOnj\r\nXsKzWvr7mN3aCXnOXBCZurIwE2Pwl8F4Hy9Z/IzcAO08mjxUvw2GvXAzvb8N\r\nJSQAj53kkeFjxHU2TaN5TiW7azc65Ynjrik=\r\n=oNSv\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "accd304abffaec9016f180496d4d3de40145b3c9", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"is-wsl": "^2.2.0", "default-browser": "^4.0.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.28.0", "@types/node": "^18.15.10"}, "_npmOperationalInternal": {"tmp": "tmp/open_9.1.0_1679793350383_0.4690584074817816", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.0.0": {"name": "open", "version": "10.0.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "60fead3e270fe5f9fd61d0c1844d06288ae30656", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.0.0.tgz", "fileCount": 6, "integrity": "sha512-WDekhGCZ7VHBw6sNDzCl42rVL315m/K/A1lYiZO2nHXn9T4yPBXxvrZKYkVPtsCahD815yhFNR9/Wq608PdIaA==", "signatures": [{"sig": "MEQCIFXfyFWUDrn/JZCmy3fN7ECWcwKi5xAiSuDYb3CtscIiAiBzuj+4R2xAKsHpgWcbHDAfehddE/ssa216jK/5I/pfcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37676}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2a8ec7046f7c1ce93dbfc6645786a2682c9def95", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.0", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.0.0_1702944438430_0.687740590028054", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.0.1": {"name": "open", "version": "10.0.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "1883d6762935c75960284b66190f12f787e30622", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.0.1.tgz", "fileCount": 6, "integrity": "sha512-+BCGXjtXs4qlmImA8X1AhSU2XyBqD8tJC/PNgWYbcEvNphnw2nyCvNEhAXYk9V+55h1B8M64oXind989YcWANw==", "signatures": [{"sig": "MEQCIAfcyaStVEhPn1SgXkN82qMMUwMt2rDU7K4n8sohRT0HAiB+D97XguGTzxWD0gbmwopedC2PbLgNmypts2B5P/eFdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37715}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "818946edd9a0554e852813773142330f9067ee64", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.0", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.0.1_1703243795793_0.27055894613873677", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.0.2": {"name": "open", "version": "10.0.2", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "93b099c6194552b47afac969c9b83f874aee1ab8", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.0.2.tgz", "fileCount": 6, "integrity": "sha512-GnYLdE+E3K8NeSE23N0g67/9q9AXRph5oTUbz6IbIgElPigEnQ2aHuqRge3y0JUr67qoc84xME5kF03fDc3fcA==", "signatures": [{"sig": "MEQCIEjPHThnucO2R16iCz+BM+qIfX/jNmvcH6mTMYMHsIL3AiB/Bry6PcA2ZUhORh6stUsZpZQuQRZdygmArt05nuJ0Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48475}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "31da4c35f6213ceaf9529f3e147fa37041c80c54", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.1", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.0.2_1703722824639_0.4806542654926671", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.0.3": {"name": "open", "version": "10.0.3", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "f60d8db49fa126c50aec751957fb5d7de3308d4f", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.0.3.tgz", "fileCount": 6, "integrity": "sha512-dtbI5oW7987hwC9qjJTyABldTaa19SuyJse1QboWv3b0qCcrrLNVDqBx1XgELAjh9QTVQaP/C5b1nhQebd1H2A==", "signatures": [{"sig": "MEYCIQDL+ou+g2EkSyDyeS4UmE2axcnBXLLe/Xgd2nG2HULj1wIhAK5sEP5zK7HMM7NtW/hChX1fjmUi4yUWPUcYeP8RM1Qx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48512}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2d5e95978807503b917482786ac5058c16c5193d", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.5.0", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.1", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.0.3_1704606627071_0.7351664090086318", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.0.4": {"name": "open", "version": "10.0.4", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.0.4", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "4869d009dc5b706ae6585699e15d8ccc6cb73629", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.0.4.tgz", "fileCount": 6, "integrity": "sha512-oujJ/FFr7ra6/7gJuQ4ZJJ8Gf2VHM0J3J/W7IvH++zaqEzacWVxzK++NiVY5NLHTTj7u/jNH5H3Ei9biL31Lng==", "signatures": [{"sig": "MEUCIBV6u6Cc5h+VQjlpy4Cny243Ft7Ywt0bxNE/wExc0EeqAiEAxZ3GVpw7y15wqQ6Nhs6A5ih+Ezdq7N1WsW1T99v1Zas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48542}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "ee13d939ad1809a87110dc82aaba6878962159f9", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.1", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.0.4_1708933918526_0.4760196414543685", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.1.0": {"name": "open", "version": "10.1.0", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "a7795e6e5d519abe4286d9937bb24b51122598e1", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.1.0.tgz", "fileCount": 6, "integrity": "sha512-mnkeQ1qP5Ue2wd+aivTD3NHd/lZ96Lu0jgf0pwktLPtx6cTZiH7tyeGRRHs0zX0rbrahXPnXlUnbeXyaBBuIaw==", "signatures": [{"sig": "MEUCIQCmyzfLokeXOjJZPLhz0kn7JVyzSQFnU5yYBWGts105eAIgCUylxLojzrSc0eUdh/TuffFQJ5Po9a5BrKmarvJQYvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55180}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2ea66da8e8b20880d235447cf4c94ba275da6a5a", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.1", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.1.0_1709912793106_0.32230408785490927", "host": "s3://npm-registry-packages"}, "contributors": []}, "10.1.1": {"name": "open", "version": "10.1.1", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "open@10.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/open#readme", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "dist": {"shasum": "5fd814699e47ae3e1a09962d39f4f4441cae6c22", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.1.1.tgz", "fileCount": 6, "integrity": "sha512-zy1wx4+P3PfhXSEPJNtZmJXfhkkIaxU1VauWIrDZw1O7uJRDRJtKr9n3Ic4NgbA16KyOxOXO2ng9gYwCdXuSXA==", "signatures": [{"sig": "MEUCIH2k2/3O7M1JHGpNxOuoO52hFh2c+XNzcsRS7EG3vwtjAiEAhZYUcUwlMWghmwGP6V9naNefNY+/MuzB1PQA87rXp3E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55216}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "31b71c5f454690acb92d2dbcedf58cdfe746ea5a", "scripts": {"test": "xo && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/open.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Open stuff like URLs, files, executables. Cross-platform.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.1", "dependencies": {"is-wsl": "^3.1.0", "default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^6.0.1", "tsd": "^0.30.1", "@types/node": "^20.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/open_10.1.1_1744706558212_0.7616565020729962", "host": "s3://npm-registry-packages-npm-production"}, "contributors": []}, "10.1.2": {"name": "open", "version": "10.1.2", "description": "Open stuff like URLs, files, executables. Cross-platform.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/open.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && tsd"}, "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0"}, "devDependencies": {"@types/node": "^20.10.5", "ava": "^6.0.1", "tsd": "^0.30.1", "xo": "^0.56.0"}, "_id": "open@10.1.2", "gitHead": "8481ddf646a6b4bc32a059d8bd5685d88c1368a0", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "homepage": "https://github.com/sindresorhus/open#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-cxN6aIDPz6rm8hbebcP7vrQNhvRcveZoJU72Y7vskh4oIm+BZwBECnx5nTmrlres1Qapvx27Qo1Auukpf8PKXw==", "shasum": "d5df40984755c9a9c3c93df8156a12467e882925", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.1.2.tgz", "fileCount": 6, "unpackedSize": 56489, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC3gu3tSJRJlszhTR1KRDTSA76MuF/TtF0DTz5juhtv1QIgFt4+60Fc5kHF3aF8DRL4sPmRMFUq2TWud8e3YGYJoyw="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/open_10.1.2_1746082184302_0.9151816832495807"}, "_hasShrinkwrap": false, "contributors": []}, "10.2.0": {"name": "open", "version": "10.2.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/open.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && tsd"}, "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "wsl-utils": "^0.1.0"}, "devDependencies": {"@types/node": "^20.10.5", "ava": "^6.4.0", "tsd": "^0.32.0", "xo": "^1.1.1"}, "_id": "open@10.2.0", "gitHead": "dc4dc77f3eba6d3612c05ec15d97e89a2ca77dc9", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "homepage": "https://github.com/sindresorhus/open#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-YgBpdJHPyQ2UE5x+hlSXcnejzAvD0b22U2OuAP+8OnlJT+PjWPxtgmGqKKc+RgTM63U9gN0YzrYc71R2WT/hTA==", "shasum": "b9d855be007620e80b6fb05fac98141fe62db73c", "tarball": "https://mirrors.cloud.tencent.com/npm/open/-/open-10.2.0.tgz", "fileCount": 6, "unpackedSize": 55605, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCPNVW8lGsUBocxcvbUrNiQaMKmQyDaygAKGD5x+njb9wIgEJzeyWoWfLlZqXB/fvyX9+J0E+RAdcQ4V1cJSJB23Qc="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/open_10.2.0_1752500802662_0.11442605626263713"}, "_hasShrinkwrap": false, "contributors": []}}, "time": {"created": "2012-04-14T16:52:46.254Z", "modified": "2025-07-14T13:46:43.085Z", "0.0.0": "2012-04-14T16:52:47.921Z", "0.0.2": "2012-06-19T18:04:28.431Z", "0.0.3": "2013-01-26T17:59:49.412Z", "0.0.4": "2013-07-25T01:14:22.671Z", "0.0.5": "2014-04-10T11:46:19.980Z", "6.0.0": "2019-03-26T17:54:43.168Z", "6.1.0": "2019-04-07T09:41:16.344Z", "6.2.0": "2019-04-23T08:12:40.548Z", "6.3.0": "2019-05-12T08:50:39.977Z", "6.4.0": "2019-06-29T14:38:25.420Z", "7.0.0": "2019-10-15T18:42:20.550Z", "7.0.1": "2020-01-29T14:39:10.822Z", "7.0.2": "2020-02-01T08:13:17.589Z", "7.0.3": "2020-03-09T06:16:34.557Z", "7.0.4": "2020-05-16T09:02:35.138Z", "7.1.0": "2020-07-20T00:04:19.787Z", "7.2.0": "2020-08-21T22:27:03.657Z", "7.2.1": "2020-08-28T11:16:33.110Z", "7.3.0": "2020-09-29T10:42:49.782Z", "7.3.1": "2021-01-07T07:36:46.471Z", "7.4.0": "2021-02-01T10:00:22.209Z", "7.4.1": "2021-02-14T07:43:10.550Z", "7.4.2": "2021-02-16T16:10:37.992Z", "8.0.0": "2021-03-02T10:49:36.266Z", "8.0.1": "2021-03-02T14:05:52.960Z", "8.0.2": "2021-03-03T08:12:10.115Z", "8.0.3": "2021-03-18T12:17:35.259Z", "8.0.4": "2021-03-21T16:27:34.087Z", "8.0.5": "2021-04-03T07:11:43.656Z", "8.0.6": "2021-04-16T05:00:05.057Z", "8.0.7": "2021-04-27T14:08:48.087Z", "8.0.8": "2021-05-07T16:07:21.344Z", "8.0.9": "2021-05-17T06:22:36.387Z", "8.1.0": "2021-05-22T08:40:56.922Z", "8.2.0": "2021-05-24T14:59:22.574Z", "8.2.1": "2021-06-20T17:06:42.976Z", "8.3.0": "2021-10-07T15:58:07.017Z", "8.4.0": "2021-10-24T03:33:06.315Z", "8.4.1": "2023-02-08T12:22:18.109Z", "8.4.2": "2023-02-20T12:43:33.252Z", "9.0.0": "2023-03-20T09:46:28.774Z", "9.1.0": "2023-03-26T01:15:50.512Z", "10.0.0": "2023-12-19T00:07:18.661Z", "10.0.1": "2023-12-22T11:16:36.029Z", "10.0.2": "2023-12-28T00:20:24.923Z", "10.0.3": "2024-01-07T05:50:27.286Z", "10.0.4": "2024-02-26T07:51:58.675Z", "10.1.0": "2024-03-08T15:46:33.286Z", "10.1.1": "2025-04-15T08:42:38.375Z", "10.1.2": "2025-05-01T06:49:44.518Z", "10.2.0": "2025-07-14T13:46:42.923Z"}, "users": {}, "dist-tags": {"latest": "10.2.0"}, "_rev": "5205-9099448d24ab213f", "_id": "open", "readme": "# open\n\n> Open stuff like URLs, files, executables. Cross-platform.\n\nThis is meant to be used in command-line tools and scripts, not in the browser.\n\nIf you need this for Electron, use [`shell.openPath()`](https://www.electronjs.org/docs/api/shell#shellopenpathpath) instead.\n\nThis package does not make any security guarantees. If you pass in untrusted input, it's up to you to properly sanitize it.\n\n#### Why?\n\n- Actively maintained.\n- Supports app arguments.\n- Safer as it uses `spawn` instead of `exec`.\n- Fixes most of the original `node-open` issues.\n- Includes the latest [`xdg-open` script](https://gitlab.freedesktop.org/xdg/xdg-utils/-/blob/master/scripts/xdg-open.in) for Linux.\n- Supports WSL paths to Windows apps.\n\n## Install\n\n```sh\nnpm install open\n```\n\n**Warning:** This package is native [ESM](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules) and no longer provides a CommonJS export. If your project uses CommonJS, you will have to [convert to ESM](https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c) or use the [dynamic `import()`](https://v8.dev/features/dynamic-import) function. Please don't open issues for questions regarding CommonJS / ESM.\n\n## Usage\n\n```js\nimport open, {openApp, apps} from 'open';\n\n// Opens the image in the default image viewer and waits for the opened app to quit.\nawait open('unicorn.png', {wait: true});\nconsole.log('The image viewer app quit');\n\n// Opens the URL in the default browser.\nawait open('https://sindresorhus.com');\n\n// Opens the URL in a specified browser.\nawait open('https://sindresorhus.com', {app: {name: 'firefox'}});\n\n// Specify app arguments.\nawait open('https://sindresorhus.com', {app: {name: 'google chrome', arguments: ['--incognito']}});\n\n// Opens the URL in the default browser in incognito mode.\nawait open('https://sindresorhus.com', {app: {name: apps.browserPrivate}});\n\n// Open an app.\nawait openApp('xcode');\n\n// Open an app with arguments.\nawait openApp(apps.chrome, {arguments: ['--incognito']});\n```\n\n## API\n\nIt uses the command `open` on macOS, `start` on Windows and `xdg-open` on other platforms.\n\n### open(target, options?)\n\nReturns a promise for the [spawned child process](https://nodejs.org/api/child_process.html#child_process_class_childprocess). You would normally not need to use this for anything, but it can be useful if you'd like to attach custom event listeners or perform other operations directly on the spawned process.\n\n#### target\n\nType: `string`\n\nThe thing you want to open. Can be a URL, file, or executable.\n\nOpens in the default app for the file type. For example, URLs opens in your default browser.\n\n#### options\n\nType: `object`\n\n##### wait\n\nType: `boolean`\\\nDefault: `false`\n\nWait for the opened app to exit before fulfilling the promise. If `false` it's fulfilled immediately when opening the app.\n\nNote that it waits for the app to exit, not just for the window to close.\n\nOn Windows, you have to explicitly specify an app for it to be able to wait.\n\n##### background <sup>(macOS only)</sup>\n\nType: `boolean`\\\nDefault: `false`\n\nDo not bring the app to the foreground.\n\n##### newInstance <sup>(macOS only)</sup>\n\nType: `boolean`\\\nDefault: `false`\n\nOpen a new instance of the app even it's already running.\n\nA new instance is always opened on other platforms.\n\n##### app\n\nType: `{name: string | string[], arguments?: string[]} | Array<{name: string | string[], arguments: string[]}>`\n\nSpecify the `name` of the app to open the `target` with, and optionally, app `arguments`. `app` can be an array of apps to try to open and `name` can be an array of app names to try. If each app fails, the last error will be thrown.\n\nThe app name is platform dependent. Don't hard code it in reusable modules. For example, Chrome is `google chrome` on macOS, `google-chrome` on Linux and `chrome` on Windows. If possible, use [`apps`](#apps) which auto-detects the correct binary to use.\n\nYou may also pass in the app's full path. For example on WSL, this can be `/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe` for the Windows installation of Chrome.\n\nThe app `arguments` are app dependent. Check the app's documentation for what arguments it accepts.\n\n##### allowNonzeroExitCode\n\nType: `boolean`\\\nDefault: `false`\n\nAllow the opened app to exit with nonzero exit code when the `wait` option is `true`.\n\nWe do not recommend setting this option. The convention for success is exit code zero.\n\n### openApp(name, options?)\n\nOpen an app.\n\nReturns a promise for the [spawned child process](https://nodejs.org/api/child_process.html#child_process_class_childprocess). You would normally not need to use this for anything, but it can be useful if you'd like to attach custom event listeners or perform other operations directly on the spawned process.\n\n#### name\n\nType: `string`\n\nThe app name is platform dependent. Don't hard code it in reusable modules. For example, Chrome is `google chrome` on macOS, `google-chrome` on Linux and `chrome` on Windows. If possible, use [`apps`](#apps) which auto-detects the correct binary to use.\n\nYou may also pass in the app's full path. For example on WSL, this can be `/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe` for the Windows installation of Chrome.\n\n#### options\n\nType: `object`\n\nSame options as [`open`](#options) except `app` and with the following additions:\n\n##### arguments\n\nType: `string[]`\\\nDefault: `[]`\n\nArguments passed to the app.\n\nThese arguments are app dependent. Check the app's documentation for what arguments it accepts.\n\n### apps\n\nAn object containing auto-detected binary names for common apps. Useful to work around [cross-platform differences](#app).\n\n```js\nimport open, {apps} from 'open';\n\nawait open('https://google.com', {\n\tapp: {\n\t\tname: apps.chrome\n\t}\n});\n```\n\n`browser` and `browserPrivate` can also be used to access the user's default browser through [`default-browser`](https://github.com/sindresorhus/default-browser).\n\n#### Supported apps\n\n- [`chrome`](https://www.google.com/chrome) - Web browser\n- [`firefox`](https://www.mozilla.org/firefox) - Web browser\n- [`edge`](https://www.microsoft.com/edge) - Web browser\n- [`brave`](https://brave.com/) - Web browser\n- `browser` - Default web browser\n- `browserPrivate` - Default web browser in incognito mode\n\n`browser` and `browserPrivate` only supports `chrome`, `firefox`, `edge`, and `brave`.\n\n## Related\n\n- [open-cli](https://github.com/sindresorhus/open-cli) - CLI for this module\n- [open-editor](https://github.com/sindresorhus/open-editor) - Open files in your editor at a specific line and column", "_attachments": {}}