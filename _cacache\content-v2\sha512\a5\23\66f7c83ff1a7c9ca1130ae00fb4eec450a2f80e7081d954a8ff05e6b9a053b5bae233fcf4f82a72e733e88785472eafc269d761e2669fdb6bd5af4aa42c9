{"name": "async", "versions": {"0.1.2": {"name": "async", "version": "0.1.2", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.2", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "be761882a64d3dc81a669f9ee3d5c28497382691", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.2.tgz", "integrity": "sha512-y7uCLBkkDe5614yPPM0Ch8MyOYvVvQjrzP7Xqua/HR2Ea+tht/zGIdppxnPi9yujy0O//0LuH5dfNN+5DFAMJQ==", "signatures": [{"sig": "MEUCIQDx/ZqWoNPdHkXCUfl+RRJD3hawX+xwwv1h8lNjQc1M7gIgSv2gHAaezX4xsAZA6LrhoJpoOZU3gS/4oJZf7wL3L/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.4": {"name": "async", "version": "0.1.4", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.4", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "29de4b98712ab8858411d8d8e3361a986c3b2c18", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.4.tgz", "integrity": "sha512-z3WOSK7jjl/DwVZ5Y4g+7v+BSFY1XdYQln480eTUp9A/23zubrfQklzH+dtgB3KeT+8nbqaFJUaJEkplcCEPgQ==", "signatures": [{"sig": "MEUCID9EblVgyJHZ1n2FkIsNwzIEdz+u0xAw77dR5o4conu0AiEAuUcxhvP4Jeb12+wmgDPjPssxIQPwobGRzRhDp5wY858=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.3": {"name": "async", "version": "0.1.3", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.3", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "629ca2357112d90cafc33872366b14f2695a1fbc", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.3.tgz", "integrity": "sha512-HdubH4L9fhDRHv3OVz+i5YfDxZ1j0a7R0Q5SrD23Cv6LRPQ16bhbJu33j23P5BpgmokKmymLuxzKrZs9ZcQMWA==", "signatures": [{"sig": "MEUCICoo37sNyiclHlRp8810G2IKQ8n8dsKz2hRusY7y9w3OAiEAjG6iIfciz68VIqNIEwEmtXdiir6rHP5DL418P1vtbxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.5": {"name": "async", "version": "0.1.5", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.5", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "9d83e3d4adb9c962fc4a30e7dd04bf1206c28ea5", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.5.tgz", "integrity": "sha512-KhhzvRCyjYe6SXWfASlCeRTO3xPOF4biDq3MO9l7LDjV7GVnwKS5EYeujPLlQ96RTZwpn94tGkgKsV5q/EZHkw==", "signatures": [{"sig": "MEYCIQDz7Pgb32wEC1fBezGEUuHlrlOVckBymFfqeaIJm+2KuQIhAJ3dESAlL+XZLdQHqiOfjItu6UWV/f1EbCd1CxdEVddO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.6": {"name": "async", "version": "0.1.6", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.6", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "2dfb4fa1915f86056060c2e2f35a7fb8549907cc", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.6.tgz", "integrity": "sha512-gbXCpNZerhfYr8Q4MqwgODncwRzYIE9r7zsZQ2xur0PxeOXbDNVfgsuT4Fq5UxQ4VnkjtaYfvv++AJz3WfNFeg==", "signatures": [{"sig": "MEUCIAagICqHFXa54YGVSuslP/BmDsJjaHvsISHrsYxIRrhoAiEA4eM4O0o/YE4QdkPxQ204Qkp38fI2KUYHUCqjHvEwo3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.7": {"name": "async", "version": "0.1.7", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.7", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "e9268d0d8cd8dcfe0db0895b27dcc4bcc5c739a5", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.7.tgz", "integrity": "sha512-gElUZ5b8Tdin6tZK8Ls/Vadz8IpCnylQJoep5bd4aqB7Jb+aMJL8ugGAegkpBdf4HW07rk7Yohp0IbioMghX0w==", "signatures": [{"sig": "MEUCICZZdwOS1Syh7ewlb70iKc2D64inRhh0YHBUGgg3ehmXAiEA/9xeeVH144Ueqe4u68dtKmtPFmXMXyis1cGQKfpknsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.4-1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true, "contributors": []}, "0.1.1": {"name": "async", "version": "0.1.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.1", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "fb965e70dbea44c8a4b8a948472dee7d27279d5e", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.1.tgz", "integrity": "sha512-YwXZvW4E3yUyc/9HBv1LFxeCBeY2AC0hsB9JnDbufoTO9VU/HXyajvD9ATGlzqE9JKBSlIp3zHO1QAsG1SimlQ==", "signatures": [{"sig": "MEQCIGsxoel6nf3x+tSA6kd8+chO5y9JSlKD4SHO6npMrmQcAiAzd3/TuIdQlAcJbWDNKBK+sLcwlWfOz/oXqpv6yCqa8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.0": {"name": "async", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.0", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "ab8ece0c40627e4e8f0e09c8fcf7c19ed0c4241c", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.0.tgz", "integrity": "sha512-9PGyP+84rspwZmkJxk/atgNCOO5LkZ+q1MQTg0SbI5tgkVjHM4iT3M/nzn40wkwAAXfqYMqEKyFnTGdLloG3CA==", "signatures": [{"sig": "MEQCIDx22dcfHtrSfCRTIK9/Dmz1vSlOBxWFhMseHpvyxs8FAiBITUWqm9hAsPe5/3h7i/qs1hbnqrDwptwcQqLt5gmTeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true, "contributors": []}, "0.1.8": {"name": "async", "version": "0.1.8", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.8", "bugs": {"web": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "52f2df6c0aa6a7f8333e1fbac0fbd93670cf6758", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.8.tgz", "integrity": "sha512-5WGWFUyVb/6iKBPBfsewoI+HtHaTcfeHeKQNIpG8IKmH+r+Y34hLUt5AH7e9vU97SmuAVvZiqX9F9NUWFwUU6A==", "signatures": [{"sig": "MEUCIFHZFTal9MRg5V92uSwD72Dhby6bSDGdXddhKB7R5U9HAiEA8WOyxr+fZe2jc52kronNkbadCw/tFP33AB5vJqLRk5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeSupported": true, "contributors": []}, "0.1.9": {"name": "async", "version": "0.1.9", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.9", "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "f984d0739b5382c949cc3bea702d21d0dbd52040", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.9.tgz", "integrity": "sha512-to+lCTfZfYBMLaOWhmmYcfeRZv8SpjcSGTV3AXCOe0HcZ4l7DGD8F4DrpGjnL1Pdor1GoPrbah3ZoicEWYQpTw==", "signatures": [{"sig": "MEUCIQCQvspvJIlSwq4AwCC9DIP/sAJyOLjp1iOXsS/AEgQTLwIgYKfAqLWIVUSC+4r0XJuOSFz+w8NWbNAF4cMinX1NXKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.1rc7", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.7", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.10": {"name": "async", "version": "0.1.10", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.10", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "12b32bf098fa7fc51ae3ac51441b8ba15f437cf1", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.10.tgz", "integrity": "sha512-IKQ+XgK9sP3w1hSbIwwVXrjfbnL2N6/lPc8yf0ODBiQlVnwkjTVYPx+yQYwVwUr/TVOpEVA2OKRINJBQp25Sng==", "signatures": [{"sig": "MEYCIQCn6Lu/CvjlY4S8r/MOGV7NoKkzfrLfgKrY3E2WhNlmiwIhAPuovHzsF3l+Wqk3QrglIOj5zSjCJohXmL0oQmSC65e8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/home/<USER>/.npm/async/0.1.10/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.11": {"name": "async", "version": "0.1.11", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.11", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "a397a69c6febae232d20a76a5b10d8742e2b8215", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.11.tgz", "integrity": "sha512-UWxk0UvrSb4s+Yrr0IUXkgWGLi6oz9n5McjIU+G2g2IswXxs9/EwGz6eWiS69/NII/4hz/+nr1CXHtBicHOqBQ==", "signatures": [{"sig": "MEUCIQCqpTXHxY3Hmo8v4lhVUJNOvz9leak6i0m4MEi5RK/72gIgazXNy1XSVcKqWGwkUQ9An0HOKDyJhGYBLo8F41fSWwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/home/<USER>/.npm/async/0.1.11/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.12": {"name": "async", "version": "0.1.12", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.12", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "ab36be6611dc63d91657128e1d65102b959d4afe", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.12.tgz", "integrity": "sha512-m3U65nGDodNLsrc1h0wyaxBLBgXcp1fOI0iyi0+yOCnuzCKQEfZsNlpheLJGe6R9f9ZpKOPQvbscKoxTf3+RZQ==", "signatures": [{"sig": "MEQCID0v3cVO5TOov/0sChcWNZUIFcPrCysYun/OYLESq51lAiBFwB/6QYk6fgarqiu9Hu7gNFaLTkgmr880uhBcp6y7HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/home/<USER>/.npm/async/0.1.12/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.13": {"name": "async", "version": "0.1.13", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.13", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "f1e53ad69dab282d8e75cbec5e2c5524b6195eab", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.13.tgz", "integrity": "sha512-HUE5SMTTRRWqeu2bwx7zCZ+Gim/O0z6iOMCJnz0hcdHbeJPMC6dTptzQbVGa8HFJ3Hp6Lv8vJeZYOfG9PN1xjA==", "signatures": [{"sig": "MEUCIQC1XBOLfpUw6UvWA3gZi1NmNuazwhtuHyug9eBsXYkqMwIgdnXTuxg56dx6JRVP8qhClShal4wI+qJjKATsLXRDaDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.101", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.14": {"name": "async", "version": "0.1.14", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.14", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "0fcfaf089229fc657798203d1a4544102f7d26dc", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.14.tgz", "integrity": "sha512-enxRg7+yAOj2LXDzWpBv9WKi5dlQVnGG9j3ekGiIczm+gmzNXaZzqf7vf+wW9NwvA1k279EsCbSOumxmjv5mtA==", "signatures": [{"sig": "MEUCIQD6UR0wANweVzJujDe7z0O+mo00TWpl2LR8BXlFshwXeAIgZOLdXsF3L7n/lDIKg2Czz8IqSglVz6uyMYIUcmn+kw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.101", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.15": {"name": "async", "version": "0.1.15", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.15", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "2180eaca2cf2a6ca5280d41c0585bec9b3e49bd3", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.15.tgz", "integrity": "sha512-AGVE6WcRsWX4QudgrVhdDUKAgCv67EwmzP3yEny/AI7/WqM+J8CStwMbGqeXC9p8ih4qota04EaMim/WvA8OCw==", "signatures": [{"sig": "MEUCIE0YUrqnO8Q0NMwUaiQm/zWvIZHQQAfdIZSWckZKD/lrAiEA504BIEMfraj3F+VfQDbFVsX4uIx82XxqAgSSj3ctmTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.0.101", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.4.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "contributors": []}, "0.1.16": {"name": "async", "version": "0.1.16", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.16", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "b3a61fdc1a9193d4f64755c7600126e254223186", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.16.tgz", "integrity": "sha512-TrtaROD5B+de7cbqPG6z96Jc+CXbnlQLzGPTj6Marf2+yh9hCe0+PVFdNMFxcdfjOF+0T/HrdN7EzC+COuxQpA==", "signatures": [{"sig": "MEUCIGrtIMUE1Pqf0ZZxsZHGBC0pcZBtYYiglG6AwjDVGb5SAiEAtJtHa+HerK1Qe1rjf3Uf3pCT/jFLLOce3JnHqAG5i1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.10", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.17": {"name": "async", "version": "0.1.17", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.17", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "03524a379e974dc9ee5c811c6ee3815d7bc54f6e", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.17.tgz", "integrity": "sha512-PvPaikCPsSEOfAR/je214lMCnaPLHAcA/KAjfEixFaQ7fSr44rrZZtD417vCn2JUt059QGZKKbaa8SZEDNPEvA==", "signatures": [{"sig": "MEUCIAF00fKoKWO4YrB/WJvpptvGLNheleNFn3Qyg2IuFvkDAiEAnrcbewblqTrZIRMyGuvet476VPOrHxgDmBd1Oha+C2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "scripts": {"test": "make test", "install": "make build", "preinstall": "make clean"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"uglify-js": "1.2.x"}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.18": {"name": "async", "version": "0.1.18", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.18", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "c59c923920b76d5bf23248c04433920c4d45086a", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.18.tgz", "integrity": "sha512-BNk8X5AAA0bk1d1E2DJ/HgfP71qvoUZtjGGkIUI2eUo8IyXdc0u0tpFOuuRTXNU99iqb9/OoLD6XPF+xoNXaTw==", "signatures": [{"sig": "MEQCIGbpzknEnfC5K0dKlbduTsjP+S+sKIM7yxeAepeqhKrjAiB9YVzDRhERuR+YAUw4U5vNrH1cE/d4citggaoy7P/qgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.19": {"name": "async", "version": "0.1.19", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.19", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "4fd6125a70f841fb10b14aeec6e23cf1479c71a7", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.19.tgz", "integrity": "sha512-Pr/gsr+9UBQPMjqAA+xVqfuCSOPzNGF79mgOTUOJnzahf4xDXBtak0aMlOqOwD+2gEkE7bNsd66qdfvBXcMOWQ==", "signatures": [{"sig": "MEYCIQCQYjlXXSckAyh4t4o/ZOJptydfkmIk03tpRUHBNcN8pAIhAL1ujP9UylSdcpsAcsuwRB0t0hq9oSFEFRRbJ4p1HN89", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.20": {"name": "async", "version": "0.1.20", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.20", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "ba0e47b08ae972e04b5215de28539b313482ede5", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.20.tgz", "integrity": "sha512-w4A6K4QUEfl7BrQGC7TGc/I5TFVi1qQKDQW1HsFvwNXbpYL+HPmuECxhQpZsYJ2z4WcRyL//8wgH6TEo/7dqtw==", "signatures": [{"sig": "MEUCIQDF9wPuKq/5lxektlLfbcoUPqMqhK3a+AfcUR1z6V7wxwIgKGROHgPQQ2rjcyw8r8ri0peByfddi3/vNeJMh2DG4bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.21": {"name": "async", "version": "0.1.21", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.21", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "b5b12e985f09ab72c202fa00f623cd9d997e9464", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.21.tgz", "integrity": "sha512-nJClLgEUpW3NIGNhuZpp/mWlZV/wc4n7EWC3sOn3VjDLADjFMvQgnxKbsNAXeLeswU8snGNUlGrHthRz3vvbFg==", "signatures": [{"sig": "MEUCICsB8YCMfaV1pa6azmvMpjWvXeGIGSr27TvxvTXtTSQHAiEAnk3gGbRsLhvnYeNUMJHyAIBmHOSNHorlz3/O/ETzxOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.1.22": {"name": "async", "version": "0.1.22", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.1.22", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "0fc1aaa088a0e3ef0ebe2d8831bab0dcf8845061", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.1.22.tgz", "integrity": "sha512-2tEzliJmf5fHNafNwQLJXUasGzQCVctvsNkXmnlELHwypU0p08/rHohYvkqKIjyXpx+0rkrYv6QbhJ+UF4QkBg==", "signatures": [{"sig": "MEYCIQDDqPsjio/NKLNOWiAERdNWlrviEM2ppmL3fFZXS9r6lwIhAPpBno0LRBxGoBzDk7EaRXnxVPKt7HCSM+TeMlNoR1QR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "_engineSupported": true, "optionalDependencies": {}, "contributors": []}, "0.2.0": {"name": "async", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "db1c645337bab79d0ca93d95f5c72d9605be0fce", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.0.tgz", "integrity": "sha512-nnO8zZ7dtTBikdb+WBynUk+LIn2jNrEM38ZM9WLFNYGMhSA6rJhnGbOhkKInnUoE9rC5VFAuIzTtq8Cl1T+W2g==", "signatures": [{"sig": "MEYCIQCuVCblXiqX9GMhMEKP1SMbIELOmXqlcovU53kU0y/vkwIhAMXabhgQnQQJH3MQJQvNH7qRwcVUfqm38bv2TJub46dQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.1": {"name": "async", "version": "0.2.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "dist": {"shasum": "4e37d08391132f79657a99ca73aa4eb471a6f771", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.1.tgz", "integrity": "sha512-hMyoohB59F7goU5qLI99Nl4ClmXb5/w/tsfPxW/LB7znoqngNtDTe6JKKPRQFGRbE9hrlbwy3et79dzIrBVL+A==", "signatures": [{"sig": "MEUCIEoatMcHLPccRP7m32zdIEnsCHtyrmINB9ZM0TShydtQAiEAnYHR87yfG/T48RxIg5XSuMtZk+tqTFM4OlWlNmsZsUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.2": {"name": "async", "version": "0.2.2", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "8414ee47da7548126b4d3d923850d54e68a72b28", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.2.tgz", "integrity": "sha512-tJRRWjpougYgbb9OIL5o1NCkMpAAo3wKznVG3ukqznOcfc/GMqo6MGVYOGbDUjwP93x3exAnrsFbQ7rD21sRyQ==", "signatures": [{"sig": "MEQCID3gx8+/AviHUXlfRYw6LqOMQEZyznVARiB7uHg0oBM5AiA6WPlKFKkfDwaYj6GQ/R34QCpnZOZW7ANg5p0Z1QJJBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.3": {"name": "async", "version": "0.2.3", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.3", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "79bf601d723a2e8c3e91cb6bb08f152dca309fb3", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.3.tgz", "integrity": "sha512-VUBA016fb0uHNgE4KJ8jVgR6WIsAqIIESLcebYhrp5tcEq5nmibVyqHtlJ4DE5chi4gzR04GpEyrqPyoyDUxvw==", "signatures": [{"sig": "MEQCID4s0i1Xcs/X40mnMn4pCxiceAuVMLe6YfH9RkNoFNePAiAzq9EPWmKUCtikDFKv884BJwCv/QSef2MnYN1AIFxO4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.4": {"name": "async", "version": "0.2.4", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.4", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "0550e510cf43b83e2fcf1cb96399f03f1efd50eb", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.4.tgz", "integrity": "sha512-YOlRWhY+HgDdAPGG23DAGKTcmeA2dRlqWx8lVCHKh44JxI0XPyaTzh2kRqL+VkR/0k3ACtWgfMbZOb2+YfrdmA==", "signatures": [{"sig": "MEUCIQDaLL1x4PzKGfX60Da8Fi8eUlZ8QlvcUeMh1PfdZA+zUgIgROy1WOQRGhg+aS+zeySmRnLc0FNg8fjvbUw9KiLB2V8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.5": {"name": "async", "version": "0.2.5", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.5", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "45f05da480749ba4c1dcd8cd3a3747ae7b36fe52", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.5.tgz", "integrity": "sha512-8t/BPKmtCPGoCxp2K24E2MK3JGU/imGZJdhh2J7MjMWJw0/99RBq/fLdI8rZpoefIDf+wmMdvRyLkgxbA4k6ug==", "signatures": [{"sig": "MEUCIBvSEM6iUELojeTkPsgmUrWx3WSCaaFrnz1AU/55GJ9QAiEA1erL0DSvXqUXroiPDQHcyeoFFE+uGTMS3xu5jwBkVeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.6": {"name": "async", "version": "0.2.6", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.6", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "ad3f373d9249ae324881565582bc90e152abbd68", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.6.tgz", "integrity": "sha512-LTdAJ0KBRK5o4BlBlUoGvfGNOMON+NLbONgDZk80SX0G8LQZyjN+74nNADIpQ/+rxun6+fYm7z4vIzAB51UKUA==", "signatures": [{"sig": "MEUCIQD3apPEm+ULbSFLX6fqTKWpzjs8dPwoZ2aMRvSvEMcedQIgYRPSCIkpdL5Z7DBp0cqCbQvKNFTAY8L+NsC331MNFfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.7": {"name": "async", "version": "0.2.7", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.7", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "44c5ee151aece6c4bf5364cfc7c28fe4e58f18df", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.7.tgz", "integrity": "sha512-5BMKmTkzcOSMOjx4UdgwVuwVEtcoqiaxC8gCRXwyJ1RqjmWJs0IRRxvzF+rTpFpXr33nWKgDlkjzgmC5isM6pA==", "signatures": [{"sig": "MEYCIQDDMxV/g189H9/JJiquBaNS1u9z19UXPDiD2BoqIl+SfgIhAMBh4FwUR4EDXUrahoJTKgFp5jlJ55V5/H4bhR1asXaN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.8": {"name": "async", "version": "0.2.8", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.8", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "ba1b3ffd1e6cdb1e999aca76ef6ecee8e7f55f53", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.8.tgz", "integrity": "sha512-OvesHI7rvKyZiwRiNZfC7kPm/KlvgKYixkxpCyj7YgHVQu6DXjjcCtAnlY+sEApfs1QYhnU6ZKGhZkVwV3ESMA==", "signatures": [{"sig": "MEQCIAaI1MAxq3xFjX0LCR+dAKufh77XB57sUdL/26/x04uYAiAIMlNWA4TPemHdvm/8JeMFwl+g/ngXMgFjRgsVUglIJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "http://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.9": {"name": "async", "version": "0.2.9", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.9", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "df63060fbf3d33286a76aaf6d55a2986d9ff8619", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.9.tgz", "integrity": "sha512-OAtM6mexGteNKdU29wcUfRW+VuBr94A3hx9h9yzBnPaQAbKoW1ORd68XM4CCAOpdL5wlNFgO29hsY1TKv2vAKw==", "signatures": [{"sig": "MEQCICw8WvYWKlg4GA+BHZpXA6mhNuTzQkOgITiRjAoQUgGsAiBn69vkSJTplNSQfVcxvg5QLE3T/bx+Kw5aph0+f0iMaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.2.23", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.2.10": {"name": "async", "version": "0.2.10", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.2.10", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "b6bbe0b0674b9d719708ca38de8c237cb526c3d1", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "signatures": [{"sig": "MEUCIQCXk3BnwZInzVs4J++eN0BacE5K5ZFVfDM+IEhYu3hZ/AIgZW0T2vqvCQ6qY7XlEPe6Ou4y5VP+NGZfrmEBgFmGhq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.3.0": {"name": "async", "version": "0.3.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.3.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "6d2c543c25f514c602bb22916ac222a519290d5d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.3.0.tgz", "integrity": "sha512-t0AbvoJp8ufti4iQnqGaD5+BqEvNE5G4IkRBut+X58yRmVxYX/0EMXuk7urOMqoDmRMAlODUQgDzEmytxBwmlw==", "signatures": [{"sig": "MEUCIQDHkon+iHzvV/kTD70bswF8V2yaONqlrnMFZHQLhlOLGAIgfi6t6Pmof6Jsnics504Iev9TyToh5JFGfn7ESoLK1wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.4.0": {"name": "async", "version": "0.4.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.4.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "208bba02850129dacc2bc3959e4126570ae80b74", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.4.0.tgz", "integrity": "sha512-U/V24wqehhL9iASh8i9ocYjwBJQx++rQRA8oefLqbVCbRkOAahxR7MP6vfwtpTaVJhNH9CgWgTY3yAUwtDfyGw==", "signatures": [{"sig": "MEQCIALcT3hgZRKQLLY2NPDKkES8byo65LMCQRFrBGJIjECyAiARxKVY47BdjwsBhmUHcVUvU+rAOihL/umtmFVhbVhHNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.4.1": {"name": "async", "version": "0.4.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.4.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "1985abade017df906bfaa8d77d424b25366b3a5b", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.4.1.tgz", "integrity": "sha512-Av6ptO/8zrd9U6Bv1p+zY/PMYvdVIno1YunAyQw2VlQBEFL+ozihMB7BIyKEQMmQIi/uIrpVEhvQ8MgE2DAFpg==", "signatures": [{"sig": "MEQCIBYYzwueFVGhI13TMExNvdcWQyfyZdZx9fpwPfyTl0vQAiA710RMqsG6I6zrw1NpKU6GLFWJItKcRnbq7ikhCFlq6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.5.0": {"name": "async", "version": "0.5.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.5.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "524bc1cf3ed2b6adc7f4a8c4987dd9c4809c764f", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.5.0.tgz", "integrity": "sha512-SbEssEno13E5+zFBHXa3PQP2f2U/tq1xjq0mwb2yBIYwOk5SYh3hWdY3ABByIQgxDNQi/yRGCl1/qf4AT7G8ng==", "signatures": [{"sig": "MEUCIQCL6uogQDUOLrEEOMSkGp2PlliEHSO830YvFBKCk15weQIgSxQzTC3L0QtF3xTNsPdXIgVP+iviNRm8O76SWHCKeio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.6.0": {"name": "async", "version": "0.6.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.6.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "025a31c8b1fb11e7481fa18dbdbc2bf2e434933a", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.6.0.tgz", "integrity": "sha512-ZOOAuacMq3yLwWL+QnaXpVwEuV6XF0QVL78edsEKBn+PYaL4kcDId/SYSLWm0s5GrWm3IIMDsaD7djfpZZQdxg==", "signatures": [{"sig": "MEUCIQDvA6j89g6w2PtFoek1qeQM91RxUAOTz6gS4WW7BCa91wIgeRSTgBUR4Va5q8avB9AiXRGXFDxpacG5UWDqpp9hFwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.6.1": {"name": "async", "version": "0.6.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.6.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "594fe360968fcdd2d7e0a6d95a874e4e92c7a26d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.6.1.tgz", "integrity": "sha512-egr4a5B/OVikzKT/uD+i0gO54+Yjm4j7rphMqfgWAAlicrszZRWge13YiswuiBYjZM/dQoaNOAnI31loFyR5Pw==", "signatures": [{"sig": "MEUCIDZpNIr69qKIBkyMrijBzDWRWTScgfjE8n7oofK0lRiiAiEA9EKMvBJ70KuEhn5kPCntp9uqBQNGT8yJNhTp2OTHPcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.6.2": {"name": "async", "version": "0.6.2", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.6.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "41fd038a3812c0a8bc1842ecf08ba63eb0392bef", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.6.2.tgz", "integrity": "sha512-fWbn+CMBgn1KOL/UvYdsmH+gMN/fW+lzAoadt4VUFvB/t0pB4aY9RfRCCvhoA58jocHyYm5TGbeuZsPc9i1Cpg==", "signatures": [{"sig": "MEUCIQDNu32pZbP5qDcT8VwlbgJRnJDTaLHhKhpmcxcEMXsQTwIgBb/jCf8wfqGbkb/rBiArwx7jK3PvPthNy3NOQfeKfPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.7.0": {"name": "async", "version": "0.7.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.7.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "4429e0e62f5de0a54f37458c49f0b897eb52ada5", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.7.0.tgz", "integrity": "sha512-9GReTNJtSSP+tNXZhlXuzPrRVlhH7Abwgf9qsV4NVLiChZpzFKvWGIwAss+uOUlsLjEnQbbVX1ERDTpjM5EmQg==", "signatures": [{"sig": "MEUCIQDI5W3AN6hMKHBiG1WI3R8Rfn17Wyr4FdIuucwf49ugiAIgTP2hXi7TzsOqxVIPtISFkduvc9jcBn56X6CXkiHquNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.8.0": {"name": "async", "version": "0.8.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.8.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "ee65ec77298c2ff1456bc4418a052d0f06435112", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.8.0.tgz", "integrity": "sha512-M2LC+aqW7VetFcnFiYEbjUsmASW6GSsMNkRzhUzwHoQNfNIRClf5GLgozwuJ4tAMLAfjywrKyQ2wWiODJivQmg==", "signatures": [{"sig": "MEYCIQDWI8ZXzWzCEMMceuswU5bUv40LoGHeBzQDDT29BGvDXQIhAJPd/aa6G08LJuh331MG6huDJDwgbUBQ13wkOnuS+tMI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.9.0": {"name": "async", "version": "0.9.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "async@0.9.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "dist": {"shasum": "ac3613b1da9bed1b47510bb4651b8931e47146c7", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.9.0.tgz", "integrity": "sha512-XQJ3MipmCHAIBBMFfu2jaSetneOrXbSyyqeU3Nod867oNOpS+i9FEms5PWgjMxSgBybRf2IVVLtr1YfrDO+okg==", "signatures": [{"sig": "MEUCIQCI1Cj5C8kd39ibGV6rt+zZpzjwFzuunfoZqqAk851UagIgBgDBJ0HJybPXHn5QnRkRkOz/qWSr8CUrb2e7Dl6s2GA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/async", "_from": ".", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "caolan", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/caolan/async/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "0.9.2": {"name": "async", "version": "0.9.2", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@0.9.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "aea74d5e61c1f899613bf64bda66d4c78f2fd17d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw==", "signatures": [{"sig": "MEUCIQCkWWXOG+S4KGmObeljhtJc2ZuIFC1Ri+ndBZR42cltdwIgcmQGu0VSb+7XvZkQZiGuPUraNudGgruKeF220pmenKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "aea74d5e61c1f899613bf64bda66d4c78f2fd17d", "gitHead": "de3a16091d5125384eff4a54deb3998b13c3814c", "scripts": {"test": "nodeunit test/test-async.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"lodash": ">=2.4.1", "nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "1.0.0": {"name": "async", "version": "1.0.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.0.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "f8fc04ca3a13784ade9e1641af98578cfbd647a9", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.0.0.tgz", "integrity": "sha512-5mO7DX4CbJzp9zjaFXusQQ4tzKJARjNB1Ih1pVBi8wkbmXy/xzIDgEMXxWePLzt2OdFwaxfneIlT1nCiXubrPQ==", "signatures": [{"sig": "MEQCIHgFN8rHIvkBBZOPON2hronH4CqbQEjZrtNpEhKfrca8AiBcyJzPNNsUJ+9M+IFH4PaQUd7JbvsI+60rmYV+pREY6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "f8fc04ca3a13784ade9e1641af98578cfbd647a9", "gitHead": "cfa81645c9cb4011b23d1d1a445ad855762568e0", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"jshint": "~2.7.0", "lodash": ">=2.4.1", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "~1.0.0", "uglify-js": "1.2.x"}, "contributors": []}, "1.1.0": {"name": "async", "version": "1.1.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.1.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "2b33ea3e87fc0c5ed624f9e31a9c902c022da09b", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.1.0.tgz", "integrity": "sha512-oDfw1XAvqquMU5ffBq/8EbRPsQ8caU5im+YD1cwvYUNzs2u5IKm1+dE7n3IXyyNPIeCEMcM7Wg42CqrZCFjIdA==", "signatures": [{"sig": "MEYCIQDmpxLOAJAPzA6e9gy45kT+yHw3wS8I/OdFw23udDh/SwIhAK2J2JMAAnCzByvdUqwU9S23uZhCUSv7AlEHbqnP4yal", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "2b33ea3e87fc0c5ed624f9e31a9c902c022da09b", "gitHead": "88906aa60d407e12185139e86204aa63aa6faf28", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"nyc": "^2.1.0", "yargs": "~3.9.1", "jshint": "~2.7.0", "lodash": ">=2.4.1", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "1.2.x"}, "contributors": []}, "1.2.0": {"name": "async", "version": "1.2.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.2.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "9029580f93d05a7cab24f502c84707ac3ef57b10", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.2.0.tgz", "integrity": "sha512-wqBtHDvCb1m5eWz5w8FWMeghRk46hdwlp+MrLfzPDnjy9cgpkMYNdN0PXlC5rnCXkeZHOoR9prohE/D2TTUVtA==", "signatures": [{"sig": "MEUCIQCmuj8bYQfpaBiDlPkDuCmot5waarg/clbcHWkCOxQXWwIgXMOFbF8MFfMX0dEqGSP49NUwSMfZ2Qx70BmDd4XR8CY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "9029580f93d05a7cab24f502c84707ac3ef57b10", "gitHead": "ff3bd90be6b862a11d0e4a11eb72a6985c26c5af", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.0.2", "devDependencies": {"nyc": "^2.1.0", "yargs": "~3.9.1", "jshint": "~2.7.0", "lodash": ">=2.4.1", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "1.2.x"}, "contributors": []}, "1.1.1": {"name": "async", "version": "1.1.1", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.1.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "753cb13df043ff08d810e4418d312d646ee1bbea", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.1.1.tgz", "integrity": "sha512-b2Jrm7t3GwqLqwm5abOsOx1o4uGqFvP3fpoEwkIqe6pANfSYCNVUxMXCY2N4yJjmjthfnsA65cftQdlXo6qohQ==", "signatures": [{"sig": "MEUCIA4okovwMu7jm5hb58WIiQn16CDSgKbPKp8KTxQBO11jAiEAnHE7VhcDz67WSf/3uPjYDCqXx25/KIAETjlUCqRPFg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "753cb13df043ff08d810e4418d312d646ee1bbea", "gitHead": "46cbdfd5324be57f15481bee32ad0fda507171b0", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.0.2", "devDependencies": {"nyc": "^2.1.0", "yargs": "~3.9.1", "jshint": "~2.7.0", "lodash": ">=2.4.1", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "1.2.x"}, "contributors": []}, "1.2.1": {"name": "async", "version": "1.2.1", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.2.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "a4816a17cd5ff516dfa2c7698a453369b9790de0", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.2.1.tgz", "integrity": "sha512-UMnr1f7iakrFTqRSvkCUv3Fs7dMHN5XYWXLlzmKUMhJpOYlCxgI/zQd6kYnEuxhCAULUfP0jtMSiTbpGNbhskw==", "signatures": [{"sig": "MEQCICvGzCv7B9U3R/arI7yXrrddlCOoxRXHAHmNdn+cV4QLAiBzwQ5PubG+z6dyy43ZzZ4SDy5pT2Xk+80HkpLo8h/Lhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "a4816a17cd5ff516dfa2c7698a453369b9790de0", "gitHead": "b66e85d1cca8c8056313253f22d18f571e7001d2", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.0.2", "devDependencies": {"nyc": "^2.1.0", "yargs": "~3.9.1", "jshint": "~2.7.0", "lodash": ">=2.4.1", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "1.2.x"}, "contributors": []}, "1.3.0": {"name": "async", "version": "1.3.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.3.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "a6f1631e8a595a663496d0a5586bd12007d4871d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.3.0.tgz", "integrity": "sha512-JsebKhdkyE+QlWiFF+Mo9n2YBiFXkUNNLN8eLJqowTxTiFV70hDdgldy8Y+muTuOVeGNyOFawqR2zLqPquLyOg==", "signatures": [{"sig": "MEYCIQDsFqIyuGAFYlhiMIELfxFzyskLHz4WENjz+hkcdkAUSgIhANSEUAj0KN9DW/mlHoU53hzDzk+m2z+t2Tddw418lXOl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "a6f1631e8a595a663496d0a5586bd12007d4871d", "gitHead": "71fa2638973dafd8761fa5457c472a312cc820fe", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0"}, "contributors": []}, "1.4.0": {"name": "async", "version": "1.4.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.4.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "35f86f83c59e0421d099cd9a91d8278fb578c00d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.4.0.tgz", "integrity": "sha512-Zlt2pNm/fE+7BhKRkQrRhP0FXkQ6sS4amtfDhra4Q+VTrQJPFmDPxk15h+6nvlR6nWK7C1QbvkvsCO1auCbtUQ==", "signatures": [{"sig": "MEQCIB+MBmWTra9R7Lh0LkMBNndjARDw7uKQ22HyMJqLsnImAiADRi0rM8Yrpr0P1OQYVqVs9F7xjgx2uGunYtLm5uvu0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "35f86f83c59e0421d099cd9a91d8278fb578c00d", "gitHead": "5bfcd31c72e003f96df025e75753463da61f49f9", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && nodeunit test/test-async.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.13.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.4.0", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "native-promise-only": "^0.8.0-a"}, "contributors": []}, "1.4.1": {"name": "async", "version": "1.4.1", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.4.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "1bc4895271551e524fd7fb338ddebad1a1440b74", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.4.1.tgz", "integrity": "sha512-Pbd2/QmBlQ6/fTzKC91ATKCTM6W+/oZ8/s9z2qk8NGXtTktrrR8mgK2Dpw1dJmuysPs92f/Dp0lZDru4RzqmOw==", "signatures": [{"sig": "MEUCIQC6xo5qUNhtgsTyYCOXWVBDW7UXIvQu3IG9FACmJv6ZmgIgXcHZRlGbiiUxjXFmuwbJNnogK7jrfMtNlST6edE26So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "1bc4895271551e524fd7fb338ddebad1a1440b74", "gitHead": "2daeb2cc898ca71d70fd664d0c1ef1dd1663325c", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6"}, "contributors": []}, "1.4.2": {"name": "async", "version": "1.4.2", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.4.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "6c9edcb11ced4f0dd2f2d40db0d49a109c088aab", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.4.2.tgz", "integrity": "sha512-O4fvy4JjdS0Q8MYH4jOODxJdXGbZ61eqfXdmfFDloHSnWoggxkn/+xWbh2eQbmQ6pJNliaravcTK1iQMpW9k4Q==", "signatures": [{"sig": "MEUCICtAw7kSoWAyUhpmg//x7m4Ak8Z/qwX3kx4NLEg/L6oVAiEA0jGUxxDj3fLnppE9VACV2OF1npNIMSZfmf6Hjc3DbWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "files": ["lib"], "_shasum": "6c9edcb11ced4f0dd2f2d40db0d49a109c088aab", "gitHead": "92f78aebad222d60c13e4299c0e723f2fe2d6611", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6"}, "contributors": []}, "1.5.0": {"name": "async", "version": "1.5.0", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.5.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "2796642723573859565633fc6274444bee2f8ce3", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.5.0.tgz", "integrity": "sha512-m9nMwCtLtz29LszVaR0q/FqsJWkrxVoQL95p7JU0us7qUx4WEcySQgwvuneYSGVyvirl81gz7agflS3V1yW14g==", "signatures": [{"sig": "MEQCICqcezv7RJ9tBMQVHrLhsHo3y+pOQjWM2MhUp3Hceye5AiAbvNW4ykTwUE9c0lRHDENmnzgazYJ1F48tENbq68yGMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "files": ["lib", "dist/async.js", "dist/async.min.js"], "_shasum": "2796642723573859565633fc6274444bee2f8ce3", "gitHead": "621f13805aa326865b85dbbf7128baf7146ab976", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "0.10.26", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "semver": "^4.3.6", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6"}, "contributors": []}, "1.5.1": {"name": "async", "version": "1.5.1", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.5.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "b05714f4b11b357bf79adaffdd06da42d0766c10", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.5.1.tgz", "integrity": "sha512-xpP8QJKDlqOhumIYy3wTwSAT6Pyw6+dK3KEG5JYq6dCY6HXP+Ykh3gnj+JI11HxnAjFQlG7ovtHmiukkTYHIkg==", "signatures": [{"sig": "MEQCIFMozfdH0IXik7NAi3lHTsOqv7lwAns80kx4+Xf/W57DAiAuMlK/LH2x3TLqDQ8If/r6hNzVyEZXugSL4Atzk+69rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "files": ["lib", "dist/async.js", "dist/async.min.js"], "_shasum": "b05714f4b11b357bf79adaffdd06da42d0766c10", "gitHead": "625a2e11fa0604a52aaec57acb6075c49325f4a9", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "semver": "^4.3.6", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6"}, "contributors": []}, "1.5.2": {"name": "async", "version": "1.5.2", "keywords": ["async", "callback", "utility", "module"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@1.5.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "lib/async.js"}, "dist": {"shasum": "ec6a61ae56480c0c3cb241c95618e20892f9672a", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-1.5.2.tgz", "integrity": "sha512-nSVgobk4rv61R9PUSDtYt7mPVB2olxNR5RWJcAsH676/ef11bUZwvu7+RGYrYauVdDPcO519v68wRhXQtxsV9w==", "signatures": [{"sig": "MEYCIQCT28L/dnB/p+z29NniEsMxyS80ae3dphdsymEpvzPBswIhAL1EgTCB4B19n55ng3u/KOL2ql5HT+rOe4zwl2AP3iY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/async.js", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "files": ["lib", "dist/async.js", "dist/async.min.js"], "_shasum": "ec6a61ae56480c0c3cb241c95618e20892f9672a", "gitHead": "9ab5c67b7cb3a4c3dad4a2d4552a2f6775545d6c", "scripts": {"lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.2.3", "devDependencies": {"nyc": "^2.1.0", "xyz": "^0.5.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "semver": "^4.3.6", "bluebird": "^2.9.32", "nodeunit": ">0.0.0", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6"}, "contributors": []}, "2.0.0-alpha.0": {"name": "async", "version": "2.0.0-alpha.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-alpha.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "72acf81eee0d641e05af3cb16953863ec8b23fe1", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-alpha.0.tgz", "integrity": "sha512-GInjvip6f39pJDApsq1NCGlOyAfTFFzH/mpLa5ZY/HGBK5Cp64HvVKAt2DrAA0DH+oEb98mKfAzjqBpt+YhSUw==", "signatures": [{"sig": "MEUCIBjKpqA2R33d1UcBgctaSbwaS0BpH2l8HoxDtErdgFL9AiEA4dW5stfTE6cvNJva0LyKI/ZoCTMuHZzi2FPo/t5i9Is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "72acf81eee0d641e05af3cb16953863ec8b23fe1", "scripts": {"lint": "jshint lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js && jscs lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"lodash": "^4.3.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "gulp": "~3.9.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.3", "nodeunit": ">0.0.0", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "~1.0.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "~1.1.0", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-alpha.0.tgz_1458344817880_0.7943291163537651", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.1": {"name": "async", "version": "2.0.0-rc.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "5298bbe0317312a3c9314e6d7cf14f765fb48735", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.1.tgz", "integrity": "sha512-17YWNSP5vMam9PBEqUiEWVAhACOpF17hk9NcrC1NsWphEBUbqWBM+ctgCFfSHDeJcoBZOCkOJue7Wyy8A/1X6w==", "signatures": [{"sig": "MEYCIQDtKCnRwOuLyys7Iy8Y0dWKWwjddAFhqVwBPIHlO6taSgIhAO0PSCDSmBIibV/P4FIQppQnfyM4vzwLSlGhkXxtyQyr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "5298bbe0317312a3c9314e6d7cf14f765fb48735", "scripts": {"lint": "jshint lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js && jscs lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"lodash": "^4.3.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "gulp": "~3.9.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.3", "nodeunit": ">0.0.0", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "~1.0.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "~1.1.0", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.1.tgz_1458345156927_0.9829358148854226", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.2": {"name": "async", "version": "2.0.0-rc.2", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "6fc56eec72574ebfe43ad30aefef6206f1ad2494", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.2.tgz", "integrity": "sha512-3DwnVa+sifQB22NIEPOHR7YeCTSvX3H63Sfytjt26V/EF0rmAyBw1QQRgUe2rjz7X+c1ilxBVXxFVv9T0/OBfw==", "signatures": [{"sig": "MEUCIFV6Zxi9/aKh+oh6x28LyRtuv5plHaKvXqDOJK2xzEfrAiEAkgvXnOxgGAaEdoRx/2X18W1ih5tvERz8pLO42bt/bk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "6fc56eec72574ebfe43ad30aefef6206f1ad2494", "scripts": {"lint": "jshint lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js && jscs lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ gulpfile.js karma.conf.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"lodash": "^4.3.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "gulp": "~3.9.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.3", "nodeunit": ">0.0.0", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "~1.0.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "~1.1.0", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.2.tgz_1458790789053_0.13189413794316351", "host": "packages-13-west.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.3": {"name": "async", "version": "2.0.0-rc.3", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.3", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "1fae1160594dd47dbe5431d4726d66b10f374d89", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.3.tgz", "integrity": "sha512-rWW6LL1PHDEiN0uJ1b3xKO2AiYUWmWio0Jvu49Fy0DKPn3IMCrF+oYvD1/7Vpdc8jgAfmuO5593jCrk2+Ws+DQ==", "signatures": [{"sig": "MEYCIQCdbUyCLw7FYujOE1ukWJyXZFTCq4C1evz05H8hdM7L0QIhAOiL4EW8R3KLI4lZ/pKyyA4Yb8BvsRBFOZoT1cwB0Sqj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "1fae1160594dd47dbe5431d4726d66b10f374d89", "scripts": {"lint": "jshint lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js && jscs lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-node-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {"lodash": "^4.3.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "nodeunit": ">0.0.0", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.3.tgz_1460063486663_0.06838028854690492", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.4": {"name": "async", "version": "2.0.0-rc.4", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.4", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "9b7f60724c17962a973f787419e0ebc5571dbad8", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.4.tgz", "integrity": "sha512-6tzimU1lFEgkjPjs8s4uvb3/ZLgKSadWudiDrPA+JPfE6XY5U4LGoWsaSt8SIMMnuntA7YNCCyaymTIcLWhaUg==", "signatures": [{"sig": "MEUCIQDi9RJxiL9YWg/fDTvmWjhVWsm77IBouD8HXOVpWg1uHQIgcqs5DoC/GaytmE7bc3fky8MUC2Gutzu4oSHkcHja47A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "9b7f60724c17962a973f787419e0ebc5571dbad8", "scripts": {"lint": "jshint lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js && jscs lib/ test/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-node-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {"lodash": "^4.3.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "nodeunit": ">0.0.0", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "jscs-jsdoc": "^1.3.2", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.4.tgz_1462490997215_0.7240071413107216", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.5": {"name": "async", "version": "2.0.0-rc.5", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.5", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "jam": {"main": "dist/async.js", "include": ["dist/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "spm": {"main": "dist/async.js"}, "dist": {"shasum": "4d6ff31604e9715899c6368bf7d0e51dc44a1433", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.5.tgz", "integrity": "sha512-x6OuBPzvQBz9vrTauWVQW/x+k4BRlblgmNJV3R5cAslPqP37Czr0ff/2yHc1uN4UwWOdLCjYbTm787dqESsSsA==", "signatures": [{"sig": "MEUCIQDbwmhG3WrYxsvrlNrVH4NyiKsL0x9+HFGtA1IGsEIx3gIgBW8oQXtiFeZ72+D/97fs/dsIiZfXm4ELdOIDP4e6j9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "volo": {"main": "dist/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}, "_from": ".", "_shasum": "4d6ff31604e9715899c6368bf7d0e51dc44a1433", "scripts": {"lint": "jshint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js && jscs lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"lodash": "^4.8.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "jscs-jsdoc": "^1.3.2", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.5.tgz_1463429699251_0.47817938844673336", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.0-rc.6": {"name": "async", "version": "2.0.0-rc.6", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0-rc.6", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "dist": {"shasum": "978fc4155d1fc30b8b58fc3f020102b2da02f2a4", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0-rc.6.tgz", "integrity": "sha512-Ttsy/KUxdL8pSYcGGOAa2ZZdpukbDb+PMZQAY6xi/7pvgFzJk2RMh3KmFfe9zlvky+5e9EcGusPoP1iEgUbcoA==", "signatures": [{"sig": "MEYCIQDQUSU7qBGFuwnFsBW3WN1raAk+2gkEhud0DpYCU7c7JgIhAL+Hlw9tAtgTwTXqdYutk4g71e3NTFrZxQFIWlGg+erH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "978fc4155d1fc30b8b58fc3f020102b2da02f2a4", "scripts": {"lint": "jshint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js && jscs lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/ karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {"lodash": "^4.8.0"}, "devDependencies": {"nyc": "^2.1.0", "chai": "^3.1.0", "jscs": "^1.13.1", "rsvp": "^3.0.18", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "jshint": "~2.8.0", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "babel-cli": "^6.3.17", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "jscs-jsdoc": "^1.3.2", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^1.0.2", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0-rc.6.tgz_1465333999650_0.07890674052760005", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.0": {"name": "async", "version": "2.0.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "d0900ad385af13804540a109c42166e3ae7b2b9d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.0.tgz", "integrity": "sha512-x4YEotAaoO+dq8o23H0Clqm+b0KQ7hYHFfqxIz4ORzLzAdwH0K7S5/Q+mDo/wVyGdFYA0l7XE70Y9915PuEyqg==", "signatures": [{"sig": "MEYCIQCnGnEk12ZkWHl2wQCVuuVeEu9o9F6tXOb8FyfVZB1vXQIhAI2gIFj/6HiVHirum5Yp8lPf0lyLdLyQ+fnBeB7v8zqz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "d0900ad385af13804540a109c42166e3ae7b2b9d", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"lodash": "^4.8.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "eslint": "^2.11.1", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "cheerio": "^0.20.0", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^1.0.2", "babel-plugin-istanbul": "^1.0.3", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.0.tgz_1468369390042_0.18694622837938368", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.0.1": {"name": "async", "version": "2.0.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.0.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "b709cc0280a9c36f09f4536be823c838a9049e25", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.0.1.tgz", "integrity": "sha512-t7yBK5Pwp8Gq7q6LkAd6vyzLapJuuBhKDnDlgsNFR5KEG5XFzsXN2DFdoEz4qtxPoQFkTMNon73q6+Yn+P8Mcg==", "signatures": [{"sig": "MEYCIQDnwe7F7a2eH1ybTm5lxtUeeniOTJSXxcaA2VfWp5IzsgIhAM2bgZcbK9xDqoBv1O7q3jCxnUXztJsWrhR4lK6c+/XW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "b709cc0280a9c36f09f4536be823c838a9049e25", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"lodash": "^4.8.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "eslint": "^2.11.1", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "cheerio": "^0.20.0", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "benchmark": "github:bestiejs/benchmark.js", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^1.0.2", "babel-plugin-istanbul": "^1.0.3", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.0.1.tgz_1469219821915_0.46895121363922954", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "2.1.0": {"name": "async", "version": "2.1.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.1.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "132c1329c300e62a06656e21b102a01122d3806c", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.1.0.tgz", "integrity": "sha512-5p+uCk1mws+xNgE6eC1mNmCbCD0cYsbFTvFTMlN4PSPkV8yVvHvaDJ0ioFDXFIc2IRl5LsUM/WwzJQ2TgjFrDw==", "signatures": [{"sig": "MEYCIQDNHtG81GWjBT+M6ibrZGu5tR0L/UB9eF4d20T0OOdVOAIhALh8CqRWOrwlQ1WhQOF5hzecNXpDox331UnOoAptCzqE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "132c1329c300e62a06656e21b102a01122d3806c", "gitHead": "3bcc2ab65b49699d6b99ad00aadc7fe3ac7d4a85", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"lodash": "^4.14.0", "lodash-es": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "eslint": "^2.11.1", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "cheerio": "^0.20.0", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^1.0.2", "babel-plugin-istanbul": "^1.0.3", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.1.0.tgz_1476296558618_0.9054915609303862", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.1.1": {"name": "async", "version": "2.1.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.1.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "e11b6d10043f2254efb61a21163d840ccddb8d28", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.1.1.tgz", "integrity": "sha512-5+Y9xI64035cwyYCdm5mpQZVGntHPnuATveR8vTqE8cZdIv1CyOw19OFtHPF4gKVA4T6l7rZ6BQLOUHzKhilUg==", "signatures": [{"sig": "MEUCIFxbnZk90FXT6b0OlXMOZ9wmueTUvMQp9LTbsWTQm/zaAiEA0FwvhUiO/GOgkdOBx6RAjqP5DhtVU8JzjnJXK85+TrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "e11b6d10043f2254efb61a21163d840ccddb8d28", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run-script lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^0.13.2", "mocha": "^2.2.5", "yargs": "~3.9.1", "eslint": "^2.11.1", "rimraf": "^2.5.0", "rollup": "^0.25.0", "semver": "^4.3.6", "cheerio": "^0.20.0", "babelify": "^7.2.0", "bluebird": "^2.9.32", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.4.0", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^0.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^4.2.1", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "~1.3.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^1.0.2", "babel-plugin-istanbul": "^1.0.3", "karma-firefox-launcher": "^0.1.6", "rollup-plugin-node-resolve": "^1.5.0", "babel-plugin-add-module-exports": "~0.1.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.1.1.tgz_1476298732568_0.5726463799364865", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "2.1.2": {"name": "async", "version": "2.1.2", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.1.2", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "612a4ab45ef42a70cde806bad86ee6db047e8385", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.1.2.tgz", "integrity": "sha512-i0Jx7SEZNG5i+F9hrUILpfDkuVJxf+UqmsS6LVn3UdUegQryKplU5t5opYYkDPW0eKBeJUSiiuphgkUZagx5ZQ==", "signatures": [{"sig": "MEUCIEYj3uAuPH9N1rgkcPs5B2KrCbAUKSZ6EqQ6/MMGCycyAiEAoa9JLQ2/WM+B1anpBnmsVXgIh/JqX6YFjb5ukldEMIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "612a4ab45ef42a70cde806bad86ee6db047e8385", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.8.0", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.16.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.1.2.tgz_1476657994986_0.4750206011813134", "host": "packages-16-east.internal.npmjs.com"}, "contributors": []}, "2.1.4": {"name": "async", "version": "2.1.4", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.1.4", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "2d2160c7788032e4dd6cbe2502f1f9a2c8f6cde4", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.1.4.tgz", "integrity": "sha512-ZAxi5cea9DNM37Ld7lIj7c8SmOVaK/ns1pTiNI8vnQbyGsS5WuL+ImnU5UVECiIw43wlx9Wnr9iXn7MJymXacA==", "signatures": [{"sig": "MEYCIQDY848jXeVhgDobBHDSSHhSPCbXPHXtA+lUMrLzK4v7eQIhAOnUYgKCqhHrCkViD2xKRghOy4oIWasPPxfU0IKXh3wx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "2d2160c7788032e4dd6cbe2502f1f9a2c8f6cde4", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.16.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.1.4.tgz_1479842208353_0.5100211726967245", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.1.5": {"name": "async", "version": "2.1.5", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.1.5", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "e587c68580994ac67fc56ff86d3ac56bdbe810bc", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.1.5.tgz", "integrity": "sha512-+g/Ncjbx0JSq2Mk03WQkyKvNh5q9Qvyo/RIqIqnmC5feJY70PNl2ESwZU2BhAB+AZPkHNzzyC2Dq2AS5VnTKhQ==", "signatures": [{"sig": "MEQCIEDtRUNUKvLjWe0/Yu2TiOgo5EDxsL23xNUBBNGJUFUWAiBsE65uwwkcdATuiuHqf20jrku7DOcmfnBndLcwQgTD0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "e587c68580994ac67fc56ff86d3ac56bdbe810bc", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.16.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.1.5.tgz_1487467859603_0.9005031916312873", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "2.2.0": {"name": "async", "version": "2.2.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.2.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "c324eba010a237e4fbd55a12dee86367d5c0ef32", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.2.0.tgz", "integrity": "sha512-b8NLjm2v8zgh50O70c2X4mEpWwCL9Bm9vfywD1OJdkdZwGzdDOFs7AXiACNYNei/lnFuMoZ9I71/1yXLw4v2yw==", "signatures": [{"sig": "MEYCIQDT5sG0X4R2lwe7+3dR4SqQQVEnwLYTztPMg7aN5AiEtgIhAI8FUHU8FlEAzXcVtapb4WjsGL0Dzx6QKvq2Uvk9znae", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "c324eba010a237e4fbd55a12dee86367d5c0ef32", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.16.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.3.26", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.2.0.tgz_1490474380922_0.9878872255794704", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "2.3.0": {"name": "async", "version": "2.3.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.3.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "1013d1051047dd320fe24e494d5c66ecaf6147d9", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.3.0.tgz", "integrity": "sha512-uDDBwBVKsWWe4uMmvVmFiW07K5BmdyZvSFzxlujNBtSJ/qzAlGM6UHOFZsQd5jsdmWatrCMWwYyVAc8cuJrepQ==", "signatures": [{"sig": "MEYCIQDT9QM+B6ZFhGVL7aGIFCNOZ7Tq+2n0UccDajXbKlLrAwIhAJ0IX/DfVSRiO6hP5ShXixDlWXxxPLUbRRkhnX36NNhx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "1013d1051047dd320fe24e494d5c66ecaf6147d9", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.24.0", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.3.0.tgz_1491173724406_0.3916843933984637", "host": "packages-12-west.internal.npmjs.com"}, "contributors": []}, "2.4.0": {"name": "async", "version": "2.4.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.4.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "4990200f18ea5b837c2cc4f8c031a6985c385611", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.4.0.tgz", "integrity": "sha512-pCN/boWoTF+A78ccPWv37hweEgcY8PZr9BnU3EErtXAQ8BabFH8KMvtxC4uC3bGgblbsmIv9Dtr7pnaIpQBh2Q==", "signatures": [{"sig": "MEYCIQDJBLhnfTFzEKDwoLeOp3LtykgQWsTLHlDVDhrMwHNtLgIhALsIWgHn93Wi96B1spbLBQT8ovYRTQPmme3B6e8xpBNx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "4990200f18ea5b837c2cc4f8c031a6985c385611", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.24.0", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.4.0.tgz_1493508210588_0.4067633217200637", "host": "packages-18-east.internal.npmjs.com"}, "contributors": []}, "2.4.1": {"name": "async", "version": "2.4.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.4.1", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "62a56b279c98a11d0987096a01cc3eeb8eb7bbd7", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.4.1.tgz", "integrity": "sha512-l4FGEG4ckq1nC3PSqULdowskm65HBAQfHPG4XH7VLRq0ZKsCWkcfLjVymfLrloqgrvijJrft/mPftclykhTA7w==", "signatures": [{"sig": "MEUCIB9Hh3n8E80ZV3LQUcRfC215m4GdwH+v7MPpPTXQ6b7EAiEAluUL4+Wt5FT70R1QBzu+HOaFIc5xNcJ0n+otXH+VQRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "_from": ".", "_shasum": "62a56b279c98a11d0987096a01cc3eeb8eb7bbd7", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.24.0", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.4.1.tgz_1495425435125_0.257761464221403", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.5.0": {"name": "async", "version": "2.5.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.5.0", "maintainers": [{"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://github.com/caolan/async#readme", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "843190fd6b7357a0b9e1c956edddd5ec8462b54d", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.5.0.tgz", "integrity": "sha512-e+lJAJeNWuPCNyxZKOBdaJGyLGHugXVQtrAwtuAe2vhxTYxFTKE73p8JuTmdH0qdQZtDvI4dhJwjZc5zsfIsYw==", "signatures": [{"sig": "MEUCIQCM8cX2U3IVZKKhzQx1w5AlNSDUI+fVf4857K1qT0NTNgIgdT4qwEl/kg2vU1uIWUI0bGikRvVHCHlRs1rgjPMpRFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.24.0", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.5.0.tgz_1498434122210_0.6593488664366305", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.6.0": {"name": "async", "version": "2.6.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.6.0", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "61a29abb6fcc026fea77e56d1c6ec53a795951f4", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.6.0.tgz", "integrity": "sha512-xAfGg1/NTLBBKlHFmnd7PlmUW9KhVQIUuSrYem9xzFUZy13ScvtyGGejaae9iAVRiRq9+Cx7DPFaAAhCpyxyPw==", "signatures": [{"sig": "MEUCIFo5RMn0FVposcCPYtd8sgqs3BJOjK7uYYMzxe11n9TiAiEA5OpT7b+9BLHQwJd/4+HQspjoUwPrZLiKbimjYL3NWZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/async.js", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "8.8.0", "dependencies": {"lodash": "^4.14.0"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.1.0", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^1.3.0", "mocha": "^3.1.2", "yargs": "~3.9.1", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^4.3.6", "cheerio": "^0.22.0", "babelify": "^7.2.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "watchify": "^3.7.0", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^2.11.2", "uglify-js": "~2.7.3", "babel-core": "^6.24.0", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "vinyl-buffer": "^1.0.0", "gh-pages-deploy": "^0.4.2", "karma-browserify": "^5.1.0", "recursive-readdir": "^1.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.0.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async-2.6.0.tgz_1510022752955_0.6282575109507889", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.6.1": {"name": "async", "version": "2.6.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.6.1", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "b245a23ca71930044ec53fa46aa00a3e87c6a610", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.6.1.tgz", "fileCount": 133, "integrity": "sha512-fNEiL2+AZt6AlAw/29Cr0UDe4sRAHCpEHh54WMz+Bb7QfNcFw4h3loofyJpLeQs4Yx7yuqu/2dLgM5hKOs6HlQ==", "signatures": [{"sig": "MEUCIQDNgK9Apoji4Tpp0QS7BrUKigrM5BQq6uq8ijCO+hpy4QIgRg7m9GIHHwB9eNAZcO7200SKozuA7In7LHhJS6nz0Ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAkxWCRA9TVsSAnZWagAAMogQAJxPRPcF4lY8dlEv78Vm\nrE88f6xFuMnVUgJunHn43+mYg36DDYpKB5VQ3jaHjAaK1WHJYepuPzQSIRFr\ndNgRs62K6s5zC+q07rbv7KyrYOKfHpLOC+PGtpRcKEuMVTQ5lzps6cYYZu5x\njtjmYcTI3t0EuJpaTZgVygtQ8iyvXFBJyt1zzqMAsRRxQx4A8VvytLw96Arl\n97x1BirrYsaamseE0AcoCpOKnSBM5AGiO4A/SeTNFWbPx7eM8Pf2rEgV5ohz\n2z5bjj6zOWpL8jyFMPBblRE82YXeMvEp14tgaruLrb15+xE7QapfjZuk6AQZ\n+DofFTGQSdHk4PZKx7OhUZTNiWbbVvBxtLBAOeStod3BP7C+dCTsFre0R8Yu\nmgrQ+l94TGSBc1xK8uqyHtBT61UGly0v85eVfe3MXT8YsAWY0MiMEsuJVz8d\n9QCjecg21j3oyJAe6F05OMaRZe7yJdgalCO9sq/W42ZztIwqDGS+GbNTRFiu\nDfZh13rSqZIakyYoBXQTXzhaCeDrsJKblYlC+kCkbo71P9M2xBsDFnUF7byC\nLWMo4xC0xUypHFdOi2lVF+FvpLTld8OPXGZOjSlX82LI93jhciNYxeydxL1T\nbr0OJDiW09zNJR7H0ISNwcJK2tPqdmw6C8aMSzNYQnuCVX0MVhaWvhiJ7IlD\nHEkH\r\n=4QoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"lodash": "^4.17.10"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.2", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "uglify-js": "~2.7.3", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.2.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_2.6.1_1526877268967_0.7566371880854756", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.1-0": {"name": "async", "version": "3.0.1-0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.0.1-0", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "ca06713f91c3d9eea3e966ace4093f41ef89f200", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.0.1-0.tgz", "fileCount": 123, "integrity": "sha512-b+lONkCWH/GCAIrU0j4m5zed5t+5dfjM2TbUSmKCagx6TZp2jQrNkGL7j1SUb0fF1yH6sKBiXC7Zid8Zj94O6A==", "signatures": [{"sig": "MEQCIFzxHHkC9EOoEmzsjog3r1TesSJSh4fWVuwwvN1Ay8vXAiB9nJCz+gcatNQ9H0erkTw0A1FO61C1/rlk1ZBrhj3+aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsXPMCRA9TVsSAnZWagAA9Z0P/3Cl3srJ86yCqOSizOrX\neTBQVUdtiGvymV7aFriSNCTPq+d7KwKKBPnLosQpW9ZMbthdNMOBsEkx13ho\nnkmmdcBVWa+dweZ8+uWgKBTdvyorsLiIH5CXEHkQ2WEFKzBBMifV8qGAbu4F\n0PnF17EuIZvwxRBC6OqI1LOBLrK+qb4jE5a8lXR9//LA/Iop2xwIDXfgjavI\no/aDpqc1EgbwAS2zZb0uOxaCYbr75h4NyytCMOF3awm/K5R7RtGkyh2L1pvM\nRCMAf4n4M0kS9RbXkB1op9QEev7z9YSB2XgLKCAjDC12cbqqU6s3b+qTUYeM\nn3TDrf7V/ZEQjx23TK2Q9yoVi1dRGuotpC+8WYUmfO8hnGQ98y88MhT7Pvyh\nIDS8GciZaWUseOReaCBnb4F5cSvWMHTfqGGneLXftGLJGQFJY0RBW3p74NAI\nsslDxLDgbVB4R9NVAzWLcbalLyjoRW0OEqmTU+7EqgrBfDSb6w+bQCcpcoN/\nF7Q+8vedYNtXckMpLVsvrYK+0ubGLsUJ3coU5l0m4kf29Fv6xvpXZzfi9ouP\nQEcs3Xt2ldM+NRvUeAkH5Arazhy3OQa4CxFYVpqfvnjOOEUqSrjpDljuVGBn\nwnGwExazrMBCBnKBcWx9zKnaa3VLs55ALaKK3prbrj9MUPX8CH+NBaKpv8nM\nz7no\r\n=daBn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "deprecated": "3.0.0 is out!", "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.5", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^4.19.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.4.3", "babel-register": "^6.26.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.2", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.0.1-0_1538356171637_0.9203414527203662", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.6.2": {"name": "async", "version": "2.6.2", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.6.2", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "18330ea7e6e313887f5d2f2a904bac6fe4dd5381", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.6.2.tgz", "fileCount": 133, "integrity": "sha512-H1qVYh1MYhEEFLsP97cVKqCGo7KfCyTt6uEWqsTBr9SO84oK9Uwbyd/yCW+6rKJLHksBNUVWZDAjfS+Ccx0Bbg==", "signatures": [{"sig": "MEYCIQD5g90mUCM74ZCaf3szH7sxBTRXtvY9kcoVh5mHA0cEOgIhAM3CDKnpa/kj9rNM5ovB+VNsNih6kKbiY8AEs+/WNElG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcY0paCRA9TVsSAnZWagAAfEsP/0uR0qKD416jeKor2uKv\nFO3HtnmTDNN/zvjRWACvxBYeTbJ3k+4xnN87dzmqcWruBE0hUka2r1QSCfdL\nSdkixgM7huFWyl/F1Ndf7BMSMkmwpvwgdPcLmtP1I2UqwpJ2YutFYRfbrqW6\nZl5OL3P30+94klwu9VTeLm58yVVYx4LAEuUN81wpvnHbEj0fdGMcYL/jbMtL\nAUEvdicjJqEe9J6gqDWwrxzNr6RKcqsXTl3KZgsCqjYerbM/4x/0hjySIr31\n7isLNleRGaWRvejGpPA/1J1oVXm96qNQSabB8jZV1soenyIQ6iceVbwB+k+7\nUCuLeNh+dT8vjhS0OvnMLTK3gJnaPFJdc0Ov2dUWIFY/AL+h3+6AoROb/UOc\nuo03gSF9WRuMA2MAbT2UBYdaeLZ9r/Z8ic1EioEdnAqiWUp623AGh2ZdZnv6\nkUrRxGJElv+PG9yQ2bHCdSW6Nycua0B+1anTufdsuoeHKdXzJYB61jQIxJlr\n3c44yNNnJehMPfAyxfAW8Vd6ye4FJiZBe4/7RwBKLv/HTGSW0bStz/HnXXCI\nr+Fiwplje5thLE0eGqRYA7UL7DSUCqIYHojVtskTqwvWS2Q7GTb/Ep27exLo\nguf9cOC0mHald8HxgZ92IBRA4Gt7/7KhWNZaTR4H9hmiZtVgz/NTxEc4uRTv\nfm7P\r\n=LP6x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"lodash": "^4.17.11"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.2", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "uglify-js": "~2.7.3", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.2.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_2.6.2_1550010969878_0.5745435408981747", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.0": {"name": "async", "version": "3.0.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.0.0", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "4c959b37d8c477dc189f2efb9340847f7ad7f785", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.0.0.tgz", "fileCount": 137, "integrity": "sha512-LNZ6JSpKraIia6VZKKbKxmX6nWIdfsG7WqrOvKpCuDjH7BnGyQRFMTSXEe8to2WF/rqoAKgZvj+L5nnxe0suAg==", "signatures": [{"sig": "MEUCIFHSQhRyNkCKdMrfQ4uE3WL+Ga+zUEY4akRAX7W0jnSnAiEA2yAgUsIwQpAQNSBaBbWHkOl5LS7ggFWSSBYNOJykQSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 687771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4hCrCRA9TVsSAnZWagAA0xQP/3t8OScGLuvT83UFA0De\ntU1Ax5RM0/nj5x+1hB4Eo68OFcqLJNfMc2UkK+/PyUJ5g4zjyQlW0ENjc0ae\nTwdSdkY14oKlfYvYgiTXHjoO7qq1Bz6r/DrZsuGt3kz2ZfuGDAiwVhgEsth8\nuaS1AgwHwBKed+31R059ryvri+foIJq2helqJeDV4EVf9BpTQ5YEXUQJ2/qO\njOIbUvAl8bpzCAJLtIUv9l+szeJu7LGeMQMcDoOXmOHnvXcZDPymY6fVxoyf\ndkobmkVxSBog1ekekKegE0eouXzqY/w3AH0BkoW/udLI4S8UsTz9guKz2hLV\nGh38Wzx+xISMJ5LmcY8JqKGblETrzNK0ixRgnjHElyBopF3yw0wIk+P74Y1B\n658zaeQaqcK37QauFRENflvIkJ3AYCL4A58dWcN6zdgvt16K5LQ+u9Vd6abQ\nboaab3gVtJWJMGk6kt5x/EmOaDBmBUHDDX9/3YeONOEiRh5hWJGTdbMUEJoO\nmBn1oUXZaZQDxjdhyV+AByLKsjQ4tArdPTl1JMq8dAMWz1VO1GWtKb16HSlD\nEqIX6tts8AS+OTkdQmG72PQ1VlwYANTWOPa8il5vhWFCU13Dmdl3QhW9CJIu\nFXCjcjcPpC4DjWd2iULNS+qQO3fHH9Nl12ggP9yjxDZ3Wf+Lw1UEhQrm08Zo\n+5yT\r\n=q9pO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.5", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^4.19.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^2.0.1", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.2", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.0.0_1558319274573_0.7546644771853339", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.0.1": {"name": "async", "version": "3.0.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.0.1", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "dfeb34657d1e63c94c0eee424297bf8a2c9a8182", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.0.1.tgz", "fileCount": 137, "integrity": "sha512-ZswD8vwPtmBZzbn9xyi8XBQWXH3AvOQ43Za1KWYq7JeycrZuUYzx01KvHcVbXltjqH4y0MWrQ33008uLTqXuDw==", "signatures": [{"sig": "MEUCIBV/4OPNZ6m/3aYHK+oMOY2QHJ7P2SfnsVrtBwZZ/GHyAiEAtp/eXkTrnGS9Xo1JcXP+7X+jzi9wVcTioUou1vmhq+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 689481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6whnCRA9TVsSAnZWagAA37wP/2GudUWGTyBV7jK+h58r\n3i/gu5Dxsig6Hm/wYVa84yKgC17KAu5S2nEV/gzX8DQrxsZbbxzK38itcOhH\n//V/YQyfrHCDLCit01M6UOX3YvhDz/6R3YYCKfw+GL168TpXK+AJWUqbaSd9\nSpbMLEGnMRKgcOl6EO5eMPaBYdmbBPoeJF7SLluZK1IMgsx+LXP/AtTLU7ei\nXB/j6KajyT5fc1jgADNG+/uSctEE0+0XXbdp+pY08+N1IUGL4P6wqNaavqEX\nOe1W5KX/EpiOdisAmUVs3QO5lJqq1Q3EP48gAlyBy7iDhToKaEMAixV6512N\n7cgLDgufZ4Dj7zYec0uaQNVzVcJzOh6mH1u5JzBEs2KGVCQaaH/+k65srZO8\n9SnEB1PIM54n+9qxR5S5V+tDGTc3DGMIxa0n+v77oiKzPN7oY9BJKsUX8dZe\nGA8RFZhkaDXSg0LUGYZu6G8HNbGefOQfEdzkJewquIkGPiFnWfrBqFAioa4C\nxEHq+C+wTk3VSbAFu4FoW7Bv2lGJveguau6LhDCRBBopFr+iJS1wxCL1VCb1\njVznKAHGGAqDc2A2YR5Ql9BH+ikdQ334wZt4UqJ2kqior+zQkozaaJRYbVXI\nd1rqcKnEHx3aPVHCczwv+wNqvhRucJd3a7O8uZP1XmzySxLYijfAXnE11FFo\nI3zl\r\n=XwVF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.5", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^4.19.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "gh-pages-deploy": "^0.5.1", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^2.0.1", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.2", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.0.1_1558906982196_0.8708044325007898", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.0": {"name": "async", "version": "3.1.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.1.0", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "42b3b12ae1b74927b5217d8c0016baaf62463772", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.1.0.tgz", "fileCount": 137, "integrity": "sha512-4vx/aaY6j/j3Lw3fbCHNWP0pPaTCew3F6F3hYyl/tHs/ndmV1q7NW9T5yuJ2XAGwdQrP+6Wu20x06U4APo/iQQ==", "signatures": [{"sig": "MEYCIQDJCH9Ucr//fwYacdZsT7RAALCW5akcSKK4p7ZTuUEr1wIhAJ4Ag0MgGnE1Se6VXu+9+sW+dpNobCKfYh4wja94Z0t4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDw5cCRA9TVsSAnZWagAAMCQP/jR2pQSeQf4QEewRMBpe\nPZZiv2WViB92+9wwgKU+qFWvUNJTSuTmOhA6XLgh1dvIee6/4SF+f2ddyXRl\nBrgUZDOPWoPDjr6mlQvOCjFHeDuPzAjwc58kNLFBEOZ9eVrgddmx4rH+T7K7\nZowq2YQsIVkU332lWYaahzfo0qTeJ9PpvYzIaWjF6/ivll3qnL5YTmclx2e+\nkNQK6H3yOar45Fo7W0Cqp8qV3pgep1+Mb23inSk5D35dOK1oBtSMulitDiCY\nrZrR/ieG6e5Ci3FwIFj1fqGpW3gHgitt261nlmLnyCknGGK0KJjIy+d2Gded\nFe0fBAxla7pJoUcVPt54YlOpZ2FoBwHDN5AdK8nPjJDtd3kfZZWLgC376xYo\nT4UCj/CNCW9/WEUUfK2jeU8HRcbJt+Md8tGivywBTXjBG/YZc4sJLTLZ7Tc+\nLbY1iPJK2J7SgevldiJaYuwBPjHmrFA8wQ6I2qB2lFpPfbD27dGlSNN0xIXn\nh9qh0yhXyBzisXwUC1wyAx/39AqrnP71kRAtT1rhMHnQQNtzejlAo/FJxZez\nkqtH/7yZgOcIhsNihN16lpzTN1hfwWo6kLJQbVruIPRaOaz6LsPflVqgupUR\nRohQaq6dp24C6HYKbSTOpEtiNTjwOrW/50s9TKaoUyfmqZXCaPI+egktx32v\n4BKu\r\n=DND2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "browserify": {"transform": [["babe<PERSON>", {"presets": ["@babel/preset-env"]}]]}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.5", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^4.19.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^2.0.1", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.2", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.1.0_1561267803752_0.2222457552089212", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.6.3": {"name": "async", "version": "2.6.3", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.6.3", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "d72625e2344a3656e3a3ad4fa749fa83299d82ff", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.6.3.tgz", "fileCount": 133, "integrity": "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==", "signatures": [{"sig": "MEYCIQD857S8UdUC+qBlrrWY+L9DF7cU077+A9tDAplGt6fUdwIhALYFJBsmhophv350P/GOlwxNTyra4E100hFu8xAI3kG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 541050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdK7smCRA9TVsSAnZWagAAV2AP/AioMbCD1rU19wSa+2pv\niIr/lLFIEGSIo5qtZMxMetPxAlp2GUH6PSGdTfx0gl8WnvPHBkBrd7TRl5Lb\nSeINx014ted9CwZmKbZHtkNl7NFpHt5Y8zn0Kdb32xTg54YJngakxBCJIXJ+\nGgmnZhkq9hQdwPYXTIpvxDCWpUFcsWEFBNSt8aab+OvOqIqdPvFyIyN6ZG48\n+0a5bmXgH1oD8XV6pPksmzeNGglFI9RdmQURF13rSWdOMC/RAeB4h8hCuGFg\ny/HfhxN9kM3dscqDu1HcmEZtv1JnX97MpD0dHAGmr7p3KPY/h0llCFA6YHBS\nMGiKuTP20qj6phD/TezgpiDNCip8N3n4yj2yuQRMIvi94emViuMXG2Raqcne\n5ZP+qFw18JjkeFchNLpDdIm1oEmcJJbRVopnRDnJbtcyg759DzDru+Zyx3S2\nsoenmGVUb1rUikxVcmCmWSf5IQfHcHxsZ/mjchbjr6pq6/RNsF7cOuKsDF6E\nfMxk1POJoVHEh4J/t64MRC3s0zHTe6SeWfeeNiLodCv+ITvHbq2HvsSPoPgQ\nLnCuxn2OBbQEYEpEgHvrmqcU7dEAPxkZSyTVXHd5NcyxE289C59RZju9YvC8\naxeWItb28EXYMmhNpUKqaDNC5VrD2PxTyA7/agtLNtaOKdF6lcq/IId6wPHt\nKBac\r\n=pBda\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"lodash": "^4.17.14"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.2", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "uglify-js": "~2.7.3", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.2.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_2.6.3_1563147045724_0.33642246034762424", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.1.1": {"name": "async", "version": "3.1.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.1.1", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "dd3542db03de837979c9ebbca64ca01b06dc98df", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.1.1.tgz", "fileCount": 137, "integrity": "sha512-X5Dj8hK1pJNC2Wzo2Rcp9FBVdJMGRR/S7V+lH46s8GVFhtbo5O4Le5GECCF/8PISVdkUA6mMPvgz7qTTD1rf1g==", "signatures": [{"sig": "MEUCIQCIzoejYkVKxQJMa2mMuQYIn5WnTOP1fInr7p7GMvzT0wIgfZTOB532eI6Saay6p4NXKgKIlshRGAT55QK8UouGRdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK4SYCRA9TVsSAnZWagAAwq8P/2fzY1CQCkZxVry1UkOA\nEPhedbmCeYk9qjRvWN9jq0STU0K75i/XMxSJXCduFvkVsJ3zYIqN89c0vXPF\n27P+rhS38kgufjEy5hVpar0qW4vA4Z3507+5kFsG3K05sBP2lcj9VeJjsa1n\nF7ZV1FL7v/h/aIVAPTCpyfBG/uTvmYQQ0cS50SmIHTpcSJMJZNYZZZpHZogz\nvpvax2W7Mk3BcEx1g/RKCqGLOTA0RcD2aGRGH8VsnvCAvegHP38NTTeLNOmt\ng+cXtXNmOviN6w5HOo6UBI0Ik1i6P+vJHXpe+ixtGdGHrpCeH9ZCV8a4f66F\nJ4EJd4BqeBj19mumNM37nuhi7rR1WikMpcAi/2l+0pgTOb92lCDzD38qv+WN\nWHlPFi0PJhLQZPQM7skr2+fiBWSTQYK+WEeMuiivuc1leGTGA5lLAuz6mEWP\nMyoXNi8ie35oLPHKIulpvEqFcRuQ7ZAds9BI9sxH9mo2GPK7aDLoEJ/D0KBK\nJZI4irVDV/0NlS6fML4ZFhSyqLF4IpQzIRI2PTJVu31zOSKFHg3dfbpiu5U/\nsCcrcg4ZOGwjzWBODtq5TZcUec5G80q+m+sxaGITIkHoaRxF/iWTpO6hqtOV\nas0ypLfjj0qYdOhjtFSWCD1La+N+pabmNNRFkp9SYxFksmtY74QL2TL+WvYQ\nDSNr\r\n=9Euh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "browserify": {"transform": [["babe<PERSON>", {"presets": ["@babel/preset-env"]}]]}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "rsvp": "^3.0.18", "jsdoc": "^3.6.2", "karma": "^4.1.0", "mocha": "^6.1.4", "yargs": "^11.0.0", "eslint": "^6.0.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.4", "babel-core": "^6.26.3", "browserify": "^16.2.3", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^5.1.4", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.5", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.1.1_1579910295944_0.5908180329547317", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.0": {"name": "async", "version": "3.2.0", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.0", "maintainers": [{"name": "aearly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "b3a2685c5ebb641d3de02d161002c60fc9f85720", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.0.tgz", "fileCount": 137, "integrity": "sha512-TR2mEZFVOj2pLStYxLht7TyfuRzaydfpxr3k9RpHIzMgw7A64dzsdqCxH1WJyQdoe8T10nDXd9wnEigmiuHIZw==", "signatures": [{"sig": "MEYCIQDsBEc7qy7jygjb3KDpnFayZNyQhWFYPe6NvtWxihd3gAIhAJ9oosGeHPu/B5oTZ4Th/EyLQyS/OKtD30qcXTcPX+gw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUzvMCRA9TVsSAnZWagAA6+QP/RUibULPbDqgUE7W3NX3\nnG1BQwtjLgTNNoD9SPiV6Y/FUTV4/Ij13/YV4Dh2l6BuJgo5Wso+WBt3Hiu+\ngev59KScUYuGd6J0NQ7H8BNEn8EmJsBs7uKcXzgzEp2JubUKkdmPLaUbLBbW\nfKjAASAyPEISQ0dz9zGq9BclBk+FV+JqBoBlVbnMnN8LQDgjM4vgwuBwtyy3\nHQ9DSDEEW/9iNd1TfHH6cT3oCPgDutGN9aAvKhC6qDnx5CgNfz/tUsKY8/aI\nLbTxOI2Eg5N6PT5s1n33uUrpEiV2Kc9zDJAiAVYQuwJgRPMPy2ZoSaPGYDYE\n5fyAU8BqAQyuiSB7ZRLxWCNkQTj0UEIxcttEsPeQTCWWgb1SjVIG9BbYi/4L\nrfBw8v4QJ1I5FZJqxL61CSCyH2ugRlgk0iNEPuhMxeRavpvG08ifOs52l5rY\n1jtcJOPlctyOgpX2bXoQxd5cfrfjn/2mI33adNz/L8W9XNsB+YuP/5EsZ58K\n6sBSQnQHK3RKU6xsMpQ9EDYT/RVFj3lEQghhexB/AzIAIJmk88qYiksguVkz\n+zxxrJMUZ1KgfuH/DPcBL8sZS34y7c9Sd7DDNs0GSJjzcO1iMovLwIBPKCe/\npEjiCqHrFs/jerZlmxoIaKPJ9TBZgLyvR5CU4J7tDRTZI9hD8lzsNse3AB0+\nJf9y\r\n=bq6i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "rsvp": "^3.0.18", "jsdoc": "^3.6.2", "karma": "^4.1.0", "mocha": "^6.1.4", "yargs": "^11.0.0", "eslint": "^6.0.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.4", "babel-core": "^6.26.3", "browserify": "^16.2.3", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^5.1.4", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.5", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.0_1582513099955_0.25980788891618545", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.1": {"name": "async", "version": "3.2.1", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "d3274ec66d107a47476a4c49136aacdb00665fc8", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.1.tgz", "fileCount": 137, "integrity": "sha512-XdD5lRO/87udXCMC9meWdYiR+Nq6ZjUfXidViUZGu2F1MO4T3XwZ1et0hb2++BgLfhyJwy44BGB/yx80ABx8hg==", "signatures": [{"sig": "MEUCIHYAB61AMMtRzmJhI5+ookuln1Bx/8cFM+4ndFO7Pd/xAiEAjivxxhy+P1C8umkBRtHG749CQPcMIATds5fsiRa6IPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 816097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDHIOCRA9TVsSAnZWagAASZ4QAJ9oNtLFgm6xJSKlWtHj\nzXCemeIfFsrb3av9mSKR6/XYk5nSEijCgLjm5Ep5x11Exuxo8OM+7t8USiKR\nJ1Gg9i/qkCqYq8PTqPy3yR0D90o4CNCwzGRwe1eRG2Mo4IoFNWYoEi3NAEHb\nOpaj5TBuWkRBjGRF9UIWeulvL5l07lOnWuvulCyHFqqvbL8bMGdpd6vwLzoo\nv4WGpiA0rshZa4v2IygeUU9CfB3eiRvzl3mKRfL+tbqaW96AS8oFcTmQXaT2\nm8M9iP4vkyKXO6cKFKfetdxEFYN3dteHwJakEAKC+bLayuK39K2Ge03qhwzH\npXXS7rllcf/PfIOU0YvZ+jNtRGsitNeaCRSEfRrvmw+5VHkEH4qtB2MQHOJP\nfnYoIMEPgOxpPJo8O2FyVBNuTRgUi4p4gMlya0wa+RdXAyOj1XuLLDySZxTO\nh7lAxPmTJERrebABvSZbGbqT2N+JdeFfc7qMUKYgGiqxcsI5jmTYWNjQOmWV\ndxesXNsNXoR2H0SFq+YlwlU+FYytIOS5oyBATfB2RdjsRGbftPKCnQL0SSAC\nie4UHknwV/rJYXkBHYhPzeAdah6XNPGVII36lU5oBHpO0EkmkosdyqT8Jw57\nzHWQphT7prBNITcRZ1jSFhiXmgDT1tGUlt/K0pIU0B4WuXkw5x9cEp/QQC/X\nECze\r\n=PRfx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "14.17.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "rsvp": "^3.0.18", "jsdoc": "^3.6.2", "karma": "^4.1.0", "mocha": "^6.1.4", "yargs": "^11.0.0", "eslint": "^6.0.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.4", "babel-core": "^6.26.3", "browserify": "^16.2.3", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^5.1.4", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.5", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.1_1628205581950_0.838478617754393", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.2": {"name": "async", "version": "3.2.2", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "2eb7671034bb2194d45d30e31e24ec7e7f9670cd", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.2.tgz", "fileCount": 137, "integrity": "sha512-H0E+qZaDEfx/FY4t7iLRv1W2fFI6+pyCeTw1uN20AQPiwqwM6ojPxHxdLv4z8hi2DtnW9BOckSspLucW7pIE5g==", "signatures": [{"sig": "MEQCIBgWpQwOsHT9iNgOpz9BoF2+C1+WwgyJ9Gn274tNeedOAiB1XYBYsrX45OueFMecnHgjoVnf0T6hVPoPgfTBIY5UmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 816672}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "rsvp": "^3.0.18", "jsdoc": "^3.6.2", "karma": "^4.1.0", "mocha": "^6.1.4", "yargs": "^11.0.0", "eslint": "^6.0.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.4", "babel-core": "^6.26.3", "browserify": "^16.2.3", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^5.1.4", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.5", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.2_1635391048363_0.37443693693197", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.3": {"name": "async", "version": "3.2.3", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "ac53dafd3f4720ee9e8a160628f18ea91df196c9", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.3.tgz", "fileCount": 137, "integrity": "sha512-spZRyzKL5l5BZQrr/6m/SqFdBN0q3OCI0f9rjfBzCMBIP4p75P620rR3gTmaksNOhmzgdxcaxdNfMy6anrbM0g==", "signatures": [{"sig": "MEUCIQDz66uC2MbtiqkGR5ZPK2R3LyfFtEWXsF6SgdU49u/JrAIgeZ1T9uXz4rYbh3LsTK6ZMWbEHJFc66aiXvOwr58D3XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 820507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh24AMCRA9TVsSAnZWagAA+loP/3QXGjSCmmNkz04uWaPt\n9PQJ2uycfydYdkUm96nVoMJZltSO2y6DbfhmdeqndRoKTQtnbwlmfq9ng6vz\n+srcqm4vjelRHPpYlZlTp2fBfNAw7uY4zlFsBUjAgYM4GE8L4wnHW4ARq84K\nuGLn1pDIUb2nzydrWwj3LFnooaPSTNwtkEGjBxgBjROpxJf/JmXX2XMWKw4G\nzq89y/5OK4LXIixgpl7m/5CvkETElVbqSTk/VPxFgRYw48vzEF4MZh+w79s9\nkrEWwmqiVxsWNB5E1+HtCfDKxh5cjmUpdx8DybAGRxb6Jfrg3gNuFXesIGeI\nE31+rIMD23oesDoVtx23ISotkGid2w7Eh7PTJMx1hDd6b39uwxDYJ+62UOvL\nEntW4t821oEbRTp6UUjuMZC2eimhygmy+FASQic5loGMcFwn91iCGGj4d3e5\nLJQmE+9ykucdgDL6/We/OyeLmiXYVjjmSP8HulioZw5eEuLMYlNZ5FO6Ijro\nOy8NxA+ngSBdOA8QPIjHYYkPIUqFalWTV2CciuHQAUtaou3TqRFRnLP/3OZ0\nLbqreVlOlv73s4g7UVwBny4yuJ0JI0DvzGlbX9kYbjvlWDWqBx6cQSiD39Qz\nPx8mWVs/hwgJBAxKB0Q8tm8sAyAKvqBmSWHsmTEpARF0JqkBGK+rQz2MvW19\n/2zV\r\n=8M/Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "scripts": {"lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "14.7.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "rsvp": "^3.0.18", "jsdoc": "^3.6.2", "karma": "^4.1.0", "mocha": "^6.1.4", "yargs": "^11.0.0", "eslint": "^6.0.1", "rollup": "^0.63.4", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "benchmark": "^2.1.1", "coveralls": "^3.0.4", "babel-core": "^6.26.3", "browserify": "^16.2.3", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^5.3.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "karma-edge-launcher": "^0.4.2", "native-promise-only": "^0.8.0-a", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha-junit-reporter": "^1.18.0", "babel-plugin-istanbul": "^5.1.4", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.1.5", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.3_1641775116794_0.1623085249174283", "host": "s3://npm-registry-packages"}, "contributors": []}, "2.6.4": {"name": "async", "version": "2.6.4", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@2.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["mocha_test"]}, "dist": {"shasum": "706b7ff6084664cd7eae713f6f965433b5504221", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-2.6.4.tgz", "fileCount": 133, "integrity": "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==", "signatures": [{"sig": "MEUCIQCKW29ZLtxpGVBPdzIdTmD/BqOYrisJ7L0fQva05kNN9QIgSP+kWyIfhhUxQkkJic2hrdcBNtZ86M/W4YJoDkE+6FE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 541303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiV1e3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprMg/+K/kylAW6VjOJrIABgxFedlr8L5qeaabchILjWewaoWtwh/aZ\r\nY4Ssx0ukLZfGPU39IloAzDhPxe2GmG/a6E+F+q3BkwWpFpAC17YFQj3eKNQO\r\nuP8dC0bDP4tPb/+pi+tHweTN4wZeTrWZcVP85smfBr7ODNxFq2ukR0LQtgg3\r\ncxwaI4Eo+2L6/joM/f4GOPuUnol8hfOmnYD7cNPn7t9ChHKCC1Y1yR9wGGby\r\n0QQ10Zlm91FMQzSw5WCly6JE8aWzk/KFpLT1/NzHXbXL4NxC7hXnLJGiWC7+\r\nm0U6jPgfkEPc0GNOTgS5b4AJNhJHLKTDE4AcCjslsa49Y9MEl6d/Q783cdme\r\nc2BJ5GaYcGkm7IdAQ7dLZS+crnCKgAkBis03lyrAixV3vzUZBTLKZN2Hy4gb\r\npnKiYSrvCQ+9PEr4J7Cgh9JykEGBwk9ildtjmNUMRybzZA0PoSHlEGTkymeT\r\nkyd4qzalRmoWCsVTzXgQr0nsSzAD+/ATIaTjUOO+TjNYTUYSJ0YVGrXUrtHQ\r\nW3uRTYuxUwBp8ifGvwqg4+4BosAimg0wQIEsZCFh8oXx5UIlJhUo8hga4tyx\r\ngYhHK+AEaXeNzgWKQ4MyZ0drvKLfpBRHYCupSaxGPcRyrW2WqL3uLjvil1SV\r\n7KfttotRxmyJB8dsmkd8mdNyk61Hrm068Dc=\r\n=Ab5D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "gitHead": "c6bdaca4f9175c14fc655d3783c6af6a883e6514", "scripts": {"lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"lodash": "^4.17.14"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^4.1.2", "rsvp": "^3.0.18", "jsdoc": "^3.4.0", "karma": "^2.0.2", "mocha": "^5.2.0", "yargs": "^11.0.0", "eslint": "^2.13.1", "rimraf": "^2.5.0", "rollup": "^0.36.3", "semver": "^5.5.0", "cheerio": "^0.22.0", "babelify": "^8.0.0", "bluebird": "^3.4.6", "fs-extra": "^0.26.7", "babel-cli": "^6.24.0", "benchmark": "^2.1.1", "coveralls": "^3.0.1", "uglify-js": "~2.7.3", "babel-core": "^6.26.3", "browserify": "^16.2.2", "es6-promise": "^2.3.0", "karma-mocha": "^1.2.0", "gh-pages-deploy": "^0.5.0", "karma-browserify": "^5.2.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^2.0.1", "karma-firefox-launcher": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "gh-pages-deploy": {"staticpath": "docs"}, "_npmOperationalInternal": {"tmp": "tmp/async_2.6.4_1649891255271_0.32423021556528187", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.4": {"name": "async", "version": "3.2.4", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "2d22e00f8cddeb5fde5dd33522b56d1cf569a81c", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.4.tgz", "fileCount": 137, "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==", "signatures": [{"sig": "MEYCIQCyfhDqY64diAlRW27C7/19Gl2Ukz1GOfln1mFh2SDJ+wIhAOPqbe0YAqe2skH5pjvV90MMGZNnjK718g2IM8Z1V9Aa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 820627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJins0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoznw//bU9qZjo/bW8nHUZKZW+Iwnlq1iA1gBGnxWUlxq5hdcouTyw6\r\nSj+HdkIb+J5egSfjiVUHk6gayLmBp2/SPuujj8kSJGpcVYR4/KvGot7QYVs8\r\nLn0wtcwgxncLYyekYoTntXilepKg/iSPOSBCZEb8ZnTvumNOAV3R/Rdg/cJn\r\njWXXWaGBhazJwLpQdubJTsjwAtMq2YGCKIt7ESkVU39NbUp3LKo9Txu0pt/m\r\nCTYBs6H5gzy5CIlDF3UHFaznKpPg+R9LecdHIK9ehKnWFSyHuCaV7Net1MM5\r\nsSxAO7215rHnXbcoAoF/tydZcFf1pjDI4FmNFeYhnYg3XD8cajhtDKMIGyIe\r\nbvGD7NaYE8rqVQ89g9kjXIk6B4HoObAA8rOsB30XQxlWi85CZs7g0yECWcRi\r\naIFs3akmF1V+uGzjDwQPGYhjXEnxc0fDt96Z3o0MUTKDAFApqkmxGlsdrlVL\r\n/rCoP6pUjRujgPDVJS5ywvIUEmdwL4AYefXGelTjubMoaiL6RqToIs6j6GtP\r\n65p7QEcGR2IBHHIDKJDZWayQp3TsP055n8lOHau6hoYGkE4CFj4/FpmJ7kDu\r\nd4bub10x/F7eXnnzkatk0H/tU8tjRs2nu2NEDWmP7f5oDE3KsPGMhj6lY3Nt\r\nkJJT3fktwIgxPxON1izHOtobgo0s4Y+F84Q=\r\n=3WXo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/async.js", "module": "dist/async.mjs", "gitHead": "f3ab51af76ca87ebe3ec67b3dd6dec4959e04816", "scripts": {"lint": "eslint --fix .", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "rsvp": "^4.8.5", "jsdoc": "^3.6.2", "karma": "^6.3.12", "mocha": "^6.1.4", "yargs": "^17.3.1", "eslint": "^8.6.0", "rollup": "^2.66.1", "semver": "^7.3.5", "cheerio": "^0.22.0", "babelify": "^10.0.0", "bluebird": "^3.4.6", "fs-extra": "^10.0.0", "benchmark": "^2.1.1", "babel-core": "^6.26.3", "browserify": "^17.0.0", "es6-promise": "^4.2.8", "karma-mocha": "^2.0.1", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^8.1.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "@babel/eslint-parser": "^7.16.5", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^6.1.1", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^2.1.2", "eslint-plugin-prefer-arrow": "^1.2.3", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.4_1654574357583_0.7995412960677097", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.5": {"name": "async", "version": "3.2.5", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "ebd52a8fdaf7a2289a24df399f8d8485c8a46b66", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.5.tgz", "fileCount": 137, "integrity": "sha512-baNZyqaaLhyLVKm/DlvdW051MSgO6b8eVfIezl9E5PqWxFgzLm/wQntEW4zOytVburDEr0JlALEpdOFwvErLsg==", "signatures": [{"sig": "MEUCIFKWDNzB+mCeiEILJjVgpASf4Vp5eiO6zAd9GUhs33eGAiEAh8icT+I0sShyVf6DNJ8KNZ6T4aw43NqbaYIBDDtwDnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 807668}, "main": "dist/async.js", "module": "dist/async.mjs", "gitHead": "87e94e658f24030f9104626e00456a5a0c1f9566", "scripts": {"lint": "eslint --fix .", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "rsvp": "^4.8.5", "jsdoc": "^3.6.2", "karma": "^6.3.12", "mocha": "^6.1.4", "yargs": "^17.3.1", "eslint": "^8.6.0", "rollup": "^4.2.0", "semver": "^7.3.5", "cheerio": "^0.22.0", "babelify": "^10.0.0", "bluebird": "^3.4.6", "fs-extra": "^11.1.1", "benchmark": "^2.1.1", "browserify": "^17.0.0", "@babel/core": "7.23.2", "es6-promise": "^4.2.8", "karma-mocha": "^2.0.1", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^8.1.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "@babel/eslint-parser": "^7.16.5", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^6.1.1", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^2.1.2", "eslint-plugin-prefer-arrow": "^1.2.3", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.5_1699049465653_0.9141691920917743", "host": "s3://npm-registry-packages"}, "contributors": []}, "3.2.6": {"name": "async", "version": "3.2.6", "keywords": ["async", "callback", "module", "utility"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "async@3.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caolan", "email": "<EMAIL>"}, {"name": "aearly", "email": "<EMAIL>"}, {"name": "megawac", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://caolan.github.io/async/", "bugs": {"url": "https://github.com/caolan/async/issues"}, "nyc": {"exclude": ["test"]}, "dist": {"shasum": "1b0728e14929d51b85b449b7f06e27c1145e38ce", "tarball": "https://mirrors.cloud.tencent.com/npm/async/-/async-3.2.6.tgz", "fileCount": 137, "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "signatures": [{"sig": "MEUCIGVIkMhFwMbLDtCb8+z9GFMA6NspoYw4xXsNMwAcOtdvAiEAhQIpoN7wNHYrlvNiTc2Cnw5bRI3O5tOocA0iLdunyQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 807741}, "main": "dist/async.js", "module": "dist/async.mjs", "gitHead": "85fb18f3d319d14d893ec24648929ff0eb908768", "scripts": {"lint": "eslint --fix .", "test": "npm run lint && npm run mocha-node-test", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "mocha-node-test": "mocha", "mocha-browser-test": "karma start"}, "_npmUser": {"name": "aearly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/caolan/async.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Higher-order functions and common patterns for asynchronous code", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.0.0", "chai": "^4.2.0", "rsvp": "^4.8.5", "jsdoc": "^4.0.3", "karma": "^6.3.12", "mocha": "^6.1.4", "yargs": "^17.3.1", "eslint": "^8.6.0", "rollup": "^4.2.0", "semver": "^7.3.5", "cheerio": "^0.22.0", "babelify": "^10.0.0", "bluebird": "^3.4.6", "fs-extra": "^11.1.1", "benchmark": "^2.1.1", "browserify": "^17.0.0", "@babel/core": "7.25.2", "es6-promise": "^4.2.8", "karma-mocha": "^2.0.1", "babel-minify": "^0.5.0", "babel-register": "^6.26.0", "karma-browserify": "^8.1.0", "rollup-plugin-npm": "^2.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "native-promise-only": "^0.8.0-a", "@babel/eslint-parser": "^7.16.5", "karma-mocha-reporter": "^2.2.0", "babel-plugin-istanbul": "^7.0.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^2.1.2", "eslint-plugin-prefer-arrow": "^1.2.3", "rollup-plugin-node-resolve": "^5.2.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2"}, "_npmOperationalInternal": {"tmp": "tmp/async_3.2.6_1724109863038_0.13024437870975958", "host": "s3://npm-registry-packages"}, "contributors": []}}, "time": {"created": "2010-12-19T16:41:51.765Z", "modified": "2024-11-05T19:14:41.599Z", "0.1.2": "2010-12-19T16:41:51.765Z", "0.1.4": "2010-12-19T16:41:51.765Z", "0.1.3": "2010-12-19T16:41:51.765Z", "0.1.5": "2010-12-19T16:41:51.765Z", "0.1.6": "2010-12-19T16:41:51.765Z", "0.1.7": "2010-12-19T16:41:51.765Z", "0.1.1": "2010-12-19T16:41:51.765Z", "0.1.0": "2010-12-19T16:41:51.765Z", "0.1.8": "2011-01-18T09:56:53.975Z", "0.1.9": "2011-04-27T20:48:08.634Z", "0.1.10": "2011-09-19T04:40:01.573Z", "0.1.11": "2011-10-14T17:07:28.752Z", "0.1.12": "2011-10-14T17:19:19.452Z", "0.1.13": "2011-10-29T22:33:52.448Z", "0.1.14": "2011-10-29T22:40:14.486Z", "0.1.15": "2011-11-01T23:05:01.415Z", "0.1.16": "2012-02-13T04:56:23.926Z", "0.1.17": "2012-02-27T02:40:58.997Z", "0.1.18": "2012-02-27T16:51:02.109Z", "0.1.19": "2012-05-24T06:51:06.109Z", "0.1.20": "2012-05-24T06:53:39.997Z", "0.1.21": "2012-05-24T07:16:16.753Z", "0.1.22": "2012-05-30T18:26:44.821Z", "0.1.23": "2012-10-04T13:52:08.947Z", "0.2.0": "2013-02-04T11:38:08.943Z", "0.2.1": "2013-02-04T11:52:34.110Z", "0.2.2": "2013-02-05T15:55:23.202Z", "0.2.3": "2013-02-06T12:48:37.415Z", "0.2.4": "2013-02-07T17:26:22.236Z", "0.2.5": "2013-02-10T22:42:00.162Z", "0.2.6": "2013-03-03T11:29:52.674Z", "0.2.7": "2013-04-09T20:50:04.712Z", "0.2.8": "2013-05-01T10:04:07.430Z", "0.2.9": "2013-05-28T07:50:48.795Z", "0.2.10": "2014-01-23T16:23:57.271Z", "0.3.0": "2014-03-28T17:16:05.640Z", "0.4.0": "2014-03-28T17:25:12.580Z", "0.4.1": "2014-03-30T11:42:54.298Z", "0.5.0": "2014-03-30T11:46:31.381Z", "0.6.0": "2014-03-30T12:04:32.275Z", "0.6.1": "2014-03-30T20:35:32.550Z", "0.6.2": "2014-03-31T09:56:20.294Z", "0.7.0": "2014-04-07T09:07:34.303Z", "0.8.0": "2014-04-29T15:26:34.028Z", "0.9.0": "2014-05-16T10:20:22.247Z", "0.9.2": "2015-05-19T08:45:57.198Z", "1.0.0": "2015-05-20T23:40:05.710Z", "1.1.0": "2015-06-01T07:59:05.989Z", "1.2.0": "2015-06-02T20:56:04.526Z", "1.1.1": "2015-06-08T01:26:56.285Z", "1.2.1": "2015-06-08T01:43:33.907Z", "1.3.0": "2015-06-29T16:14:01.899Z", "1.4.0": "2015-07-20T02:11:50.089Z", "1.4.1": "2015-08-07T21:08:08.172Z", "1.4.2": "2015-08-09T18:10:22.399Z", "1.5.0": "2015-10-26T01:41:14.220Z", "1.5.1": "2016-01-02T23:38:22.435Z", "1.5.2": "2016-01-08T00:03:32.998Z", "2.0.0-alpha.0": "2016-03-18T23:46:58.334Z", "2.0.0-rc.1": "2016-03-18T23:52:37.386Z", "2.0.0-rc.2": "2016-03-24T03:39:49.460Z", "2.0.0-rc.3": "2016-04-07T21:11:27.200Z", "2.0.0-rc.4": "2016-05-05T23:30:00.507Z", "2.0.0-rc.5": "2016-05-16T20:15:02.032Z", "2.0.0-rc.6": "2016-06-07T21:13:20.130Z", "2.0.0": "2016-07-13T00:23:10.577Z", "2.0.1": "2016-07-22T20:37:03.855Z", "2.1.0": "2016-10-12T18:22:41.697Z", "2.1.1": "2016-10-12T18:58:53.479Z", "2.1.2": "2016-10-16T22:46:37.667Z", "2.1.4": "2016-11-22T19:16:50.375Z", "2.1.5": "2017-02-19T01:31:00.277Z", "2.2.0": "2017-03-25T20:39:42.923Z", "2.3.0": "2017-04-02T22:55:24.664Z", "2.4.0": "2017-04-29T23:23:32.659Z", "2.4.1": "2017-05-22T03:57:15.218Z", "2.5.0": "2017-06-25T23:42:02.387Z", "2.6.0": "2017-11-07T02:45:53.140Z", "2.6.1": "2018-05-21T04:34:29.126Z", "3.0.1-0": "2018-10-01T01:09:31.821Z", "2.6.2": "2019-02-12T22:36:10.059Z", "3.0.0": "2019-05-20T02:27:54.697Z", "3.0.1": "2019-05-26T21:43:02.357Z", "3.1.0": "2019-06-23T05:30:03.875Z", "2.6.3": "2019-07-14T23:30:45.837Z", "3.1.1": "2020-01-24T23:58:16.097Z", "3.2.0": "2020-02-24T02:58:20.125Z", "3.2.1": "2021-08-05T23:19:42.272Z", "3.2.2": "2021-10-28T03:17:28.543Z", "3.2.3": "2022-01-10T00:38:36.966Z", "2.6.4": "2022-04-13T23:07:35.444Z", "3.2.4": "2022-06-07T03:59:17.813Z", "3.2.5": "2023-11-03T22:11:06.054Z", "3.2.6": "2024-08-19T23:24:23.363Z"}, "users": {}, "dist-tags": {"next": "3.1.0", "latest": "3.2.6"}, "_rev": "5849-c7f8644ba4b96ec2", "_id": "async", "readme": "![Async Logo](https://raw.githubusercontent.com/caolan/async/master/logo/async-logo_readme.jpg)\n\n![Github Actions CI status](https://github.com/caolan/async/actions/workflows/ci.yml/badge.svg)\n[![NPM version](https://img.shields.io/npm/v/async.svg)](https://www.npmjs.com/package/async)\n[![Coverage Status](https://coveralls.io/repos/caolan/async/badge.svg?branch=master)](https://coveralls.io/r/caolan/async?branch=master)\n[![Join the chat at https://gitter.im/caolan/async](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/caolan/async?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)\n[![jsDelivr Hits](https://data.jsdelivr.com/v1/package/npm/async/badge?style=rounded)](https://www.jsdelivr.com/package/npm/async)\n\n<!--\n|Linux|Windows|MacOS|\n|-|-|-|\n|[![Linux Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=Linux&configuration=Linux%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master) | [![Windows Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=Windows&configuration=Windows%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master) | [![MacOS Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=OSX&configuration=OSX%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master)| -->\n\nAsync is a utility module which provides straight-forward, powerful functions for working with [asynchronous JavaScript](http://caolan.github.io/async/v3/global.html). Although originally designed for use with [Node.js](https://nodejs.org/) and installable via `npm i async`, it can also be used directly in the browser.  An ESM/MJS version is included in the main `async` package that should automatically be used with compatible bundlers such as Webpack and Rollup.\n\nA pure ESM version of Async is available as [`async-es`](https://www.npmjs.com/package/async-es).\n\nFor Documentation, visit <https://caolan.github.io/async/>\n\n*For Async v1.5.x documentation, go [HERE](https://github.com/caolan/async/blob/v1.5.2/README.md)*\n\n\n```javascript\n// for use with Node-style callbacks...\nvar async = require(\"async\");\n\nvar obj = {dev: \"/dev.json\", test: \"/test.json\", prod: \"/prod.json\"};\nvar configs = {};\n\nasync.forEachOf(obj, (value, key, callback) => {\n    fs.readFile(__dirname + value, \"utf8\", (err, data) => {\n        if (err) return callback(err);\n        try {\n            configs[key] = JSON.parse(data);\n        } catch (e) {\n            return callback(e);\n        }\n        callback();\n    });\n}, err => {\n    if (err) console.error(err.message);\n    // configs is now a map of JSON data\n    doSomethingWith(configs);\n});\n```\n\n```javascript\nvar async = require(\"async\");\n\n// ...or ES2017 async functions\nasync.mapLimit(urls, 5, async function(url) {\n    const response = await fetch(url)\n    return response.body\n}, (err, results) => {\n    if (err) throw err\n    // results is now an array of the response bodies\n    console.log(results)\n})\n```", "_attachments": {}}